package com.ly.yph.api.seeksource.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商导出报价明细
 *
 * <AUTHOR>
 * @date 2024/10/08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class ExportQuotedPriceSupExcel implements Serializable {
    private static final long serialVersionUID = 4564328610663850051L;

    @ExcelProperty("询价单号")
    private String seekPriceNumber;

    @ExcelProperty("询价商品id")
    private Long goodsIndex;

    @ExcelProperty("商品名称")
    private String goodsName;

    @ExcelProperty("品牌")
    private String brandName;

    @ExcelProperty("规格型号")
    private String specModel;

    @ExcelProperty("商品描述")
    private String goodsDesc;

    @ExcelProperty("预算单价(未税)")
    private String unitBudgetPriceNaked;

    @ExcelProperty("需求数量")
    private Integer demandNum;

    @ExcelProperty("内部物料号")
    private String maraMatnr;

    @ExcelProperty("备注")
    private String remark;

    @ExcelIgnore
    private String supplierCode;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("报价品牌")
    private String referBrandName;

    @ExcelProperty("报价规格型号")
    private String specInfo;

    @ExcelProperty("标杆商品")
    private String standardSkuUrl;

    @ExcelProperty("税率")
    private Integer taxRate;

    @ExcelProperty("货期(天)")
    private Integer deliveryTime;

    @ExcelProperty("未税供货价(元)")
    private BigDecimal goodsPactNakedPrice;

    @ExcelProperty("含税供货价(元)")
    private BigDecimal goodsPactPrice;

    @ExcelProperty("未税平台售价(元)")
    private BigDecimal goodsSaleNakedPrice;

    @ExcelProperty("含税平台售价(元)")
    private BigDecimal goodsSalePrice;

    @ExcelProperty("报价时间")
    private String createTime;

    @ExcelProperty("供应商sku")
    private String goodsSku;

    @ExcelProperty("商品编码")
    private String goodsCode;
}
