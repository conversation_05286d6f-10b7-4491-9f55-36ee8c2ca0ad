package com.ly.yph.api.product.ext.jd.general.controller;

import cn.hutool.core.util.StrUtil;
import com.ly.yph.api.goods.common.SupplierConstants;
import com.ly.yph.api.product.ext.jd.context.SupplierCodeContextHolder;
import com.ly.yph.api.product.ext.jd.dto.reponse.JdGetMsgResp;
import com.ly.yph.api.product.ext.jd.general.config.JDGeneralConfig;
import com.ly.yph.api.product.ext.jd.general.entity.BackupJdGeneralSkuidInfoEntity;
import com.ly.yph.api.product.ext.jd.general.processor.*;
import com.ly.yph.api.product.ext.jd.general.service.BackupJdGeneralSkuidInfoService;
import com.ly.yph.api.product.ext.jd.general.service.JdGeneralMessageService;
import com.ly.yph.api.product.ext.jd.service.JDRemoteProxy;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.thread.ThreadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Slf4j
@Api(tags = "商品处理 - JD 积分 流程处理")
@RequestMapping("/jd-general-processor")
public class GeneralProcessController {
    @Resource
    private GeneralQueueMsgProcessor skuSrc;
    @Resource
    private GeneralInstoreProcessor instoreSrv;

    @Resource
    private GeneralPriceProcessor priceSrv;

    @Resource
    private JdGeneralMessageService msgSrv;
    @Resource
    private BackupJdGeneralSkuidInfoService skuSrv;
    @Resource
    private GeneralStockProcessor stockProc;
    @Resource
    private GeneralShelvesProcessor sheProc;
    @Resource
    private GeneralCanSaleProcessor cansaleSrv;
    @Resource
    private GeneralImageProcessor imageSrv;
    @Resource
    private GeneralGoodsValidator validateSrv;
    @Resource
    private GeneralGoodsStore goodStore;
    @Resource
    private GeneralCategoryProcessor cateProc;
    @Resource
    private JDRemoteProxy jdRemoteProxy;
    @Resource
    private JDGeneralConfig jdGeneralConfig;
    @GetMapping("validate")
    @ApiOperation("校验商品")
    public ServiceResult<?> validateProcessor(Integer count) {
        validateSrv.validate(count);
        return ServiceResult.succ();
    }

    @GetMapping("store")
    @ApiOperation("转换到标准商品")
    //最后一步
    public ServiceResult<?> storeProcessor(Integer count) {
        goodStore.store(count);
        return ServiceResult.succ();
    }

    @GetMapping("to-queue")
    //第二步
    @ApiOperation("转换到消息详情表queue_msg_supplier_product_process_info")
    public ServiceResult<?> queueProcessor(Integer count) {
        skuSrc.process(count);
        return ServiceResult.succ();
    }

    @GetMapping("in-store")
    //第三步
    @ApiOperation("转换到商品备份表backup_jd_goods")
    public ServiceResult<?> inStoreProcessor(Integer count) {
        instoreSrv.process(count);
        return ServiceResult.succ();
    }

    @GetMapping("image")
    @ApiOperation("校验图片")
    public ServiceResult<?> imageProcessor(Integer count) {
        imageSrv.process(count);
        return ServiceResult.succ();
    }

    @GetMapping("price")
    @ApiOperation("校验价格")
    public ServiceResult<?> priceProcessor(Integer count) {

        priceSrv.process(count);

        return ServiceResult.succ();
    }

    @GetMapping("cansale")
    @ApiOperation("校验可售")
    public ServiceResult<?> canSaleProcessor(Integer count) {

        cansaleSrv.process(count);

        return ServiceResult.succ();
    }

    @GetMapping("stock")
    @ApiOperation("校验库存")
    public ServiceResult<?> stockProcessor(Integer count) {
        stockProc.process(count);
        return ServiceResult.succ();
    }

    @GetMapping("shelves")
    @ApiOperation("校验上下架")
    public ServiceResult<?> shelvesProcessor(Integer count) {
        sheProc.process(count);
        return ServiceResult.succ();
    }

    @GetMapping("category")
    @ApiOperation("校验分类")
    public ServiceResult<?> categoryProcessor(Integer count) {
        cateProc.process(count);
        return ServiceResult.succ();
    }

    @ApiOperation("查看京东消息")
    @GetMapping("getMessage")
    public ServiceResult<List<JdGetMsgResp>> getMessage(String msgTypes ,String supplierCode) {
        SupplierCodeContextHolder.setSupplierCodeProxy(supplierCode);
        List<JdGetMsgResp> result;
        try {
            result = jdRemoteProxy.getMessage(Arrays.stream(msgTypes.split(",")).map(Long::valueOf).collect(Collectors.toList()));
        } finally {
            SupplierCodeContextHolder.clear();
        }
        return ServiceResult.succ(result);
    }

    @ApiOperation(value = "京东消息")
    // 第一步
    @GetMapping("message")
    public ServiceResult<?> jdMessage(String msgTypes) {
        List<JDGeneralConfig.GeneralConfig> configList = jdGeneralConfig.getConfigList();
        configList.forEach(item -> {
            SupplierCodeContextHolder.setSupplierCodeProxy(item.getCode());
            try {
                msgSrv.pollMessage(Arrays.stream(msgTypes.split(",")).map(Long::valueOf).collect(Collectors.toList()));
            } finally {
                SupplierCodeContextHolder.clear();
            }
        });
        return ServiceResult.succ();
    }

    @ApiOperation(value = "京东消息指定供应商编码")
    @GetMapping("messageBySupplierCode")
    public ServiceResult<?> jdMessageBySupplierCode(String msgTypes, String supplierCode) {
        if (StrUtil.isBlank(supplierCode)) {
            supplierCode = SupplierConstants.JD_006_CODE;
        }
        SupplierCodeContextHolder.setSupplierCodeProxy(supplierCode);
        try {
            msgSrv.pollMessage(Arrays.stream(msgTypes.split(",")).map(Long::valueOf).collect(Collectors.toList()));
        } finally {
            SupplierCodeContextHolder.clear();
        }
        return ServiceResult.succ();
    }

    @ApiOperation(value = "京东消息")
    @GetMapping("put-sku")
    public ServiceResult<?> putSku(@RequestBody List<Long> skus,String supplierCode) {
        ThreadUtil.getIoIntenseTargetThreadPool().execute(() -> {
            var sList = skus.stream().map(item -> {
                var sEnt = new BackupJdGeneralSkuidInfoEntity();
                sEnt.setSkuId(String.valueOf(item));
                sEnt.setIsProcess(false);
                sEnt.setSupplierCode(supplierCode);
                return sEnt;
            }).collect(Collectors.toList());
            skuSrv.saveBatch(sList);
        });
        return ServiceResult.succ();
    }
}
