package com.ly.yph.api.openapi.v1;

import cn.hutool.core.util.StrUtil;
import com.ly.yph.api.openapi.common.LogUtils;
import com.ly.yph.api.openapi.config.OpenAPIConfig;
import com.ly.yph.api.openapi.context.OrganizationCodeContextHolder;
import com.ly.yph.api.openapi.v1.dto.OpenPushDeliveryDto;
import com.ly.yph.api.openapi.v1.dto.OpenReceiptPackageByPurchaseNumberDto;
import com.ly.yph.api.openapi.v1.service.OpenDeliveryService;
import com.ly.yph.api.openapi.v1.vo.delivery.OpenDeliveryVo;
import com.ly.yph.api.order.dto.DpcaExportDeliveryQueryDto;
import com.ly.yph.api.product.ext.common.manage.RemoteInfoManage;
import com.ly.yph.api.system.annotations.OperateLog;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.excel.util.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * OPEN API - 发货
 *
 * <AUTHOR>
 * @date 2024/11/22 10:51
 */
@RestController
@Api(tags = "OPEN API - 发货")
@RequestMapping("/v1/delivery")
@Slf4j
public class OpenDeliveryController {

    @Resource
    private RemoteInfoManage mng;

    @Resource
    private LogUtils logUtils;
    @Resource
    private OpenDeliveryService openDeliverySrv;

    @PostMapping("pushDelivery")
    @ApiOperation("推送发货消息")
    @DataPermission(enable = false)
    @ShenyuSpringCloudClient
    public ServiceResult<?> pushDelivery(@Validated @RequestBody OpenPushDeliveryDto dto) {
        openDeliverySrv.pushDelivery(dto.getDeliveryId());
        return ServiceResult.succ();
    }

    @GetMapping("getDeliveryById")
    @ApiOperation("根据id查询发货消息")
    @DataPermission(enable = false)
    @ShenyuSpringCloudClient
    public ServiceResult<OpenDeliveryVo> getDeliveryById(@RequestParam("deliveryId") Long deliveryId) {
        return ServiceResult.succ(openDeliverySrv.getDeliveryById(deliveryId, OrganizationCodeContextHolder.getOrganizationCode()));
    }

    @PostMapping("receiptPackageByPurchaseNumber")
    @ApiOperation("根据采购单收货")
    @DataPermission(enable = false)
    @ShenyuSpringCloudClient
    public ServiceResult<?> receiptPackageByPurchaseNumber(@Validated @RequestBody OpenReceiptPackageByPurchaseNumberDto dto) {
        openDeliverySrv.receiptPackageByPurchaseNumber(dto.getPurchaseNumber(), OrganizationCodeContextHolder.getOrganizationCode());
        return ServiceResult.succ();
    }

    @GetMapping("/get-delivery-track")
    @ApiOperation("获取物流轨迹")
    @DataPermission(enable = false)
    @ShenyuSpringCloudClient
    @OperateLog(enable = false)
    public ServiceResult<?> getDeliveryTrack(String orderNo, String packageId, String supplier) {
        this.logUtils.createLog(OpenAPIConfig::getConfirmOrder, () -> {
            final String infoModule = "OpenDeliveryController";
            final String infoFunc = "get-delivery-track";
            final String infoMsg = StrUtil.format("获取物流轨迹[{},{},{}]", orderNo, packageId, supplier);
            log.info(StrUtil.format("[{}][{}]:{}", infoModule, infoFunc, infoMsg));
        });
        return ServiceResult.succ(mng.getDeliveryDetail(orderNo, packageId, supplier), "获取物流信息详情");
    }

    @GetMapping("/dpcaGetDelivery")
    @ApiOperation("提供神龙获取物流信息")
    @DataPermission(enable = false)
    public ServiceResult dpcaGetDelivery(String purchaseNumber, String begin, String end) {
        return ServiceResult.succ(openDeliverySrv.dpcaGetDelivery(purchaseNumber, begin, end));
    }

    @GetMapping("/exportDpcaGetDelivery")
    @ApiOperation("导出神龙物流信息")
    @DataPermission(enable = false)
    public ServiceResult exportDpcaGetDelivery(HttpServletResponse response,  String purchaseNumber, String begin, String end)  throws IOException {
        List<DpcaExportDeliveryQueryDto> dpcaDeliveryQueryDtos = openDeliverySrv.exportDpcaGetDelivery(purchaseNumber, begin, end);
        ExcelUtils.write(response, "神龙物流数据导出.xls", "数据", DpcaExportDeliveryQueryDto.class, dpcaDeliveryQueryDtos);
        return ServiceResult.succ();
    }
}
