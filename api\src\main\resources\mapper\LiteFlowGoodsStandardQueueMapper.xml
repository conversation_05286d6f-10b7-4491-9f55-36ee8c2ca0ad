<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.goods.mapper.LiteFlowGoodsStandardQueueMapper">

    <resultMap type="com.ly.yph.api.goods.entity.LiteFlowGoodsStandardQueueEntity" id="LiteFlowGoodsStandardQueueMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_idd" jdbcType="INTEGER"/>
        <result property="goodsCode" column="goods_code" jdbcType="VARCHAR"/>
        <result property="isStandard" column="is_standard" jdbcType="INTEGER"/>
        <result property="isLiteFlow" column="is_lite_flow" jdbcType="INTEGER"/>
        <result property="isEnable" column="is_enable" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO lite_flow_goods_standard_queue
        (goods_id,goods_code,is_standard,creator,modifier,tenant_id)
        VALUES
        <foreach collection="saveList" item="item" separator=",">
            (#{item.goodsId},
             #{item.goodsCode},
             #{item.isStandard},
             #{item.creator},
            #{item.modifier},
            #{item.tenantId})
        </foreach>
    </insert>

    <select id="selectAllList" resultType="com.ly.yph.api.goods.entity.LiteFlowGoodsStandardQueueEntity">
        SELECT
            id,
            goods_code
        FROM lite_flow_goods_standard_queue
        WHERE is_standard = 0
        limit #{limit}
    </select>

    <!-- <select id="selectAllListByLiteFlow" resultType="com.ly.yph.api.goods.entity.StandardProduct"> -->
    <select id="selectAllListByLiteFlow" resultType="com.ly.yph.api.goods.entity.ShopGoods">
        SELECT
        p.goods_id,
        p.goods_code,
        p.goods_sku,
        p.supplier_code,
        p.tenant_id
        FROM lite_flow_goods_standard_queue l
        INNER JOIN shop_goods p on p.goods_code = l.goods_code
        WHERE l.is_standard = 1
        AND l.is_lite_flow = 0
        AND p.audit_state = 1
        order by l.update_time
        limit #{limit}
    </select>

    <delete id="delByCreateTimeBefore">
        DELETE FROM lite_flow_goods_standard_queue
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL 10 DAY)
    </delete>

    <update id="batchUpdateStandardOk"> UPDATE lite_flow_goods_standard_queue SET is_standard = 1 WHERE id IN <foreach
            collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateLiteFlowOk"> UPDATE lite_flow_goods_standard_queue SET is_lite_flow = 1 WHERE goods_code IN <foreach
            collection="goodsCodeList" item="goodsCode" open="(" separator="," close=")">
            #{goodsCode}
        </foreach> AND is_lite_flow = 0 </update>

</mapper>