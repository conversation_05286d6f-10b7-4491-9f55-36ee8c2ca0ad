package com.ly.yph.api.goods.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/16 19:50
 */
@Data
@ApiModel("商品管理商品信息Vo")
public class GoodsManageVo {

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID",dataType = "Long")
    private Long goodsId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码",dataType = "String")
    private String goodsCode;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "订货编码",dataType = "String")
    private String goodsSku;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述",dataType = "String")
    private String goodsDesc;

    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类",dataType = "String")
    private String goodsClassNames;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "供应商编码",dataType = "String")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称",dataType = "String")
    private String supplierName;

    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型 0电商 1独立供应商",dataType = "Integer")
    private Integer supplierType;

    /**
     * 商品认证标签
     */
    @ApiModelProperty(value = "平台认证标签",dataType = "String")
    private String goodsLabel;

    /**
     * 企业认证标签
     */
    @ApiModelProperty(value = "企业认证标签",dataType = "String")
    private String companyLabel;

    /**
     * 商品认证标签
     */
    @ApiModelProperty(value = "商品状态 0待转换 1审核中 2已上架 3已下架 4上架失败",dataType = "String")
    private Integer goodsState;

    /**
     * 商品审核状态
     */
    @ApiModelProperty(value = "0待审核 1审核通过 2驳回",dataType = "String")
    private Integer auditState;

    /**
     * 审核状态说明
     */
    @ApiModelProperty(value = "审核状态说明",dataType = "String")
    private String auditStateDesc;

    @ApiModelProperty(value = "租户名称",dataType = "String")
    private String tenantName;

    @ApiModelProperty(value = "AI标准化 0否 1是",dataType = "Integer")
    private Integer isAiStandard;

    @ApiModelProperty(value = "销售客户端 0:全端 1:B端 2:C端")
    private Integer saleClient;
    @ApiModelProperty(value = "商品类型(1:实物商品 2:虚拟商品 3:服务)")
    private Integer goodsModel;

    @ApiModelProperty(value = "供应商TYPE：1、云中鹤  2、兑吧  3...", dataType = "String")
    private String virtualSupplierType;

}
