package com.ly.yph.api.orderlifecycle.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.json.JSONUtil;
import com.ly.yph.api.orderlifecycle.dto.fix.*;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoFixService;
import com.ly.yph.api.orderlifecycle.utils.EnhancedIPHashSnowflakeGenerator;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.excel.util.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;


@Slf4j
@SaCheckLogin
@RestController
@Api(tags = "订单池fix")
@RequestMapping("purchaseOrderInfoPoolFix")
public class PurchaseOrderInfoFixController {
    @Resource
    private PurchaseOrderInfoFixService purchaseOrderInfoFixService;

    @ApiOperation("修复东风商城账单池订单明细id数")
//    @GetMapping("fixBillPoolOrderDetailId")
    public ServiceResult<String> fixBillPoolOrderDetailId() {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixBillPoolOrderDetailId());
    }

    @ApiOperation("订单明细数据初始化")
//    @GetMapping("fixOrderPool")
    public ServiceResult<String> fixOrderPool(String endTime, Long tenantId) {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixOrderPool(endTime, tenantId));
    }

    @ApiOperation("订单明细初始化发货，妥投，售后，收货")
//    @GetMapping("fixDelivery")
    public ServiceResult<String> fixDelivery(Integer orderSalesChannel) {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixDelivery(orderSalesChannel));
    }

    @ApiOperation("修复2.0侧非sap账单数据")
//    @GetMapping("fixBillForNewMall")
    public ServiceResult<String> fixBillForNewMall(Integer orderSalesChannel) {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixBillForNewMall(orderSalesChannel));
    }

    @ApiOperation("修复2.0侧 sap账单数据")
//    @GetMapping("fixSapBillForNewMall")
    public ServiceResult<String> fixSapBillForNewMall() {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixSapBillForNewMall());
    }


    @ApiOperation("修复共享数据2.0验收数据")
//    @GetMapping("fixOutCheckForDfg")
    public ServiceResult<String> fixOutCheckForDfg() {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixOutCheckForDfg());
    }

    @ApiOperation("修复1.0账单数据")
//    @PostMapping("fixOldMallBill")
    public ServiceResult<String> fixOldMallBill(@RequestBody FixOldBillDto fixOldBillDto){
        log.info("入参：{}", JSONUtil.toJsonStr(fixOldBillDto));
        return ServiceResult.succ(purchaseOrderInfoFixService.fixOldMallBill(fixOldBillDto));
    }

    @ApiOperation("修复1.0外部验收数据")
//    @PostMapping("fixOldSapCheck")
    public ServiceResult<String> fixOldSapCheck(@RequestBody List<OldCheckDataDto> oldCheckDataDtoList){
        log.info("入参：{}", JSONUtil.toJsonStr(oldCheckDataDtoList));
        return ServiceResult.succ(purchaseOrderInfoFixService.fixOldSapCheck(oldCheckDataDtoList));
    }

    @ApiOperation("修复平台，京东供应商的发货，妥投相关数据")
//    @PostMapping("fixDeliveryTs")
    public ServiceResult<String> fixDeliveryTs(@RequestBody List<Long> deliveryIds){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixDeliveryTs(deliveryIds));
    }

    @ApiOperation("修复1.0账单结算状态")
//    @GetMapping("fixHistoryBillStatus")
    public ServiceResult<String> fixHistoryBillStatus(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHistoryBillStatus());
    }

     @ApiOperation("修复goodsCode变了的发货单")
//     @GetMapping("fixGoodsCodeDelivery")
     public ServiceResult<String> fixGoodsCodeDelivery(Integer orderSalesChannel){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixGoodsCodeDelivery(orderSalesChannel));
     }

     @ApiOperation("东风商城整单未迁移的")
//     @PostMapping("fixLeaveOrder")
     public ServiceResult<String> fixLeaveOrder(@RequestBody FixLeaveOrderDto FixLeaveOrderDto){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixLeaveOrder(FixLeaveOrderDto));
     }


     @ApiOperation("线下出账的福利账单数据修复")
//     @GetMapping("fixYflOfflineBill")
     public ServiceResult<String> fixYflOfflineBill(String activityCode){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixYflOfflineBill(activityCode));
     }

     @ApiOperation("修复福利丢失的订单")
//     @PostMapping("YflFixLeaveOrder")
     public ServiceResult<String> YflFixLeaveOrder(@RequestBody FixLeaveOrderDto FixLeaveOrderDto ){
        log.info("FixLeaveOrderDto:{}",JSONUtil.toJsonStr(FixLeaveOrderDto));
        return ServiceResult.succ(purchaseOrderInfoFixService.YflFixLeaveOrder(FixLeaveOrderDto));
     }

    @ApiOperation("修复历史结算数据结算数据")
//    @GetMapping("fixHistorySettle")
    public ServiceResult<String> fixHistorySettle(Integer orderSalesChannel){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHistorySettle(orderSalesChannel));
    }

    @ApiOperation("修复历史已开票未付款的数据")
//    @GetMapping("fixInvoicedNoSettle")
    public ServiceResult<String> fixInvoicedNoSettle(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixInvoicedNoSettle());
    }

    @ApiOperation("修复东风商城已对账未开票的数据状态")
//    @GetMapping("fixReconciliationNoInvoice")
    public ServiceResult<String> fixReconciliationNoInvoice(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixReconciliationNoInvoice());
    }

    @ApiOperation("修复福利2.0线上历史已结算的账单数据")
//    @GetMapping("fixNewMallSettleBillYfl")
    public ServiceResult<String> fixNewMallSettleBillYfl(String billSn){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixNewMallSettleBillYfl(billSn));
    }

    @ApiOperation("导入供应商已结算的数据")
//    @PostMapping("importSupplierSettleData")
    public void importSupplierSettleData(@RequestParam("file") final MultipartFile file) throws IOException {
        purchaseOrderInfoFixService.importSupplierSettleData(file);
    }

    @ApiOperation("修复历史回款数据")
//    @GetMapping("fixHistorySupplierSettle")
    public ServiceResult<String> fixHistorySupplierSettle(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHistorySupplierSettle());
    }

    @ApiOperation("修复24年线上供应商账单结算数据")
//    @GetMapping("fixOnlieSupplierSettle")
    public ServiceResult<String> fixOnlieSupplierSettle(String billSn,Integer settleStatus){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixOnlineSupplierSettle(billSn,settleStatus));
    }

    @ApiOperation("修复采购渠道/采购场景字段")
//    @GetMapping("fixPurchaseOrderType")
    public ServiceResult<String> fixPurchaseOrderType(Integer orderSalesChannel) {
        return ServiceResult.succ(purchaseOrderInfoFixService.fixPurchaseOrderType(orderSalesChannel));
    }

    @ApiOperation("修复历史本田结算状态")
//    @GetMapping("fixHondaHistorySettle")
    public ServiceResult<String> fixHondaHistorySettle(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHondaHistorySettle());
    }

    @ApiOperation("修复东本2.0账单数据")
//    @GetMapping("fixHondaNewMallBill")
    public ServiceResult<String> fixHondaNewMallBill(String companyCode){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHondaNewMallBill(companyCode));
    }

    @ApiOperation("修复东本因缺失订单明细的发票")
//    @GetMapping("fixHondaInvoice")
    public ServiceResult<String> fixHondaInvoice(String invoiceApplyNumber){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHondaInvoice(invoiceApplyNumber));
    }

    @ApiOperation("修复福利线下主体发票")
//    @GetMapping("fixYflHistoryInvoiceBill")
    public ServiceResult<String> fixYflHistoryInvoiceBill(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixYflHistoryInvoiceBill());
    }

    @ApiOperation("修复东风商城线下主体发票")
//    @GetMapping("fixDfmallHistoryInvoiceBill")
    public ServiceResult<String> fixDfmallHistoryInvoiceBill(){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixDfmallHistoryInvoiceBill());
    }

    @ApiOperation("车主商城导入数据结算数据")
//    @PostMapping("importHondaOwnerData")
    public ServiceResult<?> importHondaOwnerData(@RequestParam("file") MultipartFile file) throws IOException {
        List<HondaOwnerSettleData> hondaOwnerSettleDataList = ExcelUtils.read(file, HondaOwnerSettleData.class);
        return ServiceResult.succ(purchaseOrderInfoFixService.importHondaOwnerData(hondaOwnerSettleDataList));
    }

    @ApiOperation("本田车主发票修复")
//    @PostMapping("fixHondaOwnerInvoice")
    public ServiceResult<String> fixHondaOwnerInvoice(HondaOwnerInvoiceDto hondaOwnerInvoiceDto){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHondaOwnerInvoice(hondaOwnerInvoiceDto));
    }

    @ApiOperation("本田车主实体外部单号修复")
//    @PostMapping("fixHondaOwnerOtherRelationNumber")
    public ServiceResult<String> fixHondaOwnerOtherRelationNumber(@RequestParam("file") MultipartFile file) throws IOException{
        List<HondaOwnerOtherRelationNumber> hondaOwnerOtherRelationNumberList = ExcelUtils.read(file, HondaOwnerOtherRelationNumber.class);
        return ServiceResult.succ(purchaseOrderInfoFixService.fixHondaOwnerOtherRelationNumber(hondaOwnerOtherRelationNumberList));
    }

    @ApiOperation("修复采购单未进生命周期数据")
    @GetMapping("/fixLeavePurchaseNumber")
    public ServiceResult<String> fixLeavePurchaseNumber(String purchaseNumber){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixLeavePurchaseNumber(purchaseNumber));
    }

    @ApiOperation("修复历史供应商账单生命周期结算状态数据")
    @GetMapping("/fixSupplierInvoice")
    public ServiceResult<String> fixSupplierInvoice(Long billId){
        return ServiceResult.succ(purchaseOrderInfoFixService.fixSupplierInvoice(billId));
    }

}
