package com.ly.yph.api.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2025/04/10 15:24:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "contract_change_logs", autoResultMap = true)
@ApiModel("合同变更记录表")
public class ContractChangeLogsEntity extends BaseEntity {
    /**
     * 系统相关字段
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("变更类型：1 启用 0 停用")
    private Integer changeType;

    @ApiModelProperty(value = "变更操作时间", dataType = "Date")
    private Date changeTime;

    @ApiModelProperty("操作人ID")
    private Long operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("操作人工号")
    private String operatorUserName;

    @ApiModelProperty("文件名称多个逗号分隔")
    private String fileName;

    @ApiModelProperty("文件url多个逗号分隔")
    private String fileUrl;

    @ApiModelProperty("变更理由")
    private String reason;

    @ApiModelProperty("组织id")
    private Long organizationId;

    @ApiModelProperty("租户编号")
    private Long tenantId;
}
