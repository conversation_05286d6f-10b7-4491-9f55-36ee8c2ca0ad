package com.ly.yph.api.virtualgoods.controller;

import com.ly.yph.api.openapi.enums.ErrorCodeConstants;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.virtualgoods.dto.QueryVirtualGoodsInfoDto;
import com.ly.yph.api.virtualgoods.dto.SaveVirtualGoodsDto;
import com.ly.yph.api.virtualgoods.dto.YZHGoodInfoParamDto;
import com.ly.yph.api.virtualgoods.service.ShopVirtualGoodsService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.tenant.core.util.TenantUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月10日
 */
@Api(tags = "商品管理 - 虚拟商品")
@RestController
@RequestMapping("/virtualGoods")
@Slf4j
public class ShopVirtualGoodsController {

    @Resource
    private SystemTenantService systemTenantService;
    @Resource
    private ShopVirtualGoodsService shopVirtualGoodsService;
    @GetMapping("/queryGoodsInfo")
    @ApiOperation("云中鹤-获取虚拟商品详情信息")
    public ServiceResult queryGoodsInfo(YZHGoodInfoParamDto goodsInfoParam) {
        List<QueryVirtualGoodsInfoDto> list = shopVirtualGoodsService.queryGoodsInfo(goodsInfoParam);
        return ServiceResult.succ(list);
    }

    @PostMapping("/saveVirtualGoods")
    @ApiOperation("保存虚拟商品")
    public ServiceResult saveVirtualGoods(@RequestBody SaveVirtualGoodsDto param) {
        List<Long> tenantIds = systemTenantService.getTenantIds();
        List<Exception> exs = new ArrayList<>(2);
        TenantUtils.execute(tenantIds, () -> {
            try {
                shopVirtualGoodsService.saveVirtualGoods(param);
            } catch (Exception ex1) {
                log.error("【saveVirtualGoods】 exception:",ex1);
                exs.add(ex1);
            }
        });

        StringBuilder sb = new StringBuilder();
        for (Exception ex : exs) {
            sb.append(ex.getMessage());
            sb.append(System.lineSeparator());
        }

        if (sb.length() > 0) {
            throw HttpException.exception(ErrorCodeConstants.API_INNER_ERROR, sb.toString());
        }
        return ServiceResult.succ();
    }

    @PostMapping("/processVirtualOrderState")
    @ApiOperation("轮询虚拟订单状态")
    public ServiceResult processVirtualOrderState() {
        return shopVirtualGoodsService.processVirtualOrderState();
    }

}
