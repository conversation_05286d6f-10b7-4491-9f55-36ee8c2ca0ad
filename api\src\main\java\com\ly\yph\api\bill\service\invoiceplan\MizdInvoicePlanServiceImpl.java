package com.ly.yph.api.bill.service.invoiceplan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.yph.api.bill.controller.plan.vo.*;
import com.ly.yph.api.bill.dal.dataobject.invoiceplan.MizdInvoicePlanDO;
import com.ly.yph.api.bill.dal.mysql.invoiceplan.MizdInvoicePlanMapper;
import com.ly.yph.api.common.BeanUtils;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.enums.BillStatusEnum;
import com.ly.yph.api.settlement.common.service.SettleShopBillService;
import com.ly.yph.api.settlement.supplier.dto.SupplierContractInfoDto;
import com.ly.yph.api.settlement.supplier.entity.SettleBudget;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill;
import com.ly.yph.api.settlement.supplier.service.SettleBudgetService;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.SystemContractEntity;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.tenant.core.aop.TenantIgnore;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import static com.ly.yph.api.bill.ErrorCodeConstants.*;

/**
 * 账单计划 Service 实现类 0未发起、1审批中、2审批通过、3审批驳回
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MizdInvoicePlanServiceImpl
    extends ServiceImpl<MizdInvoicePlanMapper, MizdInvoicePlanDO>
    implements MizdInvoicePlanService {

  private static final String DATE_FORMAT = "yyyyMMdd";
  private static final String SEQUENCE_KEY = "payment_order_sequence";
  private static final String STATUS_PENDING = "未发起";
  private static final String REJECT = "审批驳回";
  private static final String PASS ="审批通过";

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private MizdInvoicePlanMapper invoicePlanMapper;
    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;
    @Resource
    private SettleBudgetService settleBudgetService;

    /**
     * 创建发票计划
     *
     * @param createReqVO 创建请求对象，包含发票计划信息
     * @return 创建的发票计划ID
     */
  @Override
  public Long createInvoicePlan(MizdInvoicePlanSaveReqVO createReqVO) {
    // 创建支付计划对象
    MizdInvoicePlanDO invoicePlan = BeanUtils.toBean(createReqVO, MizdInvoicePlanDO.class);

    // 新生成支付单号
    invoicePlan.setPayNo(_g_number());
    if (StrUtil.isBlank(invoicePlan.getPurchasingManagerCode())) {
      invoicePlan.setPurchasingManagerCode("07802");
    }

    // 设置合同金额，保持用户传递的值
    invoicePlan.setContractAmount(createReqVO.getContractAmount());

    // 执行金额验证（单账单时）
    //  _validate_money_low_case(invoicePlan);

    // 插入数据库
    invoicePlanMapper.insert(invoicePlan);
    // 返回主键ID
    return invoicePlan.getId();
  }

    @Override
    @Transactional
    public void createInvoicePlanByBill(Long billId) {
        SettleShopBill settleShopBill = settleShopBillService.getById(billId);

        MizdInvoicePlanDO invoicePlan = new MizdInvoicePlanDO();
        invoicePlan.setPayNo(_g_number());
        invoicePlan.setPayPlanStatus(STATUS_PENDING);
        invoicePlan.setInvoiceNumber(settleShopBill.getBillSn());
        invoicePlan.setInvoiceName(settleShopBill.getCustomerName() +
                "-" + settleShopBill.getCheckYear()
                + String.format("%02d月", settleShopBill.getCheckMonth()) + "验收款");

        //写死的
        invoicePlan.setPayType("项目型");
        invoicePlan.setPayEntity("联友智连科技有限公司");
        invoicePlan.setProjectBelongModule(2);
        invoicePlan.setPurchaseApplyCreatorDeptName("联采云部");
        invoicePlan.setTypeOfCurrency("CNY");

        //最后一张票的上传时间+供应商档案账期字段

        List<SupplierInvoiceBill> supplierInvoiceBillList = supplierInvoiceBillService.getBaseMapper().getLastTimeForInvoiceUpload(billId);

        Optional<SupplierInvoiceBill> max = supplierInvoiceBillList.stream().filter(e -> e.getInvoiceUploadTime() != null)
                .max(Comparator.comparing(SupplierInvoiceBill::getInvoiceUploadTime));

        LocalDateTime lastDateTime = max.get().getInvoiceUploadTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        ShopSupplier supplier = settleShopBillService.getBaseMapper().getSupplierBillDate(settleShopBill.getCustomerCode());
        LocalDateTime payDate = lastDateTime.plusDays(supplier.getSettlementPeriod() == null ? 0 : supplier.getSettlementPeriod());
        invoicePlan.setPayDate(payDate);

        QueryWrapper<SettleBudget> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleBudget::getSupplierCode, supplier.getSupplierCode());
        SettleBudget settleBudget = settleBudgetService.getOne(queryWrapper);

        //磊哥给的配置
        invoicePlan.setProjectNo(settleBudget.getProjectNo());
        invoicePlan.setProjectName(settleBudget.getProjectName());
        invoicePlan.setBudgetNumber(settleBudget.getBudgetNumber());

        //取供应商档案
        invoicePlan.setPurchasingManager("王磊");
        invoicePlan.setPurchasingManagerCode("00271");

        invoicePlan.setBank(supplier.getInvoiceBank());
        invoicePlan.setReceiveCompany(settleShopBill.getCustomerName());
        invoicePlan.setReceiveAccount(supplier.getInvoiceBankAccount());
        invoicePlan.setRoutingNumber(supplier.getInvoiceBankAccount());

        List<SupplierContractInfoDto> systemContractEntityList = settleShopBillService.getBaseMapper().getSupplierContract(supplier.getSupplierCode());
        if(CollectionUtil.isNotEmpty(systemContractEntityList)){
            invoicePlan.setBusinessContractId(systemContractEntityList.get(0).getContractCode());
            invoicePlan.setContractName(systemContractEntityList.get(0).getContractName());
            invoicePlan.setContractUrl(systemContractEntityList.get(0).getContractScannedUrl());
        }


        invoicePlan.setContractAmount(settleShopBill.getSettlementAmount());
        invoicePlan.setMoneyLowCase(settleShopBill.getSettlementAmount().toString());
        invoicePlan.setPayPercent(new BigDecimal("100"));

        List<Long> times = getBaseMapper().getMizdInvoicePlanTimes(settleShopBill.getBillSn());
        invoicePlan.setPaymentPhases(settleShopBill.getCheckYear() + "年" +
                settleShopBill.getCheckMonth() + "月账单" + "-第"+times.size()+1 + "次支付");


        //下拉选可修改
        invoicePlan.setHedge("no");
        invoicePlan.setPayMethod("电汇");
        invoicePlan.setPaymentRroperty("no");

        invoicePlan.setRemark(invoicePlan.getInvoiceName() + ",结算金额为" +
                settleShopBill.getSettlementAmount() + ",结算主体为" +
                invoicePlan.getPayEntity());

        //附件相关
        invoicePlan.setBillUrl(settleShopBill.getExcelUrl());
        invoicePlan.setCheckUrl(settleShopBill.getCheckUrl());
        invoicePlan.setInvoiceUrl(
                supplierInvoiceBillList.stream().map(SupplierInvoiceBill::getInvoiceUrl)
                .filter(url->url != null && !url.isEmpty())
                .collect(Collectors.joining(","))
        );

        invoicePlanMapper.insert(invoicePlan);

    }

    /**
   * 更新发票计划
   *
   * @param updateReqVO 更新请求对象，包含发票计划ID和更新信息
   * @throws HttpException 如果发票计划不存在或状态不是未发起，则抛出异常
   */
  @Override
  public void updateInvoicePlan(MizdInvoicePlanSaveReqVO updateReqVO) {
    // 校验存在且可编辑
    validate_invoice_plan_editable(updateReqVO.getId());

    // 创建更新对象
    MizdInvoicePlanDO updateObj = BeanUtils.toBean(updateReqVO, MizdInvoicePlanDO.class);

    // 设置合同金额，保持用户传递的值
    updateObj.setContractAmount(updateReqVO.getContractAmount());

    // 执行金额验证（单账单时）
    // _validate_money_low_case(updateObj);

    // 更新数据库
    invoicePlanMapper.updateById(updateObj);

  }

    public void validate_to_update_bill(Long id,String payPlanStatus) {
        if (!PASS.equals(payPlanStatus)) {
            log.info("非通过状态！");
            return;
        }
        MizdInvoicePlanDO plan = invoicePlanMapper.selectById(id);
        SettleShopBill settleShopBill = settleShopBillService.getBaseMapper().selectByBillSn(plan.getInvoiceNumber());
        if (settleShopBill == null) {
            log.info("未查询到账单");
            return;
        }



        if (plan.getPayPercent().compareTo(new BigDecimal("100")) != 0) {
            log.info("支付比例不足100，不更新账单");
            return;
        }



        if (settleShopBill.getSettlementAmount().compareTo(plan.getContractAmount()) != 0) {
            log.info("合同金额与账单结算金额不一致！");
            return;
        }

        if(BillStatusEnum.SETTLED.getCode().equals(settleShopBill.getBillStatus())){
            //已结清的不需要再去调用支付
            log.info("账单状态不满足支付条件");
            return;
        }

        settleShopBillService.supplierSettlement(settleShopBill.getBillSn());
    }

    /**
   * 删除发票计划
   *
   * @param id 发票计划ID
   * @throws HttpException 如果发票计划不存在或状态不是未发起，则抛出异常
   */
  @Override
  public void deleteInvoicePlan(Long id) {
    // 校验存在且可删除
    validate_invoice_plan_editable(id);
    // 删除
    invoicePlanMapper.deleteById(id);
  }

  /**
   * 验证发票计划是否可编辑
   *
   * @param id 发票计划ID
   * @throws HttpException 如果发票计划不存在或状态不是未发起，则抛出异常
   */
  private void validate_invoice_plan_editable(Long id) {
    // 根据ID查询发票计划
    MizdInvoicePlanDO plan = invoicePlanMapper.selectById(id);
    // 如果发票计划不存在
    if (plan == null) {
      // 抛出发票计划不存在异常
      throw HttpException.exception(INVOICE_PLAN_NOT_EXISTS);
    }
    // 如果发票计划状态不是未发起
    if (!REJECT.equals(plan.getPayPlanStatus())
        && !STATUS_PENDING.equals(plan.getPayPlanStatus())) {
      // 抛出发票状态更新错误异常
      throw HttpException.exception(INVOICE_STATUS_UPDATE_ERROR);
    }
  }

  /**
   * 根据ID获取发票计划
   *
   * @param id 发票计划ID
   * @return 发票计划对象
   */
  @Override
  public MizdInvoicePlanDO getInvoicePlan(Long id) {
    // 根据ID查询发票计划
    return invoicePlanMapper.selectById(id);
  }

  /**
   * 获取发票计划分页
   *
   * @param pager 分页请求对象
   * @param pageReqVO 分页查询条件
   * @return 分页响应对象，包含发票计划列表
   */
  @Override
  public PageResp<MizdInvoicePlanDO> getInvoicePlanPage(
      PageReq pager, MizdInvoicePlanPageReqVO pageReqVO) {
    return invoicePlanMapper.selectPage(pager, pageReqVO);
  }

  /**
   * 更新发票计划状态
   *
   * @param updateReqVO 更新请求对象，包含支付单号和新的支付计划状态
   * @throws HttpException 如果发票计划不存在，则抛出异常
   */
  @Override
  public void updateInvoiceStatus(MizdInvoicePlanStatusUpdateReqVO updateReqVO) {
    // 根据支付单号查询发票计划列表
    List<MizdInvoicePlanDO> mizdInvoicePlanDOS =
        this.getBaseMapper().selectByPayNo(updateReqVO.getPayNo());
    // 如果发票计划列表为空，则抛出发票计划不存在的异常
    if (mizdInvoicePlanDOS == null) {
      throw HttpException.exception(INVOICE_PLAN_NOT_EXISTS);
    }
    // 更新发票计划状态
    invoicePlanMapper.updatePayPlanStatusByPayNo(
        updateReqVO.getPayPlanStatus(), updateReqVO.getPayNo(),
            updateReqVO.getExternalBusinessOrderNumber(),updateReqVO.getCurrentStepName());

      // 执行自动完成账单支付的更新
      validate_to_update_bill(mizdInvoicePlanDOS.get(0).getId(), updateReqVO.getPayPlanStatus());
  }

  /** 生成发票编号 */
  private String _g_number() {
    RMap<String, Integer> dateSequenceMap = redissonClient.getMap(SEQUENCE_KEY);

    // 获取当前日期
    String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT));

    // 清理今天以前的键值对
    _clean_old_data(dateSequenceMap, currentDate);

    // 获取或初始化当前日期的流水号
    Integer sequence = dateSequenceMap.get(currentDate);
    if (sequence == null) {
      sequence = 0;
    }

    // 自增流水号
    sequence++;

    // 将新的流水号存入 Redis
    dateSequenceMap.put(currentDate, sequence);

    // 生成订单号
    return "ZFDH_" + currentDate + "_" + String.format("%03d", sequence);
  }

  /**
   * 清理今天以前的键值对
   *
   * @param dateSequenceMap Redis 中的 Map
   * @param currentDate 当前日期
   */
  private void _clean_old_data(RMap<String, Integer> dateSequenceMap, String currentDate) {
    // 获取 Map 中的所有键
    Set<String> keys = dateSequenceMap.keySet();

    // 遍历键，删除今天以前的键值对
    for (String key : keys) {
      if (key.compareTo(currentDate) < 0) {
        dateSequenceMap.remove(key);
      }
    }
  }

  /**
   * 导入支付计划
   *
   * @param list 支付计划导入列表
   */
  public void importInvoicePlan(List<MizdInvoicePlanImportVO> list) {
    List<MizdInvoicePlanDO> need_create = new ArrayList<>();
    for (MizdInvoicePlanImportVO item : list) {
      _validate_data(item);

      // 创建支付计划对象
      MizdInvoicePlanDO createItem = BeanUtils.toBean(item, MizdInvoicePlanDO.class);
      createItem.setPayNo(_g_number());

      // 设置合同金额，保持用户传递的值
      createItem.setContractAmount(item.getContractAmount());

      // 执行金额验证（单账单时）
      // _validate_money_low_case(createItem);

      need_create.add(createItem);
    }
    this.saveBatch(need_create);
  }

  /**
   * 计算指定账单编号列表的总结算金额
   *
   * @param invoice_numbers 账单编号列表
   * @return 总结算金额
   */
  private BigDecimal _calculate_total_settlement_amount(List<String> invoice_numbers) {
    BigDecimal total = BigDecimal.ZERO;
    for (String invoice_number : invoice_numbers) {
      SettleShopBill settleShopBill =
          settleShopBillService.getBaseMapper().selectByBillSn(invoice_number);
      if (settleShopBill != null) {
        total = total.add(settleShopBill.getAmount());
      }
    }
    return total;
  }

    @Override
    public PageResp<MizdInvoicePlanDO> getInvoicePlanPageExt(
        PageReq pager, MizdInvoicePlanPageReqExtVO pageReqVO) {
      return invoicePlanMapper.selectPageExt(pager, pageReqVO);
    }

  /**
   * 获取已导入有效支付金额总和
   *
   * @param invoice_numbers 账单编号列表
   * @return 已导入有效支付金额总和
   */
  public BigDecimal get_already_imported_amount(List<String> invoice_numbers) {
    if (invoice_numbers == null || invoice_numbers.isEmpty()) {
      return BigDecimal.ZERO;
    }
    BigDecimal sum = invoicePlanMapper.sumValidImportedAmount(invoice_numbers);
    return sum != null ? sum : BigDecimal.ZERO;
  }

  /**
   * 执行金额验证逻辑（仅单账单时）
   *
   * @param invoice_plan 支付计划对象
   */
  private void _validate_money_low_case(MizdInvoicePlanDO invoice_plan) {
    String invoice_number_str = invoice_plan.getInvoiceNumber();
    if (invoice_number_str == null || invoice_number_str.trim().isEmpty()) {
      // 如果不存在直接返回
      return;
    }

    String[] invoice_numbers = invoice_number_str.split(";");
    List<String> invoice_numbers_list = new ArrayList<>();
    for (String inv : invoice_numbers) {
      if (inv.trim().isEmpty()) continue;
      invoice_numbers_list.add(inv.trim());
    }

    if (invoice_numbers_list.isEmpty()) {
      throw HttpException.exception(INVOICE_NUMBER_EMPTY);
    }

    if (invoice_numbers_list.size() > 1) {
      // 多个账单对应一个支付计划，跳过金额校验
      return;
    }

    // 单个账单，进行金额校验
    String single_invoice = invoice_numbers_list.get(0);

    // 计算总结算金额
    BigDecimal total_settlement_amount =
        _calculate_total_settlement_amount(Collections.singletonList(single_invoice));

    // 计算已导入的有效支付金额
    BigDecimal already_imported_amount =
        get_already_imported_amount(Collections.singletonList(single_invoice));

    // 当前支付计划的金额（使用 moneyLowCase 进行验证）
    BigDecimal current_money = new BigDecimal(invoice_plan.getMoneyLowCase());

    // 验证：当前金额 <= 总结算金额 - 已导入金额
    if (current_money.compareTo(total_settlement_amount.subtract(already_imported_amount)) > 0) {
      throw HttpException.exception(AMOUT_LIMIT_EXIST);
    }
  }

  /**
   * 导入支付计划时的数据验证
   *
   * @param item 支付计划导入项
   */
  private static void _validate_data(MizdInvoicePlanImportVO item) {
    // Assert.notBlank(item.getPayPlanStatus(), () ->
    // HttpException.exception(IMPORT_PARAMS_NULL, "支付计划状态"));
    Assert.notBlank(item.getPayType(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "支付类型"));
    Assert.notBlank(item.getPayEntity(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "支付主体"));
    Assert.notBlank(
        item.getPurchasingManager(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "采购经理"));
    Assert.notNull(item.getPayDate(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "付款日期"));
    Assert.notBlank(item.getProjectNo(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "项目编号"));
    Assert.notBlank(
        item.getProjectName(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "项目名称"));
    Assert.notBlank(
        item.getBusinessContractId(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "合同编号"));
    Assert.notBlank(
        item.getContractName(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "合同名称"));
    Assert.notNull(item.getPayPercent(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "付款比例"));
    Assert.notNull(
        item.getPaymentPhases(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "付款期数"));
    Assert.notBlank(item.getBank(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "开户行"));
    Assert.notBlank(
        item.getReceiveCompany(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "收款单位"));
    Assert.notBlank(
        item.getReceiveAccount(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "账号"));
    // Assert.notNull(item.getContractAmount(), () ->
    // HttpException.exception(IMPORT_PARAMS_NULL, "合同金额"));
    Assert.notNull(
        item.getMoneyLowCase(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "金额小写"));
    Assert.notBlank(item.getHedge(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "是否冲账"));
    Assert.notBlank(item.getPayMethod(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "付款方式"));
    Assert.notBlank(
        item.getPaymentRroperty(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "是否预付"));
    Assert.notNull(
        item.getProjectBelongModule(), () -> HttpException.exception(IMPORT_PARAMS_NULL, "内部项目标识"));
  }
}
