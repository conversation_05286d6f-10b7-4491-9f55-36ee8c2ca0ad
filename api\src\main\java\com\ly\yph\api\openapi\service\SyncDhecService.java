package com.ly.yph.api.openapi.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.customization.common.Constant;
import com.ly.yph.api.customization.config.DhecConfig;
import com.ly.yph.api.customization.entity.ShopDhecMaterial;
import com.ly.yph.api.customization.entity.ShopDhecUse;
import com.ly.yph.api.customization.service.ShopDhecMaterialService;
import com.ly.yph.api.customization.service.ShopDhecUseService;
import com.ly.yph.api.goods.dto.StandardThreeTierClassDto;
import com.ly.yph.api.goods.dto.SyncShopUseListDto;
import com.ly.yph.api.goods.dto.YphStandardClassSaveDto;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.enums.GoodsAuthMode;
import com.ly.yph.api.goods.enums.GoodsLabelEnum;
import com.ly.yph.api.goods.manage.GoodsUpDownManage;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.openapi.v1.dto.ImportDhecMaterialDto;
import com.ly.yph.api.openapi.v1.vo.SyncDhecMaterialReq;
import com.ly.yph.api.organization.controller.auth.vo.AuthLoginRespVO;
import com.ly.yph.api.organization.entity.SocialUserDO;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.enums.LoginLogTypeEnum;
import com.ly.yph.api.organization.enums.LoginResultEnum;
import com.ly.yph.api.organization.enums.SocialTypeEnum;
import com.ly.yph.api.organization.mapper.SocialUserMapper;
import com.ly.yph.api.organization.service.AdminAuthService;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.organization.service.SystemUsersService;
import com.ly.yph.api.organization.util.AesCbcUtil;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.core.base.CommonStatusEnum;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.http.RequestHelper;
import com.ly.yph.core.util.DistinctUtil;
import com.ly.yph.core.util.StringHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.yph.api.organization.enums.WeComConstants.SESSION_KEY;
import static com.ly.yph.api.organization.enums.WeComConstants.WX_ACCESS_TOKEN;
import static com.ly.yph.core.base.exception.SystemErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024年01月19日
 */
@Service
@Slf4j
@RefreshScope
public class SyncDhecService {

    @Value("${customize.dfs.default-image-url}")
    private String defaultGoodsImage;

    @Resource
    DhecConfig dhecConfig;

    @Resource
    private ShopBrandService shopBrandService;

    @Resource
    private YphStandardClassService yphStandardClassService;

    @Resource
    private ShopGoodsService shopGoodsService;

    @Resource
    private CodeGeneral codeGeneral;

    @Resource
    private ShopSupplierService shopSupplierService;

    @Resource
    private ShopGoodsDetailService shopGoodsDetailService;

    @Resource
    private ShopGoodsPriceService shopGoodsPriceService;

    @Resource
    private ThreadPoolExecutor commonIoExecutors;

    @Resource
    private DfmallGoodsPoolService dfmallGoodsPoolService;

    @Resource
    private ShopDhecMaterialService shopDhecMaterialService;

    @Resource
    private ShopDhecUseService shopDhecUseService;

    @Resource
    private GoodsUpDownManage goodsUpDownManage;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RequestHelper requestHelper;

    @Resource
    private SystemUsersService userSrv;

    @Resource
    private AdminAuthService authService;
    @Resource
    private ShopGoodsSaleLabelService goodsSaleLabelSrv;

    @Resource
    private AdminAuthService authSrv;

    @Resource
    private SocialUserMapper socialUserMp;

    @Autowired
    private SystemOrganizationService systemOrganizationService;



    @Transactional(rollbackFor = Exception.class)
    public ServiceResult<?> syncDhecMaterial(List<SyncDhecMaterialReq> syncDhecMaterialReqs) {
        List<ShopDhecMaterial> materials = BeanUtil.copyToList(syncDhecMaterialReqs, ShopDhecMaterial.class);
        if (CollectionUtil.isEmpty(materials)) {
            return ServiceResult.error("参数不允许为空！");
        }
        materials.stream().forEach(item -> {
            //商品名称+描述+商品型号
            item.setGoodsDesc(item.getGoodsName() + "-" + item.getGoodsDesc() + "-" + item.getSpecArray());
            item.setFirstClass(CompanyEnum.DHEC.getCompanyCode() + item.getFirstClass());
            item.setSecondClass(CompanyEnum.DHEC.getCompanyCode() + item.getSecondClass());
            item.setThirdClass(CompanyEnum.DHEC.getCompanyCode() + item.getThirdClass());
            item.setGoodsDhecCode(CompanyEnum.DHEC.getCompanyCode() + item.getGoodsDhecCode());
        });
        shopDhecMaterialService.saveBatch(materials);
        return ServiceResult.succ();
    }

    @Transactional(rollbackFor = Exception.class)
    public ServiceResult<?> batchImportDhecMaterial(List<ImportDhecMaterialDto> importDhecMaterialDtos) {
        List<ShopDhecMaterial> materials = BeanUtil.copyToList(importDhecMaterialDtos, ShopDhecMaterial.class);
        if (CollectionUtil.isEmpty(materials)) {
            return ServiceResult.error("参数不允许为空！");
        }
        materials.stream().forEach(item -> {
            //商品名称+描述+商品型号
            item.setGoodsDesc(item.getGoodsName() + "-" + item.getGoodsDesc() + "-" + item.getSpecArray());
            item.setFirstClass(CompanyEnum.DHEC.getCompanyCode() + item.getFirstClass());
            item.setSecondClass(CompanyEnum.DHEC.getCompanyCode() + item.getSecondClass());
            item.setThirdClass(CompanyEnum.DHEC.getCompanyCode() + item.getThirdClass());
            item.setGoodsDhecCode(CompanyEnum.DHEC.getCompanyCode() + item.getGoodsDhecCode());
        });
        shopDhecMaterialService.saveBatch(materials);
        return ServiceResult.succ();
    }


    public void processShopDhecMaterial() {
        List<ShopDhecMaterial> materials = shopDhecMaterialService.list(new LambdaQueryWrapper<ShopDhecMaterial>()
                .eq(ShopDhecMaterial::getProcessStatus, 0)
                .last("limit " + dhecConfig.getGoodsSyncMax())
        );
        if (CollectionUtil.isEmpty(materials)) {
            log.info("【processShopDhecMaterial】暂无需要待处理商品数据！");
            return;
        }

        //过滤重复推送的商品
        List<String> goodsSkus = materials.stream().map(ShopDhecMaterial::getGoodsDhecCode).distinct().collect(Collectors.toList());
        List<ShopGoods> esg = shopGoodsService.list(new LambdaQueryWrapper<ShopGoods>().in(ShopGoods::getGoodsSku, goodsSkus));
        List<String> hsg = esg.stream().map(ShopGoods::getGoodsSku).collect(Collectors.toList());
        List<ShopDhecMaterial> haveMater = materials.stream().filter(item -> hsg.contains(item.getGoodsDhecCode())).collect(Collectors.toList());
        materials.removeIf(item -> hsg.contains(item.getGoodsDhecCode()));

        //没有分类创建分类 改为他们映射到我们的分类
        List<ShopDhecMaterial> result = createClass(materials);
        List<String> dhecGoodsCode = result.stream().map(ShopDhecMaterial::getGoodsDhecCode).collect(Collectors.toList());
        materials.removeIf(item -> dhecGoodsCode.contains(item.getGoodsDhecCode()));

        //供应商没有就存入A供应商，供应商没有就存入B供应商
        fillSupplierCode(materials);

        //商品数据弄完之后，保存到商品主表，同步到商品池
        List<ShopDhecMaterial> egd = createShopGoods(materials);

        //更新处理状态
        List<ShopDhecMaterial> updateProcess = Lists.newArrayList();
        for (ShopDhecMaterial material : materials) {
            ShopDhecMaterial shopDhecMaterial = new ShopDhecMaterial();
            shopDhecMaterial.setId(material.getId());
            shopDhecMaterial.setProcessStatus(1);
            updateProcess.add(shopDhecMaterial);
        }
        egd.addAll(result);
        egd.addAll(haveMater);
        for (ShopDhecMaterial material : egd) {
            ShopDhecMaterial shopDhecMaterial = new ShopDhecMaterial();
            shopDhecMaterial.setId(material.getId());
            shopDhecMaterial.setProcessStatus(2);
            updateProcess.add(shopDhecMaterial);
        }
        shopDhecMaterialService.updateBatchById(updateProcess);
    }

    public void processExpiredMaterial() {
        List<ShopDhecMaterial> materials = shopDhecMaterialService.list(new LambdaQueryWrapper<ShopDhecMaterial>().lt(ShopDhecMaterial::getEndDate, new Date()));
        if (CollectionUtil.isEmpty(materials)) {
            log.info("【processExpiredMaterial】暂无需要待处理过期物料数据！");
            return;
        }
        QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ShopGoods::getGoodsSku, materials.stream().map(ShopDhecMaterial::getGoodsDhecCode));
        List<ShopGoods> goods = shopGoodsService.list(queryWrapper);
        if (CollectionUtil.isEmpty(goods)) {
            log.info("【processExpiredMaterial】暂未查询到商品主数据！");
            return;
        }
        shopGoodsService.shopGoodsDown(goods.stream().map(ShopGoods::getGoodsCode).collect(Collectors.toList()), "发动机物料商品过期下架");
    }

    public List<ShopDhecMaterial> createClass(List<ShopDhecMaterial> syncDhecMaterialReqs) {
        List<ShopDhecMaterial> result = Lists.newArrayList();
        List<YphStandardClassSaveDto> yphStandardClassSaveDtos = Lists.newArrayList();
        for (ShopDhecMaterial material : syncDhecMaterialReqs) {
            YphStandardClassSaveDto yphStandardClassSaveDto = new YphStandardClassSaveDto();
            yphStandardClassSaveDto.setClassCode(material.getFirstClass());
            yphStandardClassSaveDto.setClassName(material.getFirstClassName());
            yphStandardClassSaveDto.setParentClassCode("0");
            yphStandardClassSaveDto.setPurchaseType(2);
            yphStandardClassSaveDto.setTypeCode("A002001");
            yphStandardClassSaveDto.setTypeName("通用物资");
            yphStandardClassSaveDto.setClassDataSource(CompanyEnum.DHEC.getCompanyCode());

            YphStandardClassSaveDto yphStandardClassSaveDto2 = new YphStandardClassSaveDto();
            yphStandardClassSaveDto2.setClassCode(material.getSecondClass());
            yphStandardClassSaveDto2.setClassName(material.getSecondClassName());
            yphStandardClassSaveDto2.setParentClassCode(material.getFirstClass());
            yphStandardClassSaveDto2.setPurchaseType(2);
            yphStandardClassSaveDto2.setTypeCode("A002001");
            yphStandardClassSaveDto2.setTypeName("通用物资");
            yphStandardClassSaveDto.setClassDataSource(CompanyEnum.DHEC.getCompanyCode());

            YphStandardClassSaveDto yphStandardClassSaveDto3 = new YphStandardClassSaveDto();
            yphStandardClassSaveDto3.setClassCode(material.getThirdClass());
            yphStandardClassSaveDto3.setClassName(material.getThirdClassName());
            yphStandardClassSaveDto3.setParentClassCode(material.getSecondClass());
            yphStandardClassSaveDto3.setPurchaseType(2);
            yphStandardClassSaveDto3.setTypeCode("A002001");
            yphStandardClassSaveDto3.setTypeName("通用物资");
            yphStandardClassSaveDto.setClassDataSource(CompanyEnum.DHEC.getCompanyCode());


            yphStandardClassSaveDtos.add(yphStandardClassSaveDto);
            yphStandardClassSaveDtos.add(yphStandardClassSaveDto2);
            yphStandardClassSaveDtos.add(yphStandardClassSaveDto3);
        }
        List<YphStandardClassSaveDto> fscList = yphStandardClassSaveDtos.stream().filter(DistinctUtil.distinctByKey(x -> x.getClassCode())).collect(Collectors.toList());
        for (YphStandardClassSaveDto yphStandardClassSaveDto : fscList) {
            try {
                YphStandardClassEntity classEty = yphStandardClassService.selectByCode(yphStandardClassSaveDto.getClassCode());
                if (Objects.isNull(classEty)) {
                    yphStandardClassService.createOrUpdateClass(yphStandardClassSaveDto);
                }
            } catch (Exception e) {
                log.error("DHEC分类创建失败", e);
                result.addAll(syncDhecMaterialReqs.stream().filter(item ->
                        item.getFirstClass().equals(yphStandardClassSaveDto.getClassCode())
                                || item.getSecondClass().equals(yphStandardClassSaveDto.getClassCode())
                                || item.getThirdClass().equals(yphStandardClassSaveDto.getClassCode())).collect(Collectors.toList()));
            }
        }
        return result;
    }

    public void fillSupplierCode(List<ShopDhecMaterial> syncDhecMaterialReqs) {
        for (ShopDhecMaterial material : syncDhecMaterialReqs) {
            if (StrUtil.isBlank(material.getSupplierCode())) {
                material.setSupplierCode(dhecConfig.getNoSupplierCode());
            } else {
                material.setSupplierCode(dhecConfig.getSupplierCode());
            }
        }
    }

    public List<ShopDhecMaterial> createShopGoods(List<ShopDhecMaterial> syncDhecMaterialReqs) {
        //需要上架商品
        List<ShopDhecMaterial> filterResult = filterGoods(syncDhecMaterialReqs);

        List<ShopDhecMaterial> egd = Lists.newArrayList();
        //查询供应商
        List<ShopSupplier> shopSupplierList = shopSupplierService.list(new LambdaQueryWrapper<ShopSupplier>().in(ShopSupplier::getSupplierCode, Arrays.asList(dhecConfig.getSupplierCode(), dhecConfig.getNoSupplierCode())));
        Map<String, ShopSupplier> supplierMap = shopSupplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity()));

        List<String> goodIds = new ArrayList<>();

        filterResult.stream().forEach(dhecMaterial -> {
            //物料编码作为商品SKU
            String goodsSku = dhecMaterial.getGoodsDhecCode();
            ShopGoods shopGoods = BeanUtil.copyProperties(dhecMaterial, ShopGoods.class);
            String goodsCode = codeGeneral.getProductCode(CompanyEnum.DHEC.getCompanyCode());
            shopGoods.setGoodsCode(goodsCode);
            shopGoods.setGoodsSku(goodsSku);
            //商品名称+描述+商品型号
            if (StringHelper.IsEmptyOrNull(dhecMaterial.getMaterialsCode())) {
                dhecMaterial.setMaterialsCode("-");
            }
            shopGoods.setGoodsDesc(dhecMaterial.getGoodsName() + " "
                    + dhecMaterial.getBrandName() + " "
                    + dhecMaterial.getMaterialsCode() + " "
                    + dhecMaterial.getSaleUnit() + " "
                    + dhecMaterial.getSpecArray());

            // 品牌
            ShopBrand shopBrand = shopBrandService.selectByBrandName(dhecMaterial.getBrandName());
            if (shopBrand == null) {
                shopBrand = new ShopBrand();
                shopBrand.setBrandName(dhecMaterial.getBrandName());
                shopBrandService.saveShopBrand(shopBrand);
            }
            shopGoods.setBrandId(shopBrand.getBrandId().toString());
            shopGoods.setBrandName(dhecMaterial.getBrandName());

            StandardThreeTierClassDto classDto = yphStandardClassService.queryThreeTierClassByCode(dhecMaterial.getThirdClass());
            if (null == classDto) {
                egd.add(dhecMaterial);
                return;
            }
            shopGoods.setFirstLevelGcid(classDto.getClassIdLv1().toString());
            shopGoods.setFirstClass(classDto.getClassCodeLv1());
            shopGoods.setFirstClassName(classDto.getClassNameLv1());
            shopGoods.setSecondLevelGcid(classDto.getClassIdLv2().toString());
            shopGoods.setSecondClass(classDto.getClassCodeLv2());
            shopGoods.setSecondClassName(classDto.getClassNameLv2());
            shopGoods.setThirdLevelGcid(classDto.getClassIdLv3().toString());
            shopGoods.setThirdClass(classDto.getClassCodeLv3());
            shopGoods.setThirdClassName(classDto.getClassNameLv3());
            shopGoods.setMaterialsCode(dhecMaterial.getSpecArray());
            shopGoods.setAuditState(1);
            shopGoods.setShelvesState(1);
            shopGoods.setAuthMode(GoodsAuthMode.PURCHASE_ALLIANCE.getCode());
            shopGoods.setGoodsLabel(GoodsLabelEnum.DHEC_FRAMEWORK.getCode());

            ShopSupplier shopSupplier = supplierMap.get(dhecMaterial.getSupplierCode());
            shopGoods.setSupplierName(shopSupplier.getSupplierShortName());
            shopGoods.setSupplierType(shopSupplier.getSupplierType());
            shopGoods.setOrganizationId(shopSupplier.getOrganizationId());
            shopGoodsService.save(shopGoods);

            ShopGoodsDetail shopGoodsDetail = new ShopGoodsDetail();
            shopGoodsDetail.setGoodsCode(shopGoods.getGoodsCode());
            shopGoodsDetail.setGoodsSku(shopGoods.getGoodsSku());
            shopGoodsDetail.setGoodsMoq(dhecMaterial.getGoodsMoq());
            if (StrUtil.isBlank(dhecMaterial.getImageArray())) {
                shopGoodsDetail.setGoodsImage(defaultGoodsImage);
            } else {
                shopGoodsDetail.setGoodsImage(dhecMaterial.getImageArray());
            }
            shopGoodsDetail.setGoodsImageMore(dhecMaterial.getImageArray());
            shopGoodsDetail.setOrganizationId(shopSupplier.getOrganizationId());
            shopGoodsDetailService.save(shopGoodsDetail);

            ShopGoodsPrice shopGoodsPrice = new ShopGoodsPrice();
            shopGoodsPrice.setGoodsCode(shopGoods.getGoodsCode());
            shopGoodsPrice.setGoodsSku(shopGoods.getGoodsSku());
            shopGoodsPrice.setOrganizationId(shopSupplier.getOrganizationId());
            shopGoodsPrice.setGoodsPactPrice(dhecMaterial.getGoodsUnitPriceTax());
            shopGoodsPrice.setGoodsPactNakedPrice(dhecMaterial.getGoodsUnitPriceNaked());
            shopGoodsPrice.setGoodsOriginalPrice(dhecMaterial.getGoodsUnitPriceTax());
            shopGoodsPrice.setGoodsOriginalNakedPrice(dhecMaterial.getGoodsUnitPriceNaked());
            shopGoodsPrice.setGoodsSalePrice(dhecMaterial.getGoodsUnitPriceTax());
            shopGoodsPrice.setGoodsSaleNakedPrice(dhecMaterial.getGoodsUnitPriceNaked());
            shopGoodsPriceService.saveItem(shopGoodsPrice);

            SystemOrganization organizationByCode = systemOrganizationService.getOrganizationByCode(CompanyEnum.DHEC.getCompanyCode());

            ShopGoodsSaleLabelEntity labelEntity = new ShopGoodsSaleLabelEntity();
            labelEntity.setAuthMode(GoodsAuthMode.PURCHASE_ALLIANCE.getCode());
            labelEntity.setGoodsLabel(GoodsLabelEnum.DHEC_FRAMEWORK.getCode());
            labelEntity.setGoodsId(shopGoods.getGoodsId());
            labelEntity.setOrganizationId(organizationByCode.getId());
            goodsSaleLabelSrv.save(labelEntity);
            goodIds.add(shopGoods.getGoodsId().toString());
        });

        if (CollectionUtil.isNotEmpty(goodIds)) {
            dfmallGoodsPoolService.saveGoodsId(Long.valueOf(dhecConfig.getDefaultPoolId()), CollectionUtil.join(goodIds, ","));
        }
        return egd;
    }

    private List<ShopDhecMaterial> filterGoods(List<ShopDhecMaterial> syncDhecMaterialReqs) {
        List<String> supplierGoods = syncDhecMaterialReqs.stream()
                .filter(item -> item.getSupplierCode().equals(dhecConfig.getSupplierCode()))
                .map(ShopDhecMaterial::getGoodsDhecCode).collect(Collectors.toList());

        List<String> noSupplierGoods = syncDhecMaterialReqs.stream().filter(item -> item.getSupplierCode().equals(dhecConfig.getNoSupplierCode()))
                .map(ShopDhecMaterial::getGoodsDhecCode).collect(Collectors.toList());

        List<ShopGoods> goods = Lists.newArrayList();
        List<ShopGoods> noGoods = Lists.newArrayList();

        if (CollectionUtil.isNotEmpty(supplierGoods)) {
            LambdaQueryWrapperX<ShopGoods> queryGoods = new LambdaQueryWrapperX<>();
            queryGoods.in(ShopGoods::getGoodsSku, supplierGoods)
                    .eq(ShopGoods::getSupplierCode, dhecConfig.getSupplierCode());
            goods = shopGoodsService.list(queryGoods);
        }
        if (CollectionUtil.isNotEmpty(noSupplierGoods)) {
            LambdaQueryWrapperX<ShopGoods> queryNoGoods = new LambdaQueryWrapperX<>();
            queryNoGoods.in(ShopGoods::getGoodsSku, noSupplierGoods)
                    .eq(ShopGoods::getSupplierCode, dhecConfig.getNoSupplierCode());
            noGoods = shopGoodsService.list(queryNoGoods);
        }
        if (CollectionUtil.isNotEmpty(goods)) {
            List<String> goodsCodes = goods.stream().map(ShopGoods::getGoodsCode).collect(Collectors.toList());
            supplierGoods.removeIf(item -> goodsCodes.contains(item));
        }
        if (CollectionUtil.isNotEmpty(noGoods)) {
            List<String> noGoodsCodes = noGoods.stream().map(ShopGoods::getGoodsCode).collect(Collectors.toList());
            noSupplierGoods.removeIf(item -> noGoodsCodes.contains(item));
        }
        noSupplierGoods.addAll(supplierGoods);
        return syncDhecMaterialReqs.stream().filter(item -> noSupplierGoods.contains(item.getGoodsDhecCode())).collect(Collectors.toList());
    }

    public ServiceResult<?>
    syncDhecUse(List<SyncShopUseListDto> syncShopUseListDto) {
        List<ShopDhecUse> shopDhecUseList = BeanUtil.copyToList(syncShopUseListDto, ShopDhecUse.class);
        shopDhecUseService.saveOrUpdateBatch(shopDhecUseList);
        return ServiceResult.succ();
    }

    public Map<?, ?> login(String code) {
        HashMap<Object, Object> result = new HashMap<>();
        String accessToken = authSrv.getWxToken("DHEC_" + WX_ACCESS_TOKEN, dhecConfig.getCorpid(), dhecConfig.getCorpsecret());
        //https:qyapi.weixin.qq.com/cgi-bin/miniprogram/jscode2session?access_token=ACCESS_TOKEN&js_code=CODE&grant_type=authorization_code
        String userid = authSrv.code2Session(accessToken, code);
        result.put("userId", userid);
        SocialUserDO socialUserDO = socialUserMp.selectByTypeAndOpenid(SocialTypeEnum.DHEC_WECHAT_ENTERPRISE.getType(), userid);
        //是否绑定过第三方用户
        result.put("login", ObjectUtil.isNull(socialUserDO) ? 0 : 1);
        if (ObjectUtil.isNull(socialUserDO)) {
            return result;
        }
        String username = socialUserDO.getUsername();
        SystemUsers user = userSrv.getUserByUsername(username);
        if (user == null) {
            log.error("东本发动机企业微信登录接口 username{}", socialUserDO.getUsername());
            throw new HttpException(USER_NOT_EXISTS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            authSrv.createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL, LoginResultEnum.USER_DISABLED);
            throw new HttpException(AUTH_LOGIN_USER_DISABLED);
        }
        //copy 登录逻辑
        StpUtil.login(user.getId().toString() + ":" + user.getTenantId().toString());
        authSrv.registerUserSession(user);
        //创建 Token 令牌，记录登录日志
        authSrv.createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL, LoginResultEnum.SUCCESS);
        val tokenInfo = StpUtil.getTokenInfo();
        AuthLoginRespVO authLogin = AuthLoginRespVO.builder().accessToken(tokenInfo.getTokenValue())
                .userId(user.getId()).tokenTimeout(tokenInfo.getTokenTimeout()).build();
        result.put("authLogin", authLogin);
        return result;
    }

    public AuthLoginRespVO qyWxLoginBind(String encryptedData, String iv, String userId) {
        String sessionKey = stringRedisTemplate.opsForValue().get(SESSION_KEY);
        if (StrUtil.isEmptyIfStr(sessionKey))
            throw new HttpException("企业微信登陆绑定失败,sessionKey 已过期");

        String decrypt = AesCbcUtil.decrypt(encryptedData, sessionKey, iv);
        if (StrUtil.isEmptyIfStr(decrypt))
            throw new HttpException("企业微信解析失败");

        JSONObject jsonObject = JSONUtil.parseObj(decrypt);
        String mobile = jsonObject.getStr("mobile");

        List<SystemUsers> userByMobile = userSrv.getUserByMobile(mobile);
        if (CollectionUtil.isEmpty(userByMobile)) {
            throw new HttpException(AUTH_MOBILE_NOT_EXISTS);
        }

        SystemUsers user = userByMobile.get(0);
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            authSrv.createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL, LoginResultEnum.USER_DISABLED);
            throw new HttpException(AUTH_LOGIN_USER_DISABLED);
        }
        //copy 登录逻辑
        StpUtil.login(user.getId().toString() + ":" + user.getTenantId().toString());
        authSrv.registerUserSession(user);
        //创建 Token 令牌，记录登录日志
        authSrv.createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL, LoginResultEnum.SUCCESS);
        val tokenInfo = StpUtil.getTokenInfo();

        //建立第三方绑定关系
        SocialUserDO socialUser = socialUserMp.selectByTypeAndUsername(SocialTypeEnum.DHEC_WECHAT_ENTERPRISE.getType(), user.getUsername());
        if (socialUser == null) {
            socialUser = new SocialUserDO();
            socialUser.setType(SocialTypeEnum.DHEC_WECHAT_ENTERPRISE.getType());
            socialUser.setOpenid(userId);
            socialUser.setToken(tokenInfo.getTokenValue());
            socialUser.setRawTokenInfo("");
            socialUser.setUsername(user.getUsername());
            socialUser.setNickname(user.getNickname());
            socialUser.setAvatar(user.getAvatar());
            socialUser.setRawUserInfo("");
            socialUser.setCode("");
            socialUserMp.insert(socialUser);
        }
        return AuthLoginRespVO.builder().accessToken(tokenInfo.getTokenValue())
                .userId(user.getId()).tokenTimeout(tokenInfo.getTokenTimeout()).build();
    }
}
