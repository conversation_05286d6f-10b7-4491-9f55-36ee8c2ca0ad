<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.bill.dal.mysql.invoiceplan.MizdInvoicePlanMapper">
    <select id="sumValidImportedAmount" parameterType="list" resultType="java.math.BigDecimal">
        SELECT
        SUM(contract_amount) AS total_imported_amount
        FROM
        mizd_invoice_plan
        WHERE
        invoice_number IN
        <foreach item="item" index="index" collection="invoiceNumbers" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND pay_plan_status IN ('审批中', '审批通过')
    </select>

    <select id="getMizdInvoicePlanTimes" resultType="java.lang.Long">
        select
        id
        from mizd_invoice_plan where is_enable =1 and invoice_number =#{invoiceNumber}
    </select>
</mapper>