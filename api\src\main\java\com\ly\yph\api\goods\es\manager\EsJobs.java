package com.ly.yph.api.goods.es.manager;

import javax.annotation.Resource;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.common.DeepseekService;
import com.ly.yph.api.goods.controller.vo.ProductDetailVO;
import com.ly.yph.api.goods.service.ProductService;
import com.ly.yph.core.base.exception.ErrorCode;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.tenant.core.aop.TenantIgnore;

import java.io.IOException;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * EsJobs 是一个定时任务类，用于定期刷新 Elasticsearch 索引。
 * 该类使用 Spring 的 @Service 注解标记为服务组件，并使用 @Slf4j 注解启用日志记录。
 * 
 * @RefreshScope 注解用于支持动态刷新配置。
 */
@Service
@Slf4j
@RefreshScope
public class EsJobs {
    @Value("${elasticsearch.process-status:start}")
    private String processStatus;

    /**
     * GoodsEsProcessor 是一个处理 Elasticsearch 操作的组件。
     * 通过 @Resource 注解注入 GoodsEsProcessor 实例。
     */
    @Resource
    private GoodsEsProcessor esProcessor;

    @Resource
    private GoodsLogEsProcessor logEsProcessor;
    @Resource
    private ProductService productService;
    @Resource
    private DeepseekService deepseekService;

    @Resource
    private SearchInfoManager searchInfoManager;
    /**
     * SUB_SIZE 是一个常量，表示每次处理的子任务大小。
     */
    public static final int SUB_SIZE = 100;

    /**
     * SCHEDULER_PERIOD 是一个常量，表示定时任务的执行周期（毫秒）。
     */
    private static final long SCHEDULER_PERIOD = 5000L;

    /**
     * schedulePeriodicRefresh 方法是一个定时任务，用于定期刷新 Elasticsearch 索引。 该方法使用 @Scheduled 注解配置为每隔
     * SCHEDULER_PERIOD 毫秒执行一次，并在初始延迟 SCHEDULER_PERIOD 毫秒后开始执行。
     * 
     * @TenantIgnore 注解用于忽略租户上下文。
     */
    @Scheduled(fixedDelay = SCHEDULER_PERIOD, initialDelay = SCHEDULER_PERIOD)
    @TenantIgnore
    public void schedulePeriodicRefresh() {
        if (processStatus.equals("start")) {
            esProcessor.schedulePeriodicRefresh();
        }
    }

    @Scheduled(fixedDelay = 1000L * 10, initialDelay = 1000L * 10)
    @TenantIgnore
    public void schedulePeriodicRefreshForLog() {
        logEsProcessor.schedulePeriodicRefresh();
    }

    // 一天执行一次
    @Scheduled(fixedDelay = 1000L * 60 * 60 * 24, initialDelay = 1000L * 60 * 60 * 24)
    @TenantIgnore
    public void schedulePeriodicRefreshForSearchInfo() {
        try {
            searchInfoManager.delete(12);
        } catch (Exception e) {
            log.error("清理搜索日志失败", e);
        }
    }
}
