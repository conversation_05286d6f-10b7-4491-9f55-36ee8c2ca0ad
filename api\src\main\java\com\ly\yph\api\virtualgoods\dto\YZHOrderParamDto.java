package com.ly.yph.api.virtualgoods.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月19日
 */
@Data
public class YZHOrderParamDto implements Serializable {
    private static final long serialVersionUID = 7916920849127542121L;

    /**
     * 客户方订单编号 可以为空。
     * 若后续其他的订单 API 接口想用客户自己的订单号来完成业务，这里建议最好是传
     */
    String tparOrderCode;
    /**
     * 客户方订单编号
     */
    String target;
    /**
     * 下单人账号
     */
    String account;
    /**
     * 订单购买的商品信息列表（目前只支
     * 持一个商品）
     */
    List<GoodsInfo> goodsList;

    @Data
    public static class GoodsInfo implements Serializable {

        private static final long serialVersionUID = -8101054690794959147L;

        String goodsSkuCode;

        Integer num;

        Double sellPrice;
    }
}
