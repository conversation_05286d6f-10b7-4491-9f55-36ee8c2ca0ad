package com.ly.yph.api.orderlifecycle.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.entity.ShopPurchaseOrder;
import com.ly.yph.api.order.enums.DeliveryStateEnum;
import com.ly.yph.api.order.service.ShopPurchaseOrderService;
import com.ly.yph.api.order.vo.check.OutCheckSapDetailVo;
import com.ly.yph.api.orderlifecycle.dto.OrderDetailPoolPreOrderDto;
import com.ly.yph.api.orderlifecycle.dto.PurchaseOrderInfoDto;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto;
import com.ly.yph.api.orderlifecycle.dto.UpdatePurchaseOrderInfoForConfirmDto;
import com.ly.yph.api.orderlifecycle.dto.fix.*;
import com.ly.yph.api.orderlifecycle.entity.OrderDetailPool;
import com.ly.yph.api.orderlifecycle.entity.PurchaseOrderInfoPool;
import com.ly.yph.api.orderlifecycle.enums.OrderPlacementScenarioEnum;
import com.ly.yph.api.orderlifecycle.enums.OrderSalesChannelEnum;
import com.ly.yph.api.orderlifecycle.enums.PurchaseChannelEnum;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleFactory;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleStrategy;
import com.ly.yph.api.orderlifecycle.mapper.PurchaseOrderInfoPoolMapper;
import com.ly.yph.api.orderlifecycle.utils.EnhancedIPHashSnowflakeGenerator;
import com.ly.yph.api.orderlifecycle.utils.OrderPoolPurchaseTypeUtils;
import com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto;
import com.ly.yph.api.settlement.common.entity.*;
import com.ly.yph.api.settlement.common.enums.BillCustomerTypeEnum;
import com.ly.yph.api.settlement.common.enums.CustomerPriceModeEnum;
import com.ly.yph.api.settlement.common.enums.ReconciliationStatusEnum;
import com.ly.yph.api.settlement.common.service.*;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;
import com.ly.yph.core.base.CommonCodeGeneral;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.excel.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PurchaseOrderInfoFixService {

    @Resource
    private SettleBillPoolService settleBillPoolService;

    @Resource
    private YflYamlConfig yflYamlConfig;

    @Resource
    private PurchaseOrderInfoPoolMapper purchaseOrderInfoPoolMapper;

    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;

    @Resource
    private OrderDetailPoolService orderDetailPoolService;

    @Resource
    private PurchaseOrderInfoFixService purchaseOrderInfoFixService;

    @Resource
    private SettleShopBillService settleShopBillService;

    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;

    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;

    @Resource
    private InvoiceBillService invoiceBillService;

    @Resource
    private SettleBillLifeCycleService settleBillLifeCycleService;

    @Resource
    private InvoiceDetailBillService invoiceDetailBillService;

    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;

    @Resource
    private CommonCodeGeneral commonCodeGeneral;
    @Resource
    private OrderLifeCycleFactory orderLifeCycleFactory;

    @Transactional
    public String fixBillPoolOrderDetailId() {
        List<String> companyList = settleBillPoolService.getBaseMapper().getBillPoolCompanyList(1L);

        for (String company : companyList) {
            List<SettleBillPool> settleBillPoolList = settleBillPoolService.getBaseMapper()
                    .getBillPoolForOrderDetailId(1L, company);
            if (CollectionUtils.isEmpty(settleBillPoolList)) {
                continue;
            }
            settleBillPoolService.updateBatchById(settleBillPoolList);
        }

        List<String> yflCompanyList = settleBillPoolService.getBaseMapper()
                .getBillPoolCompanyList(yflYamlConfig.getTenantId());

        for (String company : yflCompanyList) {
            List<SettleBillPool> settleBillPoolList = settleBillPoolService.getBaseMapper()
                    .getBillPoolForOrderDetailId(yflYamlConfig.getTenantId(), company);
            if (CollectionUtils.isEmpty(settleBillPoolList)) {
                continue;
            }
            settleBillPoolService.updateBatchById(settleBillPoolList);
        }

        return "东风商城账单池订单明细id修复完成";
    }

    @Transactional
    public String fixOrderPool(String endTime, Long tenantId) {
        Integer orderSaleChannel;
        Integer orderPlacementScenario;
        if (tenantId.equals(1L)) {
            orderSaleChannel = OrderSalesChannelEnum.DFMALL.getCode();
            orderPlacementScenario = OrderPlacementScenarioEnum.MROPURCHASING.getCode();
        } else if (tenantId.equals(yflYamlConfig.getTenantId())) {
            orderSaleChannel = OrderSalesChannelEnum.YFLMALL.getCode();
            orderPlacementScenario = OrderPlacementScenarioEnum.EMPLOYEEBENEFITS.getCode();
        } else {
            throw new ParameterException("输入的租户异常！");
        }
        List<String> companyCodeList = purchaseOrderInfoPoolMapper.getFixOrderPoolCompanyList(tenantId, endTime);

        for (String companyCode : companyCodeList) {
            log.info("开始查询企业：{}", companyCode);
            List<PurchaseOrderInfoDto> purchaseOrderInfoPoolList = purchaseOrderInfoPoolMapper
                    .getFixPurchaseOrderInfo(tenantId, companyCode, endTime);
            log.info("企业采购单信息数：{}", purchaseOrderInfoPoolList.size());
            List<PurchaseOrderInfoPool> updatePurchaseOrderInfoPool = new ArrayList<>();
            purchaseOrderInfoPoolList.forEach(e -> {
                PurchaseOrderInfoPool orderInfoPool = DataAdapter.convert(e, PurchaseOrderInfoPool.class);
                orderInfoPool.setOrderSalesChannel(orderSaleChannel);
                // orderInfoPool.setOrderPlacementScenario(orderPlacementScenario);
                // 平台内结算
                if (e.getSapOrderType() == 0 || e.getSapOrderType() == 4) {
                    orderInfoPool.setIsOutSettle(0);
                } else {
                    orderInfoPool.setIsOutSettle(1);
                }

                orderInfoPool.setId(commonCodeGeneral.makeMysqlId());
                updatePurchaseOrderInfoPool.add(orderInfoPool);
            });

            Map<String, PurchaseOrderInfoPool> purchaseNumberMap = updatePurchaseOrderInfoPool.stream()
                    .collect(Collectors.toMap(PurchaseOrderInfoPool::getPurchaseNumber, Function.identity()));

            List<OrderDetailPool> orderDetailPoolList = purchaseOrderInfoPoolMapper.getFixOrderDetailPoolInfo(tenantId,
                    companyCode, endTime);
            log.info("订单明细数：{}", orderDetailPoolList.size());
            if (CollectionUtils.isEmpty(orderDetailPoolList)) {
                log.info("企业：{}未查询到有效的订单明细数据", companyCode);
                continue;
            }
            Map<String, List<OrderDetailPool>> orderDetailForPurchaseMap = orderDetailPoolList.stream()
                    .collect(Collectors.groupingBy(OrderDetailPool::getPurchaseNumber));

            List<String> errorPurchaseNumberList = new ArrayList<>();
            List<OrderDetailPool> orderDetailPoolListForSave = new ArrayList<>();

            for (String purchaseNumber : orderDetailForPurchaseMap.keySet()) {
                PurchaseOrderInfoPool purchaseOrderInfoPool = purchaseNumberMap.get(purchaseNumber);
                if (purchaseOrderInfoPool == null) {
                    errorPurchaseNumberList.add(purchaseNumber);
                } else {
                    List<OrderDetailPool> orderDetailPools = orderDetailForPurchaseMap.get(purchaseNumber);
                    orderDetailPools.forEach(e -> {
                        e.setPurchaseInfoId(purchaseOrderInfoPool.getId());
                        e.setOrderSalesChannel(purchaseOrderInfoPool.getOrderSalesChannel());
                        e.setId(commonCodeGeneral.makeMysqlId());
                    });
                    orderDetailPoolListForSave.addAll(orderDetailPools);
                }
            }
            List<PurchaseOrderInfoPool> validUpdatePurchaseOrderInfoPool = new ArrayList<>();
            List<String> orderDetailPurchaseNumberList = orderDetailPoolListForSave.stream()
                    .map(OrderDetailPool::getPurchaseNumber).distinct().collect(Collectors.toList());
            List<String> outPlatformList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderDetailPurchaseNumberList)) {
                validUpdatePurchaseOrderInfoPool = updatePurchaseOrderInfoPool.stream().filter(e -> {
                    List<String> collect = orderDetailPurchaseNumberList.stream()
                            .filter(c -> c.equals(e.getPurchaseNumber())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(collect)) {
                        outPlatformList.add(e.getPurchaseNumber());
                        return false;
                    } else {
                        return true;
                    }
                }).collect(Collectors.toList());
            }
            log.info("准备存储企业：{}的采购单数据：{}和订单明细的数据：{}", companyCode, validUpdatePurchaseOrderInfoPool.size(),
                    orderDetailPoolListForSave.size());
            log.info("过滤后，完全在外部结算的采购单集合有：{}个，具体为：{}", outPlatformList.size(), outPlatformList);
            purchaseOrderInfoPoolService.saveBatch(validUpdatePurchaseOrderInfoPool, 800);
            orderDetailPoolService.saveBatch(orderDetailPoolListForSave, 800);
            log.info("企业：{}数据更新完成", companyCode);
        }
        return "订单明细池数据修复成功！";
    }

    /**
     * 发货，妥投，售后，收货
     *
     * @param orderSalesChannel
     * @return
     */
    @Transactional
    public String fixDelivery(Integer orderSalesChannel) {
        List<String> companyCodeList = purchaseOrderInfoPoolMapper.getDeliveryCompanyCode(orderSalesChannel);

        Long tenantId;
        if (orderSalesChannel.equals(OrderSalesChannelEnum.DFMALL.getCode())) {
            tenantId = 1L;
        } else {
            tenantId = yflYamlConfig.getTenantId();
        }

        // 查询售后数据
        List<AfterSaleFix> afterSaleFixList = purchaseOrderInfoPoolMapper.getAfterSaleForFixPurchaseOrderInfo(tenantId);
        Map<String, List<AfterSaleFix>> afterSaleMap = afterSaleFixList.stream()
                .collect(Collectors.groupingBy(AfterSaleFix::getOrderDetailId));

        for (String companyCode : companyCodeList) {
            log.info("开始查询企业：{}的发货数据", companyCode);
            // 查询发货数据
            List<DeliveryDetailFix> deliveryList = purchaseOrderInfoPoolMapper
                    .getDeliveryDetailForFixPurchaseOrderInfo(companyCode, tenantId);
            if (CollectionUtils.isEmpty(deliveryList)) {
                log.info("企业：{}未发现相关的发货明细", companyCode);
                continue;
            }
            // 查询订单池数据
            List<OrderDetailPool> orderDetailPoolList = purchaseOrderInfoPoolMapper
                    .getOrderDetailForCompanyAndOrderSaleChannel(companyCode, orderSalesChannel, null);
            if (CollectionUtils.isEmpty(orderDetailPoolList)) {
                log.info("企业：{}订单池未查询到要修复的池数据", companyCode);
                continue;
            }

            Map<String, OrderDetailPool> orderDetailPoolMap = orderDetailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getOrderDetailId, Function.identity()));

            Map<String, List<DeliveryDetailFix>> deliveryMap = deliveryList.stream()
                    .collect(Collectors.groupingBy(DeliveryDetailFix::getOrderDetailId));

            List<OrderDetailPool> updateList = new ArrayList<>();
            for (String orderDetailId : orderDetailPoolMap.keySet()) {
                OrderDetailPool orderDetailPool = orderDetailPoolMap.get(orderDetailId);

                List<DeliveryDetailFix> deliveryDetailFixes = deliveryMap.get(orderDetailId);
                if (CollectionUtils.isEmpty(deliveryDetailFixes)) {
                    log.info("企业：{},订单：{}，明细id:{}无发货相关数据", companyCode, orderDetailPool.getOrderNumber(),
                            orderDetailId);
                    continue;
                }

                // 发货
                BigDecimal decimalDelivery = deliveryDetailFixes.stream().map(DeliveryDetailFix::getDeliveryNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 妥投
                List<DeliveryDetailFix> properDelivery = deliveryDetailFixes.stream()
                        .filter(e -> e.getSupDeliveryState().equals(DeliveryStateEnum.DELIVERY_OK.getCode()))
                        .collect(Collectors.toList());
                BigDecimal decimalProper = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(properDelivery)) {
                    decimalProper = properDelivery.stream().map(DeliveryDetailFix::getDeliveryNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // 收货
                BigDecimal receiveNum = deliveryDetailFixes.stream().map(DeliveryDetailFix::getReceivingNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 售后
                List<AfterSaleFix> afterSaleFixes = afterSaleMap.get(orderDetailId);
                BigDecimal decimalAfterSale = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(afterSaleFixes)) {
                    decimalAfterSale = afterSaleFixes.stream().map(AfterSaleFix::getReturnNum).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                }

                // 设置发货数据，妥投数据，售后数据，及相关的状态
                orderDetailPool.setDeliveryNum(decimalDelivery);
                orderDetailPool.setProperDeliveryNum(decimalProper);
                orderDetailPool.setCustomerReceiptNum(receiveNum);
                orderDetailPool.setAfterSaleNum(decimalAfterSale);

                BigDecimal deliveryFlag = orderDetailPool.getConfirmNum().subtract(decimalDelivery);
                BigDecimal afterSaleFlag = orderDetailPool.getConfirmNum().subtract(decimalAfterSale);
                BigDecimal receiveFlag = orderDetailPool.getConfirmNum().subtract(receiveNum)
                        .subtract(decimalAfterSale);

                if (deliveryFlag.compareTo(BigDecimal.ZERO) > 0) {
                    if (decimalDelivery.compareTo(BigDecimal.ZERO) == 0) {
                        orderDetailPool.setDeliveryStatus(0);
                    } else {
                        orderDetailPool.setDeliveryStatus(1);
                    }
                } else if (deliveryFlag.compareTo(BigDecimal.ZERO) == 0) {
                    orderDetailPool.setDeliveryStatus(2);
                } else {
                    orderDetailPool.setDeliveryStatus(3);
                }

                if (afterSaleFlag.compareTo(BigDecimal.ZERO) > 0) {
                    if (decimalAfterSale.compareTo(BigDecimal.ZERO) == 0) {
                        orderDetailPool.setAfterSaleStatus(0);
                    } else {
                        orderDetailPool.setAfterSaleStatus(1);
                    }
                } else if (afterSaleFlag.compareTo(BigDecimal.ZERO) == 0) {
                    orderDetailPool.setAfterSaleStatus(2);
                } else {
                    orderDetailPool.setAfterSaleStatus(3);
                }

                if (receiveFlag.compareTo(BigDecimal.ZERO) > 0) {
                    if (receiveNum.compareTo(BigDecimal.ZERO) == 0) {
                        orderDetailPool.setCustomerReceiptStatus(0);
                    } else {
                        orderDetailPool.setCustomerReceiptStatus(1);
                    }
                } else if (receiveFlag.compareTo(BigDecimal.ZERO) == 0) {
                    orderDetailPool.setCustomerReceiptStatus(2);
                } else {
                    orderDetailPool.setCustomerReceiptStatus(3);
                }
                updateList.add(orderDetailPool);
            }
            log.info("开始更新企业：{}的池数据,数据量：{}", companyCode, updateList.size());
            orderDetailPoolService.updateBatchById(updateList, 800);
            log.info("企业：{}发货单相关数据修复完成：", companyCode);
        }

        return "订单发货，妥投数据修复完成！";
    }

    /**
     * 已出账的
     * 修复2.0标准商城的验收数据（不包含外部验收的数据）
     * 修复商城&福利 的出账，开票，结算相关的数据
     *
     * @param orderSalesChannel
     * @return
     */
    @Transactional
    public String fixBillForNewMall(Integer orderSalesChannel) {
        // 修复验收数据
        List<String> companyCodeList = new ArrayList<>();
        if (orderSalesChannel.equals(OrderSalesChannelEnum.DFMALL.getCode())) {
            companyCodeList = purchaseOrderInfoPoolMapper.getDfmallBillDetailFixCompanyList();
        } else {
            companyCodeList = purchaseOrderInfoPoolMapper.getYflBillDetailFixCompanyList();

        }
        if (CollectionUtils.isEmpty(companyCodeList)) {
            log.info("渠道：{}未查询到要修复的企业数据！", orderSalesChannel);
            return "无数据需要修复！";
        }

        for (String companyCode : companyCodeList) {
            List<BillDetailFixDto> billDetailFixList = new ArrayList();
            // 开票数据[4, 6, 7]
            List<BillDetailInvoiceFixDto> billDetailInvoiceFixDtoList = new ArrayList<>();
            if (OrderSalesChannelEnum.YFLMALL.getCode().equals(orderSalesChannel)) {
                billDetailFixList = purchaseOrderInfoPoolMapper.getYflBillDetailFixList(companyCode);
                billDetailInvoiceFixDtoList = purchaseOrderInfoPoolMapper.getYflInvoiceDetailForCompany(companyCode);
            } else {
                billDetailFixList = purchaseOrderInfoPoolMapper.getDfmallBillDetailFixList(companyCode);
                billDetailInvoiceFixDtoList = purchaseOrderInfoPoolMapper.getDfmallInvoiceDetailForCompany(companyCode);
            }

            if (CollectionUtils.isEmpty(billDetailFixList)) {
                log.info("销售渠道:{},企业：{} 未查询到相关的修复数", orderSalesChannel, companyCode);
                continue;
            }

            purchaseOrderInfoFixService.fixingBill(billDetailFixList, billDetailInvoiceFixDtoList, companyCode,
                    orderSalesChannel, Boolean.FALSE);

        }
        return "2.0账单数据修复完成";
    }

    public void fixingBill(List<BillDetailFixDto> billDetailFixList,
            List<BillDetailInvoiceFixDto> billDetailInvoiceFixDtoList, String companyCode, Integer orderSalesChannel,
            Boolean isSapFlag) {
        Map<String, BigDecimal> billDetailFixMap = billDetailFixList.stream().collect(
                Collectors.toMap(BillDetailFixDto::getOrderDetailId,
                        BillDetailFixDto::getCheckNum,
                        BigDecimal::add));

        List<OrderDetailPool> orderDetailPoolList = purchaseOrderInfoPoolMapper
                .getOrderDetailForCompanyAndOrderSaleChannel(companyCode, orderSalesChannel, billDetailFixMap.keySet());
        // 已经对账的数据
        List<BillDetailFixDto> haveReconciliationList = billDetailFixList.stream()
                .filter(e -> e.getReconciliationStatus() != 0 && e.getReconciliationStatus() != 1)
                .collect(Collectors.toList());
        Map<String, BigDecimal> haveReconciliationNumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(haveReconciliationList)) {
            haveReconciliationNumMap = haveReconciliationList.stream().collect(
                    Collectors.toMap(BillDetailFixDto::getOrderDetailId,
                            BillDetailFixDto::getCheckNum,
                            BigDecimal::add));
        }

        List<BillDetailInvoiceFixDto> invoicingList = billDetailInvoiceFixDtoList.stream()
                .filter(e -> e.getInvoiceState() == 4).collect(Collectors.toList());
        // 开票中的
        Map<String, BigDecimal> invoicingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(invoicingList)) {
            invoicingMap = invoicingList.stream().collect(
                    Collectors.toMap(BillDetailInvoiceFixDto::getOrderDetailId,
                            BillDetailInvoiceFixDto::getInvoiceNum,
                            BigDecimal::add));
        }

        // 已经开票的
        List<BillDetailInvoiceFixDto> invoicedList = billDetailInvoiceFixDtoList.stream()
                .filter(e -> e.getInvoiceState() == 6 || e.getInvoiceState() == 7).collect(Collectors.toList());
        Map<String, BigDecimal> invoicedMap = new HashMap<>();
        Map<String, BigDecimal> invoicedMoneyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(invoicedList)) {
            invoicedMap = invoicedList.stream().collect(
                    Collectors.toMap(BillDetailInvoiceFixDto::getOrderDetailId,
                            BillDetailInvoiceFixDto::getInvoiceNum,
                            BigDecimal::add));
            invoicedMoneyMap = invoicedList.stream().collect(
                    Collectors.toMap(BillDetailInvoiceFixDto::getOrderDetailId,
                            BillDetailInvoiceFixDto::getInvoicedMoney,
                            BigDecimal::add));
        }

        // 结算数据
        List<BillDetailInvoiceFixDto> settleList = billDetailInvoiceFixDtoList.stream()
                .filter(e -> e.getRepaymentStatus() == 2).collect(Collectors.toList());
        Map<String, BigDecimal> settleMap = new HashMap<>();
        Map<String, BigDecimal> settleMoneyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(settleList)) {
            settleMap = settleList.stream().collect(
                    Collectors.toMap(BillDetailInvoiceFixDto::getOrderDetailId,
                            BillDetailInvoiceFixDto::getInvoiceNum,
                            BigDecimal::add));
            settleMoneyMap = settleList.stream().collect(
                    Collectors.toMap(BillDetailInvoiceFixDto::getOrderDetailId,
                            BillDetailInvoiceFixDto::getInvoicedMoney,
                            BigDecimal::add));
        }

        List<Long> haveNoBillDetailFixList = new ArrayList<>();
        List<Long> haveNoReconciliationList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {
            BigDecimal decimalCheckNum = billDetailFixMap.get(orderDetailPool.getOrderDetailId());
            if (decimalCheckNum == null) {
                haveNoBillDetailFixList.add(orderDetailPool.getId());
                continue;
            }
            orderDetailPool.setNewBilFixFlag(1);

            // 出账，验收数量
            BigDecimal subtractFlag = orderDetailPool.getConfirmNum().subtract(decimalCheckNum)
                    .subtract(orderDetailPool.getAfterSaleNum());
            orderDetailPool.setCustomerCheckoutNum(decimalCheckNum);

            if (subtractFlag.compareTo(BigDecimal.ZERO) > 0) {
                orderDetailPool.setCustomerCheckoutStatus(1);
            } else if (subtractFlag.compareTo(BigDecimal.ZERO) == 0) {
                orderDetailPool.setCustomerCheckoutStatus(2);
            } else {
                orderDetailPool.setCustomerCheckoutStatus(3);
            }

            // 非sap企业的 在此处修复验收数据和验收状态
            if (!isSapFlag) {
                orderDetailPool.setCustomerAcceptanceNum(decimalCheckNum);
                orderDetailPool.setCustomerAcceptanceStatus(orderDetailPool.getCustomerCheckoutStatus());
            }

            // 对账
            BigDecimal reconciliationNum = haveReconciliationNumMap.get(orderDetailPool.getOrderDetailId());
            if (reconciliationNum == null) {
                haveNoReconciliationList.add(orderDetailPool.getId());
                continue;
            }

            orderDetailPool.setCustomerReconciliationNum(reconciliationNum);
            BigDecimal reconciliationFlag = orderDetailPool.getConfirmNum()
                    .subtract(orderDetailPool.getCustomerReconciliationNum())
                    .subtract(orderDetailPool.getAfterSaleNum());

            if (reconciliationFlag.compareTo(BigDecimal.ZERO) > 0) {
                orderDetailPool.setCustomerReconciliationStatus(1);
            } else if (reconciliationFlag.compareTo(BigDecimal.ZERO) == 0) {
                orderDetailPool.setCustomerReconciliationStatus(2);
            } else {
                orderDetailPool.setCustomerReconciliationStatus(3);
            }

            // 开票
            BigDecimal invoingNum = invoicingMap.get(orderDetailPool.getOrderDetailId());
            if (invoingNum != null) {
                orderDetailPool.setCustomerInvoicingNum(invoingNum);
            }

            BigDecimal invoicedNum = invoicedMap.get(orderDetailPool.getOrderDetailId());
            if (invoicedNum != null) {
                orderDetailPool.setCustomerInvoicedNum(invoicedNum);
                orderDetailPool.setCustomerInvoicedMoney(invoicedMoneyMap.get(orderDetailPool.getOrderDetailId()));
                BigDecimal invoicedFlag = orderDetailPool.getConfirmNum()
                        .subtract(orderDetailPool.getCustomerInvoicedNum()).subtract(orderDetailPool.getAfterSaleNum());
                if (invoicedFlag.compareTo(BigDecimal.ZERO) > 0) {
                    orderDetailPool.setCustomerInvoiceStatus(1);
                } else if (invoicedFlag.compareTo(BigDecimal.ZERO) == 0) {
                    orderDetailPool.setCustomerInvoiceStatus(2);
                } else {
                    orderDetailPool.setCustomerInvoiceStatus(3);
                }
            }

            // 结算
            BigDecimal settleNum = settleMap.get(orderDetailPool.getOrderDetailId());
            if (settleNum != null) {
                orderDetailPool.setCustomerSettlementNum(settleNum);
                orderDetailPool.setCustomerSettlementMoney(settleMoneyMap.get(orderDetailPool.getOrderDetailId()));
                BigDecimal settleFlag = orderDetailPool.getConfirmNum()
                        .subtract(orderDetailPool.getCustomerSettlementNum())
                        .subtract(orderDetailPool.getAfterSaleNum());
                if (settleFlag.compareTo(BigDecimal.ZERO) > 0) {
                    orderDetailPool.setCustomerSettlementStatus(1);
                } else if (settleFlag.compareTo(BigDecimal.ZERO) == 0) {
                    orderDetailPool.setCustomerSettlementStatus(2);
                } else {
                    orderDetailPool.setCustomerSettlementStatus(3);
                }
            }
        }
        log.info("企业：{}未找到账单明细个数：{},订单明细id：{}", companyCode, haveNoBillDetailFixList.size(), haveNoBillDetailFixList);
        log.info("企业：{}未对账数据个数：{},订单明细id:{}", companyCode, haveNoReconciliationList.size(), haveNoReconciliationList);
        log.info("准备开始修复企业：{}的账单相关数据，数据量：{}", companyCode, orderDetailPoolList.size());
        orderDetailPoolService.updateBatchById(orderDetailPoolList, 800);
        log.info("企业：{}数据账单数据修复完成。。", companyCode);
    }

    /**
     * 修复2.0侧的集团共享四家的外部验收数据
     *
     * @return
     */
    @Transactional
    public String fixOutCheckForDfg() {
        // 查询共享的回传的所有数据【voyah,dfhs,dfgm,dfg01】
        List<OutCheckSapDetailVo> outCheckSapDetailVoList = purchaseOrderInfoPoolMapper.getallSapDeliveryData();
        // sap[dfpv,dna]
        List<OutCheckSapDetailVo> dfpvSapList = purchaseOrderInfoPoolMapper.getallDfpvSap();
        outCheckSapDetailVoList.addAll(dfpvSapList);
        // 线下导入[dfcv]
        List<OutCheckSapDetailVo> dfcvSapList = purchaseOrderInfoPoolMapper.getallDfcvSap();
        outCheckSapDetailVoList.addAll(dfcvSapList);
        // [dhec]
        List<OutCheckSapDetailVo> dhecSapList = purchaseOrderInfoPoolMapper.getallDhec();
        outCheckSapDetailVoList.addAll(dhecSapList);

        Map<String, List<OutCheckSapDetailVo>> companyMap = outCheckSapDetailVoList.stream()
                .collect(Collectors.groupingBy(OutCheckSapDetailVo::getCompanyCode));

        for (String companyCode : companyMap.keySet()) {
            log.info("开始修复外部验收数据，企业：{}", companyCode);
            List<OutCheckSapDetailVo> detailVosForCompany = companyMap.get(companyCode);
            List<String> purchaseNumberList = detailVosForCompany.stream().map(OutCheckSapDetailVo::getPurchaseNumber)
                    .distinct().collect(Collectors.toList());

            QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.DFMALL.getCode())
                    .in(OrderDetailPool::getPurchaseNumber, purchaseNumberList)
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getPurchaseInfoId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getPurchaseNumber,
                            OrderDetailPool::getRowSerialNumber,
                            OrderDetailPool::getCustomerAcceptanceNum,
                            OrderDetailPool::getAfterSaleNum);

            List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);

            if (CollectionUtils.isEmpty(orderDetailPoolList)) {
                log.info("企业：{}的订单明细池数据查询为空，入参采购单号集合：{}", companyCode, purchaseNumberList);
                continue;
            }

            Map<String, List<OutCheckSapDetailVo>> outCheckPurchaseMap = detailVosForCompany.stream()
                    .collect(Collectors.groupingBy(OutCheckSapDetailVo::getPurchaseNumber));

            Map<String, List<OrderDetailPool>> companyAndPurchaseNumberMap = orderDetailPoolList.stream()
                    .collect(Collectors.groupingBy(OrderDetailPool::getPurchaseNumber));
            // 没有对应采购单号的
            List<String> poolHaveNoOutCheckPurchaseNumbers = new ArrayList<>();
            List<OutCheckSapDetailVo> poolHaveNoItemNoList = new ArrayList<>();
            List<OrderDetailPool> updateList = new ArrayList<>();
            List<Long> purchaseInfoIdList = new ArrayList<>();

            for (String purchaseNumber : outCheckPurchaseMap.keySet()) {
                List<OutCheckSapDetailVo> checkSapDetailVoList = outCheckPurchaseMap.get(purchaseNumber);

                List<OrderDetailPool> detailPoolList = companyAndPurchaseNumberMap.get(purchaseNumber);

                if (CollectionUtils.isEmpty(detailPoolList)) {
                    poolHaveNoOutCheckPurchaseNumbers.add(purchaseNumber);
                    continue;
                }

                Map<Integer, BigDecimal> outCheckRowNumberMap = checkSapDetailVoList.stream().collect(
                        Collectors.toMap(OutCheckSapDetailVo::getItemNo,
                                OutCheckSapDetailVo::getMenge,
                                BigDecimal::add));

                Map<Integer, OrderDetailPool> poolRowSerialNumberMap = detailPoolList.stream()
                        .collect(Collectors.toMap(OrderDetailPool::getRowSerialNumber, Function.identity()));

                for (Integer rowSerialNumber : outCheckRowNumberMap.keySet()) {
                    BigDecimal outCheckNum = outCheckRowNumberMap.get(rowSerialNumber);
                    OrderDetailPool orderDetailPool = poolRowSerialNumberMap.get(rowSerialNumber);

                    if (orderDetailPool == null) {
                        OutCheckSapDetailVo outCheckSapDetailVo = new OutCheckSapDetailVo();
                        outCheckSapDetailVo.setCompanyCode(companyCode);
                        outCheckSapDetailVo.setPurchaseNumber(purchaseNumber);
                        outCheckSapDetailVo.setItemNo(rowSerialNumber);
                        outCheckSapDetailVo.setMenge(outCheckNum);
                        poolHaveNoItemNoList.add(outCheckSapDetailVo);
                        continue;
                    }

                    OrderDetailPool update = new OrderDetailPool();
                    update.setId(orderDetailPool.getId());
                    purchaseInfoIdList.add(orderDetailPool.getPurchaseInfoId());

                    update.setCustomerAcceptanceNum(outCheckNum);

                    BigDecimal subtract = orderDetailPool.getConfirmNum().subtract(outCheckNum);

                    if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                        update.setCustomerAcceptanceStatus(1);
                    } else if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                        update.setCustomerAcceptanceStatus(2);
                    } else {
                        update.setCustomerAcceptanceStatus(3);
                    }

                    updateList.add(update);
                }
            }
            log.info("企业：{},外部验收的但是商城没有的采购单数量有：{}，具体采购单号：{}", companyCode, poolHaveNoOutCheckPurchaseNumbers.size(),
                    JSONUtil.toJsonStr(poolHaveNoOutCheckPurchaseNumbers));
            log.info("企业：{}，外部验收部分明细商城订单未查询到，数量有：{}，具体明细：{}", companyCode, poolHaveNoItemNoList.size(),
                    JSONUtil.toJsonStr(poolHaveNoItemNoList));
            log.info("企业：{}，需要更新的验收数据，数量为：{}", companyCode, updateList.size());
            log.info("具体更新明细：{}", JSONUtil.toJsonStr(updateList));
            orderDetailPoolService.updateBatchById(updateList);
            UpdateWrapper<PurchaseOrderInfoPool> poolUpdateWrapper = new UpdateWrapper<>();
            poolUpdateWrapper.lambda().set(PurchaseOrderInfoPool::getIsOutSettle, 1).in(PurchaseOrderInfoPool::getId,
                    purchaseInfoIdList);
            purchaseOrderInfoPoolService.update(poolUpdateWrapper);
            log.info("企业：{}验收数据更新完成", companyCode);
        }

        return "2.0系统外部验收数据修复完成！";
    }

    /**
     * 修复sap的账单数据,验收数据 已经以外部的验收完成了修复
     *
     * @return
     */
    @Transactional
    public String fixSapBillForNewMall() {
        List<BillDetailFixDto> billDetailFixList = purchaseOrderInfoPoolMapper.getDfmallSapBillDetailFixList();
        if (CollectionUtils.isEmpty(billDetailFixList)) {
            return "暂无sap企业数据";
        }
        Map<String, List<BillDetailFixDto>> companyBillDetailFixMap = billDetailFixList.stream()
                .collect(Collectors.groupingBy(BillDetailFixDto::getCompanyCode));
        for (String companyCode : companyBillDetailFixMap.keySet()) {
            log.info("开始遍历企业：{}", companyCode);
            List<BillDetailFixDto> billDetailFixDtoList = companyBillDetailFixMap.get(companyCode);

            List<BillDetailInvoiceFixDto> invoiceDetailForCompany = purchaseOrderInfoPoolMapper
                    .getDfmallInvoiceDetailForCompany(companyCode);

            purchaseOrderInfoFixService.fixingBill(billDetailFixDtoList, invoiceDetailForCompany, companyCode,
                    OrderSalesChannelEnum.DFMALL.getCode(), Boolean.TRUE);

        }

        return "外部数据账单已更新！";

    }

    /**
     * 1.0账单数据修复
     *
     * @param fixOldBillDto
     * @return
     */
    @Transactional
    public String fixOldMallBill(FixOldBillDto fixOldBillDto) {
        Integer orderSalesChannel;
        if (fixOldBillDto.getIsDfmallFlag() == 1) {
            orderSalesChannel = OrderSalesChannelEnum.DFMALL.getCode();
        } else if (fixOldBillDto.getIsDfmallFlag() == 0) {
            orderSalesChannel = OrderSalesChannelEnum.YFLMALL.getCode();
        } else {
            return "商城类型异常！";
        }

        if (CollectionUtils.isEmpty(fixOldBillDto.getFixOldBillDetailDtoList())) {
            return "传入明细数据为空！";
        }

        log.info("1.0传入企业：{}的账单明细数据有：{}", fixOldBillDto.getCompanyCode(),
                fixOldBillDto.getFixOldBillDetailDtoList().size());

        List<String> purchaseNumberList = fixOldBillDto.getFixOldBillDetailDtoList().stream()
                .map(FixOldBillDetailDto::getPurchaseNumber).distinct().collect(Collectors.toList());
        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, orderSalesChannel)
                .in(OrderDetailPool::getPurchaseNumber, purchaseNumberList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getPurchaseNumber,
                        OrderDetailPool::getGoodsSku,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getCustomerCheckoutNum,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getCustomerAcceptanceNum,
                        OrderDetailPool::getAfterSaleNum);
        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            log.info("企业：{}的订单明细全部未查询到，传入数据量为:{}", fixOldBillDto.getCompanyCode(),
                    fixOldBillDto.getFixOldBillDetailDtoList().size());
            purchaseOrderInfoPoolMapper.insetBatchFixDto(fixOldBillDto.getFixOldBillDetailDtoList(),
                    fixOldBillDto.getCompanyCode(), orderSalesChannel);
            return "未查询到订单明细相关数据！";
        }

        Map<String, List<FixOldBillDetailDto>> purchaseNumberFixOldMap = fixOldBillDto.getFixOldBillDetailDtoList()
                .stream().collect(Collectors.groupingBy(FixOldBillDetailDto::getPurchaseNumber));

        Map<String, List<OrderDetailPool>> purchaseNumberOrderDetailMap = orderDetailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getPurchaseNumber));

        List<OrderDetailPool> updateList = new ArrayList<>();
        List<FixOldBillDetailDto> fixOldBillDetailDtoList = new ArrayList<>();

        for (String purchaseNumber : purchaseNumberFixOldMap.keySet()) {
            List<OrderDetailPool> detailPoolList = purchaseNumberOrderDetailMap.get(purchaseNumber);
            if (CollectionUtils.isEmpty(detailPoolList)) {
                fixOldBillDetailDtoList.addAll(purchaseNumberFixOldMap.get(purchaseNumber));
                continue;
            }

            List<FixOldBillDetailDto> fixOldBillDetailDtos = purchaseNumberFixOldMap.get(purchaseNumber);
            Map<String, List<FixOldBillDetailDto>> goodsSkuFixMap = fixOldBillDetailDtos.stream()
                    .collect(Collectors.groupingBy(FixOldBillDetailDto::getGoodsSku));

            Map<String, OrderDetailPool> orderDetailGoodsSkuMap = detailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            for (String goodsSku : goodsSkuFixMap.keySet()) {
                OrderDetailPool goodsSkuOrderDetail = orderDetailGoodsSkuMap.get(goodsSku);
                List<FixOldBillDetailDto> oldBillDetailDtos = goodsSkuFixMap.get(goodsSku);

                if (goodsSkuOrderDetail == null) {
                    fixOldBillDetailDtoList.addAll(oldBillDetailDtos);
                    continue;
                }

                OrderDetailPool updateItem = new OrderDetailPool();
                updateItem.setId(goodsSkuOrderDetail.getId());

                BigDecimal fixOldcheckNum = oldBillDetailDtos.stream().map(FixOldBillDetailDto::getCheckNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal newAcceptanceNum = goodsSkuOrderDetail.getCustomerAcceptanceNum().add(fixOldcheckNum);

                BigDecimal newCheckOutNum = goodsSkuOrderDetail.getCustomerCheckoutNum().add(fixOldcheckNum);

                BigDecimal newReconciliationNum = goodsSkuOrderDetail.getCustomerReconciliationNum()
                        .add(fixOldcheckNum);

                updateItem.setCustomerAcceptanceNum(newAcceptanceNum);
                updateItem.setCustomerCheckoutNum(newCheckOutNum);
                updateItem.setCustomerReconciliationNum(newReconciliationNum);
                BigDecimal acceptFlag = goodsSkuOrderDetail.getConfirmNum().subtract(newAcceptanceNum)
                        .subtract(goodsSkuOrderDetail.getAfterSaleNum());
                BigDecimal checkOutFlag = goodsSkuOrderDetail.getConfirmNum().subtract(newCheckOutNum)
                        .subtract(goodsSkuOrderDetail.getAfterSaleNum());
                BigDecimal reconciliationFlag = goodsSkuOrderDetail.getConfirmNum().subtract(newReconciliationNum)
                        .subtract(goodsSkuOrderDetail.getAfterSaleNum());

                if (acceptFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateItem.setCustomerAcceptanceStatus(1);
                } else if (acceptFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateItem.setCustomerAcceptanceStatus(2);
                } else {
                    updateItem.setCustomerAcceptanceStatus(3);
                }

                if (checkOutFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateItem.setCustomerCheckoutStatus(1);
                } else if (checkOutFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateItem.setCustomerCheckoutStatus(2);
                } else {
                    updateItem.setCustomerCheckoutStatus(3);
                }

                if (reconciliationFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateItem.setCustomerReconciliationStatus(1);
                } else if (reconciliationFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateItem.setCustomerReconciliationStatus(2);
                } else {
                    updateItem.setCustomerReconciliationStatus(3);
                }

                updateItem.setHistoryBillFixFlag(1);
                updateList.add(updateItem);
            }

        }

        if (CollectionUtils.isNotEmpty(fixOldBillDetailDtoList)) {
            log.info("企业：{}存在数量未查询到明细，数量为：{}", fixOldBillDto.getCompanyCode(), fixOldBillDetailDtoList);
            Lists.partition(fixOldBillDetailDtoList, 1000).forEach(son -> purchaseOrderInfoPoolMapper
                    .insetBatchFixDto(son, fixOldBillDto.getCompanyCode(), orderSalesChannel));
        }
        log.info("准备更新企业：{}的账单数据，有：{}个", fixOldBillDto.getCompanyCode(), updateList.size());
        orderDetailPoolService.updateBatchById(updateList, 1000);
        return "企业:" + fixOldBillDto.getCompanyCode() + "1.0的账单数据修复完成";
    }

    @Transactional
    public String fixOldSapCheck(List<OldCheckDataDto> oldCheckDataDtoList) {
        if (CollectionUtils.isEmpty(oldCheckDataDtoList)) {
            return "入参为空！";
        }

        List<String> orderNumberList = oldCheckDataDtoList.stream().map(OldCheckDataDto::getOrderNumber).distinct()
                .collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.DFMALL.getCode())
                .in(OrderDetailPool::getOrderNumber, orderNumberList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getPurchaseInfoId,
                        OrderDetailPool::getOrderNumber,
                        OrderDetailPool::getGoodsSku,
                        OrderDetailPool::getCustomerAcceptanceNum,
                        OrderDetailPool::getConfirmNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            log.info("1.0传入验收数据：{}", JSONUtil.toJsonStr(oldCheckDataDtoList));
            return "按1.0的明细，查询不到2.0系统的明细";
        }

        Map<String, List<OrderDetailPool>> orderDetailPollMapForOrderNumber = orderDetailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));

        Map<String, List<OldCheckDataDto>> oldCheckMapForOrderNumber = oldCheckDataDtoList.stream()
                .collect(Collectors.groupingBy(OldCheckDataDto::getOrderNumber));

        List<OldCheckDataDto> newMallHaveNoList = new ArrayList<>();
        List<OrderDetailPool> updateList = new ArrayList<>();
        List<Long> purchaseInfoIdList = new ArrayList<>();

        for (String orderNumber : oldCheckMapForOrderNumber.keySet()) {
            List<OrderDetailPool> detailPoolList = orderDetailPollMapForOrderNumber.get(orderNumber);
            List<OldCheckDataDto> oldCheckDataDtos = oldCheckMapForOrderNumber.get(orderNumber);

            if (CollectionUtils.isEmpty(detailPoolList)) {
                newMallHaveNoList.addAll(oldCheckDataDtos);
                continue;
            }

            Map<String, OrderDetailPool> detailPoolMap = detailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));
            Map<String, List<OldCheckDataDto>> oldCheckMap = oldCheckDataDtos.stream()
                    .collect(Collectors.groupingBy(OldCheckDataDto::getGoodsSku));

            for (String goodsSku : oldCheckMap.keySet()) {
                OrderDetailPool orderDetailPool = detailPoolMap.get(goodsSku);
                List<OldCheckDataDto> checkDataDtos = oldCheckMap.get(goodsSku);
                if (orderDetailPool == null) {
                    newMallHaveNoList.addAll(checkDataDtos);
                    continue;
                }

                OrderDetailPool update = new OrderDetailPool();
                update.setId(orderDetailPool.getId());

                BigDecimal checkNumCount = checkDataDtos.stream().map(OldCheckDataDto::getCheckNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal newChekNum = orderDetailPool.getCustomerAcceptanceNum().add(checkNumCount);

                update.setCustomerAcceptanceNum(newChekNum);
                purchaseInfoIdList.add(orderDetailPool.getPurchaseInfoId());

                BigDecimal checkFlag = orderDetailPool.getConfirmNum().subtract(newChekNum);
                if (checkFlag.compareTo(BigDecimal.ZERO) > 0) {
                    update.setCustomerAcceptanceStatus(1);
                } else if (checkFlag.compareTo(BigDecimal.ZERO) == 0) {
                    update.setCustomerAcceptanceStatus(2);
                } else {
                    update.setCustomerAcceptanceStatus(3);
                }
                updateList.add(update);
            }
        }

        if (CollectionUtils.isNotEmpty(newMallHaveNoList)) {
            log.info("存在1.0商城的验收数据在2.0商城查询不到明细，数量为：{}", newMallHaveNoList.size());
            log.info("具体明细为：{}", JSONUtil.toJsonStr(newMallHaveNoList));
        }
        log.info("准备修复外部验收数据，明细数量：{}", updateList.size());
        orderDetailPoolService.updateBatchById(updateList, 1000);

        UpdateWrapper<PurchaseOrderInfoPool> poolUpdateWrapper = new UpdateWrapper<>();
        poolUpdateWrapper.lambda().set(PurchaseOrderInfoPool::getIsOutSettle, 1)
                .in(PurchaseOrderInfoPool::getId, purchaseInfoIdList);
        purchaseOrderInfoPoolService.update(poolUpdateWrapper);

        return "历史1.0验收数据完成";
    }

    public String fixDeliveryTs(List<Long> deliveryIds) {
        List<DeliveryDetailFix> deliveryDetailFixList = purchaseOrderInfoPoolMapper.getFixDeliveryTs(deliveryIds);

        Map<String, BigDecimal> deliveryMap = deliveryDetailFixList.stream().collect(
                Collectors.toMap(DeliveryDetailFix::getOrderDetailId,
                        DeliveryDetailFix::getDeliveryNum,
                        BigDecimal::add));

        List<DeliveryDetailFix> deliveryOkList = deliveryDetailFixList.stream()
                .filter(e -> e.getSupDeliveryState().equals(DeliveryStateEnum.DELIVERY_OK.getCode()))
                .collect(Collectors.toList());
        Map<String, BigDecimal> deliveryOkMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deliveryOkList)) {
            deliveryOkMap = deliveryOkList.stream().collect(
                    Collectors.toMap(DeliveryDetailFix::getOrderDetailId,
                            DeliveryDetailFix::getDeliveryNum,
                            BigDecimal::add));
        }

        Map<String, Integer> orderDetailStateMap = deliveryDetailFixList.stream().collect(Collectors.toMap(
                DeliveryDetailFix::getOrderDetailId, DeliveryDetailFix::getOrderDetailState, (key1, key2) -> key1));

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, deliveryMap.keySet())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getDeliveryNum,
                        OrderDetailPool::getProperDeliveryNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);

        List<OrderDetailPool> updateForOrderDetailPool = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {
            OrderDetailPool update = new OrderDetailPool();
            update.setId(orderDetailPool.getId());

            BigDecimal decimal = deliveryMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal newDeliveryNum = orderDetailPool.getDeliveryNum().add(decimal);
            update.setDeliveryNum(newDeliveryNum);

            BigDecimal properDeliveryNum = deliveryOkMap.get(orderDetailPool.getOrderDetailId());
            if (properDeliveryNum != null) {
                BigDecimal newProperDeliveryNum = orderDetailPool.getProperDeliveryNum().add(properDeliveryNum);
                update.setProperDeliveryNum(newProperDeliveryNum);
            }

            BigDecimal deliveyFlag = orderDetailPool.getConfirmNum().subtract(newDeliveryNum);
            if (deliveyFlag.compareTo(BigDecimal.ZERO) > 0) {
                update.setDeliveryStatus(1);
            } else if (deliveyFlag.compareTo(BigDecimal.ZERO) == 0) {
                update.setDeliveryStatus(2);
            } else {
                update.setDeliveryStatus(3);
            }

            // 订单明细状态
            Integer integer = orderDetailStateMap.get(orderDetailPool.getOrderDetailId());
            update.setOrderDetailState(integer);

            updateForOrderDetailPool.add(update);

        }

        orderDetailPoolService.updateBatchById(updateForOrderDetailPool, 1000);

        return "修复成功";
    }

    @Transactional
    public String fixHistoryBillStatus() {
        return "修复1.0账单结算状态";
    }

    @Transactional
    public String fixGoodsCodeDelivery(Integer orderSalesChannel) {
        List<DeliveryDetailFix> deliveryDetailFixList = purchaseOrderInfoPoolMapper.getFixGoodsCodeDelivery();
        if (CollectionUtils.isEmpty(deliveryDetailFixList)) {
            return "未查询到商品编码变化的包裹数据！";
        }
        log.info("要修复的数据量：{}", deliveryDetailFixList.size());
        log.info("要修复的明细有：{}", JSONUtil.toJsonStr(deliveryDetailFixList));

        List<String> orderNumberList = deliveryDetailFixList.stream().map(DeliveryDetailFix::getOrderNumber).distinct()
                .collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, orderSalesChannel)
                .in(OrderDetailPool::getOrderNumber, orderNumberList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderNumber,
                        OrderDetailPool::getGoodsSku,
                        OrderDetailPool::getConfirmNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        log.info("orderDetailPoolList:{}", JSONUtil.toJsonStr(orderDetailPoolList));

        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            log.info("orderNumberList:{}", JSONUtil.toJsonStr(orderNumberList));
            return "渠道" + orderSalesChannel + "的订单明细未查询到！";
        }

        Map<String, List<DeliveryDetailFix>> orderNumberForDeliveryMap = deliveryDetailFixList.stream()
                .collect(Collectors.groupingBy(DeliveryDetailFix::getOrderNumber));
        Map<String, List<OrderDetailPool>> orderNumberForDetailPoolMap = orderDetailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));

        List<String> orderNumberHaveNotDetail = new ArrayList<>();
        List<DeliveryDetailFix> deliveryDetailFixesNoGoodsSku = new ArrayList<>();
        List<OrderDetailPool> updateList = new ArrayList<>();

        for (String orderNumber : orderNumberForDeliveryMap.keySet()) {
            log.info("orderNumber:{}", orderNumber);
            List<OrderDetailPool> detailPoolList = orderNumberForDetailPoolMap.get(orderNumber);
            if (CollectionUtils.isEmpty(detailPoolList)) {
                orderNumberHaveNotDetail.add(orderNumber);
                continue;
            }
            List<DeliveryDetailFix> detailFixList = orderNumberForDeliveryMap.get(orderNumber);

            Map<String, List<DeliveryDetailFix>> skuDeliveryMap = detailFixList.stream()
                    .collect(Collectors.groupingBy(DeliveryDetailFix::getGoodsSku));
            Map<String, OrderDetailPool> skuOrderDetailMap = detailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            List<DeliveryDetailFix> porList = detailFixList.stream()
                    .filter(e -> e.getSupDeliveryState().equals(DeliveryStateEnum.DELIVERY_OK.getCode()))
                    .collect(Collectors.toList());
            Map<String, List<DeliveryDetailFix>> porSkuMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(porList)) {
                porSkuMap = porList.stream().collect(Collectors.groupingBy(DeliveryDetailFix::getGoodsSku));
            }

            for (String sku : skuDeliveryMap.keySet()) {
                OrderDetailPool orderDetailPool = skuOrderDetailMap.get(sku);
                if (orderDetailPool == null) {
                    DeliveryDetailFix deliveryDetailFix = new DeliveryDetailFix();
                    deliveryDetailFix.setOrderNumber(orderNumber);
                    deliveryDetailFix.setGoodsSku(sku);
                    deliveryDetailFixesNoGoodsSku.add(deliveryDetailFix);
                    continue;
                }

                List<DeliveryDetailFix> fixList = skuDeliveryMap.get(sku);
                BigDecimal deliveryNum = fixList.stream().map(DeliveryDetailFix::getDeliveryNum).reduce(BigDecimal.ZERO,
                        BigDecimal::add);

                BigDecimal deliveryFlag = orderDetailPool.getConfirmNum().subtract(deliveryNum);
                OrderDetailPool updateOrderDetail = new OrderDetailPool();
                updateOrderDetail.setId(orderDetailPool.getId());
                updateOrderDetail.setDeliveryNum(deliveryNum);

                if (deliveryFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetail.setDeliveryStatus(1);
                } else if (deliveryFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setDeliveryStatus(2);
                } else {
                    updateOrderDetail.setDeliveryStatus(3);
                }

                BigDecimal receiveNum = fixList.stream().map(DeliveryDetailFix::getReceivingNum).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                BigDecimal receiveNumFlag = orderDetailPool.getConfirmNum().subtract(receiveNum);
                updateOrderDetail.setCustomerReceiptNum(receiveNum);

                if (receiveNumFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetail.setCustomerReceiptStatus(1);
                } else if (receiveNumFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerReceiptStatus(2);
                } else {
                    updateOrderDetail.setCustomerReceiptStatus(3);
                }

                // 妥投
                List<DeliveryDetailFix> proList = porSkuMap.get(sku);
                if (CollectionUtils.isNotEmpty(proList)) {
                    BigDecimal proNum = proList.stream().map(DeliveryDetailFix::getDeliveryNum).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
                    updateOrderDetail.setProperDeliveryNum(proNum);
                }

                updateList.add(updateOrderDetail);
                log.info("updateList:{}", updateList.size());
            }

        }

        if (CollectionUtils.isNotEmpty(orderNumberHaveNotDetail)) {
            log.info("订单号找不到明细的有：{}", JSONUtil.toJsonStr(orderNumberHaveNotDetail));
        }

        if (CollectionUtils.isNotEmpty(deliveryDetailFixesNoGoodsSku)) {
            log.info("SKU找不到的包裹明细：{}", JSONUtil.toJsonStr(deliveryDetailFixesNoGoodsSku));
        }

        log.info("准备更新数据，条数：{}", updateList.size());
        orderDetailPoolService.updateBatchById(updateList);

        return "商品编码变了的包裹修复成功";
    }

    @Transactional
    public String fixLeaveOrder(FixLeaveOrderDto fixLeaveOrderDto) {
        log.info("入参：{}", JSONUtil.toJsonStr(fixLeaveOrderDto));

        Map<String, List<OrderDetailPoolPreOrderDto>> orderDetailMap = fixLeaveOrderDto
                .getOrderDetailPoolPreOrderDtoList().stream()
                .collect(Collectors.groupingBy(OrderDetailPoolPreOrderDto::getPurchaseNumber));

        Map<String, PurchaseOrderInfoDto> purchaseInfoMap = fixLeaveOrderDto.getPurchaseOrderInfoDtoList().stream()
                .collect(Collectors.toMap(PurchaseOrderInfoDto::getPurchaseNumber, Function.identity()));

        // 查询遗漏的订单对应的账单数据
        List<BillDetailFix> billDetailFixList = purchaseOrderInfoPoolMapper
                .getBillDetailFixListForPurchaseNumber(purchaseInfoMap.keySet());
        Map<String, List<BillDetailFix>> billfixMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(billDetailFixList)) {
            billfixMap = billDetailFixList.stream().collect(Collectors.groupingBy(BillDetailFix::getPurchaseNumber));
        }

        List<String> purchaseNumberNoDetailList = new ArrayList<>();
        List<PurchaseOrderInfoPool> purchaseOrderInfoPoolList = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolList = new ArrayList<>();
        List<Long> fixIds = new ArrayList<>();

        for (String purchaseNumber : purchaseInfoMap.keySet()) {
            PurchaseOrderInfoDto purchaseOrderInfoDto = purchaseInfoMap.get(purchaseNumber);
            Long purchaseOrderInfoId = commonCodeGeneral.makeMysqlId();

            PurchaseOrderInfoPool orderInfoPool = DataAdapter.convert(purchaseOrderInfoDto,
                    PurchaseOrderInfoPool.class);

            orderInfoPool.setId(purchaseOrderInfoId);
            // 整个采购单的迁移漏了的修复
            orderInfoPool.setIsFix(3);
            // orderInfoPool.setOrderPlacementScenario(1);
            purchaseOrderInfoPoolList.add(orderInfoPool);

            List<OrderDetailPoolPreOrderDto> orderDetailPoolPreOrderDtoList = orderDetailMap.get(purchaseNumber);
            if (CollectionUtils.isEmpty(orderDetailPoolPreOrderDtoList)) {
                purchaseNumberNoDetailList.add(purchaseNumber);
                continue;
            }

            List<BillDetailFix> billDetailFixes = billfixMap.get(purchaseNumber);
            Map<String, List<BillDetailFix>> skuMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(billDetailFixes)) {
                skuMap = billDetailFixes.stream().collect(Collectors.groupingBy(BillDetailFix::getGoodsSku));
            }

            for (OrderDetailPoolPreOrderDto orderDetailPoolPreOrderDto : orderDetailPoolPreOrderDtoList) {

                OrderDetailPool orderDetailPool = DataAdapter.convert(orderDetailPoolPreOrderDto,
                        OrderDetailPool.class);
                Long aLong = commonCodeGeneral.makeMysqlId();
                orderDetailPool.setId(aLong);
                orderDetailPool.setPurchaseInfoId(purchaseOrderInfoId);
                orderDetailPool.setOrderSalesChannel(1);
                List<BillDetailFix> fixList = skuMap.get(orderDetailPoolPreOrderDto.getGoodsSku());
                if (CollectionUtils.isNotEmpty(fixList)) {
                    BigDecimal checkNumSum = fixList.stream().map(BillDetailFix::getCheckNum).reduce(BigDecimal.ZERO,
                            BigDecimal::add);

                    orderDetailPool.setCustomerAcceptanceNum(checkNumSum);
                    orderDetailPool.setCustomerCheckoutNum(checkNumSum);
                    orderDetailPool.setDeliveryNum(checkNumSum);
                    orderDetailPool.setCustomerReceiptNum(checkNumSum);
                    orderDetailPool.setProperDeliveryNum(checkNumSum);
                    orderDetailPool.setCustomerReconciliationNum(checkNumSum);

                    BigDecimal subtract = orderDetailPool.getConfirmNum().subtract(checkNumSum);

                    if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                        orderDetailPool.setCustomerAcceptanceStatus(1);
                        orderDetailPool.setCustomerCheckoutStatus(1);
                        orderDetailPool.setDeliveryStatus(1);
                        orderDetailPool.setCustomerReceiptStatus(1);
                        orderDetailPool.setCustomerReconciliationStatus(1);

                    } else if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                        orderDetailPool.setCustomerAcceptanceStatus(2);
                        orderDetailPool.setCustomerCheckoutStatus(2);
                        orderDetailPool.setDeliveryStatus(2);
                        orderDetailPool.setCustomerReceiptStatus(2);
                        orderDetailPool.setCustomerReconciliationStatus(2);
                    } else {
                        orderDetailPool.setCustomerAcceptanceStatus(3);
                        orderDetailPool.setCustomerCheckoutStatus(3);
                        orderDetailPool.setDeliveryStatus(3);
                        orderDetailPool.setCustomerReceiptStatus(3);
                        orderDetailPool.setCustomerReconciliationStatus(3);
                    }
                    fixIds.addAll(fixList.stream().map(BillDetailFix::getId).collect(Collectors.toList()));
                }
                orderDetailPoolList.add(orderDetailPool);

            }
        }

        if (CollectionUtils.isNotEmpty(purchaseNumberNoDetailList)) {
            log.info("采购单没有明细的有：{}", JSONUtil.toJsonStr(purchaseNumberNoDetailList));
        }

        log.info("准备开始保存采购单信息,数据量：{}", purchaseOrderInfoPoolList.size());
        purchaseOrderInfoPoolService.saveBatch(purchaseOrderInfoPoolList);
        log.info("准备开始保存明细数据，数据量：{}", orderDetailPoolList.size());
        orderDetailPoolService.saveBatch(orderDetailPoolList);
        if (CollectionUtils.isNotEmpty(fixIds)) {
            log.info("开始更新billDetailfix表数据,数据量：{}", fixIds);
            purchaseOrderInfoPoolMapper.updateBillDetailFix(fixIds);
        }

        return "迁移漏的整单已修复到订单生命周期中";

    }

    @Transactional(rollbackFor = Exception.class)
    public String fixYflOfflineBill(String activityCode) {
        log.info("准备修复线下活动账单，活动编码：{}", activityCode);
        // 取线下账单数据
        List<YfllineBillDto> yfllineBillDtoList = purchaseOrderInfoPoolMapper
                .getYflOfflineBillForActivityCode(activityCode);
        if (CollectionUtils.isEmpty(yfllineBillDtoList)) {
            throw new ParameterException("该活动未查询到要修复的账单明细");
        }

        List<String> orderNumberList = yfllineBillDtoList.stream().map(YfllineBillDto::getOrderNumber).distinct()
                .collect(Collectors.toList());
        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.YFLMALL.getCode())
                .in(OrderDetailPool::getOrderNumber, orderNumberList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getPurchaseNumber,
                        OrderDetailPool::getOrderNumber,
                        OrderDetailPool::getGoodsSku,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getCustomerCheckoutNum,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getCustomerAcceptanceNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        log.info("查询生命周期明细数据：{}", orderDetailPoolList.size());
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            throw new ParameterException("未查询到对应采购采购单的订单明细数据！");
        }

        Map<String, List<YfllineBillDto>> purchaseOfflineMap = yfllineBillDtoList.stream()
                .collect(Collectors.groupingBy(YfllineBillDto::getOrderNumber));

        Map<String, List<OrderDetailPool>> purchaseNumberOrderDetailMap = orderDetailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));

        List<Long> noHaveOrderDetailIdList = new ArrayList<>();
        List<Long> okOfflineDetailIdList = new ArrayList<>();

        List<OrderDetailPool> updateOrderDetailList = new ArrayList<>();

        for (String orderNumber : purchaseOfflineMap.keySet()) {

            List<OrderDetailPool> orderDetailPools = purchaseNumberOrderDetailMap.get(orderNumber);
            List<YfllineBillDto> yfllineBillDtos = purchaseOfflineMap.get(orderNumber);
            if (CollectionUtils.isEmpty(orderDetailPools)) {
                noHaveOrderDetailIdList
                        .addAll(yfllineBillDtos.stream().map(YfllineBillDto::getId).collect(Collectors.toList()));
                continue;
            }

            Map<String, List<YfllineBillDto>> skuYfllineMap = yfllineBillDtos.stream()
                    .collect(Collectors.groupingBy(YfllineBillDto::getGoodsSku));

            Map<String, OrderDetailPool> orderDetailSkuMap = orderDetailPools.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            for (String goodsSku : skuYfllineMap.keySet()) {
                OrderDetailPool orderDetailPool = orderDetailSkuMap.get(goodsSku);
                List<YfllineBillDto> yfllineBillDtoList1 = skuYfllineMap.get(goodsSku);
                if (orderDetailPool == null) {
                    noHaveOrderDetailIdList.addAll(
                            yfllineBillDtoList1.stream().map(YfllineBillDto::getId).collect(Collectors.toList()));
                    continue;
                }

                BigDecimal reduce = yfllineBillDtoList1.stream().map(YfllineBillDto::getCheckNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                OrderDetailPool update = new OrderDetailPool();
                update.setId(orderDetailPool.getId());
                update.setHistoryBillFixFlag(1);

                BigDecimal newCheckOutNum = orderDetailPool.getCustomerCheckoutNum().add(reduce);
                BigDecimal newReconciliationNum = orderDetailPool.getCustomerReconciliationNum().add(reduce);
                BigDecimal newAcceptanceNum = orderDetailPool.getCustomerAcceptanceNum().add(reduce);

                update.setCustomerCheckoutNum(newCheckOutNum);
                update.setCustomerReconciliationNum(newReconciliationNum);
                update.setCustomerAcceptanceNum(newAcceptanceNum);

                BigDecimal chekoutFlag = orderDetailPool.getConfirmNum().subtract(newCheckOutNum);
                BigDecimal reconciliationFlag = orderDetailPool.getConfirmNum().subtract(newReconciliationNum);
                BigDecimal acceptTanceFlag = orderDetailPool.getConfirmNum().subtract(newAcceptanceNum);

                if (chekoutFlag.compareTo(BigDecimal.ZERO) > 0) {
                    update.setCustomerCheckoutStatus(1);
                } else if (chekoutFlag.compareTo(BigDecimal.ZERO) == 0) {
                    update.setCustomerCheckoutStatus(2);
                } else {
                    update.setCustomerCheckoutStatus(3);
                }

                if (reconciliationFlag.compareTo(BigDecimal.ZERO) > 0) {
                    update.setCustomerReconciliationStatus(1);
                } else if (reconciliationFlag.compareTo(BigDecimal.ZERO) == 0) {
                    update.setCustomerReconciliationStatus(2);
                } else {
                    update.setCustomerReconciliationStatus(3);
                }

                if (acceptTanceFlag.compareTo(BigDecimal.ZERO) > 0) {
                    update.setCustomerAcceptanceStatus(1);
                } else if (acceptTanceFlag.compareTo(BigDecimal.ZERO) == 0) {
                    update.setCustomerAcceptanceStatus(2);
                } else {
                    update.setCustomerAcceptanceStatus(3);
                }
                updateOrderDetailList.add(update);
                okOfflineDetailIdList
                        .addAll(yfllineBillDtoList1.stream().map(YfllineBillDto::getId).collect(Collectors.toList()));
            }
        }

        log.info("准备更新订单生命周期明细数据，数据量为：{}", updateOrderDetailList.size());
        orderDetailPoolService.updateBatchById(updateOrderDetailList);
        if (CollectionUtils.isNotEmpty(noHaveOrderDetailIdList)) {
            log.info("准备更新线下账单存在，订单生命周期没有的数据，数据量：{}", noHaveOrderDetailIdList.size());
            purchaseOrderInfoPoolMapper.updateYfloffline(noHaveOrderDetailIdList, 2);
        }
        log.info("准备更新线下账单和订单明细生命周期刚好匹配上的数据，数据量：{}", okOfflineDetailIdList.size());
        purchaseOrderInfoPoolMapper.updateYfloffline(okOfflineDetailIdList, 1);

        return "线下账单[" + activityCode + "]的数据已维护完成！";
    }

    @Transactional
    public String YflFixLeaveOrder(FixLeaveOrderDto fixLeaveOrderDto) {
        Map<String, List<OrderDetailPoolPreOrderDto>> orderDetailMap = fixLeaveOrderDto
                .getOrderDetailPoolPreOrderDtoList().stream()
                .collect(Collectors.groupingBy(OrderDetailPoolPreOrderDto::getPurchaseNumber));

        Map<String, PurchaseOrderInfoDto> purchaseInfoMap = fixLeaveOrderDto.getPurchaseOrderInfoDtoList().stream()
                .collect(Collectors.toMap(PurchaseOrderInfoDto::getPurchaseNumber, Function.identity()));
        List<String> goodsSkuList = fixLeaveOrderDto.getOrderDetailPoolPreOrderDtoList().stream()
                .map(OrderDetailPoolPreOrderDto::getGoodsSku).distinct().collect(Collectors.toList());
        List<ShopGoods> shopGoodsList = purchaseOrderInfoPoolMapper.getLeaveGoods(goodsSkuList,
                yflYamlConfig.getTenantId());
        Map<String, String> classNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(shopGoodsList)) {
            classNameMap = shopGoodsList.stream().collect(
                    Collectors.toMap(ShopGoods::getGoodsSku, ShopGoods::getThirdClassName, (key1, key2) -> key1));
        }

        List<String> purchaseNumberNoDetailList = new ArrayList<>();
        List<PurchaseOrderInfoPool> purchaseOrderInfoPoolList = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolList = new ArrayList<>();

        for (String purchaseNumber : purchaseInfoMap.keySet()) {
            PurchaseOrderInfoDto purchaseOrderInfoDto = purchaseInfoMap.get(purchaseNumber);
            Long purchaseOrderInfoId = commonCodeGeneral.makeMysqlId();

            PurchaseOrderInfoPool orderInfoPool = DataAdapter.convert(purchaseOrderInfoDto,
                    PurchaseOrderInfoPool.class);

            orderInfoPool.setId(purchaseOrderInfoId);
            // 整个采购单的迁移漏了的修复
            orderInfoPool.setIsFix(3);
            // orderInfoPool.setOrderPlacementScenario(2);
            purchaseOrderInfoPoolList.add(orderInfoPool);

            List<OrderDetailPoolPreOrderDto> orderDetailPoolPreOrderDtoList = orderDetailMap.get(purchaseNumber);
            if (CollectionUtils.isEmpty(orderDetailPoolPreOrderDtoList)) {
                purchaseNumberNoDetailList.add(purchaseNumber);
                continue;
            }

            for (OrderDetailPoolPreOrderDto orderDetailPoolPreOrderDto : orderDetailPoolPreOrderDtoList) {

                OrderDetailPool orderDetailPool = DataAdapter.convert(orderDetailPoolPreOrderDto,
                        OrderDetailPool.class);
                Long aLong = commonCodeGeneral.makeMysqlId();
                orderDetailPool.setId(aLong);
                orderDetailPool.setPurchaseInfoId(purchaseOrderInfoId);
                orderDetailPool.setOrderSalesChannel(2);
                orderDetailPool.setThreeClassName(classNameMap.get(orderDetailPool.getGoodsSku()));
                orderDetailPoolList.add(orderDetailPool);

            }
        }

        if (CollectionUtils.isNotEmpty(purchaseNumberNoDetailList)) {
            log.info("采购单没有明细的有：{}", JSONUtil.toJsonStr(purchaseNumberNoDetailList));
        }

        log.info("准备开始保存采购单信息,数据量：{}", purchaseOrderInfoPoolList.size());
        purchaseOrderInfoPoolService.saveBatch(purchaseOrderInfoPoolList);
        log.info("准备开始保存明细数据，数据量：{}", orderDetailPoolList.size());
        orderDetailPoolService.saveBatch(orderDetailPoolList);

        return "迁移漏的整单已修复到订单生命周期中";

    }

    /**
     * 得提前完成后面两步骤，才能使用此步骤
     * 修复历史对账且线下已结算的数据
     *
     * @param orderSalesChannel
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String fixHistorySettle(Integer orderSalesChannel) {
        log.info("准备修复历史结算数据，销售渠道：{}", OrderSalesChannelEnum.get(orderSalesChannel).getName());

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, orderSalesChannel)
                .eq(OrderDetailPool::getHistoryBillFixFlag, 1)
                .eq(OrderDetailPool::getNewBilFixFlag, 0)
                .eq(OrderDetailPool::getHistorySettleFlag, 0)
                .eq(OrderDetailPool::getCustomerReconciliationStatus, 2)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderSalesChannel,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getGoodsUnitPriceNaked,
                        OrderDetailPool::getTaxRate,
                        OrderDetailPool::getCustomerReconciliationStatus,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getGoodsTotalPriceTax,
                        OrderDetailPool::getGoodsPayIntegral)
                .last("limit 20000");
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPools)) {
            return "未查询到需要修复历史结算数据的明细！";
        }

        List<OrderDetailPool> orderDetailPoolUpdateList = new ArrayList<>();
        if (OrderSalesChannelEnum.DFMALL.getCode().equals(orderSalesChannel)) {
            for (OrderDetailPool orderDetailPool : orderDetailPools) {
                OrderDetailPool updatePool = new OrderDetailPool();
                updatePool.setId(orderDetailPool.getId());

                updatePool.setCustomerInvoicedNum(orderDetailPool.getCustomerReconciliationNum());
                updatePool.setCustomerInvoiceStatus(2);
                // 计算开票金额
                BigDecimal reallyTaxRate = new BigDecimal(orderDetailPool.getTaxRate()).multiply(new BigDecimal("0.01"))
                        .add(BigDecimal.ONE);
                BigDecimal invoicedMoney = orderDetailPool.getGoodsUnitPriceNaked()
                        .multiply(orderDetailPool.getCustomerReconciliationNum()).multiply(reallyTaxRate)
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
                updatePool.setCustomerInvoicedMoney(invoicedMoney);

                updatePool.setCustomerSettlementStatus(2);
                updatePool.setCustomerSettlementNum(orderDetailPool.getCustomerReconciliationNum());
                updatePool.setCustomerSettlementMoney(invoicedMoney);

                updatePool.setHistorySettleFlag(1);

                orderDetailPoolUpdateList.add(updatePool);
            }
        } else if (OrderSalesChannelEnum.YFLMALL.getCode().equals(orderSalesChannel)) {
            for (OrderDetailPool orderDetailPool : orderDetailPools) {
                OrderDetailPool updatePool = new OrderDetailPool();
                updatePool.setId(orderDetailPool.getId());

                updatePool.setCustomerInvoicedNum(orderDetailPool.getCustomerReconciliationNum());
                updatePool.setCustomerInvoiceStatus(2);
                updatePool.setCustomerInvoicedMoney(orderDetailPool.getGoodsPayIntegral());

                updatePool.setCustomerSettlementStatus(2);
                updatePool.setCustomerSettlementNum(orderDetailPool.getCustomerReconciliationNum());
                updatePool.setCustomerSettlementMoney(orderDetailPool.getGoodsPayIntegral());

                updatePool.setHistorySettleFlag(1);
                orderDetailPoolUpdateList.add(updatePool);
            }
        } else {
            throw new ParameterException("销售渠道异常！");
        }

        orderDetailPoolService.updateBatchById(orderDetailPoolUpdateList);

        return "修复渠道[" + OrderSalesChannelEnum.get(orderSalesChannel).getName() + "]的历史结算数据完成！";
    }

    @Transactional
    public String fixInvoicedNoSettle() {
        log.info("准备修复已开票未付款的数据");
        List<HistorySettleBillDto> historySettleBillDtoList = purchaseOrderInfoPoolMapper.getInvoicedNoSettle(1);
        if (CollectionUtils.isEmpty(historySettleBillDtoList)) {
            return "无数据需要修复";
        }
        log.info("查询历史已开票未付款的数据有：{}个", historySettleBillDtoList.size());

        List<String> orderNumberList = historySettleBillDtoList.stream().map(HistorySettleBillDto::getOrderNumber)
                .distinct().collect(Collectors.toList());
        log.info("orderNumberList:{}", orderNumberList);

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderDetailPool::getOrderNumber, orderNumberList)
                .eq(OrderDetailPool::getHistoryBillFixFlag, 1)
                .eq(OrderDetailPool::getNewBilFixFlag, 0)
                .eq(OrderDetailPool::getHistorySettleFlag, 0)
                .eq(OrderDetailPool::getCustomerReconciliationStatus, 2)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderNumber,
                        OrderDetailPool::getGoodsSku,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getCustomerReconciliationStatus,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getSupplierInvoiceStatus,
                        OrderDetailPool::getCustomerInvoicedNum,
                        OrderDetailPool::getCustomerInvoicingNum,
                        OrderDetailPool::getCustomerInvoicedMoney);
        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            return "未查询到对应的订单明细生命周期数据！";
        }
        log.info("查询到的订单明细生命周期个数：{}", orderDetailPoolList.size());
        Map<String, List<HistorySettleBillDto>> orderNumberHistoryMap = historySettleBillDtoList.stream()
                .collect(Collectors.groupingBy(HistorySettleBillDto::getOrderNumber));
        Map<String, List<OrderDetailPool>> orderNumberDetailMap = orderDetailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));

        List<Long> haveNoOrderDetailList = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolUpdateList = new ArrayList<>();
        List<Long> historyUpdateList = new ArrayList<>();

        for (String orderNumber : orderNumberHistoryMap.keySet()) {
            List<HistorySettleBillDto> historySettleBillDtos = orderNumberHistoryMap.get(orderNumber);
            List<OrderDetailPool> detailPoolList = orderNumberDetailMap.get(orderNumber);

            if (CollectionUtils.isEmpty(detailPoolList)) {
                haveNoOrderDetailList.addAll(
                        historySettleBillDtos.stream().map(HistorySettleBillDto::getId).collect(Collectors.toList()));
                continue;
            }

            Map<String, List<HistorySettleBillDto>> goodsSkuHistoryMap = historySettleBillDtos.stream()
                    .collect(Collectors.groupingBy(HistorySettleBillDto::getGoodsSku));
            Map<String, OrderDetailPool> skuOrderDetailMap = detailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            for (String goodsSku : goodsSkuHistoryMap.keySet()) {
                List<HistorySettleBillDto> skuHistoryList = goodsSkuHistoryMap.get(goodsSku);
                OrderDetailPool orderDetailPool = skuOrderDetailMap.get(goodsSku);
                if (orderDetailPool == null) {
                    haveNoOrderDetailList.addAll(
                            skuHistoryList.stream().map(HistorySettleBillDto::getId).collect(Collectors.toList()));
                    continue;
                }

                OrderDetailPool updateDetailPool = new OrderDetailPool();
                updateDetailPool.setId(orderDetailPool.getId());
                updateDetailPool.setHistorySettleFlag(1);

                BigDecimal invoicedNum = skuHistoryList.stream().map(HistorySettleBillDto::getSettleNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal newInvoicedNum = orderDetailPool.getCustomerInvoicedNum().add(invoicedNum);
                updateDetailPool.setCustomerInvoicedNum(newInvoicedNum);
                // 更新金额
                BigDecimal invoicedMoney = skuHistoryList.stream().map(HistorySettleBillDto::getSettleMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                updateDetailPool
                        .setCustomerInvoicedMoney(orderDetailPool.getCustomerInvoicedMoney().add(invoicedMoney));

                BigDecimal invoicedFlag = orderDetailPool.getConfirmNum().subtract(newInvoicedNum)
                        .subtract(orderDetailPool.getAfterSaleNum());

                if (invoicedFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateDetailPool.setCustomerInvoiceStatus(1);
                } else if (invoicedFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateDetailPool.setCustomerInvoiceStatus(2);
                } else {
                    updateDetailPool.setCustomerInvoiceStatus(3);
                }

                orderDetailPoolUpdateList.add(updateDetailPool);
                historyUpdateList
                        .addAll(skuHistoryList.stream().map(HistorySettleBillDto::getId).collect(Collectors.toList()));
            }
        }

        log.info("根据要修复的数据未查询到订单明细的数据有：{}个，具体为：{}", haveNoOrderDetailList.size(),
                JSONUtil.toJsonStr(haveNoOrderDetailList));
        if (CollectionUtils.isNotEmpty(haveNoOrderDetailList)) {
            // 更新
            purchaseOrderInfoPoolMapper.updateHistorySettleBillList(haveNoOrderDetailList, 2);
        }
        log.info("要修复的数据匹配成功需要更新的个数：{},具体为：{}", historyUpdateList.size(), JSONUtil.toJsonStr(historyUpdateList));
        if (CollectionUtils.isNotEmpty(historyUpdateList)) {
            // 更新
            purchaseOrderInfoPoolMapper.updateHistorySettleBillList(historyUpdateList, 1);
        }

        log.info("需要更新的订单明细生命周期数量：{}", orderDetailPoolUpdateList.size());
        if (CollectionUtils.isNotEmpty(orderDetailPoolUpdateList)) {
            orderDetailPoolService.updateBatchById(orderDetailPoolUpdateList);
        }

        return "历史已开票未付款的数据修复完成！";
    }

    /**
     * 修复已对账未开票的数据
     *
     * @return
     */
    @Transactional
    public String fixReconciliationNoInvoice() {
        List<HistorySettleBillDto> historySettleBillDtoList = purchaseOrderInfoPoolMapper.getInvoicedNoSettle(0);
        if (CollectionUtils.isEmpty(historySettleBillDtoList)) {
            return "无需要修复已对账未开票的历史数据！";
        }

        log.info("已对账未开票的历史数据有：{}个", historySettleBillDtoList.size());

        List<String> orderNumberList = historySettleBillDtoList.stream().map(HistorySettleBillDto::getOrderNumber)
                .distinct().collect(Collectors.toList());
        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderDetailPool::getOrderNumber, orderNumberList)
                .eq(OrderDetailPool::getHistoryBillFixFlag, 1)
                .eq(OrderDetailPool::getNewBilFixFlag, 0)
                .eq(OrderDetailPool::getHistorySettleFlag, 0)
                .eq(OrderDetailPool::getCustomerReconciliationStatus, 2)
                .select(OrderDetailPool::getId, OrderDetailPool::getOrderNumber, OrderDetailPool::getGoodsSku);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            return "未查询到对应要修复的订单明细生命周期数据！";
        }

        Map<String, List<HistorySettleBillDto>> orderNumberHistoryMap = historySettleBillDtoList.stream()
                .collect(Collectors.groupingBy(HistorySettleBillDto::getOrderNumber));
        Map<String, List<OrderDetailPool>> orderNumberDetailMap = orderDetailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));

        List<Long> haveNoOrderDetailList = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolUpdateList = new ArrayList<>();
        List<Long> historyUpdateList = new ArrayList<>();

        for (String orderNumber : orderNumberHistoryMap.keySet()) {

            List<HistorySettleBillDto> historySettleBillDtos = orderNumberHistoryMap.get(orderNumber);
            List<OrderDetailPool> orderDetailPools = orderNumberDetailMap.get(orderNumber);
            if (CollectionUtils.isEmpty(orderDetailPools)) {
                haveNoOrderDetailList.addAll(
                        historySettleBillDtos.stream().map(HistorySettleBillDto::getId).collect(Collectors.toList()));
                continue;
            }

            Map<String, List<HistorySettleBillDto>> skuHistoryMap = historySettleBillDtos.stream()
                    .collect(Collectors.groupingBy(HistorySettleBillDto::getGoodsSku));
            Map<String, OrderDetailPool> detailPoolMap = orderDetailPools.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            for (String goodsSku : skuHistoryMap.keySet()) {
                OrderDetailPool orderDetailPool = detailPoolMap.get(goodsSku);
                List<HistorySettleBillDto> historyList = skuHistoryMap.get(goodsSku);
                if (orderDetailPool == null) {
                    haveNoOrderDetailList
                            .addAll(historyList.stream().map(HistorySettleBillDto::getId).collect(Collectors.toList()));
                    continue;
                }

                OrderDetailPool updateDetail = new OrderDetailPool();
                updateDetail.setId(orderDetailPool.getId());
                updateDetail.setHistorySettleFlag(1);

                orderDetailPoolUpdateList.add(updateDetail);
                historyUpdateList
                        .addAll(historyList.stream().map(HistorySettleBillDto::getId).collect(Collectors.toList()));
            }
        }

        log.info("修复已对账未开票的历史数据未找到明细的有：{}个", haveNoOrderDetailList.size());
        if (CollectionUtils.isNotEmpty(haveNoOrderDetailList)) {
            purchaseOrderInfoPoolMapper.updateHistorySettleBillList(haveNoOrderDetailList, 2);
        }

        log.info("修复已对账未开票找到明细的有：{}个", historyUpdateList.size());
        if (CollectionUtils.isNotEmpty(historyUpdateList)) {
            purchaseOrderInfoPoolMapper.updateHistorySettleBillList(historyUpdateList, 1);
        }

        log.info("需要更新的生命周期的明细个数：{}", orderDetailPoolUpdateList.size());
        if (CollectionUtils.isNotEmpty(orderDetailPoolUpdateList)) {
            orderDetailPoolService.updateBatchById(orderDetailPoolUpdateList);
        }

        return "修复已对账未开票的数据的状态完成！";
    }

    @Transactional
    public String fixNewMallSettleBillYfl(String billSn) {
        log.info("账单编号：{}", billSn);
        QueryWrapper<SettleShopBill> settleShopBillQueryWrapper = new QueryWrapper<>();
        settleShopBillQueryWrapper.lambda().eq(SettleShopBill::getBillSn, billSn);
        SettleShopBill one = settleShopBillService.getOne(settleShopBillQueryWrapper);
        if (one == null) {
            throw new ParameterException("未查询到账单编号为：{}的账单信息", billSn);
        }

        // 查询账单明细数据
        List<YflNemMallSettleBillDto> yflNemMallSettleBillDtoList = purchaseOrderInfoPoolMapper
                .getYflNewMallSettleBillList(one.getBillId());
        if (CollectionUtils.isEmpty(yflNemMallSettleBillDtoList)) {
            throw new ParameterException("未查询到相关的账单明细！");
        }

        log.info("账单编号：{}的明细有：{}个", billSn, yflNemMallSettleBillDtoList.size());

        List<String> orderDetailIdList = yflNemMallSettleBillDtoList.stream()
                .map(YflNemMallSettleBillDto::getOrderDetailId).distinct().collect(Collectors.toList());

        // 查询生命周期数据
        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.YFLMALL.getCode())
                .eq(OrderDetailPool::getNewBilFixFlag, 1)
                .eq(OrderDetailPool::getHistoryBillFixFlag, 0)
                .eq(OrderDetailPool::getHistorySettleFlag, 0)
                .in(OrderDetailPool::getOrderDetailId, orderDetailIdList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getCustomerCheckoutNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            throw new ParameterException("未查询到符合条件的订单明细生命周期数据");
        }
        // 更新对账结算数据
        Map<String, YflNemMallSettleBillDto> billDetailMap = yflNemMallSettleBillDtoList.stream()
                .collect(Collectors.toMap(YflNemMallSettleBillDto::getOrderDetailId, Function.identity()));

        Map<String, OrderDetailPool> orderDetailMap = orderDetailPoolList.stream()
                .collect(Collectors.toMap(OrderDetailPool::getOrderDetailId, Function.identity()));

        List<Long> billDetailIdNoPool = new ArrayList<>();
        List<SettleShopBillDetail> settleShopBillDetailListForUpdate = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolForUpdate = new ArrayList<>();

        for (String orderDetailId : billDetailMap.keySet()) {
            OrderDetailPool orderDetailPool = orderDetailMap.get(orderDetailId);
            YflNemMallSettleBillDto yflNemMallSettleBillDto = billDetailMap.get(orderDetailId);
            if (orderDetailPool == null) {
                billDetailIdNoPool.add(yflNemMallSettleBillDto.getDetailId());
                continue;
            }

            SettleShopBillDetail updateShopBillDetail = new SettleShopBillDetail();
            updateShopBillDetail.setDetailId(yflNemMallSettleBillDto.getDetailId());

            updateShopBillDetail.setReconciliationStatus(6);
            updateShopBillDetail.setReconciliationUserId(6358L);
            updateShopBillDetail.setReconciliationUserName("李启茹");
            updateShopBillDetail.setReconciliationConfirmUserId(6358L);
            updateShopBillDetail.setReconciliationConfirmUserName("李启茹");
            updateShopBillDetail.setReconciliationConfirmTime(new Date());
            updateShopBillDetail.setBillMatchReason("settle");
            updateShopBillDetail.setInvoicedQuantity(yflNemMallSettleBillDto.getCheckedNum());

            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setCustomerReconciliationNum(yflNemMallSettleBillDto.getCheckedNum());
            updateOrderDetailPool.setCustomerInvoicedNum(yflNemMallSettleBillDto.getCheckedNum());
            updateOrderDetailPool.setCustomerInvoicedMoney(yflNemMallSettleBillDto.getGoodsPayIntegral());
            updateOrderDetailPool.setCustomerSettlementNum(yflNemMallSettleBillDto.getCheckedNum());
            updateOrderDetailPool.setCustomerSettlementMoney(yflNemMallSettleBillDto.getGoodsPayIntegral());

            BigDecimal reconciliationFlag = orderDetailPool.getConfirmNum()
                    .subtract(yflNemMallSettleBillDto.getCheckedNum()).subtract(orderDetailPool.getAfterSaleNum());
            if (reconciliationFlag.compareTo(BigDecimal.ZERO) > 0) {
                updateOrderDetailPool.setCustomerReconciliationStatus(1);
            } else if (reconciliationFlag.compareTo(BigDecimal.ZERO) == 0) {
                updateOrderDetailPool.setCustomerReconciliationStatus(2);
            } else {
                updateOrderDetailPool.setCustomerReconciliationStatus(3);
            }

            updateOrderDetailPool.setCustomerInvoiceStatus(updateOrderDetailPool.getCustomerReconciliationStatus());
            updateOrderDetailPool.setCustomerSettlementStatus(updateOrderDetailPool.getCustomerReconciliationStatus());
            updateOrderDetailPool.setHistorySettleFlag(1);

            settleShopBillDetailListForUpdate.add(updateShopBillDetail);
            orderDetailPoolForUpdate.add(updateOrderDetailPool);
        }

        // 更新账单明细对账状态
        log.info("账单存在但是没找到生命周期的账单明细个数有：{}", billDetailIdNoPool.size());
        if (CollectionUtils.isNotEmpty(billDetailIdNoPool)) {
            UpdateWrapper<SettleShopBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(SettleShopBillDetail::getBillMatchReason, "noDetailPool")
                    .in(SettleShopBillDetail::getDetailId, billDetailIdNoPool);
            settleShopBillDetailService.update(updateWrapper);
        }

        log.info("与生命周期匹配上的订单明细个数：{}，马上进行数据更新", settleShopBillDetailListForUpdate.size());
        if (CollectionUtils.isNotEmpty(settleShopBillDetailListForUpdate)) {
            settleShopBillDetailService.updateBatchById(settleShopBillDetailListForUpdate);
        }

        log.info("需要更新的账单生命周期的个数：{}", orderDetailPoolForUpdate.size());
        if (CollectionUtils.isNotEmpty(orderDetailPoolForUpdate)) {
            orderDetailPoolService.updateBatchById(orderDetailPoolForUpdate);
        }

        QueryWrapper<SettleShopBillPostageDetail> postageDetailQueryWrapper = new QueryWrapper<>();
        postageDetailQueryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, one.getBillId());
        List<SettleShopBillPostageDetail> billPostageDetails = settleShopBillPostageDetailService
                .list(postageDetailQueryWrapper);
        log.info("准备更新邮费数据个数：{}", billPostageDetails.size());
        if (CollectionUtils.isNotEmpty(billPostageDetails)) {
            UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .in(SettleShopBillPostageDetail::getBillDetailPostageId,
                            billPostageDetails.stream().map(SettleShopBillPostageDetail::getBillDetailPostageId)
                                    .collect(Collectors.toList()))
                    .set(SettleShopBillPostageDetail::getInvoiceFlag, 6);
            settleShopBillPostageDetailService.update(updateWrapper);
        }

        return "账单编号[" + billSn + "]的结算数据已经更新完成！";

    }

    @Transactional
    public void importSupplierSettleData(MultipartFile file) throws IOException {
        List<SupplierSettleDataDto> supplierSettleDataDtos = ExcelUtils.read(file, SupplierSettleDataDto.class);
        log.info("supplierSettleDataDtos:{}", supplierSettleDataDtos.get(0));
        Lists.partition(supplierSettleDataDtos, 5000)
                .forEach(sublist -> purchaseOrderInfoPoolMapper.saveSupplierSettleDataList(sublist));
    }

    /**
     * 修复历史的供应商结算数据
     *
     * @return
     */
    @Transactional
    public String fixHistorySupplierSettle() {
        log.info("开始准备修复历史供应商结算数据");
        List<SupplierSettleDataDto> supplierSettleDataDtoList = purchaseOrderInfoPoolMapper
                .getFixSupplierSettleDataList();
        if (CollectionUtils.isEmpty(supplierSettleDataDtoList)) {
            throw new ParameterException("无历史已结算的供应商账单数据需要修复");
        }
        log.info("查询历史已结算供应商数据条数：{}", supplierSettleDataDtoList.size());

        List<String> orderNumberList = supplierSettleDataDtoList.stream().map(SupplierSettleDataDto::getOrderNumber)
                .distinct().collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderDetailPool::getOrderNumber, orderNumberList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getSupplierUnitPriceTax,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getOrderNumber,
                        OrderDetailPool::getGoodsSku);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(queryWrapper);

        if (CollectionUtils.isEmpty(orderDetailPools)) {
            log.info("供应商已结算的数据在订单明细生命周期中未查询到，将历史数据更新状态为异常！");
            purchaseOrderInfoPoolMapper.updateSupplierSettleDataStatus(
                    supplierSettleDataDtoList.stream().map(SupplierSettleDataDto::getId).collect(Collectors.toList()),
                    2);
            return "未找到生命周期的历史结算数据，更新供应商历史已结算数据状态完成";
        }

        Map<String, List<SupplierSettleDataDto>> orderNumberMapHistory = supplierSettleDataDtoList.stream()
                .collect(Collectors.groupingBy(SupplierSettleDataDto::getOrderNumber));

        Map<String, List<OrderDetailPool>> orderNumberMapPool = orderDetailPools.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));

        List<Long> noOrderDetailPoolList = new ArrayList<>();
        List<OrderDetailPool> updateOrderDetailList = new ArrayList<>();
        List<Long> haveOrderDetailPoolList = new ArrayList<>();

        for (String orderNumber : orderNumberMapHistory.keySet()) {
            List<SupplierSettleDataDto> supplierSettleDataDtos = orderNumberMapHistory.get(orderNumber);
            List<OrderDetailPool> orderDetailPoolList = orderNumberMapPool.get(orderNumber);
            if (CollectionUtils.isEmpty(orderDetailPoolList)) {
                noOrderDetailPoolList.addAll(
                        supplierSettleDataDtos.stream().map(SupplierSettleDataDto::getId).collect(Collectors.toList()));
                continue;
            }

            Map<String, List<SupplierSettleDataDto>> skuMapHistory = supplierSettleDataDtos.stream()
                    .collect(Collectors.groupingBy(SupplierSettleDataDto::getGoodsSku));
            Map<String, OrderDetailPool> skuDetailMap = orderDetailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            for (String goodsSku : skuMapHistory.keySet()) {
                OrderDetailPool orderDetailPool = skuDetailMap.get(goodsSku);
                List<SupplierSettleDataDto> supplierSettleDataDtoSkuList = skuMapHistory.get(goodsSku);

                if (orderDetailPool == null) {
                    noOrderDetailPoolList.addAll(supplierSettleDataDtoSkuList.stream().map(SupplierSettleDataDto::getId)
                            .collect(Collectors.toList()));
                    continue;
                }

                OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
                updateOrderDetailPool.setId(orderDetailPool.getId());

                BigDecimal totalCheckNum = supplierSettleDataDtoSkuList.stream()
                        .map(SupplierSettleDataDto::getCheckedNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                updateOrderDetailPool.setSupplierInvoicedNum(totalCheckNum);
                updateOrderDetailPool.setSupplierSettlementNum(totalCheckNum);

                BigDecimal settleMoneyTax = totalCheckNum.multiply(orderDetailPool.getSupplierUnitPriceTax());
                updateOrderDetailPool.setSupplierInvoicedMoney(settleMoneyTax);
                updateOrderDetailPool.setSupplierSettlementMoney(settleMoneyTax);

                BigDecimal settleFlag = orderDetailPool.getConfirmNum().subtract(totalCheckNum)
                        .subtract(orderDetailPool.getAfterSaleNum());

                if (settleFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setSupplierInvoiceStatus(1);
                } else if (settleFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setSupplierInvoiceStatus(2);
                } else {
                    updateOrderDetailPool.setSupplierInvoiceStatus(3);
                }

                updateOrderDetailPool.setSupplierSettlementStatus(updateOrderDetailPool.getSupplierInvoiceStatus());
                updateOrderDetailList.add(updateOrderDetailPool);

                haveOrderDetailPoolList.addAll(supplierSettleDataDtoSkuList.stream().map(SupplierSettleDataDto::getId)
                        .collect(Collectors.toList()));
            }

        }

        log.info("历史结算数据未找到明细的有：{}个", noOrderDetailPoolList.size());
        if (CollectionUtils.isNotEmpty(noOrderDetailPoolList)) {
            purchaseOrderInfoPoolMapper.updateSupplierSettleDataStatus(noOrderDetailPoolList, 2);
        }

        log.info("需要更新的订单明细生命周期有：{}个", updateOrderDetailList.size());
        if (CollectionUtils.isNotEmpty(updateOrderDetailList)) {
            orderDetailPoolService.updateBatchById(updateOrderDetailList);
        }

        log.info("历史已结算数据匹配完成的有：{}个", haveOrderDetailPoolList);
        if (CollectionUtils.isNotEmpty(haveOrderDetailPoolList)) {
            purchaseOrderInfoPoolMapper.updateSupplierSettleDataStatus(haveOrderDetailPoolList, 1);
        }

        return "供应商历史结算数据已完成修复！";
    }

    /**
     * 修复24年线上供应商账单结算数据
     *
     * @param billSn
     * @param settleStatus 1.仅开票 2.开票且已结算
     * @return
     */
    @Transactional
    public String fixOnlineSupplierSettle(String billSn, Integer settleStatus) {
        log.info("准备修复线上账单：{},结算状态：{}", billSn, settleStatus);

        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getBillSn, billSn);
        SettleShopBill settleShopBill = settleShopBillService.getOne(queryWrapper);
        if (settleShopBill == null) {
            throw new ParameterException("未查询到账单！");
        }

        if (!BillCustomerTypeEnum.SUPPLIER.getCode().equals(settleShopBill.getCustomerType())) {
            throw new ParameterException("非供应商账单！");
        }

        QueryWrapper<SettleShopBillDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.lambda().eq(SettleShopBillDetail::getBillId, settleShopBill.getBillId())
                .select(SettleShopBillDetail::getDetailId,
                        SettleShopBillDetail::getOrderNumber,
                        SettleShopBillDetail::getGoodsSku,
                        SettleShopBillDetail::getCheckedNum,
                        SettleShopBillDetail::getTotalPriceTax);
        List<SettleShopBillDetail> settleShopBillDetailList = settleShopBillDetailService.list(detailQueryWrapper);
        if (CollectionUtils.isEmpty(settleShopBillDetailList)) {
            throw new ParameterException("无账单明细！");
        }

        List<String> orderNumberList = settleShopBillDetailList.stream().map(SettleShopBillDetail::getOrderNumber)
                .distinct().collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> poolQueryWrapper = new QueryWrapper<>();
        poolQueryWrapper.lambda().in(OrderDetailPool::getOrderNumber, orderNumberList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderNumber,
                        OrderDetailPool::getGoodsSku,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum);
        List<OrderDetailPool> detailPoolList = orderDetailPoolService.list(poolQueryWrapper);
        if (CollectionUtils.isEmpty(detailPoolList)) {
            log.info("根据账单查询生命周期数据为空，标记账单明细,个数：{}", settleShopBillDetailList.size());
            UpdateWrapper<SettleShopBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(SettleShopBillDetail::getBillMatchReason, "supplierSettleNoMatch")
                    .in(SettleShopBillDetail::getDetailId, settleShopBillDetailList.stream()
                            .map(SettleShopBillDetail::getDetailId).collect(Collectors.toList()));
            settleShopBillDetailService.update(updateWrapper);
        }

        Map<String, List<OrderDetailPool>> orderNumberPoolMap = detailPoolList.stream()
                .collect(Collectors.groupingBy(OrderDetailPool::getOrderNumber));
        Map<String, List<SettleShopBillDetail>> orderNumberBillMap = settleShopBillDetailList.stream()
                .collect(Collectors.groupingBy(SettleShopBillDetail::getOrderNumber));

        List<Long> noPoolList = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolListForUpdate = new ArrayList<>();
        List<Long> havePoolList = new ArrayList<>();

        for (String orderNumber : orderNumberBillMap.keySet()) {
            List<SettleShopBillDetail> shopBillDetails = orderNumberBillMap.get(orderNumber);
            List<OrderDetailPool> orderDetailPoolList = orderNumberPoolMap.get(orderNumber);
            if (CollectionUtils.isEmpty(orderDetailPoolList)) {
                noPoolList.addAll(
                        shopBillDetails.stream().map(SettleShopBillDetail::getDetailId).collect(Collectors.toList()));
                continue;
            }

            Map<String, List<SettleShopBillDetail>> skuBillMap = shopBillDetails.stream()
                    .collect(Collectors.groupingBy(SettleShopBillDetail::getGoodsSku));
            Map<String, OrderDetailPool> skuPoolMap = orderDetailPoolList.stream()
                    .collect(Collectors.toMap(OrderDetailPool::getGoodsSku, Function.identity()));

            for (String goodsSku : skuBillMap.keySet()) {
                OrderDetailPool orderDetailPool = skuPoolMap.get(goodsSku);
                List<SettleShopBillDetail> billDetails = skuBillMap.get(goodsSku);

                if (orderDetailPool == null) {
                    noPoolList.addAll(
                            billDetails.stream().map(SettleShopBillDetail::getDetailId).collect(Collectors.toList()));
                    continue;
                }

                // 准备更新数据
                OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
                updateOrderDetailPool.setId(orderDetailPool.getId());
                BigDecimal supplierInvoiceNum = billDetails.stream().map(SettleShopBillDetail::getCheckedNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal supplierInvoiceMoney = billDetails.stream().map(SettleShopBillDetail::getTotalPriceTax)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal invoiceFlag = orderDetailPool.getConfirmNum().subtract(supplierInvoiceNum)
                        .subtract(orderDetailPool.getAfterSaleNum());
                if (invoiceFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setSupplierInvoiceStatus(1);
                } else if (invoiceFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setSupplierInvoiceStatus(2);
                } else {
                    updateOrderDetailPool.setSupplierInvoiceStatus(3);
                }

                updateOrderDetailPool.setSupplierInvoicedNum(supplierInvoiceNum);
                updateOrderDetailPool.setSupplierInvoicedMoney(supplierInvoiceMoney);

                if (settleStatus == 2) {
                    // 已结算了
                    updateOrderDetailPool.setSupplierSettlementNum(supplierInvoiceNum);
                    updateOrderDetailPool.setSupplierSettlementMoney(supplierInvoiceMoney);
                    updateOrderDetailPool.setSupplierSettlementStatus(updateOrderDetailPool.getSupplierInvoiceStatus());
                }

                orderDetailPoolListForUpdate.add(updateOrderDetailPool);
                havePoolList.addAll(
                        billDetails.stream().map(SettleShopBillDetail::getDetailId).collect(Collectors.toList()));
            }

        }

        log.info("账单明细没有匹配账单生命周期的个数有：{}", noPoolList.size());
        if (CollectionUtils.isNotEmpty(noPoolList)) {
            UpdateWrapper<SettleShopBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(SettleShopBillDetail::getBillMatchReason, "supplierSettleNoMatch")
                    .in(SettleShopBillDetail::getDetailId, noPoolList);
            settleShopBillDetailService.update(updateWrapper);
        }

        log.info("生命周期匹配到的明细有：{}", orderDetailPoolListForUpdate.size());
        if (CollectionUtils.isNotEmpty(orderDetailPoolListForUpdate)) {
            orderDetailPoolService.updateBatchById(orderDetailPoolListForUpdate);
        }

        log.info("账单明细匹配完成，需要更新状态的个数有:{}", havePoolList.size());
        if (CollectionUtils.isNotEmpty(havePoolList)) {
            UpdateWrapper<SettleShopBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(SettleShopBillDetail::getBillMatchReason, "supplierSettle")
                    .in(SettleShopBillDetail::getDetailId, havePoolList);
            settleShopBillDetailService.update(updateWrapper);
        }

        return "修复账单：[" + billSn + "]结算状态完成！";
    }

    @Transactional
    public String fixPurchaseOrderType(Integer orderSalesChannel) {

        List<PurchaseTypeFixDto> purchaseTypeFixDtoList = purchaseOrderInfoPoolMapper
                .getFixPurchaseTypeList(orderSalesChannel);

        if (CollectionUtils.isEmpty(purchaseTypeFixDtoList)) {
            throw new ParameterException("无需要修复的数据,销售渠道：{}", OrderSalesChannelEnum.get(orderSalesChannel));
        }

        if (!OrderSalesChannelEnum.DFMALL.getCode().equals(orderSalesChannel)) {
            throw new ParameterException("只支持东风商城的数据修复！");
        }

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (PurchaseTypeFixDto purchaseTypeFixDto : purchaseTypeFixDtoList) {
            OrderDetailPool orderDetailPool = DataAdapter.convert(purchaseTypeFixDto, OrderDetailPool.class);
            PurchaseOrderInfoPool purchaseOrderInfoPool = new PurchaseOrderInfoPool();
            purchaseOrderInfoPool.setCompanyCode(purchaseTypeFixDto.getCompanyCode());
            purchaseOrderInfoPool.setCompanyName(purchaseTypeFixDto.getCompanyName());
            purchaseOrderInfoPool.setOrderSalesChannel(orderSalesChannel);

            OrderPoolPurchaseTypeUtils.fillPurchaseTypeInfo(orderDetailPool, purchaseOrderInfoPool,
                    purchaseTypeFixDto.getAddressType());

            OrderDetailPool update = new OrderDetailPool();
            update.setId(orderDetailPool.getId());
            update.setPurchaseChannel(orderDetailPool.getPurchaseChannel());
            update.setOrderPlacementScenario(orderDetailPool.getOrderPlacementScenario());
            updateList.add(update);
        }

        orderDetailPoolService.updateBatchById(updateList);
        return "字段修复成功！";
    }

    /**
     * 修复1.0历史东本账单结算状态
     * 
     * @return
     */
    @Transactional
    public String fixHondaHistorySettle() {
        log.info("准备修复1.0历史东本账单结算状态");
        List<OrderDetailPool> hondaHistorySettle = purchaseOrderInfoPoolMapper.getHondaHistorySettle();
        log.info("查询历史已对账数据条数：{}", hondaHistorySettle.size());

        if (CollectionUtils.isEmpty(hondaHistorySettle)) {
            throw new ParameterException("无数据需要去修复！");
        }

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : hondaHistorySettle) {
            OrderDetailPool updateDetailPool = new OrderDetailPool();
            updateDetailPool.setId(orderDetailPool.getId());

            updateDetailPool.setCustomerInvoicedNum(
                    orderDetailPool.getCustomerInvoicedNum().add(orderDetailPool.getCustomerReconciliationNum()));
            updateDetailPool.setCustomerSettlementNum(
                    orderDetailPool.getCustomerSettlementNum().add(orderDetailPool.getCustomerReconciliationNum()));

            BigDecimal taxRow = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForBill(orderDetailPool.getTaxRate(),
                    orderDetailPool.getGoodsUnitPriceNaked(),
                    orderDetailPool.getGoodsUnitPriceTax(),
                    CustomerPriceModeEnum.NAKED_PRICE.getCode(),
                    orderDetailPool.getCustomerReconciliationNum());

            updateDetailPool.setCustomerInvoicedMoney(orderDetailPool.getCustomerInvoicedMoney().add(taxRow));
            updateDetailPool.setCustomerSettlementMoney(orderDetailPool.getCustomerSettlementMoney().add(taxRow));

            if (orderDetailPool.getConfirmNum().compareTo(updateDetailPool.getCustomerInvoicedNum()) > 0) {
                updateDetailPool.setCustomerInvoiceStatus(1);
            } else if (orderDetailPool.getConfirmNum().compareTo(updateDetailPool.getCustomerInvoicedNum()) == 0) {
                updateDetailPool.setCustomerInvoiceStatus(2);
            } else {
                updateDetailPool.setCustomerInvoiceStatus(3);
            }

            if (orderDetailPool.getConfirmNum().compareTo(updateDetailPool.getCustomerSettlementNum()) > 0) {
                updateDetailPool.setCustomerSettlementStatus(1);
            } else if (orderDetailPool.getConfirmNum().compareTo(updateDetailPool.getCustomerSettlementNum()) == 0) {
                updateDetailPool.setCustomerSettlementStatus(2);
            } else {
                updateDetailPool.setCustomerSettlementStatus(3);
            }
            updateDetailPool.setHistorySettleFlag(1);

            updateList.add(updateDetailPool);
        }

        log.info("准备更新1.0历史东本的账单结算数据，数量：{}", updateList.size());
        orderDetailPoolService.updateBatchById(updateList);
        log.info("完成更新");
        return "1.0东本结算状态已修复！";
    }

    /**
     * 修复东风本田2.0线上账单结算数据到订单生命周期
     *
     * @return
     */
    @Transactional
    public String fixHondaNewMallBill(String companyCode) {
        log.info("准备修复2.0东本账单数据，企业编码：{}", companyCode);

        List<BillDetailFixDto> BillDetailFixDtoList = purchaseOrderInfoPoolMapper.getHondaNewMallBill(companyCode);
        log.info("账单数据量：{}", BillDetailFixDtoList.size());

        List<BillDetailInvoiceFixDto> billDetailInvoiceFixDtoList = purchaseOrderInfoPoolMapper
                .getDfmallInvoiceDetailForCompany(companyCode);
        log.info("发票数据量：{}", billDetailInvoiceFixDtoList.size());

        Map<String, List<BillDetailFixDto>> billDetailMap = BillDetailFixDtoList.stream()
                .collect(Collectors.groupingBy(BillDetailFixDto::getOrderDetailId));
        Map<String, List<BillDetailInvoiceFixDto>> billDetailFixDtoMap = billDetailInvoiceFixDtoList.stream()
                .collect(Collectors.groupingBy(BillDetailInvoiceFixDto::getOrderDetailId));

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.DFMALL.getCode())
                .in(OrderDetailPool::getOrderDetailId, billDetailMap.keySet())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getCustomerAcceptanceNum,
                        OrderDetailPool::getCustomerCheckoutNum,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getCustomerInvoicedNum,
                        OrderDetailPool::getCustomerInvoicedMoney,
                        OrderDetailPool::getCustomerSettlementNum,
                        OrderDetailPool::getCustomerSettlementMoney,
                        OrderDetailPool::getAfterSaleNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            throw new ParameterException("未查询到企业：{}的订单明细", orderDetailPoolList);
        }

        List<OrderDetailPool> updateOrderDetailPoolList = new ArrayList<>();

        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {

            List<BillDetailFixDto> billDetailFixDtoList = billDetailMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateDetail = new OrderDetailPool();
            updateDetail.setId(orderDetailPool.getId());

            BigDecimal checkNum = billDetailFixDtoList.stream().map(BillDetailFixDto::getCheckNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            updateDetail.setCustomerAcceptanceNum(orderDetailPool.getCustomerAcceptanceNum().add(checkNum));
            updateDetail.setCustomerCheckoutNum(orderDetailPool.getCustomerCheckoutNum().add(checkNum));
            updateDetail.setCustomerReconciliationNum(orderDetailPool.getCustomerReconciliationNum().add(checkNum));

            if (orderDetailPool.getConfirmNum().compareTo(updateDetail.getCustomerReconciliationNum()) > 0) {
                updateDetail.setCustomerReconciliationStatus(1);
                updateDetail.setCustomerAcceptanceStatus(1);
                updateDetail.setCustomerCheckoutStatus(1);
            } else if (orderDetailPool.getConfirmNum().compareTo(updateDetail.getCustomerReconciliationNum()) == 0) {
                updateDetail.setCustomerReconciliationStatus(2);
                updateDetail.setCustomerAcceptanceStatus(2);
                updateDetail.setCustomerCheckoutStatus(2);
            } else {
                updateDetail.setCustomerReconciliationStatus(3);
                updateDetail.setCustomerAcceptanceStatus(3);
                updateDetail.setCustomerCheckoutStatus(3);
            }

            List<BillDetailInvoiceFixDto> billDetailInvoiceFixDtos = billDetailFixDtoMap
                    .get(orderDetailPool.getOrderDetailId());

            if (CollectionUtils.isNotEmpty(billDetailInvoiceFixDtos)) {
                BigDecimal invoicedNum = billDetailInvoiceFixDtos.stream().map(BillDetailInvoiceFixDto::getInvoiceNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal invoicedMoney = billDetailInvoiceFixDtos.stream()
                        .map(BillDetailInvoiceFixDto::getInvoicedMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                updateDetail.setCustomerInvoicedNum(orderDetailPool.getCustomerInvoicedNum().add(invoicedNum));
                updateDetail.setCustomerInvoicedMoney(orderDetailPool.getCustomerInvoicedMoney().add(invoicedMoney));

                if (orderDetailPool.getConfirmNum().compareTo(updateDetail.getCustomerInvoicedNum()) > 0) {
                    updateDetail.setCustomerInvoiceStatus(1);
                } else if (orderDetailPool.getConfirmNum().compareTo(updateDetail.getCustomerInvoicedNum()) == 0) {
                    updateDetail.setCustomerInvoiceStatus(2);
                } else {
                    updateDetail.setCustomerInvoiceStatus(3);
                }

                List<BillDetailInvoiceFixDto> settleList = billDetailInvoiceFixDtos.stream()
                        .filter(e -> e.getRepaymentStatus() == 2).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(settleList)) {
                    BigDecimal settleNum = settleList.stream().map(BillDetailInvoiceFixDto::getInvoiceNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal settleMoney = settleList.stream().map(BillDetailInvoiceFixDto::getInvoicedMoney)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    updateDetail.setCustomerSettlementNum(orderDetailPool.getCustomerSettlementNum().add(settleNum));
                    updateDetail
                            .setCustomerSettlementMoney(orderDetailPool.getCustomerSettlementMoney().add(settleMoney));

                    if (orderDetailPool.getConfirmNum().compareTo(updateDetail.getCustomerSettlementNum()) > 0) {
                        updateDetail.setCustomerSettlementStatus(1);
                    } else if (orderDetailPool.getConfirmNum()
                            .compareTo(updateDetail.getCustomerSettlementNum()) == 0) {
                        updateDetail.setCustomerSettlementStatus(2);
                    } else {
                        updateDetail.setCustomerSettlementStatus(3);
                    }
                }
            }
            updateDetail.setNewBilFixFlag(1);
            updateOrderDetailPoolList.add(updateDetail);
        }

        log.info("企业：{}需要更新的数据量：{}", companyCode, updateOrderDetailPoolList.size());
        orderDetailPoolService.updateBatchById(updateOrderDetailPoolList);
        log.info("修复完成！");
        return "数据修复完成！";
    }

    @Transactional
    public String fixHondaInvoice(String invoiceApplyNumber) {
        QueryWrapper<InvoiceBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InvoiceBill::getInvoiceApplyNumber, invoiceApplyNumber);
        InvoiceBill invoiceBill = invoiceBillService.getOne(queryWrapper);

        List<BillInvoiceDetailDto> billInvoiceDetailDtoList = settleShopBillDetailService.getBaseMapper()
                .getInvoiceRejectBillDetail(invoiceBill.getId());
        LoginUser loginUser = LocalUserHolder.get();
        List<SettleShopBillDetail> updateShopBillDetailList = new ArrayList<>();
        List<SettleBillLifeCycle> updateLifeCycleList = new ArrayList<>();
        List<UpdateOrderDetailDto> updateOrderDetailDtoList = new ArrayList<>();

        for (BillInvoiceDetailDto billInvoiceDetailDto : billInvoiceDetailDtoList) {
            SettleShopBillDetail updateSettleBillDetail = new SettleShopBillDetail();
            UpdateOrderDetailDto updateOrderDetailDto = new UpdateOrderDetailDto();
            updateSettleBillDetail.setDetailId(billInvoiceDetailDto.getDetailId());
            updateSettleBillDetail.setUpdateTime(new Date());
            updateSettleBillDetail.setModifier(loginUser.getUsername());

            SettleBillLifeCycle updateLifeCycle = new SettleBillLifeCycle();
            updateLifeCycle.setId(billInvoiceDetailDto.getLifeCycleId());
            updateLifeCycle.setUpdateTime(new Date());
            updateLifeCycle.setModifier(loginUser.getUsername());

            // 出账数量
            BigDecimal checkedNum = billInvoiceDetailDto.getCheckedNum();
            // 已开票数量
            BigDecimal invoicedQuantity = billInvoiceDetailDto.getInvoicedQuantity();

            if (checkedNum.compareTo(invoicedQuantity) > 0) {
                // 部分开票
                updateSettleBillDetail.setReconciliationStatus(
                        ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode());
                updateLifeCycle.setCustomerInvoicingStatus(
                        ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode());
            } else if (checkedNum.compareTo(invoicedQuantity) == 0) {
                // 已开票
                updateSettleBillDetail
                        .setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode());
                updateLifeCycle
                        .setCustomerInvoicingStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode());

            } else {
                // 开票数量有异常
                log.info("开票数量大于出账数量 billInvoiceDetailDto:{}", billInvoiceDetailDto);
                throw new ParameterException("开票数量大于出账数量！");
            }

            updateOrderDetailDto.setOrderDetailId(billInvoiceDetailDto.getOrderDetailId());
            updateOrderDetailDto.setInvoiceNum(billInvoiceDetailDto.getInvoiceNum());
            updateOrderDetailDto.setInvoiceMoney(billInvoiceDetailDto.getGoodsTotalPriceTax());

            updateShopBillDetailList.add(updateSettleBillDetail);
            updateLifeCycleList.add(updateLifeCycle);
            updateOrderDetailDtoList.add(updateOrderDetailDto);
        }

        settleShopBillDetailService.updateBatchById(updateShopBillDetailList);
        settleBillLifeCycleService.updateBatchById(updateLifeCycleList);
        return "发票申请单的账单结算数据修复完成，发票号为：" + invoiceApplyNumber;
    }

    @Transactional
    public String fixYflHistoryInvoiceBill() {

       List<YflHistoryInvoiceDto> yflHistoryInvoiceDtoList = purchaseOrderInfoPoolMapper.getyflHistoryInvoiceBill();

        for (YflHistoryInvoiceDto yflHistoryInvoiceDto : yflHistoryInvoiceDtoList) {
            InvoiceBill invoiceBill = new InvoiceBill();
            String invoiceTime = yflHistoryInvoiceDto.getInvoiceTime();

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parsedDate = null;
            try {
                parsedDate = dateFormat.parse(invoiceTime);
            } catch (ParseException e) {
                e.printStackTrace();
                throw new ParameterException("转换时间失败");
            }
            log.info("parsedDate:{}", parsedDate);

            // 获取当前时间的Calendar对象
            Calendar calendar = Calendar.getInstance();
            // 设置日期部分
            calendar.setTime(parsedDate);

            // 获取当前时间的时分秒
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            int millis = calendar.get(Calendar.MILLISECOND);

            // 设置Calendar对象的时分秒
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, second);
            calendar.set(Calendar.MILLISECOND, millis);

            // 获取最终的Date对象
            Date finalDate = calendar.getTime();

            // 打印结果
            log.info("最终的Date对象: " + finalDate);
            String invoiceApplyNumber ="";//FLH240718 00 00 00 000 47198
            if (yflHistoryInvoiceDto.getTaxRate() == 6) {
                invoiceApplyNumber = "FLPH" + DateUtil.format(finalDate, "yyMMdd")+RandomUtil.randomInt(*********,*********) + RandomUtil.randomInt(10000, 99999);
                invoiceBill.setBillInvoiceType(1);
            } else {
                invoiceApplyNumber = "FLH" + DateUtil.format(finalDate, "yyMMdd")+RandomUtil.randomInt(*********,*********) + RandomUtil.randomInt(10000, 99999);
                invoiceBill.setBillInvoiceType(0);
            }

            invoiceBill.setInvoiceApplyNumber(invoiceApplyNumber);
            invoiceBill.setInvoiceNumber(yflHistoryInvoiceDto.getInvoiceNumber());
            invoiceBill.setApplyerId(6358L);
            invoiceBill.setApplyerName("李启茹");
            invoiceBill.setConfirmerId("6358");
            invoiceBill.setConfirmerName("李启茹");
            invoiceBill.setApplyTime(finalDate);
            invoiceBill.setInvoiceTime(finalDate);
            invoiceBill.setMsdpInvoiceTime(parsedDate);
            invoiceBill.setInvoiceSubjectName(yflHistoryInvoiceDto.getInvoiceSubject());
            invoiceBill.setCompanyCode(yflHistoryInvoiceDto.getCompanyCode());
            invoiceBill.setCompanyName(yflHistoryInvoiceDto.getCompanyName());
            invoiceBill.setInvoiceType("增值税普通发票");
            invoiceBill.setAmountNaked(BigDecimal.ZERO);
            invoiceBill.setAmountTax(yflHistoryInvoiceDto.getAmountTax());
            invoiceBill.setTaxRate(yflHistoryInvoiceDto.getTaxRate());
            invoiceBill.setState(6);
            invoiceBill.setRemark("线下福利发票修复至线上");
            invoiceBill.setTicketType(1);
            invoiceBill.setCompanyType(2);
            invoiceBill.setCustomType(2);
            invoiceBill.setCreator("07117");
            invoiceBill.setCreateTime(new Date());
            invoiceBill.setExporter("李启茹");
            invoiceBill.setExportFileUrl("-");
            invoiceBill.setMsdpState(2);
            if(yflHistoryInvoiceDto.getState()==0){
                //0：已结清 1：未结清
                invoiceBill.setRepaymentStatus(2);
                invoiceBill.setAmountReceived(yflHistoryInvoiceDto.getAmountTax());
            }else {
                invoiceBill.setRepaymentStatus(0);
            }
            invoiceBill.setPriceMode(1);
            log.info("发票主体信息为：{}",invoiceBill);
            invoiceBillService.save(invoiceBill);
        }

       return "线下福利历史发票修复成功！";
    }

    @Transactional
    public String fixDfmallHistoryInvoiceBill() {
      List<DfmallHistoryInvoiceDto> dfmallHistoryInvoiceDtoList =  purchaseOrderInfoPoolMapper.getDfmallHistoryInvoice();
      if(CollectionUtils.isEmpty(dfmallHistoryInvoiceDtoList)){
          throw new ParameterException("无数据需要修复");
      }

        for (DfmallHistoryInvoiceDto dfmallHistoryInvoiceDto : dfmallHistoryInvoiceDtoList) {
            InvoiceBill invoiceBill = new InvoiceBill();
            String invoiceTime = dfmallHistoryInvoiceDto.getInvoiceTime();

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parsedDate = null;
            try {
                parsedDate = dateFormat.parse(invoiceTime);
            } catch (ParseException e) {
                e.printStackTrace();
                throw new ParameterException("转换时间失败");
            }
            log.info("parsedDate:{}", parsedDate);

            // 获取当前时间的Calendar对象
            Calendar calendar = Calendar.getInstance();
            // 设置日期部分
            calendar.setTime(parsedDate);

            // 获取当前时间的时分秒
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            int millis = calendar.get(Calendar.MILLISECOND);

            // 设置Calendar对象的时分秒
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, second);
            calendar.set(Calendar.MILLISECOND, millis);

            // 获取最终的Date对象
            Date finalDate = calendar.getTime();

            // 打印结果
            log.info("最终的Date对象: " + finalDate);

            String invoiceApplyNumber ="FPH" + DateUtil.format(finalDate, "yyMMdd")+RandomUtil.randomInt(*********,*********) + RandomUtil.randomInt(10000, 99999);
            invoiceBill.setInvoiceApplyNumber(invoiceApplyNumber);
            invoiceBill.setInvoiceNumber(dfmallHistoryInvoiceDto.getInvoiceNumber());
            invoiceBill.setApplyerId(5061L);
            invoiceBill.setApplyerName("郭艳");
            invoiceBill.setConfirmerId("5061");
            invoiceBill.setConfirmerName("郭艳");
            invoiceBill.setApplyTime(finalDate);
            invoiceBill.setInvoiceTime(finalDate);
            invoiceBill.setMsdpInvoiceTime(parsedDate);
            invoiceBill.setInvoiceSubjectName(dfmallHistoryInvoiceDto.getInvoiceSubjectName());
            invoiceBill.setInvoiceTaxPayerNumber(dfmallHistoryInvoiceDto.getInvoiceTaxPayerNumber());
            invoiceBill.setInvoiceSubjectAddress(dfmallHistoryInvoiceDto.getInvoiceSubjectAddress());
            invoiceBill.setInvoiceBank(dfmallHistoryInvoiceDto.getInvoiceBank());
            invoiceBill.setInvoiceBankAccount(dfmallHistoryInvoiceDto.getInvoiceBankAccount());
            invoiceBill.setCompanyCode(dfmallHistoryInvoiceDto.getCompanyCode());
            invoiceBill.setCompanyName(dfmallHistoryInvoiceDto.getCompanyName());
            invoiceBill.setInvoiceType("增值税普通发票");
            BigDecimal rate = new BigDecimal("0.01").multiply(new BigDecimal(dfmallHistoryInvoiceDto.getTaxRate())).add(BigDecimal.ONE);
            invoiceBill.setAmountNaked(dfmallHistoryInvoiceDto.getAmountTax().divide(rate,2,BigDecimal.ROUND_HALF_UP));
            invoiceBill.setAmountTax(dfmallHistoryInvoiceDto.getAmountTax());
            invoiceBill.setTaxRate(dfmallHistoryInvoiceDto.getTaxRate());
            invoiceBill.setState(6);
            invoiceBill.setRemark("线下东风商城发票修复至线上");
            invoiceBill.setTicketType(1);
            invoiceBill.setCompanyType(1);
            invoiceBill.setCustomType(2);
            invoiceBill.setCreator("5061");
            invoiceBill.setExporter("郭艳");
            invoiceBill.setExportFileUrl("-");
            invoiceBill.setMsdpState(2);
            invoiceBill.setRepaymentStatus(2);
            invoiceBill.setAmountReceived(dfmallHistoryInvoiceDto.getAmountTax());
            invoiceBill.setCreateTime(finalDate);

            log.info("invoiceBill:{}",JSONUtil.toJsonStr(invoiceBill));
            invoiceBillService.save(invoiceBill);

        }

      return "东风商城转结发票数据修复完成！";
    }

    @Transactional
    public String importHondaOwnerData(List<HondaOwnerSettleData> hondaOwnerSettleDataList) {
        log.info("hondaOwnerSettleDataList.getOne:{}",JSONUtil.toJsonStr(hondaOwnerSettleDataList.get(0)));
        List<SettleBillPool> settleBillPoolList = DataAdapter.convertList(hondaOwnerSettleDataList, SettleBillPool.class);

        List<PurchaseOrderInfoPool> purchaseOrderInfoPoolList = new ArrayList<>();

        List<OrderDetailPool> orderDetailPoolList = new ArrayList<>();

        for (SettleBillPool e : settleBillPoolList) {
            e.setCompanyCode("DSDB000");
            e.setCompanyName("东风本田信赖商城");
            e.setInvoiceTypeName("增值税普通发票");
            e.setPurchaseNumber(e.getOrderNumber());
            e.setAuditTime(e.getApplyTime());
            e.setApplyDeptName("东风本田信赖商城");
            e.setOtherRelationNumber(e.getOrderNumber());
            e.setGoodsCode(e.getGoodsSku());
            e.setCusReceivingState(2);
            e.setTenantId(1L);
            e.setApplyUserId(175964L);
            e.setCreator("车主虚拟商品");

            PurchaseOrderInfoPool purchaseOrderInfoPool = new PurchaseOrderInfoPool();
            Long purchaseOrderInfoId = commonCodeGeneral.makeMysqlId();
            purchaseOrderInfoPool.setId(purchaseOrderInfoId);
            purchaseOrderInfoPool.setPurchaseNumber(e.getOrderNumber());
            purchaseOrderInfoPool.setOrderSalesChannel(OrderSalesChannelEnum.DFMALL.getCode());
            purchaseOrderInfoPool.setPurchaseState(30);
            purchaseOrderInfoPool.setApplyTime(e.getApplyTime());
            purchaseOrderInfoPool.setPurchaseGoodsPriceTax(e.getGoodsTotalPriceTax());
            purchaseOrderInfoPool.setPurchaseGoodsPriceNaked(e.getGoodsTotalPriceNaked());
            purchaseOrderInfoPool.setPurchaseFreightPrice(BigDecimal.ZERO);
            purchaseOrderInfoPool.setPurchaseTotalPriceTax(e.getGoodsTotalPriceTax());
            purchaseOrderInfoPool.setOtherRelationNumber(e.getOtherRelationNumber());
            purchaseOrderInfoPool.setApplyUserName(e.getApplyUserName());
            purchaseOrderInfoPool.setApplyUserPhone(e.getMobPhone());
            purchaseOrderInfoPool.setReceiverName(e.getAddressName());
            purchaseOrderInfoPool.setCompanyCode(e.getCompanyCode());
            purchaseOrderInfoPool.setCompanyName(e.getCompanyName());
            purchaseOrderInfoPool.setCreator("车主虚拟商品");
            purchaseOrderInfoPool.setIsFix(0);
            purchaseOrderInfoPoolList.add(purchaseOrderInfoPool);

            OrderDetailPool orderDetailPool = new OrderDetailPool();
            orderDetailPool.setId(commonCodeGeneral.makeMysqlId());
            orderDetailPool.setPurchaseInfoId(purchaseOrderInfoId);
            orderDetailPool.setOrderDetailId(e.getOrderDetailId());
            orderDetailPool.setOrderSalesChannel(OrderSalesChannelEnum.DFMALL.getCode());
            orderDetailPool.setPurchaseChannel(PurchaseChannelEnum.E_COMMERCE_PURCHASING.getCode());
            orderDetailPool.setOrderPlacementScenario(OrderPlacementScenarioEnum.MARKETINGPOINTS.getCode());
            orderDetailPool.setRowSerialNumber(1);
            orderDetailPool.setSupplierCode(e.getSupplierCode());
            orderDetailPool.setSupplierName(e.getSupplierName());
            orderDetailPool.setPurchaseNumber(e.getOrderNumber());
            orderDetailPool.setOrderNumber(e.getOrderNumber());
            orderDetailPool.setSupplierOrderNumber(e.getSupplierOrderNumber());
            orderDetailPool.setConfirmTime(e.getAuditTime());
            orderDetailPool.setIsPrePay(0);
            orderDetailPool.setGoodsCode(e.getGoodsCode());
            orderDetailPool.setGoodsSku(e.getGoodsSku());
            orderDetailPool.setGoodsImage(e.getGoodsImage());
            orderDetailPool.setGoodsDesc(e.getGoodsDesc());
            orderDetailPool.setGoodsUnitPriceNaked(e.getGoodsUnitPriceNaked());
            orderDetailPool.setGoodsUnitPriceTax(e.getGoodsUnitPriceTax());
            orderDetailPool.setGoodsTotalPriceNaked(e.getGoodsTotalPriceNaked());
            orderDetailPool.setGoodsTotalPriceTax(e.getGoodsTotalPriceTax());
            orderDetailPool.setSupplierUnitPriceNaked(e.getSupplierUnitPriceNaked());
            orderDetailPool.setSupplierUnitPriceTax(e.getSupplierUnitPriceTax());
            orderDetailPool.setSupplierTotalPriceNaked(e.getSupplierTotalPriceNaked());
            orderDetailPool.setSupplierTotalPriceTax(e.getSupplierTotalPriceTax());
            orderDetailPool.setThreeClassName(e.getThirdClassName());
            orderDetailPool.setTaxRate(e.getTaxRate());
            orderDetailPool.setTaxCode(e.getTaxCode());
            orderDetailPool.setOrderDetailState(40);
            orderDetailPool.setConfirmNum(e.getConfirmNum());
            orderDetailPool.setDeliveryNum(e.getConfirmNum());
            orderDetailPool.setDeliveryStatus(2);
            orderDetailPool.setProperDeliveryNum(e.getConfirmNum());
            orderDetailPool.setCustomerReceiptNum(e.getConfirmNum());
            orderDetailPool.setCustomerReceiptStatus(2);
            orderDetailPool.setCustomerAcceptanceStatus(2);
            orderDetailPool.setCustomerAcceptanceNum(e.getConfirmNum());
            orderDetailPool.setCreator("车主虚拟商品");
            orderDetailPoolList.add(orderDetailPool);
        }


        log.info("settleBillPoolList的个数:{}",settleBillPoolList.size());
        Lists.partition(settleBillPoolList, 1000).forEach(e -> settleBillPoolService.saveBatch(e));

        log.info("准备生成账单生命周期数据");
        Lists.partition(settleBillPoolList, 1000).forEach(e -> settleBillLifeCycleService.saveLifeCycleFromPools(e));

        log.info("采购单主表-生命周期个数：{}",purchaseOrderInfoPoolList.size());
        Lists.partition(purchaseOrderInfoPoolList,1000).forEach(e->purchaseOrderInfoPoolService.saveBatch(e));

        log.info("订单明细生命周期个数：{}",orderDetailPoolList.size());
        Lists.partition(orderDetailPoolList,1000).forEach(e->orderDetailPoolService.saveBatch(e));

        return "东本车主商城数据导入完成！";
    }

    @Transactional
    public String fixHondaOwnerInvoice(HondaOwnerInvoiceDto hondaOwnerInvoiceDto) {
        InvoiceBill invoiceBill = invoiceBillService.getById(hondaOwnerInvoiceDto.getInvoiceId());
        if (invoiceBill == null) {
            throw new ParameterException("未查询到发票！");
        }

        QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleShopBillDetail::getDetailId, hondaOwnerInvoiceDto.getSettleBillDetailIdList())
                .eq(SettleShopBillDetail::getReconciliationStatus, 2);
        List<SettleShopBillDetail> settleShopBillDetailList = settleShopBillDetailService.list(queryWrapper);

        if (CollectionUtils.isEmpty(settleShopBillDetailList)) {
            throw new ParameterException("未查询到账单明细数据！");
        }

        Map<Long, SettleShopBillDetail> shopBillDetailMap = settleShopBillDetailList.stream().
                collect(Collectors.toMap(SettleShopBillDetail::getDetailId, Function.identity(), (key1, key2) -> key1));

        QueryWrapper<SettleBillLifeCycle> lifeCycleQueryWrapper = new QueryWrapper<>();
        lifeCycleQueryWrapper.lambda().in(SettleBillLifeCycle::getSettleBillPoolId,
                settleShopBillDetailList.stream().map(SettleShopBillDetail::getSettleBillPoolId).collect(Collectors.toList()));


        List<SettleBillLifeCycle> billLifeCycleList = settleBillLifeCycleService.list(lifeCycleQueryWrapper);
        if (CollectionUtils.isEmpty(billLifeCycleList)) {
            throw new ParameterException("未查询到账单生命周期数据！");
        }
        Map<Long, SettleBillLifeCycle> billLifeCycleMap = billLifeCycleList.stream().
                collect(Collectors.toMap(SettleBillLifeCycle::getSettleBillPoolId, Function.identity(), (key1, key2) -> key1));

        List<SettleBillPool> settleBillPoolList = settleBillPoolService.
                listByIds(settleShopBillDetailList.stream().map(SettleShopBillDetail::getSettleBillPoolId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(settleBillPoolList)) {
            throw new ParameterException("未查询到池数据！");
        }
        QueryWrapper<OrderDetailPool> orderDetailPoolQueryWrapper = new QueryWrapper<>();
        orderDetailPoolQueryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.DFMALL.getCode())
                .in(OrderDetailPool::getOrderDetailId, settleBillPoolList.stream().map(SettleBillPool::getOrderDetailId).collect(Collectors.toList()));
        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(orderDetailPoolQueryWrapper);
        if (CollectionUtils.isEmpty(orderDetailPoolList)) {
            throw new ParameterException("未查询到订单生命周期数据！");
        }

        List<Integer> taxrateList = orderDetailPoolList.stream().map(OrderDetailPool::getTaxRate).distinct().collect(Collectors.toList());
        if (taxrateList.size() > 1) {
            throw new ParameterException("存在多种税率的商品！");
        }

        if (invoiceBill.getTaxRate().compareTo(taxrateList.get(0)) != 0) {
            throw new ParameterException("要添加的数据与原发票的税率不一致！");
        }

        List<InvoiceDetailBill> invoiceDetailBillList = new ArrayList<>();
        List<SettleShopBillDetail> updateForBillDetailList = new ArrayList<>();
        List<SettleBillLifeCycle> settleBillLifeCycleList = new ArrayList<>();

        BigDecimal amountNaked = invoiceBill.getAmountNaked();
        BigDecimal amountTax = invoiceBill.getAmountTax();

        for (Long detailId : hondaOwnerInvoiceDto.getSettleBillDetailIdList()) {
            SettleShopBillDetail settleShopBillDetail = shopBillDetailMap.get(detailId);
            if (settleShopBillDetail == null) {
                log.info("id：{}", detailId);
                throw new ParameterException("存在未查询到的明细数据！");
            }

            InvoiceDetailBill invoiceDetailBill = new InvoiceDetailBill();
            invoiceDetailBill.setInvoiceId(invoiceBill.getId());
            invoiceDetailBill.setBillId(settleShopBillDetail.getBillId());
            invoiceDetailBill.setBillSn(settleShopBillDetail.getBillSn());
            invoiceDetailBill.setBillDetailId(settleShopBillDetail.getDetailId());
            invoiceDetailBill.setInvoiceNum(settleShopBillDetail.getCheckedNum());
            invoiceDetailBill.setGoodsUnitPriceNaked(settleShopBillDetail.getUnitPriceNaked());
            invoiceDetailBill.setGoodsUnitPriceTax(settleShopBillDetail.getUnitPriceTax());
            invoiceDetailBill.setGoodsTotalPriceNaked(settleShopBillDetail.getTotalPriceNaked());
            invoiceDetailBill.setGoodsTotalPriceTax(settleShopBillDetail.getTotalPriceTax());
            invoiceDetailBillList.add(invoiceDetailBill);

            SettleShopBillDetail updateBillDetail = new SettleShopBillDetail();
            updateBillDetail.setDetailId(settleShopBillDetail.getDetailId());
            updateBillDetail.setInvoicableQuantity(BigDecimal.ZERO);
            updateBillDetail.setInvoicedQuantity(settleShopBillDetail.getCheckedNum());
            updateBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode());
            updateForBillDetailList.add(updateBillDetail);


            SettleBillLifeCycle billLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
            if (billLifeCycle == null) {
                throw new ParameterException("存在未查询到账单明细生命周期");
            }
            SettleBillLifeCycle settleBillLifeCycle = new SettleBillLifeCycle();
            settleBillLifeCycle.setId(billLifeCycle.getId());
            settleBillLifeCycle.setCustomerInvoicingStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode());
            settleBillLifeCycle.setCustomerInvoicingNum(settleShopBillDetail.getCheckedNum());
            settleBillLifeCycle.setCustomerInvoiceMoney(settleShopBillDetail.getTotalPriceTax());
            settleBillLifeCycle.setCustomerInvoicingTime(new Date());
            settleBillLifeCycleList.add(settleBillLifeCycle);

            amountNaked = amountNaked.add(settleShopBillDetail.getTotalPriceNaked());
            amountTax = amountTax.add(settleShopBillDetail.getTotalPriceTax());
        }


        invoiceDetailBillService.saveBatch(invoiceDetailBillList, 500);
        settleShopBillDetailService.updateBatchById(updateForBillDetailList, 500);
        settleBillLifeCycleService.updateBatchById(settleBillLifeCycleList, 500);

        UpdateWrapper<InvoiceBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(InvoiceBill::getId, invoiceBill.getId())
                .set(InvoiceBill::getAmountNaked, amountNaked)
                .set(InvoiceBill::getAmountTax, amountTax);
        invoiceBillService.update(updateWrapper);

        Map<String, List<SettleBillPool>> orderDetailIdMap = settleBillPoolList.stream().collect(Collectors.groupingBy(SettleBillPool::getOrderDetailId));

        List<OrderDetailPool> updateForOrderDetailPoolList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {
            List<SettleBillPool> settleBillPools = orderDetailIdMap.get(orderDetailPool.getOrderDetailId());
            if(CollectionUtils.isEmpty(settleBillPools)){
                throw new ParameterException("生命周期存在，未查询到池明细！");
            }

            BigDecimal checkNumAll = settleBillPools.stream().map(SettleBillPool::getCheckedNum).reduce(BigDecimal.ZERO, BigDecimal::add);

            OrderDetailPool updateDetailPool = new OrderDetailPool();
            updateDetailPool.setId(orderDetailPool.getId());
            updateDetailPool.setCustomerInvoicingNum(checkNumAll.add(orderDetailPool.getCustomerInvoicingNum()));
            updateForOrderDetailPoolList.add(updateDetailPool);
        }

        orderDetailPoolService.updateBatchById(updateForOrderDetailPoolList);

        return "修复成功！";
    }

    @Transactional(rollbackFor = Exception.class)
    @DataPermission(enable = false)
    public String fixHondaOwnerOtherRelationNumber(List<HondaOwnerOtherRelationNumber> hondaOwnerOtherRelationNumberList) {
        log.info("hondaOwnerOtherRelationNumberList:{}",JSONUtil.toJsonStr(hondaOwnerOtherRelationNumberList));

        List<String> oldOtherNumberList = hondaOwnerOtherRelationNumberList.stream()
                .map(HondaOwnerOtherRelationNumber::getOldOrderNumber).distinct().collect(Collectors.toList());

        QueryWrapper<ShopPurchaseOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ShopPurchaseOrder::getOtherRelationNumber, oldOtherNumberList)
                .select(ShopPurchaseOrder::getPurchaseId,
                        ShopPurchaseOrder::getOtherRelationNumber);
        List<ShopPurchaseOrder> shopPurchaseOrderList = shopPurchaseOrderService.list(queryWrapper);

        Map<String, String> map = hondaOwnerOtherRelationNumberList.stream().collect(Collectors.toMap(HondaOwnerOtherRelationNumber::getOldOrderNumber, HondaOwnerOtherRelationNumber::getNewOrderNumber));

        if (CollectionUtils.isNotEmpty(shopPurchaseOrderList)) {
            List<ShopPurchaseOrder> updateList = new ArrayList<>();
            shopPurchaseOrderList.forEach(e -> {
                String newOrderNumber = map.get(e.getOtherRelationNumber());
                ShopPurchaseOrder shopPurchaseOrder = new ShopPurchaseOrder();
                shopPurchaseOrder.setPurchaseId(e.getPurchaseId());
                shopPurchaseOrder.setOtherRelationNumber(newOrderNumber);
                updateList.add(shopPurchaseOrder);
            });

            shopPurchaseOrderService.updateBatchById(updateList);
        }

        QueryWrapper<SettleBillPool> poolQueryWrapper = new QueryWrapper<>();
        poolQueryWrapper.lambda().in(SettleBillPool::getOtherRelationNumber,oldOtherNumberList)
                .select(SettleBillPool::getId,SettleBillPool::getOtherRelationNumber);

        List<SettleBillPool> settleBillPoolList = settleBillPoolService.list(poolQueryWrapper);

        if(CollectionUtils.isNotEmpty(settleBillPoolList)){
            List<SettleBillPool> updateList = new ArrayList<>();
            settleBillPoolList.forEach(e->{
                SettleBillPool settleBillPool = new SettleBillPool();
                settleBillPool.setId(e.getId());
                settleBillPool.setOtherRelationNumber(map.get(e.getOtherRelationNumber()));
                updateList.add(settleBillPool);
            });

            settleBillPoolService.updateBatchById(updateList);
        }

        QueryWrapper<PurchaseOrderInfoPool> purchaseOrderInfoPoolQueryWrapper = new QueryWrapper<>();
        purchaseOrderInfoPoolQueryWrapper.lambda().eq(PurchaseOrderInfoPool::getOrderSalesChannel,OrderSalesChannelEnum.DFMALL.getCode())
                .in(PurchaseOrderInfoPool::getOtherRelationNumber,oldOtherNumberList)
        .select(PurchaseOrderInfoPool::getId,PurchaseOrderInfoPool::getOtherRelationNumber);
        List<PurchaseOrderInfoPool> purchaseOrderInfoPoolList = purchaseOrderInfoPoolService.list(purchaseOrderInfoPoolQueryWrapper);

        if(CollectionUtils.isNotEmpty(purchaseOrderInfoPoolList)){
            List<PurchaseOrderInfoPool> updateList = new ArrayList<>();
            purchaseOrderInfoPoolList.forEach(e->{
                PurchaseOrderInfoPool purchaseOrderInfoPool = new PurchaseOrderInfoPool();
                purchaseOrderInfoPool.setId(e.getId());
                purchaseOrderInfoPool.setOtherRelationNumber(map.get(e.getOtherRelationNumber()));
                updateList.add(purchaseOrderInfoPool);
            });
            purchaseOrderInfoPoolService.updateBatchById(updateList);
        }

        return "数据修复完成！";
    }

    @Transactional
    public String fixLeavePurchaseNumber(String purchaseNumber) {
        //保存预订单信息至生命周期
        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
        orderLifeCycleStrategy.savePurchaseOrderInfoForPreOrder(purchaseNumber);

        return "成功！";
    }

    @Transactional
    public String fixSupplierInvoice(Long billId) {

        SettleShopBill settleShopBill = settleShopBillService.getById(billId);
        if (settleShopBill == null) {
            throw new ParameterException("未查询到账单数据！");
        }

        List<SupplierInvoiceDelDto> supplierInvoiceDelDtoList = purchaseOrderInfoPoolService.getBaseMapper().getFixSupplierInvoice(billId);
        if (CollectionUtils.isEmpty(supplierInvoiceDelDtoList)) {
            throw new ParameterException("未查询到明细！");
        }

        Map<Integer, List<SupplierInvoiceDelDto>> orderSaleChannelMap = supplierInvoiceDelDtoList.stream()
                .collect(Collectors.groupingBy(SupplierInvoiceDelDto::getOrderSalesChannel));

        //更新账单明细,账单生命周期
        Lists.partition(supplierInvoiceDelDtoList, 1000).forEach(sublist ->
                purchaseOrderInfoPoolService.getBaseMapper().fixSupplierBillDetail(supplierInvoiceDelDtoList));
        Lists.partition(supplierInvoiceDelDtoList, 1000).forEach(subList ->
                purchaseOrderInfoPoolService.getBaseMapper().fixSupplierLifeCycle(supplierInvoiceDelDtoList));


        for (Integer orderSaleChannel : orderSaleChannelMap.keySet()) {
            OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(orderSaleChannel);
            orderLifeCycleStrategy.updatePurchaseOrderInfoForSupplierInvoice(orderSaleChannelMap.get(orderSaleChannel));
        }

        return "账单" + settleShopBill.getBillSn() + "开票数据已完成维护！";

    }
}
