package com.ly.yph.api.bill.controller.plan.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ly.yph.core.dic.DictTypeConstants;
import com.ly.yph.core.excel.annotations.DictFormat;
import com.ly.yph.core.excel.convert.DictConvert;
import com.ly.yph.core.excel.convert.LocalDateConvert;
import com.ly.yph.core.excel.convert.LocalDateTimeConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel("管理后台 - 账单计划联友易报返回对象 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MizdInvoicePlanSettleVo {

    @ApiModelProperty(value = "主键", required = true, example = "18135")
    @ExcelProperty("主键")
    private Long id;

    @ApiModelProperty(value = "支付单号 ZFDH+账单号+001(流水号)", required = true)
    @ExcelProperty("支付单号 ZFDH+账单号+001(流水号)")
    private String payNo;

    @ApiModelProperty(value = "支付计划状态 0：未发起1：审批中；2：审批通过；3：审批驳回", required = true, example = "1")
    @ExcelProperty("支付计划状态 0：未发起1：审批中；2：审批通过；3：审批驳回")
    private String payPlanStatus;

    @ApiModelProperty(value = "审批完成时间", required = true)
    @ExcelProperty(value = "审批完成时间", converter = LocalDateTimeConvert.class)
    private LocalDateTime approvedCompletedTime;

    @ApiModelProperty(value = "账单编号", required = true)
    @ExcelProperty("账单编号")
    private String invoiceNumber;

    @ApiModelProperty(value = "账单名称", required = true, example = "李四")
    @ExcelProperty("账单名称")
    private String invoiceName;

    @ApiModelProperty(value = "支付类型", required = true, example = "2")
    @ExcelProperty("支付类型")
    private String payType;

    @ApiModelProperty(value = "支付主体", required = true)
    @ExcelProperty("支付主体")
    private String payEntity;

    @ApiModelProperty(value = "采购经理", required = true)
    @ExcelProperty("采购经理")
    private String purchasingManager;

    @ApiModelProperty(value = "付款日期", required = true)
    @ExcelProperty(value = "付款日期", converter = LocalDateConvert.class)
    private LocalDate payDate;

    @ApiModelProperty(value = "项目编号", required = true)
    @ExcelProperty("项目编号")
    private String projectNo;

    @ApiModelProperty(value = "项目名称", required = true, example = "")
    @ExcelProperty("项目名称")
    private String projectName;

    @ApiModelProperty(value = "合同编号", required = true, example = "9307")
    @ExcelProperty("合同编号")
    private String businessContractId;

    @ApiModelProperty(value = "合同名称", required = true, example = "李四")
    @ExcelProperty("合同名称")
    private String contractName;

    @ApiModelProperty(value = "支付比例", required = true)
    @ExcelProperty("支付比例")
    private BigDecimal payPercent;

    @ApiModelProperty(value = "付款期数", required = true)
    @ExcelProperty("付款期数")
    private String paymentPhases;

    @ApiModelProperty(value = "开户行", required = true)
    @ExcelProperty("开户行")
    private String bank;

    @ApiModelProperty(value = "收款单位", required = true)
    @ExcelProperty("收款单位")
    private String receiveCompany;

    @ApiModelProperty(value = "账号", required = true, example = "30011")
    @ExcelProperty("账号")
    private String receiveAccount;

    @ApiModelProperty(value = "合同金额", required = true)
    @ExcelProperty("合同金额")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "预算编号", required = true)
    @ExcelProperty("预算编号")
    private String budgetNumber;

    @ApiModelProperty(value = "金额小写", required = true)
    @ExcelProperty("金额小写")
    private String moneyLowCase;

    @ApiModelProperty(value = "是否冲账(yes|no)", required = true)
    @ExcelProperty(value = "*是否冲账(yes|no)", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.YES_NO_BY_STRING)
    private String hedge;

    @ApiModelProperty(value = "支付方式（电汇/承兑/现金）", required = true)
    @ExcelProperty("支付方式（电汇/承兑/现金）")
    private String payMethod;

    @ApiModelProperty(value = "备注", required = true, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "是否预付 yes no", example = "yes")
    @ExcelProperty(value = "*是否预付", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.YES_NO_BY_STRING)
    private String paymentRroperty;

    @ApiModelProperty(value = "内部项目标识", required = true, example = "随便")
    @ExcelProperty("内部项目标识")
    private Integer projectBelongModule;

    @ApiModelProperty(value = "申请合同部门名称", required = true, example = "随便")
    @ExcelProperty("申请合同部门名称")
    private String purchaseApplyCreatorDeptName;

    @ApiModelProperty(value = "采购经理工号", required = true, example = "随便")
    @ExcelProperty("采购经理工号")
    private String purchasingManagerCode;

    @ExcelProperty("币种")
    @ApiModelProperty(value = "币种", example = "随便")
    private String typeOfCurrency;

    @ExcelProperty("收款方开户行联行号")
    @ApiModelProperty(value = "收款方开户行联行号", example = "随便")
    private String routingNumber;

    @ApiModelProperty(value = "外部单号", example = "随便")
    @ExcelProperty("外部单号")
    private String externalBusinessOrderNumber;

    @ApiModelProperty(value = "当前步骤名称", example = "随便")
    @ExcelProperty("当前步骤名称")
    private String currentStepName;

    @ApiModelProperty("账单附件")
    @ExcelIgnore
    private String billUrl;

    @ApiModelProperty("发票附件")
    @ExcelIgnore
    private String invoiceUrl;

    @ApiModelProperty("验收单附件")
    @ExcelIgnore
    private String checkUrl;

    @ApiModelProperty("合同附件")
    @ExcelIgnore
    private String contractUrl;

    @ApiModelProperty("其他附件")
    @ExcelIgnore
    private String otherUrl;
}
