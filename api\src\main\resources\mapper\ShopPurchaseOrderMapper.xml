<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.order.mapper.ShopPurchaseOrderMapper">
    <resultMap type="com.ly.yph.api.order.entity.ShopPurchaseOrder" id="ShopPurchaseOrderMap">
        <result property="purchaseId" column="purchase_id" jdbcType="VARCHAR"/>
        <result property="purchaseName" column="purchase_name" jdbcType="VARCHAR"/>
        <result property="purchaseNumber" column="purchase_number" jdbcType="VARCHAR"/>
        <result property="purchaseState" column="purchase_state" jdbcType="INTEGER"/>
        <result property="purchaseGoodsNumber" column="purchase_goods_number" jdbcType="INTEGER"/>
        <result property="purchaseGoodsPrice" column="purchase_goods_price" jdbcType="NUMERIC"/>
        <result property="purchaseFreightPrice" column="purchase_freight_price" jdbcType="NUMERIC"/>
        <result property="purchaseTotalPrice" column="purchase_total_price" jdbcType="NUMERIC"/>
        <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="applyDeptId" column="apply_dept_id" jdbcType="VARCHAR"/>
        <result property="applyDeptName" column="apply_dept_name" jdbcType="VARCHAR"/>
        <result property="applyUserId" column="apply_user_id" jdbcType="VARCHAR"/>
        <result property="applyUserName" column="apply_user_name" jdbcType="VARCHAR"/>
        <result property="applyEmpCode" column="apply_emp_code" jdbcType="VARCHAR"/>
        <result property="applyUserPhone" column="apply_user_phone" jdbcType="VARCHAR"/>
        <result property="applyBuyerEmail" column="apply_buyer_email" jdbcType="VARCHAR"/>
        <result property="budgetId" column="budget_id" jdbcType="INTEGER"/>
        <result property="budgetApplyCode" column="budget_apply_code" jdbcType="VARCHAR"/>
        <result property="budgetCode" column="budget_code" jdbcType="VARCHAR"/>
        <result property="invoiceId" column="invoice_id" jdbcType="VARCHAR"/>
        <result property="addressId" column="address_id" jdbcType="VARCHAR"/>
        <result property="isUrgent" column="is_urgent" jdbcType="INTEGER"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="purchaseComment" column="purchase_comment" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="otherRelationNumber" column="other_relation_number" jdbcType="VARCHAR"/>
        <result property="isEnable" column="is_enable" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ShopPurchaseOrderVo" type="com.ly.yph.api.order.vo.ShopPurchaseOrderVo">
        <result property="purchaseId" column="purchase_id" jdbcType="VARCHAR"/>
        <result property="purchaseName" column="purchase_name" jdbcType="VARCHAR"/>
        <result property="purchaseNumber" column="purchase_number" jdbcType="VARCHAR"/>
        <result property="purchaseState" column="purchase_state" jdbcType="INTEGER"/>
        <result property="purchaseGoodsPrice" column="purchase_goods_price" jdbcType="NUMERIC"/>
        <result property="purchaseFreightPrice" column="purchase_freight_price" jdbcType="NUMERIC"/>
        <result property="purchaseTotalPrice" column="purchase_total_price" jdbcType="NUMERIC"/>
        <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="applyDeptName" column="apply_dept_name" jdbcType="VARCHAR"/>
        <result property="applyUserName" column="apply_user_name" jdbcType="VARCHAR"/>
        <result property="applyEmpCode" column="apply_emp_code" jdbcType="VARCHAR"/>
        <result property="applyUserPhone" column="apply_user_phone" jdbcType="VARCHAR"/>
        <result property="budgetId" column="budget_id" jdbcType="INTEGER"/>
        <result property="budgetApplyCode" column="budget_apply_code" jdbcType="VARCHAR"/>
        <result property="budgetCode" column="budget_code" jdbcType="VARCHAR"/>
        <result property="budgetType" column="budget_type" jdbcType="VARCHAR"/>
        <result property="invoiceId" column="order_invoice_id" jdbcType="VARCHAR"/>
        <result property="invoiceSubject" column="invoice_subject" jdbcType="VARCHAR"/>
        <result property="isUrgent" column="is_urgent" jdbcType="INTEGER"/>
        <result property="purchaseComment" column="purchase_comment" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="isHistory" column="is_history" jdbcType="INTEGER"/>
        <result property="accepterId" column="accepter_id" jdbcType="INTEGER"/>
        <result property="accepterCode" column="accepter_code" jdbcType="VARCHAR"/>
        <result property="accepterName" column="accepter_name" jdbcType="VARCHAR"/>
        <result property="accepterOrgPath" column="accepter_org_path" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="attachmentUrl" column="attachment_url" jdbcType="VARCHAR"/>
        <result property="purchaseOrgName" column="purchase_org_name" jdbcType="VARCHAR"/>
        <association property="addressUser" column="address_user"
                     javaType="com.ly.yph.api.organization.entity.SystemUsers"
                     select="selectAddressUser"/>
        <collection property="orderAddressList" ofType="com.ly.yph.api.order.entity.ShopOrderAddress"
                    javaType="arraylist" column="purchase_id"
                    select="com.ly.yph.api.order.mapper.ShopOrderAddressMapper.queryListByPurchaseId"/>
        <collection property="orderDetailList" ofType="com.ly.yph.api.order.vo.PurchaseSubOrderDetailVo"
                    javaType="arraylist" column="purchase_number"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryListByPurchaseNumber"/>
    </resultMap>

    <resultMap id="myOrderFrontVo" type="com.ly.yph.api.order.vo.MyOrderFrontVo">
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <collection property="orderDetailList" ofType="com.ly.yph.api.order.vo.MyOrderDetailFrontVo"
                    javaType="arraylist" column="order_id"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryMyOrderDetailFrontById"/>
    </resultMap>
    <resultMap id="mallOrderQueryVo" type="com.ly.yph.api.order.vo.MallOrderQueryVo">
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <collection property="orderDetailList" ofType="com.ly.yph.api.order.vo.MyOrderDetailFrontVo"
                    javaType="arraylist" column="order_id"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryMyOrderDetailFrontById"/>
    </resultMap>

    <resultMap id="YouServiceOrderPageVo" type="com.ly.yph.api.order.vo.youServiceOrder.YouServiceOrderPageVo">
        <result property="purchaseNumber" column="purchase_number" jdbcType="VARCHAR"/>
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <result property="supplierOrderNumber" column="supplier_order_number" jdbcType="VARCHAR"/>
        <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
        <result property="orderPriceTax" column="order_price_tax" jdbcType="VARCHAR"/>
        <result property="orderPriceNaked" column="order_price_naked" jdbcType="VARCHAR"/>
        <result property="orderState" column="order_state" jdbcType="INTEGER"/>
        <result property="applyTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="addressName" column="address_name" jdbcType="VARCHAR"/>
        <result property="mobPhone" column="mob_phone" jdbcType="VARCHAR"/>
        <result property="addressInfo" column="address_info" jdbcType="VARCHAR"/>
        <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
        <result property="addressType" column="address_type" jdbcType="INTEGER"/>
        <result property="supplierPhone" column="after_sales_people" jdbcType="VARCHAR"/>
        <result property="supplierShortName" column="supplier_short_name" jdbcType="VARCHAR"/>
        <result property="applyUserName" column="apply_user_name" jdbcType="VARCHAR"/>
        <result property="applyEmpCode" column="apply_emp_code" jdbcType="VARCHAR"/>
        <collection property="youServiceOrderDetailVos"
                    ofType="com.ly.yph.api.order.vo.youServiceOrder.YouServiceOrderDetailVo"
                    javaType="arraylist" column="order_number"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryYouServiceOrderDetailByOrderNumber"/>
    </resultMap>

    <select id="selectAddressUser" resultType="com.ly.yph.api.organization.entity.SystemUsers">
        SELECT x.username,
               x.nickname,
               x.organization_code,
               x.organization_name,
               x.entity_organization_code,
               x.entity_organization_name
        FROM system_users x
        WHERE username = #{address_user}
          and X.tenant_id = 1
          and X.is_enable = 1
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="ShopPurchaseOrderMap">
        select purchase_id,
               purchase_name,
               purchase_number,
               purchase_state,
               purchase_goods_number,
               purchase_goods_price,
               purchase_freight_price,
               purchase_total_price,
               company_code,
               company_name,
               apply_dept_id,
               apply_dept_name,
               apply_user_id,
               apply_user_name,
               apply_emp_code,
               apply_user_phone,
               apply_buyer_email,
               budget_id,
               budget_apply_code,
               budget_code,
               invoice_id,
               address_id,
               is_urgent,
               audit_time,
               purchase_comment,
               remark,
               other_relation_number,
               is_enable,
               creator,
               create_time,
               modifier,
               update_time,
               can_confirm
        from shop_purchase_order
        where purchase_id = #{purchaseId}
          and is_enable = '1'
    </select>

    <select id="querySupplierPreOrder" resultType="com.ly.yph.api.product.ext.common.dto.request.PreOrderInfo">
        SELECT o.supplier_code supplierCode,
        o.order_number yphOrderNo,
        o.order_freight_price orderFreightPrice,
        o.supplier_order_price_tax supplierOrderPriceTax,
        o.supplier_order_price_naked supplierOrderPriceNaked,
        address.address_name `name`,
        address.mob_phone mobile,
        address.province province,
        address.city city,
        address.district county,
        case
            when po.company_code='HONDA' and po.order_label=9 then concat(address.address, '（东本积分兑换）')
            else address.address
        end as address,
        address.zip_code zipCode,
        i.invoice_subject invoiceTitle,
        '明细' invoiceContent,
        i.taxpayer_number invoiceTaxPayer,
        i.invoice_bank invoiceBank,
        i.bank_account invoiceBankAccount,
        i.subject_phone invoiceBankTel,
        i.subject_address invoiceBankAddress,
        address.address_name invoiceName,
        address.mob_phone invoicePhone,
        CONCAT(address.province, address.city, address.district, address.address) invoiceAddress,
        po.company_name companyName,
        po.apply_user_phone purchasePhone,
        o.remark,
        o.is_company_store,
        o.sale_client,o.order_model
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_purchase_order po ON po.purchase_number = o.purchase_number
                 LEFT JOIN shop_order_address address ON address.purchase_id = po.purchase_id
                 LEFT JOIN shop_order_invoice i ON i.purchase_id = po.purchase_id
        WHERE o.purchase_number = #{purchaseNumber}
          and o.is_enable = '1'
    </select>

    <select id="queryCompanyStoreSupplierPreOrder" resultType="com.ly.yph.api.product.ext.common.dto.request.PreOrderInfo">
        SELECT o.supplier_code                                                                           supplierCode,
               o.order_number                                                                            yphOrderNo,
               o.order_freight_price                                                                     orderFreightPrice,
               o.supplier_order_price_tax                                                                supplierOrderPriceTax,
               o.supplier_order_price_naked                                                              supplierOrderPriceNaked,
               IFNULL(address.qpc_address_name, sa.address_name) AS                                      `name`,
               IFNULL(address.qpc_mob_phone, sa.mob_phone)       AS                                      mobile,
               IFNULL(address.qpc_province, sa.province)         AS                                      province,
               IFNULL(address.qpc_city, sa.city)                 AS                                      city,
               IFNULL(address.qpc_district, sa.district)         AS                                      county,
               IFNULL(address.qpc_address, sa.address)           AS                                      address,
               sa.zip_code                                                                               zipCode,
               i.invoice_subject                                                                         invoiceTitle,
               '明细'                                                                                    invoiceContent,
               i.taxpayer_number                                                                         invoiceTaxPayer,
               i.invoice_bank                                                                            invoiceBank,
               i.bank_account                                                                            invoiceBankAccount,
               i.subject_phone                                                                           invoiceBankTel,
               i.subject_address                                                                         invoiceBankAddress,
               IFNULL(address.qpc_address_name, sa.address_name) AS                                      invoiceName,
               IFNULL(address.qpc_mob_phone, sa.mob_phone)       AS                                      invoicePhone,
               CONCAT(address.qpc_province, address.qpc_city, address.qpc_district, address.qpc_address) invoiceAddress,
               po.company_name                                                                           companyName,
               po.apply_user_phone                                                                       purchasePhone,
               o.remark
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_purchase_order po ON po.purchase_number = o.purchase_number
                 LEFT JOIN company_store_order_address address ON address.order_number = o.order_number
                 LEFT JOIN shop_order_address sa ON sa.purchase_id = po.purchase_id
                 LEFT JOIN shop_order_invoice i ON i.purchase_id = po.purchase_id
        WHERE o.purchase_number = #{purchaseNumber}
          and o.is_enable = '1'
    </select>


    <select id="queryPurchaseOrderVoByNumber" resultMap="ShopPurchaseOrderVo">
        SELECT o.purchase_id,
        o.purchase_name,
        o.purchase_number,
        o.purchase_state,
        o.purchase_goods_price,
        o.purchase_goods_price_naked,
        o.purchase_freight_price,
        o.purchase_total_price,
        o.activity_id,
        o.purchase_pay_integral,
        o.purchase_pay_money,
        o.company_code,
        o.company_name,
        o.apply_dept_id,
        o.apply_dept_name,
        o.apply_user_id,
        o.apply_user_name,
        o.apply_emp_code,
        o.apply_user_phone,
        o.budget_apply_code,
        o.budget_id,
        o.budget_code,
        o.invoice_type,
        di.label invoice_type_name,
        b.type_code budget_type,
        t.type_name budget_type_name,
        i.order_invoice_id,
        i.invoice_subject,
        o.is_urgent,
        o.purchase_comment,
        o.remark,
        o.other_relation_number,
        o.is_history,
        o.accepter_id,
        o.accepter_code,
        o.accepter_name,
        o.accepter_org_path,
        o.dfs_need_delivery,
        o.create_time,
        o.attachment_url,
        o.trade_union_flag,
        o.address_user,
        o.order_label,
        o.apply_section_code,
        o.apply_section_name,
        o.bear_department_code,
        o.bear_department_name,
        o.purchase_org_name,
        o.goods_zone_id,
        o.auto_invoice_flag,
        o.tenant_id,
        (select srm_apply_id from shop_purchase_sub_order where purchase_number = #{purchaseNumber} limit 1) AS
        srm_apply_id
        FROM shop_purchase_order o
        LEFT JOIN system_budget b ON b.id = o.budget_id
        LEFT JOIN sys_budget_type t ON t.type_code = b.type_code
        LEFT JOIN shop_order_invoice i ON i.purchase_id = o.purchase_id
        LEFT JOIN shop_order_address d ON d.purchase_id = o.purchase_id
        LEFT join system_dict_data di on di.dict_type = 'A010' AND di.value = o.invoice_type
        WHERE o.purchase_number = #{purchaseNumber}
        and o.is_enable = '1'
    </select>

    <select id="queryDeployByCompanyCode" resultType="java.lang.Long">
        select id
        from act_cu_deploy_info
        where company_code = #{companyCode}
          and proc_type = #{procType}
          and is_enable = 1
          and state = 1
    </select>

    <select id="queryPurchaseOrderPageVo" resultType="com.ly.yph.api.order.vo.ShopPurchaseOrderPageVo">
        select spo.purchase_id,
               spo.purchase_name,
               spo.purchase_number,
               spo.purchase_state,
               spo.approval_opinion,
               spo.purchase_goods_number,
               spo.purchase_total_price,
               spo.purchase_goods_price_naked,
               spo.purchase_freight_price,
               spo.purchase_pay_integral,
               spo.purchase_pay_money,
               spo.company_code,
               spo.company_name,
               spo.apply_emp_code,
               spo.apply_user_name,
               spo.apply_dept_name,
               spo.budget_code,
               spo.budget_apply_code,
               sb.type_code  as budget_type_code,
               sbt.type_name as budget_type_name,
               spo.is_urgent,
               spo.purchase_comment,
               spo.create_time,
               spo.order_label,
               spo.other_relation_number,
               spo.remark,
               soi.invoice_subject,
               spo.goods_zone_id,
               d.label       as invoiceTypeName,
               spo.budget_type
        from shop_purchase_order spo
                 left join system_budget sb on sb.id = spo.budget_id
                 left join sys_budget_type sbt on sb.type_code = sbt.type_code
                 left join system_dict_data d on d.dict_type = 'A010' AND d.value = spo.invoice_type
                 left join shop_order_invoice soi on soi.purchase_id=spo.purchase_id
        where spo.can_confirm != 2
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.applyEmpCode != null and queryDto.applyEmpCode != ''">
            and spo.apply_emp_code = #{queryDto.applyEmpCode}
        </if>
        <if test="queryDto.companyCode != null and queryDto.companyCode != ''">
            and spo.company_code = #{queryDto.companyCode}
        </if>
        <if test="queryDto.purchaseState != null">
            and spo.purchase_state = #{queryDto.purchaseState}
        </if>
        <if test="queryDto.budgetCode != null and queryDto.budgetCode != ''">
            and spo.budget_code = #{queryDto.budgetCode}
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spo.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spo.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.keyWords != null and queryDto.keyWords != ''">
            and (
                spo.purchase_number like concat('%', #{queryDto.keyWords}, '%') or
                spo.apply_user_name like concat('%', #{queryDto.keyWords}, '%')
                )
        </if>
        and spo.is_enable = '1'
        <if test="queryDto.orgIds != null and queryDto.orgIds.size() != 0">
            <foreach item="orgId" open="and spo.organization_id in (" collection="queryDto.orgIds" close=")"
                     separator=",">
                ${orgId}
            </foreach>
        </if>
        <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() != 0">
            <foreach item="companyCode" open="and spo.company_code in (" collection="queryDto.companyCodes" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        order by spo.create_time desc
    </select>

    <select id="queryMyOrderPage" resultMap="myOrderFrontVo">
        select distinct spo.purchase_id,
        spo.purchase_number,
        spo.purchase_state,
        spso.order_price_tax,
        spso.order_price_naked,
        soa.address_name,
        soa.address_type,
        soa.mob_phone,
        concat(soa.province, soa.city, soa.district, soa.address) as address,
        spo.create_time as purchase_create_time,
        spo.audit_time,
        spso.order_id,
        spso.order_number,
        spso.order_state,
        spso.fail_reason,
        spso.supplier_code,
        spso.supplier_name,
        spso.supplier_data_source,
        spso.create_time as order_create_time,
        spso.supplier_confirm_state,
        spo.order_label,
        spo.other_relation_number,
        spo.goods_zone_id,
        spso.is_platform_reconciliation,
        spso.supplier_order_number,
        ss.order_people,spo.budget_type,spso.order_sap_number,soa.factory_code,spso.contract_number
        from shop_purchase_order spo
        left join shop_purchase_sub_order spso on spo.purchase_number = spso.purchase_number
        left join shop_order_address soa on soa.purchase_id = spo.purchase_id
        left join shop_purchase_sub_order_detail spsod on spso.order_id = spsod.order_id
        left join shop_supplier ss on ss.supplier_code = spso.supplier_code and ss.tenant_id = spo.tenant_id
        where spo.apply_emp_code = #{queryDto.applyEmpCode} and spo.can_confirm != 2
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.purchaseState != null">
            and spo.purchase_state = #{queryDto.purchaseState}
        </if>
        <if test="queryDto.startCreateTime != null and queryDto.startCreateTime != ''">
            and spo.create_time &gt;= #{queryDto.startCreateTime}
        </if>
        <if test="queryDto.endCreateTime != null and queryDto.endCreateTime != ''">
            and spo.create_time &lt;= #{queryDto.endCreateTime}
        </if>
        <if test="queryDto.orderState != null">
            and spso.order_state = #{queryDto.orderState}
        </if>
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() != 0">
            and spso.supplier_code in
            <foreach collection="queryDto.supplierCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryDto.addressName != null and queryDto.addressName != ''">
            and soa.address_name like concat('%', #{queryDto.addressName}, '%')
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and spsod.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.orderLabel != null">
            and spo.order_label = #{queryDto.orderLabel}
        </if>
        <if test="queryDto.orderSapNumber != null and queryDto.orderSapNumber != ''">
            and spso.order_sap_number = #{queryDto.orderSapNumber}
        </if>
        <if test="queryDto.contractNumber != null and queryDto.contractNumber != ''">
            and spso.contract_number =#{queryDto.contractNumber}
        </if>
        and spo.is_enable=1 and spso.is_enable=1
        order by spso.create_time desc
    </select>

    <select id="exportMyOrder" resultType="com.ly.yph.api.order.vo.MyOrderFrontExcelVo">
        select spo.purchase_number,
        spso.order_number,
        spo.apply_user_name,
        spo.company_name,
        spo.create_time as purchase_create_time,
        spso.create_time as order_create_time,
        spso.supplier_name,
        spsod.goods_desc,
        spsod.goods_code,
        spsod.goods_unit_price_naked,
        spsod.goods_unit_price_tax,
        (spsod.confirm_num + spsod.confirm_num_decimal) confirm_num,
        soa.address_name,
        ca.qpc_address_name,
        concat(soa.province, soa.city, soa.district, soa.address) as address,
        if( spso.is_company_store = 1 , concat(ca.qpc_province, ca.qpc_city, ca.qpc_district, ca.qpc_address) ,'' ) as
        qpcAddress,
        (case spsod.order_detail_state
        when -1 then '订单失败'
        when 0 then '订单取消'
        when 10 then '已提交'
        when 20 then '待发货'
        when 30 then '待收货'
        when 40 then '收货完成'
        else '收货完成' end) as order_state,
        spsod.goods_sku,
        spsod.currency_name,spsod.row_serial_number,spso.order_sap_number,spso.contract_number
        from shop_purchase_order spo
        left join shop_purchase_sub_order spso on spo.purchase_number = spso.purchase_number
        left join shop_order_address soa on soa.purchase_id = spo.purchase_id
        LEFT JOIN company_store_order_address ca ON ca.order_number = spso.order_number
        left join shop_purchase_sub_order_detail spsod on spso.order_id = spsod.order_id
        where spo.apply_emp_code = #{queryDto.applyEmpCode} and spo.can_confirm != 2
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number = #{queryDto.purchaseNumber}
        </if>
        <if test="queryDto.purchaseState != null">
            and spo.purchase_state = #{queryDto.purchaseState}
        </if>
        <if test="queryDto.startCreateTime != null and queryDto.startCreateTime != ''">
            and spo.create_time &gt;= #{queryDto.startCreateTime}
        </if>
        <if test="queryDto.endCreateTime != null and queryDto.endCreateTime != ''">
            and spo.create_time &lt;= #{queryDto.endCreateTime}
        </if>
        <if test="queryDto.orderState != null">
            and spso.order_state = #{queryDto.orderState}
        </if>
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number = #{queryDto.orderNumber}
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() != 0">
            and spso.supplier_code in
            <foreach collection="queryDto.supplierCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryDto.addressName != null and queryDto.addressName != ''">
            and soa.address_name like concat('%', #{queryDto.addressName}, '%')
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and spsod.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.orderSapNumber != null and queryDto.orderSapNumber != ''">
            and spso.order_sap_number = #{queryDto.orderSapNumber}
        </if>
        <if test="queryDto.contractNumber != null and queryDto.contractNumber != ''">
            and spso.contract_number =#{queryDto.contractNumber}
        </if>
        and spo.is_enable=1 and spso.is_enable=1
    </select>

    <select id="queryPurchaseOrder" resultType="com.ly.yph.api.order.vo.ShopPurchaseOrdersVo">
        select d.goods_desc,
        c.apply_num,
        c.apply_num_decimal,
        c.goods_unit_price_tax,
        c.goods_unit_price_naked,
        d.goods_code,
        d.goods_sku,
        b.order_sap_number,
        c.order_detail_state,
        c.goods_id,
        gg.address_name as applyUserName,
        gg.mob_phone as applyUserPhone,
        p.goods_image,
        a.purchase_number,
        a.create_time,
        b.supplier_code,
        b.supplier_name,
        b.supplier_data_source,
        b.order_number,
        b.order_id,
        p.goods_moq,
        a.goods_zone_id
        from shop_purchase_order a
        left join shop_purchase_sub_order b on a.purchase_number = b.purchase_number
        left join shop_purchase_sub_order_detail c on b.order_id = c.order_id
        left join shop_order_address gg on a.purchase_id = gg.purchase_id
        left join shop_goods d on c.goods_id = d.goods_id
        left join shop_goods_detail p on p.goods_code = d.goods_code
        WHERE a.can_confirm != 2
        <if test="purchaseOrderQuery.purchaseNumber != null and purchaseOrderQuery.purchaseNumber != ''">
            AND a.purchase_number = #{purchaseOrderQuery.purchaseNumber}
        </if>
        <if test="purchaseOrderQuery.createTimeStart != null and purchaseOrderQuery.createTimeStart != ''
        and purchaseOrderQuery.createTimeEnd != null and purchaseOrderQuery.createTimeEnd != ''">
            AND a.create_time &gt;= #{purchaseOrderQuery.createTimeStart} AND
            a.create_time &lt;= #{purchaseOrderQuery.createTimeEnd}
        </if>
        <if test="purchaseOrderQuery.keyWords != null and purchaseOrderQuery.keyWords != ''">
            AND concat(d.goods_desc, d.goods_code) like concat('%', #{query.keyWords}, '%')
        </if>
        and a.is_enable=1 and b.is_enable=1
        ORDER BY a.create_time DESC
    </select>
    <select id="queryPurchaseOrderByNumber" resultType="com.ly.yph.api.order.vo.ShopPurchaseOrderVo">
        SELECT o.purchase_id,
               o.purchase_name,
               o.purchase_number,
               o.purchase_state,
               o.purchase_goods_price,
               o.purchase_freight_price,
               o.purchase_total_price,
               o.company_code,
               o.company_name,
               o.apply_dept_name,
               o.apply_user_name,
               o.apply_emp_code,
               o.apply_user_phone,
               o.budget_apply_code,
               o.budget_id,
               o.budget_code,
               o.address_id,
               t.type_name budget_type,
               i.order_invoice_id,
               i.invoice_subject,
               o.is_urgent,
               o.purchase_comment,
               o.remark,
               o.create_time
        FROM shop_purchase_order o
                 LEFT JOIN system_budget b ON b.id = o.budget_id
                 LEFT JOIN sys_budget_type t ON t.type_code = b.type_code
                 LEFT JOIN shop_order_invoice i ON i.purchase_id = o.purchase_id
        WHERE 1 = 1
          and o.is_enable = '1'
          and o.can_confirm != 2
        <if test="purchaseNumber != null and purchaseNumber != ''">
            and o.purchase_number = #{purchaseNumber}
        </if>
    </select>
    <select id="queryPurchaseOrderPageVos" resultType="com.ly.yph.api.order.vo.PurchaseSubOrderDetailVo">
        select o.purchase_id,
               j.order_detail_state,
               j.goods_desc,
               j.goods_image,
               j.goods_unit_price_tax,
               j.apply_num,
               o.create_time,
               j.confirm_num,
               j.goods_sku,
               c.supplier_name,
               j.goods_code,
               c.order_id,
               c.order_number,
               j.order_detail_id,
               j.detail_return_state,
               c.purchase_number,
               j.create_time,
               t.goods_moq
        FROM shop_purchase_sub_order_detail j
                 left join shop_purchase_sub_order c on c.order_number = j.order_number
                 left join shop_purchase_order o on o.purchase_number = c.purchase_number
                 left join shop_goods_detail t on j.goods_code = t.goods_code
        where 1 = 1
          and o.is_enable = '1'
          and o.can_confirm != 2
        <if test="Query.applyEmpCode != null and Query.applyEmpCode != ''">
            AND o.apply_emp_code = #{Query.applyEmpCode}
        </if>
        <if test="Query.createTimeStart != null and Query.createTimeStart != ''
        and Query.createTimeEnd != null and Query.createTimeEnd != ''">
            AND o.create_time &gt;= #{Query.createTimeStart}
            AND o.create_time &lt;=
                #{Query.createTimeEnd}
        </if>
        <if test="Query.purchaseNumber != null and Query.purchaseNumber != ''">
            AND o.purchase_number = #{Query.purchaseNumber}
        </if>
        <if test="Query.activeName != null">
            AND j.order_detail_state = #{Query.activeName}
        </if>
        <if test="Query.keyWords != null and Query.keyWords != ''">
            AND concat(j.goods_desc, j.goods_code) like concat('%', #{Query.keyWords}, '%')
        </if>
        ORDER BY o.create_time DESC
    </select>
    <select id="queryOrderPage" resultType="com.ly.yph.api.order.vo.ShopOrderPageVo">
        select spso.order_id,
               spso.order_number,
               spso.supplier_order_number,
               spo.purchase_number,
               CASE
                   WHEN e.`no` IS NULL THEN
                       (SELECT pe.`no`
                        from pay_order pay
                                 LEFT JOIN pay_order_extension pe ON pe.order_id = pay.id
                        where pay.merchant_order_id = spso.purchase_number
                          and pe.status = 10)
                   ELSE
                       e.`no`
                   END    payOrderNumber,
               spo.other_relation_number,
               spo.company_name,
               spo.apply_emp_code,
               spo.apply_user_name,
               spo.create_time,
               spso.supplier_name,
               spso.order_freight_price,
               spso.supplier_order_price_tax,
               spso.supplier_order_price_naked,
               spso.order_price_tax,
               spso.order_price_naked,
               spso.order_pay_integral,
               spso.order_pay_money,
               a.activity_name,
               od.address_name,
               spso.order_state,
               spso.order_subsidy_freight,
               i.invoice_subject,
               d.label as invoiceTypeName,
               spso.supplier_code,
               spso.is_platform_reconciliation,
               spso.sale_client,spso.supplier_data_source,spso.order_sap_number
        from shop_purchase_sub_order spso
                 left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
                 left join shop_order_invoice i on i.purchase_id = spo.purchase_id
                 left join system_dict_data d on d.dict_type = 'A010' AND d.value = spo.invoice_type
                 left JOIN system_activity a ON a.id = spo.activity_id
                 left join shop_order_address od ON od.purchase_id = spo.purchase_id
                 LEFT JOIN pay_order p ON p.merchant_order_id = spso.order_number
                 LEFT JOIN pay_order_extension e ON e.order_id = p.id and e.status = 10
        where 1 = 1
          and spso.is_enable = '1'
          and spo.can_confirm != 2
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() > 0">
            <foreach item="supplierCode" open="and spso.supplier_code in (" collection="queryDto.supplierCodes"
                     close=")"
                     separator=",">
                #{supplierCode}
            </foreach>
        </if>
        <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() > 0">
            <foreach item="companyCode" open="and spo.company_code in (" collection="queryDto.companyCodes" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        <if test="queryDto.orderStates != null and queryDto.orderStates.size() > 0">
            <foreach item="orderState" open="and spso.order_state in (" collection="queryDto.orderStates" close=")"
                     separator=",">
                #{orderState}
            </foreach>
        </if>
        <if test="queryDto.orderPriceTaxStart != null">
            and spso.order_price_tax &gt;= #{queryDto.orderPriceTaxStart}
        </if>
        <if test="queryDto.orderPriceTaxEnd != null">
            and spso.order_price_tax &lt;= #{queryDto.orderPriceTaxEnd}
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spso.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spso.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.activityName != null and queryDto.activityName != ''">
            and a.activity_name like concat('%', #{queryDto.activityName}, '%')
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number like concat('%', #{queryDto.supplierOrderNumber}, '%')
        </if>
        <if test="queryDto.isPlatformReconciliation != null ">
            and spso.is_platform_reconciliation  = #{queryDto.isPlatformReconciliation}
        </if>
        <if test="queryDto.saleClient != null">
            and spso.sale_client = #{queryDto.saleClient}
        </if>
        order by spso.create_time desc
    </select>

    <select id="queryYouServiceSubOrderPage" resultMap="YouServiceOrderPageVo">
        select
        distinct
        spso.purchase_number,
        spso.order_number,
        spso.supplier_order_number,
        spso.supplier_code,
        ss.supplier_short_name,
        spso.order_price_tax,
        spso.order_price_naked,
        spso.order_state,
        spo.create_time,
        spo.company_code,
        od.address_name,
        od.mob_phone,
        CONCAT(od.province, od.city, od.district, od.address) address_info,
        od.address_type,
        ss.after_sales_people,
        spo.apply_user_name,
        spo.apply_emp_code
        from shop_purchase_sub_order spso
        left join shop_purchase_sub_order_detail spsod on spsod.order_id =spso.order_id
        left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
        left join shop_order_address od on od.purchase_id = spo.purchase_id
        left join shop_supplier ss on ss.supplier_code = spso.supplier_code and spso.tenant_id = ss.tenant_id
        where spso.is_enable =1 and spo.can_confirm != 2
        <if test="query.orderState != null">
            and spso.order_state =#{query.orderState}
        </if>
        <if test="query.createTimeStart != null and query.createTimeStart != ''">
            and spo.create_time &gt;=#{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
            and spo.create_time &lt;=#{query.createTimeEnd}
        </if>
        <if test="query.applyUserId != null">
            and spo.apply_user_id =#{query.applyUserId}
        </if>
        <if test="query.supplierCodes != null and query.supplierCodes.size() > 0">
            <foreach item="supplierCode" open="and spso.supplier_code in (" collection="query.supplierCodes" close=")"
                     separator=",">
                #{supplierCode}
            </foreach>
        </if>
        <if test="query.keyWords != null and query.keyWords != ''">
            and (
            spso.order_number like concat('%', #{query.keyWords}, '%') or
            spsod.goods_desc like concat('%', #{query.keyWords}, '%')
            )
        </if>
        <if test="query.orderStates != null and query.orderStates.size() > 0">
            <foreach item="orderState" collection="query.orderStates" open="and spso.order_state in (" close=")"
                     separator=",">
                #{orderState}
            </foreach>
        </if>
        <if test="query.companyCodes != null and query.companyCodes.size() > 0">
            <foreach item="companyCode" collection="query.companyCodes" open="and spo.company_code in (" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        order by spso.create_time desc
    </select>

    <select id="queryOrderPageForDC" resultType="com.ly.yph.api.order.vo.ShopOrderPageVo">
        select spso.order_id,
               spso.order_number,
               spso.supplier_order_number,
               spo.purchase_number,
               CASE
                   WHEN e.`no` IS NULL THEN
                       (SELECT pe.`no`
                        from pay_order pay
                                 LEFT JOIN pay_order_extension pe ON pe.order_id = pay.id
                        where pay.merchant_order_id = spso.purchase_number
                          and pe.status = 10)
                   ELSE
                       e.`no`
                   END                                           payOrderNumber,
               (select sum(supplier_unit_original_price_tax * confirm_num)
                from shop_purchase_sub_order_detail spsod
                where spsod.order_number = spso.order_number) as supplier_original_price_tax,
               spo.other_relation_number,
               spo.company_name,
               spo.apply_emp_code,
               spo.apply_user_name,
               spo.create_time,
               spso.supplier_name,
               spso.order_freight_price,
               spso.order_price_tax,
               spso.order_price_naked,
               spso.order_pay_integral,
               spso.order_pay_money,
               a.activity_name,
               od.address_name,
               spso.order_state,
               spso.order_subsidy_freight,
               i.invoice_subject,
               spso.is_platform_reconciliation,
               spso.sale_client,
               d.label                                        as invoiceTypeName
        from shop_purchase_sub_order spso
                 left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
                 left join shop_order_invoice i on i.purchase_id = spo.purchase_id
                 left join system_dict_data d on d.dict_type = 'A010' AND d.value = spo.invoice_type
                 left JOIN system_activity a ON a.id = spo.activity_id
                 left join shop_order_address od ON od.purchase_id = spo.purchase_id
                 LEFT JOIN pay_order p ON p.merchant_order_id = spso.order_number
                 LEFT JOIN pay_order_extension e ON e.order_id = p.id and e.status = 10
        where 1 = 1
          and spso.is_enable = '1'
          and spo.can_confirm != 2
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() > 0">
            <foreach item="supplierCode" open="and spso.supplier_code in (" collection="queryDto.supplierCodes"
                     close=")"
                     separator=",">
                #{supplierCode}
            </foreach>
        </if>
        <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() > 0">
            <foreach item="companyCode" open="and spo.company_code in (" collection="queryDto.companyCodes" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        <if test="queryDto.orderStates != null and queryDto.orderStates.size() > 0">
            <foreach item="orderState" open="and spso.order_state in (" collection="queryDto.orderStates" close=")"
                     separator=",">
                #{orderState}
            </foreach>
        </if>
        <if test="queryDto.orderPriceTaxStart != null">
            and spso.order_price_tax &gt;= #{queryDto.orderPriceTaxStart}
        </if>
        <if test="queryDto.orderPriceTaxEnd != null">
            and spso.order_price_tax &lt;= #{queryDto.orderPriceTaxEnd}
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spso.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spso.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.activityName != null and queryDto.activityName != ''">
            and a.activity_name like concat('%', #{queryDto.activityName}, '%')
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number like concat('%', #{queryDto.supplierOrderNumber}, '%')
        </if>
        <if test="queryDto.isPlatformReconciliation != null ">
            and spso.is_platform_reconciliation = #{queryDto.isPlatformReconciliation}
        </if>
        <if test="queryDto.saleClient != null">
            and spso.sale_client = #{queryDto.saleClient}
        </if>
        order by spso.create_time desc
    </select>

    <insert id="queryOrderDetailPage_report_clear" statementType="STATEMENT">
        DROP table if exists temp_report_order_detail;
        CREATE TABLE if not exists temp_report_order_detail LIKE report_order_detail;
        truncate table temp_report_order_detail;
    </insert>

    <select id="queryOrderDetailPage_report_get_detail_id_1" resultType="string">
        select distinct od.order_detail_id
        from shop_purchase_sub_order_detail od
        where od.create_time &lt; #{createTime}
        and od.update_time>#{createTime} order by  od.order_detail_id
    </select>

    <select id="queryOrderDetailPage_report_get_detail_id_2" resultType="string">
        select distinct  od.order_detail_id
        from shop_delivery d
        left join shop_purchase_sub_order_detail od on od.order_id=d.order_id and od.create_time  &lt; #{createTime}
        where   d.update_time> #{createTime} and od.order_detail_id is not null
        order by  d.id
    </select>

    <insert id="queryOrderDetailPage_report_to_temp">
        INSERT INTO temp_report_order_detail
        select spsod.order_detail_id,
               spso.order_number,
               spo.purchase_number,
               spo.company_name,
               spo.apply_emp_code,
               spo.apply_user_name,
               spsod.goods_sku,
               spsod.goods_desc,
               spsod.goods_code,
               spso.supplier_name,
               spsod.supplier_unit_original_price_tax,
               spsod.supplier_unit_price_tax,
               spsod.supplier_unit_price_naked,
               spsod.goods_unit_price_tax,
               spsod.goods_unit_price_naked,
               IF(spsod.confirm_num = 0, spsod.confirm_num_decimal, spsod.confirm_num) confirm_num,
               spsod.supplier_total_price_tax,
               spsod.supplier_total_price_naked,
               spsod.goods_total_price_tax,
               spsod.goods_total_price_naked,
               spsod.goods_pay_integral,
               spsod.goods_pay_money,
               IF(spo.tenant_id = #{tenantId}, spso.create_time, spo.audit_time) as    purchaseTime,
               spo.create_time                                                   as    createTime,
               spo.other_relation_number,
               spsod.order_detail_state,
               a.activity_name,
               soi.invoice_subject,
               spso.supplier_order_number,
               g.third_class_name                                                as    thirdClassName,
               g.first_class_name                                                as    firstClassName,
               g.second_class_name                                               as    secondClassName,
               g.materials_code,
               g.sale_unit,
               spsod.tax_rate,
               g.tax_code,
               g.brand_name,
               g.delivery_time,
               de.goods_spec,
               spso.order_freight_price,
               spo.company_code,
               spo.apply_dept_name,
               soa.address_name,
               concat(soa.province, soa.city, soa.district, soa.address) as address,
               ca.qpc_address_name as qpcAddressName,
               if(spso.is_company_store = 1, concat(ca.qpc_province, ca.qpc_city, ca.qpc_district, ca.qpc_address), '') as qpcAddress,
               spso.organization_id,
               g.tenant_id,
               spso.create_time,
               g.supplier_code,
               g.goods_name,
               acdi.type_name                                                    as    budget_type_name,
               sb.budget_number,
               sd.sup_receiving_time,
               sd.cus_receiving_time,
               spo.remark,
               spso.is_pre_pay,
               spso.is_platform_reconciliation
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
                 left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
                 left join shop_order_invoice soi on soi.purchase_id = spo.purchase_id
                 left JOIN system_activity a ON a.id = spo.activity_id
                 left join shop_goods g on g.goods_id = spsod.goods_id
                 left join shop_goods_detail de on de.goods_code = g.goods_code and de.is_enable = g.is_enable
                 left join shop_order_address soa on soa.purchase_id = spo.purchase_id
                 LEFT JOIN company_store_order_address ca ON ca.order_number = spso.order_number
                 left join system_budget sb on sb.id = spo.budget_id
                 left join sys_budget_type acdi on sb.type_code = acdi.type_code
                 LEFT JOIN (SELECT c.order_detail_id,
                                   GROUP_CONCAT(b.sup_receiving_time) AS sup_receiving_time,
                                   GROUP_CONCAT(b.cus_receiving_time) AS cus_receiving_time
                            FROM shop_delivery_detail a
                                     LEFT JOIN shop_delivery b ON a.delivery_id = b.id
                                     LEFT JOIN shop_purchase_sub_order_detail c ON c.order_number = b.order_number
                                AND c.goods_sku = a.goods_sku
                            WHERE b.cus_receiving_state = 2
                            <if test="createTime!=null and createTime!=''">
                                and c.create_time>#{createTime}
                            </if>
                            <if test="orderDetailIds!=null and orderDetailIds.size()!=0">
                                and c.order_detail_id in
                                <foreach collection="orderDetailIds" open="(" close=")" separator="," item="item" >
                                    #{item}
                                </foreach>
                            </if>
                            GROUP BY c.order_detail_id
        ) as sd on sd.order_detail_id = spsod.order_detail_id
        where spsod.is_enable = '1'
        and spso.is_enable = '1'
        and spo.is_enable = '1'
        and spo.can_confirm!=2
        <if test="createTime!=null and createTime!=''">
            and spo.create_time>#{createTime}
        </if>
        <if test="orderDetailIds!=null and orderDetailIds.size()!=0">
            and spsod.order_detail_id in
            <foreach collection="orderDetailIds" open="(" close=")" separator="," item="item" >
                #{item}
            </foreach>
        </if>
        ;
    </insert>
    <delete id="queryOrderDetailPage_report_delete" statementType="STATEMENT">
        DELETE r
        FROM report_order_detail r
        INNER JOIN temp_report_order_detail t
        ON r.order_detail_id = t.order_detail_id ;
    </delete>
    <insert id="queryOrderDetailPage_report_insert" statementType="STATEMENT">
        INSERT INTO report_order_detail
        SELECT *
        FROM temp_report_order_detail;
    </insert>

    <select id="queryOrderDetailPage" resultType="com.ly.yph.api.order.vo.ShopOrderDetailPageVo">
        select order_detail_id,
                order_number,
                purchase_number,
                company_name,
                apply_emp_code,
                apply_user_name,
                goods_sku,
                goods_desc,
                goods_code,
                supplier_name,
                supplier_unit_price_tax,
                supplier_unit_price_naked,
                supplier_unit_original_price_tax,
                goods_unit_price_tax,
                goods_unit_price_naked,
                confirm_num,
                supplier_total_price_tax,
                supplier_total_price_naked,
                goods_total_price_tax,
                goods_total_price_naked,
                goods_pay_integral,
                goods_pay_money,
                purchaseTime,
                createTime,
                other_relation_number ,
                order_detail_state,
                activity_name,
                invoice_subject,
                supplier_order_number,
                thirdClassName,
                firstClassName,
                secondClassName,
                materials_code,
                sale_unit,
                tax_rate,
                tax_code,
                brand_name,
                delivery_time,
                goods_spec,
                order_freight_price,
                company_code,
                apply_dept_name,
                address_name,
                address,
                qpc_address,
                qpc_address_name,
                budget_number,
                budget_type_name,
                sup_receiving_time ,
                cus_receiving_time,
                supplier_code,
                cus_receiving_time,
                remark,
                is_platform_reconciliation,
                is_pre_pay
        from report_order_detail
        <where>
            <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
                and order_number = #{queryDto.orderNumber}
            </if>
            <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
                and apply_user_name = #{queryDto.applyUserName}
            </if>
            <if test="queryDto.applyEmpCode != null and queryDto.applyEmpCode != ''">
                and apply_emp_code = #{queryDto.applyEmpCode}
            </if>
            <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() > 0">
                <foreach item="supplierCode" open="and supplier_code in (" collection="queryDto.supplierCodes" close=")"
                         separator=",">
                    #{supplierCode}
                </foreach>
            </if>
            <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() > 0">
                <foreach item="companyCode" open="and company_code in (" collection="queryDto.companyCodes" close=")"
                         separator=",">
                    #{companyCode}
                </foreach>
            </if>
            <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
                and create_time &gt;= #{queryDto.createTimeStart}
            </if>
            <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
                and create_time &lt;= #{queryDto.createTimeEnd}
            </if>
            <if test="queryDto.goodsSku != null and queryDto.goodsSku != ''">
                and goods_sku = #{queryDto.goodsSku}
            </if>
            <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
                and goods_code = #{queryDto.goodsCode}
            </if>
            <if test="queryDto.goodsName != null and queryDto.goodsName != ''">
                and goods_name like concat(#{queryDto.goodsName}, '%')
            </if>
            <if test="queryDto.activityName != null and queryDto.activityName != ''">
                and activity_name like concat(#{queryDto.activityName}, '%')
            </if>
            <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
                and supplier_order_number like concat(#{queryDto.supplierOrderNumber}, '%')
            </if>
            <if test="queryDto.isPlatformReconciliation != null ">
                and is_platform_reconciliation = #{queryDto.isPlatformReconciliation}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="queryOrderDetailPrice" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
        select spsod.goods_code,
               spsod.goods_total_price_tax,
               spsod.goods_total_price_naked,
               spsod.supplier_total_price_tax,
               spsod.supplier_total_price_naked
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
                 left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
                 left JOIN system_activity a ON a.id = spo.activity_id
        where 1 = 1
          and spsod.is_enable = '1'
          and spo.can_confirm != 2
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() != 0">
            <foreach item="supplierCode" open="and spso.supplier_code in (" collection="queryDto.supplierCodes"
                     close=")"
                     separator=",">
                #{supplierCode}
            </foreach>
        </if>
        <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() != 0">
            <foreach item="companyCode" open="and spo.company_code in (" collection="queryDto.companyCodes" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spso.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spso.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.goodsSku != null and queryDto.goodsSku != ''">
            and spsod.goods_sku = #{queryDto.goodsSku}
        </if>
        <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
            and spsod.goods_code = #{queryDto.goodsCode}
        </if>
        <if test="queryDto.goodsName != null and queryDto.goodsName != ''">
            and spsod.goods_name like concat('%', #{queryDto.goodsName}, '%')
        </if>
        <if test="queryDto.activityName != null and queryDto.activityName != ''">
            and a.activity_name like concat('%', #{queryDto.activityName}, '%')
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number like concat('%', #{queryDto.supplierOrderNumber}, '%')
        </if>
        <if test="queryDto.isPlatformReconciliation != null ">
            and spso.is_platform_reconciliation = #{queryDto.isPlatformReconciliation}
        </if>
    </select>

    <select id="queryOrderPrice" resultType="com.ly.yph.api.order.vo.ShopTaxVo">
        select spso.order_price_tax,
               spso.order_price_naked,
               spso.supplier_order_price_tax,
               spso.supplier_order_price_naked,
               spso.order_freight_price
        from shop_purchase_sub_order spso
                 left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
                 left JOIN system_activity a ON a.id = spo.activity_id
        where 1 = 1
          and spso.is_enable = '1'
          and spo.can_confirm != 2
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() != 0">
            <foreach item="supplierCode" open="and spso.supplier_code in (" collection="queryDto.supplierCodes"
                     close=")"
                     separator=",">
                #{supplierCode}
            </foreach>
        </if>
        <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() != 0">
            <foreach item="companyCode" open="and spo.company_code in (" collection="queryDto.companyCodes" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        <if test="queryDto.orderStates != null and queryDto.orderStates.size() != 0">
            <foreach item="orderState" open="and spso.order_state in (" collection="queryDto.orderStates" close=")"
                     separator=",">
                #{orderState}
            </foreach>
        </if>
        <if test="queryDto.orderPriceTaxStart != null">
            and spso.order_price_tax &gt;= #{queryDto.orderPriceTaxStart}
        </if>
        <if test="queryDto.orderPriceTaxEnd != null">
            and spso.order_price_tax &lt;= #{queryDto.orderPriceTaxEnd}
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spso.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spso.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.activityName != null and queryDto.activityName != ''">
            and a.activity_name like concat('%', #{queryDto.activityName}, '%')
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number like concat('%', #{queryDto.supplierOrderNumber}, '%')
        </if>
        <if test="queryDto.isPlatformReconciliation != null ">
            and spso.is_platform_reconciliation  = #{queryDto.isPlatformReconciliation}
        </if>
    </select>
    <select id="getFailReason" resultType="com.ly.yph.api.order.vo.ShopTaxVo">
        select fail_reason, supplier_name
        from shop_purchase_sub_order
        where order_number = #{orderNumber}
          and is_enable = '1'
    </select>
    <select id="queryBySkus" resultType="com.ly.yph.api.order.vo.PurchaseSubOrderDetailVo">
        SELECT j.goods_desc,
               j.goods_code,
               j.goods_unit_price_tax,
               j.goods_unit_price_naked,
               j.apply_num,
               j.confirm_num,
               j.confirm_num_decimal,
               j.goods_total_price_tax,
               j.goods_total_price_naked,
               j.supplier_unit_price_tax,
               j.order_detail_state,
               j.order_number,
               j.goods_image,
               j.sale_unit,
               j.needer_name,
               c.supplier_order_number,
               c.order_price_tax,
               c.remark      as sremark,
               c.create_time as times,
               c.supplier_code,
               c.supplier_name,
               c.purchase_number,
               sscd.user_name,
               su.nickname,
               c.order_sap_number,
               j.goods_id,
               j.goods_sku
        FROM shop_purchase_sub_order c
                 left join shop_purchase_sub_order_detail j on c.order_number = j.order_number
                 left join shop_srm_contract_detail sscd
                           on c.contract_number = sscd.contract_number and j.goods_code = sscd.goods_code
                 left join system_users su on sscd.user_name = su.username
        WHERE 1 = 1
          and c.is_enable = '1'
          and c.order_state != 5
        <if test="Query.purchaseNumber != null and Query.purchaseNumber != ''">
            AND c.purchase_number = #{Query.purchaseNumber}
        </if>
        <if test="Query.orderNumber != null and Query.orderNumber != ''">
            AND c.order_number = #{Query.orderNumber}
        </if>
    </select>
    <select id="queryTimeOutOrder" resultMap="ShopPurchaseOrderMap">
        SELECT purchase_number, company_code, tenant_id, apply_user_id, purchase_id, send_or_not, organization_id
        FROM shop_purchase_order
        WHERE create_time &lt;= #{overTime}
        <if test="designCompanyCode != null and designCompanyCode != ''">
            AND company_code = #{designCompanyCode}
        </if>
        <if test="filterCompanyCodes != null and filterCompanyCodes.size > 0">
            AND company_code NOT IN
            <foreach close=")" collection="filterCompanyCodes" item="companyCode" open="(" separator=",">
                #{companyCode}
            </foreach>
        </if>
        and is_enable = '1'
        AND purchase_state = 10
        AND is_history != 1
        and can_confirm != 2
    </select>

    <select id="queryTimeOutNotMakeOrder" resultMap="ShopPurchaseOrderMap">
        SELECT
        distinct
        p.purchase_id,
        p.purchase_number,
        p.company_code,
        p.tenant_id,
        p.apply_user_id,
        p.purchase_id,
        p.send_or_not,
        p.organization_id,
        p.tenant_id
        FROM shop_purchase_order p
        left join shop_purchase_sub_order o on o.purchase_number=p.purchase_number
        WHERE p.create_time &lt;= #{overTime}
          and o.is_platform_reconciliation=#{isPlatformReconciliation}
          and p.is_enable = '1'
          AND p.purchase_state = 10
          AND p.is_history != 1
          and p.can_confirm = 2
    </select>

    <select id="querySupplierOrderPage" resultType="com.ly.yph.api.supplier.vo.SupplierOrderVo">
        SELECT o.order_id,
        o.order_number,
        o.supplier_order_number,
        p.apply_user_name,
        p.company_name,
        p.company_code,
        p.other_relation_number,
        o.create_time,
        o.finished_time,
        o.supplier_order_price_tax,
        o.supplier_order_price_naked,
        o.supplier_name,
        o.order_state,
        CASE o.order_state
        WHEN 0 THEN
        '已取消'
        WHEN 10 THEN
        '已提交'
        WHEN 20 THEN
        '待发货'
        WHEN 30 THEN
        '待收货'
        WHEN 40 THEN
        '收货完成'
        WHEN 45 THEN
        '部分退货'
        WHEN 50 THEN
        '全部退货'
        ELSE '订单失败'
        END order_state_name,
        a.address_name,
        a.mob_phone,
        CONCAT(a.province, a.city, a.district, a.address) address_info,
        p.remark as purchaseRemark,
        o.remark,
        ssc.currency_code,
        ssc.currency_name,
        o.supplier_confirm_state,
        i.taxpayer_number,
        p.order_label,
        i.invoice_subject,
        o.sale_client,o.supplier_data_source,o.order_sap_number,o.contract_number
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_purchase_order p ON p.purchase_number = o.purchase_number
                 LEFT JOIN shop_supplier s ON s.supplier_code = o.supplier_code
                 LEFT JOIN shop_order_address a ON a.purchase_id = p.purchase_id
                 LEFT JOIN shop_srm_contract ssc ON ssc.contract_number = o.contract_number
                 LEFT JOIN shop_order_invoice i ON i.purchase_id = p.purchase_id
        WHERE o.supplier_code = #{supplierOrderQueryDto.supplierCode}
          and o.is_enable = '1'
          and p.can_confirm != 2
          and o.order_state > 10
        <if test="supplierOrderQueryDto.orderNumber != null and supplierOrderQueryDto.orderNumber != ''">
            AND o.order_number = #{supplierOrderQueryDto.orderNumber}
        </if>
        <if test="supplierOrderQueryDto.contractNumber != null and supplierOrderQueryDto.contractNumber != ''">
            AND o.contract_number = #{supplierOrderQueryDto.contractNumber}
        </if>
        <if test="supplierOrderQueryDto.supplierOrderNumber != null and supplierOrderQueryDto.supplierOrderNumber != ''">
            AND o.supplier_order_number = #{supplierOrderQueryDto.supplierOrderNumber}
        </if>
        <if test="supplierOrderQueryDto.orderState != null">
            AND o.order_state = #{supplierOrderQueryDto.orderState}
        </if>
        <if test="supplierOrderQueryDto.applyUserName != null and supplierOrderQueryDto.applyUserName != ''">
            AND p.apply_user_name LIKE CONCAT('%', #{supplierOrderQueryDto.applyUserName}, '%')
        </if>
        <if test="supplierOrderQueryDto.companyName != null and supplierOrderQueryDto.companyName != ''">
            AND p.company_name LIKE CONCAT('%', #{supplierOrderQueryDto.companyName}, '%')
        </if>
        <if test="supplierOrderQueryDto.createTimeStart != null and supplierOrderQueryDto.createTimeStart != ''
        and supplierOrderQueryDto.createTimeEnd != null and supplierOrderQueryDto.createTimeEnd != ''">
            AND o.create_time &gt;= #{supplierOrderQueryDto.createTimeStart}
            AND o.create_time &lt;= #{supplierOrderQueryDto.createTimeEnd}
        </if>
        <if test="supplierOrderQueryDto.taxpayerNumber != null and supplierOrderQueryDto.taxpayerNumber != ''">
            AND i.taxpayer_number LIKE CONCAT('%', #{supplierOrderQueryDto.taxpayerNumber}, '%')
        </if>
        <if test="supplierOrderQueryDto.invoiceSubject != null and supplierOrderQueryDto.invoiceSubject != ''">
            AND i.invoice_subject LIKE CONCAT('%', #{supplierOrderQueryDto.invoiceSubject}, '%')
        </if>
        <if test="supplierOrderQueryDto.saleClient != null">
            and  o.sale_client = #{supplierOrderQueryDto.saleClient}
        </if>
        <if test="supplierOrderQueryDto.finishedTimeOrder==null or supplierOrderQueryDto.finishedTimeOrder=='' ">
            ORDER BY o.finished_time asc
        </if>
        <if test="supplierOrderQueryDto.finishedTimeOrder==1">
            ORDER BY o.finished_time desc
        </if>

    </select>

    <select id="querySupplierOrderById" resultType="com.ly.yph.api.supplier.vo.SupplierOrderDetailVo">
        SELECT o.order_id,
               o.order_number,
               o.supplier_order_number,
               p.purchase_number,
               p.purchase_manager_number,
               p.apply_user_name                                 purchase_manager_name,
               p.company_name,
               p.company_code,
               o.create_time,
               o.supplier_order_price_tax,
               o.order_state,
               IF( o.is_company_store = 0, a.address_name, ca.qpc_address_name )as addressName,
               IF( o.is_company_store = 0, a.mob_phone, ca.qpc_mob_phone ) as  mobPhone,
               s.supplier_type,
               CONCAT(
                       IF( o.is_company_store = 0, a.province, ca.qpc_province ),
                       IF	( o.is_company_store = 0, a.city, ca.qpc_city ),
                       IF	( o.is_company_store = 0, a.district, ca.qpc_district ),
                       IF	( o.is_company_store = 0, a.address, ca.qpc_address )
                   )  as address_info,
               o.remark,
               o.srm_apply_id,
               o.is_company_store,
               o.sale_client,p.sap_order_type,p.other_relation_number
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_purchase_order p ON p.purchase_number = o.purchase_number
                 LEFT JOIN shop_supplier s ON s.supplier_code = o.supplier_code
                 LEFT JOIN shop_order_address a ON a.purchase_id = p.purchase_id
                 LEFT JOIN company_store_order_address ca ON ca.order_number = o.order_number
        WHERE o.order_id = #{orderId}
          and o.is_enable = '1'
          and p.can_confirm != 2
    </select>
    <select id="getOftenBuyList" resultType="com.ly.yph.api.order.vo.OftenBuyListVo">
        select
        s.goods_id,
        s.goods_code,
        s.goods_sku,
        s.goods_desc,
        e.goods_image,
        o.supplier_code,
        o.supplier_name,
        (d.confirm_num + d.confirm_num_decimal) as buyNum,
        d.goods_total_price_tax as goodsTotalPriceTax
        from shop_purchase_order p
        LEFT JOIN shop_purchase_sub_order o on o.purchase_number = p.purchase_number
        LEFT JOIN shop_purchase_sub_order_detail d on d.order_id = o.order_id
        LEFT JOIN shop_goods s on s.goods_code = d.goods_code
        LEFT JOIN shop_goods_detail e on e.goods_code = d.goods_code
        <where>
            <if test="query.applyUserId != null and query.applyUserId != ''">
                and p.apply_user_id = #{query.applyUserId}
            </if>
            <if test="query.goodsCode != null and query.goodsCode != ''">
                and e.goods_code = #{query.goodsCode}
            </if>
            <if test="query.goodsDesc != null and query.goodsDesc != ''">
                and s.goods_desc like CONCAT('%', #{query.goodsDesc}, '%')
            </if>
            <if test="query.supplierCodes != null and query.supplierCodes.size() != 0">
                and o.supplier_code in
                <foreach collection="query.supplierCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and o.order_state >='30'
            and p.is_enable = '1'
            and p.can_confirm != 2
        </where>
    </select>

    <select id="queryMallOrderPage" resultMap="mallOrderQueryVo">
        select distinct spo.purchase_id,
        spo.purchase_number,
        spo.purchase_state,
        spo.apply_user_name,
        spso.order_price_tax,
        spso.order_price_naked,
        soa.address_name,
        soa.mob_phone,
        concat(soa.province, soa.city, soa.district, soa.address) as address,
        spo.create_time as purchase_create_time,
        spso.order_id,
        spso.order_number,
        spso.order_state,
        spso.fail_reason,
        spso.supplier_code,
        spso.supplier_name,
        sp.after_sales_people ,
        spso.create_time as order_create_time,
        spo.audit_time,
        spo.order_label,
        spo.other_relation_number,
        spso.is_platform_reconciliation,
        spso.order_sap_number
        from shop_purchase_order spo
        left join shop_purchase_sub_order spso on spo.purchase_number = spso.purchase_number
        left join shop_order_address soa on soa.purchase_id = spo.purchase_id
        left join shop_purchase_sub_order_detail spsod on spso.order_id = spsod.order_id
        left join shop_supplier sp on sp.supplier_code = spso.supplier_code and sp.tenant_id = spso.tenant_id
        where spo.can_confirm != 2 and spo.is_enable = '1'
        <if test="organizationIds.size > 0">
            and spo.organization_id IN
            <foreach close=")" collection="organizationIds" item="organizationId" open="(" separator=",">
                #{organizationId}
            </foreach>
        </if>
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.startCreateTime != null and queryDto.startCreateTime != ''">
            and spo.create_time &gt;= #{queryDto.startCreateTime}
        </if>
        <if test="queryDto.endCreateTime != null and queryDto.endCreateTime != ''">
            and spo.create_time &lt;= #{queryDto.endCreateTime}
        </if>
        <if test="queryDto.orderState != null">
            and spso.order_state = #{queryDto.orderState}
        </if>
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.orderSapNumber != null and queryDto.orderSapNumber != ''">
            and spso.order_sap_number = #{queryDto.orderSapNumber}
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() != 0">
            and spso.supplier_code in
            <foreach collection="queryDto.supplierCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.addressName != null and queryDto.addressName != ''">
            and soa.address_name like concat('%', #{queryDto.addressName}, '%')
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and spsod.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.orderLabels != null and queryDto.orderLabels.size() != 0">
            and spo.order_label in
            <foreach collection="queryDto.orderLabels" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by spso.create_time desc
    </select>

    <select id="exportMallOrder" resultType="com.ly.yph.api.order.vo.MyOrderFrontExcelVo">
        select spo.purchase_number,
        spso.order_number,
        spo.apply_user_name,
        spo.company_name,
        spo.create_time as purchase_create_time,
        spso.create_time as order_create_time,
        spso.supplier_name,
        spsod.goods_desc,
        spsod.goods_code,
        spsod.goods_sku,
        spsod.goods_unit_price_naked,
        spsod.goods_unit_price_tax,
        (spsod.confirm_num + spsod.confirm_num_decimal) confirm_num,
        soa.address_name,
        spsod.currency_name,
        concat(soa.province, soa.city, soa.district, soa.address) as address,
        (case spsod.order_detail_state
        when -1 then '订单失败'
        when 0 then '订单取消'
        when 10 then '已提交'
        when 20 then '待发货'
        when 30 then '待收货'
        when 40 then '收货完成'
        else '收货完成' end) as order_state,
        spso.order_sap_number
        from shop_purchase_order spo
        left join shop_purchase_sub_order spso on spo.purchase_number = spso.purchase_number
        left join shop_order_address soa on soa.purchase_id = spo.purchase_id
        left join shop_purchase_sub_order_detail spsod on spso.order_id = spsod.order_id
        where spo.can_confirm != 2 and spo.is_enable = '1'
        <if test="organizationIds.size > 0">
            and spo.organization_id IN
            <foreach close=")" collection="organizationIds" item="organizationId" open="(" separator=",">
                #{organizationId}
            </foreach>
        </if>
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.startCreateTime != null and queryDto.startCreateTime != ''">
            and spo.create_time &gt;= #{queryDto.startCreateTime}
        </if>
        <if test="queryDto.endCreateTime != null and queryDto.endCreateTime != ''">
            and spo.create_time &lt;= #{queryDto.endCreateTime}
        </if>
        <if test="queryDto.orderState != null">
            and spso.order_state = #{queryDto.orderState}
        </if>
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.orderSapNumber != null and queryDto.orderSapNumber != ''">
            and spso.order_sap_number = #{queryDto.orderSapNumber}
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() != 0">
            and spso.supplier_code in
            <foreach collection="queryDto.supplierCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.addressName != null and queryDto.addressName != ''">
            and soa.address_name like concat('%', #{queryDto.addressName}, '%')
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and spsod.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.orderLabels != null and queryDto.orderLabels.size() != 0">
            and spo.order_label in
            <foreach collection="queryDto.orderLabels" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by spso.create_time desc
    </select>
    <select id="getOtherRelationNumberRank" resultType="java.lang.String">
        select other_relation_number
        from shop_purchase_order
        where other_relation_number like concat(#{relationNumber}, "%");
    </select>

    <select id="queryAgreement" resultType="com.ly.yph.api.order.vo.AgreementQueryVo">
        select
        spsod.goods_desc ,
        spsod.goods_code,
        spsod.confirm_num,
        round(spsod.goods_unit_price_tax,2) as price,
        case spsod.order_detail_state
        when 0 then "已取消"
        when -1 then "订单失败"
        when 10 then "已提交"
        when 20 then "待发货"
        when 30 then "待收货"
        when 40 then "收货完成"
        when 45 then "部分退货"
        when 50 then "全部退货"
        else "" end state,
        spo.purchase_number,
        spso.supplier_order_number,
        spso.order_number,
        spso.supplier_name,
        spso.supplier_code,
        spo.company_name,
        spo.create_time as submissionTime,
        spo.audit_time,
        g.delivery_time deliveryDate,
        de.delivery_time,
        de.sup_receiving_time,
        de.delivery_code,
        ad.address_name,
        ad.mob_phone,
        case spso.is_platform_reconciliation when 1 then '平台内结算' when 2 then '平台外结算' else "" end isPlatformReconciliation
        from shop_delivery_detail sdd
        left join shop_delivery de on de.id = sdd.delivery_id
        left join shop_purchase_sub_order spso on de.order_id = spso.order_id and spso.is_enable = '1'
        left join shop_purchase_sub_order_detail spsod on spsod.order_number = de.order_number and spsod.goods_code =
        sdd.goods_code
        left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number and spo.is_enable = '1'
        left join shop_goods g on g.goods_code = sdd.goods_code and g.tenant_id = 1 and g.is_enable =1
        left join shop_order_address ad on ad.purchase_id = spo.purchase_id
        where spsod.is_enable = '1'
        and spsod.order_detail_state not in (0,-1,50)
        and sdd.is_enable = '1'
        and de.is_enable = '1'
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number = #{queryDto.purchaseNumber}
        </if>
        <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
            and spso.supplier_code = #{queryDto.supplierCode}
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number = #{queryDto.supplierOrderNumber}
        </if>
        <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
            and g.goods_code = #{queryDto.goodsCode}
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and g.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.isPlatformReconciliation != null and queryDto.isPlatformReconciliation != ''">
            and spso.is_platform_reconciliation = #{queryDto.isPlatformReconciliation}
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spo.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spo.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.state != null">
            <if test="queryDto.state == 30">
                and de.cus_receiving_state = 0
            </if>
        </if>
        <if test="queryDto.state != null and queryDto.state == 30">
            and de.sup_delivery_state = 1
        </if>
        <if test="organizationIds != null and organizationIds.size > 0">
            and spo.organization_id IN
            <foreach close=")" collection="organizationIds" item="organizationId" open="(" separator=",">
                #{organizationId}
            </foreach>
        </if>
        order by spo.create_time desc
    </select>
    <select id="queryAgreementDelivery" resultType="com.ly.yph.api.order.vo.AgreementQueryVo">
        select spsod.goods_desc,
               spsod.goods_code,
               spsod.goods_sku,
               spsod.goods_unit_price_tax       as price,
               case spsod.order_detail_state
                   when 0 then "已取消"
                   when -1 then "订单失败"
                   when 10 then "已提交"
                   when 20 then "待发货"
                   when 30 then "待收货"
                   when 40 then "收货完成"
                   when 45 then "部分退货"
                   when 50 then "全部退货"
                   else "" end                     state,
               spsod.confirm_num                as confirm_num,
               sum(IFNULL(sdd.delivery_num, 0)) AS delivery_num,
               --         (spsod.confirm_num -((IFNULL( SUM(sdd.delivery_num), 0 )))) as pre_delivery_num,
               CASE
                   WHEN TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'),
                                      DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 0 AND 15 THEN
                       "1"
                   WHEN TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'),
                                      DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 15 AND 30 THEN
                       "2"
                   WHEN TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'),
                                      DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 30 AND 45 THEN
                       "3"
                   ELSE "4"
                   end                             overlap_days_flag,
               spo.purchase_number,
               spso.supplier_order_number,
               spso.order_number,
               spso.supplier_name,
               spso.supplier_code,
               spo.organization_id,
               spo.company_code,
               spo.company_name,
               spo.create_time                  as submissionTime,
               spo.audit_time,
               sd.delivery_time,
               sd.sup_receiving_time,
               sd.delivery_code
        from shop_purchase_sub_order_detail spsod
                 left join shop_delivery sd on sd.order_id = spsod.order_id
                 left join shop_delivery_detail sdd on sdd.delivery_id = sd.id and sdd.goods_id = spsod.goods_id
                 left join shop_purchase_sub_order spso on spso.order_id = spsod.order_id
                 left join shop_purchase_order spo
                           on spo.purchase_number = spso.purchase_number
        where spsod.order_detail_state in (20, 30) and spo.can_confirm != 2
          and spso.supplier_data_source = 'dfmall'
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spso.purchase_number = #{queryDto.purchaseNumber}
        </if>
        <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
            and spso.supplier_code = #{queryDto.supplierCode}
        </if>
        <if test="queryDto.companyCode != null and queryDto.companyCode != ''">
            and spo.company_code = #{queryDto.companyCode}
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number = #{queryDto.supplierOrderNumber}
        </if>
        <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
            and spsod.goods_code = #{queryDto.goodsCode}
        </if>
        <if test="queryDto.goodsSku != null and queryDto.goodsSku != ''">
            and spsod.goods_sku = #{queryDto.goodsSku}
        </if>
        <if test="queryDto.overlapDaysFlag != null and queryDto.overlapDaysFlag != ''">
            <if test="queryDto.overlapDaysFlag == 1">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'),
                                  DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 0 AND 15
            </if>
            <if test="queryDto.overlapDaysFlag == 2">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'),
                                  DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 16 AND 30
            </if>
            <if test="queryDto.overlapDaysFlag == 3">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'),
                                  DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 31 AND 45
            </if>
            <if test="queryDto.overlapDaysFlag == 4">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'), DATE_FORMAT(now(), '%Y-%m-%d')) > 45
            </if>
        </if>
        <if test="queryDto.overlapDaysFlag == null or queryDto.overlapDaysFlag == ''">
            and TIMESTAMPDIFF(DAY, DATE_FORMAT(spo.create_time, '%Y-%m-%d'), DATE_FORMAT(now(), '%Y-%m-%d')) > 15
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and spsod.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.keyWords != null and queryDto.keyWords != ''">
            and (spso.purchase_number = #{queryDto.keyWords} OR
                 spso.supplier_order_number = #{queryDto.keyWords} OR
                 spsod.goods_code = #{queryDto.keyWords} OR
                 spsod.goods_sku = #{queryDto.keyWords} OR
                 spsod.goods_desc like concat('%', #{queryDto.keyWords}, '%')
                )
        </if>
        <if test="organizationIds != null and organizationIds.size > 0">
            and spo.organization_id IN
            <foreach close=")" collection="organizationIds" item="organizationId" open="(" separator=",">
                #{organizationId}
            </foreach>
        </if>
        <if test="queryDto.companyCodeList != null and queryDto.companyCodeList.size > 0">
            and spo.company_code IN
            <foreach close=")" collection="queryDto.companyCodeList" item="companyCode" open="(" separator=",">
                #{companyCode}
            </foreach>
        </if>
        group by spsod.order_detail_id, spsod.confirm_num, spsod.create_time
        having spsod.confirm_num > sum(IFNULL(sdd.delivery_num, 0))
        order by spsod.create_time
    </select>
    <select id="queryAgreementReceiving" resultType="com.ly.yph.api.order.vo.AgreementQueryVo">
        SELECT sdd.goods_desc,
               sdd.goods_code,
               sdd.goods_sku,
               spso.supplier_name,
               spso.supplier_code,
               spo.company_code,
               spo.company_name,
               spo.create_time,
               spo.audit_time,
               de.delivery_time,
               TIMESTAMPDIFF(
                       DAY,
                       de.delivery_time,
                       now())                   AS overlap_days,
               CASE
                   WHEN TIMESTAMPDIFF(
                           DAY,
                           DATE_FORMAT(de.delivery_time, '%Y-%m-%d'),
                           DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 0
                       AND 7 THEN
                       "1"
                   WHEN TIMESTAMPDIFF(
                           DAY,
                           DATE_FORMAT(de.delivery_time, '%Y-%m-%d'),
                           DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 7
                       AND 15 THEN
                       "2"
                   WHEN TIMESTAMPDIFF(
                           DAY,
                           DATE_FORMAT(de.delivery_time, '%Y-%m-%d'),
                           DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 15
                       AND 30 THEN
                       "3"
                   ELSE "4"
                   END                             overlap_days_flag,
               de.sup_receiving_time,
               SUM(IFNULL(sdd.delivery_num, 0)) AS delivery_num,
               spo.purchase_number,
               spso.supplier_order_number,
               spso.order_number,
               de.delivery_code
        FROM shop_delivery_detail sdd
                 LEFT JOIN shop_delivery de ON de.id = sdd.delivery_id
                 LEFT JOIN shop_purchase_sub_order spso ON de.order_id = spso.order_id
            AND spso.is_enable = '1'
                 LEFT JOIN shop_purchase_order spo ON spso.purchase_number = spo.purchase_number
            AND spo.is_enable = '1'
                 LEFT JOIN shop_supplier ss ON spso.supplier_code = ss.supplier_code
            AND spso.tenant_id = ss.tenant_id
        WHERE de.sup_delivery_state = 0
          AND spo.create_time > '2023-07-01 00:00:00'
          AND ss.supplier_type = 0
          AND sdd.is_enable = '1'
          AND de.is_enable = '1'
          and spo.can_confirm != 2
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number = #{queryDto.purchaseNumber}
        </if>
        <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
            and spso.supplier_code = #{queryDto.supplierCode}
        </if>
        <if test="queryDto.companyCode != null and queryDto.companyCode != ''">
            and spo.company_code = #{queryDto.companyCode}
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number = #{queryDto.supplierOrderNumber}
        </if>
        <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
            and sdd.goods_code = #{queryDto.goodsCode}
        </if>
        <if test="queryDto.goodsSku != null and queryDto.goodsSku != ''">
            and sdd.goods_sku = #{queryDto.goodsSku}
        </if>
        <if test="queryDto.keyWords != null and queryDto.keyWords != ''">
            and (spo.purchase_number = #{queryDto.keyWords} OR
                 spso.supplier_order_number = #{queryDto.keyWords} OR
                 sdd.goods_code = #{queryDto.keyWords} OR
                 sdd.goods_sku = #{queryDto.keyWords} OR
                 sdd.goods_desc like concat('%', #{queryDto.keyWords}, '%')
                )
        </if>
        <if test="queryDto.overlapDaysFlag != null and queryDto.overlapDaysFlag != ''">
            <if test="queryDto.overlapDaysFlag == 1">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(de.delivery_time, '%Y-%m-%d'),
                                  DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 0 AND 7
            </if>
            <if test="queryDto.overlapDaysFlag == 2">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(de.delivery_time, '%Y-%m-%d'),
                                  DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 8 AND 15
            </if>
            <if test="queryDto.overlapDaysFlag == 3">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(de.delivery_time, '%Y-%m-%d'),
                                  DATE_FORMAT(now(), '%Y-%m-%d')) BETWEEN 16 AND 30
            </if>
            <if test="queryDto.overlapDaysFlag == 4">
                and TIMESTAMPDIFF(DAY, DATE_FORMAT(de.delivery_time, '%Y-%m-%d'), DATE_FORMAT(now(), '%Y-%m-%d')) > 30
            </if>
        </if>
        <if test="queryDto.overlapDaysFlag == null or queryDto.overlapDaysFlag == ''">
            and TIMESTAMPDIFF(DAY, DATE_FORMAT(de.delivery_time, '%Y-%m-%d'), DATE_FORMAT(now(), '%Y-%m-%d')) > 7
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and sdd.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.state != null">
            <if test="queryDto.state == 30">
                and de.cus_receiving_state = 0
            </if>
        </if>
        <if test="queryDto.state != null and queryDto.state == 30">
            and de.sup_delivery_state = 1
        </if>
        <if test="organizationIds != null and organizationIds.size > 0">
            and spo.organization_id IN
            <foreach close=")" collection="organizationIds" item="organizationId" open="(" separator=",">
                #{organizationId}
            </foreach>
        </if>
        <if test="queryDto.companyCodeList != null and queryDto.companyCodeList.size > 0">
            and spo.company_code IN
            <foreach close=")" collection="queryDto.companyCodeList" item="companyCode" open="(" separator=",">
                #{companyCode}
            </foreach>
        </if>
        GROUP BY de.delivery_code
        ORDER BY de.delivery_time ASC
    </select>
    <select id="queryListBySubOrderNumber" resultType="com.ly.yph.api.order.vo.AgreementQueryVo">
        SELECT spsod.goods_desc,
               spsod.goods_code,
               spsod.goods_sku,
               spsod.order_number
        FROM shop_purchase_sub_order_detail spsod
        WHERE spsod.is_enable = '1'
          and spsod.order_number = #{orderNumber}
    </select>
    <select id="queryGoodsListBySubOrderNumber" resultType="com.ly.yph.api.order.vo.AgreementQueryVo">
        SELECT spsod.goods_desc,
               spsod.goods_code,
               spsod.goods_sku,
               spsod.order_number,
               ifnull(sdd.delivery_num, 0) AS delivery_num
        FROM shop_purchase_sub_order_detail spsod
                 left join shop_delivery sd on sd.order_id = spsod.order_id
                 left join shop_delivery_detail sdd on sdd.delivery_id = sd.id and sdd.goods_id = spsod.goods_id
        WHERE spsod.is_enable = '1'
          and spsod.order_number = #{orderNumber}
          and sd.delivery_code = #{deliveryCode}
    </select>
    <select id="exportSupDeliveryDetail" resultType="com.ly.yph.api.supplier.vo.SupDeliveryDetailExcelVO">
        select spo.company_name,
        spo.apply_user_name,
        concat(soa.province, ' ', soa.city, ' ', soa.district, ' ', soa.address) as address,
        concat(ca.qpc_province, ' ', ca.qpc_city, ' ', ca.qpc_district, ' ', ca.qpc_address) as qpcAddress,
        spso.remark as orderRemark,
        spso.supplier_order_number,
        spso.create_time,
        sd.delivery_code,
        sd.sup_receiving_time,
        sd.real_receiving_time,
        sd.check_time,
        (case sd.check_state
        when -99 then '验收完成'
        when -2 then '取消'
        when -1 then '验收驳回'
        when 0 then '待验收'
        when 1 then '验收中'
        when 2 then '部分验收'
        when 3 then '验收完成'
        else '' end) as check_state,
        sdd.goods_sku,
        sdd.goods_code,
        sdd.goods_desc,
        sdd.supplier_unit_price_tax,
        sdd.supplier_unit_price_naked,
        sdd.supplier_total_price_tax,
        sdd.supplier_total_price_naked,
        spso.order_number
        from shop_delivery sd
                 left join shop_delivery_detail sdd on sdd.delivery_id = sd.id
                 left join shop_purchase_sub_order spso on spso.order_id = sd.order_id
                 left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
                 left join shop_order_address soa on soa.purchase_id = spo.purchase_id
        LEFT JOIN company_store_order_address ca ON ca.order_number = spso.order_number
        where spso.supplier_code = #{queryDto.supplierCode}
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number = #{queryDto.orderNumber}
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number = #{queryDto.supplierOrderNumber}
        </if>
        <if test="queryDto.orderState != null">
            and spso.order_state = #{queryDto.orderState}
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.companyName != null and queryDto.companyName != ''">
            and spo.company_name like concat('%', #{queryDto.companyName}, '%')
        </if>
        and sd.is_enable = 1
        and sdd.is_enable = 1
    </select>
    <select id="queryAgreementDelivered" resultType="com.ly.yph.api.order.vo.AgreementQueryVo">
        select spsod.goods_desc,
               spsod.goods_code,
               spsod.confirm_num,
               round(spsod.goods_unit_price_tax, 2) as price,
               case spsod.order_detail_state
                   when 0 then '已取消'
                   when -1 then '订单失败'
                   when 10 then '已提交'
                   when 20 then '待发货'
                   when 30 then '待收货'
                   when 40 then '收货完成'
                   when 45 then '部分退货'
                   when 50 then '全部退货'
                   else '' end                         state,
               spo.purchase_number,
               spso.supplier_order_number,
               spso.order_number,
               g.delivery_time                         deliveryDate,
               spso.supplier_name,
               spo.company_name,
               spo.create_time                      as submissionTime,
               spo.audit_time,
        case spso.is_platform_reconciliation when 1 then '平台内结算' when 2 then '平台外结算' else "" end isPlatformReconciliation
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spsod.order_number = spso.order_number and spso.is_enable = 1
                 left join shop_purchase_order spo
                           on spo.purchase_number = spso.purchase_number and spo.is_enable = 1
                 left join shop_goods g on g.goods_code = spsod.goods_code and g.tenant_id = 1 and g.is_enable = 1
        where spsod.is_enable = '1' and spo.can_confirm != 2
          and spsod.order_detail_state not in (0, -1, 50)
          and spsod.order_detail_state = #{queryDto.state}
        <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
            and spso.supplier_code = #{queryDto.supplierCode}
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number = #{queryDto.supplierOrderNumber}
        </if>
        <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
            and g.goods_code = #{queryDto.goodsCode}
        </if>
        <if test="queryDto.isPlatformReconciliation != null and queryDto.isPlatformReconciliation != ''">
            and spso.is_platform_reconciliation = #{queryDto.isPlatformReconciliation}
        </if>
        <if test="queryDto.goodsDesc != null and queryDto.goodsDesc != ''">
            and g.goods_desc like concat('%', #{queryDto.goodsDesc}, '%')
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spo.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spo.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="organizationIds != null and organizationIds.size > 0">
            and spo.organization_id IN
            <foreach close=")" collection="organizationIds" item="organizationId" open="(" separator=",">
                #{organizationId}
            </foreach>
        </if>
        order by spo.create_time desc
    </select>
    <select id="queryOrderList" resultType="com.ly.yph.api.order.vo.ShopOrderPageVo">
        select spso.order_id,
               spso.order_number,
               spso.supplier_order_number,
               spo.purchase_number,
               CASE
                   WHEN e.`no` IS NULL THEN
                       (SELECT pe.`no`
                        from pay_order pay
                                 LEFT JOIN pay_order_extension pe ON pe.order_id = pay.id
                        where pay.merchant_order_id = spso.purchase_number
                          and pe.status = 10)
                   ELSE
                       e.`no`
                   END    payOrderNumber,
               spo.other_relation_number,
               spo.company_name,
               spo.apply_emp_code,
               spo.apply_user_name,
               spo.create_time,
               spso.supplier_name,
               spso.order_freight_price,
               spso.supplier_order_price_tax,
               spso.supplier_order_price_naked,
               spso.order_price_tax,
               spso.order_price_naked,
               spso.order_pay_integral,
               spso.order_pay_money,
               a.activity_name,
               od.address_name,
               spso.order_state,
               spso.is_platform_reconciliation,
               spso.order_subsidy_freight,
               i.invoice_subject,
               spso.sale_client,
        <if test="queryDto.queryType != null and queryDto.queryType == 1">
            IFNULL(sr.return_state, 99)                             AS returnState,
            IFNULL(sr.return_all_price_tax + sr.return_freight, '') AS return_all_price_tax,
            IFNULL(sr.return_all_money_tax, '')                     AS return_all_money_tax,
            sr.return_amount_time,
        </if>
        d.label as invoiceTypeName
        from shop_purchase_sub_order spso
            left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
            left join shop_order_invoice i on i.purchase_id = spo.purchase_id
            left join system_dict_data d on d.dict_type = 'A010' AND d.value = spo.invoice_type
            left JOIN system_activity a ON a.id = spo.activity_id
            left join shop_order_address od ON od.purchase_id = spo.purchase_id
            LEFT JOIN pay_order p ON p.merchant_order_id = spso.order_number
            LEFT JOIN pay_order_extension e ON e.order_id = p.id and e.status = 10
        <if test="queryDto.queryType != null and queryDto.queryType == 1">
            LEFT JOIN shop_return sr ON sr.order_id = spso.order_id AND sr.return_state not IN (-2, -1)
        </if>
        where 1 = 1
          and spso.is_enable = '1' and spo.can_confirm != 2
        <if test="queryDto.orderNumber != null and queryDto.orderNumber != ''">
            and spso.order_number like concat('%', #{queryDto.orderNumber}, '%')
        </if>
        <if test="queryDto.purchaseNumber != null and queryDto.purchaseNumber != ''">
            and spo.purchase_number like concat('%', #{queryDto.purchaseNumber}, '%')
        </if>
        <if test="queryDto.otherRelationNumber != null and queryDto.otherRelationNumber != ''">
            and spo.other_relation_number = #{queryDto.otherRelationNumber}
        </if>
        <if test="queryDto.applyUserName != null and queryDto.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{queryDto.applyUserName}, '%')
        </if>
        <if test="queryDto.supplierCodes != null and queryDto.supplierCodes.size() > 0">
            <foreach item="supplierCode" open="and spso.supplier_code in (" collection="queryDto.supplierCodes"
                     close=")"
                     separator=",">
                #{supplierCode}
            </foreach>
        </if>
        <if test="queryDto.companyCodes != null and queryDto.companyCodes.size() > 0">
            <foreach item="companyCode" open="and spo.company_code in (" collection="queryDto.companyCodes" close=")"
                     separator=",">
                #{companyCode}
            </foreach>
        </if>
        <if test="queryDto.orderStates != null and queryDto.orderStates.size() > 0">
            <foreach item="orderState" open="and spso.order_state in (" collection="queryDto.orderStates" close=")"
                     separator=",">
                #{orderState}
            </foreach>
        </if>
        <if test="queryDto.orderPriceTaxStart != null">
            and spso.order_price_tax &gt;= #{queryDto.orderPriceTaxStart}
        </if>
        <if test="queryDto.orderPriceTaxEnd != null">
            and spso.order_price_tax &lt;= #{queryDto.orderPriceTaxEnd}
        </if>
        <if test="queryDto.createTimeStart != null and queryDto.createTimeStart != ''">
            and spso.create_time &gt;= #{queryDto.createTimeStart}
        </if>
        <if test="queryDto.createTimeEnd != null and queryDto.createTimeEnd != ''">
            and spso.create_time &lt;= #{queryDto.createTimeEnd}
        </if>
        <if test="queryDto.activityName != null and queryDto.activityName != ''">
            and a.activity_name like concat('%', #{queryDto.activityName}, '%')
        </if>
        <if test="queryDto.supplierOrderNumber != null and queryDto.supplierOrderNumber != ''">
            and spso.supplier_order_number like concat('%', #{queryDto.supplierOrderNumber}, '%')
        </if>
        <if test="queryDto.isPlatformReconciliation != null ">
            and spso.is_platform_reconciliation  = #{queryDto.isPlatformReconciliation}
        </if>
        <if test="queryDto.saleClient != null">
            and spso.sale_client = #{queryDto.saleClient}
        </if>
        order by spso.create_time desc, spso.order_id
    </select>
    <select id="getApproachingExpirationOrder" resultMap="ShopPurchaseOrderMap">
        SELECT purchase_number, company_code, tenant_id, apply_user_id, purchase_id, send_or_not
        FROM shop_purchase_order
        WHERE DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(ADDDATE(NOW(), #{timeOutDay}), '%Y-%m-%d')
          and is_enable = '1'
          AND purchase_state = 10
          AND is_history != 1
    </select>

    <select id="queryDfsPayPurchaseNumber" resultType="string">
        SELECT DISTINCT p.purchase_number
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_purchase_order p ON p.purchase_number = o.purchase_number
                 LEFT JOIN system_organization org ON org.`code` = p.company_code
                 LEFT JOIN dfs_doa_pay d ON d.purchase_number = p.purchase_number
        WHERE p.dfs_pay_state in (2, 3)
          AND p.purchase_state = 30
          AND org.org_range_mark = 'DFS'
          AND p.tenant_id = 1
          and p.can_confirm != 2
        <if test="purchaseNumber != null and purchaseNumber != ''">
            AND o.purchase_number = #{purchaseNumber}
        </if>
    </select>

    <select id="queryDsDfsPayPurchaseNumber" resultType="string">
        SELECT DISTINCT p.purchase_number
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_purchase_order p ON p.purchase_number = o.purchase_number
                 LEFT JOIN system_organization org ON org.`code` = p.company_code
                 LEFT JOIN dfs_doa_pay d ON d.purchase_number = p.purchase_number
                 LEFT JOIN shop_supplier s ON s.supplier_code = o.supplier_code
        WHERE o.order_state = 10
          AND p.purchase_state = 30
          AND org.org_range_mark = 'DFS'
          AND o.supplier_data_source != #{srmDataSource}
          AND p.tenant_id = 1
          AND s.tenant_id = 1
          AND (d.pay_state IS NULL OR p.dfs_pay_state in (2, 3))
    </select>

    <select id="queryOrderVo" resultType="com.ly.yph.api.order.vo.ShopPurchaseSubOrderVo">
        SELECT t.order_id,
               t.order_number,
               t.order_model,
               t.supplier_order_number,
               t.purchase_number,
               p.other_relation_number,
               p.apply_user_id,
               p.apply_user_name,
               p.apply_dept_id,
               p.apply_dept_name,
               p.budget_id,
               p.invoice_type,
               d.label                                           invoice_type_name,
               p.activity_id,
               t.order_price_tax,
               t.order_freight_price,
               t.order_pay_integral,
               t.order_state,
               t.supplier_code,
               t.supplier_name,
               t.remark,
               t.create_time,
               a.address_name                                    address_user_name,
               a.mob_phone,
               p.company_code,
               p.company_name,
               p.purchase_number,
               CONCAT(A.province, A.city, A.district, A.address) address_info
        FROM shop_purchase_sub_order t
                 LEFT JOIN shop_purchase_order p ON p.purchase_number = t.purchase_number
                 LEFT JOIN shop_order_address a ON a.purchase_id = p.purchase_id
                 LEFT join system_dict_data d on d.dict_type = 'A010' AND d.value = p.invoice_type
        WHERE p.purchase_number = #{purchaseNumber}
          and t.is_enable = '1'
        and p.can_confirm != 2
    </select>

    <select id="exportSupplierOrderDetail" resultType="com.ly.yph.api.supplier.vo.SupplierOrderDetailExportVo">
        select (@i := @i + 1)     as indexNum,
        spsod.order_number,
        case spsod.order_detail_state
        when 0 then '已取消'
        when 10 then '已提交'
        when 20 then '待发货'
        when 30 then '待收货'
        when 40 then '收货完成'
        when 45 then '部分退货'
        when 50 then '全部退货'
        else '订单失败'
        end order_state_name,
        case spso.sale_client
        when 1 then 'B端'
        when 2 then 'C端'
        else '未知'
        end saleClientName,
        sa.address_name,
        sa.mob_phone,
        concat(sa.province, sa.city, sa.district,
        sa.address) as complete_address,
        ca.qpc_address_name,
        ca.qpc_mob_phone,
        if( spso.is_company_store = 1 , concat(ca.qpc_province, ca.qpc_city, ca.qpc_district,
        ca.qpc_address) ,'' ) as qpcAddress,
        spsod.create_time,
        spso.supplier_order_price_tax,
        spsod.goods_name,
        spsod.goods_code,
        spsod.goods_sku,
        spsod.supplier_unit_price_tax,
        (spsod.confirm_num + spsod.confirm_num_decimal) as confirm_num,
        IFNULL(( IFNULL( SUM(de.delivery_num), 0 ) + IFNULL( SUM(de.delivery_num_decimal), 0.00 )), 0 ) AS delivery_num,
        case spsod.order_detail_state
        when 10 then IFNULL(((IFNULL(spsod.confirm_num,0) + IFNULL(spsod.confirm_num_decimal,0.00)) -
        (IFNULL(de.delivery_num,0) + IFNULL(de.delivery_num_decimal,0.00))
        ), 0)
        when 20 then IFNULL(((IFNULL(spsod.confirm_num,0) + IFNULL(spsod.confirm_num_decimal,0.00)) -
        (IFNULL(de.delivery_num,0) + IFNULL(de.delivery_num_decimal,0.00))
        ), 0)
        when 30 then IFNULL(((IFNULL(spsod.confirm_num,0) + IFNULL(spsod.confirm_num_decimal,0.00)) -
        (IFNULL(de.delivery_num,0) + IFNULL(de.delivery_num_decimal,0.00))
        ), 0)
        else 0
        end pre_delivery_num,
        spso.supplier_order_number,
        spo.remark as purchaseRemark,
        spso.remark,
        ssc.currency_name,
        spo.company_name,
        spo.apply_user_name,
        spso.supplier_name,
        spso.contract_number,
        sgd.goods_spec,
        g.manufacturer_material_no,
        g.brand_name,
        IFNULL(sscd.goods_srm_code ,"") as goods_srm_code,
        concat(spsod.order_number,spsod.row_serial_number) as stalking_order_number
        from shop_purchase_sub_order_detail spsod
        left join shop_purchase_sub_order spso on spsod.order_number = spso.order_number
        left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        left join shop_order_address sa on sa.purchase_id = spo.purchase_id
        LEFT JOIN company_store_order_address ca ON ca.order_number = spso.order_number
        LEFT JOIN shop_srm_contract ssc ON ssc.contract_number = spso.contract_number
        LEFT JOIN shop_delivery d on spsod.order_id = d.order_id and spsod.order_number = d.order_number
        LEFT JOIN shop_delivery_detail de ON d.id = de.delivery_id and spsod.goods_code = de.goods_code
        LEFT JOIN shop_order_invoice i ON i.purchase_id = spo.purchase_id
        left join shop_srm_contract_detail sscd on spsod.goods_code =sscd.goods_code
        left join shop_goods g on g.goods_code=spsod.goods_code
        left join shop_goods_detail sgd on sgd.goods_code=spsod.goods_code
        , (select @i := 0) as itable
        where spsod.is_enable = '1' and spo.can_confirm != 2
          and spso.supplier_code = #{query.supplierCode}
        <if test="query.orderNumber != null and query.orderNumber != ''">
            and spso.order_number = #{query.orderNumber}
        </if>
        <if test="query.contractNumber != null and query.contractNumber != ''">
            AND spso.contract_number = #{query.contractNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber != ''">
            and spso.supplier_order_number = #{query.supplierOrderNumber}
        </if>
        <if test="query.applyUserName != null and query.applyUserName != ''">
            and spo.apply_user_name like concat('%', #{query.applyUserName}, '%')
        </if>
        <if test="query.orderState != null">
            and spsod.order_detail_state = #{query.orderState}
        </if>
        <if test="query.companyName != null and query.companyName != ''">
            and spo.company_name like concat('%', #{query.companyName}, '%')
        </if>
        <if test="query.createTimeStart != null and query.createTimeStart != ''">
            and spso.create_time &gt;= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
            and spso.create_time &lt;= #{query.createTimeEnd}
        </if>
        <if test="query.taxpayerNumber != null and query.taxpayerNumber != ''">
            AND i.taxpayer_number LIKE CONCAT('%', #{query.taxpayerNumber}, '%')
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject != ''">
            AND i.invoice_subject LIKE CONCAT('%', #{query.invoiceSubject}, '%')
        </if>
        <if test="query.saleClient != null">
            and spso.sale_client = #{query.saleClient}
        </if>
        GROUP BY spsod.order_detail_id
        order by spso.create_time desc
    </select>
    <select id="queryHondaMixShortOrderSn" resultType="java.lang.String">
        select other_relation_number
        from shop_purchase_order
        where company_code = 'HONDA'
          and other_relation_number like concat('', #{preShortOrderSn}, '%')
        order by other_relation_number desc
        limit 1
        for
        update
    </select>
    <select id="getUnSyncJfPurchaseOrder" resultType="com.ly.yph.api.order.entity.ShopPurchaseOrder">
        select *
        from shop_purchase_order
        where company_code = 'HONDA'
          AND purchase_state = 30
          AND can_confirm = 1
          AND order_label between 7 and 10
          AND create_time &lt; #{beginTime}
        order by create_time
    </select>

    <select id="queryHondaJiaQinGoods" resultType="com.ly.yph.api.order.entity.ShopPurchaseOrder">
        select p.*
        from shop_purchase_order p
                 left join shop_purchase_sub_order o on p.purchase_number = o.purchase_number
        where p.company_code = 'HONDA'
          AND p.purchase_state = 30
          AND p.apply_user_id = #{applyUserId}
          AND p.order_label = #{orderLabel}
          AND p.create_time > #{year}
          AND o.order_state >= 10
        limit 1
    </select>

    <select id="selectForAuthorizedDealerReportData"
            resultType="com.ly.yph.api.reportapi.controller.vo.AuthorizedDealerRespVO">
        select po.purchase_number,
               sub.order_number,
               sub.supplier_name,
               po.apply_user_name,
               po.apply_section_name,
               po.create_time,
               po.purchase_goods_number,
               po.purchase_goods_price,
               po.purchase_goods_price_naked,
               po.purchase_freight_price,
               po.purchase_total_price,
               po.company_name,
               sg.third_class_name,
               sg.brand_name,
               gz.zone_name,
               soa.address,
               soa.province
        from shop_purchase_order as po
                 inner join goods_zone as gz on gz.id = po.goods_zone_id
                 inner join shop_purchase_sub_order as sub on po.purchase_number = sub.purchase_number
                 inner join shop_purchase_sub_order_detail as detail on detail.order_number = sub.order_number
                 inner join shop_goods as sg on detail.goods_code = sg.goods_code
                 inner join shop_order_address as soa on soa.address_id = po.address_id
        where order_label in (7, 8)
          and po.is_enable = 1
          and po.create_time between
            #{requestVO.orderDateTime[0]}
            and
            #{requestVO.orderDateTime[1]}
    </select>
    <select id="selectForTotalData"
            resultType="com.ly.yph.api.reportapi.controller.vo.TotalFlowRespVO">
        select o.apply_user_name,
               a.factory_name,
               o.apply_section_name,
               o.create_time,
               o.order_label,
               o.other_relation_number,
               so.supplier_order_number,
               so.order_number,
               d.order_detail_state,
               so.supplier_name,
               c1.class_name as                                    first_class_name,
               c2.class_name as                                    second_class_name,
               c3.class_name as                                    third_class_name,
               d.goods_sku,
               ifnull((select case
                                  when ddd.order_detail_id is null then '首购'
                                  else '复购' end as isfirstBuy
                       from shop_purchase_sub_order_detail ddd
                       where ddd.goods_id = d.goods_id
                         and ddd.create_time &lt; d.create_time
                         and ddd.is_enable = 1 limit 1 ) , '首购') is_first_buy,
               d.goods_desc,
               d.apply_num,
               d.supplier_total_price_naked,
               d.goods_unit_price_naked,
               d.goods_total_price_naked,
               so.finished_time,
               t.real_receiving_time
        from shop_purchase_sub_order_detail d
                 left join shop_purchase_sub_order so on d.order_id = so.order_id
                 left join shop_purchase_order o on o.purchase_number = so.purchase_number
                 left join shop_order_address a on a.purchase_id = o.purchase_id
                 left join shop_goods g on g.goods_id = d.goods_id
                 left join yph_standard_class c1 on c1.standard_class_id = g.first_level_gcid
                 left join yph_standard_class c2 on c2.standard_class_id = g.second_level_gcid
                 left join yph_standard_class c3 on c3.standard_class_id = g.third_level_gcid
                 left join (select sd.order_id            as order_id,
                                   sd.real_receiving_time as real_receiving_time,
                                   sdd.goods_id           as good_id
                            from shop_delivery sd
                                     left join shop_delivery_detail sdd on sdd.delivery_id = sd.id) t
                           on t.order_id = d.order_id and t.good_id = d.goods_id
        where o.company_code = 'HONDA'
          and o.is_enable = 1
          and o.order_label is not null
          and o.can_confirm != 2;
    </select>

    <select id="checkGoodsIsBuyed"  resultType="com.ly.yph.api.order.entity.ShopPurchaseOrder">
        select p.purchase_id from shop_purchase_order p
        left join shop_purchase_sub_order o on p.purchase_number=o.purchase_number
        left join shop_purchase_sub_order_detail d on d.order_id = o.order_id
        where p.apply_user_id=#{userId} and p.activity_id=#{activityId}
        and p.purchase_state!=0 and p.purchase_state!=20 and p.is_enable=1
        and o.order_state>=10 and o.is_enable=1
        and d.goods_id=#{goodsId} and d.is_enable=1
    </select>

    <select id="orderCount" resultType="map">
        WITH company_stats AS (
            SELECT
                COUNT(DISTINCT p.company_code) AS company_count,
                (SELECT COUNT(DISTINCT supplier_code) FROM dfmall_goods_pool_sub WHERE goods_pool_id IN (1, 264)) AS supplier_count,
                (SELECT COUNT(id) FROM dfmall_goods_pool_sub WHERE goods_pool_id IN (1, 264)) AS goods_shelves_count
            FROM shop_purchase_order p
            WHERE p.purchase_state = 30
              AND p.is_enable = 1
        ),
             order_stats AS (
                 SELECT
                     COALESCE(
                             SUM(CASE
                                 WHEN YEAR(o.apply_time) = YEAR(CURDATE())
                                     THEN d.goods_unit_price_naked * d.tax_rate / 100 * (d.confirm_num - d.after_sale_num) +
                                          d.goods_unit_price_naked * (d.confirm_num - d.after_sale_num)
                                 ELSE 0 END), 0) AS year_order_amount,
                     COALESCE(SUM(d.goods_unit_price_naked * d.tax_rate / 100 * (d.confirm_num - d.after_sale_num)
                         + d.goods_unit_price_naked * (d.confirm_num - d.after_sale_num)), 0) AS total_order_amount
                 FROM order_detail_pool d
                          LEFT JOIN purchase_order_info_pool o ON o.id = d.purchase_info_id
                 WHERE o.purchase_state IN (10, 30)
                   AND d.order_detail_state IN (10, 20, 30, 40)
                   AND o.company_name != '东风商城'
                   AND o.company_name != '测试'
             )
        SELECT
            cs.company_count,
            cs.supplier_count,
            cs.goods_shelves_count,
            os.year_order_amount,
            os.total_order_amount
        FROM company_stats cs
                 CROSS JOIN order_stats os
    </select>
    <select id="queryVoyahSrmOrderSn" resultType="java.lang.String">
        select order_sap_number
        from shop_purchase_sub_order
        where supplier_data_source = 'VOYAH'
          and order_sap_number like concat(#{sapNumber}, '%')
        order by order_sap_number desc limit 1 for update
    </select>
</mapper>

