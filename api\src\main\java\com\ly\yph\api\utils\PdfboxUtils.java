package com.ly.yph.api.utils;


import com.ly.yph.api.settlement.supplier.dto.SupplierInvoicePdfDto;
import com.ly.yph.api.utils.dto.PdfInvoiceInfoDto;
import com.ly.yph.core.base.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
public class PdfboxUtils {


    public static String convertMultipartFileToPdf(MultipartFile file) {
        try {
            InputStream inputStream = file.getInputStream();
            PDDocument document = PDDocument.load(inputStream);


            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            stripper.setStartPage(1);
            stripper.setEndPage(1);

            String lastPageText = stripper.getText(document).replace(" ", "");
            Pattern pattern = Pattern.compile("发票号码[:：]{1}[0-9]{20}");
            Matcher matcher = pattern.matcher(lastPageText);
            boolean find = matcher.find();//是否包含正则匹配内容
            String invoiceNumber = "";
            if (find) {
                invoiceNumber = matcher.group();
                invoiceNumber = invoiceNumber.replace("发票号码：", "").trim();
            }
            //正则匹配发票申请单号，返回系统申请单号
            log.info("invoiceNumber:{}", invoiceNumber);
            return invoiceNumber;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static PdfInvoiceInfoDto getPdfInvoiceInfo(String path) {
        URL url = null;
        InputStream is = null;
        HttpURLConnection httpUrl = null;
        try {
            url = new URL(path);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            httpUrl.getInputStream();

            is = httpUrl.getInputStream();
            PDDocument document = PDDocument.load(is);
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            int numberOfPages = document.getNumberOfPages();
            log.info("numberOfPages:{}",numberOfPages);
            stripper.setStartPage(numberOfPages);
            stripper.setEndPage(numberOfPages);

            String lastPageText = stripper.getText(document).replace(" ", "");
            Pattern pattern = Pattern.compile("发票号码[:：]{1}[0-9]{20}");
            Matcher matcher = pattern.matcher(lastPageText);
            boolean find = matcher.find();//是否包含正则匹配内容
            PdfInvoiceInfoDto pdfInvoiceInfoDto = new PdfInvoiceInfoDto();
            String invoiceNumber = "";
            if (find) {
                invoiceNumber = matcher.group();
                invoiceNumber = invoiceNumber.replace("发票号码：", "").trim();
                log.info("invoiceNumber:{}", invoiceNumber);
                pdfInvoiceInfoDto.setInvoiceNumber(invoiceNumber);
            }

            //匹配金额
            String regex = "价税合计.*?（小写）¥(\\d+\\.\\d+)";
            Pattern pattern1= Pattern.compile(regex, Pattern.DOTALL);
            Matcher amountMatcher = pattern1.matcher(lastPageText);
            if (amountMatcher.find()) {
                String amount = amountMatcher.group(1);
                log.info("amount:{}", amount);
                pdfInvoiceInfoDto.setAmountTax(new BigDecimal(amount));
            }
            return pdfInvoiceInfoDto;
        } catch (IOException e) {
            e.printStackTrace();
            log.info("异常的图片路径是：{}",path);
            log.error("获取发票信息异常", e);
            return null;
        }

    }

    public static Date getInvoiceTime(String path){
        URL url = null;
        InputStream is = null;
        HttpURLConnection httpUrl = null;
        try {
            url = new URL(path);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            httpUrl.getInputStream();

            is = httpUrl.getInputStream();
            PDDocument document = PDDocument.load(is);
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setSortByPosition(true);
            int numberOfPages = document.getNumberOfPages();
            stripper.setStartPage(numberOfPages);
            stripper.setEndPage(numberOfPages);

            String lastPageText = stripper.getText(document).replace(" ", "");
            String regex = "开票日期：(\\d{4}年\\d{2}月\\d{2}日)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(lastPageText);
            if(matcher.find()){
                String invoiceDateStr = matcher.group(1);
                log.info("invoiceDate:{}",invoiceDateStr);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
                Date parse = sdf.parse(invoiceDateStr);
                return parse;
            }else {
                return null;
            }

        } catch (IOException | ParseException e) {
            e.printStackTrace();
            log.info("异常的图片路径是：{}",path);
            log.error("获取开票时间异常", e);
            return null;
        }
    }

    public static SupplierInvoicePdfDto parseInvoice(MultipartFile file) throws IOException {
        try (PDDocument document = PDDocument.load(file.getInputStream())) {
            PDFTextStripper stripper = new PDFTextStripper();

            // 提取最后一页数据（获取价税合计金额）
            int lastPage = document.getNumberOfPages();
            stripper.setSortByPosition(true);
            stripper.setStartPage(lastPage);
            stripper.setEndPage(lastPage);
            String lastPageText = stripper.getText(document);

            return new SupplierInvoicePdfDto(
                    extractInvoiceNumber(lastPageText),
                    formatInvoiceDate(extractInvoiceDate(lastPageText)),
                    extractTotalAmount(lastPageText)
            );
        }
    }

    public static String extractInvoiceNumber(String text) {
        // 匹配模式：发票号码/发票号 + 数字序列
        log.info("text:{}",text);
        Pattern pattern = Pattern.compile("发票(?:号码?|号)[:：]?\\s*(\\d+)");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new IllegalArgumentException("未找到发票号码");
    }

    public static String extractInvoiceDate(String text) {
        // 匹配模式：开票日期 + YYYY年MM月DD日格式
        Pattern pattern = Pattern.compile("开票日期[:：]?\\s*(\\d{4}年\\d{1,2}月\\d{1,2}日)");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new IllegalArgumentException("未找到开票日期");
    }

    public static BigDecimal extractTotalAmount(String text) {
        // 匹配模式：优先查找"价税合计"附近的金额
        Pattern pattern = Pattern.compile(
                "价税合计(?:[(（]小写[)）])?[^\\d]*[￥¥]\\s*(\\d+(?:\\.\\d+)?)"
        );
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return new BigDecimal(matcher.group(1));
        }

        // 备选方案：查找文本中最后一个金额（价税合计通常在末尾）
        Pattern fallbackPattern = Pattern.compile("[￥¥]\\s*(\\d+(?:\\.\\d+)?)");
        Matcher fallbackMatcher = fallbackPattern.matcher(text);
        BigDecimal lastAmount = null;
        while (fallbackMatcher.find()) {
            lastAmount = new BigDecimal(fallbackMatcher.group(1));
        }

        if (lastAmount != null) {
            return lastAmount;
        }

        throw new IllegalArgumentException("未找到价税合计金额");
    }

    private static LocalDate formatInvoiceDate(String rawDate) {
        // 1. 替换中文分隔符为横杠
        String normalized = rawDate
                .replace("年", "-")
                .replace("月", "-")
                .replace("日", "")
                .replace("/", "-");

        // 2. 使用Java 8时间API标准化格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-M-d");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        return LocalDate.parse(normalized, inputFormatter);
    }


}
