# 同步两个远程仓库的脚本
param(
    [string]$sourceBranch = "feature/master_for_job",
    [string]$targetBranch = "main"
)

Write-Host "仓库同步脚本" -ForegroundColor Green
Write-Host "============" -ForegroundColor Green

# 检查远程仓库状态
Write-Host "检查远程仓库状态..." -ForegroundColor Yellow
git remote -v

# 从 GitLab 拉取最新代码
Write-Host "从 GitLab 拉取最新代码..." -ForegroundColor Cyan
git fetch gitlab

# 推送到 GitHub
Write-Host "推送到 GitHub..." -ForegroundColor Cyan
try {
    # 如果是第一次推送，可能需要设置上游分支
    git push -u github $sourceBranch
    Write-Host "✓ GitHub 同步成功" -ForegroundColor Green
} catch {
    Write-Host "✗ GitHub 同步失败: $_" -ForegroundColor Red
    Write-Host "尝试强制推送..." -ForegroundColor Yellow
    try {
        git push -f github $sourceBranch
        Write-Host "✓ GitHub 强制推送成功" -ForegroundColor Green
    } catch {
        Write-Host "✗ 强制推送也失败，请检查仓库权限" -ForegroundColor Red
    }
}

# 显示同步状态
Write-Host ""
Write-Host "同步状态:" -ForegroundColor Green
Write-Host "GitLab: https://devcloud.szlanyou.com/gitlab/lyshopcloud/lylcyyph" -ForegroundColor Cyan
Write-Host "GitHub: https://github.com/zhangliang2198/lylcyyph" -ForegroundColor Cyan
