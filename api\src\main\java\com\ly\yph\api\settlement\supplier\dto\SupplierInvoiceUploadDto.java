package com.ly.yph.api.settlement.supplier.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("供应商发票上传对象")
public class SupplierInvoiceUploadDto {

    @ApiModelProperty("发票申请id")
    private Long id;

    @ApiModelProperty("发票容差金额")
    private BigDecimal toleranceAmount;

    @ApiModelProperty("不一致说明")
    private String inconsistentRemark;


}
