package com.ly.yph.api.settlement.supplier.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@ApiModel("供应商结算预算配置表")
@TableName("settle_budget")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettleBudget extends BaseEntity {

    private Long id;

    private String projectNo;

    private String projectName;

    private String budgetNumber;

    private String supplierCode;

    private String supplierName;
}
