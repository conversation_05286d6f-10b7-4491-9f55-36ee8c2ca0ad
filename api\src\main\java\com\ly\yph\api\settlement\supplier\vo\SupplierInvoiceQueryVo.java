package com.ly.yph.api.settlement.supplier.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierInvoiceQueryVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("账单编号")
    private String billSn;

    @ApiModelProperty("发票申请单号")
    private String invoiceApplyNumber;

    @ApiModelProperty("发票号")
    private  String invoiceNumber;

    @ApiModelProperty("发票状态")
    private Integer state;

    @ApiModelProperty("发票申请时间开始时间")
    private String createTimeStart;

    @ApiModelProperty("发票申请时间结束时间")
    private String createTimeEnd;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("发票明细类型 默认：0 账单明细类型 ;1:邮费明细类型")
    private Integer billInvoiceType;


}
