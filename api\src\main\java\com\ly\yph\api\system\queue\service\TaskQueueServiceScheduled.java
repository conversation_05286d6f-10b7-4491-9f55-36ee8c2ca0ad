package com.ly.yph.api.system.queue.service;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
/**
 * TaskQueueServiceScheduled类用于定时执行任务队列服务中的任务检查与执行逻辑。
 * <p>
 * 此类通过Spring的@Scheduled注解，每隔固定时间(SCHEDULER_PERIOD)调用任务处理方法。
 * 它依赖TaskQueueService来完成具体的任务队列检查与执行操作。
 * </p>
 *
 * <p>
 * 作者: 张亮<<EMAIL>>
 * </p>
 */
@Service
@Validated
@Slf4j
public class TaskQueueServiceScheduled {

    @Resource
    private TaskQueueService taskQueueService;
    private static final long SCHEDULER_PERIOD = 5000L;
    
    @Scheduled(fixedDelay = SCHEDULER_PERIOD, initialDelay = SCHEDULER_PERIOD)
    public void executeTask() {
        taskQueueService.checkAndExecutor();
    }

}
