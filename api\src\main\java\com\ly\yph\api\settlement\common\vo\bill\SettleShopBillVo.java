package com.ly.yph.api.settlement.common.vo.bill;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.yph.api.settlement.common.enums.BillApproveStatusEnum;
import com.ly.yph.api.settlement.common.enums.BillCustomerTypeEnum;
import com.ly.yph.api.settlement.common.enums.BillStatusEnum;
import com.ly.yph.api.settlement.common.enums.CustomerSourceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("账单vo")
public class SettleShopBillVo {

    @ApiModelProperty("主键")
    @ExcelIgnore
    private Long billId;

    @ApiModelProperty("账单编号")
    @ExcelProperty(index = 0, value = "账单编号")
    private String billSn;

    @ApiModelProperty("客户名称")
    @ExcelProperty(index = 1, value = "客户名称")
    private String customerName;

    @ApiModelProperty("客户编码")
    @ExcelIgnore
    private String customerCode;

    @ApiModelProperty("参考BillCustomerTypeEnum")
    @ExcelIgnore
    private Integer customerType;

    @ApiModelProperty("客户类型中文")
    @ExcelProperty(index = 2, value = "客户类别")
    private String customerTypeStr;

    @ApiModelProperty("参考CustomerSourceTypeEnum")
    @ExcelIgnore
    private Integer customerSourceType;

    @ApiModelProperty("客户来源商城中文")
    @ExcelProperty(index = 3, value = "企业类别")
    private String customerSourceTypeStr;

    @ApiModelProperty("出账年")
    @ExcelProperty(index = 4, value = "对账年度")
    private Integer checkYear;

    @ApiModelProperty("出账月")
    @ExcelProperty(index = 5, value = "对账周期")
    private Integer checkMonth;

    @ApiModelProperty("活动名称")
    @ExcelProperty(index = 6,value = "活动名称")
    private String activityName;

    @ExcelIgnore
    private String activityCode;

    @ApiModelProperty("商品含税总金额")
    @ExcelProperty(index = 7, value = "商品金额(含税)")
    private BigDecimal amountTax;

    @ApiModelProperty("商品未税总金额")
    @ExcelProperty(index = 8, value = "商品金额(未税)")
    private BigDecimal amount;

    @ApiModelProperty("积分金额")
    @ExcelProperty(index = 9, value = "积分金额")
    private BigDecimal pointAmount;

    @ApiModelProperty("个人支付金额")
    @ExcelProperty(index = 10, value = "个人支付金额")
    private BigDecimal individualPaymentAmount;

    @ApiModelProperty("邮费金额")
    @ExcelProperty(index = 11, value = "运费")
    private BigDecimal postage;

    @ApiModelProperty("含税总金额，友福利=积分金额+邮费;其他=含税总金额+邮费")
    @ExcelProperty(index = 12, value = "合计金额(含税)")
    private BigDecimal totalTaxAmount;

    @ApiModelProperty("容差金额")
    @ExcelProperty(index = 13, value = "容差金额")
    private BigDecimal toleranceAmount;


    @ApiModelProperty("结算金额")
    @ExcelProperty(index = 14, value = "结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("参考账单状态BillStatusEnum")
    @ExcelIgnore
    private Integer billStatus;

    @ApiModelProperty("账单状态中文")
    @ExcelProperty(index = 15, value = "账单状态")
    private String billStatusStr;

    @ApiModelProperty("账单审批状态")
    @ExcelIgnore
    private Integer billApproveStatus;

    @ApiModelProperty("账单审批状态中文")
    @ExcelProperty(index = 16, value = "审批状态")
    private String billApproveStatusStr;

    @ApiModelProperty("账单备注")
    @ExcelProperty(index = 17, value = "备注")
    private String remark;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ExcelProperty(index = 18,value = "出账时间")
    private Date createTime;

    @ApiModelProperty("是否平台内对账;1：平台内对账，2：平台外对账")
    @ExcelIgnore
    private Integer isPlatformReconciliation;

    @ApiModelProperty("是否推送电商账单 0：默认不推送，1：已推送")
    @ExcelIgnore
    private Integer isPush;

    @ApiModelProperty("验收单url")
    @ExcelIgnore
    private String checkUrl;

    public String getCustomerTypeStr() {
        String billCustomerTypeNameByCode = BillCustomerTypeEnum.getBillCustomerTypeNameByCode(customerType);
        return billCustomerTypeNameByCode;
    }

    public String getCustomerSourceTypeStr() {
        String customerSourceTypeNameByCode = CustomerSourceTypeEnum.getCustomerSourceTypeNameByCode(customerSourceType);
        return customerSourceTypeNameByCode;
    }

    public String getBillStatusStr() {
        String billStatusNameByCode = BillStatusEnum.getBillStatusNameByCode(billStatus);
        return billStatusNameByCode;
    }

    public String getBillApproveStatusStr() {
        String billApproveStatusNameByCode = BillApproveStatusEnum.getBillApproveStatusNameByCode(billApproveStatus);
        return billApproveStatusNameByCode;
    }


}
