package com.ly.yph.api.settlement.supplier.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.ly.yph.api.bill.service.invoiceplan.MizdInvoicePlanService;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailPageReqVo;
import com.ly.yph.api.settlement.supplier.dto.RemoveToNextMonthDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierReconciliationSumDto;
import com.ly.yph.api.settlement.supplier.service.SupplierBillService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.lock.annotation.DistributedLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@SaCheckLogin
@RestController
@Api(tags = "供应商开票")
@RequestMapping("supplierBill")
public class SupplierBillController {

    @Resource
    private SupplierBillService supplierBillService;
    @Resource
    private MizdInvoicePlanService mizdInvoicePlanService;

    /**
     * 供应商账单移月
     */
    @ApiOperation("供应商账单移月操作")
    @PostMapping("supplierDetailRemoveToNext")
    public ServiceResult<String> removeToNextMonth(@RequestBody @Validated RemoveToNextMonthDto removeToNextMonthDto) {
        return ServiceResult.succ(supplierBillService.removeToNextMonth(removeToNextMonthDto));
    }

    /**
     * 供应商账单确认
     */
    @ApiOperation("供应商账单确认")
    @PostMapping("billReconciliation")
    @DistributedLock(value = "supplierReconciliationIndex", key = "#billId", leaseTime = 90, waitLock = false, throwMessage = "当前正在执行中,请耐心等待~")
    public ServiceResult<String> supplierBillReconciliation(@RequestBody Long billId) {
        return ServiceResult.succ(supplierBillService.supplierBillReconciliation(billId));
    }

    /**
     * 生成支付计划
     */
    @ApiOperation("账单生成支付计划")
    @PostMapping("createInvoicePlan")
    public ServiceResult<String> createInvoicePlan(@RequestBody Long billId) {
        mizdInvoicePlanService.createInvoicePlanByBill(billId);
        return ServiceResult.succ();
    }

    /**
     * 供应商账单导出明细
     */
    @ApiOperation("导出明细")
    @GetMapping("exportDetail")
    public void exportDetail(HttpServletResponse response, SettleShopBillDetailPageReqVo reqVo) throws IOException {
      supplierBillService.exportBillDetail(response,reqVo);
    }

    /**
     * 上传供应商PDF验收单
     */
    @ApiOperation("上传供应商PDF验收单")
    @PostMapping("uploadCheck")
    public ServiceResult<?> uploadSupplierCheck(@RequestParam("file") MultipartFile file,
                                                @RequestParam("billSn") String billSn) throws IOException {
        return ServiceResult.succ(supplierBillService.uploadSupplierCheck(file, billSn));
    }

    /**
     * 上传账单execl
     */
    @PostMapping("uploadExcel")
    @ApiOperation("上传供应商excel")
    public ServiceResult<?> uploadExcel(@RequestBody Long billId){
        supplierBillService.supplierExcelToObs(billId);
        return ServiceResult.succ();
    }

    @GetMapping("supplierReconciliationSum")
    @ApiOperation("获取供应商对账金额")
    public ServiceResult<SupplierReconciliationSumDto> supplierReconciliationSum(String billId){
        return ServiceResult.succ(supplierBillService.supplierReconciliationSum(billId));
    }


}
