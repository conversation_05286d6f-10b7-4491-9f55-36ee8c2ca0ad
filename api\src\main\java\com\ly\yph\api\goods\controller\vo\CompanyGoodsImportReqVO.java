package com.ly.yph.api.goods.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class CompanyGoodsImportReqVO {
    @ExcelProperty("序号")
    private String index;

    @ExcelProperty("*商品名称")
    private String goodsName;

    @ExcelProperty("*商品品牌")
    private String brandName;

    @ExcelProperty("商品型号")
    private String materialsCode;

    @ExcelProperty("*交期")
    private Integer deliveryTime;

    @ExcelProperty("*销售单位")
    private String saleUnit;

    @ExcelProperty("*起订量")
    private Integer goodsMoq;

    @ExcelProperty("*库存数量")
    private Integer stockAvailable;

    @ExcelProperty("*商品分类（参考附件_商品分类）")
    private String goodsClass;

    @ExcelProperty("*税率（%）")
    private Integer taxRate;

    @ExcelProperty("SAP/物料编码")
    private String sapCode;

    @ExcelProperty("*合同编号")
    private String contractNumber;

    @ExcelProperty("*采购价（未税）")
    private BigDecimal nackPurchasePrice;
}
