package com.ly.yph.api.system.service;

import com.ly.yph.api.product.ext.zkh.entity.QueueMsgSupplier;
import com.ly.yph.api.product.ext.zkh.entity.QueueMsgSupplierProductProcessInfo;
import com.ly.yph.api.product.ext.zkh.mapper.QueueMsgSupplierMapper;
import com.ly.yph.api.product.ext.zkh.mapper.QueueMsgSupplierProductProcessInfoMapper;
import com.ly.yph.api.system.mapper.XxlJobLogMapper;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.lock.annotation.DistributedLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import javax.annotation.Resource;

@Service
public class CommonService {
  @Resource private XxlJobLogMapper xxlJobLogMapper;

  @Resource private QueueMsgSupplierMapper messageMapper;

  @Resource private QueueMsgSupplierProductProcessInfoMapper processInfoMapper;

  // 每天早上5点执行
  @Scheduled(cron = "0 0 5 * * ?")
  @DistributedLock(key = "deleteJobLog", waitLock = false)
  public void deleteJobLog() {
        xxlJobLogMapper.truncateTable();
  }

  // 每天早上5点10分执行,删除一个月之前的消息
  @Scheduled(cron = "0 10 5 * * ?")
  public void deleteQueueMessage() {
    Date oneMonthAgo = new Date(System.currentTimeMillis() - 100L * 24 * 60 * 60 * 1000);
    messageMapper.delete(
        new LambdaQueryWrapperX<QueueMsgSupplier>()
            .lt(QueueMsgSupplier::getCreateTime, oneMonthAgo));
  }

  // 每天早上5点15分执行
  @Scheduled(cron = "0 10 6 * * ?")
  public void deleteQueueMessageProc() {
    Date oneMonthAgo = new Date(System.currentTimeMillis() - 100L * 24 * 60 * 60 * 1000);
    processInfoMapper.delete(
        new LambdaQueryWrapperX<QueueMsgSupplierProductProcessInfo>()
            .lt(QueueMsgSupplierProductProcessInfo::getProcessTime, oneMonthAgo));
  }
}
