package com.ly.yph.api.bill.controller.plan;

import com.ly.yph.api.bill.controller.plan.vo.*;
import com.ly.yph.api.bill.dal.dataobject.invoiceplan.MizdInvoicePlanDO;
import com.ly.yph.api.bill.service.invoiceplan.MizdInvoicePlanService;
import com.ly.yph.api.common.BeanUtils;
import com.ly.yph.api.goods.queuehandler.QueueUtils;
import com.ly.yph.api.system.annotations.OperateLog;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.excel.util.ExcelUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import static com.ly.yph.api.bill.ErrorCodeConstants.APPKEY_ERROR;
import static com.ly.yph.api.bill.ErrorCodeConstants.STATUS_ERROR;
import static com.ly.yph.api.system.enums.OperateTypeEnum.EXPORT;
import static com.ly.yph.api.system.enums.OperateTypeEnum.IMPORT;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;

@Api(tags = "管理后台 - 账单计划")
@RestController
@RequestMapping("/mizd/invoice-plan")
@Validated
public class MizdInvoicePlanController {
  @Value("${zdjh.app.key:wd2Wpx0qFDOHoUnvXj7Ec4RbAVYMP9LkrmSfBCigt3ZNeGIaysKTQ51ul8Jh6zxd}")
  private String appKey;

  @Resource private MizdInvoicePlanService invoicePlanService;

  @PostMapping("/create")
  @ApiOperation("创建账单计划")
  @SaCheckPermission("mizd:invoice-plan:create")
  public ServiceResult<Long> createInvoicePlan(
      @Valid @RequestBody MizdInvoicePlanSaveReqVO createReqVO) {
    // 返回
    return ServiceResult.succ(invoicePlanService.createInvoicePlan(createReqVO));
  }

  @PutMapping("/update")
  @ApiOperation("更新账单计划")
  @SaCheckPermission("mizd:invoice-plan:update")
  public ServiceResult<Boolean> updateInvoicePlan(
      @Valid @RequestBody MizdInvoicePlanSaveReqVO updateReqVO) {
    invoicePlanService.updateInvoicePlan(updateReqVO);
    return ServiceResult.succ(true);
  }

  @DeleteMapping("/delete")
  @ApiOperation("删除账单计划")
  @SaCheckPermission("mizd:invoice-plan:delete")
  public ServiceResult<Boolean> deleteInvoicePlan(@RequestParam("id") Long id) {
    invoicePlanService.deleteInvoicePlan(id);
    return ServiceResult.succ(true);
  }

  @GetMapping("/get")
  @ApiOperation("获得账单计划")
  @SaCheckPermission("mizd:invoice-plan:query")
  public ServiceResult<MizdInvoicePlanSettleVo> getInvoicePlan(@RequestParam("id") Long id) {
    MizdInvoicePlanDO invoicePlan = invoicePlanService.getInvoicePlan(id);
    return ServiceResult.succ(BeanUtils.toBean(invoicePlan, MizdInvoicePlanSettleVo.class));
  }

  @GetMapping("/page")
  @ApiOperation("获得账单计划分页")
  @SaCheckPermission("mizd:invoice-plan:query")
  public ServiceResult<PageResp<MizdInvoicePlanSettleVo>> getInvoicePlanPage(
      PageReq pager, @Valid MizdInvoicePlanPageReqVO pageReqVO) {
    PageResp<MizdInvoicePlanDO> pageResult =
        invoicePlanService.getInvoicePlanPage(pager, pageReqVO);
    return ServiceResult.succ(BeanUtils.toBean(pageResult, MizdInvoicePlanSettleVo.class));
  }

  @GetMapping("/export-excel")
  @ApiOperation("导出账单计划 Excel")
  @SaCheckPermission("mizd:invoice-plan:export")
  @OperateLog(type = EXPORT)
  public void exportInvoicePlanExcel(
      PageReq pager, @Valid MizdInvoicePlanPageReqVO pageReqVO, HttpServletResponse response)
      throws IOException {

    List<MizdInvoicePlanDO> list =
        invoicePlanService.getInvoicePlanPage(pager, pageReqVO).getData();
    // 导出 Excel
    ExcelUtils.write(
        response,
        "账单计划.xls",
        "数据",
        MizdInvoicePlanRespVO.class,
        BeanUtils.toBean(list, MizdInvoicePlanRespVO.class));
  }

  /**
   * 导入账单计划的 Excel 文件，并根据解析完成的数据执行导入操作。
   *
   * @param file 包含账单计划数据的 Excel 文件，为上传文件对象
   * @return 返回导入结果的标识值
   * @throws IOException 文件读取过程中发生的 I/O 异常
   * @throws IllegalAccessException 当无法访问文件中指定字段时触发的异常
   */
  @PostMapping("/import-excel")
  @ApiOperation("导入账单计划 Excel")
  @SaCheckPermission("mizd:invoice-plan:create")
  @OperateLog(type = IMPORT)
  public ServiceResult<Long> importInvoiceExcel(@RequestParam("file") MultipartFile file)
      throws IOException, IllegalAccessException {
    List<MizdInvoicePlanImportVO> list = ExcelUtils.read(file, MizdInvoicePlanImportVO.class);
    list =
        list.stream()
            .filter(item -> StrUtil.isNotBlank(item.getIndex()))
            .collect(Collectors.toList());
    QueueUtils.trimStringFields(list);

    invoicePlanService.importInvoicePlan(list);
    return ServiceResult.succ(1L);
  }

  /**
   * 获取导入账单计划模版。
   *
   * @param response HttpServletResponse对象，用于发送Excel文件至客户端。
   * @throws IOException 当写入响应过程出现输入/输出异常时抛出。
   */
  @GetMapping("/get-import-template")
  @ApiOperation("获取导入账单计划模版")
  @SaCheckPermission("mizd:invoice-plan:create")
  public void getImportInvoiceTemplate(HttpServletResponse response) throws IOException {
    final List<MizdInvoicePlanImportVO> list = new ArrayList<>();
    MizdInvoicePlanImportVO simpleData1 =
        MizdInvoicePlanImportVO.builder()
            .index("2")
            // .payNo("ZFDH_1232123_1231_123123")
            // .payPlanStatus("未发起")
            // .approvedCompletedTime(LocalDateTime.parse("2024-12-12T12:12:12"))
            .invoiceNumber("ZFDH_001")
            .invoiceName("账单名称")
            .payType("项目类")
            .payEntity("联友智连科技有限公司")
            .purchasingManager("郑绵芳")
            .payDate(LocalDate.parse("2024-12-12"))
            .projectNo("GRM12-2241")
            .projectName("东风商城项目（M事业部）")
            .businessContractId("LYZL-GRM12-2104-*********")
            .contractName("企业商城合作协议-京东-2024/12/17（付155421.11元）")
            .payPercent(BigDecimal.ONE)
            .paymentPhases("12")
            .bank("招商银行")
            .receiveCompany("联友")
            .receiveAccount("155652288756187814")
            .contractAmount(BigDecimal.valueOf(1000000.21))
            .budgetNumber("DSS15652454")
            .moneyLowCase("1654.21")
            .hedge("no")
            .payMethod("电汇")
            .remark("账号备注")
            .paymentRroperty("no")
            .purchasingManagerCode("07802")
            .build();
    MizdInvoicePlanImportVO simpleData10 =
        MizdInvoicePlanImportVO.builder()
            .index("1")
            // .payNo("ZFDH_1232123_1231_123124")
            // .payPlanStatus("未发起")
            // .approvedCompletedTime(LocalDateTime.parse("2024-12-12T12:12:12"))
            .invoiceNumber("ZFDH_001")
            .invoiceName("账单名称")
            .payType("项目类")
            .payEntity("联友智连科技有限公司")
            .purchasingManager("郑绵芳")
            .payDate(LocalDate.parse("2024-12-12"))
            .projectNo("GRM12-2241")
            .projectName("东风商城项目（M事业部）")
            .businessContractId("LYZL-GRM12-2104-*********")
            .contractName("企业商城合作协议-京东-2024/12/17（付155421.11元）")
            .payPercent(BigDecimal.ONE)
            .paymentPhases("12")
            .bank("招商银行")
            .receiveCompany("联友")
            .receiveAccount("155652288756187814")
            .contractAmount(BigDecimal.valueOf(1000000.21))
            .budgetNumber("DSS15652454")
            .moneyLowCase("1654.21")
            .hedge("no")
            .payMethod("电汇")
            .remark("账号备注")
            .paymentRroperty("no")
            .purchaseApplyCreatorDeptName("联采云部")
            .purchasingManagerCode("07802")
            .typeOfCurrency("CNY")
            .projectBelongModule(2)
            .build();
    list.add(simpleData1);
    list.add(simpleData10);

    // 输出
    ExcelUtils.write(response, "账单计划.xls", "账单计划", MizdInvoicePlanImportVO.class, list);
  }

  /**
   * 更新账单状态的方法。
   *
   * @param updateReqVO 包含账单状态更新信息的请求对象，必须包含有效的应用密钥（appKey）和支付计划状态（payPlanStatus）。 payPlanStatus
   *     的值可以为以下之一： - "0": 未发起 - "1": 审批中 - "2": 审批通过 - "3": 审批驳回 非法的状态会抛出异常。
   * @return 返回一个包含布尔值的 {@link ServiceResult} 对象，当账单状态更新成功时返回 true。
   * @throws HttpException 当 appKey 不匹配或 payPlanStatus 值非法时抛出该异常。
   */
  // ========================= 给外部调用 ==============================
  @PutMapping("/update-status")
  @ApiOperation("更新账单状态（外部）")
  public ServiceResult<Boolean> updateInvoiceStatus(
      @Valid @RequestBody MizdInvoicePlanStatusUpdateReqVO updateReqVO) {
    if (!appKey.equals(updateReqVO.getAppKey())) {
      throw HttpException.exception(APPKEY_ERROR);
    }
    switch (updateReqVO.getPayPlanStatus()) {
      case "0":
        updateReqVO.setPayPlanStatus("未发起");
        break;
      case "1":
        updateReqVO.setPayPlanStatus("审批中");
        break;
      case "2":
        updateReqVO.setPayPlanStatus("审批通过");
        break;
      case "3":
        updateReqVO.setPayPlanStatus("审批驳回");
        break;
      default:
        throw HttpException.exception(STATUS_ERROR);
    }
    invoicePlanService.updateInvoiceStatus(updateReqVO);
    return ServiceResult.succ(true);
  }

  /**
   * 查询账单列表（外部）的接口方法。
   *
   * @param appKey 用于认证的应用密钥
   * @param pager 分页请求参数，包括页码和每页大小等信息
   * @param pageReqVO 查询条件对象，包含筛选账单列表的具体条件
   * @return 返回包含分页数据的服务结果，数据类型为账单列表的响应对象
   */
  @GetMapping("/list-data")
  @ApiOperation("查询账单列表（外部）")
  public ServiceResult<PageResp<MizdInvoicePlanRespVO>> listData(
      String appKey, PageReq pager, @Valid MizdInvoicePlanPageReqExtVO pageReqVO) {
    if (!this.appKey.equals(appKey)) {
      throw HttpException.exception(APPKEY_ERROR);
    }

    final String[] split = pageReqVO.getPayPlanStatus().split(",");

    StringBuilder payPlanStatus = new StringBuilder();
    for (final String s : split) {
      switch (s) {
        case "0":
          payPlanStatus.append("未发起,");
          break;
        case "1":
          payPlanStatus.append("审批中,");
          break;
        case "2":
          payPlanStatus.append("审批通过,");
          break;
        case "3":
          payPlanStatus.append("审批驳回,");
          break;
        default:
          throw HttpException.exception(STATUS_ERROR);
      }
    }

    // 去掉最后一个逗号
    payPlanStatus.deleteCharAt(payPlanStatus.length() - 1);
    pageReqVO.setPayPlanStatus(payPlanStatus.toString());
    PageResp<MizdInvoicePlanDO> pageResult =
        invoicePlanService.getInvoicePlanPageExt(pager, pageReqVO);
    pageResult
        .getData()
        .forEach(
            item -> {
              item.setHedge(item.getHedge().equals("yes") ? "是" : "否");
              item.setPaymentRroperty(item.getPaymentRroperty().equals("yes") ? "是" : "否");
            });
    return ServiceResult.succ(BeanUtils.toBean(pageResult, MizdInvoicePlanRespVO.class));
  }

  /**
   * 查询账单详情（外部）。
   *
   * @param appKey 调用方的授权标识，用于验证请求来源权限
   * @param id 账单的唯一标识，用于查询指定的账单详情
   * @return 返回包含账单详情的响应对象
   * @throws HttpException 如果提供的 appKey 无效，则抛出异常
   */
  @GetMapping("/detail-data")
  @ApiOperation("查询账单详情（外部）")
  public ServiceResult<MizdInvoicePlanRespExtDetailVO> detailData(String appKey, Long id) {
    if (!this.appKey.equals(appKey)) {
      throw HttpException.exception(APPKEY_ERROR);
    }
    MizdInvoicePlanDO invoicePlan = invoicePlanService.getInvoicePlan(id);

    invoicePlan.setHedge(invoicePlan.getHedge().equals("yes") ? "是" : "否");
    invoicePlan.setPaymentRroperty(invoicePlan.getPaymentRroperty().equals("yes") ? "是" : "否");
    return ServiceResult.succ(BeanUtils.toBean(invoicePlan, MizdInvoicePlanRespExtDetailVO.class));
  }
}
