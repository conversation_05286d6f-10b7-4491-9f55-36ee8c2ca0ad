package com.ly.yph.api.bill.controller.plan.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 账单计划新增/修改 Request VO")
@Data
public class MizdInvoicePlanSaveReqVO {

    @ApiModelProperty(value = "主键", required = true, example = "18135")
    private Long id;

    @ApiModelProperty(value = "支付单号 ZFDH+账单号+001(流水号)", required = true)
//    @NotEmpty(message = "支付单号 ZFDH+账单号+001(流水号)不能为空")
    private String payNo;

    @ApiModelProperty(value = "支付计划状态 0：未发起1：审批中；2：审批通过；3：审批驳回", required = true, example = "1")
    @NotEmpty(message = "支付计划状态 0：未发起1：审批中；2：审批通过；3：审批驳回不能为空")
    private String payPlanStatus;

    @ApiModelProperty(value = "审批完成时间", required = true)
    private LocalDateTime approvedCompletedTime;

    @ApiModelProperty(value = "账单编号", required = true)
    private String invoiceNumber;

    @ApiModelProperty(value = "账单名称", required = true, example = "李四")
    private String invoiceName;

    @ApiModelProperty(value = "支付类型", required = true, example = "2")
    @NotNull(message = "支付类型不能为空")
    private String payType;

    @ApiModelProperty(value = "支付主体", required = true)
    @NotEmpty(message = "支付主体不能为空")
    private String payEntity;

    @ApiModelProperty(value = "采购经理", required = true)
    @NotEmpty(message = "采购经理不能为空")
    private String purchasingManager;

    @ApiModelProperty(value = "付款日期", required = true)
    @NotNull(message = "付款日期不能为空")
    private LocalDate payDate;

    @ApiModelProperty(value = "项目编号", required = true)
    @NotEmpty(message = "项目编号不能为空")
    private String projectNo;

    @ApiModelProperty(value = "项目名称", required = true, example = "")
    private String projectName;

    @ApiModelProperty(value = "合同编号", required = true, example = "9307")
    @NotEmpty(message = "合同编号不能为空")
    private String businessContractId;

    @ApiModelProperty(value = "合同名称", required = true, example = "李四")
    @NotEmpty(message = "合同名称不能为空")
    private String contractName;

    @ApiModelProperty(value = "支付比例", required = true)
    @NotNull(message = "支付比例不能为空")
    private BigDecimal payPercent;

    @ApiModelProperty(value = "付款期数", required = true)
    @NotNull(message = "付款期数不能为空")
    private String paymentPhases;

    @ApiModelProperty(value = "开户行", required = true)
    @NotEmpty(message = "开户行不能为空")
    private String bank;

    @ApiModelProperty(value = "收款单位", required = true)
    @NotEmpty(message = "收款单位不能为空")
    private String receiveCompany;

    @ApiModelProperty(value = "账号", required = true, example = "30011")
    @NotEmpty(message = "账号不能为空")
    private String receiveAccount;

    @ApiModelProperty(value = "合同金额", required = true)
    @NotNull(message = "合同金额不能为空")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "预算编号", required = true)
    @NotEmpty(message = "预算编号不能为空")
    private String budgetNumber;

    @ApiModelProperty(value = "金额小写", required = true)
    @NotEmpty(message = "金额小写不能为空")
    private String moneyLowCase;

    @ApiModelProperty(value = "是否冲账yes no", required = true)
    @NotEmpty(message = "yes no不能为空")
    private String hedge;

  @ApiModelProperty(value = "支付方式（电汇/承兑/现金）", required = true)
  @NotEmpty(message = "支付方式（电汇/承兑/现金）")
  private String payMethod;

    @ApiModelProperty(value = "备注", required = true, example = "随便")
    private String remark;

    @ApiModelProperty(value = "是否预付 yes no", example = "yes")
    @NotEmpty(message = "是否预付不能为空")
    private String paymentRroperty;

    @ApiModelProperty(value = "内部项目标识 内部项目!= '1'  其他的都是内部", example = "1")
    private Integer projectBelongModule;

    @ApiModelProperty(value = "申请合同部门名称")
    private String purchaseApplyCreatorDeptName;

    @ApiModelProperty(value = "采购经理工号")
    private String purchasingManagerCode;

    @ApiModelProperty(value = "币种")
    private String typeOfCurrency;

    @ApiModelProperty(value = "收款方开户行联行号")
    private String routingNumber;

    @ApiModelProperty("账单附件")
    private String billUrl;

    @ApiModelProperty("发票附件")
    private String invoiceUrl;

    @ApiModelProperty("验收单附件")
    private String checkUrl;

    @ApiModelProperty("合同附件")
    private String contractUrl;

    @ApiModelProperty("其他附件")
    private String otherUrl;
}