package com.ly.yph.api.owrdandpdf;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PdfContractProcessor {

    public static byte[] makePdfBytes(String templateUrl, Map<String, String> replacements) throws IOException {
        try (PDDocument document = PDDocument.load(new ClassPathResource(templateUrl).getInputStream())) {
            String smallKey = "aMail,aName,aPhone,aAddress,bMail,bName,bPhone，bAddress";
            ClassPathResource classPathResource = new ClassPathResource("ttf/Alibaba-PuHuiTi-Bold.ttf");
            // 加载中文字体（确保字体文件在项目中）
            PDType0Font chineseFont = PDType0Font.load(document,classPathResource.getInputStream()); // 使用思源黑体

            List<TextPlaceholder> placeholders = new ArrayList<>();

            PDFTextStripper stripper = createStripper(placeholders, replacements);

            stripper.setSortByPosition(true);
            stripper.getText(document);

            // 替换占位符
            for (TextPlaceholder ph : placeholders) {
                PDPage page = document.getPage(ph.pageIndex);
                PDRectangle pageSize = page.getMediaBox();

                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {

                    int fontSize = 10;
                    if("contractCode".equals(ph.placeholder)){
                        fontSize = 14;
                        ph.x = ph.x - 50;
                    }else if(smallKey.contains(ph.placeholder)){
                        fontSize = 8;
                    }

                    // 2. 写入新文本（使用中文字体）
                    contentStream.setNonStrokingColor(80, 80, 80); // 黑色文本
                    contentStream.setFont(chineseFont, fontSize);

                    // 计算垂直位置（PDF坐标系原点在左下角）
                    float textY = pageSize.getHeight() - ph.y;
                    String text = replacements.get(ph.placeholder);

                    if(("bAddress".equals(ph.placeholder)
                            || "aAddress".equals(ph.placeholder)
                            || "supplierName".equals(ph.placeholder))
                            && text.length()>17){
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY+10);
                        contentStream.showText(text.substring(0, 17));
                        contentStream.endText();
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY-4);
                        contentStream.showText(text.substring(17, text.length()));
                        contentStream.endText();
                    }else if(("aMail".equals(ph.placeholder)
                            || "bMail".equals(ph.placeholder))
                            && text.length()>16){
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY+10);
                        contentStream.showText(text.substring(0, 16));
                        contentStream.endText();
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY-4);
                        contentStream.showText(text.substring(16, text.length()));
                        contentStream.endText();
                    }else if("supplierAddress".equals(ph.placeholder) && text.length()>31){
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY);
                        contentStream.showText(text.substring(0, 31));
                        contentStream.endText();
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY-14);
                        contentStream.showText(text.substring(31, text.length()));
                        contentStream.endText();
                    }else{
                        contentStream.beginText();
                        // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                        contentStream.newLineAtOffset(ph.x, textY);
                        contentStream.showText(text);
                        contentStream.endText();
                    }

                }
            }

            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                document.save(outputStream);  // 关键步骤：将PDF写入字节流
                return outputStream.toByteArray();
            }
        }
    }

    public static void replacePlaceholders(String inputPath, String outputPath,
                                           Map<String, String> replacements) throws IOException {
        try (PDDocument document = PDDocument.load(new File(inputPath))) {
            String smallKey = "aMail,aName,aPhone,aAddress,bMail,bName,bPhone，bAddress";
            ClassPathResource classPathResource = new ClassPathResource("ttf/Alibaba-PuHuiTi-Bold.ttf");
            // 加载中文字体（确保字体文件在项目中）
            PDType0Font chineseFont = PDType0Font.load(document,classPathResource.getInputStream()); // 使用思源黑体

            List<TextPlaceholder> placeholders = new ArrayList<>();

            PDFTextStripper stripper = createStripper(placeholders, replacements);

            stripper.setSortByPosition(true);
            stripper.getText(document);

            // 替换占位符
            for (TextPlaceholder ph : placeholders) {
                PDPage page = document.getPage(ph.pageIndex);
                PDRectangle pageSize = page.getMediaBox();

                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {

                    int fontSize = 10;
                    if("contractCode".equals(ph.placeholder)){
                        ph.x = ph.x - 30;
                        fontSize = 16;
                    }else if(smallKey.contains(ph.placeholder)){
                        fontSize = 8;
                    }

                    // 2. 写入新文本（使用中文字体）
                    contentStream.setNonStrokingColor(80, 80, 80); // 黑色文本
                    contentStream.setFont(chineseFont, fontSize);

                    // 计算垂直位置（PDF坐标系原点在左下角）
                    float textY = pageSize.getHeight() - ph.y;

                    contentStream.beginText();
                    // 调整位置：X坐标不变，Y坐标转换为PDF坐标系
                    contentStream.newLineAtOffset(ph.x, textY);
                    contentStream.showText(replacements.get(ph.placeholder));
                    contentStream.endText();
                }
            }
            document.save(outputPath);
        }
    }

    private static PDFTextStripper createStripper(List<TextPlaceholder> placeholders, Map<String, String> replacements) throws IOException{
        return new PDFTextStripper() {
            private int currentPage = 0;

            @Override
            protected void startPage(PDPage page) {
                currentPage = getCurrentPageNo() - 1;
            }

            @Override
            protected void writeString(String text, List<TextPosition> textPositions) {
                for (int i = 0; i < textPositions.size(); i++) {
                    for (String placeholder : replacements.keySet()) {
                        if (i + placeholder.length() <= textPositions.size()) {
                            // 构建实际文本进行比较
                            StringBuilder sb = new StringBuilder();
                            for (int j = 0; j < placeholder.length(); j++) {
                                sb.append(textPositions.get(i + j).getUnicode());
                            }

                            if (placeholder.equals(sb.toString())) {
                                TextPosition start = textPositions.get(i);
                                TextPosition end = textPositions.get(i + placeholder.length() - 1);

                                // 计算边界框（考虑字体下降部分）
                                float minY = start.getYDirAdj();
                                float maxY = start.getYDirAdj();

                                for (int j = 0; j < placeholder.length(); j++) {
                                    TextPosition pos = textPositions.get(i + j);
                                    float posY = pos.getYDirAdj();
                                    float height = pos.getHeightDir();
//                                        if (posY < minY) minY = posY;
//                                        if (posY + height > maxY) maxY = posY + height;
                                }

                                placeholders.add(new TextPlaceholder(
                                        placeholder,
                                        currentPage,
                                        start.getXDirAdj(),
                                        maxY, // 使用最大Y作为顶部基准
                                        end.getXDirAdj() + end.getWidthDirAdj(),
                                        maxY - minY // 计算总高度
                                ));

                                i += placeholder.length() - 1;
                                break;
                            }
                        }
                    }
                }
            }
        };
    }

    static class TextPlaceholder {
        String placeholder;
        int pageIndex;
        float x;        // 左下角X坐标
        float y;        // 顶部Y坐标（页面坐标系）
        float width;
        float height;

        public TextPlaceholder(String placeholder, int pageIndex,
                               float x, float y, float width, float height) {
            this.placeholder = placeholder;
            this.pageIndex = pageIndex;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
    }
}