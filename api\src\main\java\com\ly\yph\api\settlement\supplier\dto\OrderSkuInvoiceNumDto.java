package com.ly.yph.api.settlement.supplier.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderSkuInvoiceNumDto {
    private String orderNumber;
    private String goodsSku;
    private BigDecimal checkedNum;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderSkuInvoiceNumDto that = (OrderSkuInvoiceNumDto) o;

        if (!Objects.equals(orderNumber, that.orderNumber)) return false;
        if (!Objects.equals(goodsSku, that.goodsSku)) return false;

        if (checkedNum == null && that.checkedNum == null) return true;
        if (checkedNum == null || that.checkedNum == null) return false;
        return checkedNum.compareTo(that.checkedNum) == 0;
    }

    @Override
    public int hashCode() {
        // 关键修改：标准化BigDecimal（去除末尾零），统一哈希值
        BigDecimal normalizedCheckedNum = (checkedNum != null)
                ? checkedNum.stripTrailingZeros()
                : null;
        return Objects.hash(orderNumber, goodsSku, normalizedCheckedNum);
    }
}
