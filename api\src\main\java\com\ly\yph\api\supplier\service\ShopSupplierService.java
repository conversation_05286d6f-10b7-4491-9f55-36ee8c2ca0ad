package com.ly.yph.api.supplier.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.goods.mapper.SupplierSelfBrandMapper;
import com.ly.yph.api.goods.service.SupplierSelfBrandService;
import com.ly.yph.api.order.enums.SupplierDataSourceEnum;
import com.ly.yph.api.order.mapper.ShopPurchaseOrderMapper;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemTenant;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.enums.OrganizationTypeEnum;
import com.ly.yph.api.organization.mq.organization.OrganizationProducer;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.organization.service.SystemUsersService;
import com.ly.yph.api.settlement.common.service.SettleSupplierEmailService;
import com.ly.yph.api.supplier.convert.SupplierConvert;
import com.ly.yph.api.supplier.dto.*;
import com.ly.yph.api.supplier.entity.*;
import com.ly.yph.api.supplier.enums.SupplierLinkManEnum;
import com.ly.yph.api.supplier.enums.SupplierModelEnum;
import com.ly.yph.api.supplier.mapper.ShopSupplierMapper;
import com.ly.yph.api.supplier.mapper.SystemContractMapper;
import com.ly.yph.api.supplier.vo.*;
import com.ly.yph.api.system.enums.ActProcTypeEnum;
import com.ly.yph.api.system.feign.ActTaskFeign;
import com.ly.yph.api.system.service.EmailSendLogService;
import com.ly.yph.api.system.util.WorkflowUtils;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.mapper.SystemDictDataMapper;
import com.ly.yph.core.dic.core.vo.data.DictDataExportReqVO;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.excel.util.ExcelUtils;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.core.util.StringHelper;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.exception.SystemErrorCodeConstants.ORGANIZATION_PURCHASE_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2022/1/11 11:13
 */
@Service
@Slf4j
public class ShopSupplierService extends ServiceImpl<ShopSupplierMapper, ShopSupplier> {
    @Resource
    private SystemOrganizationService orgSrv;
    @Resource
    private SystemUsersService userSrv;
    @Resource
    @Lazy
    private SystemTenantService tenantService;
    @Resource
    private OrganizationProducer organizationProducer;
    @Resource
    private WorkflowUtils workflowUtils;
    @Resource
    private ActTaskFeign actTaskFeign;
    @Resource
    private SupplierUserService supplierUserSrv;
    @Resource
    private SystemDictDataMapper systemDictDataMapper;
    @Resource
    private SystemContractService contractSrv;
    @Resource
    private SystemContractMapper contractMapper;
    @Resource
    private SupplierSelfBrandMapper supSelfBrandMapper;
    @Resource
    private SupplierContractRealService supplierContractRealService;
    @Resource
    private SettleSupplierEmailService settleSupplierEmailService;
    @Resource
    private ShopPurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private MailService mailService;
    @Resource
    private EmailSendLogService emailSendLogService;
    @Resource
    private SupplierSelfBrandService supplierSelfBrandService;
    @Resource
    private ShopSupplierBlacklistService shopSupplierBlacklistService;
    @Resource
    private ShopSupplierDetailService shopSupplierDetailService;
    @Resource
    private ShopSupplierQualificationService shopSupplierQualificationService;
    @Resource
    private ShopSupplierLinkManService shopSupplierLinkManService;
    @Resource
    private SupplierOrganizationRuleService supplierOrganizationRuleService;
    @Resource
    private ContractChangeLogsService contractChangeLogsService;

    // 定义邮箱正则表达式
    private final static Pattern EMAIL_REGEX = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$");

    /**
     * 查询页面签证官
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@link PageResp}<{@link QuerySupplierPageVo}>
     */
    public PageResp<QuerySupplierPageVo> queryPageVo(PageReq pageReq, ShopSupplierQueryDto queryDto) {
        return baseMapper.queryPage(pageReq, queryDto);
    }

    @DataPermission(enable = false)
    @Cacheable(value = "ch_query_supplier_item", key = "'id_'+#supplierId", sync = true)
    public ShopSupplier querySupplierById(String supplierId) {
        return this.getById(supplierId);
    }

    public SupplierDetailVo querySupplierDetail(Long supplierId, String supplierFullName, String creditCode) {
        if (supplierId == null && StrUtil.isBlank(supplierFullName) && StrUtil.isBlank(creditCode)) {
            throw new ParameterException("请输入查询条件!");
        }
        SupplierDetailVo vo = baseMapper.querySupplierDetail(supplierId, supplierFullName, creditCode);
        if (vo != null) {
            vo.setShopSupplierContractVos(contractMapper.queryVoBySupplierId(vo.getSupplierId()));
            vo.setSelfBrandList(supSelfBrandMapper.queryBySupplierId(vo.getSupplierId()));
            vo.setSupplierQualificationList(shopSupplierQualificationService.queryBySupplierId(vo.getSupplierId()));
        }
        return vo;
    }

    public MySupplierVo queryMySupplier() {
        return baseMapper.queryMySupplier(LocalUserHolder.get().getId());
    }


    /**
     * 创建供应商
     *
     * @param dto 系统管理员新增供应商dto
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public ShopSupplier createSupplier(CreateSupplierDto dto) {
        SystemOrganization organization = orgSrv.getOrganization(dto.getOrganizationId());
        if (organization.getIsVirtual() != 1) {
            throw new ParameterException("请选择实体组织");
        }
        ShopSupplier shopSupplier = this.searchSupplierByOrgId(dto.getOrganizationId());
        if (shopSupplier != null) {
            throw new ParameterException("该组织已有档案，不允许再新增档案");
        }
        String organizationCode = organization.getCode();
        String adminUsername = dto.getAdminUsername();
        //准备用户信息
        SystemUsers userByUsernameNoCache = userSrv.getUserByUsernameNoCache(adminUsername);
        //准备组织信息
        List<SystemOrganization> organizationsByParentIdFromCache = orgSrv.getParentOrganizationList(organization.getId());
        Map<Long, String> orgIdToCodeMap = organizationsByParentIdFromCache.stream()
                .collect(Collectors.toMap(
                        SystemOrganization::getId,  // Key: 组织ID
                        SystemOrganization::getCode // Value: 组织编码
                ));        //多租户同步
        AtomicReference<ShopSupplier> newSupplier = new AtomicReference<>(new ShopSupplier());
        Long tenantId = TenantContextHolder.getTenantId();
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            SystemOrganization systemOrganization = orgSrv.getOrganizationByCode(organizationCode);
            Long organizationId;
            if (systemOrganization == null) {
                // 基于组织树 创建组织树
                // 重建组织树
                Map<String, Long> orgMap = orgSrv.rebuildOrganizationTree(organizationsByParentIdFromCache);
                //创建用户
                userByUsernameNoCache.setId(null);
                userByUsernameNoCache.setOrganizationId(orgMap.get(orgIdToCodeMap.get(userByUsernameNoCache.getOrganizationId())));
                userByUsernameNoCache.setEntityOrganizationId(orgMap.get(orgIdToCodeMap.get(userByUsernameNoCache.getEntityOrganizationId())));
                userByUsernameNoCache.setTenantId(TenantContextHolder.getTenantId());
                SystemUsers user = userSrv.getUserByUsername(userByUsernameNoCache.getUsername());
                if (user == null) {
                    userSrv.save(userByUsernameNoCache);
                }
                organizationId = orgMap.get(organizationCode);
            } else {
                organizationId = systemOrganization.getId();
            }

            //创建供应商
            ShopSupplier supplier = DataAdapter.convert(dto, ShopSupplier.class);
            supplier.setSupplierCode(organizationCode);
            supplier.setSupplierFullName(organization.getName());
            if (TenantContextHolder.getTenantId() != tenantId) {
                supplier.setOrganizationId(organizationId);
            }
            this.dfmallDefaultConfig(supplier);
            supplier.setTenantId(TenantContextHolder.getTenantId());
            // 处理供应商管理员，不要在代码里面重置角色！！
            if (dto.getIsCompanyAdmin().equals("no")) {
                SystemUsers adminUser = userSrv.getUserByUsername(adminUsername);
                adminUser.setCompanyAdmin(1);
                adminUser.setIdCard(dto.getAdminIdCard());
                userSrv.updateById(adminUser);
                supplierUserSrv.setSupAllRole(supplier.getSupplierType(), adminUser.getId());
            }

            // 系统管理员新增的不需要审批
            supplier.setApproveState(1);
            supplier.setApproveTime(new Date());
            this.save(supplier);
            if (TenantContextHolder.getTenantId() == tenantId) {
                newSupplier.set(supplier);
            }

            // 处理自有品牌
            supplierSelfBrandService.saveByBrandBatch(dto.getBrandIdList(), supplier.getSupplierId(),
                    supplier.getSupplierCode());

            //维护供应商联系人
            keepSupplierPeople(supplier);

            // 包含平台意向发送邮件通知
            if (Arrays.asList(SupplierModelEnum.DFMALL.getCode(), SupplierModelEnum.ALL.getCode())
                    .contains(dto.getCooperationModel())) {
                String template = "供应商名称：【" + supplier.getSupplierFullName() + "】  供应商编码：【" + supplier.getSupplierCode()
                        + "】，有意向与东风商城平台合作，请前往供应商档案查看详情";
                mailService.sendEmail("供应商意向合作通知", template, Arrays.asList("<EMAIL>", "<EMAIL>"),
                        null);
                emailSendLogService.createEmailSendLog("供应商意向合作通知", template, "<EMAIL>,<EMAIL>",
                        true);
            }
        });

        return newSupplier.get();
    }

    /**
     * 维护供应商联系人
     *
     * @param supplier
     */
    private void keepSupplierPeople(ShopSupplier supplier) {
        //每次进来默认删除
        shopSupplierLinkManService.delByCode(supplier.getSupplierCode());

        List<ShopSupplierLinkMan> saveList = new ArrayList<>();

        List<String> fieldList = Arrays.asList(SupplierLinkManEnum.SEARCH.getField(),
                SupplierLinkManEnum.PRE.getField(),
                SupplierLinkManEnum.ORDER.getField(),
                SupplierLinkManEnum.AFTER.getField(),
                SupplierLinkManEnum.CLEARING.getField()
        );

        fieldList.forEach(fieldName -> {
            // 获取 supplier 对应 field 属性的值
            try {
                Field field = supplier.getClass().getDeclaredField(fieldName);

                field.setAccessible(true);
                JSONArray jsonArray = JSONUtil.parseArray(field.get(supplier));
                List<SupplierContactVo> contacts = jsonArray.toList(SupplierContactVo.class);
                SupplierLinkManEnum linkManEnum = SupplierLinkManEnum.fromField(fieldName);
                // [{"name":"喻晓容","email":"<EMAIL>","showIcon":0,"phone":"19387562095"},{"name":"小小","phone":"13394859309","email":"<EMAIL>","showIcon":0}]
                contacts.forEach(e -> {
                    if (StringUtil.isBlank(e.getName())) {
                        return;
                    }
                    ShopSupplierLinkMan shopSupplierLinkMan = new ShopSupplierLinkMan();
                    shopSupplierLinkMan.setSupplierId(supplier.getSupplierId());
                    shopSupplierLinkMan.setSupplierCode(supplier.getSupplierCode());
                    shopSupplierLinkMan.setLinkManName(e.getName());
                    shopSupplierLinkMan.setLinkManEmail(e.getEmail());
                    shopSupplierLinkMan.setLinkManMobile(e.getPhone());
                    shopSupplierLinkMan.setLinkManType(linkManEnum.getCode());
                    saveList.add(shopSupplierLinkMan);
                });
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        });

        if (CollectionUtil.isNotEmpty(saveList)) {
            shopSupplierLinkManService.saveBatch(saveList);
        }
    }

    private void dfmallDefaultConfig(ShopSupplier supplier) {
        supplier.setIsSeekPrice(1);
    }

    public void registerSupplierCheck(String supplierCode, String creditCode, String supplierFullName) {
        if (StrUtil.isNotBlank(supplierCode)) {
            ShopSupplier supplier = this.selectByCode(supplierCode);
            if (supplier != null) {
                throw new ParameterException("供应商编码已经存在!");
            }
        }

        if (StrUtil.isNotBlank(creditCode)) {
            ShopSupplier supplier = baseMapper.selectByCreditCodeDataSource(creditCode, SupplierDataSourceEnum.DFMALL.getName());
            if (supplier != null) {
                throw new ParameterException("统一社会信用代码已经存在!");
            }
        }

        if (StrUtil.isNotBlank(supplierFullName)) {
            QueryWrapper<ShopSupplier> qw = new QueryWrapper<>();
            qw.lambda().eq(ShopSupplier::getSupplierFullName, supplierFullName).eq(ShopSupplier::getDataSource, SupplierDataSourceEnum.DFMALL.getName());
            ShopSupplier supplier = this.getOne(qw);
            if (supplier != null) {
                throw new ParameterException("供应商全称已经存在!");
            }
        }

        // 加一个黑名单校验
        if (StrUtil.isNotBlank(creditCode)) {
            ShopSupplierBlacklistEntity blacklistEntity = shopSupplierBlacklistService
                    .getOne(new QueryWrapper<ShopSupplierBlacklistEntity>().lambda()
                            .eq(ShopSupplierBlacklistEntity::getCreditCode, creditCode));
            if (blacklistEntity != null) {
                throw new ParameterException("该供应商已被加入平台黑名单，无法注册");
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public void registerSupplier(RegisterSupplierDto dto) {
        // 查询审批流程
        final Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("procType", ActProcTypeEnum.SUPPLIER_SETTLE_IN.getCode());
        final Long deployId = this.purchaseOrderMapper.queryDeployByCompanyCode(paramMap);

        // 按租户注册供应商
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            try {
                SystemOrganization org = orgSrv.getBaseMapper().selectByCode(dto.getSupplierCode());
                if (org != null) {
                    throw new ParameterException("供应商编码[{}]已存在!", dto.getSupplierCode());
                }
                ShopSupplier creditExist = baseMapper.selectByCreditCodeDataSource(dto.getCreditCode(), SupplierDataSourceEnum.DFMALL.getName());
                if (creditExist != null) {
                    throw new ParameterException("统一社会信用代码[{}]已存在!", dto.getCreditCode());
                }
                SystemUsers adminUser = userSrv.getUserByUsername(dto.getAdminUsername());
                if (adminUser == null) {
                    throw new ParameterException("管理员账号[{}]不存在", dto.getAdminUsername());
                }

                // 查询上级组织
                SystemOrganization parentOrg;
                if (dto.getSupplierType() == 0) {
                    // 电商平台
                    parentOrg = orgSrv.getOrganizationByCode("ElectronicCommerce");
                } else {
                    // 独立供应商
                    parentOrg = orgSrv.getOrganizationByCode("IndependentSupplier");
                }
                // 创建组织
                SystemOrganization orgCreate = new SystemOrganization();
                orgCreate.setParentId(parentOrg.getId());
                orgCreate.setCode(dto.getSupplierCode());
                orgCreate.setName(dto.getSupplierFullName());
                // 实体组织
                orgCreate.setIsVirtual(1);
                orgCreate.setStatus(1);
                orgCreate.setSort(0);
                // 供应商
                orgCreate.setOrganizationType(1);
                orgSrv.save(orgCreate);
                organizationProducer.sendOrganizationRefreshMessage();

                // 设置管理员
                adminUser.setCompanyAdmin(1);
                // 直接把管理员放到新组织
                adminUser.setLinkCompanyState(2);
                adminUser.setOrganizationId(orgCreate.getId());
                adminUser.setOrganizationCode(orgCreate.getCode());
                adminUser.setOrganizationName(orgCreate.getName());
                adminUser.setEntityOrganizationId(orgCreate.getId());
                adminUser.setEntityOrganizationCode(orgCreate.getCode());
                adminUser.setEntityOrganizationName(orgCreate.getName());
                userSrv.updateById(adminUser);

                // 保存供应商
                ShopSupplier supplier = SupplierConvert.INSTANCE.convert(dto);
                this.dfmallDefaultConfig(supplier);
                supplier.setOrganizationId(orgCreate.getId());

                if (deployId == null) {
                    supplier.setApproveState(1);
                    supplier.setApproveTime(new Date());
                    supplierUserSrv.setSupAllRole(supplier.getSupplierType(), adminUser.getId());
                } else {
                    supplier.setApproveState(0);
                }
                supplier.setAuthenticationConditionUrl(dto.getAuthenticationConditionUrl());
                supplier.setIsAudited(1);
                supplier.setSettlementType(1);
                supplier.setSettlementPeriod(60);
                this.save(supplier);
            } catch (Exception ex) {
                SystemTenant tenant = tenantService.getTenant(TenantContextHolder.getRequiredTenantId());
                log.error(StrUtil.format("[{}]registerSupplier error", tenant.getName()), ex);
                throw new ParameterException(StrUtil.format("[{}]{}", tenant.getName(), ex.getMessage()));
            }
        });
        // 包含平台意向发送邮件通知
        if (Arrays.asList(SupplierModelEnum.DFMALL.getCode(), SupplierModelEnum.ALL.getCode())
                .contains(dto.getCooperationModel())) {
            String template = "供应商名称：【" + dto.getSupplierFullName() + "】  供应商编码：【" + dto.getSupplierCode()
                    + "】，有意向与东风商城平台合作，请前往供应商档案查看详情";
            mailService.sendEmail("供应商意向合作通知", template, Arrays.asList("<EMAIL>", "<EMAIL>"),
                    null);
            emailSendLogService.createEmailSendLog("供应商意向合作通知", template, "<EMAIL>,<EMAIL>",
                    true);

        }

        // 多租户只启动一次审批流
//        workflowUtils.startWorkflowAdmin(ActProcTypeEnum.SUPPLIER_SETTLE_IN, dto.getCreditCode(),
//                Collections.emptyMap());
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public void registerSupplierAgain(RegisterSupplierAgainDto dto) {
        ShopSupplier supplier = this.getBaseMapper().selectByCreditCodeDataSource(dto.getCreditCode(), SupplierDataSourceEnum.DFMALL.getName());
        if (supplier == null) {
            throw new ParameterException("供应商[{}]不存在", dto.getCreditCode());
        }
        if (supplier.getApproveState() != 2) {
            throw new ParameterException("审批驳回的供应商才能重新发起注册");
        }

        AtomicReference<Boolean> i = new AtomicReference<>(true);
        // 按租户重新发起注册
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            ShopSupplier exist = this.getBaseMapper().selectByCreditCodeDataSource(dto.getCreditCode(), SupplierDataSourceEnum.DFMALL.getName());
            if (exist == null) {
                return;
            }
            // 供应商类型改变则需要同步修改组织
            if (!exist.getSupplierType().equals(dto.getSupplierType())) {
                // 查询上级组织
                SystemOrganization parentOrg;
                if (dto.getSupplierType() == 0) {
                    // 电商平台
                    parentOrg = orgSrv.getOrganizationByCode("ElectronicCommerce");
                } else {
                    // 独立供应商
                    parentOrg = orgSrv.getOrganizationByCode("IndependentSupplier");
                }
                SystemOrganization supplierOrg = orgSrv.getOrganizationByCode(exist.getSupplierCode());
                supplierOrg.setParentId(parentOrg.getId());
                orgSrv.updateById(supplierOrg);
            }
            if (i.get() && SupplierModelEnum.COMPANY.getCode().equals(exist.getCooperationModel())
                    && Arrays.asList(SupplierModelEnum.DFMALL.getCode(), SupplierModelEnum.ALL.getCode())
                    .contains(dto.getCooperationModel())) {
                String template = "供应商名称：【" + exist.getSupplierFullName() + "】  供应商编码：【" + exist.getSupplierCode()
                        + "】，有意向与东风商城平台合作，请前往供应商档案查看详情";
                mailService.sendEmail("供应商意向合作通知", template,
                        Arrays.asList("<EMAIL>", "<EMAIL>"), null);
                emailSendLogService.createEmailSendLog("供应商意向合作通知", template,
                        "<EMAIL>,<EMAIL>", true);
                i.set(false);
            }
            BeanUtil.copyProperties(dto, exist);
            exist.setApproveState(0);
            this.updateById(exist);
        });

        // 多租户只启动一次审批流
        workflowUtils.startWorkflowAdmin(ActProcTypeEnum.SUPPLIER_SETTLE_IN, dto.getCreditCode(),
                Collections.emptyMap());
    }

    /**
     * 供应商入驻审批不通过
     *
     * @param creditCode 统一社会信用代码
     */
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public void supplierApprovalVoided(String creditCode) {
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            UpdateWrapper<ShopSupplier> update = new UpdateWrapper<>();
            update.lambda().set(ShopSupplier::getApproveState, 2).eq(ShopSupplier::getCreditCode, creditCode);
            this.update(update);
        });
    }

    /**
     * 供应商入驻审批通过
     *
     * @param creditCode 统一社会信用代码
     */
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public void supplierApprovalCompleted(String creditCode) {
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            ShopSupplier supplier = baseMapper.selectByCreditCodeDataSource(creditCode, SupplierDataSourceEnum.DFMALL.getName());
            supplier.setApproveState(1);
            supplier.setApproveTime(new Date());
            this.updateById(supplier);
            SystemUsers adminUser = userSrv.getUserByUsername(supplier.getAdminUsername());
            supplierUserSrv.setSupAllRole(supplier.getSupplierType(), adminUser.getId());
        });
    }

    @DataPermission(enable = false)
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public void companyAdminUpdateSup(CompanyAdminUpdateSupDto dto) {
        ShopSupplier supplier = baseMapper.selectByCode(dto.getSupplierCode());
        if (supplier == null) {
            throw new ParameterException("供应商[{}]不存在", dto.getSupplierCode());
        }
        if (supplier.getApproveState() != 1) {
            throw new ParameterException("审批通过的供应商才能重新发起注册");
        }

        Long tenantId = TenantContextHolder.getRequiredTenantId();
        AtomicReference<Boolean> i = new AtomicReference<>(true);
        // 按租户同步修改
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            ShopSupplier exist = this.getBaseMapper().selectByCode(dto.getSupplierCode());

            if (exist != null) {
                // 处理人员信息
                processingAdminUser(exist, dto.getAdminUsername(), dto.getAdminIdCard());

                // 意向供应商邮件通知
                potentialEmailNotification(i, exist, dto.getCooperationModel());
                String oldOperatorIdCard = exist.getOperatorIdCard();
                BeanUtil.copyProperties(dto, exist);
                if(StringUtil.isNotBlank(exist.getEAccountId())
                        && dto.getOperatorIdCard()!=null
                        && !dto.getOperatorIdCard().equals(oldOperatorIdCard)){
                    exist.setEAccountId("");
                }
                this.updateById(exist);
                companyAdminUpdateDetail(exist.getSupplierId(), dto);

                //处理资质
                companyAdminUpdateQualification(dto.getSupplierQualificationList(), dto.getDelQualificationIds(), exist.getSupplierId(), exist.getSupplierCode());

                //维护供应商联系人
                keepSupplierPeople(exist);

                //if (exist.getTenantId().equals(tenantId)) {
                // 只处理当前租户
                //合同不处理了
                //companyAdminUpdateSupContract(dto.getContractInfoList(), exist.getSupplierId());
                //}
            }
        });
    }

    /**
     * 维护资质信息数据
     *
     * @param list
     * @param delQualificationIds
     * @param supplierId
     * @param supplierCode
     */
    private void companyAdminUpdateQualification(List<SupplierQualificationDto> list, List<Long> delQualificationIds, Long supplierId, String supplierCode) {
        //先删除
        if (CollectionUtil.isNotEmpty(delQualificationIds)) {
            shopSupplierQualificationService.removeBatchByIds(delQualificationIds);
        }
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        log.info("操作资质管理信息更改: {} , 供应商编码: {}", list, supplierCode);

        List<ShopSupplierQualification> updateList = new ArrayList<>();
        List<ShopSupplierQualification> addList = new ArrayList<>();
        list.stream().forEach(item -> {
            ShopSupplierQualification one = DataAdapter.convert(item, ShopSupplierQualification.class);
            //设置是否有效
            boolean isIn = DateUtil.isIn(new Date(), DateUtil.parse(item.getStartDate(), DatePattern.NORM_DATETIME_PATTERN), DateUtil.parse(item.getEndDate(), DatePattern.NORM_DATETIME_PATTERN));
            one.setIsStart(isIn ? 1 : 0);
            one.setSupplierId(supplierId);
            one.setSupplierCode(supplierCode);
            if (item.getId() == null) {
                // 新增
                addList.add(one);
            } else {
                updateList.add(one);
            }
        });

        if (CollectionUtil.isNotEmpty(addList)) {
            shopSupplierQualificationService.saveBatch(addList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            shopSupplierQualificationService.updateBatchById(updateList);
        }
    }

    public void companyAdminUpdateOenSupContract(ShopSupplierContractDto dto) {
        Long supplierId = dto.getSupplierId();
        if (supplierId == null) {
            return;
        }
        log.info("操作合同信息, 供应商id: {}", supplierId);

        List<Long> finalOrganizationIds = orgSrv.queryIdsByType(OrganizationTypeEnum.SYSTEM.getCode());

        if (!dto.getIsUpdate()) {
            contractSrv.checkContract(supplierId, dto.getSignCompanyId(), dto.getContractCode());
        }

        // 合同不支持删除
        // 签约企业 + 合同编号 + 供应商 唯一
        // 签约企业 + 合同编号 + 供应商 合同文件,合同效期 + 合同id 唯一
        // 是否已经存在签约的合同
        SystemContractEntity contractEntity = contractSrv.queryBySignCompanyCodeContractCodeSupplierId(dto.getSignCompanyCode(),
                dto.getContractCode(), supplierId);
        Long contractId;
        Boolean isSend = false;
        if (contractEntity == null) {
            // 新增
            SystemContractEntity contract = DataAdapter.convert(dto, SystemContractEntity.class);
            contractSrv.save(contract);
            contractId = contract.getId();
        } else {
            contractId = contractEntity.getId();
        }
        // 新增关系
        SupplierContractRealEntity realEntity = DataAdapter.convert(dto, SupplierContractRealEntity.class);

        realEntity.setSupplierId(supplierId)
                .setContractId(contractId)
                .setIsStart(dto.getIsStart())
                .setSignStatus(contractSrv.getSignStatus(dto.getContractEndDate()));
        SupplierContractRealEntity supplierContractRealEntity = supplierContractRealService
                .queryBySupplierIdContractId(supplierId, contractId);
        Integer oldIsStart = 0;
        if (supplierContractRealEntity != null) {
            realEntity.setId(supplierContractRealEntity.getId());
            oldIsStart = supplierContractRealEntity.getIsStart();
        } else {
            isSend = true;
        }
        supplierContractRealService.saveOrUpdate(realEntity);
        if (finalOrganizationIds.contains(dto.getSignCompanyId())) {
            //查询此供应商其余有效平台合同, 有则不需要修改
            List<SupplierContractRealVo> supplierContractRealVos = supplierContractRealService.queryPlatformContract(supplierId, finalOrganizationIds, 1, realEntity.getId());
            if (CollectionUtil.isEmpty(supplierContractRealVos)) {
                ShopSupplier shopSupplier = new ShopSupplier();
                // 平台合同启用, 是平台; 停用,不是平台
                shopSupplier.setSupplierId(supplierId).setIsPlatform(dto.getIsStart());
                this.updateById(shopSupplier);
            }

            //商业条款完善邮件提醒
            if (isSend) {
                reminderPlatformEmail(supplierId, dto.getSignCompany(), dto.getContractName(), dto.getContractCode());
            }
        }

        //停启用
        if (!dto.getIsStart().equals(oldIsStart) && StrUtil.isNotBlank(dto.getReason())) {
            ContractChangeDto contractChangeDto = new ContractChangeDto();
            contractChangeDto.setContractId(contractId)
                    .setReason(dto.getReason())
                    .setFileName(dto.getFileName())
                    .setFileUrl(dto.getFileUrl())
                    .setIsStart(dto.getIsStart());
            contractChangeLogsService.createChangeLog(Arrays.asList(contractChangeDto));
        }

        if (dto.getIsStart() == 0 && oldIsStart != 0) {
            log.info("合同失效下架商品:{}", contractId);
            try {
                contractSrv.goodsDownByContract(Arrays.asList(contractId));
            } catch (Exception e) {
                log.info("操作下架失败,{}", e.getMessage());
            }
        }
    }

    private void reminderPlatformEmail(Long supplierId, String signCompany, String contractName, String contractCode) {
        ShopSupplier supplier = this.getById(supplierId);
        String reminderPlatformTemplate = mailService.getReminderPlatformTemplate(supplier.getSupplierFullName(), signCompany, contractCode, contractName);
        List<String> emails = contractSrv.getFiltrationEmails();
        String subject = "供应商签约东风商城通知";
        mailService.sendEmail(subject, reminderPlatformTemplate, emails);
        String sendToStr = String.join(";", emails);
        emailSendLogService.createEmailSendLog(subject, reminderPlatformTemplate, sendToStr, true);
    }

    public void companyAdminUpdateSupContract(List<ShopSupplierContractDto> list, Long supId) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        log.info("操作合同信息更改: {} , 供应商id: {}", list, supId);

        List<Long> finalOrganizationIds = orgSrv.queryIdsByType(OrganizationTypeEnum.SYSTEM.getCode());
        // 合同不支持删除
        // 签约企业 + 合同编号 唯一
        // 签约企业 + 合同编号 + 供应商 合同文件,合同效期唯一
        // 是否已经存在签约的合同

        Set<Long> contractIds = new HashSet<>();
        AtomicReference<Boolean> isPlatform = new AtomicReference<>(false);
        list.stream().forEach(item -> {
            SystemContractEntity contractEntity = contractSrv.queryBySignCompanyIdContractCodeSupplierId(item.getSignCompanyId(),
                    item.getContractCode(), supId);
            Long contractId;
            if (contractEntity == null) {
                // 新增
                SystemContractEntity contract = DataAdapter.convert(item, SystemContractEntity.class);
                SystemOrganization org = orgSrv.getOrganizationByCode(item.getSignCompanyCode());
                contract.setSignCompanyId(org.getId());
                contract.setSignCompany(org.getName());
                contractSrv.save(contract);
                contractId = contract.getId();
            } else {
                contractId = contractEntity.getId();
            }
            // 新增关系
            SupplierContractRealEntity realEntity = DataAdapter.convert(item, SupplierContractRealEntity.class);

            realEntity.setSupplierId(supId)
                    .setContractId(contractId)
                    .setIsStart(item.getIsStart());
            SupplierContractRealEntity supplierContractRealEntity = supplierContractRealService
                    .queryBySupplierIdContractId(supId, contractId);
            Integer oldIsStart = 0;
            if (supplierContractRealEntity != null) {
                realEntity.setId(supplierContractRealEntity.getId());
                oldIsStart = supplierContractRealEntity.getIsStart();
            }
            supplierContractRealService.saveOrUpdate(realEntity);
            if (item.getIsStart() == 1 && finalOrganizationIds.contains(item.getSignCompanyId())) {
                // 平台合同启用, 是平台; 停用,不是平台
                isPlatform.set(true);
            }

            if (item.getIsStart() == 0 && oldIsStart != 0) {
                contractIds.add(contractId);
            }
        });

        //维护供应商是否平台
        ShopSupplier shopSupplier = new ShopSupplier();
        shopSupplier.setSupplierId(supId).setIsPlatform(isPlatform.get() ? 1 : 0);
        this.updateById(shopSupplier);
        //下架
        if (CollectionUtil.isNotEmpty(contractIds)) {
            log.info("合同失效下架商品:{}", contractIds);
            try {
                contractSrv.goodsDownByContract(contractIds);
            } catch (Exception e) {
                log.info("操作下架失败,{}", e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public Map<String, Boolean> sysAdminUpdateSup(SysAdminUpdateSupDto dto) {
        ShopSupplier supplier = baseMapper.selectByCode(dto.getSupplierCode());
        if (supplier == null) {
            throw new ParameterException("供应商[{}]不存在", dto.getSupplierCode());
        }
        if (supplier.getApproveState() != 1) {
            throw new ParameterException("审批通过的供应商才能重新发起注册");
        }
        if(StrUtil.isBlank(dto.getTenantIds())){
            throw new ParameterException("上架租户配置不能为空");
        }
        if (dto.getIsBlacklist() == 1 && (supplier.getSupplierType() == 0
                || Arrays.asList("GYSYZH0", "DSXFS00", "DSKLP00").contains(supplier.getSupplierCode()))) {
            throw new ParameterException("此供应商不能加入黑名单,{}", supplier.getSupplierCode());
        }
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        AtomicReference<Boolean> i = new AtomicReference<>(true);
        // 按租户同步修改
        TenantUtils.execute(tenantService.getTenantIds(), () -> {
            ShopSupplier exist = this.getBaseMapper().selectByCode(dto.getSupplierCode());
            if (exist == null) {
                return;
            }
            // 处理人员信息
            processingAdminUser(supplier, dto.getAdminUsername(), dto.getAdminIdCard());

            // 意向供应商邮件通知
            potentialEmailNotification(i, exist, dto.getCooperationModel());
            String oldOperatorIdCard = exist.getOperatorIdCard();
            BeanUtil.copyProperties(dto, exist);
            exist.setIsBlacklist(null);
            if (dto.getIsBlacklist() == 1) {
                exist.setIsUpNew(null)
                        .setIsSeekPrice(null);
            }
            if (!exist.getTenantId().equals(tenantId)) {
                exist.setIsAudited(null)
                        .setCanReceiveEmail(null)
                        .setIsUpNew(null)
                        .setIsSeekPrice(null);
            }
            if(StringUtil.isNotBlank(exist.getEAccountId())
                    && dto.getOperatorIdCard()!=null
                    && !dto.getOperatorIdCard().equals(oldOperatorIdCard)){
                exist.setEAccountId("");
            }
            this.updateById(exist);
            //维护明细
            companyAdminUpdateDetail(exist.getSupplierId(), dto);
            //处理资质
            companyAdminUpdateQualification(dto.getSupplierQualificationList(), dto.getDelQualificationIds(), exist.getSupplierId(), exist.getSupplierCode());

            //维护供应商联系人
            keepSupplierPeople(exist);

            if (exist.getTenantId().equals(tenantId)) {
                // 只处理当前租户
                //companyAdminUpdateSupContract(dto.getContractInfoList(), exist.getSupplierId());

                // 处理自有品牌
                supplierSelfBrandService.saveByBrandBatch(dto.getBrandIdList(), exist.getSupplierId(),
                        exist.getSupplierCode());

            }
        });

        if (dto.getIsBlacklist() == 1) {
            // 加入供应商黑名单
            shopSupplierBlacklistService
                    .saveBlacklist(DataAdapter.convert(supplier, ShopSupplierBlacklistEntity.class));
        } else {
            // 解除供应商黑名单
            shopSupplierBlacklistService.removeBlacklist(supplier.getCreditCode());
        }

        HashMap<String, Boolean> resultMap = new HashMap<>();
        resultMap.put("isUpNew", checkEmail(SupplierLinkManEnum.SEARCH.getCode(), dto.getIsUpNew(), supplier.getSupplierCode()));
        resultMap.put("isSeekPrice",
                checkEmail(SupplierLinkManEnum.SEARCH.getCode(), dto.getIsSeekPrice(), supplier.getSupplierCode()));
                
        return resultMap;
    }

    private void companyAdminUpdateDetail(Long supplierId, Object dto) {
        ShopSupplierDetail detail = shopSupplierDetailService.getBaseMapper().selectBySupplierId(supplierId);
        if (detail == null) {
            ShopSupplierDetail shopSupplierDetail = new ShopSupplierDetail();
            BeanUtil.copyProperties(dto, shopSupplierDetail);
            shopSupplierDetail.setSupplierId(supplierId);
            shopSupplierDetailService.save(shopSupplierDetail);
        } else {
            BeanUtil.copyProperties(dto, detail);
            shopSupplierDetailService.updateById(detail);
        }
    }

    /**
     * 意向供应商邮件通知
     *
     * @param i
     * @param shopSupplier
     * @param cooperationModel
     */
    public void potentialEmailNotification(AtomicReference<Boolean> i, ShopSupplier shopSupplier,
                                           Integer cooperationModel) {
        if (i.get() && SupplierModelEnum.COMPANY.getCode().equals(shopSupplier.getCooperationModel())
                && Arrays.asList(SupplierModelEnum.DFMALL.getCode(), SupplierModelEnum.ALL.getCode())
                .contains(cooperationModel)) {
            String template = "供应商名称：【" + shopSupplier.getSupplierFullName() + "】  供应商编码：【"
                    + shopSupplier.getSupplierCode() + "】，有意向与东风商城平台合作，请前往供应商档案查看详情";
            mailService.sendEmail("供应商意向合作通知", template, Arrays.asList("<EMAIL>", "<EMAIL>"),
                    null);
            emailSendLogService.createEmailSendLog("供应商意向合作通知", template, "<EMAIL>,<EMAIL>",
                    true);
            i.set(false);
        }
    }

    /**
     * 处理人员信息 切换供应商管理员
     *
     * @param supplier
     * @param adminUsername
     * @param adminIdCard
     */
    public void processingAdminUser(ShopSupplier supplier, String adminUsername, String adminIdCard) {
        if (!supplier.getAdminUsername().equals(adminUsername)) {
            SystemUsers oldAdminUser = userSrv.getUserByUsername(supplier.getAdminUsername());
            SystemUsers adminUser = userSrv.getUserByUsername(adminUsername);
            if (adminUser == null) {
                return;
            }
            SystemOrganization org = orgSrv.getOrganizationByCode(supplier.getSupplierCode());
            if (org == null) {
                return;
            }
            if (oldAdminUser != null) {
                // 旧管理员改成普通员工
                oldAdminUser.setCompanyAdmin(0);
                userSrv.updateById(oldAdminUser);
                // 旧管理员角色修改
                supplierUserSrv.setSupDefaultRole(supplier.getSupplierType(), oldAdminUser.getId(),
                        supplier.getSupplierCode());
            }
            // 设置新管理员
            adminUser.setCompanyAdmin(1);
            adminUser.setIdCard(adminIdCard);
            // 直接把管理员放到新组织
            adminUser.setOrganizationId(org.getId());
            adminUser.setOrganizationCode(org.getCode());
            adminUser.setOrganizationName(org.getName());
            adminUser.setEntityOrganizationId(org.getId());
            adminUser.setEntityOrganizationCode(org.getCode());
            adminUser.setEntityOrganizationName(org.getName());
            userSrv.updateById(adminUser);
            supplierUserSrv.setSupAllRole(supplier.getSupplierType(), adminUser.getId());
        }
    }

    public Boolean checkEmail(Integer type, Integer config, String supplierCode) {
        if (!TenantContextHolder.getRequiredTenantId().equals(1L)) {
            return false;
        }
        List<String> emailList = shopSupplierLinkManService.getEmailsBySupplierCodeAndType(supplierCode, type);
        if (config != null && config.equals(1)) {
            if (CollectionUtil.isNotEmpty(emailList)) {
                // 未配置收件人,需要维护
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public String sysAdminBatchUpdateSup(MultipartFile file) throws IOException {
        try (final ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory()) {
            List<SysAdminBatchUpdateSupExcelDto> list =
                    ExcelUtils.read(file, SysAdminBatchUpdateSupExcelDto.class);
            if (CollUtil.isEmpty(list)) {
                return "成功!";
            }
            StringBuffer sb = new StringBuffer();
            List<SysAdminBatchUpdateSupExcelDto> filterList =
                    list.stream()
                            .filter(
                                    vo -> {
                                        Set<ConstraintViolation<SysAdminBatchUpdateSupExcelDto>> validate =
                                                validatorFactory.getValidator().validate(vo);
                                        if (CollUtil.isEmpty(validate)) {
                                            return true;
                                        } else {
                                            validate.forEach(
                                                    cv ->
                                                            sb.append(
                                                                    StrUtil.format(
                                                                            "供应商编码[{}]:{}\r\n", vo.getSupplierCode(), cv.getMessage())));
                                            return false;
                                        }
                                    })
                            .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterList)) {
                return StrUtil.isBlank(sb) ? "成功!" : sb.toString();
            }
            // 按租户同步修改
            TenantUtils.execute(
                    tenantService.getTenantIds(),
                    () -> {
                        String tenantName =
                                tenantService.getTenant(TenantContextHolder.getRequiredTenantId()).getName();
                        List<ShopSupplier> supplierList =
                                baseMapper.batchSelectBySupplierCode(
                                        filterList.stream()
                                                .map(SysAdminBatchUpdateSupExcelDto::getSupplierCode)
                                                .collect(Collectors.toList()));
                        Map<String, ShopSupplier> supplierMap =
                                supplierList.stream()
                                        .collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity()));
                        List<ShopSupplier> updateSup = new ArrayList<>();
                        filterList.forEach(
                                vo -> {
                                    ShopSupplier supplier = supplierMap.get(vo.getSupplierCode());
                                    if (supplier == null) {
                                        sb.append(
                                                StrUtil.format(
                                                        "租户:[{}],供应商编码:[{}]不存在!\r\n", tenantName, vo.getSupplierCode()));
                                        return;
                                    }
                                    if (supplier.getApproveState() != 1) {
                                        sb.append(
                                                StrUtil.format(
                                                        "租户:[{}],供应商编码:[{}]未审批通过,不允许修改!\r\n",
                                                        tenantName,
                                                        vo.getSupplierCode()));
                                        return;
                                    }
                                    BeanUtil.copyProperties(vo, supplier);
                                    updateSup.add(supplier);
                                });
                        this.updateBatchById(updateSup);
                    });
            return StrUtil.isBlank(sb) ? "成功!" : sb.toString();
        }
    }

    public void exportShopSupplier(HttpServletResponse response, ShopSupplierQueryDto shopSupplierQueryDto) {
        try {
            List<ShopSupplierExcelVo> supplierList = baseMapper.querySupplierExportList(shopSupplierQueryDto);
            // 导出 Excel
            ExcelUtils.write(response, "供应商信息导出" + DateUtils.format(new Date(), "yyyy-MM-dd") + ".xlsx", "数据", ShopSupplierExcelVo.class, supplierList);
        } catch (IOException e) {
            log.error("异常为：", e);
        }
    }

    public String batchApproveSupplier(MultipartFile file) throws IOException {
        List<ApproveSupplierExcelVo> list = ExcelUtils.read(file, ApproveSupplierExcelVo.class);
        List<ShopSupplier> supplierList = baseMapper.batchSelectBySupplierCode(list.stream()
                .map(ApproveSupplierExcelVo::getSupplierCode).collect(Collectors.toList()));
        Map<String, ShopSupplier> supplierMap = supplierList.stream()
                .collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity()));
        Map<String, String> businessKeyMap = workflowUtils.queryBusinessKey(supplierList.stream()
                .map(ShopSupplier::getCreditCode).collect(Collectors.toList()));
        Long tid = TenantContextHolder.getRequiredTenantId();
        LoginUser loginUser = LocalUserHolder.get();
        String result = list.parallelStream().map(vo -> TenantUtils.execute(tid, loginUser, () -> {
            ShopSupplier supplier = supplierMap.get(vo.getSupplierCode());
            if (supplier == null) {
                return StrUtil.format("供应商[{}]不存在!\r\n", vo.getSupplierCode());
            }
            String businessKey = businessKeyMap.get(supplier.getCreditCode());
            if (StrUtil.isNotBlank(businessKey)) {
                try {
                    actTaskFeign.completeTaskByBusinessKey(businessKey, "通过".equals(vo.getApprovalStatus()) ? 1 : 0,
                            vo.getRemark(), "");
                } catch (Exception ex) {
                    log.error(StrUtil.format("供应商[{}]审批失败!", vo.getSupplierCode()), ex);
                    return StrUtil.format("供应商[{}]审批失败!\r\n", vo.getSupplierCode());
                }
                return "";
            } else {
                return StrUtil.format("供应商[{}]审批信息不存在!\r\n", vo.getSupplierCode());
            }
        })).collect(Collectors.joining());
        return StrUtil.isBlank(result) ? "成功!" : result;
    }

    public List<ShopSupplier> getSimpleList(ShopSupplierSimpleQueryVo reqVo) {
        LambdaQueryWrapperX<ShopSupplier> queryWrapper = new LambdaQueryWrapperX<>();

        queryWrapper.eq(ShopSupplier::getApproveState, 1)
                .eq(reqVo.getSupplierType() != null, ShopSupplier::getSupplierType, reqVo.getSupplierType())
                .eqIfPresent(ShopSupplier::getIsUpNew, reqVo.getIsUpNew())
                .eqIfPresent(ShopSupplier::getDataSource, reqVo.getSupplierDataSource())
                .eqIfPresent(ShopSupplier::getIsPlatform, reqVo.getIsPlatform());

        if (reqVo.getIsFilter() == 1) {
            queryWrapper.notIn(ShopSupplier::getSupplierCode, filterSupplierList());
        }
        List<ShopSupplier> list = this.list(queryWrapper);

        this.jdCustomization(reqVo.getIsJdCustomization(), reqVo.getCompanyCode(), list, 0);

        if (reqVo.getIsAddOpen() == 1) {
            List<ShopSupplier> openList = this.list(new LambdaQueryWrapperX<ShopSupplier>()
                    .in(ShopSupplier::getSupplierCode, Arrays.asList("GYSYZH0", "DSXFS00", "DSKLP00")));
            list.addAll(openList);
        }

        if (reqVo.getMergerShort() == 1) {
            for (ShopSupplier shopSupplier : list) {
                List<ShopSupplier> RepeatList = list.stream()
                        .filter(item -> item.getSupplierShortName().equals(shopSupplier.getSupplierShortName()))
                        .collect(Collectors.toList());
                String supplierCodes = RepeatList.stream().map(item -> item.getSupplierCode())
                        .collect(Collectors.joining(","));
                shopSupplier.setSupplierCode(supplierCodes);
            }
            list = list.stream()
                    .collect(
                            Collectors.toMap(ShopSupplier::getSupplierShortName, name -> name, (name1, name2) -> name1))
                    .values().stream().collect(Collectors.toList());
        }
        return list;
    }

    /**
     * @param isJdCustomization
     * @param orgCode
     * @param list              供应商集合
     * @param operationType     操作类型 0 列表查询(销服和用户都得过滤) 1 导出模板(销服不过滤,用户过滤)
     */
    public void jdCustomization(Integer isJdCustomization, String orgCode, List<ShopSupplier> list, Integer operationType) {
        if (isJdCustomization != null && isJdCustomization == 1) {
            //获取当前用户是哪个企业
            LoginUser loginUser = LocalUserHolder.get();
            String companyCode = "";
            if (userSrv.containsRole(loginUser.getId(), "PTYY")) {
                //如果是运营
                if (operationType == 1) {
                    return;
                }
                if (StringUtil.isBlank(orgCode)) {
                    throw new ParameterException("运营上新,申请企业编码不能为空");
                }
                companyCode = orgCode;
            } else {
                //如果是客户
                companyCode = loginUser.getEntityOrganizationCode();
            }
            //获取配置 过滤供应商
            ShopSupplierOrganizationRule rule = supplierOrganizationRuleService.getByCode(companyCode);
            if (rule == null) {
                rule = supplierOrganizationRuleService.getByCode("DEFAULT");
            }
            List<String> excludeCodes = Arrays.stream(
                            Optional.ofNullable(rule.getDelSupplierCodes())
                                    .orElse("")
                                    .split(",")
                    )
                    .map(String::trim)
                    .filter(code -> !code.isEmpty())
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(excludeCodes)) {
                return;
            }

            List<ShopSupplier> supplierList = new ArrayList<>();
            //处理
            list.forEach(e -> {
                if (excludeCodes.contains(e.getSupplierCode())) {
                    supplierList.add(e);
                }
            });
            list.removeAll(supplierList);
        }
    }

    // 当前用户若属于实体组织编码为：HONDA;HONDA_SALE ,供应商中不展示【
    // 广州晶东贸易有限公司（DSJD000，华南京东供东本外客户）】，其余组织供应商中不展示【武汉京东世纪贸易有限公司（DSJD000_WH，华中京东仅供东本）】

    /**
     * 过滤配置的指定供应商
     */
    public List<String> filterSupplierList() {
        LoginUser localUser = LocalUserHolder.get();
        List<SystemDictDataEntity> showConfig = systemDictDataMapper.selectListByDictType("supplier_show_config");
        List<String> filterSupplierList = new ArrayList<>();
        List<String> defaultFilterSupplierList = new ArrayList<>();
        final Boolean[] isDefault = {true};
        showConfig.forEach(e -> {
            List<String> supplierList = new ArrayList<>(Arrays.asList(e.getLabel().split(",")));
            if (e.getValue().equals("default")) {
                CollectionUtil.addAll(defaultFilterSupplierList, supplierList);
            } else if (new ArrayList<>(Arrays.asList(e.getValue().split(",")))
                    .contains(localUser.getEntityOrganizationCode())) {
                isDefault[0] = false;
                CollectionUtil.addAll(filterSupplierList, supplierList);
            }
        });
        return isDefault[0] ? defaultFilterSupplierList : filterSupplierList;
    }

    @DataPermission(enable = false)
    @Cacheable(value = "ch_query_supplier_item", key = "'organizationId_'+#organizationId", sync = true)
    public ShopSupplier searchSupplierByOrgId(Long organizationId) {
        return this.getBaseMapper().selectOne(ShopSupplier::getOrganizationId, organizationId);
    }

    @DataPermission(enable = false)
    @Cacheable(value = "ch_query_supplier_item", key = "'supplierCode_'+#supplierCode", sync = true)
    public ShopSupplier selectByCode(String supplierCode) {
        return baseMapper.selectByCode(supplierCode);
    }

    @DataPermission(enable = false)
    @Cacheable(value = "ch_query_supplier_item", key = "'all'", sync = true)
    public List<ShopSupplier> getAllSupplier() {
        return baseMapper.getAllSupplier();
    }

    public List<ShopSupplier> getFreeShippingCondition(String supplierCode) {
        QueryWrapper<ShopSupplier> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(ShopSupplier::getSupplierCode, ShopSupplier::getFreeShippingCondition);
        if (!StringHelper.IsEmptyOrNull(supplierCode)) {
            queryWrapper.lambda().eq(ShopSupplier::getSupplierCode, supplierCode);
        }
        return this.list(queryWrapper);
    }

    public List<ShopSupplier> getSupplierInfoByCodes(List<String> codes) {
        QueryWrapper<ShopSupplier> queryWrapper = new QueryWrapper<>();
        if (CollectionUtil.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        queryWrapper.lambda().in(ShopSupplier::getSupplierCode, codes);
        return this.list(queryWrapper);
    }

    /**
     * 供应商档案 - 校验是否继续补充 true 需要补充
     *
     * @return
     * @throws IllegalAccessException
     */
    public Boolean checkContinuationSupplement() throws IllegalAccessException {
        MySupplierVo mySupplierVo = this.queryMySupplier();
        if (mySupplierVo == null) {
            throw new HttpException(ORGANIZATION_PURCHASE_NOT_FOUND);
        }
        SupplierDetailVo supplierDetailVo = this.querySupplierDetail(mySupplierVo.getSupplierId(),
                mySupplierVo.getSupplierFullName(), mySupplierVo.getCreditCode());
        DictDataExportReqVO reqVO = new DictDataExportReqVO();
        reqVO.setDictType("supplier_check_field").setStatus(1);
        List<SystemDictDataEntity> showConfig = systemDictDataMapper.selectList(reqVO);
        List<String> checkFieldList = showConfig.stream().map(SystemDictDataEntity::getValue)
                .collect(Collectors.toList());
        // List<String> checkSettlementList = new
        // ArrayList<>(Arrays.asList("invoiceBank","invoiceBankAccount","invoicePhone","invoiceAddress","invoiceType","settlementPeriod"));

        Class<?> clazz = supplierDetailVo.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true); // 使私有属性也可以访问
            String name = field.getName();
            Object o = field.get(supplierDetailVo);
            if (o == null) {
                if (checkFieldList.contains(name)) {
                    log.info("[{}]此属性没有维护数据", name);
                    return true;
                }
            } else if (o.toString().equals("[{\"name\":\"\",\"email\":\"\",\"showIcon\":0}]")) {
                log.info("[{}]此属性没有维护数据", name);
                return true;
            }
        }
        return false;
    }

    public PageResp<ShopSupplierInviteVo> queryPageByInvite(PageReq pageReq, ShopSupplierInviteQueryVo dto) {
        return baseMapper.queryPageByInvite(pageReq, dto);
    }

    public ShopSupplier selectByName(String supplierName) {
        return null;
    }

    /**
     * 获取可以开始的合同
     *
     * @param dataSource
     * @return
     */
    public List<Long> getBeginContract(String dataSource) {
        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(dataSource) || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(dataSource)) {
            return baseMapper.getBeginContract(dataSource);
        }
        return baseMapper.autoStartContract(dataSource);
    }


    public void initializeJob() {

        List<ShopSupplier> list = this.list(new LambdaQueryWrapperX<ShopSupplier>().eq(ShopSupplier::getDataSource, SupplierDataSourceEnum.DFMALL.getName()));

        list.forEach(supplier -> {
            //维护供应商联系人
            keepSupplierPeople(supplier);
        });
    }

    /**
     * 通过供应商编码获取供应商信息
     *
     * @param supplierCode
     * @return
     */
    public ShopSupplier getBySupplierCode(String supplierCode) {
        QueryWrapper<ShopSupplier> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopSupplier::getSupplierCode, supplierCode);
        return this.getOne(queryWrapper);
    }

    public ShopSupplierSyncInfoVo getSupplierInfoById(Long supplierId){
        return baseMapper.getSupplierInfoById(supplierId);
    }

    @CacheEvict(value = "ch_query_supplier_item", allEntries = true)
    public void approveContractUpdateSup(ApproveContractUpdateSupDto dto) {
        ShopSupplier supplier = baseMapper.selectByCode(dto.getSupplierCode());
        ShopSupplierDetail detail = shopSupplierDetailService.getBaseMapper().selectByCode(dto.getSupplierCode());
        if (detail == null) {
            Assert.notNull(supplier, () -> new ParameterException("供应商不存在"));
            detail = new ShopSupplierDetail();
            detail.setSupplierId(supplier.getSupplierId());
            detail.setSupplierCode(dto.getSupplierCode());
        }
        BeanUtil.copyProperties(dto, detail);
        detail.setUpdateTime(null);
        shopSupplierDetailService.saveOrUpdate(detail);
        if(dto.getSettlementPeriod()!=null){
            supplier.setUpdateTime(new Date());
            supplier.setSettlementPeriod(dto.getSettlementPeriod());
            this.updateById(supplier);
        }
    }

    public void updateOperatorInfo(ContractStartUpdateSupDto dto) {
        ShopSupplier supplier = baseMapper.selectByCode(dto.getSupplierCode());
        Assert.notNull(supplier, () -> new ParameterException("供应商不存在"));
        BeanUtil.copyProperties(dto, supplier);
        if(StringUtil.isNotBlank(supplier.getEAccountId()) && !dto.getOperatorIdCard().equals(supplier.getOperatorIdCard())){
            supplier.setEAccountId("");
        }
        supplier.setUpdateTime(new Date());
        saveOrUpdate(supplier);
    }

    public MySupplierVo querySupplierInfo(Long supplierId) {
        return baseMapper.querySupplierInfo(supplierId);
    }
}
