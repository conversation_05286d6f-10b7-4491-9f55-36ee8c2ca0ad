package com.ly.yph.api.settlement.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@ApiModel("账单明细表")
@TableName("supplier_invoice_bill_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SupplierInvoiceBillDetail extends BaseEntity {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    @ApiModelProperty("供应商发票id")
    private Long invoiceId;

    @ApiModelProperty("账单明细id")
    private Long billDetailId;

    @ApiModelProperty("开票数量")
    private BigDecimal invoiceNum;

    @ApiModelProperty("未税单价")
    private BigDecimal goodsUnitPriceNaked;

    @ApiModelProperty("含税单价")
    private BigDecimal goodsUnitPriceTax;

    @ApiModelProperty("未税总计")
    private BigDecimal goodsTotalPriceNaked;

    @ApiModelProperty("含税总计")
    private BigDecimal goodsTotalPriceTax;

}
