package com.ly.yph.api.settlement.supplier.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.ly.yph.api.settlement.supplier.dto.*;
import com.ly.yph.api.settlement.supplier.factory.SupplierInvoiceFactory;
import com.ly.yph.api.settlement.supplier.factory.SupplierInvoiceStrategy;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillService;
import com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceBillVo;
import com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceQueryVo;
import com.ly.yph.api.utils.PdfboxUtils;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;

@Slf4j
@SaCheckLogin
@RestController
@Api(tags = "供应商开票")
@RequestMapping("supplierInvoice")
public class SupplierInvoiceBillController {

    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;
    @Resource
    private SupplierInvoiceFactory supplierInvoiceFactory;


    @ApiOperation("供应商EXCEL账单开票模板导出")
    @GetMapping("supplierInvoiceTemplate")
    public void excelInvoiceTemplate(HttpServletResponse response, Integer billInvoiceType) throws IOException {
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(billInvoiceType);
        supplierInvoiceStrategy.exportInvoiceTemplate(response);
    }

    @ApiOperation("导入账单明细excel进行开票")
    @PostMapping("excelInvoice")
    public ServiceResult<String> excelInvoice(@RequestParam(value = "file") MultipartFile file, SupplierInvoiceApplyDto supplierInvoiceApplyDto) throws IOException {
        return ServiceResult.succ(supplierInvoiceBillService.excelInvoice(file, supplierInvoiceApplyDto));
    }

    @ApiOperation("整单明细开票")
    @PostMapping("supplierAllDetailInvoice")
    public ServiceResult<String> allDetailInvoice(@RequestBody @Validated SupplierInvoiceApplyDto supplierInvoiceApplyDto) {
        return ServiceResult.succ(supplierInvoiceBillService.allDetailInvoice(supplierInvoiceApplyDto));
    }

    @ApiOperation("勾选明细开票")
    @PostMapping("supplierInvoiceByDetailId")
    public ServiceResult<String> supplierInvoiceByDetailIds(@RequestBody @Validated SupplierInvoiceByDetailIdDto supplierInvoiceByDetailIdDto) {
        return ServiceResult.succ(supplierInvoiceBillService.supplierInvoiceByDetailIds(supplierInvoiceByDetailIdDto));
    }

    @ApiOperation("获取供应商上传电子发票的信息")
    @PostMapping("getSupplierInvoiceInfo")
    public ServiceResult<?> batchUploadInvoice(@RequestParam("file") MultipartFile file) throws IOException {
        SupplierInvoicePdfDto supplierInvoicePdfDto = PdfboxUtils.parseInvoice(file);
        return ServiceResult.succ(supplierInvoicePdfDto);
    }

    @ApiOperation("上传供应商发票")
    @PostMapping("supplierInvoiceUpload")
    public ServiceResult<?> supplierInvoiceUpload(
            @RequestParam("invoice") MultipartFile invoice,
            @RequestParam("inconsistent") MultipartFile inconsistent,
            @RequestParam("id") Long id,
            @RequestParam("toleranceAmount") BigDecimal toleranceAmount,
            @RequestParam("inconsistentRemark") String inconsistentRemark) throws IOException {
        return ServiceResult.succ(supplierInvoiceBillService.supplierInvoiceUpload(invoice,inconsistent,id,toleranceAmount,inconsistentRemark));
    }

    @ApiOperation("导出发票明细")
    @GetMapping("export")
    public void supplierInvoiceExport(HttpServletResponse response, Long id) throws IOException {
        supplierInvoiceBillService.supplierInvoiceExport(response, id);
    }

    @ApiOperation("发票删除")
    @PostMapping("invoiceDel")
    public ServiceResult<?> invoiceDel(@RequestBody Long id) {
        return ServiceResult.succ(supplierInvoiceBillService.invoiceDel(id));
    }

    @ApiOperation("供应商发票列表")
    @GetMapping("queryPageVo")
    public ServiceResult<PageResp<SupplierInvoiceBillVo>> queryPageVo(PageReq pageReq,
                                                                      SupplierInvoiceQueryVo supplierInvoiceQueryVo) {
        return ServiceResult.succ(supplierInvoiceBillService.queryPageVo(pageReq, supplierInvoiceQueryVo));
    }

    @ApiOperation("供应商发票审核")
    @PostMapping("approve")
    public ServiceResult<?> approve(@RequestBody @Validated SupplierInvoiceApproveDto supplierInvoiceApproveDto) {
        return ServiceResult.succ(supplierInvoiceBillService.approveInvoice(supplierInvoiceApproveDto));
    }


    @ApiOperation("获取供应商账单已结算的发票")
    @GetMapping("getInvoiceUrl")
    public ServiceResult<?> getInvoiceUrl(Long billId) {
        return ServiceResult.succ(supplierInvoiceBillService.getInvoiceUrl(billId));
    }


}
