# 设置 GitHub 仓库的脚本

$username = "zhangliang2198"
$reponame = "lylcyyph"

Write-Host "GitHub 仓库设置指南" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

Write-Host "1. 在浏览器中访问: https://github.com/new" -ForegroundColor Yellow
Write-Host "2. 填写仓库信息:" -ForegroundColor Yellow
Write-Host "   - Repository name: $reponame" -ForegroundColor Cyan
Write-Host "   - Description: 蓝云优品汇项目 - 私有备份仓库" -ForegroundColor Cyan
Write-Host "   - Visibility: Private (私有)" -ForegroundColor Cyan
Write-Host "   - 不要勾选任何初始化选项" -ForegroundColor Red
Write-Host "3. 点击 'Create repository'" -ForegroundColor Yellow

Write-Host ""
Write-Host "当前远程仓库配置:" -ForegroundColor Green
git remote -v

Write-Host ""
Write-Host "创建仓库后，运行以下命令测试连接:" -ForegroundColor Green
Write-Host "git push github feature/master_for_job" -ForegroundColor Cyan

Write-Host ""
Write-Host "或者使用便捷脚本:" -ForegroundColor Green
Write-Host ".\scripts\push-to-both.ps1" -ForegroundColor Cyan
