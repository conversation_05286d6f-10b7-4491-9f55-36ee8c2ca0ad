package com.ly.yph.core.data.delayprocess;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ly.yph.core.base.exception.ErrorCodeConstants;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 延迟过程
 *
 * <AUTHOR>
 * @date 2022/12/12
 */
@Component
@Slf4j
@Data
public class DelayProcess {
    private static final long SCHEDULER_PERIOD = 60;
    @Resource
    private ThreadPoolExecutor computeThread;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    @Resource
    private RedissonClient redissonClient;
    private ReentrantLock lock = new ReentrantLock();
    private final static Map<String, DelayProcessItem> _holder = new HashMap<>(16);

    public void addProcess(DelayProcessItem item) {
        _holder.put(item.getName(), item);
    }

    public void pushProcessInfo(String name, String infoJsonStr) {
        DelayProcessItem delayProcessItem = _holder.get(name);
        if (delayProcessItem == null) {
            throw HttpException.exception(ErrorCodeConstants.NOT_CONTAINER, name);
        }
        var tId = TenantContextHolder.getRequiredTenantId();
        final RSet<String> oriList = this.redissonClient.getSet(delayProcessItem.getRedisName());
        oriList.tryAdd(tId + "_" + infoJsonStr);
        // log.info("update" + name + ":" + tId + "_" + infoJsonStr);
    }

    @Scheduled(fixedDelay = SCHEDULER_PERIOD, timeUnit = TimeUnit.SECONDS)
    public void schedulePeriodicRefresh() {
        commonIoExecutors.execute(() -> _holder.forEach(this::_doExecutor));
    }

    @DistributedLock(value = "delay_exec_lock", key = "#hk", leaseTime = 600, waitLock = false)
    private void _doExecutor(String hk, DelayProcessItem hv) {
        final Set<String> od = _getList(hk);
        if (od.isEmpty()) {
            return;
        }
        Map<String, List<String>> tids = od.stream().collect(Collectors.groupingBy(item -> StrUtil.split(item, "_").get(0)));

        // bean 需要继承自 IDelayProcessor
        IDelayProcessor processor = SpringUtil.getBean(hv.getBeanName());
        tids.forEach((item, ld) -> {
            TenantUtils.execute(Long.valueOf(item), () -> {
                List<String> tData = CollectionUtils.convertList(ld, i -> StrUtil.split(i, "_").get(1));
                if (hv.getBatchSize() <= 0) {
                    try {
                        processor.processData(tData);
                    } catch (Exception e) {
                        // log.error("delay process data error:{}", ExceptionUtil.stacktraceToString(e));
                    }
                } else {
                    List<List<String>> split = CollectionUtil.split(tData, hv.getBatchSize());
                    try {
                        split.forEach(processor::processData);
                    } catch (Exception e) {
                        // log.error("delay process data error:{}", ExceptionUtil.stacktraceToString(e));
                    }
                }
            });
        });
    }

    private Set<String> _getList(String name) {
        DelayProcessItem delayProcessItem = _holder.get(name);
        if (delayProcessItem == null) {
            throw HttpException.exception(ErrorCodeConstants.NOT_CONTAINER, name);
        }

        final RSet<String> l = this.redissonClient.getSet(delayProcessItem.getRedisName());
        if (l == null || l.isEmpty()) {
            return new HashSet<>();
        }
        final Set<String> d = l.removeRandom(delayProcessItem.getDefaultCountOneTime());
        // log.info("处理" + name + "数据，共:{}条", d.size());

        if (d.isEmpty()) {
            return new HashSet<>();
        }
        return d;
    }
}
