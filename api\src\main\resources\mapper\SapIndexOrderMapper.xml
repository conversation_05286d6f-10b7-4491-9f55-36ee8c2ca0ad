<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ly.yph.api.customization.mapper.SapIndexOrderMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ly.yph.api.customization.entity.SapIndexOrderEntity" id="sapIndexOrderMap">
        <result property="sioId" column="sio_id"/>
        <result property="indexOrderSn" column="index_order_sn"/>
        <result property="logicalSystem" column="logical_system"/>
        <result property="sapCompanyCode" column="sap_company_code"/>
        <result property="werksCode" column="werks_code"/>
        <result property="businessScope" column="business_scope"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="indexPrice" column="index_price"/>
        <result property="prepaymentFee" column="prepayment_fee"/>
        <result property="invoiceAmount" column="invoice_amount"/>
        <result property="invoiceTotalPrice" column="invoice_total_price"/>
        <result property="invoiceNum" column="invoice_num"/>
        <result property="invoicePeople" column="invoice_people"/>
        <result property="invoiceMobile" column="invoice_mobile"/>
        <result property="amountUnit" column="amount_unit"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="indexState" column="index_state"/>
    </resultMap>

    <!--VOMap 可根据自己的需求，是否要使用 -->
    <resultMap type="com.ly.yph.api.customization.vo.SapIndexOrderVo" id="sapIndexOrderPageVoMap">
        <result property="sioId" column="sio_id"/>
        <result property="indexOrderSn" column="index_order_sn"/>
        <result property="logicalSystem" column="logical_system"/>
        <result property="sapCompanyCode" column="sap_company_code"/>
        <result property="werksCode" column="werks_code"/>
        <result property="businessScope" column="business_scope"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="indexPrice" column="index_price"/>
        <result property="prepaymentFee" column="prepayment_fee"/>
        <result property="invoiceAmount" column="invoice_amount"/>
        <result property="invoiceTotalPrice" column="invoice_total_price"/>
        <result property="invoiceNum" column="invoice_num"/>
        <result property="invoicePeople" column="invoice_people"/>
        <result property="invoiceMobile" column="invoice_mobile"/>
        <result property="invoiceApplyNumber" column="invoice_apply_number"/>
        <result property="amountUnit" column="amount_unit"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="indexState" column="index_state"/>
    </resultMap>

    <!-- VOMap 可根据自己的需求，是否要使用  -->
    <resultMap type="com.ly.yph.api.customization.vo.SapDeliveryVo" id="sapDeliveryPageVoMap">
        <result property="sdId" column="sd_id"/>
        <result property="idoctye" column="idoctye"/>
        <result property="sndprn" column="sndprn"/>
        <result property="mestyp" column="mestyp"/>
        <result property="docnum" column="docnum"/>
        <result property="mblnr" column="mblnr"/>
        <result property="orderSn" column="order_sn"/>
        <result property="mjahr" column="mjahr"/>
        <result property="budat" column="budat"/>
        <result property="bldat" column="bldat"/>
        <result property="cpudt" column="cpudt"/>
        <result property="cputm" column="cputm"/>
        <result property="usnam" column="usnam"/>
        <result property="bktxt" column="bktxt"/>
        <result property="aedat" column="aedat"/>
        <result property="xblnr" column="xblnr"/>
        <result property="abstamp" column="abstamp"/>
        <result property="sourceSysId" column="source_sys_id"/>
        <result property="acceptanceCertificate" column="acceptance_certificate"/>
        <result property="acceptanceCertificateItemNo" column="acceptance_certificate_item_no"/>
        <result property="werksCode" column="werks_code"/>
        <result property="companyCode" column="company_code"/>
        <result property="matchState" column="match_state"/>
        <result property="submitState" column="submit_state"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <collection property="sapDeliveryDetailVos" ofType="com.ly.yph.api.customization.vo.SapDeliveryDetailVo"
                    column="mblnr"
                    select="com.ly.yph.api.customization.mapper.DfgSapDeliveryDetailMapper.getSapDeliveryDetailByMblnr">
        </collection>
    </resultMap>

    <insert id="saveSapIndexOrder" parameterType="com.ly.yph.api.customization.entity.SapIndexOrderEntity">
        insert into sap_index_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sioId != null  and  sioId !=''">
                sio_id,
            </if>
            <if test="indexOrderSn != null  and  indexOrderSn !=''">
                index_order_sn,
            </if>
            <if test="logicalSystem != null  and  logicalSystem !=''">
                logical_system,
            </if>
            <if test="sapCompanyCode != null  and  sapCompanyCode !=''">
                sap_company_code,
            </if>
            <if test="businessScope != null  and  businessScope !=''">
                business_scope,
            </if>
            <if test="supplierCode != null  and  supplierCode !=''">
                supplier_code,
            </if>
            <if test="supplierName != null  and  supplierName !=''">
                supplier_name,
            </if>
            <if test="indexPrice != null ">
                index_price,
            </if>
            <if test="prepaymentFee != null ">
                prepayment_fee,
            </if>
            <if test="invoiceAmount != null ">
                invoice_amount,
            </if>
            <if test="invoiceTotalPrice != null ">
                invoice_total_price,
            </if>
            <if test="invoiceNum != null ">
                invoice_num,
            </if>
            <if test="invoicePeople != null  and  invoicePeople !=''">
                invoice_people,
            </if>
            <if test="invoiceMobile != null  and  invoiceMobile !=''">
                invoice_mobile,
            </if>
            <if test="amountUnit != null  and  amountUnit !=''">
                amount_unit,
            </if>
            <if test="remark != null  and  remark !=''">
                remark,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="indexState != null ">
                index_state,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sioId != null  and  sioId !=''">
                #{sioId},
            </if>
            <if test="indexOrderSn != null  and  indexOrderSn !=''">
                #{indexOrderSn},
            </if>
            <if test="logicalSystem != null  and  logicalSystem !=''">
                #{logicalSystem},
            </if>
            <if test="sapCompanyCode != null  and  sapCompanyCode !=''">
                #{sapCompanyCode},
            </if>
            <if test="businessScope != null  and  businessScope !=''">
                #{businessScope},
            </if>
            <if test="supplierCode != null  and  supplierCode !=''">
                #{supplierCode},
            </if>
            <if test="supplierName != null  and  supplierName !=''">
                #{supplierName},
            </if>
            <if test="indexPrice != null ">
                #{indexPrice},
            </if>
            <if test="prepaymentFee != null ">
                #{prepaymentFee},
            </if>
            <if test="invoiceAmount != null ">
                #{invoiceAmount},
            </if>
            <if test="invoiceTotalPrice != null ">
                #{invoiceTotalPrice},
            </if>
            <if test="invoiceNum != null ">
                #{invoiceNum},
            </if>
            <if test="invoicePeople != null  and  invoicePeople !=''">
                #{invoicePeople},
            </if>
            <if test="invoiceMobile != null  and  invoiceMobile !=''">
                #{invoiceMobile},
            </if>
            <if test="amountUnit != null  and  amountUnit !=''">
                #{amountUnit},
            </if>
            <if test="remark != null  and  remark !=''">
                remark,
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="indexState != null ">
                #{indexState},
            </if>
        </trim>
    </insert>

    <update id="updateSapIndexOrder" parameterType="com.ly.yph.api.customization.entity.SapIndexOrderEntity">
        update sap_index_order
        <set>
            <if test="indexOrderSn != null  and  indexOrderSn !=''">
                index_order_sn = #{indexOrderSn},
            </if>
            <if test="logicalSystem != null  and  logicalSystem !=''">
                logical_system = #{logicalSystem},
            </if>
            <if test="sapCompanyCode != null  and  sapCompanyCode !=''">
                sap_company_code = #{sapCompanyCode},
            </if>
            <if test="businessScope != null  and  businessScope !=''">
                business_scope = #{businessScope},
            </if>
            <if test="supplierCode != null  and  supplierCode !=''">
                supplier_code = #{supplierCode},
            </if>
            <if test="supplierName != null  and  supplierName !=''">
                supplier_name = #{supplierName},
            </if>
            <if test="indexPrice != null ">
                index_price = #{indexPrice},
            </if>
            <if test="prepaymentFee != null ">
                prepayment_fee = #{prepaymentFee},
            </if>
            <if test="invoiceAmount != null ">
                invoice_amount = #{invoiceAmount},
            </if>
            <if test="invoiceTotalPrice != null ">
                invoice_total_price = #{invoiceTotalPrice},
            </if>
            <if test="invoiceNum != null ">
                invoice_num = #{invoiceNum},
            </if>
            <if test="invoicePeople != null  and  invoicePeople !=''">
                invoice_people = #{invoicePeople},
            </if>
            <if test="invoiceMobile != null  and  invoiceMobile !=''">
                invoice_mobile = #{invoiceMobile},
            </if>
            <if test="amountUnit != null  and  amountUnit !=''">
                amount_unit = #{amountUnit},
            </if>
            <if test="remark != null  and  remark !=''">
                remark = #{remark},
            </if>
            <if test="createTime != null ">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
            <if test="indexState != null ">
                index_state = #{indexState},
            </if>
        </set>
        where sio_id = #{sioId}
    </update>

    <!-- 模糊条件查询分页 -->
    <select id="queryPageVo" resultMap="sapIndexOrderPageVoMap">
        select * from sap_index_order
        <!-- 自己去组装where条件 -->
        <where>
            <if test="query.werksCode != null and  query.werksCode != '' ">
                and werks_code = #{query.werksCode}
            </if>
            <if test="query.indexOrderSn != null and  query.indexOrderSn != '' ">
                and index_order_sn = #{query.indexOrderSn}
            </if>
            <if test="query.indexState != null and  query.indexState != '' or query.indexState ==0  ">
                and index_state = #{query.indexState}
            </if>
            <if test="query.beginTime != null and  query.beginTime != '' ">
                and create_time &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != '' ">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.invoiceApplyNumber != null and  query.invoiceApplyNumber != '' ">
                and invoice_apply_number = #{query.invoiceApplyNumber}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="exportIndexOrderList" resultType="com.ly.yph.api.customization.dto.SapIndexOrderDto">
        select a.*,b.invoice_number from sap_index_order a left join invoice_bill b on a.invoice_apply_number = b.invoice_apply_number
        <!-- 自己去组装where条件 -->
        <where>
            <if test="query.werksCode != null and  query.werksCode != '' ">
                and a.werks_code = #{query.werksCode}
            </if>
            <if test="query.indexOrderSn != null and  query.indexOrderSn != '' ">
                and a.index_order_sn = #{query.indexOrderSn}
            </if>
            <if test="query.indexState != null and  query.indexState != '' or query.indexState ==0  ">
                and a.index_state = #{query.indexState}
            </if>
            <if test="query.beginTime != null and  query.beginTime != '' ">
                and a.create_time &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != '' ">
                and a.create_time &lt;= #{query.endTime}
            </if>
            <if test="query.invoiceApplyNumber != null and  query.invoiceApplyNumber != '' ">
                and a.invoice_apply_number = #{query.invoiceApplyNumber}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="querySapIndexDetalExport" resultMap="sapDeliveryPageVoMap">
        select * from sap_delivery sd left join shop_purchase_order spo on sd.order_sn = spo.purchase_number
        <where>
            sd.order_sn is not null
            <if test="query.werksCode != null and  query.werksCode != '' ">
                and sd.werks_code = #{query.werksCode}
            </if>
            <if test="query.orderSn != null and  query.orderSn != '' ">
                and sd.order_sn = #{query.orderSn}
            </if>
            <if test="query.cgpz != null and query.cgpz != '' ">
                and sd.mblnr = #{query.cgpz}
            </if>
            <if test="query.beginTime != null and  query.beginTime != '' ">
                and sd.cpudt &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != '' ">
                and sd.cpudt &lt;= #{query.endTime}
            </if>
            <if test="query.orderType != null">
                and sd.order_type = #{query.orderType}
            </if>
            <if test="query.submitState != null and query.submitState.size() != 0">
                and sd.submit_state in
                <foreach collection="query.submitState" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.matchState != null  and query.matchState.size() != 0">
                and sd.match_state in
                <foreach collection="query.matchState" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.applyNumber != null and query.applyNumber != '' ">
                and spo.budget_apply_code = #{query.applyNumber}
            </if>
            <if test="query.companyCode != null and query.companyCode != '' ">
                and sd.company_code = #{query.companyCode}
            </if>
        </where>
    </select>
    <select id="waitIndexList" resultMap="sapDeliveryPageVoMap">
        select sd.* from sap_delivery sd left join shop_purchase_order spo on sd.order_sn = spo.purchase_number
        <where>
            <if test="query.werksCode != null and  query.werksCode != '' ">
                and sd.werks_code = #{query.werksCode}
            </if>
            <if test="query.orderSn != null and  query.orderSn != '' ">
                and sd.order_sn = #{query.orderSn}
            </if>
            <if test="query.cgpz != null and query.cgpz != '' ">
                and sd.mblnr = #{query.cgpz}
            </if>
            <if test="query.beginTime != null and  query.beginTime != '' ">
                and sd.cpudt &gt;= #{query.beginTime}
            </if>
            <if test="query.endTime != null and query.endTime != '' ">
                and sd.cpudt &lt;= #{query.endTime}
            </if>
            <if test="query.orderType != null">
                and sd.order_type = #{query.orderType}
            </if>
            <if test="query.submitState != null and query.submitState.size() != 0">
                and sd.submit_state in
                <foreach collection="query.submitState" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.matchState != null  and query.matchState.size() != 0">
                and sd.match_state in
                <foreach collection="query.matchState" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.applyNumber != null and query.applyNumber != '' ">
                and spo.budget_apply_code = #{query.applyNumber}
            </if>
            <if test="query.companyCode != null and query.companyCode != '' ">
                and sd.company_code = #{query.companyCode}
            </if>
            <if test="query.invoiceId != null and query.invoiceId != '' ">
                and spo.invoice_id = #{query.invoiceId}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getIndexOrderSnRank" resultType="java.lang.String">
        select index_order_sn from sap_index_order where index_order_sn like concat(#{indexOrderSn},"%");
    </select>


</mapper>