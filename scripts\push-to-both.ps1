# 推送到两个远程仓库的脚本
param(
    [string]$branch = "feature/master_for_job",
    [string]$message = "同步代码更新"
)

Write-Host "开始推送到双远程仓库..." -ForegroundColor Green

# 检查当前分支
$currentBranch = git branch --show-current
Write-Host "当前分支: $currentBranch" -ForegroundColor Yellow

# 如果有未提交的更改，先提交
$status = git status --porcelain
if ($status) {
    Write-Host "发现未提交的更改，正在提交..." -ForegroundColor Yellow
    git add .
    git commit -m $message
}

# 推送到 GitLab (原始仓库)
Write-Host "推送到 GitLab..." -ForegroundColor Cyan
try {
    git push gitlab $branch
    Write-Host "✓ GitLab 推送成功" -ForegroundColor Green
} catch {
    Write-Host "✗ GitLab 推送失败: $_" -ForegroundColor Red
}

# 推送到 GitHub (备份仓库)
Write-Host "推送到 GitHub..." -ForegroundColor Cyan
try {
    git push github $branch
    Write-Host "✓ GitHub 推送成功" -ForegroundColor Green
} catch {
    Write-Host "✗ GitHub 推送失败: $_" -ForegroundColor Red
    Write-Host "请确保 GitHub 仓库已创建并且有推送权限" -ForegroundColor Yellow
}

Write-Host "推送完成!" -ForegroundColor Green
