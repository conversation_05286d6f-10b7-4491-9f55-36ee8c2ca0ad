package com.ly.yph.api.settlement.common.mapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.yph.api.settlement.common.dto.bill.BillAfterSaleDto;
import com.ly.yph.api.settlement.common.dto.bill.SupplierSettlementDto;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.vo.bill.ReconciliationBillReqVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillExcelVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillPageReqVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillVo;
import com.ly.yph.api.settlement.supplier.dto.SupplierContractInfoDto;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.tenant.core.aop.TenantIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface SettleShopBillMapper extends BaseMapperX<SettleShopBill> {

    /**
     * 分页查询账单列表
     *
     * @param adapterPageReq
     * @param reqVo
     * @return
     */
    IPage<SettleShopBillVo> querySettleShopBillPage(Page<SettleShopBillVo> adapterPageReq, @Param("query") SettleShopBillPageReqVo reqVo);

    /**
     * 条件查询
     *
     * @param reqVo
     * @return
     */
    List<SettleShopBillVo> querySettleShopBill(@Param("query") SettleShopBillPageReqVo reqVo);

    SettleShopBillExcelVo getBillExcelById(@Param("billId") Long billId);

    /**
     * 计算账单金额
     *
     * @param billId
     * @return
     */
    Map<String, BigDecimal> getSumBill(@Param("billId") Long billId);

    /**
     * 更新对账单状态
     *
     * @param reconciliationBillReqVo
     */
    void updateSettleShopBill(ReconciliationBillReqVo reconciliationBillReqVo);

    void rollBackBill();

    BillAfterSaleDto getBillAfterDto(@Param("poolId") Long poolId, @Param("customerType") Integer customerType);

    List<SupplierSettlementDto> getSupplierSettlementList(@Param("billId") Long billId);


    default SettleShopBill selectByBillSn(String billSn) {
        LambdaQueryWrapper<SettleShopBill> myQuery = Wrappers.lambdaQuery(SettleShopBill.class);
        myQuery.eq(SettleShopBill::getBillSn, billSn);
        return selectOne(myQuery);
    }

    @TenantIgnore
    ShopSupplier getSupplierBillDate(String supplierCode);

    @TenantIgnore
    List<SupplierContractInfoDto> getSupplierContract(String supplierCode);

    Map<String, BigDecimal> getSupplierReconciliationSum(String billId);

    Map<String, BigDecimal> getSupplierReconciliationSumForPostage(String billId);
}
