package com.ly.yph.api.settlement.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("账单明细表")
@TableName("settle_shop_bill_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettleShopBillDetail extends BaseEntity {

    private static final long serialVersionUID = -8318349372334796689L;

    @TableId(type = IdType.AUTO, value = "detail_id")
    private Long detailId;

    @ApiModelProperty("账单关联主键")
    private Long billId;

    @ApiModelProperty("账单编号")
    private String billSn;

    @ApiModelProperty("账单池明细id[友福利客户关联不上]")
    private Long settleBillPoolId;

    @ApiModelProperty("出账所在年")
    private Integer checkYear;

    @ApiModelProperty("出账所在月")
    private Integer checkMonth;

    @ApiModelProperty("采购单号")
    private String purchaseNumber;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("电商订单号")
    private String supplierOrderNumber;

    @ApiModelProperty("开票主体")
    private String invoiceSubject;

    @ApiModelProperty("下单人id")
    private Long applyUserId;

    @ApiModelProperty("下单人姓名")
    private String applyUserName;

    @ApiModelProperty("下单人部门id")
    private Long applyDeptId;

    @ApiModelProperty("下单人部门名称")
    private String applyDeptName;

    @ApiModelProperty("企业/供应商来源编码")
    private String storeCode;

    @ApiModelProperty("企业/供应商来源名称")
    private String storeName;
    /**
     * 客户账单中，电商分南方独立和平台供应商，南方独立供应商：srm;平台供应商：dfmall
     */
    @ApiModelProperty("企业/供应商来源")
    private String storeDataSource;

    @ApiModelProperty("对账人id")
    private Long reconciliationUserId;

    @ApiModelProperty("对账人姓名")
    private String reconciliationUserName;

    @ApiModelProperty("对账状态0.已出帐，1.待确认，2：已确认 3.已驳回 4.待开票，5.开票驳回，6.已开票，7.部分开票")
    private Integer reconciliationStatus;

    @ApiModelProperty("对账确认人id")
    private Long reconciliationConfirmUserId;

    @ApiModelProperty("对账确认人姓名")
    private String reconciliationConfirmUserName;

    @ApiModelProperty("对账确认时间")
    private Date reconciliationConfirmTime;

    @ApiModelProperty("商城编码")
    private String goodsCode;

    @ApiModelProperty("电商sku")
    private String goodsSku;

    @ApiModelProperty("订单数量")
    private BigDecimal confirmNum;

    @ApiModelProperty("出账数量")
    private BigDecimal checkedNum;

    @ApiModelProperty("可开票数量")
    private BigDecimal invoicableQuantity;

    @ApiModelProperty("已开票数量")
    private BigDecimal invoicedQuantity;

    @ApiModelProperty("未税单价")
    private BigDecimal unitPriceNaked;

    @ApiModelProperty("含税单价")
    private BigDecimal unitPriceTax;

    @ApiModelProperty("未税总价")
    private BigDecimal totalPriceNaked;

    @ApiModelProperty("含税总价")
    private BigDecimal totalPriceTax;

    @ApiModelProperty("对账备注")
    private String reconciliationRemark;
    /**
     * 商城类型 用于电商账单 友福利电商账单和东风商城电商账单在一起
     * 1：东风商城账单明细
     * 2：友福利账单明细
     */
    @ApiModelProperty("商城类型 默认1：东风商城,2:友福利的电商明细")
    private Integer mallType;
    /**
     * 东方商城电商&客户账单明细是1，
     * 友福利客户是2
     */
    @ApiModelProperty("池类型，1：东风商城,2:友福利")
    private Integer poolType;

    @ApiModelProperty("合同号")
    private String contractNumber;

    private Integer priceMode;

    @ApiModelProperty("账单类型 0：正数账单，1：负向账单")
    private Integer billDetailType;

    @ApiModelProperty("SAP匹配原因")
    private String  billMatchReason;

    @ApiModelProperty("账单暂存标记 0:未暂存 1:已暂存")
    private Integer billStagingFlag;

    @TableField(exist = false)
    private String orderDetailId;

}
