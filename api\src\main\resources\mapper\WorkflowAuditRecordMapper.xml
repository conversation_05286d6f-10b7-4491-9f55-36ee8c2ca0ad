<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.workflow.mapper.WorkflowAuditRecordMapper">
  <resultMap id="BaseResultMap" type="com.ly.yph.api.workflow.entity.WorkflowAuditRecord">
    <!--@mbg.generated-->
    <!--@Table workflow_audit_record-->
    <id column="id" property="id" />
    <result column="workflow_name" property="workflowName" />
    <result column="workflow_description" property="workflowDescription" />
    <result column="step_name" property="stepName" />
    <result column="bus_id" property="busId" />
    <result column="bus_code" property="busCode" />
    <result column="bus_code_x" property="busCodeX" />
    <result column="step_description" property="stepDescription" />
    <result column="workflow_id" property="workflowId" />
    <result column="step_id" property="stepId" />
    <result column="user_name" property="userName" />
    <result column="context" property="context" />
    <result column="status" property="status" />
    <result column="comment" property="comment" />
    <result column="created_at" property="createdAt" />
    <result column="updated_at" property="updatedAt" />
    <result column="tenant_id" property="tenantId" />
    <result column="supplier_code" property="supplierCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, workflow_name, workflow_description, step_name, bus_id, bus_code, bus_code_x, 
    step_description, workflow_id, step_id, user_name, context, `status`, `comment`, 
    created_at, updated_at, tenant_id, supplier_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from workflow_audit_record
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from workflow_audit_record
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.WorkflowAuditRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into workflow_audit_record (workflow_name, workflow_description, step_name, bus_id, bus_code, 
      bus_code_x, step_description, workflow_id, step_id, user_name, context, 
      `status`, `comment`, created_at, updated_at, tenant_id, supplier_code
      )
    values (#{workflowName}, #{workflowDescription}, #{stepName}, #{busId}, #{busCode}, 
      #{busCodeX}, #{stepDescription}, #{workflowId}, #{stepId}, #{userName}, #{context}, 
      #{status}, #{comment}, #{createdAt}, #{updatedAt}, #{tenantId}, #{supplierCode}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.WorkflowAuditRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into workflow_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="workflowName != null">
        workflow_name,
      </if>
      <if test="workflowDescription != null">
        workflow_description,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="busId != null">
        bus_id,
      </if>
      <if test="busCode != null">
        bus_code,
      </if>
      <if test="busCodeX != null">
        bus_code_x,
      </if>
      <if test="stepDescription != null">
        step_description,
      </if>
      <if test="workflowId != null">
        workflow_id,
      </if>
      <if test="stepId != null">
        step_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="context != null">
        context,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="workflowName != null">
        #{workflowName},
      </if>
      <if test="workflowDescription != null">
        #{workflowDescription},
      </if>
      <if test="stepName != null">
        #{stepName},
      </if>
      <if test="busId != null">
        #{busId},
      </if>
      <if test="busCode != null">
        #{busCode},
      </if>
      <if test="busCodeX != null">
        #{busCodeX},
      </if>
      <if test="stepDescription != null">
        #{stepDescription},
      </if>
      <if test="workflowId != null">
        #{workflowId},
      </if>
      <if test="stepId != null">
        #{stepId},
      </if>
      <if test="userName != null">
        #{userName},
      </if>
      <if test="context != null">
        #{context},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="comment != null">
        #{comment},
      </if>
      <if test="createdAt != null">
        #{createdAt},
      </if>
      <if test="updatedAt != null">
        #{updatedAt},
      </if>
      <if test="tenantId != null">
        #{tenantId},
      </if>
      <if test="supplierCode != null">
        #{supplierCode},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ly.yph.api.workflow.entity.WorkflowAuditRecord">
    <!--@mbg.generated-->
    update workflow_audit_record
    <set>
      <if test="workflowName != null">
        workflow_name = #{workflowName},
      </if>
      <if test="workflowDescription != null">
        workflow_description = #{workflowDescription},
      </if>
      <if test="stepName != null">
        step_name = #{stepName},
      </if>
      <if test="busId != null">
        bus_id = #{busId},
      </if>
      <if test="busCode != null">
        bus_code = #{busCode},
      </if>
      <if test="busCodeX != null">
        bus_code_x = #{busCodeX},
      </if>
      <if test="stepDescription != null">
        step_description = #{stepDescription},
      </if>
      <if test="workflowId != null">
        workflow_id = #{workflowId},
      </if>
      <if test="stepId != null">
        step_id = #{stepId},
      </if>
      <if test="userName != null">
        user_name = #{userName},
      </if>
      <if test="context != null">
        context = #{context},
      </if>
      <if test="status != null">
        `status` = #{status},
      </if>
      <if test="comment != null">
        `comment` = #{comment},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ly.yph.api.workflow.entity.WorkflowAuditRecord">
    <!--@mbg.generated-->
    update workflow_audit_record
    set workflow_name = #{workflowName},
      workflow_description = #{workflowDescription},
      step_name = #{stepName},
      bus_id = #{busId},
      bus_code = #{busCode},
      bus_code_x = #{busCodeX},
      step_description = #{stepDescription},
      workflow_id = #{workflowId},
      step_id = #{stepId},
      user_name = #{userName},
      context = #{context},
      `status` = #{status},
      `comment` = #{comment},
      created_at = #{createdAt},
      updated_at = #{updatedAt},
      tenant_id = #{tenantId},
      supplier_code = #{supplierCode}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update workflow_audit_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="workflow_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.workflowName}
        </foreach>
      </trim>
      <trim prefix="workflow_description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.workflowDescription}
        </foreach>
      </trim>
      <trim prefix="step_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.stepName}
        </foreach>
      </trim>
      <trim prefix="bus_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.busId}
        </foreach>
      </trim>
      <trim prefix="bus_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.busCode}
        </foreach>
      </trim>
      <trim prefix="bus_code_x = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.busCodeX}
        </foreach>
      </trim>
      <trim prefix="step_description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.stepDescription}
        </foreach>
      </trim>
      <trim prefix="workflow_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.workflowId}
        </foreach>
      </trim>
      <trim prefix="step_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.stepId}
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userName}
        </foreach>
      </trim>
      <trim prefix="context = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.context}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.status}
        </foreach>
      </trim>
      <trim prefix="`comment` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.comment}
        </foreach>
      </trim>
      <trim prefix="created_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createdAt}
        </foreach>
      </trim>
      <trim prefix="updated_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updatedAt}
        </foreach>
      </trim>
      <trim prefix="tenant_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.tenantId}
        </foreach>
      </trim>
      <trim prefix="supplier_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.supplierCode}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update workflow_audit_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="workflow_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.workflowName != null">
            when id = #{item.id} then #{item.workflowName}
          </if>
        </foreach>
      </trim>
      <trim prefix="workflow_description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.workflowDescription != null">
            when id = #{item.id} then #{item.workflowDescription}
          </if>
        </foreach>
      </trim>
      <trim prefix="step_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stepName != null">
            when id = #{item.id} then #{item.stepName}
          </if>
        </foreach>
      </trim>
      <trim prefix="bus_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busId != null">
            when id = #{item.id} then #{item.busId}
          </if>
        </foreach>
      </trim>
      <trim prefix="bus_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busCode != null">
            when id = #{item.id} then #{item.busCode}
          </if>
        </foreach>
      </trim>
      <trim prefix="bus_code_x = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busCodeX != null">
            when id = #{item.id} then #{item.busCodeX}
          </if>
        </foreach>
      </trim>
      <trim prefix="step_description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stepDescription != null">
            when id = #{item.id} then #{item.stepDescription}
          </if>
        </foreach>
      </trim>
      <trim prefix="workflow_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.workflowId != null">
            when id = #{item.id} then #{item.workflowId}
          </if>
        </foreach>
      </trim>
      <trim prefix="step_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stepId != null">
            when id = #{item.id} then #{item.stepId}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userName != null">
            when id = #{item.id} then #{item.userName}
          </if>
        </foreach>
      </trim>
      <trim prefix="context = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.context != null">
            when id = #{item.id} then #{item.context}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id} then #{item.status}
          </if>
        </foreach>
      </trim>
      <trim prefix="`comment` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.comment != null">
            when id = #{item.id} then #{item.comment}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdAt != null">
            when id = #{item.id} then #{item.createdAt}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedAt != null">
            when id = #{item.id} then #{item.updatedAt}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenant_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantId != null">
            when id = #{item.id} then #{item.tenantId}
          </if>
        </foreach>
      </trim>
      <trim prefix="supplier_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplierCode != null">
            when id = #{item.id} then #{item.supplierCode}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into workflow_audit_record
    (workflow_name, workflow_description, step_name, bus_id, bus_code, bus_code_x, step_description, 
      workflow_id, step_id, user_name, context, `status`, `comment`, created_at, updated_at, 
      tenant_id, supplier_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.workflowName}, #{item.workflowDescription}, #{item.stepName}, #{item.busId}, 
        #{item.busCode}, #{item.busCodeX}, #{item.stepDescription}, #{item.workflowId}, 
        #{item.stepId}, #{item.userName}, #{item.context}, #{item.status}, #{item.comment}, 
        #{item.createdAt}, #{item.updatedAt}, #{item.tenantId}, #{item.supplierCode})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.WorkflowAuditRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into workflow_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      workflow_name,
      workflow_description,
      step_name,
      bus_id,
      bus_code,
      bus_code_x,
      step_description,
      workflow_id,
      step_id,
      user_name,
      context,
      `status`,
      `comment`,
      created_at,
      updated_at,
      tenant_id,
      supplier_code,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      #{workflowName},
      #{workflowDescription},
      #{stepName},
      #{busId},
      #{busCode},
      #{busCodeX},
      #{stepDescription},
      #{workflowId},
      #{stepId},
      #{userName},
      #{context},
      #{status},
      #{comment},
      #{createdAt},
      #{updatedAt},
      #{tenantId},
      #{supplierCode},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      workflow_name = #{workflowName},
      workflow_description = #{workflowDescription},
      step_name = #{stepName},
      bus_id = #{busId},
      bus_code = #{busCode},
      bus_code_x = #{busCodeX},
      step_description = #{stepDescription},
      workflow_id = #{workflowId},
      step_id = #{stepId},
      user_name = #{userName},
      context = #{context},
      `status` = #{status},
      `comment` = #{comment},
      created_at = #{createdAt},
      updated_at = #{updatedAt},
      tenant_id = #{tenantId},
      supplier_code = #{supplierCode},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.WorkflowAuditRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into workflow_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="workflowName != null">
        workflow_name,
      </if>
      <if test="workflowDescription != null">
        workflow_description,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="busId != null">
        bus_id,
      </if>
      <if test="busCode != null">
        bus_code,
      </if>
      <if test="busCodeX != null">
        bus_code_x,
      </if>
      <if test="stepDescription != null">
        step_description,
      </if>
      <if test="workflowId != null">
        workflow_id,
      </if>
      <if test="stepId != null">
        step_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="context != null">
        context,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="workflowName != null">
        #{workflowName},
      </if>
      <if test="workflowDescription != null">
        #{workflowDescription},
      </if>
      <if test="stepName != null">
        #{stepName},
      </if>
      <if test="busId != null">
        #{busId},
      </if>
      <if test="busCode != null">
        #{busCode},
      </if>
      <if test="busCodeX != null">
        #{busCodeX},
      </if>
      <if test="stepDescription != null">
        #{stepDescription},
      </if>
      <if test="workflowId != null">
        #{workflowId},
      </if>
      <if test="stepId != null">
        #{stepId},
      </if>
      <if test="userName != null">
        #{userName},
      </if>
      <if test="context != null">
        #{context},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="comment != null">
        #{comment},
      </if>
      <if test="createdAt != null">
        #{createdAt},
      </if>
      <if test="updatedAt != null">
        #{updatedAt},
      </if>
      <if test="tenantId != null">
        #{tenantId},
      </if>
      <if test="supplierCode != null">
        #{supplierCode},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="workflowName != null">
        workflow_name = #{workflowName},
      </if>
      <if test="workflowDescription != null">
        workflow_description = #{workflowDescription},
      </if>
      <if test="stepName != null">
        step_name = #{stepName},
      </if>
      <if test="busId != null">
        bus_id = #{busId},
      </if>
      <if test="busCode != null">
        bus_code = #{busCode},
      </if>
      <if test="busCodeX != null">
        bus_code_x = #{busCodeX},
      </if>
      <if test="stepDescription != null">
        step_description = #{stepDescription},
      </if>
      <if test="workflowId != null">
        workflow_id = #{workflowId},
      </if>
      <if test="stepId != null">
        step_id = #{stepId},
      </if>
      <if test="userName != null">
        user_name = #{userName},
      </if>
      <if test="context != null">
        context = #{context},
      </if>
      <if test="status != null">
        `status` = #{status},
      </if>
      <if test="comment != null">
        `comment` = #{comment},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode},
      </if>
    </trim>
  </insert>

  <update id="updateStatusByCreatedAtBefore">
        update workflow_audit_record
        set `status`=#{updatedStatus}
        where created_at <![CDATA[<]]> #{maxCreatedAt}
    </update>

  <select id="selectByStatusAndCreatedAtBefore" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from workflow_audit_record
        where `status`=#{status} and created_at <![CDATA[<]]> #{maxCreatedAt}
    </select>

  <delete id="deleteByCreatedAtBefore">
        delete from workflow_audit_record
        where created_at <![CDATA[<]]> #{maxCreatedAt}
    </delete>

    <select id="selectAuditRecord" resultMap="BaseResultMap">
        select
        w.bus_id,
        r.created_at,
        r.updated_at,
        u.nickname as user_name,
        case
            when r.comment='即时自动开启流程' then '供应商发起'
        else step_name end as step_name,
        r.status ,
        r.comment
        from workflow_audit_record r
        left join workflow  w on r.workflow_id = w.id
        left join system_users u on r.user_name = u.username  COLLATE utf8mb4_unicode_ci
        where w.bus_id =${busId} order by r.created_at asc
    </select>
</mapper>