package com.ly.yph.api.customization.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.yph.api.customization.common.Constant;
import com.ly.yph.api.customization.dto.*;
import com.ly.yph.api.customization.entity.SapDeliveryEntity;
import com.ly.yph.api.customization.entity.SapIndexOrderEntity;
import com.ly.yph.api.customization.service.DfgDeliveryService;
import com.ly.yph.api.customization.service.SapIndexOrderService;
import com.ly.yph.api.customization.vo.SapDeliveryVo;
import com.ly.yph.api.customization.vo.SapIndexOrderVo;
import com.ly.yph.api.goods.service.YphStandardClassService;
import com.ly.yph.api.job.vo.log.JobLogExcelVO;
import com.ly.yph.api.settlement.common.service.ImportExtBillService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.web.ServletUtils;
import com.ly.yph.core.excel.util.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * sap索引单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 09:37:12
 */
@Api(value = "sap索引单API", tags = { "商城后台业务API" })
@RestController
@RequestMapping("customization/sapindexorder")
@Slf4j
public class SapIndexOrderController {
    @Autowired
    private SapIndexOrderService sapIndexOrderService;

    @Resource
    private YphStandardClassService yphStandardClassService;

    @Resource
    private DfgDeliveryService dfgDeliveryService;

    @Resource
    private ImportExtBillService importExtBillService;

    /**
     * 列表
     */
    @ApiOperation(value = "sap索引单-获取列表（分页）", httpMethod = "GET")
    @RequestMapping("/indexOrderList")
    public ServiceResult<PageResp<SapIndexOrderVo>> list(PageReq pageReq,
            IndexOrderListParamDto indexOrderListParamDto) {
        return ServiceResult.succ(sapIndexOrderService.queryPageVo(pageReq, indexOrderListParamDto));
    }

    @ApiOperation(value = "sap索引单-导出", httpMethod = "GET")
    @RequestMapping("/exportIndexOrderList")
    public void ExportIndexOrderList(HttpServletResponse response, IndexOrderListParamDto indexOrderListParamDto) throws IOException {
        List<SapIndexOrderDto> sapIndexOrderDtos = sapIndexOrderService.exportIndexOrderList(indexOrderListParamDto);
        ExcelUtils.write(response, "索引单导出.xls", "数据", SapIndexOrderDto.class, sapIndexOrderDtos);
    }

    /**
     * 信息
     */
    @ApiOperation(value = "sap索引单-获取详细信息", httpMethod = "GET")
    @RequestMapping("/info/{sioId}")
    public ServiceResult info(@PathVariable("sioId") String sioId) {
        SapIndexOrderEntity sapIndexOrder = sapIndexOrderService.getById(sioId);

        return ServiceResult.succ(sapIndexOrder);
    }

    /**
     * 修改
     */
    @ApiOperation(value = "sap索引单-更新", httpMethod = "POST")
    @RequestMapping("/update")
    public ServiceResult update(@RequestBody SapIndexOrderEntity sapIndexOrder) {
        sapIndexOrderService.updateSapIndexOrder(sapIndexOrder);
        return ServiceResult.succ();
    }

    /**
     * 删除
     */
    @ApiOperation(value = "sap索引单-删除", httpMethod = "POST")
    @RequestMapping("/delete")
    public ServiceResult delete(@RequestBody String[] sioIds) {
        sapIndexOrderService.removeByIds(Arrays.asList(sioIds));
        return ServiceResult.succ();
    }

    /**
     * 待结算清单
     */
    @ApiOperation(value = "待结算清单列表（分页）", httpMethod = "GET")
    @GetMapping("/waitIndexList")
    public ServiceResult<PageResp<SapDeliveryVo>> waitIndexList(PageReq requestPage,
            WaitIndexListParamDto waitIndexListParamDto) {
        PageResp<SapDeliveryVo> page = sapIndexOrderService.waitIndexList(requestPage, waitIndexListParamDto);
        return ServiceResult.succ(page);
    }

    @ApiOperation(value = "获取公司列表（分页）", httpMethod = "GET")
    @GetMapping("/newGetWeaksInfo")
    public List<WeaksInfoDto> getSapCompanyAll() {
        return yphStandardClassService.getSapCompanyAll();
    }

    /**
     * 保存索引单
     * saveType:1：草稿 2：发送
     */
    @ApiOperation(value = "保存索引单", httpMethod = "POST")
    @PostMapping("/saveIndexOrder")
    public ServiceResult<T> saveIndexOrder(@RequestBody SaveIndexOrderParamDto saveIndexOrderParamDto) {
        return sapIndexOrderService.saveSapIndexOrder(saveIndexOrderParamDto);
    }

    /**
     * 保存索引单
     * saveType:1：草稿 2：发送
     */
    @ApiOperation(value = "发送索引单到sap", httpMethod = "POST")
    @PostMapping("/sendIndexOrderToSapTest")
    public ServiceResult<T> saveIndexOrder(@RequestBody String indexOrderSn) {
        return sapIndexOrderService.sendIndexOrderToDfgSapTest(indexOrderSn);
    }

    /**
     * 汇总索引单
     */
    @ApiOperation(value = "汇总索引单", httpMethod = "POST")
    @PostMapping("/collectIndexOrder")
    public ServiceResult<SapIndexOrderVo> collectIndexOrder(
            @RequestBody CollectIndexOrderParamDto collectIndexOrderParamDto) {
        SapIndexOrderVo sapIndexOrderVo = sapIndexOrderService.collectIndexOrder(collectIndexOrderParamDto);
        return ServiceResult.succ(sapIndexOrderVo);
    }

    /**
     * 汇总索引单
     */
    @ApiOperation(value = "删除草稿状态的索引单", httpMethod = "POST")
    @PostMapping("/deleteDraftIndexOrder")
    public ServiceResult<T> deleteDraftIndexOrder(@RequestBody List<String> indexOrderIds) {
        return sapIndexOrderService.deleteDraftIndexOrder(indexOrderIds);
    }

    @ApiOperation(value = "待结算清单导出", produces = "application/octet-stream")
    @GetMapping("/waitListExport")
    public void waitListExport(HttpServletResponse response, WaitIndexListParamDto waitIndexListParamDto) {
        try {
            List<IndexOrderExportDto> list = sapIndexOrderService.querySapIndexDetailExport(waitIndexListParamDto);
            // 导出 Excel
            ExcelUtils.write(response, "待结算清单导出.xls", "数据", IndexOrderExportDto.class, list);
        } catch (IOException e) {
            log.error("异常为：", e);
        }
    }

    @ApiOperation(value = "清空匹配记录")
    @GetMapping("/clearMatchingRecords")
    public void clearMatchingRecords() {
        UpdateWrapper<SapDeliveryEntity> sapDeliveryUpdate = new UpdateWrapper<>();
        sapDeliveryUpdate.lambda().in(SapDeliveryEntity::getMatchState, Constant.ALL_MATCH, Constant.P_MATCH)
                .set(SapDeliveryEntity::getMatchState, Constant.NOT_MATCH);
        dfgDeliveryService.update(sapDeliveryUpdate);
    }

    @ApiOperation(value = "匹配未提交索引单，未匹配成功的数据")
    @GetMapping("/matchNcNmData")
    public ServiceResult<T> matchNcNmData(@RequestParam(value = "companyCode") String companyCode,
            @RequestParam(value = "yearAndMonth") String yearAndMonth) {
        if (StrUtil.isBlank(companyCode)) {
            throw new ParameterException("【companyCode】参数异常");
        }
        return importExtBillService.matchNcNmData(companyCode, yearAndMonth);
    }

}
