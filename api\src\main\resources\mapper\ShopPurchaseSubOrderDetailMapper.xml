<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper">
    <resultMap type="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail" id="ShopPurchaseSubOrderDetailMap">
        <result property="orderDetailId" column="order_detail_id" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <result property="supplierOrderNumber" column="supplier_order_number" jdbcType="VARCHAR"/>
        <result property="goodsId" column="goods_id" jdbcType="BIGINT"/>
        <result property="goodsCode" column="goods_code" jdbcType="VARCHAR"/>
        <result property="goodsSku" column="goods_sku" jdbcType="VARCHAR"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="goodsDesc" column="goods_desc" jdbcType="VARCHAR"/>
        <result property="thirdLevelGcid" column="third_level_gcid" jdbcType="VARCHAR"/>
        <result property="thirdLevelGcName" column="third_level_gc_name" jdbcType="VARCHAR"/>
        <result property="typeCode" column="type_code" jdbcType="VARCHAR"/>
        <result property="applyNum" column="apply_num" jdbcType="INTEGER"/>
        <result property="applyNumDecimal" column="apply_num_decimal" jdbcType="VARCHAR"/>
        <result property="confirmNum" column="confirm_num" jdbcType="INTEGER"/>
        <result property="confirmNumDecimal" column="confirm_num_decimal" jdbcType="VARCHAR"/>
        <result property="supplierUnitPriceTax" column="supplier_unit_price_tax" jdbcType="VARCHAR"/>
        <result property="supplierUnitPriceNaked" column="supplier_unit_price_naked" jdbcType="VARCHAR"/>
        <result property="supplierTotalPriceTax" column="supplier_total_price_tax" jdbcType="VARCHAR"/>
        <result property="supplierTotalPriceNaked" column="supplier_total_price_naked" jdbcType="VARCHAR"/>
        <result property="goodsUnitPriceTax" column="goods_unit_price_tax" jdbcType="VARCHAR"/>
        <result property="goodsUnitPriceNaked" column="goods_unit_price_naked" jdbcType="VARCHAR"/>
        <result property="goodsTotalPriceTax" column="goods_total_price_tax" jdbcType="VARCHAR"/>
        <result property="goodsTotalPriceNaked" column="goods_total_price_naked" jdbcType="VARCHAR"/>
        <result property="taxRate" column="tax_rate" jdbcType="INTEGER"/>
        <result property="taxCode" column="tax_code" jdbcType="VARCHAR"/>
        <result property="orderDetailState" column="order_detail_state" jdbcType="INTEGER"/>
        <result property="auditState" column="audit_state" jdbcType="INTEGER"/>
        <result property="auditOpinion" column="audit_opinion" jdbcType="VARCHAR"/>
        <result property="rowSerialNumber" column="row_serial_number" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="isEnable" column="is_enable" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="getOrderDetailMap" type="com.ly.yph.api.openapi.v1.vo.order.GetOrderDetailResp">
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <collection property="orderDetailList" ofType="com.ly.yph.api.openapi.v1.vo.order.OrderDetailList"
                    javaType="arraylist" column="order_number"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.getOrderDetailSubQuery"/>
    </resultMap>

    <resultMap id="queryOrderLogisticsMap" type="com.ly.yph.api.openapi.v1.vo.AdvOrderDeliveryFrontVo">
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <collection property="advOrderDeliveryVoList" ofType="com.ly.yph.api.openapi.v1.vo.AdvOrderDeliveryVo"
                    javaType="arraylist" column="order_id"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryOrderDeliveryById"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ShopPurchaseSubOrderDetailMap">
        select order_detail_id,
        order_id,
        order_number,
        supplier_order_number,
        goods_id,
        goods_code,
        goods_sku,
        goods_name,
        goods_desc,
        type_code,
        apply_num,
        apply_num_decimal,
        confirm_num,
        confirm_num_decimal,
        sale_unit,
        supplier_unit_price_tax,
        supplier_unit_price_naked,
        supplier_total_price_tax,
        supplier_total_price_naked,
        goods_unit_price_tax,
        goods_unit_price_naked,
        goods_total_price_tax,
        goods_total_price_naked,
        tax_rate,
        tax_code,
        order_detail_state,
        row_serial_number,
        remark,
        fail_reason,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time
        from shop_purchase_sub_order_detail
        where order_detail_id = #{orderDetailId}
        and is_enable = '1'
    </select>

    <select id="queryByOrderNumber" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
        select *
        from shop_purchase_sub_order_detail
        where order_number = #{orderNumber}
          and is_enable = '1'
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ShopPurchaseSubOrderDetailMap">
        select order_detail_id,
        order_id,
        order_number,
        supplier_order_number,
        goods_id,
        goods_code,
        goods_sku,
        goods_name,
        goods_desc,
        class_id,
        class_name,
        type_code,
        apply_num,
        confirm_num,
        supplier_unit_price_tax,
        supplier_unit_price_naked,
        supplier_total_price_tax,
        supplier_total_price_naked,
        goods_unit_price_tax,
        goods_unit_price_naked,
        goods_total_price_tax,
        goods_total_price_naked,
        tax_rate,
        tax_code,
        order_detail_state,
        row_serial_number,
        remark,
        fail_reason,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time
        from shop_purchase_sub_order_detail
        where is_enable = '1'
        <if test="orderDetailId != null and orderDetailId != ''">
            and order_detail_id = #{orderDetailId}
        </if>
        <if test="orderId != null and orderId != ''">
            and order_id = #{orderId}
        </if>
        <if test="orderNumber != null and orderNumber != ''">
            and order_number = #{orderNumber}
        </if>
        <if test="supplierOrderNumber != null and supplierOrderNumber != ''">
            and supplier_order_number = #{supplierOrderNumber}
        </if>
        <if test="goodsId != null">
            and goods_id = #{goodsId}
        </if>
        <if test="goodsCode != null and goodsCode != ''">
            and goods_code = #{goodsCode}
        </if>
        <if test="goodsSku != null and goodsSku != ''">
            and goods_sku = #{goodsSku}
        </if>
        <if test="goodsName != null and goodsName != ''">
            and goods_name = #{goodsName}
        </if>
        <if test="goodsDesc != null and goodsDesc != ''">
            and goods_desc = #{goodsDesc}
        </if>
        <if test="classId != null and classId != ''">
            and class_id = #{classId}
        </if>
        <if test="className != null and className != ''">
            and class_name = #{className}
        </if>
        <if test="typeCode != null and typeCode != ''">
            and type_code = #{typeCode}
        </if>
        <if test="applyNum != null">
            and apply_num = #{applyNum}
        </if>
        <if test="confirmNum != null">
            and confirm_num = #{confirmNum}
        </if>
        <if test="supplierUnitPriceTax != null">
            and supplier_unit_price_tax = #{supplierUnitPriceTax}
        </if>
        <if test="supplierUnitPriceNaked != null">
            and supplier_unit_price_naked = #{supplierUnitPriceNaked}
        </if>
        <if test="supplierTotalPriceTax != null">
            and supplier_total_price_tax = #{supplierTotalPriceTax}
        </if>
        <if test="supplierTotalPriceNaked != null">
            and supplier_total_price_naked = #{supplierTotalPriceNaked}
        </if>
        <if test="goodsUnitPriceTax != null">
            and goods_unit_price_tax = #{goodsUnitPriceTax}
        </if>
        <if test="goodsUnitPriceNaked != null">
            and goods_unit_price_naked = #{goodsUnitPriceNaked}
        </if>
        <if test="goodsTotalPriceTax != null">
            and goods_total_price_tax = #{goodsTotalPriceTax}
        </if>
        <if test="goodsTotalPriceNaked != null">
            and goods_total_price_naked = #{goodsTotalPriceNaked}
        </if>
        <if test="taxRate != null">
            and tax_rate = #{taxRate}
        </if>
        <if test="taxCode != null and taxCode != ''">
            and tax_code = #{taxCode}
        </if>
        <if test="orderDetailState != null">
            and order_detail_state = #{orderDetailState}
        </if>
        <if test="rowSerialNumber != null">
            and row_serial_number = #{rowSerialNumber}
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}
        </if>
        <if test="failReason != null and failReason != ''">
            and fail_reason = #{failReason}
        </if>
        <if test="isEnable != null">
            and is_enable = #{isEnable}
        </if>
        <if test="creator != null and creator != ''">
            and creator = #{creator}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="modifier != null and modifier != ''">
            and modifier = #{modifier}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <select id="querySupplierOrderDetail" resultType="com.ly.yph.api.product.ext.common.dto.request.OrderSkuList">
        SELECT goods_sku skuId,
        apply_num skuCount,
        apply_num_decimal skuCountDecimal,
        supplier_unit_price_tax unitPrice,
        supplier_unit_price_naked unitNakePrice,
        goods_name skuName,
        tax_rate taxRate,
        goods_code goodsCode,
        goods_name goodsName
        FROM shop_purchase_sub_order_detail
        WHERE order_number = #{orderNumber}
        and is_enable = '1'
    </select>

    <select id="queryListByPurchaseNumber" resultType="com.ly.yph.api.order.vo.PurchaseSubOrderDetailVo">
        SELECT d.order_detail_id,
        d.order_id,
        d.order_number,
        d.goods_id,
        d.goods_code,
        d.goods_sku,
        d.goods_name,
        d.goods_desc,
        d.goods_image,
        d.sale_unit,
        d.confirm_num,
        d.confirm_num_decimal,
        d.goods_unit_price_tax,
        d.goods_unit_price_naked,
        d.goods_total_price_tax,
        d.goods_total_price_naked,
        d.goods_unit_tax_price_cny,
        d.goods_unit_naked_price_cny,
        d.currency_code,
        d.currency_name,
        d.order_detail_state,
        d.audit_state,
        d.audit_opinion,
        d.third_level_gcid,
        d.third_level_gc_name,
        d.detail_return_state,
        so.order_model as goods_model,
        so.supplier_code,
        so.supplier_name,
        so.supplier_data_source,
        (SELECT IFNULL(SUM(dd.delivery_num), 0)
        FROM shop_delivery_detail dd
        LEFT JOIN shop_delivery sd ON sd.id = dd.delivery_id
        WHERE sd.order_id = d.order_id
        AND dd.goods_code = d.goods_code
        and dd.is_enable = '1'
        ) delivery_num,
        d.needer_name,
        d.needer_department_name,
        sgd.goods_spec_array as goods_spec,
        so.remark,
        d.use_name,
        d.budget_code,
        d.budget_name,
        d.not_min_price_reason,
        d.flow,
        so.is_platform_reconciliation as settlementType
        FROM shop_purchase_sub_order_detail d
        LEFT JOIN shop_purchase_sub_order so ON so.order_number = d.order_number
        LEFT join shop_goods_detail sgd on d.goods_code = sgd.goods_code
        WHERE so.purchase_number = #{purchaseNumber}
        and d.is_enable = '1'
        and so.is_enable = '1'
    </select>

    <select id="queryListBySubOrderNumber" resultType="com.ly.yph.api.order.vo.PurchaseSubOrderDetailVo">
        SELECT d.order_detail_id,
        d.order_id,
        d.order_number,
        d.goods_id,
        d.goods_code,
        d.goods_sku,
        d.goods_name,
        d.goods_desc,
        sb.brand_name,
        sg.materials_code,
        d.goods_image,
        d.sale_unit,
        d.confirm_num,
        d.confirm_num_decimal,
        d.goods_unit_price_tax,
        d.goods_unit_price_naked,
        d.goods_total_price_tax,
        d.goods_total_price_naked,
        d.supplier_unit_price_tax,
        d.order_detail_state,
        d.audit_state,
        d.audit_opinion,
        d.third_level_gcid,
        d.third_level_gc_name,
        so.supplier_code,
        so.supplier_name,
        so.supplier_data_source,
        d.goods_pay_integral,
        d.goods_pay_money,
        d.goods_unit_tax_price_cny,
        d.goods_unit_naked_price_cny,
        d.currency_code,
        d.currency_name,
        po.goods_zone_Id,
        (SELECT IFNULL(SUM(dd.delivery_num ), 0)
        FROM shop_delivery_detail dd
        LEFT JOIN shop_delivery sd ON sd.id = dd.delivery_id
        WHERE sd.order_id = d.order_id
        AND dd.goods_code = d.goods_code
        and dd.is_enable = '1'
        ) delivery_num,
        (SELECT IFNULL(SUM(dd.delivery_num_decimal ), 0)
        FROM shop_delivery_detail dd
        LEFT JOIN shop_delivery sd ON sd.id = dd.delivery_id
        WHERE sd.order_id = d.order_id
        AND dd.goods_code = d.goods_code
        and dd.is_enable = '1'
        ) as delivery_num_decimal,
        d.supplier_unit_original_price_tax
        FROM shop_purchase_sub_order_detail d
        LEFT JOIN shop_purchase_sub_order so ON so.order_number = d.order_number
        LEFT JOIN shop_purchase_order po ON so.purchase_number = po.purchase_number
        left join shop_goods sg on sg.goods_id = d.goods_id
        left join shop_brand sb on sb.brand_id = sg.brand_id
        WHERE so.order_number = #{orderNumber}
        and d.is_enable = '1'
    </select>

    <select id="querySupplierOrderGoods" resultType="com.ly.yph.api.supplier.vo.SupplierOrderGoodsVo">
        SELECT g.order_detail_id,
               g.goods_id,
               g.goods_desc,
               g.goods_code,
               od.goods_spec,
               od.goods_spec_array,
               g.supplier_unit_price_tax,
               g.supplier_total_price_tax,
               (g.apply_num + g.apply_num_decimal)  apply_num,
               (SELECT IFNULL(SUM(dd.delivery_num + dd.delivery_num_decimal), 0)
                FROM shop_delivery_detail dd
                         LEFT JOIN shop_delivery sd ON sd.id = dd.delivery_id
                WHERE sd.order_id = g.order_id
                  and sd.is_enable = '1'
                  and dd.is_enable = '1'
                  AND dd.goods_code = g.goods_code) delivery_num,
               s.supplier_data_source,
               g.goods_unit_tax_price_cny,
               g.goods_unit_naked_price_cny,
               g.currency_code,
               g.currency_name
        FROM shop_purchase_sub_order_detail g
         left join shop_purchase_sub_order s on s.order_number = g.order_number
         LEFT JOIN shop_goods_detail od on od.goods_code = g.goods_code
        WHERE g.order_id = #{orderId}
          and g.is_enable = '1';

    </select>

<!--    <select id="getOrderDetailByGoodsId" resultType="com.ly.yph.api.order.vo.GoodsHistoryRecordVo">-->
<!--        SELECT d.goods_id,-->
<!--        d.order_id,-->
<!--        (d.confirm_num + d.confirm_num_decimal) confirm_num,-->
<!--        round(d.goods_unit_price_tax,4) as goodsUnitPriceTax,-->
<!--        d.create_time ,-->
<!--        o.order_state,-->
<!--        p.company_name,-->
<!--        p.organization_id,-->
<!--        p.company_code-->
<!--        FROM shop_purchase_sub_order_detail d-->
<!--        left join shop_purchase_sub_order o on o.order_id = d.order_id-->
<!--        left join shop_purchase_order p on p.purchase_number = o.purchase_number-->
<!--        WHERE d.goods_id = #{goodsId}-->
<!--        and d.order_detail_state &gt;= 20-->
<!--        and d.is_enable = '1'-->
<!--        order by d.goods_id DESC-->
<!--        limit 10-->
<!--    </select>-->

    <select id="getOrderDetailByGoodsId" resultType="com.ly.yph.api.order.vo.GoodsHistoryRecordVo">
        SELECT
        d.confirm_num as confirm_num,
        d.confirm_num_decimal as confirm_num_decimal,
        goods_unit_price_tax as goodsUnitPriceTax,
        d.create_time
        FROM shop_purchase_sub_order_detail d
        left join shop_purchase_sub_order o on o.order_id = d.order_id
        left join shop_purchase_order p on p.purchase_number = o.purchase_number
        WHERE d.goods_id = #{goodsId}
        and d.create_time &gt;= DATE_SUB(NOW(), INTERVAL 1 YEAR)
        and d.order_detail_state &gt;= 20
        and d.is_enable = '1'
        order by d.goods_id DESC
        limit 30;
    </select>

    <select id="queryMyOrderDetailFrontById" resultType="com.ly.yph.api.order.vo.MyOrderDetailFrontVo">
        select d.order_detail_id,
        d.goods_id,
        d.goods_code,
        d.goods_desc,
        d.goods_image,
        d.goods_unit_price_tax,
        d.goods_unit_price_naked,
        d.confirm_num,
        d.confirm_num_decimal,
        d.goods_unit_tax_price_cny,
        d.goods_unit_naked_price_cny,
        d.currency_code,
        d.currency_name,
        sgd.goods_moq,
        s.supplier_data_source,
        d.order_detail_state
        from shop_purchase_sub_order_detail d
        left join shop_purchase_sub_order s on s.order_number = d.order_number
        left join shop_goods_detail sgd on sgd.goods_code = d.goods_code
        where d.order_id = #{orderId}
    </select>

    <select id="queryOrderLogistics" resultMap="queryOrderLogisticsMap">
        SELECT
        soa.address_name,
        soa.address_type,
        soa.mob_phone,
        concat(soa.province, soa.city, soa.district, soa.address) as address,
        spo.purchase_number,
        spo.purchase_state,
        spso.order_price_tax,
        spo.create_time AS purchase_create_time,
        spo.audit_time,
        spso.order_id,
        spso.order_number,
        spso.order_state,
        spso.fail_reason,
        spso.supplier_code,
        spso.supplier_name,
        spso.create_time AS order_create_time
        FROM
        shop_purchase_order spo
        LEFT JOIN shop_purchase_sub_order spso ON spo.purchase_number = spso.purchase_number
        LEFT JOIN shop_order_address soa on soa.purchase_id = spo.purchase_id
        WHERE
        spo.is_enable = '1'
        AND spo.purchase_state = 30
        AND spso.order_state NOT IN ( -1, 0, 10, 20 )
        <if test="req.purchaseNumber != null and req.purchaseNumber != ''">
            AND spso.purchase_number = #{req.purchaseNumber}
        </if>
        <if test="req.contractNumber != null and req.contractNumber != ''">
            AND spso.contract_number = #{req.contractNumber}
        </if>
    </select>

    <select id="queryOrderDeliveryById" resultType="com.ly.yph.api.openapi.v1.vo.AdvOrderDeliveryVo">
        select sd.id,
        sd.package_id,
        sd.supplier_code,
        sd.order_number,
        sd.supplier_short_name,
        sd.cus_receiving_state,
        sd.delivery_code,
        sd.delivery_name,
        sd.delivery_time,
        sd.postman,
        sd.postman_phone,
        sd.accepter_name,
        sd.check_time,
        sd.cus_receiving_time
        from shop_delivery sd
        where sd.order_id = #{orderId}
        and sd.sup_delivery_state != -99
        and sd.is_enable = '1'
    </select>

    <select id="getOrderDetail" resultMap="getOrderDetailMap">
        select spso.order_id,
        spso.order_number,
        spo.apply_user_name,
        spo.apply_user_phone,
        spso.remark,
        spso.supplier_order_price_tax,
        spso.supplier_order_price_naked,
        spso.order_freight_price,
        spso.order_state,
        spso.sale_client,
        spso.finished_time,
        spso.create_time,
               if(spso.is_company_store = 0, soa.province_id, ca.qpc_province_id)   as province_id,
               if(spso.is_company_store = 0, soa.province, ca.qpc_province)         as province,
               if(spso.is_company_store = 0, soa.city_id, ca.qpc_city_id)           as city_id,
               if(spso.is_company_store = 0, soa.city, ca.qpc_city)                 as city,
               if(spso.is_company_store = 0, soa.district_id, ca.qpc_district_id)   as district_id,
               if(spso.is_company_store = 0, soa.district, ca.qpc_district)         as district,
               if(spso.is_company_store = 0, soa.address, ca.qpc_address)           as address,
               if(spso.is_company_store = 0, soa.address_name, ca.qpc_address_name) as address_name,
               if(spso.is_company_store = 0, soa.mob_phone, ca.qpc_mob_phone)       as mob_phone
        from shop_purchase_sub_order spso
        left join shop_purchase_order spo on spso.purchase_number = spo.purchase_number
        left join shop_order_address soa on spo.purchase_id = soa.purchase_id
        LEFT JOIN company_store_order_address ca ON ca.order_number = spso.order_number
        where spso.order_number = #{req.orderNumber};
    </select>

    <select id="getOrderDetailSubQuery" resultType="com.ly.yph.api.openapi.v1.vo.order.OrderDetailList">
        SELECT goods_sku,
        goods_name,
        goods_desc,
        confirm_num,
        supplier_unit_price_tax,
        supplier_unit_price_naked,
        ifnull(ssd.checked_num, 0) as checked_num
        FROM shop_purchase_sub_order_detail sod
        LEFT JOIN (SELECT order_id,
        sum(ifnull(sdd.checked_num, 0)) AS checked_num,
        sdd.goods_id
        FROM shop_delivery sd
        LEFT JOIN shop_delivery_detail sdd ON sd.id = sdd.delivery_id
        where sd.order_number = #{orderNumber}
        GROUP BY sdd.goods_id) AS ssd ON ssd.order_id = sod.order_id and ssd.goods_id = sod.goods_id
        WHERE sod.order_number = #{orderNumber}
    </select>

    <select id="queryYflOrderDetailList" resultType="com.ly.yph.api.order.vo.ShopYflOrderDetailVo">
        SELECT
        d.order_detail_id,
        d.order_number,
        d.goods_id,
        d.goods_sku,
        d.goods_code,
        d.goods_name,
        d.goods_desc,
        d.goods_image,
        d.supplier_unit_price_tax,
        d.goods_unit_price_tax,
        d.goods_unit_price_naked,
        d.tax_rate,
        d.confirm_num,
        d.supplier_total_price_tax,
        d.goods_total_price_tax,
        d.integral_ceiling,
        d.detail_return_state,
        d.order_detail_state,
        d.order_detail_after_sale_state,
        IFNULL( srd.return_num, 0 ) AS return_num
        FROM
        shop_purchase_sub_order_detail d
        LEFT JOIN (
        SELECT
        r.order_id,
        d.goods_code,
        r.tenant_id,
        sum( d.return_num ) AS return_num
        FROM
        shop_return_detail d
        LEFT JOIN shop_return r ON d.return_id = r.id
        where d.is_enable = 1 and r.is_enable = 1 and  r.return_state not in (-1,-2)
        GROUP BY
        d.goods_code,
        r.order_id
        ) srd ON srd.order_id = d.order_id
        AND srd.goods_code = d.goods_code
        AND d.tenant_id = srd.tenant_id
        WHERE
        d.order_number = #{orderNumber}
    </select>
    <select id="queryExcelList" resultType="com.ly.yph.api.system.dto.OrderDetailExportExcelVO">
        SELECT
        (@i:=@i+1) AS indexNum,
        sod.order_number,
        sod.order_id,
        sod.order_detail_id,
        so.purchase_number,
        so.supplier_order_number,
        s.invoice_subject,
        sod.create_time as createTimeStart,
        sod.create_time,
        CASE
        WHEN so.order_state = - 1 THEN
        "订单失败"
        WHEN so.order_state = 0 THEN
        "已取消"
        WHEN so.order_state = 10 THEN
        "已提交"
        WHEN so.order_state = 20 THEN
        "待发货"
        WHEN so.order_state = 30 THEN
        "待收货"
        WHEN so.order_state = 40 THEN
        "收货完成"
        WHEN so.order_state = 45 THEN
        "部分退货"
        WHEN so.order_state = 50 THEN
        "全部退货"
        ELSE "未知"
        END AS orderStateName,
        (case so.order_state
        when 20 then 30
        when 30 then 40
        when 40 then 55
        else 999 end) as order_state,
        sod.goods_desc,
        sod.order_detail_state,
        CASE
        WHEN sod.order_detail_state = - 1 THEN
        "订单失败"
        WHEN sod.order_detail_state = 0 THEN
        "已取消"
        WHEN sod.order_detail_state = 10 THEN
        "已提交"
        WHEN sod.order_detail_state = 20 THEN
        "待发货"
        WHEN sod.order_detail_state = 30 THEN
        "待收货"
        WHEN sod.order_detail_state = 40 THEN
        "收货完成"
        WHEN sod.order_detail_state = 45 THEN
        "部分退货"
        WHEN sod.order_detail_state = 50 THEN
        "全部退货"
        ELSE "未知"
        END AS orderDetailStateName,
        sod.goods_code,
        sod.goods_sku,
        sod.apply_num,
        sod.supplier_unit_price_naked,
        sod.supplier_unit_price_tax,
        sod.supplier_total_price_tax,
        sod.goods_unit_price_naked,
        sod.goods_unit_price_tax,
        sod.goods_total_price_tax,
        sod.goods_total_price_naked,
        sg.sale_unit,
        sg.materials_code,
        sg.tax_code,
        sg.tax_rate,
        sod.goods_pay_money,
        sod.goods_pay_integral,
        td.transformers_district_name,
        so.supplier_code,
        so.supplier_name,
        CONCAT(a.activity_name,'积分' ) AS integralName,
        sod.goods_name,
        sod.confirm_num,
        sod.confirm_num as checkedNum,
        po.apply_dept_name,
        po.apply_dept_id,
        po.apply_user_id,
        po.apply_emp_code,
        po.apply_user_name,
        soa.address,
        soa.mob_phone,
        soa.address_name,
        po.remark,
        po.company_name,
        lower(case po.company_code
        when '045192' then '45192'
        when 'dfgm' then 'dfg-m'
        else po.company_code end) as source_type,
        sg.spec_goods_ware_qd
        FROM
        shop_purchase_sub_order_detail sod
        LEFT JOIN shop_purchase_sub_order so ON so.order_number = sod.order_number
        AND so.is_enable = 1
        LEFT JOIN shop_purchase_order po ON po.purchase_number = so.purchase_number
        AND po.is_enable = 1
        LEFT JOIN shop_order_invoice s ON s.purchase_id = po.purchase_id
        AND s.is_enable = 1
        LEFT JOIN shop_goods sg on sg.goods_id = sod.goods_id AND sg.tenant_id = sod.tenant_id
        LEFT JOIN system_activity a on a.id = po.activity_id AND a.is_enable = 1
        LEFT JOIN transformers_district td ON td.transformers_district_code = a.activity_type
        left join shop_order_address soa on po.purchase_id = soa.purchase_id
        AND td.tenant_id = a.tenant_id AND td.is_enable = 1
        , (SELECT @i:=0) AS itable
        where sod.is_enable = 1
        AND a.activity_code = #{activityCode}
        AND sod.order_detail_state in (20,30,40,45)
    </select>

    <select id="queryActivityOrderDetail" resultType="com.ly.yph.api.system.dto.OrderDetailExportExcelVO">
        SELECT
        (@i:=@i+1) AS indexNum,
        sod.order_number,
        sod.order_id,
        sod.order_detail_id,
        so.purchase_number,
        so.supplier_order_number,
        s.invoice_subject,
        sod.create_time as createTimeStart,
        sod.create_time,
        CASE
        WHEN so.order_state = - 1 THEN
        "订单失败"
        WHEN so.order_state = 0 THEN
        "已取消"
        WHEN so.order_state = 10 THEN
        "已提交"
        WHEN so.order_state = 20 THEN
        "待发货"
        WHEN so.order_state = 30 THEN
        "待收货"
        WHEN so.order_state = 40 THEN
        "收货完成"
        WHEN so.order_state = 45 THEN
        "部分退货"
        WHEN so.order_state = 50 THEN
        "全部退货"
        ELSE "未知"
        END AS orderStateName,
        so.order_state as order_state,
        sod.goods_desc,
        sod.order_detail_state,
        CASE
        WHEN sod.order_detail_state = - 1 THEN
        "订单失败"
        WHEN sod.order_detail_state = 0 THEN
        "已取消"
        WHEN sod.order_detail_state = 10 THEN
        "已提交"
        WHEN sod.order_detail_state = 20 THEN
        "待发货"
        WHEN sod.order_detail_state = 30 THEN
        "待收货"
        WHEN sod.order_detail_state = 40 THEN
        "收货完成"
        WHEN sod.order_detail_state = 45 THEN
        "部分退货"
        WHEN sod.order_detail_state = 50 THEN
        "全部退货"
        ELSE "未知"
        END AS orderDetailStateName,
        sod.goods_code,
        sod.goods_sku,
        sod.apply_num,
        sod.supplier_unit_price_naked,
        sod.supplier_unit_price_tax,
        sod.supplier_total_price_tax,
        sod.goods_unit_price_naked,
        sod.goods_unit_price_tax,
        sod.goods_total_price_tax,
        sod.goods_total_price_naked,
        sg.sale_unit,
        sg.materials_code,
        sg.tax_code,
        sg.tax_rate,
        sod.goods_pay_money,
        sod.goods_pay_integral,
        td.transformers_district_name,
        so.supplier_code,
        so.supplier_name,
        CONCAT(a.activity_name,'积分' ) AS integralName,
        sod.goods_name,
        sod.confirm_num,
        sod.confirm_num as checkedNum,
        po.apply_dept_name,
        po.apply_dept_id,
        po.apply_user_id,
        po.apply_emp_code,
        po.apply_user_name,
        soa.address,
        soa.mob_phone,
        soa.address_name,
        po.remark,
        po.company_name,
        po.company_code as source_type,
        sg.spec_goods_ware_qd
        FROM
        shop_purchase_sub_order_detail sod
        LEFT JOIN shop_purchase_sub_order so ON so.order_number = sod.order_number
        AND so.is_enable = 1
        LEFT JOIN shop_purchase_order po ON po.purchase_number = so.purchase_number
        AND po.is_enable = 1
        LEFT JOIN shop_order_invoice s ON s.purchase_id = po.purchase_id
        AND s.is_enable = 1
        LEFT JOIN shop_goods sg on sg.goods_id = sod.goods_id AND sg.tenant_id = sod.tenant_id
        LEFT JOIN system_activity a on a.id = po.activity_id AND a.is_enable = 1
        LEFT JOIN transformers_district td ON td.transformers_district_code = a.activity_type
        left join shop_order_address soa on po.purchase_id = soa.purchase_id
        AND td.tenant_id = a.tenant_id AND td.is_enable = 1
        , (SELECT @i:=0) AS itable
        where sod.is_enable = 1
        AND a.activity_code = #{activityCode}
        AND sod.order_detail_state in (20,30,40,45)
        AND sod.order_number in
        <foreach collection="list" item="orderNumber" separator="," close=")" open="(">
            #{orderNumber}
        </foreach>
    </select>
    <update id="updateBatch" parameterType="java.util.List">
        update shop_purchase_sub_order_detail
        <trim prefix="set" suffixOverrides=",">
            status=
            <foreach collection="list" item="item" open="case " close=" end,">
                when field2=#{item.field2} and company_id=#{item.field3} then #{item.status}
            </foreach>
            create_time =
            <foreach collection="list" item="item" open="case " close=" end,">
                when field2=#{item.field2} and company_id=#{item.field3} then
                <choose>
                    <when test="item.createTime!=null">
                        #{item.createTime}
                    </when>
                    <otherwise>now()</otherwise>
                </choose>
            </foreach>
        </trim>
        WHERE
        <foreach collection="list" item="item" open="( " separator=") or (" close=" )">
            device_num=#{item.field2} and company_id=#{item.field3}
        </foreach>
    </update>

    <select id="getLastPrice" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
        WITH ranked_orders AS (
        SELECT goods_code,
        supplier_unit_original_price_tax,
        supplier_unit_original_price_naked,
        supplier_unit_price_tax,
        supplier_unit_price_naked,
        goods_unit_price_tax,
        goods_unit_price_naked,
        ROW_NUMBER() OVER (PARTITION BY goods_code ORDER BY create_time DESC) AS rn
        FROM shop_purchase_sub_order_detail
        WHERE goods_code in
        <foreach collection="goodsCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
        SELECT goods_code,
        supplier_unit_original_price_tax,
        supplier_unit_original_price_naked,
        supplier_unit_price_tax,
        supplier_unit_price_naked,
        goods_unit_price_tax,
        goods_unit_price_naked
        FROM ranked_orders
        WHERE rn = 1;
    </select>

    <select id="queryYouServiceOrderDetailByOrderNumber"
            resultType="com.ly.yph.api.order.vo.youServiceOrder.YouServiceOrderDetailVo">
        select order_id,
        goods_desc,
        goods_image,
        order_detail_State,
        goods_unit_price_naked,
        goods_total_price_tax,
        confirm_num,
        confirm_num_decimal
        from shop_purchase_sub_order_detail
        where is_enable = 1 and order_number =#{orderNumber}
    </select>

    <update id="updateBudgetNoByPurchaseNumber">
        update shop_purchase_sub_order_detail a, shop_purchase_sub_order b
        set a.budget_code=#{budgetCode}, a.budget_name=#{budgetName}
        where a.order_id=b.order_id and b.purchase_number=#{purchaseNumber} and b.is_enable = 1 and a.is_enable=1
    </update>

    <update id="updateGoodsStateByPurchaseNumber">
        update shop_purchase_sub_order_detail a, shop_purchase_sub_order b
        set a.order_detail_state=#{orderDetailState}
        where a.order_id=b.order_id and b.purchase_number=#{purchaseNumber} and b.is_enable = 1 and a.is_enable=1
    </update>

    <select id="hondaNpmsBudgetDealVo" resultType="com.ly.yph.api.honda.dto.HondaNpmsBudgetDealDto">
        select
        po.other_relation_number as orderShortSn,
        po.other_relation_number as grpId,
        po.order_label,
        po.purchase_id,
        nb.bukrs,
        nb.ysgk,
        nb.yscd,
        nb.yshm,
        nb.gjahr as gjahr,
        sum(og.goods_total_price_naked) as dmbtr
        from shop_purchase_sub_order_detail og
        LEFT JOIN shop_purchase_sub_order o on og.order_id=o.order_id
        LEFT JOIN shop_purchase_order po on po.purchase_number=o.purchase_number
        LEFT JOIN honda_npms_budget nb on nb.id=og.budget_id
        where
            po.purchase_id=#{purchaseId} and po.purchase_state!=0 and po.is_enable=1 and o.is_enable=1  group by po.other_relation_number, nb.yshm
    </select>
    <select id="getPuOrderDetailByPurchaseNumber"
            resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
          select
            order_detail_id,
            b.order_number,
            goods_id,
            goods_sku,
            goods_code,
            apply_num,
            purchase_goods_id,
            tax_rate,
            apply_num_decimal,
            goods_name,
            goods_unit_price_naked,
            goods_unit_price_tax,
            order_detail_State,
            d.row_serial_number,
            d.goods_total_price_naked,
            d.goods_total_price_tax
        from shop_purchase_sub_order_detail d
        left join shop_purchase_sub_order b on b.order_id=d.order_id
        where  b.purchase_number=#{purchaseNumber} and b.is_enable = 1 and d.is_enable=1
    </select>

    <select id="getOutCheckOrderDetailByPurchaseNumbers" resultType="com.ly.yph.api.order.vo.check.NeedForOutCheckVo">
        select
        spo.purchase_number,
        spo.company_code,
        spo.company_name,
        spso.order_number,
        spso.supplier_order_number,
        spsod.goods_code,
        spsod.goods_sku,
        spsod.confirm_num,
        spsod.row_serial_number
        from
        shop_purchase_sub_order_detail spsod
        left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
        left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        where spo.purchase_number in
        <foreach collection="list" item="purchaseNumber" open="(" close=")" separator=",">
            #{purchaseNumber}
        </foreach>
    </select>

    <select id="getOutCheckOrderDetail" resultType="com.ly.yph.api.order.vo.check.NeedForOutCheckVo">
        select spo.purchase_number,
        spo.company_code,
        spo.company_name,
        spso.order_number,
        spso.supplier_order_number,
        spsod.goods_code,
        spsod.goods_sku,
        spsod.confirm_num,
        spsod.row_serial_number,
        spso.supplier_code,
        spso.supplier_name,
        CASE
        WHEN spsod.order_detail_state = - 1 THEN
        "订单失败"
        WHEN spsod.order_detail_state = 0 THEN
        "已取消"
        WHEN spsod.order_detail_state = 10 THEN
        "已提交"
        WHEN spsod.order_detail_state = 20 THEN
        "待发货"
        WHEN spsod.order_detail_state = 30 THEN
        "待收货"
        WHEN spsod.order_detail_state = 40 THEN
        "收货完成"
        WHEN spsod.order_detail_state = 45 THEN
        "部分退货"
        WHEN spsod.order_detail_state = 50 THEN
        "全部退货"
        ELSE "未知"
        END AS orderDetailStatusStr
        from shop_purchase_sub_order_detail spsod
        left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
        left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        where spo.sap_order_type not in (0,4)
        and spo.purchase_state = 30
    </select>

    <select id="getOrderDetailByPurchaseNumberForDms" resultType="com.ly.yph.api.openapi.v1.vo.dms.DmsOrderDetailVo">
        select spsod.goods_code,
        spsod.goods_sku,
        spsod.goods_desc,
        spsod.goods_unit_price_naked,
        spsod.goods_total_price_naked,
        spsod.tax_rate as mwskz,
        spsod.apply_num,
        spsod.row_serial_number
        from shop_purchase_sub_order_detail spsod
        left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
        where spsod.is_enable = 1
        and spso.is_enable = 1
        and spso.purchase_number =#{purchaseOrderNumber}
    </select>

    <select id="getGoodsHistoryCount" resultType="java.lang.Long">
        SELECT count(d.order_detail_id)
        FROM shop_purchase_sub_order_detail d
        LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
        LEFT JOIN shop_purchase_order p on p.purchase_number = o.purchase_number
        LEFT JOIN system_organization s on s.code = p.company_code and s.tenant_id = 1
        where  d.goods_code = #{goodsCode} and d.order_detail_after_sale_state > 10 and s.id = #{companyOrgId}
    </select>
    <select id="getOrderDetailByPurNumberAndGoodsCode" resultType="com.ly.yph.api.system.dto.OrderDetailReceiptVO">
        select
        spso.purchase_number ,
        spsod.order_number ,
        spsod.goods_code ,
        spsod.goods_sku ,
        spsod.apply_num ,
        spsod.row_serial_number
        from
        shop_purchase_sub_order spso
        left join
        shop_purchase_sub_order_detail spsod
        on spso.order_number = spsod.order_number
        where spso.purchase_number = #{purchaseNumber}
        and spsod.goods_code = #{goodsCode}
    </select>

    <select id="getUpdateOrderDetailDtoByPurchaseNumber"
            resultType="com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto">
        select spsod.order_detail_id,
               spsod.order_detail_state,
               spo.company_code,
               spo.purchase_state,
               spo.approval_opinion,
               spo.sap_order_type,
               spo.other_relation_number,
               spso.finished_time as confirmTime,
               spo.order_sales_channel
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
                 left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        where spsod.is_enable = 1
          and spso.is_enable = 1
          and spso.purchase_number = #{purchaseOrderNumber}
          and spo.order_sales_channel !=4
    </select>

    <select id="getOrderDetailByPurchaseNumbersForDfpvSap"
            resultType="com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto">
        select
        spsod.order_detail_id,
        spsod.row_serial_number,
        spso.purchase_number,
        spso.order_number
        from shop_purchase_sub_order_detail spsod
        left join shop_purchase_sub_order spso on spsod.order_id = spso.order_id
        where spsod.is_enable = 1
        and spso.is_enable = 1
        and spso.purchase_number in
        <foreach collection="purchaseOrderNumbers" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getEnableByOrderId" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
        select spsod.*
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spso.order_id = spsod.order_id
        where spsod.order_detail_state not in (-1, 0)
          and spsod.is_enable = 1
          and spsod.order_id = #{orderId}
          and spso.order_state not in (-1, 0)
          and spso.is_enable = 1
    </select>

    <select id="queryTop100" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT d.goods_id,
               d.goods_code,
               d.third_level_gcid as standardClassId,
               o.supplier_code,
               SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND d.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) -- 限定前30天
          AND d.supplier_unit_price_tax &lt;= 1000
          AND o.supplier_code NOT IN('DSXFS00','DSOFS00')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 100
    </select>

    <select id="queryTop20By1" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND g.third_class IN('MS001002')
          AND ( g.goods_desc like '%汽水%' OR g.goods_desc like '%气泡%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By2" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND g.third_class IN('MS001001')
          AND ( g.goods_desc like '%果冻%' OR g.goods_desc like '%碎冰冰%' OR g.goods_desc like '%龟苓膏%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By3" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND ( g.goods_desc like '%防晒霜%' OR g.goods_desc like '%防晒喷雾%' OR g.goods_desc like '%防晒口罩%' OR g.goods_desc like '%防晒口服%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By4" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND ( g.goods_desc like '%驱蚊%' OR g.goods_desc like '%花露水%' OR g.goods_desc like '%蚊香%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By1" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND g.third_class IN('MS001002')
          AND ( g.goods_desc like '%汽水%' OR g.goods_desc like '%气泡%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By2" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND g.third_class IN('MS001001')
          AND ( g.goods_desc like '%果冻%' OR g.goods_desc like '%碎冰冰%' OR g.goods_desc like '%龟苓膏%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By3" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND ( g.goods_desc like '%防晒霜%' OR g.goods_desc like '%防晒喷雾%' OR g.goods_desc like '%防晒口罩%' OR g.goods_desc like '%防晒口服%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="queryTop20By4" resultType="com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity">
        SELECT
            d.goods_id,
            d.goods_code,
            d.third_level_gcid as standardClassId,
            o.supplier_code,
            SUM(goods_total_price_tax)   AS salePrice
        FROM shop_purchase_sub_order_detail d
                 LEFT JOIN shop_purchase_sub_order o on o.order_id = d.order_id
                 LEFT JOIN shop_goods g on g.goods_id = d.goods_id
        WHERE d.order_detail_state >= 10
          AND ( g.goods_desc like '%驱蚊%' OR g.goods_desc like '%花露水%' OR g.goods_desc like '%蚊香%')
          AND g.shelves_state = 1
          AND g.is_enable = 1
        GROUP BY d.goods_code
        ORDER BY salePrice DESC
        LIMIT 20
    </select>

    <select id="hondaDetailVo" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
        select
            spsod.order_detail_id,
            spso.order_id,
            spso.order_number,
            spso.supplier_code,
            spso.supplier_order_number,
            spsod.goods_code,
            spsod.goods_sku
        from shop_purchase_order spo
        left join shop_purchase_sub_order spso on spo.purchase_number = spso.purchase_number
        left join shop_purchase_sub_order_detail spsod on spso.order_number = spsod.order_number
        where spo.other_relation_number = #{otherRelationNumber} and spo.company_code='HONDA'
        and spsod.goods_sku in
        <foreach collection="goodsSkus" item="goodsSku" separator="," open="(" close=")" >
            #{goodsSku}
        </foreach>
    </select>
</mapper>

