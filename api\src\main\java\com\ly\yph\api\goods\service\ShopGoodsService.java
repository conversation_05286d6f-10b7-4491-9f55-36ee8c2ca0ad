package com.ly.yph.api.goods.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ly.yph.api.bidding.dto.BiddingSupplierGoodsSaveDto;
import com.ly.yph.api.bidding.service.BiddingSupplierGoodsService;
import com.ly.yph.api.goods.common.GoodsChangeLogEnum;
import com.ly.yph.api.goods.common.HuoshanDeepseekCategory;
import com.ly.yph.api.goods.common.HuoshanDeepseekProduct;
import com.ly.yph.api.goods.common.dto.HuoshanKnowledgeAnswerVO;
import com.ly.yph.api.goods.controller.vo.*;
import com.ly.yph.api.goods.convert.GoodsConvert;
import com.ly.yph.api.goods.dto.*;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.enums.GoodsAuthMode;
import com.ly.yph.api.goods.enums.GoodsLabelEnum;
import com.ly.yph.api.goods.enums.GoodsPoolLevelEnum;
import com.ly.yph.api.goods.enums.OpenUpdateSceneEnum;
import com.ly.yph.api.goods.es.EsUpdateType;
import com.ly.yph.api.goods.es.manager.GoodsEsProcessor;
import com.ly.yph.api.goods.exception.GoodsException;
import com.ly.yph.api.goods.manage.GoodsFlowManage;
import com.ly.yph.api.goods.manage.GoodsUpDownManage;
import com.ly.yph.api.goods.mapper.*;
import com.ly.yph.api.goods.vo.*;
import com.ly.yph.api.goods.workflow.WorkFlowManager;
import com.ly.yph.api.openapi.exception.ErrorCodeConstants;
import com.ly.yph.api.openapi.v1.vo.*;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.order.entity.ShopCart;
import com.ly.yph.api.order.entity.ShopSrmContractDetailEntity;
import com.ly.yph.api.order.entity.ShopSrmContractEntity;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.order.service.ShopSrmContractDetailService;
import com.ly.yph.api.order.service.ShopSrmContractService;
import com.ly.yph.api.order.vo.GoodsHistoryRecordVo;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemOrganizationPurchaseContractEntity;
import com.ly.yph.api.organization.enums.OrganizationTypeEnum;
import com.ly.yph.api.organization.service.SystemOrganizationPurchaseContractService;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.product.ext.common.dto.request.SupplierShopGoodsQueryDto;
import com.ly.yph.api.product.ext.common.dto.response.RemoteMoqInfoResp;
import com.ly.yph.api.product.ext.common.dto.response.RemotePriceInfoResp;
import com.ly.yph.api.product.ext.common.entity.SyncDetailInfo;
import com.ly.yph.api.product.ext.common.enums.ErrorGoodsPriceMsgEnum;
import com.ly.yph.api.product.ext.common.manage.BatchPriceGetter;
import com.ly.yph.api.product.ext.common.manage.RemoteInfoManage;
import com.ly.yph.api.product.ext.common.service.GoodsProcessMonitorService;
import com.ly.yph.api.product.ext.common.service.SyncDetailInfoService;
import com.ly.yph.api.product.ext.common.vo.GoodsProcessVo;
import com.ly.yph.api.product.ext.dl.service.BackupDlGoodsService;
import com.ly.yph.api.product.ext.jd.dto.reponse.GetSimilarSkuGoodsResp;
import com.ly.yph.api.product.ext.jd.general.config.JDGeneralConfig;
import com.ly.yph.api.product.ext.jd.general.service.BackupJdGeneralGoodsService;
import com.ly.yph.api.product.ext.jd.integral.service.BackupJdIntegralGoodsService;
import com.ly.yph.api.product.ext.jd.service.BackupJdGoodsService;
import com.ly.yph.api.product.ext.ofs.service.BackupOfsGoodsService;
import com.ly.yph.api.product.ext.qx.service.BackupQxGoodsService;
import com.ly.yph.api.product.ext.sn.service.BackupSnGoodsService;
import com.ly.yph.api.product.ext.xy.service.BackupXyGoodsService;
import com.ly.yph.api.product.ext.yph.config.YphConfig;
import com.ly.yph.api.product.ext.zkh.service.BackupZkhGoodsService;
import com.ly.yph.api.supplier.dto.ShopSupplierContractDto;
import com.ly.yph.api.supplier.dto.SupplierProductDownDto;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierClass;
import com.ly.yph.api.supplier.entity.ShopSupplierClassRc;
import com.ly.yph.api.supplier.entity.SystemContractEntity;
import com.ly.yph.api.supplier.mapper.ShopDfnissan01ContractMapper;
import com.ly.yph.api.supplier.service.ShopSupplierClassRcService;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.supplier.service.SystemContractService;
import com.ly.yph.api.system.queue.manager.MIQueueManager;
import com.ly.yph.api.system.service.FileService;
import com.ly.yph.api.workflow.entity.WorkflowAuditRecord;
import com.ly.yph.api.workflow.service.WorkFlowService;
import com.ly.yph.api.zone.dto.GoodsPushDownDto;
import com.ly.yph.api.zone.service.GoodsZoneService;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.core.base.CommonCodeGeneral;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.exception.AssertUtil;
import com.ly.yph.core.base.exception.Code;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.exception.types.NotFoundException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.excel.util.ExcelUtils;
import com.ly.yph.core.lock.annotation.RedisCache;
import com.ly.yph.core.oss.OSSAutoConfig;
import com.ly.yph.core.oss.OSSClient;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.core.util.JsonUtils;
import com.ly.yph.core.util.StringHelper;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ly.yph.api.goods.service.ShopGoodsDetailService.replaceAmp;

/**
 * <AUTHOR>
 * @Date 2022/1/19 11:03
 **/
@Slf4j
@Service
@RefreshScope
public class ShopGoodsService extends ServiceImpl<ShopGoodsMapper, ShopGoods> {

    @Resource
    private RemoteInfoManage rmtMng;
    @Resource
    private MIQueueManager miQueueManager;

    @Resource
    private SystemOrganizationService organizationService;
    @Resource
    private SystemOrganizationPurchaseContractService organizationContractService;
    @Resource
    private ShopGoodsPriceService priceSrv;
    @Resource
    private ShopGoodsDetailService shopGoodsDetailService;
    @Resource
    private ShopGoodsMapper shopGoodsMapper;
    @Resource
    private YphStandardClassService yphStandardClassService;
    @Resource
    private ShopSupplierService supplierSrv;
    @Resource
    private CodeGeneral codeGeneral;
    @Resource
    private ShopGoodsStockService shopGoodsStockService;
    @Resource
    private ShopSupplierClassService classSrv;
    @Resource
    private BackupZkhGoodsService backupZkhGoodsService;
    @Resource
    private BackupOfsGoodsService backupOfsGoodsService;
    @Resource
    private BackupQxGoodsService backupQxGoodsService;
    @Resource
    private BackupXyGoodsService backupXyGoodsService;
    @Resource
    private BackupDlGoodsService backupDlGoodsService;
    @Resource
    private BackupJdGoodsService backupJdGoodsService;
    @Resource
    private BackupJdIntegralGoodsService backupJdIntegralGoodsService;
    @Resource
    private BackupJdGeneralGoodsService backupJdGeneralGoodsService;
    @Resource
    private BackupSnGoodsService backupSnGoodsService;
    @Resource
    private ShopBrandService shopBrandService;
    @Resource
    private SyncDetailInfoService syncDetailInfoService;
    @Resource
    private OSSAutoConfig ossConfig;
    @Resource
    private YphConfig yphConfig;
    @Resource
    private JDGeneralConfig jdGeneralConfig;
    @Resource
    private BatchPriceGetter priceGetter;
    @Resource
    private ShopGoodsChangeLogService changeLogSrv;
    @Resource
    private ShopPurchaseSubOrderDetailService orderDetailService;
    @Resource
    private ShopGoodsPoolGoodsService goodsPoolGoodsService;
    @Resource
    private ShopGoodsService goodsSrv;
    @Resource
    private YphStandardClassMapper scMapper;
    @Resource
    private ShopSrmContractService srmContractService;
    @Resource
    private ShopSrmContractDetailService srmContractDetailService;
    @Resource
    private YphGoodsPriceStrategyService yphGoodsPriceStrategyService;

    @Resource
    private ShopGoodsPoolService shopGoodsPoolService;

    @Resource
    private ShopGoodsPoolClassService shopGoodsPoolClassService;

    @Resource
    private ShopGoodsPoolSupplierService shopGoodsPoolSupplierService;

    @Resource
    private DfmallGoodsPoolService dfmallGoodsPoolService;

    @Resource
    private DfmallGoodsPoolSubService dfmallGoodsPoolSubService;

    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;
    @Resource
    private GoodsEsProcessor goodsEsProcessor;
    @Resource
    private WorkFlowManager workFlowManager;
    @Resource
    private WorkFlowService workFlowService;
    @Value("${customization.open.supplier:DSOB000}")
    private List<String> supplierCodeList;
    @Value("${customization.open.independentSupplier.customization:DSJSBG0}")
    private List<String> customizationSupplierCodeList;
    @Value("${yfl.tenant-id}")
    private Long yflTenantId;
    @Resource
    private ShopSupplierClassRcService shopSupplierClassRcService;
    @Resource
    private ShopProjectUsersService shopProjectUsersService;
    @Resource
    private SupplierGoodsShelvesDownService supplierGoodsShelvesDownService;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private GoodsFlowManage goodsFlowManage;
    @Resource
    private GoodsZoneService goodsZoneService;
    @Resource
    private GoodsUpDownManage goodsUpDownManage;
    @Resource
    private ShopGoodsContractService goodsContractService;
    @Resource
    private ShopGoodsSaleLabelService goodsSaleLabelService;
    @Resource
    private GoodsProcessMonitorService goodsProcessMonitorSrv;
    @Resource
    private ShopGoodsIncidentalDetailService shopGoodsIncidentalDetailService;
    @Resource
    private CommonCodeGeneral codeMaker;
    @Resource
    private ShopGoodsSameMapper goodsSameMapper;
    @Resource
    private ShopGoodsSameService goodsSameService;
    @Resource
    private ShopGoodsEditRecordMapper goodsEditRecordMapper;
    @Resource
    private FileService fileService;
    @Resource
    private SystemContractService systemContractService;
    @Resource
    private ShopDfnissan01ContractMapper dfnissan01ContractMapper;
    @Resource
    private BiddingSupplierGoodsService biddingSupplierGoodsService;
    @Value("${customize.dfs.custom-ly}")
    private String customLy;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private HuoshanDeepseekProduct huoshanDeepseekProduct;
    @Resource
    private HuoshanDeepseekCategory huoshanDeepseekCategory;
    @Resource
    private SystemTenantService systemTenantService;
    @Resource
    private ShopGoodsUpNewService goodsUpNewService;

    @Transactional
    public void createShopGoods(ProductCreateReq reqVo) {
        ShopGoods exist = this.selectBySkuAndSupplier(reqVo.getGoodsSku(), reqVo.getSupplierCode());

        // 如果已经存在过的商品则返回已经推送过的错误
        if (exist != null) {
            throw HttpException.exception(ErrorCodeConstants.GOODS_ALREADY_EXIST, reqVo.getGoodsSku());
        }
        // 创建商品信息并保存
        ShopGoods se = createShopGoodsInfo(reqVo);
        // 保存同款商品，如有 且商品明细规格存储结构改变
        goodsSameService.openSaveGoodsSame(reqVo.getSpecArray(), se, reqVo.getSameCode());
        // 创建商品价格并保存
        ShopGoodsPrice price = createShopGoodsPrice(reqVo, se);
        // 创建商品详细信息并保存
        ShopGoodsDetail detail = createShopGoodsDetail(reqVo, se);
        // 创建商品库存并保存
        ShopGoodsStock stock = createShopGoodsStock(reqVo, se);

        //欧贝定制化
        createSupplierClass(reqVo);
        if (se.getAuditState() == 1) {
            workFlowManager.goodsContractOperation(CollectionUtil.newArrayList(se.getGoodsId()), CollectionUtil.newArrayList(se), false, new HashMap<>());
            goodsUpDownManage.goodsIdUpToLiteFlow(CollectionUtil.newArrayList(se.getGoodsId()));
        } else {
            workFlowManager.submitShelves(CollectionUtil.toList(Math.toIntExact(se.getGoodsId())));
        }
    }

    private void checkSpec(List<cn.hutool.json.JSONObject> specArray, String sameCode) {
        //规格名称一致性：当前商品的规格名称必须与目标SPU的规格定义完全一致
        //（例：SPU定义规格为["颜色","尺码"]，商品必须有且仅有这两个规格名）
        //规格值唯一性：商品规格值可修改，但在同SPU下全局不能重复
        //（例：同SPU中不能存在两个["红色","M"]的商品）
        Map<String, Object> specMap = goodsSameService.queryGoodsSpecArray(sameCode);
        // 1. 规格名称一致性校验
        checkSpecNameConsistency(specArray,specMap);

        // 2. 规格值唯一性校验
        checkSpecValueUniqueness(specArray, specMap );
    }

    /**
     * 校验规格名称是否与SPU定义完全一致
     * @param specArray
     * @param specMap ["颜色":{黑色,白色},"规格":{50只/盒,20只/盒}]
     */
    private void checkSpecNameConsistency(List<cn.hutool.json.JSONObject> specArray, Map<String, Object> specMap) {
        Set<String> spuSpecNames = specMap.keySet();

        // 提取当前商品的规格名称
        Set<String> currentSpecNames = specArray.stream()
                .map(obj -> obj.keySet().iterator().next()) // 获取每个JSONObject的key
                .collect(Collectors.toSet());

        // 比较是否一致
        if (!currentSpecNames.equals(spuSpecNames)) {
            throw new IllegalArgumentException(
                    "规格名称不一致！SPU定义：" + spuSpecNames + "，当前商品：" + currentSpecNames
            );
        }
    }

    /**
     * 校验规格值在同SPU下是否唯一
     * @param specArray 当前商品的规格列表（如 [{"颜色":"黑色"}, {"规格":"50只/盒"}]）
     * @param specMap   SPU定义的规格模板（如 {"颜色": ["黑色", "白色"], "规格": ["50只/盒", "20只/盒"]}）
     */
    private void checkSpecValueUniqueness(List<cn.hutool.json.JSONObject> specArray, Map<String, Object> specMap) {
        for (cn.hutool.json.JSONObject jsonObject : specArray) {
            Set<String> keySet = jsonObject.keySet();
            keySet.forEach(key -> {
                String value = jsonObject.getStr(key);
                Object specValue = specMap.get(key);
                if (specValue != null) {
                    Set<String> specValue1 = (Set<String>) specValue;
                    if (specValue1.contains(value)) {
                        throw new IllegalArgumentException("规格值重复！重复规格值" + value);
                    }
                }
            });
        }
    }

    /**
     * 日产对接商城对接接口平台 定制化 存储接口平台方的供应商分类编码 后续查询给日产用
     *
     * @param reqVo
     */
    private void createSupplierClass(ProductCreateReq reqVo) {
        if (!supplierCodeList.contains(reqVo.getSupplierCode())) {
            return;
        }
        if (StrUtil.isBlank(reqVo.getSupplierClass())) {
            throw new HttpException("未推送分类信息");
        }
        //保存
        ShopSupplierClassRc bySkuSupplierCode = shopSupplierClassRcService.getBySkuSupplierCode(reqVo.getSupplierCode(), reqVo.getGoodsSku());
        if (bySkuSupplierCode == null) {
            ShopSupplierClassRc shopSupplierClassRc = new ShopSupplierClassRc();
            shopSupplierClassRc.setClassCode(reqVo.getSupplierClass()).setSupplierCode(reqVo.getSupplierCode()).setGoodsSku(reqVo.getGoodsSku());
            shopSupplierClassRcService.save(shopSupplierClassRc);
        } else {
            bySkuSupplierCode.setClassCode(reqVo.getSupplierClass());
            shopSupplierClassRcService.updateById(bySkuSupplierCode);
        }
    }

    /**
     * 保存商品
     * 「*」* @param supplierGoodsReleaseDto 供应商货物
     *
     * @return {@link String}
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveShopGoods(SupplierGoodsReleaseDto p, String supplierCode) {
        // 填充三级分类编码
        if (p.getIsImportGoods() != 1 && StringUtils.isBlank(p.getGoodsClassCode())) {
            this.getStandardClassCode(p, ",");
        }

        p.getSaveGoodsContactDto().forEach( saveGoodsContractDto -> {
             String orgCode = organizationService.getById(saveGoodsContractDto.getCompanyOrgId()).getCode();
             saveGoodsContractDto.setCompanyOrgCode(orgCode);
        });
        // 独立供应商 新建商品时给签约的每个租户都创建商品
        ShopSupplier shopSupplier = supplierSrv.selectByCode(supplierCode);
        if (shopSupplier.getSupplierType() == 1 && Objects.isNull(p.getGoodsId())) {
            List<Long> tenantIdsList = new ArrayList<>();
            switch (p.getSaleClient()){
                case 0:
                    tenantIdsList.add(1L);
                    tenantIdsList.add(yflTenantId);
                break;
                case 1:
                    tenantIdsList.add(1L);
                    break;
                case 2:
                    tenantIdsList.add(yflTenantId);
                    break;
            }
            tenantCheck(shopSupplier.getTenantIds(), tenantIdsList);
            TenantUtils.execute(tenantIdsList, () -> {
                this.goodsSrv.toSaveShopGoods(p);
            });
        } else {
            this.goodsSrv.toSaveShopGoods(p);
        }
        return p.getGoodsSku();
    }

    public void tenantCheck(String tenantIds, List<Long> tenantIdsList) {
        // 将输入的tenantIds字符串转换为Long列表
        List<Long> tenantList = Arrays.stream(tenantIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::valueOf)
                .collect(Collectors.toList());
        // 转换为Set以便比较
        Set<Long> tenantSet = new HashSet<>(tenantList);
        Set<Long> expectedSet = new HashSet<>(tenantIdsList);

        // 检查tenantSet是否包含expectedSet中的所有元素
        if (!tenantSet.containsAll(expectedSet)) {
            StringBuilder msgBuilder = new StringBuilder();
            for (Long tenantId : tenantSet) {
                if (msgBuilder.length() > 0) {
                    msgBuilder.append("/");
                }
                switch (tenantId.toString()) {  // 使用字符串比较更安全
                    case "1":
                        msgBuilder.append("东风商城");
                        break;
                    case "12":
                        msgBuilder.append("东风商城（积分）");
                        break;
                    case "40":
                        msgBuilder.append("东风商城（积分）");
                        break;
                    // 可以添加更多case处理其他tenantId
                    default:
                        msgBuilder.append("未知平台(").append(tenantId).append(")");
                }
            }
            throw new ParameterException("当前供应商仅支持上架（{}），请修改商品标签或联系运营调整配置。", msgBuilder);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveSpuGoods(SpuGoodsSaveDto dto) {
        List<SpuGoodsSkuSaveDto> spuGoodsSkuList = dto.getSpuGoodsSkuList();
        if (CollUtil.isEmpty(spuGoodsSkuList)) {
            return;
        }

        if (spuGoodsSkuList.stream().map(SpuGoodsSkuSaveDto::getSpecArray).collect(Collectors.toSet()).size() < spuGoodsSkuList.size()) {
            throw new ParameterException("商品规格不能重复");
        }

        Set<String> paramSkuSet = spuGoodsSkuList.stream().map(SpuGoodsSkuSaveDto::getGoodsSku).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        List<Long> tenantIdsList;
        switch (dto.getSaleClient()) {
            case 0:
                tenantIdsList = Arrays.asList(1L, yflTenantId);
                break;
            case 1:
                tenantIdsList = Collections.singletonList(1L);
                break;
            case 2:
                tenantIdsList = Collections.singletonList(yflTenantId);
                break;
            default:
                tenantIdsList = Collections.singletonList(TenantContextHolder.getRequiredTenantId());
        }
        String supplierCode = dto.getSupplierCode();
        TenantUtils.execute(tenantIdsList, () -> {
            Long currentTenantId = TenantContextHolder.getTenantId();
            ShopSupplier shopSupplier = supplierSrv.getBaseMapper().selectByCode(supplierCode);
            if (null == shopSupplier) {
                throw new ParameterException("租户【" + currentTenantId + "】下未查询到供应商信息");
            }
            if (CollUtil.isNotEmpty(paramSkuSet)) {
                List<ShopGoods> existGoods = baseMapper.getBySkuInAndSupplier(paramSkuSet, supplierCode);
                if (CollUtil.isNotEmpty(existGoods)) {
                    Set<String> existSkuSet = existGoods.stream().map(ShopGoods::getGoodsSku).collect(Collectors.toSet());
                    throw new ParameterException("租户【" + currentTenantId + "】下的商品SKU【" + existSkuSet + "】已存在,请使用编辑功能");
                }
            }

            String brandName = dto.getBrandName();
            ShopBrand brand = shopBrandService.selectByBrandName(brandName);
            if (brand == null) {
                brand = new ShopBrand();
                brand.setBrandName(brandName);
                shopBrandService.saveShopBrand(brand);
            }
        });

        List<String> createSkuList = new ArrayList<>();
        spuGoodsSkuList.forEach(sgs -> {
            SupplierGoodsReleaseDto d = new SupplierGoodsReleaseDto();
            BeanUtil.copyProperties(dto, d);
            BeanUtil.copyProperties(sgs, d);
            createSkuList.add(saveShopGoods(d, supplierCode));
        });

        if (spuGoodsSkuList.size() > 1) {
            TenantUtils.execute(tenantIdsList, () -> {
                String sameCode = CodeGeneral.getProductCodeEx("SA");
                List<ShopGoods> createGoods = baseMapper.getBySkuInAndSupplier(createSkuList, supplierCode);
                List<ShopGoodsSameEntity> batchInsertSame = new ArrayList<>();
                createGoods.forEach(goods -> {
                    ShopGoodsSameEntity insertSame = new ShopGoodsSameEntity();
                    insertSame.setSameCode(sameCode);
                    insertSame.setGoodsId(goods.getGoodsId());
                    insertSame.setGoodsCode(goods.getGoodsCode());
                    insertSame.setGoodsSku(goods.getGoodsSku());
                    insertSame.setSupplierCode(goods.getSupplierCode());
                    batchInsertSame.add(insertSame);
                });
                goodsSameMapper.insertBatch(batchInsertSame);
            });
        }
    }

    public List<SpuOtherGoodsVo> querySpuOtherGoods(String goodsCode) {
        ShopGoodsSameEntity goodsSame = goodsSameMapper.getOneByGoodsCode(goodsCode);
        if (goodsSame == null) {
            return Collections.emptyList();
        }
        return goodsSameMapper.querySpuOtherGoods(goodsSame.getSameCode(), goodsCode);
    }

    public String toSaveShopGoods(SupplierGoodsReleaseDto params) {
        ShopGoods shop_goods = GoodsConvert.INSTANCE.convertGoodsRelease(params);
        Long currentTenantId = TenantContextHolder.getTenantId();

        // 基础校验
        ShopSupplier shopSupplier = _baseInfo(params, shop_goods, currentTenantId);

        // 设置 3 级分类
        _setGoodsClass(params, currentTenantId, shop_goods);

        this.setIntroduce(shop_goods, params.getGoodsBody());

        // 供应商档案配置不审批或者曾经已经审批通过的,无需审批,其他都需要审批
        if (params.getIsImportGoods() == 1) {
            // 批量导入直接审批通过
            shop_goods.setAuditState(1);
        } else {
            shop_goods.setAuditState(shopSupplier.getIsAudited() == 0
                    || (shop_goods.getAuditState() != null && shop_goods.getAuditState() == 1) ? 1 : 0);
        }

        goodsSrv.saveOrUpdate(shop_goods);

        if(shop_goods.getShelvesState() == -2){
            //先删除商品合同关系
            goodsContractService.getBaseMapper().deleteByGoodsId(shop_goods.getGoodsId());
            //删除价格策略
            yphGoodsPriceStrategyService.getBaseMapper().deleteByGoodsId(String.valueOf(shop_goods.getGoodsId()));
        }

        // 保存价格信息
        _computePrice(params, shop_goods, shopSupplier);

        // 保存商品详情信息
        ShopGoodsDetail shopGoodsDetail = _saveGoodsDetail(params, shop_goods, shopSupplier);

        // 存储附加信息
        _saveAdditionInfo(params, shop_goods);

        // SRM供应商不适用库存
        // 保存商品库存信息
        _saveGoodsStock(params, shopSupplier, shop_goods);

        // 保存同款商品，如有
        _saveGoodsSame(shop_goods, shopGoodsDetail, params.getSameGoodsSku());

        //保存商品与合同关系
        goodsContractService.saveGoodsContract(params.getSaveGoodsContactDto(), shopSupplier.getSupplierId(), shop_goods);

        //上架模式才
        if(shop_goods.getShelvesState() >= -1){
            if (shop_goods.getAuditState() == 1) {
                // 判断是否要创建企业审批 非电商处理
                workFlowManager.goodsContractOperation(CollectionUtil.newArrayList(shop_goods.getGoodsId()), CollectionUtil.newArrayList(shop_goods), false,
                        CollectionUtil.isEmpty(params.getSaveGoodsContactDto()) ? new HashMap<>() :
                                params.getSaveGoodsContactDto().stream().collect(Collectors.toMap(SaveGoodsContractDto::getContractId, SaveGoodsContractDto::getIsAudit)));
                //上架
                goodsUpDownManage.goodsIdUpToLiteFlow(CollectionUtil.newArrayList(shop_goods.getGoodsId()));
            } else {
                submitShelvesEstimate(shopSupplier, shop_goods.getGoodsId(), shop_goods, CollectionUtil.isEmpty(params.getSaveGoodsContactDto()) ? new HashMap<>() :
                        params.getSaveGoodsContactDto().stream().collect(Collectors.toMap(SaveGoodsContractDto::getContractId, SaveGoodsContractDto::getIsAudit)));
            }
            supplierGoodsShelvesDownService.saveShelvesDownLog(new ArrayList<>(Collections.singletonList(shop_goods.getGoodsCode())), GoodsChangeLogEnum.GOODS_UP.getCode(), "商品发布");

            // 独立供应商-新增商品-同款竞价-需要保存竞价数据
            _toMakeBiddingRecord(shopSupplier, params, shop_goods, shopGoodsDetail);
        }

        return "保存成功";
    }

    private void submitShelvesEstimate(ShopSupplier shopSupplier, Long goodsId, ShopGoods shop_goods, Map<Long, Integer> contractMap) {
        //电商/接口平台
        if (openApiConfig.getSuppliers().contains(shopSupplier.getSupplierCode()) || shopSupplier.getSupplierType() == 0) {
            workFlowManager.submitShelvesPlatform(CollectionUtil.toList(Math.toIntExact(goodsId)));
        } else {
            workFlowManager.goodsContractOperation(CollectionUtil.newArrayList(goodsId), CollectionUtil.newArrayList(shop_goods), true, contractMap);
        }
    }

    private void _saveGoodsStock(SupplierGoodsReleaseDto params, ShopSupplier shopSupplier, ShopGoods shop_goods) {
        if (!"srm".equals(shopSupplier.getDataSource())) {
            ShopGoodsStock shopGoodsStock = this.shopGoodsStockService.selectOneByGoodsCode(shop_goods.getGoodsCode());
            if (null == shopGoodsStock) {
                shopGoodsStock = new ShopGoodsStock();
            }
            shopGoodsStock.setGoodsSku(shop_goods.getGoodsSku());
            shopGoodsStock.setGoodsCode(shop_goods.getGoodsCode());
            shopGoodsStock.setStockAvailable(params.getStockAvailable());
            shopGoodsStock.setStockAlert(params.getGoodsMoq());
            shopGoodsStock.setOrganizationId(shopSupplier.getOrganizationId());
            this.shopGoodsStockService.saveOrUpdate(shopGoodsStock);
        }
    }

    private void _saveGoodsSame(ShopGoods goods, ShopGoodsDetail shopGoodsDetail, String sameGoodsSku) {
        if (StringHelper.IsEmptyOrNull(sameGoodsSku)) return;

        String sameCode = CodeGeneral.getProductCodeEx("SA");

        ShopGoodsSameEntity shopGoodsSameEntity = goodsSameMapper.selectOne(new LambdaQueryWrapperX<ShopGoodsSameEntity>()
                .eq(ShopGoodsSameEntity::getGoodsSku, sameGoodsSku)
                .eq(ShopGoodsSameEntity::getSupplierCode, goods.getSupplierCode()));
        List<ShopGoodsSameEntity> saveSameGoods = new ArrayList<>();

        Set<String> codes = new HashSet<>();
        codes.add(sameGoodsSku);
        if (shopGoodsSameEntity != null) {
            List<ShopGoodsSameEntity> same = goodsSameMapper.selectList(new LambdaQueryWrapperX<ShopGoodsSameEntity>()
                    .eq(ShopGoodsSameEntity::getSameCode, shopGoodsSameEntity.getSameCode()));

            codes.addAll(same.stream().map(ShopGoodsSameEntity::getGoodsCode).collect(Collectors.toList()));
        }
        List<ShopGoodsDetail> detailList = shopGoodsDetailService.getShopGoodsDetailByGoodsCodes(codes);
        List<String> specList = detailList.stream().map(ShopGoodsDetail::getGoodsSpecArray).collect(Collectors.toList());
        if (specList.contains(shopGoodsDetail.getGoodsSpecArray())) {
            throw new GoodsException("已存在同规格同属性的商品,请修改商品规格");
        }

        ShopGoodsSameEntity insertSame = new ShopGoodsSameEntity();
        insertSame.setSameCode(shopGoodsSameEntity == null ? sameCode : shopGoodsSameEntity.getSameCode());
        insertSame.setGoodsId(goods.getGoodsId());
        insertSame.setGoodsCode(goods.getGoodsCode());
        insertSame.setGoodsSku(goods.getGoodsSku());
        insertSame.setSupplierCode(goods.getSupplierCode());
        saveSameGoods.add(insertSame);
        if (Objects.isNull(shopGoodsSameEntity)) {
            ShopGoods oldGoods = goodsSrv.selectBySkuAndSupplier(sameGoodsSku, goods.getSupplierCode());
            if (Objects.isNull(oldGoods)) return;
            insertSame = new ShopGoodsSameEntity();
            insertSame.setSameCode(sameCode);
            insertSame.setGoodsId(oldGoods.getGoodsId());
            insertSame.setGoodsCode(oldGoods.getGoodsCode());
            insertSame.setGoodsSku(oldGoods.getGoodsSku());
            insertSame.setSupplierCode(goods.getSupplierCode());
            saveSameGoods.add(insertSame);
        }
        goodsSameMapper.insertBatch(saveSameGoods);
    }

    private void _saveAdditionInfo(SupplierGoodsReleaseDto params, ShopGoods shop_goods) {
        ShopGoodsIncidentalDetail shopGoodsIncidentalDetail = this.shopGoodsIncidentalDetailService.selectOneByGoodsCode(shop_goods.getGoodsCode());
        if (null == shopGoodsIncidentalDetail) {
            shopGoodsIncidentalDetail = new ShopGoodsIncidentalDetail();
            shopGoodsIncidentalDetail.setId(codeMaker.makeMysqlId());
        }
        BeanUtil.copyProperties(params, shopGoodsIncidentalDetail);
        shopGoodsIncidentalDetail.setGoodsSku(shop_goods.getGoodsSku());
        shopGoodsIncidentalDetail.setGoodsCode(shop_goods.getGoodsCode());
        this.shopGoodsIncidentalDetailService.saveOrUpdate(shopGoodsIncidentalDetail);
    }

    private ShopGoodsDetail _saveGoodsDetail(SupplierGoodsReleaseDto params, ShopGoods shop_goods, ShopSupplier shopSupplier) {
        ShopGoodsDetail shopGoodsDetail = this.shopGoodsDetailService.selectOneByGoodsCode(shop_goods.getGoodsCode());
        if (null == shopGoodsDetail) {
            shopGoodsDetail = new ShopGoodsDetail();
        }
        BeanUtil.copyProperties(params, shopGoodsDetail);
        shopGoodsDetail.setGoodsSku(shop_goods.getGoodsSku());
        shopGoodsDetail.setGoodsCode(shop_goods.getGoodsCode());
        JSONArray specArray = JSONObject.parseArray(params.getSpecArray());

        StringBuffer specStr = new StringBuffer();
        if (specArray != null) {
            for (int i = 0; i < specArray.size(); i++) {
                JSONObject jsonObject = specArray.getJSONObject(i);
                for (Map.Entry entry : jsonObject.entrySet()) {
                    specStr.append(entry.getKey()).append(" ").append(entry.getValue()).append(" ");
                }
            }
            shopGoodsDetail.setGoodsSpecArray(params.getSpecArray());
        }

        log.info("商品图片信息{}", params.getImageArray());
        if (params.getImageArray() != null && !params.getImageArray().isEmpty()) {
            List<String> images = new ArrayList<String>(Arrays.asList(params.getImageArray().split(";")));
            shopGoodsDetail.setGoodsImage(images.get(0));
            images.remove(0);
            shopGoodsDetail.setGoodsImageMore(Joiner.on(";").join(images));
        }

        shopGoodsDetail.setGoodsSpec(specStr.toString());
        shopGoodsDetail.setOrganizationId(shopSupplier.getOrganizationId());
        this.shopGoodsDetailService.saveOrUpdate(shopGoodsDetail);
        return shopGoodsDetail;
    }

    private void _computePrice(SupplierGoodsReleaseDto params, ShopGoods shop_goods, ShopSupplier shopSupplier) {
        if (CollectionUtil.isNotEmpty(params.getSaveGoodsContactDto())) {
            //计算含税价
            params.getSaveGoodsContactDto().forEach(dto -> {
                keepGoodsPrice(shop_goods, dto);
            });

            Optional<SaveGoodsContractDto> goodsContractDto = params.getSaveGoodsContactDto()
                    .stream().filter(dto -> OrganizationTypeEnum.SYSTEM.getCode().equals(dto.getOrgType()))
                    .findFirst();
            goodsContractDto.ifPresent(saveGoodsContractDto -> this.saveGoodsPrice(shop_goods, shopSupplier, saveGoodsContractDto));
            // 保存企业商品固定价格
            List<SaveGoodsContractDto> goodsStrategyArray = params.getSaveGoodsContactDto()
                    .stream().filter(dto -> OrganizationTypeEnum.PURCHASE.getCode().equals(dto.getOrgType())).collect(Collectors.toList());
            goodsSrv.saveGoodsStrategy(goodsStrategyArray, shop_goods);
        }
    }

    private void _setGoodsClass(SupplierGoodsReleaseDto params, Long currentTenantId, ShopGoods shop_goods) {
        String[] classCodes = params.getGoodsClassCode().split(",");
        // 查询一级分类
        YphStandardClassEntity yphStandardClassEntity = this.yphStandardClassService.selectByCode(classCodes[0]);
        if (yphStandardClassEntity == null) {
            throw new ParameterException("租户【" + currentTenantId + "】下的" + classCodes[0] + "分类不存在，请修改分类信息");
        }
        shop_goods.setFirstLevelGcid(String.valueOf(yphStandardClassEntity.getStandardClassId()));
        shop_goods.setFirstClass(yphStandardClassEntity.getClassCode());
        shop_goods.setFirstClassName(yphStandardClassEntity.getClassName());

        // 查询二级分类
        yphStandardClassEntity = this.yphStandardClassService.selectByCode(classCodes[1]);
        if (yphStandardClassEntity == null) {
            throw new ParameterException("租户【" + currentTenantId + "】下的" + classCodes[1] + "分类不存在，请修改分类信息");
        }
        shop_goods.setSecondLevelGcid(String.valueOf(yphStandardClassEntity.getStandardClassId()));
        shop_goods.setSecondClass(yphStandardClassEntity.getClassCode());
        shop_goods.setSecondClassName(yphStandardClassEntity.getClassName());

        //查询三级分类
        yphStandardClassEntity = this.yphStandardClassService.selectByCode(classCodes[2]);
        if (yphStandardClassEntity == null) {
            throw new ParameterException("租户【" + currentTenantId + "】下的" + classCodes[2] + "分类不存在，请修改分类信息");
        }
        shop_goods.setThirdLevelGcid(String.valueOf(yphStandardClassEntity.getStandardClassId()));
        shop_goods.setThirdClass(yphStandardClassEntity.getClassCode());
        shop_goods.setThirdClassName(yphStandardClassEntity.getClassName());

        shop_goods.setSupplierClass(yphStandardClassEntity.getClassCode());
        shop_goods.setSupplierClassId(String.valueOf(yphStandardClassEntity.getStandardClassId()));
        shop_goods.setSupplierClassName(yphStandardClassEntity.getClassName());
    }

    @NotNull
    private ShopSupplier _baseInfo(SupplierGoodsReleaseDto params, ShopGoods shop_goods, Long currentTenantId) {
        // 创建商品
        if (null == params.getGoodsId()) {
            // 如果已经提前生成了code 就不要再生成一遍了
            if (StrUtil.isBlank(params.getGoodsCode())) {
                // 改成新的编码规则
                String goodsCode = this.codeGeneral.getProductCodeEx("TR");
                shop_goods.setGoodsCode(goodsCode);
            }
            if (StringHelper.IsEmptyOrNull(shop_goods.getGoodsSku())) {
                // 未传sku，使用goodsCode作为sku
                shop_goods.setGoodsSku(shop_goods.getGoodsCode());
                params.setGoodsSku(shop_goods.getGoodsSku());
            } else {
                //去掉首位空格
                shop_goods.setGoodsSku(shop_goods.getGoodsSku().replaceAll("^[\\s\\u3000]+|[\\s\\u3000]+$", ""));
            }
            ShopGoods goods = this.selectOneBySkuAndSupplier(shop_goods.getGoodsSku(), shop_goods.getSupplierCode());
            if (null != goods) {
                throw new ParameterException("租户【" + currentTenantId + "】下的商品SKU已存在,请使用编辑功能");
            }
        }

        ShopBrand shopBrand = this.shopBrandService.selectByBrandName(params.getBrandName());
        if (null == shopBrand) {
            throw new ParameterException("租户【" + currentTenantId + "】下的商品品牌不存在，请联系[付冰 <EMAIL>]新增品牌");
        } else {
            shop_goods.setBrandId(shopBrand.getBrandId().toString());
        }

        //查询供应商信息
        ShopSupplier shopSupplier = supplierSrv.getBaseMapper().selectByCode(shop_goods.getSupplierCode());
        if (null == shopSupplier) {
            throw new GoodsException("租户【" + currentTenantId + "】下未查询到供应商信息");
        }

        shop_goods.setOrganizationId(shopSupplier.getOrganizationId());
        shop_goods.setSupplierCode(shopSupplier.getSupplierCode());
        shop_goods.setSupplierType(shopSupplier.getSupplierType());
        shop_goods.setSupplierName(shopSupplier.getSupplierShortName());
        shop_goods.setDeliveryTime(params.getDeliveryTime());
        shop_goods.setShelvesState(params.getSubmitType() == 0 ? -1 : -2);
        shop_goods.setGoodsFrom(params.getGoodsFrom());
        shop_goods.setMaterialsCode(params.getMaterialsCode());
        shop_goods.setManufacturerMaterialNo(params.getMaterialsCode());
        return shopSupplier;
    }

    public void saveGoodsStrategy(List<SaveGoodsContractDto> goodsStrategy, ShopGoods goods) {
        goodsStrategy.forEach(dto -> {
            YphGoodsPriceStrategy goodsPriceStrategyEntity = new YphGoodsPriceStrategy();
            goodsPriceStrategyEntity.setCompanyOrganizationId(String.valueOf(dto.getCompanyOrgId()));
            goodsPriceStrategyEntity.setSupplierCode(goods.getSupplierCode());
            goodsPriceStrategyEntity.setChangeType(2);
            goodsPriceStrategyEntity.setRelationId(String.valueOf(goods.getGoodsId()));
            goodsPriceStrategyEntity.setRelationName(goods.getGoodsDesc());
            goodsPriceStrategyEntity.setGoodsSku(goods.getGoodsSku());
            goodsPriceStrategyEntity.setStrategyName("供应商指定商品价格");
            goodsPriceStrategyEntity.setGoodsPactPrice(dto.getGoodsPactPrice());
            goodsPriceStrategyEntity.setGoodsPactNakedPrice(dto.getGoodsPactNakedPrice());
            goodsPriceStrategyEntity.setBeforePrice(dto.getGoodsOriginalPrice());
            goodsPriceStrategyEntity.setAfterPrice(dto.getGoodsPlatformPrice());
            goodsPriceStrategyEntity.setIsFixed(1);
            goodsPriceStrategyEntity.setValidityStart(dto.getValidityStart());
            goodsPriceStrategyEntity.setValidityEnd(dto.getValidityEnd());
            goodsPriceStrategyEntity.setIsEnable("0");
            yphGoodsPriceStrategyService.saveGoodsPriceStrategy(goodsPriceStrategyEntity, new YphGoodsPriceStrategyLog(), String.valueOf(dto.getCompanyOrgId()));
        });
    }

    public void keepGoodsPrice(ShopGoods goods, SaveGoodsContractDto goodsSettlementDto) {
        BigDecimal taxRate = new BigDecimal(goods.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));
        //供货价含税协议价
        BigDecimal goodsPactPrice = goodsSettlementDto.getGoodsPactNakedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
        goodsSettlementDto.setGoodsPactPrice(goodsPactPrice);

        //商品原价
        BigDecimal goodsOriginalPrice = goodsSettlementDto.getGoodsOriginalNakedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
        goodsSettlementDto.setGoodsOriginalPrice(goodsOriginalPrice);

        //平台销售价
        BigDecimal goodsPlatformPrice = goodsSettlementDto.getGoodsPlatformNakedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
        goodsSettlementDto.setGoodsPlatformPrice(goodsPlatformPrice);

    }

    public void saveGoodsPrice(ShopGoods goods, ShopSupplier supplier, SaveGoodsContractDto goodsSettlementDto) {
        ShopGoodsPrice shopGoodsPrice = this.priceSrv.selectOneByGoodsCode(goods.getGoodsCode());
        if (null == shopGoodsPrice) {
            shopGoodsPrice = new ShopGoodsPrice();
        }
        shopGoodsPrice.setGoodsCode(goods.getGoodsCode());
        shopGoodsPrice.setGoodsPactPrice(goodsSettlementDto.getGoodsPactPrice());
        shopGoodsPrice.setGoodsPactNakedPrice(goodsSettlementDto.getGoodsPactNakedPrice());
        shopGoodsPrice.setGoodsOriginalPrice(goodsSettlementDto.getGoodsOriginalPrice());
        shopGoodsPrice.setGoodsOriginalNakedPrice(goodsSettlementDto.getGoodsOriginalNakedPrice());
        BeanUtil.copyProperties(goodsSettlementDto, shopGoodsPrice);
        shopGoodsPrice.setGoodsSalePrice(goodsSettlementDto.getGoodsPlatformPrice());
        shopGoodsPrice.setGoodsSaleNakedPrice(goodsSettlementDto.getGoodsPlatformNakedPrice());
        if (!StringHelper.IsEmptyOrNull(goodsSettlementDto.getValidityStart()) && !StringHelper.IsEmptyOrNull(goodsSettlementDto.getValidityEnd())) {
            shopGoodsPrice.setValidityStart(goodsSettlementDto.getValidityStart());
            shopGoodsPrice.setValidityEnd(goodsSettlementDto.getValidityEnd());
        }
        shopGoodsPrice.setGoodsSku(goods.getGoodsSku());
        shopGoodsPrice.setOrganizationId(supplier.getOrganizationId());
        this.priceSrv.saveOrUpdateItem(shopGoodsPrice);

        this.syncTenantGoodsPrice(goods,shopGoodsPrice);

    }

    public void getStandardClassCode(SupplierGoodsReleaseDto goods, String spliter) {
        String[] classIds = goods.getGoodsClass().split(spliter);
        List<String> classCodeList = new ArrayList<>();
        for (int i = 0; i < classIds.length; i++) {
            QueryWrapper<YphStandardClassEntity> standardClassQueryWrapper = new QueryWrapper<>();
            standardClassQueryWrapper.lambda().eq(YphStandardClassEntity::getStandardClassId, classIds[i]);
            YphStandardClassEntity yphStandardClassEntity = this.yphStandardClassService.getOne(standardClassQueryWrapper);
            if (yphStandardClassEntity == null) {
                throw new ParameterException(classIds[i] + "分类不存在，请修改分类信息");
            }
            classCodeList.add(yphStandardClassEntity.getClassCode());
        }
        goods.setGoodsClassCode(String.join(",", classCodeList));
    }

    /**
     * 电商提交商品审批
     */
    public void supplierSubmitGoods(Collection<ShopGoods> goods) {
        workFlowManager.submitShelves(goods.stream().filter(item -> item.getAuditState() == 0)
                .map(item -> Math.toIntExact(item.getGoodsId()))
                .collect(Collectors.toList())
        );
    }

    /**
     * 商品编辑
     *
     * @param supplierGoodsUpdateDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateShopGoods(SupplierGoodsUpdateDto supplierGoodsUpdateDto) {
        ShopGoods shopGoods = new ShopGoods();
        shopGoods.setGoodsId(supplierGoodsUpdateDto.getGoodsId());
        // 找到品牌信息
        if (supplierGoodsUpdateDto.getBrandId() != null) {
            ShopBrand shopBrand = shopBrandService.selectById(supplierGoodsUpdateDto.getBrandId());
            if (shopBrand == null) {
                throw new ParameterException("品牌信息不存在");
            }
            shopGoods.setBrandId(shopBrand.getBrandId().toString());
            shopGoods.setBrandName(shopBrand.getBrandName());
        }

        // 处理分类信息
        if (supplierGoodsUpdateDto.getClassId() != null) {
            YphStandardClassEntity goodsClass = yphStandardClassService.getById(supplierGoodsUpdateDto.getClassId());
            if (goodsClass == null) {
                throw new ParameterException("分类信息不存在");
            }
            List<String> classIds = Arrays.stream(goodsClass.getClassPath().split(",")).collect(Collectors.toList());
            changeGoodsClass(supplierGoodsUpdateDto.getSupplierCode(), classIds, shopGoods);
        }
        // 找到供应商信息
        if (StrUtil.isNotBlank(supplierGoodsUpdateDto.getSupplierCode())) {
            ShopSupplier shopSupplier = supplierSrv.selectByCode(supplierGoodsUpdateDto.getSupplierCode());
            if (shopSupplier == null) {
                throw new ParameterException("供应商信息不存在");
            }
            shopGoods.setSupplierType(shopSupplier.getSupplierType());
            shopGoods.setSupplierName(shopSupplier.getSupplierFullName());
            shopGoods.setSupplierCode(shopSupplier.getSupplierCode());
        }

        // 详情
        shopGoods.setGoodsSku(supplierGoodsUpdateDto.getGoodsSku());
        shopGoods.setGoodsKeywords(supplierGoodsUpdateDto.getGoodsKeyword());
        shopGoods.setGoodsDesc(supplierGoodsUpdateDto.getGoodsDesc());
        shopGoods.setDeliveryTime(supplierGoodsUpdateDto.getDeliveryTime());
        shopGoods.setSaleUnit(supplierGoodsUpdateDto.getSaleUtil());
        shopGoods.setSpecGoodsWareQd(supplierGoodsUpdateDto.getSpecGoodsWareQd());
        shopGoods.setManufacturerMaterialNo(supplierGoodsUpdateDto.getModel());
        shopGoods.setMaterialsCode(supplierGoodsUpdateDto.getModel());
        UpdateWrapper<ShopGoods> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ShopGoods::getGoodsCode, supplierGoodsUpdateDto.getGoodsCode());
        this.update(shopGoods, updateWrapper);

        // 详情
        ShopGoodsDetail shopGoodsDetail = new ShopGoodsDetail();
        shopGoodsDetail.setGoodsSpecArray(supplierGoodsUpdateDto.getSpecArray());
        shopGoodsDetail.setGoodsMoq(supplierGoodsUpdateDto.getMoq());
        UpdateWrapper<ShopGoodsDetail> goodsDetailUpdateWrapper = new UpdateWrapper<>();
        goodsDetailUpdateWrapper.lambda().eq(ShopGoodsDetail::getGoodsCode, supplierGoodsUpdateDto.getGoodsCode());
        this.shopGoodsDetailService.update(shopGoodsDetail, goodsDetailUpdateWrapper);

        // 库存
        ShopGoodsStock stock = new ShopGoodsStock();
        stock.setStockAvailable(supplierGoodsUpdateDto.getStock());
        UpdateWrapper<ShopGoodsStock> query = new UpdateWrapper<>();
        query.lambda().eq(ShopGoodsStock::getGoodsCode, supplierGoodsUpdateDto.getGoodsCode());
        this.shopGoodsStockService.update(stock, query);

        ShopGoodsIncidentalDetail shopGoodsIncidentalDetail = shopGoodsIncidentalDetailService.selectOneByGoodsCode(supplierGoodsUpdateDto.getGoodsCode());
        if (Objects.isNull(shopGoodsIncidentalDetail)) {
            shopGoodsIncidentalDetail = new ShopGoodsIncidentalDetail();
        }
        shopGoodsIncidentalDetail.setFileUrl(supplierGoodsUpdateDto.getFileUrl());
        shopGoodsIncidentalDetail.setGoodsSource(supplierGoodsUpdateDto.getGoodsSource());
        shopGoodsIncidentalDetail.setSeekPriceNumbers(supplierGoodsUpdateDto.getSeekPriceNumbers());
        shopGoodsIncidentalDetail.setMarketReference(supplierGoodsUpdateDto.getMarketReference());
        UpdateWrapper<ShopGoodsIncidentalDetail> incidentalDetailUpdateWrapper = new UpdateWrapper<>();
        incidentalDetailUpdateWrapper.lambda().eq(ShopGoodsIncidentalDetail::getGoodsCode, supplierGoodsUpdateDto.getGoodsCode());
        this.shopGoodsIncidentalDetailService.saveOrUpdate(shopGoodsIncidentalDetail, incidentalDetailUpdateWrapper);

        ShopGoodsEditRecordEntity editRecord = new ShopGoodsEditRecordEntity();
        editRecord.setGoodsId(shopGoods.getGoodsId());
        editRecord.setEditCertificateFile(supplierGoodsUpdateDto.getEditCertificateFile());
        editRecord.setUserId(LocalUserHolder.get().getId());
        editRecord.setNickName(LocalUserHolder.get().getNickname());
        editRecord.setOrganizationId(LocalUserHolder.get().getOrganizationId());
        goodsEditRecordMapper.insert(editRecord);

        ShopGoods sgs = this.selectOneByGoodsCode(supplierGoodsUpdateDto.getGoodsCode());
        goodsEsProcessor.updateIndex(CollectionUtil.toList(sgs.getGoodsId()), EsUpdateType.ALL);
    }

    private ShopBrand validateBrand(SupplierGoodsUpdateDto supplierGoodsUpdateDto) {
        ShopBrand shopBrand = shopBrandService.selectById(supplierGoodsUpdateDto.getBrandId());
        if (shopBrand == null) {
            throw new ParameterException("品牌信息不存在");
        }
        return shopBrand;
    }

    /**
     * 查询商品的上架信息
     *
     * @param g_code 商品编码
     * @return 商品的上架信息
     * @throws GoodsException 商品无价格的异常
     */
    public ShopGoodsUpInfoVo queryGoodsUpInfo(String g_code) {
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        // 获取远程价格信息
        var rmt_price = priceGetter.getSalePrice(g_code);
        if (rmt_price == null || rmt_price.getGoodsSalePrice() == null || rmt_price.getGoodsSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
            // 如果价格为0，则抛出异常
            throw new GoodsException("商品[" + g_code + "]无价格");
        }
        // 构造返回值对象
        var rtn = new ShopGoodsUpInfoVo();
        rtn.setGoodsPactPrice(rmt_price.getGoodsPactPrice());
        rtn.setGoodsOriginalPrice(rmt_price.getGoodsOriginalPrice());

        // 设置折扣
        BigDecimal discount = rmt_price.getGoodsPactPrice().divide(rmt_price.getGoodsOriginalPrice(), 2, RoundingMode.HALF_UP);
        rtn.setDiscount(discount.multiply(new BigDecimal(100)));

        // 查询商品信息
        ShopGoods goods = this.baseMapper.selectOne(ShopGoods::getGoodsCode, g_code);

        // 查询
        List<DfmallGoodsPoolSubEntity> dfmallGoodsPoolSubList = dfmallGoodsPoolSubService.queryGoodsPools(String.valueOf(goods.getGoodsId()), goods.getThirdLevelGcid(), goods.getSupplierCode(), null);

        // 根据公司id和货品编码获取远程调用价格信息
        RemotePriceInfoResp priceInfoResp = priceGetter.getRemotePrice(goods);


        List<ShopGoodsUpCompanyVo> companyVoList = new ArrayList<>();
        List<Long> poolLevel = new ArrayList<>();
        poolLevel.add(1L);
        poolLevel.add(2L);
        //查询商品所存在的平台级以及企业级商品池，以及他们关联的企业
        List<GoodsRelationPoolVo> goodsRelationPoolVos = dfmallGoodsPoolSubService.queryGoodsRelationPool(goods.getGoodsId(), poolLevel);
        goodsRelationPoolVos.forEach(goodsRelationPoolVo -> this.toAddCompanyGoodsItem(goodsRelationPoolVo.getCompanyCode(),
                goodsRelationPoolVo.getGoodsPoolId(),
                goods.getTenantId(),
                goods, companyVoList, priceInfoResp));
        rtn.setGoodsUpCompanyVoList(companyVoList);
        return rtn;
    }

    private void toAddCompanyGoodsItem(String companyCode, Long goodsPoolId, Long tenantId, ShopGoods goods,
                                       List<ShopGoodsUpCompanyVo> companyVoList, RemotePriceInfoResp priceInfoResp) {


        RemotePriceInfoResp priceInfoRespTemp = new RemotePriceInfoResp();
        BeanUtil.copyProperties(priceInfoResp, priceInfoRespTemp);
        TenantUtils.execute(tenantId, () -> {
            var goods_info = this.getShopGoodsUpCompanyVo(priceInfoRespTemp, companyCode, goods);
            if (goods_info != null) {
                goods_info.setShelvesState(1);
                goods_info.setGoodsPoolId(goodsPoolId);
                companyVoList.add(goods_info);
            }
        });
    }


    /**
     * 根据公司id和货品编码获取商品上架信息
     *
     * @param remotePriceInfoResp 远程调用价格信息
     * @param companyCode         公司 id
     * @param shopGoods           商品编码
     * @return 商品上架信息
     */
    public ShopGoodsUpCompanyVo getShopGoodsUpCompanyVo(RemotePriceInfoResp remotePriceInfoResp, String companyCode, ShopGoods shopGoods) {

        // 根据公司id获取公司信息
        SystemOrganization org = this.organizationService.getOrganizationByCode(companyCode);

        // 公司不存在则抛出异常
        if (null == org) {
            log.error("company: [{}]不存在", companyCode);
            return null;
        }

        final GoodsSalePriceQueryDto goodsSalePriceQueryDto = RemoteInfoManage.getGoodsSalePriceQueryDto(shopGoods);
        goodsSalePriceQueryDto.setOrganizationId(String.valueOf(org.getId()));
        remotePriceInfoResp = this.rmtMng.getSalePrice(remotePriceInfoResp, goodsSalePriceQueryDto);

        // 封装商品上架信息
        ShopGoodsUpCompanyVo result = new ShopGoodsUpCompanyVo();
        result.setCompanyOrgId(String.valueOf(org.getId()));
        result.setCompanyCode(org.getCode());
        result.setCompanyName(org.getName());
        result.setGoodsSalePrice(remotePriceInfoResp.getGoodsSalePrice());

        // 计算公司折扣率
        BigDecimal companyDiscount = remotePriceInfoResp.getGoodsSalePrice().divide(remotePriceInfoResp.getGoodsOriginalPrice(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        result.setCompanyDiscount(companyDiscount);

        // 计算加价率
        if (remotePriceInfoResp.getAddRate() == null) {
            remotePriceInfoResp.setAddRate(remotePriceInfoResp.getGoodsSalePrice().divide(remotePriceInfoResp.getGoodsPactPrice(), 4, RoundingMode.HALF_UP));
        }
        BigDecimal addRate = remotePriceInfoResp.getAddRate().multiply(new BigDecimal(100)).subtract(new BigDecimal(100));
        result.setAddRate(addRate);

        return result;
    }

    /**
     * 商品上架
     *
     * @param goodsIdList 要上架的商品
     */
    @DataPermission(enable = false)
    public Map<String, String> shopGoodsIdUp(List<Long> goodsIdList) {
        Long tid = TenantContextHolder.getRequiredTenantId();
        ConcurrentHashMap<String, String> result = new ConcurrentHashMap<>();
        TenantUtils.execute(tid, () -> goodsUpDownManage.goodsIdUpToLiteFlow(goodsIdList));
        return result;
    }

    /**
     * 电商推送上架
     *
     * @param goodsCodes  商品编码
     * @param goodsPoolId 商品池ID
     */
    public void shopGoodsCodeUp(List<String> goodsCodes, Long goodsPoolId) {
        goodsUpDownManage.goodsCodeUpToLiteFlow(goodsCodes);
    }

    /**
     * 电商推送下架
     *
     * @param goodsCodes 商品编码
     * @param downReason 下架原因
     */
    public void shopGoodsDown(List<String> goodsCodes, String downReason) {
        goodsUpDownManage.goodsCodeDown(goodsCodes, downReason, null);
    }

    /**
     * 商品手动上架
     * 上架失败，需要返回失败原因
     * 将下架表中的数据删除，并调用es 将商品加入索引
     * o
     *
     * @param upDto 上架的商品列表
     */
    public Map<String, String> shopGoodsManualUp(ShopGoodsUpDownDto upDto) {
        ConcurrentHashMap<String, String> result = new ConcurrentHashMap<>();
        Long tid = TenantContextHolder.getRequiredTenantId();
        TenantUtils.execute(tid, () -> {
            List<Long> ids = Arrays.stream(upDto.getGoodsIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            goodsUpDownManage.goodsUpToManual(ids, null, "平台手动操作");
        });
        return result;
    }

    private void saveErrorInfo(ShopGoods shopGoods, RemotePriceInfoResp remotePriceInfoResp, String desc) {
        SyncDetailInfo syncDetailInfo = new SyncDetailInfo();
        syncDetailInfo.setSupplierCode(shopGoods.getSupplierCode());
        syncDetailInfo.setSupplierName(shopGoods.getSupplierName());
        syncDetailInfo.setTargetId(shopGoods.getGoodsId().intValue());
        syncDetailInfo.setTargetSku(shopGoods.getGoodsSku());
        syncDetailInfo.setTargetName(shopGoods.getGoodsName());
        syncDetailInfo.setSyncDateTime(LocalDateTime.now());
        syncDetailInfo.setDuration(0);
        syncDetailInfo.setIsSuccess(false);
        syncDetailInfo.setErrorValidatorId(100);
        syncDetailInfo.setErrorColumn("price");
        syncDetailInfo.setErrorColumnData(Convert.toStr(remotePriceInfoResp.getGoodsSalePrice()));
        syncDetailInfo.setProcessFlag((byte) 0);
        syncDetailInfo.setValidateTypeCode(999);
        syncDetailInfo.setErrorValidatorDesc(desc);
        syncDetailInfo.setErrorValidatorName(desc);
        syncDetailInfoService.save(syncDetailInfo);
    }

    /**
     * 商品信息查询列表-分页
     *
     * @param pageReq
     * @param shopGoodsQueryDto
     */
    public PageResp<ShopGoodsVo> queryGoodsPageVo(PageReq pageReq, ShopGoodsQueryDto shopGoodsQueryDto) {
        IPage<ShopGoodsVo> shopGoodsVoPage = this.baseMapper.queryGoodsPage(DataAdapter.adapterPageReq(pageReq), shopGoodsQueryDto);
        if (shopGoodsQueryDto.getApplicationRecord() == 1) {
            List<ShopGoodsVo> records = shopGoodsVoPage.getRecords();
            //查询商品是否有审批通过的记录
            records.forEach(e -> {
                String busKey = e.getGoodsId() + "--";
                List<ShopGoodsActInfoVo> shopGoodsActInfoVos = this.baseMapper.queryActByGoodsId(busKey);
                if (CollectionUtil.isNotEmpty(shopGoodsActInfoVos) && CollectionUtil.isNotEmpty(shopGoodsActInfoVos.stream().filter(item -> item.getInstanceState() == 0).collect(Collectors.toList()))) {
                    e.setActAuditState(1);
                    log.info("商品审批记录:{}", shopGoodsActInfoVos);
                } else {
                    e.setActAuditState(0);
                }
            });
        }
        return DataAdapter.adapterPage(shopGoodsVoPage, ShopGoodsVo.class);
    }

    public ServiceResult<?> favoritesList() {
        LoginUser user = LocalUserHolder.get();
        val shopGoodsVoPage = this.baseMapper.queryGoodsPages(user.getId());
        List<ShopFavoritesVo> goodsList = new ArrayList<>();
        shopGoodsVoPage.forEach(itm -> {
            //查询加价后价格
            try {
                RemotePriceInfoResp remotePriceInfoResp = priceGetter.getSalePrice(itm.getSupplierCode(), itm.getGoodsSku());
                itm.setGoodsPactPrice(remotePriceInfoResp.getGoodsSalePrice());
                itm.setMsg(remotePriceInfoResp.getMsg());
                itm.setMsgType(remotePriceInfoResp.getMsgType());
            } catch (Exception e) {
                itm.setGoodsPactPrice(BigDecimal.ZERO);
                log.error(e.getMessage());
            }
            goodsList.add(itm);
        });
        HashMap<String, Object> map = new HashMap<>();
        map.put("shopGoodsVoPage", goodsList);
        return ServiceResult.succ(map);
    }

    public void querySupplierGoodsExport(HttpServletResponse response, GoodsManageQueryDto queryDto) throws IOException {
        LoginUser loginUser = LocalUserHolder.get();
        if (loginUser.getOrganizationType().equals(OrganizationTypeEnum.SUPPLIER.getCode())) {
            queryDto.setSupplierCode(loginUser.getEntityOrganizationCode());
        }
        if (StrUtil.isNotBlank(queryDto.getSupplierCode())) {
            ShopSupplier supplier = supplierSrv.selectByCode(queryDto.getSupplierCode());
            queryDto.setSupplierType(supplier.getSupplierType());
        }
        Integer limitCount = baseMapper.queryGoodsPoolVoPage_c(queryDto);
        if (limitCount > 10000) {
            throw new ParameterException("导出数据过大,联系平台运营协助");
        }
        try (ExcelWriter writer = ExcelUtil.getWriter(); ServletOutputStream out = response.getOutputStream()) {
            List<ShopGoodsVo> shopGoodsList = this.baseMapper.querySupplierGoodsExport(queryDto);
            writer.writeHeadRow(CollUtil.newArrayList("商品编码", "商城分类", "商品描述", "商品SKU", "供应商名称", "上架状态", "审核状态"));
            List<List<String>> rows = new ArrayList<>();
            shopGoodsList.forEach(data -> {
                try {
                    String goodsCode = data.getGoodsCode();
                    String goodsDesc = data.getGoodsDesc();
                    String goodsSku = data.getGoodsSku();
                    String supplierName = data.getSupplierName();
                    String goodsClassNames = data.getGoodsClassNames();
                    String auditState = "";
                    switch (data.getAuditState()) {
                        case 0:
                            auditState = "待审核";
                            break;
                        case 1:
                            auditState = "审核通过";
                            break;
                        case 2:
                            auditState = "审核驳回";
                            break;
                    }
                    String upSate = data.getShelvesState() == 1 ? "上架" : "下架";

                    rows.add(ListUtil.toList(goodsCode, goodsClassNames, goodsDesc, goodsSku, supplierName, upSate, auditState));
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("输出excel数据异常" + e.getMessage());
                }

            });
            writer.write(rows);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("供应商商品信息.xlsx", "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        }
    }

    /**
     * 商品池管理分页查询
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@link PageResp}<{@link ShopGoods}>
     */
    public PageResp<GoodsManageVo> queryGoodsPoolVoPage(PageReq pageReq, GoodsManageQueryDto queryDto) {
        LoginUser loginUser = LocalUserHolder.get();
        if (pageReq.getCurPage() < 1) {
            pageReq.setCurPage(1);
        }

        if (loginUser.getOrganizationType().equals(OrganizationTypeEnum.SUPPLIER.getCode())) {
            queryDto.setSupplierCode(loginUser.getEntityOrganizationCode());
        }

        if (Objects.nonNull(queryDto.getGoodsSku()) && queryDto.getGoodsSku().split(",").length > 50) {
            throw new ParameterException("sku查询数量不能超过50个");
        }
        if (Objects.nonNull(queryDto.getGoodsCode()) && queryDto.getGoodsCode().split(",").length > 50) {
            throw new ParameterException("商品编码查询数量不能超过50个");
        }

        if (StrUtil.isNotBlank(queryDto.getSupplierCode())) {
            ShopSupplier supplier = supplierSrv.selectByCode(queryDto.getSupplierCode());
            queryDto.setSupplierType(supplier.getSupplierType());
        }

        IPage<GoodsManageVo> page = null;
        if (queryDto.getGoodsState() == null || (queryDto.getGoodsState() != 4 && queryDto.getGoodsState() != 0)) {
            page = baseMapper.queryGoodsPoolVoPage(DataAdapter.adapterPageReq(pageReq), queryDto);
            page.getRecords().forEach(goodsManageVo -> {
                if (1 == goodsManageVo.getSupplierType()) {
                    List<GoodsContractApprovalVo> approvalVos = this.queryGoodsContractApproval(goodsManageVo.getGoodsId());
                    if (CollectionUtil.isNotEmpty(approvalVos)) {
                        long approvalCount = approvalVos.stream()
                                .filter(approval -> Objects.nonNull(approval.getApprovalState()) && approval.getApprovalState() == 1 && approval.getIsStart() == 1)
                                .count();
                        goodsManageVo.setAuditState(approvalCount > 0 ? 0 : goodsManageVo.getAuditState());

                        Map<Integer, Long> stateCounts = approvalVos.stream().filter(approval -> Objects.nonNull(approval.getApprovalState()))
                                .collect(Collectors.groupingBy(
                                        GoodsContractApprovalVo::getApprovalState,
                                        Collectors.counting()
                                ));

                        long approval = stateCounts.getOrDefault(0, 0L);
                        long wait = stateCounts.getOrDefault(1, 0L);
                        long refuse = stateCounts.getOrDefault(2, 0L);

                        List<String> auditDesc = new ArrayList<>();
                        auditDesc.add("审批通过:" + approval);
                        auditDesc.add("审批中:" + wait);
                        auditDesc.add("审批驳回:" + refuse);
                        goodsManageVo.setAuditStateDesc(String.join(";", auditDesc));
                    }
                }
                if("GYSYZHVIRTUAL".equalsIgnoreCase(goodsManageVo.getSupplierCode())){
                    goodsManageVo.setVirtualSupplierType("1");
                }

            });
        }

        // 如果标准商品表没有数据,则查询供应商原始数据
//        if ((null == page || page.getRecords().isEmpty()) && !Objects.equals(queryDto.getSupplierType(), 1)) {
//            page = baseMapper.queryAllSupplierBackUpGoods(DataAdapter.adapterPageReq(pageReq), queryDto);
//        }
//
        if (page == null || page.getRecords().isEmpty()) {
            return new PageResp<GoodsManageVo>();
        }

        return DataAdapter.adapterPage(page, GoodsManageVo.class);
    }

    public List<JSONObject> queryGoodsLabelName(List<Long> goodsIds) {
        List<JSONObject> labelList = new ArrayList<>();
        List<ShopGoodsSaleLabelVo> labelVos = goodsSaleLabelService.queryLabelByGoodsIds(goodsIds);

        Map<Long, List<ShopGoodsSaleLabelVo>> goodsGroup = labelVos.stream().collect(Collectors.groupingBy(ShopGoodsSaleLabelVo::getGoodsId));

        for (Map.Entry<Long, List<ShopGoodsSaleLabelVo>> entry : goodsGroup.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("goodsId", entry.getKey());
            jsonObject.put("goodsLabel", null);
            jsonObject.put("goodsLabelCode", null);
            jsonObject.put("companyLabel", null);
            jsonObject.put("companyLabelCode", null);

            List<ShopGoodsSaleLabelVo> labels = entry.getValue();
            List<ShopGoodsSaleLabelVo> palatLabels = labels.stream().filter(label -> label.getOrganizationId() == 0).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(palatLabels)) {
                String goodsLabel = palatLabels.stream()
                        .map(label -> label.getLabelName() + "|" + label.getRemark())
                        .collect(Collectors.joining(","));
                jsonObject.put("goodsLabel", goodsLabel);
                List<JSONObject> goodsLabelCode = palatLabels.stream()
                        .map(label -> {
                            JSONObject jsonMap = new JSONObject();
                            jsonMap.put("labelName", label.getLabelName());
                            jsonMap.put("goodsLabel", label.getGoodsLabel());
                            jsonMap.put("remark", label.getRemark());
                            return jsonMap;
                        })
                        .collect(Collectors.toList());
                jsonObject.put("goodsLabelCode", goodsLabelCode);
            }
            List<ShopGoodsSaleLabelVo> companyLabel = labels.stream().filter(label -> label.getOrganizationId() != 0).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(companyLabel)) {
                String company = companyLabel.stream()
                        .map(label -> label.getLabelName() + "|" + label.getRemark())
                        .distinct().collect(Collectors.joining(","));
                jsonObject.put("companyLabel", company);
                List<JSONObject> companyLabelCode = palatLabels.stream()
                        .map(label -> {
                            JSONObject jsonMap = new JSONObject();
                            jsonMap.put("labelName", label.getLabelName());
                            jsonMap.put("goodsLabel", label.getGoodsLabel());
                            jsonMap.put("remark", label.getRemark());
                            return jsonMap;
                        })
                        .collect(Collectors.toList());
                jsonObject.put("companyLabelCode", companyLabelCode);
            }
            labelList.add(jsonObject);
        }


        return labelList;
    }


    public List<JSONObject> queryGoodsStandardState(List<String> goodsCodeList){
        return productMapper.queryStandardInfo(goodsCodeList).stream()
                .map(this::buildValidationResult)
                .collect(Collectors.toList());
    }

    private JSONObject buildValidationResult(StandardProduct product) {
        List<String> missingFields = Stream.of(
                        new AbstractMap.SimpleEntry<>("物料名称", product.getMaterialName()),
                        new AbstractMap.SimpleEntry<>("品牌信息", product.getBrand()),
                        new AbstractMap.SimpleEntry<>("商品型号", product.getModel()),
                        new AbstractMap.SimpleEntry<>("计量单位", product.getUnit())
                )
                .filter(entry -> StringUtils.isBlank(entry.getValue()))
                .map(entry -> entry.getKey() + "缺失")
                .collect(Collectors.toList());

        String stateDesc = missingFields.isEmpty()
                ? "校验通过"
                : "校验未通过:" + String.join(";", missingFields);

        return new JSONObject()
                .fluentPut("goodsCode", product.getGoodsCode())
                .fluentPut("standardState", stateDesc);
    }

    /**
     * 电商商品分页查询
     *
     * @param pageReq       页面请求
     * @param goodsQueryDto 商品查询dto
     * @return {@link PageResp}<{@link GoodsNetWorkVo}>
     */
    public PageResp<GoodsNetWorkVo> goodsNetWorkList(PageReq pageReq, SupplierShopGoodsQueryDto goodsQueryDto) {
        if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(goodsQueryDto.getSupplierCode())) {
            return this.backupJdGeneralGoodsService.queryJdGoodsPage(pageReq, goodsQueryDto);
        }
        switch (goodsQueryDto.getSupplierCode()) {
            case "DSZKH00":
                return this.backupZkhGoodsService.queryZkhGoodsPage(pageReq, goodsQueryDto);
            case "DSOFS00":
                return this.backupOfsGoodsService.queryOfsGoodsPage(pageReq, goodsQueryDto);
            case "DSQXKJ0":
                return this.backupQxGoodsService.queryQxGoodsPage(pageReq, goodsQueryDto);
            case "DSXY000":
                return this.backupXyGoodsService.queryXyGoodsPage(pageReq, goodsQueryDto);
            case "DSDLJS0":
                return this.backupDlGoodsService.queryDlGoodsPage(pageReq, goodsQueryDto);
            case "DSJD000":
                return this.backupJdGoodsService.queryJdGoodsPage(pageReq, goodsQueryDto);
            case "DSJD001":
                return this.backupJdIntegralGoodsService.queryJdGoodsPage(pageReq, goodsQueryDto);
            case "DSSNDQ0":
                return this.backupSnGoodsService.querySnGoodsPage(pageReq, goodsQueryDto);
            default:
                if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(goodsQueryDto.getSupplierCode())) {
                    return this.backupJdGeneralGoodsService.queryJdGoodsPage(pageReq, goodsQueryDto);
                }
                break;
        }
        return null;
    }

    /**
     * 查询商品主数据详情
     *
     * @param goodsCode 商品编码
     * @return {@link GoodsMasterDetailVo}
     */
    public GoodsMasterDetailVo queryGoodsMasterDetailByCode(String goodsCode) {
        var result = this.shopGoodsMapper.queryGoodsMasterDetailVo(goodsCode);
        if (null == result) {
            throw new NotFoundException("商品[" + goodsCode + "]不存在");
        }

        ShopGoodsPrice shopGoodsPrice = priceSrv.selectOneByGoodsCode(goodsCode);
        if (null != shopGoodsPrice) {
            result.setGoodsPlatformPrice(shopGoodsPrice.getGoodsSalePrice());
            result.setGoodsPactPrice(shopGoodsPrice.getGoodsPactPrice());
            result.setGoodsOriginalPrice(shopGoodsPrice.getGoodsOriginalPrice());

            result.setGoodsPlatformNakedPrice(shopGoodsPrice.getGoodsSaleNakedPrice());
            result.setGoodsPactNakedPrice(shopGoodsPrice.getGoodsPactNakedPrice());
            result.setGoodsOriginalNakedPrice(shopGoodsPrice.getGoodsOriginalNakedPrice());

            result.setValidityStart(shopGoodsPrice.getValidityStart());
            result.setValidityEnd(shopGoodsPrice.getValidityEnd());
            BigDecimal discount = shopGoodsPrice.getGoodsPactNakedPrice().divide(shopGoodsPrice.getGoodsOriginalNakedPrice(), 4, RoundingMode.HALF_UP);
            result.setAgreementDiscount(discount);
        }
        if (LocalUserHolder.get().getOrganizationType().equals(OrganizationTypeEnum.PURCHASE.getCode())) {
            YphGoodsPriceStrategy goodsPriceStrategy = yphGoodsPriceStrategyService.getPriceStrategyByCompany(LocalUserHolder.get().getEntityOrganizationId(), result.getGoodsId());
            if (null != goodsPriceStrategy) {
                result.setGoodsPlatformPrice(goodsPriceStrategy.getAfterPrice());
                result.setGoodsPactPrice(goodsPriceStrategy.getGoodsPactPrice());
                result.setGoodsOriginalPrice(goodsPriceStrategy.getBeforePrice());

                result.setGoodsPlatformNakedPrice(goodsPriceStrategy.getGoodsPactNakedPrice());
                result.setGoodsPactNakedPrice(goodsPriceStrategy.getGoodsPactNakedPrice());
                result.setGoodsOriginalNakedPrice(goodsPriceStrategy.getGoodsPactNakedPrice());

                result.setAgreementDiscount(BigDecimal.ONE);
            }
        }

        if (!StringHelper.IsEmptyOrNull(result.getGoodsBoydUrl())) {
            // todo: http请求统一管理
            String xmlStr = HttpUtil.get(result.getGoodsBoydUrl(), new HashMap<>());
            result.setGoodsBoydUrl(xmlStr);
        }
        //查询当前商品的上下架状态
        ShopGoods shopGoods = new ShopGoods();
        shopGoods.setGoodsId(result.getGoodsId());
        shopGoods.setThirdLevelGcid(result.getThirdLevelGcid());
        shopGoods.setSupplierCode(result.getSupplierCode());

        fillSupplierType(result);

        if (result.getSupplierType() == 1) {
            //独立供应商才有多规格数据
            List<ShopGoodsSameEntity> sameList = goodsSameMapper.selectList("same_code", result.getSameCode());
            if (CollectionUtil.isNotEmpty(sameList)) {
                String skus = sameList.stream().map(same -> same.getGoodsSku()).collect(Collectors.joining(","));
                result.setSameSkuArray(skus);
            }
        }

        //展示附加信息
        this.keepIncidentalDetail(result, goodsCode);

        if (result.getSupplierType() == 1) {
            List<WorkflowAuditRecord> auditRecordList = Optional.ofNullable(workFlowService.getAuditRecordList(goodsCode))
                    .orElse(Collections.emptyList());
            int freedom = auditRecordList.stream()
                    .anyMatch(w -> w.getStatus() == 1) ? 0 : 1;
            result.setFreedomEdit(freedom);
        }
        return result;
    }

    private void fillSupplierType(GoodsMasterDetailVo result) {
        ShopSupplier shopSupplier = supplierSrv.selectByCode(result.getSupplierCode());
        result.setSupplierType(shopSupplier.getSupplierType());

        if (openApiConfig.getSuppliers().contains(result.getSupplierCode())) {
            result.setSupplierType(1);
        }
    }

    public List<GoodsMasterDetailVo> queryGoodsMasterDetailByCodes(Collection<String> goodsCodes) {
        if (goodsCodes.isEmpty()) {
            return new ArrayList<>();
        }
        List<GoodsMasterDetailVo> result = new ArrayList<>();
        var temList = this.shopGoodsMapper.queryGoodsMasterDetailVos(goodsCodes);
        Map<String, GoodsMasterDetailVo> cmap = temList.stream().collect(Collectors.toMap(GoodsMasterDetailVo::getGoodsCode, Function.identity()));
        Map<Long, GoodsMasterDetailVo> imap = temList.stream().collect(Collectors.toMap(GoodsMasterDetailVo::getGoodsId, Function.identity()));

        List<YphGoodsPriceStrategy> strategies = yphGoodsPriceStrategyService.getBaseMapper().selectSimpleList(imap.keySet(), 1);
        Map<String, List<YphGoodsPriceStrategy>> strategyMap = strategies.stream().collect(Collectors.groupingBy(YphGoodsPriceStrategy::getRelationId));

        temList.forEach(item -> {
            List<YphGoodsPriceStrategy> yphGoodsPriceStrategyList = strategyMap.get(item.getGoodsId().toString());

            if (CollectionUtil.isNotEmpty(yphGoodsPriceStrategyList)) {
                item.setGoodsPlatformPrice(yphGoodsPriceStrategyList.get(0).getAfterPrice());
                item.setValidityStart(yphGoodsPriceStrategyList.get(0).getValidityStart());
                item.setValidityEnd(yphGoodsPriceStrategyList.get(0).getValidityEnd());
            }

            if (!StringHelper.IsEmptyOrNull(item.getGoodsBoydUrl())) {
                // todo: http请求统一管理
                String xmlStr = HttpUtil.get(item.getGoodsBoydUrl(), new HashMap<>());
                item.setGoodsBoydUrl(xmlStr);
            }
            //查询当前商品的上下架状态
            ShopGoods shopGoods = new ShopGoods();
            shopGoods.setGoodsId(item.getGoodsId());
            shopGoods.setThirdLevelGcid(item.getThirdLevelGcid());
            shopGoods.setSupplierCode(item.getSupplierCode());
            item.setShelvesState(dfmallGoodsPoolShelvesDownService.checkGoodsIsDown(shopGoods) ? 0 : 1);
            result.add(item);
        });

        return result;
    }

    /**
     * 商品主数据查询 协议价和原价取电商实时价格
     *
     * @param goodsCode
     * @return
     */
    public GoodsMasterDetailVo queryGoodsRemotePriceByCode(String goodsCode) {
        GoodsMasterDetailVo goodsMasterDetailVo = queryGoodsMasterDetailByCode(goodsCode);
        ShopGoods shopGoods = this.selectOneByGoodsCode(goodsCode);
        final RemotePriceInfoResp res = priceGetter.getRemotePrice(shopGoods);
        log.info("【queryGoodsRemotePriceByCode】获取电商商品价格：" + JSONUtil.toJsonStr(res));
        if (Objects.isNull(res)) {
            return goodsMasterDetailVo;
        }
        if (ObjectUtil.isNotEmpty(res.getGoodsOriginalPrice())) {
            goodsMasterDetailVo.setGoodsOriginalPrice(res.getGoodsOriginalPrice());
            goodsMasterDetailVo.setGoodsOriginalNakedPrice(res.getGoodsOriginalNakedPrice());
        }
        if (ObjectUtil.isNotEmpty(res.getGoodsPactPrice())) {
            goodsMasterDetailVo.setGoodsPactPrice(res.getGoodsPactPrice());
            goodsMasterDetailVo.setGoodsPactNakedPrice(res.getGoodsPactNakedPrice());
        }

        if(goodsMasterDetailVo.getGoodsOriginalNakedPrice().compareTo(BigDecimal.ZERO) > 0){
            BigDecimal discount = goodsMasterDetailVo.getGoodsPactPrice().divide(goodsMasterDetailVo.getGoodsOriginalPrice(), 4, RoundingMode.HALF_UP);
            goodsMasterDetailVo.setAgreementDiscount(discount);
        }

        List<ShopGoodsEditRecordEntity> editRecord = goodsEditRecordMapper.selectList(new LambdaQueryWrapperX<ShopGoodsEditRecordEntity>()
                .eq(ShopGoodsEditRecordEntity::getGoodsId, shopGoods.getGoodsId())
                .orderByDesc(ShopGoodsEditRecordEntity::getId));
        goodsMasterDetailVo.setEditRecordList(editRecord);
        return goodsMasterDetailVo;
    }

    /**
     * 供应商商品管理统计查询
     */
    public ShopGoodsSupplierCountVo queryGoodsSupplierCount(ShopGoodsQueryDto shopGoodsQueryDto) {
        ShopGoodsSupplierCountVo goodsSupplierCountVo = new ShopGoodsSupplierCountVo();
        //统计商品总数
        QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopGoods::getSupplierCode, shopGoodsQueryDto.getSupplierCode());
        Long goodsTatalNum = this.baseMapper.selectCount(queryWrapper);
        goodsSupplierCountVo.setGoodsTotalNum(goodsTatalNum.intValue());

        //统计待审核商品
        queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopGoods::getSupplierCode, shopGoodsQueryDto.getSupplierCode()).eq(ShopGoods::getAuditState, 0);
        Long goodsWaitAudit = this.baseMapper.selectCount(queryWrapper);
        goodsSupplierCountVo.setGoodsWaitAudit(goodsWaitAudit.intValue());

//        List<Integer> upNum = this.srv.queryUpCountBySupplierCode(shopGoodsQueryDto.getSupplierCode());
//        Map<Object, Long> unMap = upNum.stream().collect(Collectors.groupingBy(Integer::byteValue, Collectors.counting()));
//        unMap.forEach((key, v) -> {
//            if ("1".equals(String.valueOf(key))) {
//                goodsSupplierCountVo.setGoodsUpNum(v.intValue());
//            } else {
//                goodsSupplierCountVo.setGoodsDownNum(v.intValue());
//            }
//        });
        goodsSupplierCountVo.setGoodsUpNum(0);
        goodsSupplierCountVo.setGoodsDownNum(0);
        return goodsSupplierCountVo;
    }

    public void deleteShopGoods(List<String> goodCodes) {
        this.shopGoodsMapper.deleteShopGoods(goodCodes);
    }

    @RedisCache(
            key = "'goodsCode_'+#goodsCode",
            timeout = 5,
            timeUnit = TimeUnit.MINUTES,
            withCompany = false)
    public ShopGoods selectOneByGoodsCode(String goodsCode) {
        return this.shopGoodsMapper.selectOneByGoodsCode(goodsCode);
    }

    public List<ShopGoods> selectListByGoodsCodes(String goodsCodes) {
        LambdaQueryWrapperX<ShopGoods> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper
                .select(
                        ShopGoods::getGoodsId,
                        ShopGoods::getGoodsCode,
                        ShopGoods::getGoodsName,
                        ShopGoods::getGoodsSku,
                        ShopGoods::getTenantId,
                        ShopGoods::getSupplierCode)
                .in(
                        ShopGoods::getGoodsCode,
                        Arrays.stream(goodsCodes.split(",")).map(String::trim).collect(Collectors.toList()));
        return this.shopGoodsMapper.selectList(queryWrapper);
    }

    public List<ShopGoods> selectListByGoodsCode(List<String> goodsCodeList) {
        return this.shopGoodsMapper.selectListByGoodsCode(goodsCodeList);
    }

    public ShopGoods selectOneBySkuAndSupplier(String sku, String supplier) {
        QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ShopGoods::getGoodsSku, sku)
                .eq(ShopGoods::getSupplierCode, supplier);
        return this.baseMapper.selectOne(queryWrapper);
    }

    public ShopGoods selectBySkuAndSupplier(String sku, String supplier) {
        return this.baseMapper.selectBySkuAndSupplier(sku, supplier);
    }

    public List<ShopGoodsVo> selectVoBySkuAndSupplier(String sku, String supplier) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }
        return this.baseMapper.selectVoBySkuAndSupplier(sku, supplier);
    }

    public Long supplierGoodsImport(List<SupplierGoodsImportReqVO> list, String name) {
        List<String> paramsList = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
        return miQueueManager.createQueueTask(paramsList,
                true,
                false,
                "IMPORT_PRODUCT",
                1,
                "supplierProductHandler",
                name, "goodsName");
    }

    public Long supplierImport(List<SupplierImportReqVO> list, String name) {
        List<String> paramsList = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
        return miQueueManager.createQueueTask(paramsList,
                true,
                false,
                "IMPORT_SUPPLIER",
                1,
                "supplierHandler",
                name, "supplierName");
    }

    public Long contractImport(List<ContractImportReqVO> list, String name) {
        List<String> paramsList = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
        return miQueueManager.createQueueTask(paramsList,
                true,
                false,
                "IMPORT_CONTRACT",
                1,
                "importContractHandler",
                name, "contractNumber");
    }

    public Long companyGoodsImport(List<CompanyGoodsImportReqVO> list, String name) {
        List<String> paramsList = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
        return miQueueManager.createQueueTask(paramsList,
                true,
                false,
                "IMPORT_COMPANY_PRODUCT",
                1,
                "importProductHandler",
                name, "sapCode");
    }

    public String selectGoodsCodeByGoodsSkuAndSupplierCode(String goodsSku, String supplierCode) {
        return this.shopGoodsMapper.selectGoodsCodeByGoodsSkuAndSupplierCode(goodsSku, supplierCode);
    }

    public List<String> selectGoodsCodeByGoodsSkusAndSupplierCode(List<String> goodsSkus, String supplierCode) {
        return this.shopGoodsMapper.selectGoodsCodeByGoodsSkusAndSupplierCode(goodsSkus, supplierCode);
    }

    /**
     * 获取价格信息
     *
     * @param goodsCode 好代码
     */
    public RemotePriceInfoResp getGoodPriceInfo(String goodsCode) {
        RemotePriceInfoResp remotePriceInfoResp = priceGetter.getSalePrice(goodsCode);

        //在这里判断超出折扣商品记录到异常商品，因为上面的方法被调用的地方太多，记录太频繁
        if (ErrorGoodsPriceMsgEnum.ERROR_REMOTE_PRICE_RULE.getType().equals(remotePriceInfoResp.getMsgType())) {
            //折扣异常
            //实际折扣
            BigDecimal actualDiscount = remotePriceInfoResp.getGoodsPactPrice().divide(remotePriceInfoResp.getGoodsOriginalPrice(), 2, RoundingMode.HALF_UP);
            //分类折扣
            BigDecimal classDiscount = classSrv.queryClassDiscountByGoodsCode(goodsCode);
            if (actualDiscount.compareTo(classDiscount) > 0) {
                //超出折扣范围
                ShopGoods shopGoods = shopGoodsMapper.selectOneByGoodsCode(goodsCode);
                if (!Objects.isNull(shopGoods)) {
                    //供应商分类【xx】折扣【xx】,当前商品协议价xxx,官网价xxx,实际折扣xxx
                    String desc = "供应商分类【" + shopGoods.getSupplierClassName() + "】折扣【" + classDiscount + "】,当前商品协议价：" + remotePriceInfoResp.getGoodsPactPrice() + ",官网价：" + remotePriceInfoResp.getGoodsOriginalPrice() + ",实际折扣：" + actualDiscount;
                    saveErrorInfo(shopGoods, remotePriceInfoResp, desc);
                }
            }
        }

        return remotePriceInfoResp;
    }

    public List<SkuAndSupplierVo> selectAllByGoodCodes(String[] codes) {
        return this.shopGoodsMapper.selectAllByGoodCodes(codes);
    }

    public Map<String, Object> getGoodsSpec(String goodsId) {
        ShopGoods goods = this.getById(goodsId);
        if (goods == null) {
            return null;
        }
        Long companyOrgId = LocalUserHolder.get().getEntityOrganizationId();
        ShopGoodsDetailVo goodsDetailVo = this.shopGoodsDetailService.getByGoodsId(goodsId, goods.getSupplierCode(), goods.getThirdLevelGcid(), null);
        if (goodsDetailVo == null) {
            log.error("查询不出此商品详情, goodsId [" + goodsId + "]");
            throw new NotFoundException("查询不到此商品详情数据,请联系管理员!");
        }
        String goodsSpec = goodsDetailVo.getGoodsSpecArray();

        Map<String, Object> specmap = new HashMap<String, Object>();
        specmap.put("goodsSpecs", goodsSpec);

        specmap.put("goodsbrandname", goods.getBrandName());
        return specmap;
    }

    public ShopGoods getByGoodsId(String goodsId) {
        QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopGoods::getGoodsId, goodsId);
        return this.getOne(queryWrapper);
    }

    public ShopGoods getByGoodsCode(String goodsCode) {
        QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopGoods::getGoodsCode, goodsCode).eq(ShopGoods::getIsEnable, 1);
        return this.getOne(queryWrapper);
    }


    public ShopGoods selectDescByGoodsCode(String goodsCode) {
        QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopGoods::getGoodsCode, goodsCode)
                .select(ShopGoods::getGoodsDesc,ShopGoods::getSaleUnit)
                .eq(ShopGoods::getIsEnable, 1);
        return this.getOne(queryWrapper);
    }
    

    public ShopGoodsDetailsVo select(String goodsId) {
        return this.shopGoodsMapper.select(goodsId);
    }

    public Integer selelctFavorites(Long userId, String goodsId) {
        return this.shopGoodsMapper.selelctFavorites(userId, goodsId);
    }

    /**
     * 查询已审核通过的商品编码
     *
     * @param supplierOrgId       供应商组织id
     * @param standardClassLv3Ids 标准类lv3 id
     * @return {@link List}<{@link String}>
     */
    public List<Long> queryGoodsBySupplierAndClass(Long supplierOrgId, Set<Long> standardClassLv3Ids) {
        if (standardClassLv3Ids == null || standardClassLv3Ids.isEmpty()) {
            return Collections.emptyList();
        }
        return this.shopGoodsMapper.queryGoodsBySupplierAndClass(supplierOrgId, standardClassLv3Ids).stream().map(ShopGoods::getGoodsId).collect(Collectors.toList());
    }

    /**
     * 根据供应商和标准分类，查询商品总数量
     *
     * @param supplierOrgId
     * @param standardClassLv3Ids
     * @return
     */
    public Long queryGoodsCountBySupplierAndClass(Long supplierOrgId, Set<Long> standardClassLv3Ids) {
        if (standardClassLv3Ids == null || standardClassLv3Ids.isEmpty()) {
            return 0L;
        }
        return this.shopGoodsMapper.queryGoodsCountBySupplierAndClass(supplierOrgId, standardClassLv3Ids);
    }

    /**
     * 商品详情上传云存储
     *
     * @param shopGoods
     */
    public void setIntroduce(ShopGoods shopGoods, String content) {
        if (null != shopGoods.getGoodsId()) {
            try (OSSClient ossClient = new OSSClient(this.ossConfig.getAccessKey(), this.ossConfig.getSecretKey(), this.ossConfig.getEndpoint())) {
                // 先删除原来的数据
                ossClient.delete(this.ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", this.yphConfig.getOssPrefix(), shopGoods.getGoodsCode()));
            } catch (IOException e) {
                throw new GoodsException("更新商品详情失败");
            }
        }

        if(content.contains(this.ossConfig.getTextBuketName())){
            // 租户同步的商品，已经上传了详情，直接保存url
            shopGoods.setGoodsBoydUrl(content);
        }else {
            // 内容设置到oss,商品下架注意清除
            try (OSSClient ossClient = new OSSClient(this.ossConfig.getAccessKey(), this.ossConfig.getSecretKey(), this.ossConfig.getEndpoint())) {
                String resultUrl = ossClient.putString(this.ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", this.yphConfig.getOssPrefix(), shopGoods.getGoodsCode()), content);
                shopGoods.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                shopGoods.setGoodsBoydUrl(this.yphConfig.getDefaultShopGoodBodyUrl());
            }
        }


    }

    /**
     * 查询简单的商品信息，供下拉列表使用
     *
     * @param pageReq        页面请求
     * @param goodsRequestVO 商品请求签证官
     * @return {@link Object}
     */
    public PageResp<ShopGoods> selectSimpleGoodsInfo(PageReq pageReq, GoodsRequestVO goodsRequestVO) {
        LambdaQueryWrapperX<ShopGoods> query = new LambdaQueryWrapperX<>();
        query.likeIfPresent(ShopGoods::getGoodsName, goodsRequestVO.getName());
        query.likeIfPresent(ShopGoods::getGoodsSku, goodsRequestVO.getSku());
        query.likeIfPresent(ShopGoods::getGoodsCode, goodsRequestVO.getGoodsCode());
        if (goodsRequestVO.getSupplierCode() != null) {
            String[] suppliersCode = goodsRequestVO.getSupplierCode().split(",");
            query.inIfPresent(ShopGoods::getSupplierCode, CollUtil.toList(suppliersCode));
        }

        return this.baseMapper.selectPage(pageReq, query, ShopGoods.class);
    }

    public List<ShopGoodsForEsEntity> selectForElasticsearchBatch(List<Long> goodsIds) {
        return this.shopGoodsMapper.selectByGoodsCodeForElasticsearchBatch(goodsIds);
    }

    public List<GoodsHistoryRecordVo> getHistoricalRecord(String goodsId) {
        return this.orderDetailService.getOrderDetailByGoodsId(goodsId);
    }

    private YphStandardClassEntity validateClass(String classCode) {
        YphStandardClassEntity goodsClass = this.yphStandardClassService.selectByCode(classCode);
        if (goodsClass == null) {
            throw HttpException.exception(ErrorCodeConstants.CLASS_NOT_EXIST, classCode);
        }
        return goodsClass;
    }

    private SystemOrganization validateSuppler(String orgCode) {
        SystemOrganization organization = this.organizationService.getOrganizationByCode(orgCode);
        if (organization == null) {
            throw HttpException.exception(ErrorCodeConstants.ORGANIZATION_NOT_FOUND, orgCode);
        }
        return organization;
    }

    /**
     * 验证品牌
     *
     * @param brandName 品牌名称
     * @return {@link ShopBrand}
     */
    public ShopBrand validateBrand(String brandName) {
        ShopBrand shopBrand = this.shopBrandService.selectByBrandName(brandName);
        if (shopBrand == null) {
            throw HttpException.exception(ErrorCodeConstants.BRAND_NOT_MAPPER, brandName);
        }
        return shopBrand;
    }

    public void changeProductPrice(ChangeProductPriceReq changePriceReq, ShopGoods shopGoods) {
        ShopGoods newShopGoods = new ShopGoods();

        newShopGoods.setPrice(changePriceReq.getGoodsPactPrice());
        newShopGoods.setNakePrice(changePriceReq.getGoodsPactNakedPrice());
        newShopGoods.setUpdateTime(new Date());
        newShopGoods.setGoodsId(shopGoods.getGoodsId());
        this.updateById(newShopGoods);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeProductDetail(ChangeProductDetailReq detailReq, final ShopGoods shopGoods) {
        //校验同款, 1.无同款 2 绑定同款 3 解绑同款 4变更同款
        validateSameGoods(detailReq,shopGoods);
        // 校验品牌
        ShopBrand shopBrand = validateBrand(detailReq.getBrandName());
        // 校验分类
        YphStandardClassEntity yphStandardClassEntity = validateClass(detailReq.getGoodsClass());
        BeanUtil.copyProperties(detailReq, shopGoods);
        shopGoods.setBrandId(shopBrand.getBrandId().toString());
        shopGoods.setGoodsSubtitle(detailReq.getGoodsName());

        List<String> classIds =
                Arrays.stream(yphStandardClassEntity.getClassPath().split(","))
                        .collect(Collectors.toList());
        // 分类名称，ID，Code 都要存进去
        changeGoodsClass(detailReq.getSupplierCode(), classIds, shopGoods);
        // 更改云存储商品详情
        this.setIntroduce(shopGoods, detailReq.getGoodsBody());
        shopGoods.setUpdateTime(new Date());
        this.updateById(shopGoods);
        // 详情表变更
        ShopGoodsDetail detail =
                shopGoodsDetailService
                        .getDetailForTrans(CollectionUtil.toList(shopGoods.getGoodsCode()))
                        .get(shopGoods.getGoodsCode());
        if (detail == null) {
            throw HttpException.exception(ErrorCodeConstants.PRODUCT_NOT_EXIST, shopGoods.getGoodsCode());
        }
        StringBuilder specStr = new StringBuilder();
        try {
            JSONObject.parseObject(detailReq.getSpecArray())
                    .forEach((key, value) -> specStr.append(key).append(" ").append(value).append(" "));
        } catch (Exception ex) {
            throw HttpException.exception(ErrorCodeConstants.ATTR_ERROR);
        }
        BeanUtil.copyProperties(detailReq, detail);
        detail
                .setGoodsImage(replaceAmp(StrUtil.subBefore(detailReq.getImageArray(), ";", false)))
                .setGoodsImageMore(replaceAmp(StrUtil.subAfter(detailReq.getImageArray(), ";", false)))
                .setGoodsSpec(specStr.toString())
                .setGoodsSpecArray(StrUtil.isNotBlank(detailReq.getSameSpecArray()) ? detailReq.getSameSpecArray() : detailReq.getSpecArray())
                .setOrganizationId(shopGoods.getOrganizationId());
        shopGoodsDetailService.updateById(detail);
    }

    /**
     * 校验同款, 1.无同款 2 绑定同款 3 解绑同款 4变更同款
     * @param detailReq
     */
    private void validateSameGoods(ChangeProductDetailReq detailReq, ShopGoods shopGoods) {
        String goodsSku = detailReq.getGoodsSku();
        String supplierCode = detailReq.getSupplierCode();
        String specArray = detailReq.getSpecArray();
        String sameCode = detailReq.getSameCode();
        String delSameCode = detailReq.getDelSameCode();

        Map<String, Object> checkMap = new HashMap<>();

        //接口平台变更类别
        Integer updateScene;
        if (StrUtil.isBlank(sameCode)) {
            updateScene = StrUtil.isBlank(delSameCode) ? OpenUpdateSceneEnum.NOT_SAM.getCode() : OpenUpdateSceneEnum.DEL_SAM.getCode();
        } else {
            updateScene = StrUtil.isBlank(delSameCode) ? OpenUpdateSceneEnum.ADD_SAM.getCode() : OpenUpdateSceneEnum.CHANGE_SAM.getCode();
        }
        //是否有历史同款
        ShopGoodsSameEntity oldSame = goodsSameMapper.selectOne(new LambdaQueryWrapperX<ShopGoodsSameEntity>()
                .eq(ShopGoodsSameEntity::getGoodsSku, goodsSku).eq(ShopGoodsSameEntity::getSupplierCode, supplierCode));
        if (updateScene == OpenUpdateSceneEnum.NOT_SAM.getCode()) {
            if (oldSame == null) {
                return;
            } else {
                sameCode = oldSame.getSameCode();
            }
        } else if (updateScene == OpenUpdateSceneEnum.CHANGE_SAM.getCode()) {
            if (oldSame== null || !oldSame.getSameCode().equals(delSameCode)){
                throw new IllegalArgumentException("无同款数据,请勿变更！");
            }
            goodsSameMapper.deleteById(oldSame);
            oldSame = null;
        } else if (updateScene == OpenUpdateSceneEnum.ADD_SAM.getCode()) {
            if (oldSame != null) {
                if (!oldSame.getSameCode().equals(sameCode)) {
                    throw new IllegalArgumentException("同款编码重复！" + sameCode);
                }
            }
        } else if (updateScene == OpenUpdateSceneEnum.DEL_SAM.getCode()) {
            if (oldSame == null) {
                throw new IllegalArgumentException("无同款数据,请勿解绑！");
            } else {
                if (!oldSame.getSameCode().equals(delSameCode)) {
                    throw new IllegalArgumentException("待删除同款编码错误！");
                }
                goodsSameMapper.deleteById(oldSame);
                return;
            }
        }
        Map<String, Object> specMap = goodsSameService.queryGoodsSpecArray(sameCode);
        if (specMap != null) {
            List<cn.hutool.json.JSONObject> toArray = JsonUtils.parseObjectToArray(specArray);
            //删除
            if (oldSame != null) {
                checkMap = removeSpecs(shopGoods.getGoodsCode(), specMap);
            } else {
                checkMap = specMap;
            }
            // 1. 规格名称一致性校验
            goodsSameService.checkSpecNameConsistency(toArray, checkMap);

            // 2. 规格值唯一性校验
            goodsSameService.checkSpecValueUniqueness(toArray, checkMap);
        }

        if (oldSame == null || updateScene == OpenUpdateSceneEnum.CHANGE_SAM.getCode()) {
            //没有 新增同款
            ShopGoodsSameEntity insertSame = new ShopGoodsSameEntity();
            insertSame.setSameCode(sameCode);
            insertSame.setGoodsId(shopGoods.getGoodsId());
            insertSame.setGoodsCode(shopGoods.getGoodsCode());
            insertSame.setGoodsSku(goodsSku);
            insertSame.setSupplierCode(supplierCode);
            goodsSameMapper.insert(insertSame);
        }
        //新增 变更   重要!
        detailReq.setSameSpecArray(JsonUtils.parseObjectToArray(detailReq.getSpecArray()).toString());
    }

    public Map<String, Object> removeSpecs (String goodsCode, Map < String, Object > specMap){
        ShopGoodsDetail shopGoodsDetail = shopGoodsDetailService.selectOneByGoodsCode(goodsCode);
        //  [{"颜色":"白色"},{"规格":"50/盒"}]
        String goodsSpecArray = shopGoodsDetail.getGoodsSpecArray();
        cn.hutool.json.JSONArray deleteArray = new cn.hutool.json.JSONArray(goodsSpecArray);
        //specMap ["颜色":{黑色,白色},"规格":{50只/盒,20只/盒}]
        // 创建删除操作的映射表 (key: 规格名, value: 要删除的值列表)
        Map<String,String> deleteMap = new HashMap<>();

        // 构建删除映射表
        for (int i = 0; i < deleteArray.size(); i++) {
            cn.hutool.json.JSONObject obj = deleteArray.getJSONObject(i);
            for (String key : obj.keySet()) {
                String valueToDelete = obj.getStr(key);
                deleteMap.computeIfAbsent(key, k -> valueToDelete);
            }
        }
        // 创建结果Map
        Map<String, Object> resultMap = new HashMap<>();
        // 处理每个规格项
        for (Map.Entry<String, Object> entry : specMap.entrySet()) {
            String specName = entry.getKey();
            Object specValues = entry.getValue();
            // 只处理集合类型的规格值
            if (specValues instanceof HashMap) {
                // 黑色 白色
                Map<String, Set<Long>> values = (Map<String, Set<Long>>) specValues;
                List<String> resultList = new ArrayList<>();
                // 获取该规格对应的删除列表（如果没有则为空列表）
                //白色
                String deleteValue = deleteMap.getOrDefault(specName,"");
                // 过滤需要保留的值
                Set<String> keySet = values.keySet();
                if (keySet.contains(deleteValue)){
                    values.remove(deleteValue);
                }
                resultMap.put(specName, values);
            } else {
                // 非集合类型直接保留
                resultMap.put(specName, specValues);
            }
        }
        return resultMap;
    }

    public void deleteSameShopGoods(String goodsCode) {
        ShopGoodsSameEntity sameEntity = goodsSameService.getOne(new LambdaQueryWrapperX<ShopGoodsSameEntity>().eq(ShopGoodsSameEntity::getGoodsCode, goodsCode));
        if (sameEntity != null) {
            goodsSameService.removeById(sameEntity);
        }
    }

    private void changeGoodsClass(String supplierCode, List<String> classIds, ShopGoods shopGoods) {
        LambdaQueryWrapperX<YphStandardClassEntity> query = new LambdaQueryWrapperX<>();
        query.in(YphStandardClassEntity::getClassCode, classIds);
        List<YphStandardClassEntity> scList = scMapper.selectList(query);
        scList.forEach(item -> {
            switch (item.getClassLevel()) {
                case 1:
                    shopGoods.setFirstClass(item.getClassCode());
                    shopGoods.setFirstLevelGcid(String.valueOf(item.getStandardClassId()));
                    shopGoods.setFirstClassName(item.getClassName());
                    break;
                case 2:
                    shopGoods.setSecondClass(item.getClassCode());
                    shopGoods.setSecondLevelGcid(String.valueOf(item.getStandardClassId()));
                    shopGoods.setSecondClassName(item.getClassName());
                    break;
                case 3:
                    shopGoods.setThirdClass(item.getClassCode());
                    shopGoods.setThirdLevelGcid(String.valueOf(item.getStandardClassId()));
                    shopGoods.setThirdClassName(item.getClassName());

                    //更改供应商分类
                    shopGoods.setSupplierClass(item.getClassCode());
                    shopGoods.setSupplierClassName(item.getClassName());
                    ShopSupplierClass supplierClass = classSrv.selectOneBySupAndClass(supplierCode, item.getClassCode());
                    if (supplierClass != null) {
                        shopGoods.setSupplierClassId(String.valueOf(supplierClass.getSupplierClassId()));
                    } else {
                        shopGoods.setSupplierClassId(null);
                    }
                    break;
                default:
                    break;
            }
        });
    }

    public List<ShopGoodsVo> queryGoodsClassByActivityId(Long activityId, Long organizationId) {
        return this.shopGoodsMapper.queryGoodsClassByActivityId(activityId, organizationId);
    }

    public ShopGoods getGoodsForStore(String goodsCode) {
        return this.shopGoodsMapper.queryGoodsByGoodsCode(goodsCode);
    }

    public Integer updateGoods(ShopGoods sEnt) {
        return this.shopGoodsMapper.updateGoods(sEnt);
    }

    public List<ShopGoods> selectBeach(List<String> goodsIdList) {
        return this.shopGoodsMapper.selectBeach(goodsIdList);
    }

    public void changeProductShelves(List<ChangeProductShelvesReq> shelvesReq) {
        if (CollectionUtil.isEmpty(shelvesReq)) {
            return;
        }
        List<Long> shelves = new ArrayList<>();
        List<Long> unShelve = new ArrayList<>();
        Map<String, Integer> statusMap =
                shelvesReq.stream()
                        .collect(
                                Collectors.toMap(
                                        ChangeProductShelvesReq::getGoodsSku,
                                        ChangeProductShelvesReq::getShelvesState));
        List<String> skus =
                shelvesReq.stream().map(ChangeProductShelvesReq::getGoodsSku).collect(Collectors.toList());

        // 批量查询商品
        List<ShopGoods> shopGoodsList =
                this.shopGoodsMapper.selectBeachBysSkusAndSupplierCode(
                        skus, shelvesReq.get(0).getSupplierCode());
        shopGoodsList.forEach(
                itm -> {
                    // 1上架 0下架
                    Integer state = statusMap.get(itm.getGoodsSku());
                    if (state == 1) {
                        shelves.add(itm.getGoodsId());
                    } else if (state == 0) {
                        unShelve.add(itm.getGoodsId());
                    } else {
                        throw HttpException.exception(
                                ErrorCodeConstants.STATUS_IS_NOT_CORRECT, itm.getGoodsSku());
                    }
                });
        // 执行上下架,未考企业手动下架的商品
        if (CollectionUtil.isNotEmpty(shelves)) {
            goodsUpDownManage.goodsIdUpToLiteFlow(shelves);
        }
        if (CollectionUtil.isNotEmpty(unShelve)) {
            goodsUpDownManage.goodsIdDown(unShelve, "开放接口下架：changeProductShelves", null);
        }
    }

    // 校验商品合同是否过期
    public Map<String, ShopSrmContractEntity> goodsCheckContract(Collection<String> goodsCodes, Consumer<Map<String, ShopSrmContractEntity>> succCallback) {
        Map<String, ShopSrmContractEntity> error = new HashMap<>();
        Map<String, ShopSrmContractEntity> succ = new HashMap<>();
        Map<String, ShopSrmContractDetailEntity> contractDetails = srmContractDetailService.getByGoodsCode(goodsCodes);
        List<ShopSrmContractEntity> carray = srmContractService.getBaseMapper().getBySrmContractNumbers(contractDetails.values().stream().map(ShopSrmContractDetailEntity::getContractNumber).collect(Collectors.toList()));
        Map<String, ShopSrmContractEntity> cmap = carray.stream().collect(Collectors.toMap(ShopSrmContractEntity::getContractNumber, Function.identity()));

        goodsCodes.forEach(item -> {
            var contractDetail = contractDetails.get(item);
            if (null != contractDetail && !StringHelper.IsEmptyOrNull(contractDetail.getContractNumber())) {
                // 如果该商品存在SRM合同，则判断合同有效性
                var srmContractEntity = cmap.get(contractDetail.getContractNumber());
                //如果合同履约结束时间小于当前时间
                int compareTo = srmContractEntity.getEndContractValidity().compareTo(new Date());
                if (compareTo < 0) {
                    error.put(item, srmContractEntity);
                    return;
                }
                succ.put(item, srmContractEntity);
            }
        });
        if (succCallback != null) {
            succCallback.accept(succ);
        }
        return error;
    }

    public List<ShopCart> queryGoodsBySku(Collection<String> goodsSkus, String supplierCode) {
        if (goodsSkus.isEmpty()) {
            return new ArrayList<>();
        }
        return this.getBaseMapper().queryGoodsBySku(goodsSkus, supplierCode);
    }

    private void setGoodsBrand(ShopGoods se, String brandName) {
        ShopBrand shopBrand = this.validateBrand(brandName);
        se.setBrandId(String.valueOf(shopBrand.getBrandId()));
    }

    private void setGoodsClass(ShopGoods shop_goods_params, String goodsClass) {
        YphStandardClassEntity standard_cls = null;
        try {
            standard_cls = this.validateClass(goodsClass);
        } catch (Exception e) {
            log.error("分类异常,{}", e.getMessage());
            HuoshanKnowledgeAnswerVO classVo;
            String query = shop_goods_params.getGoodsName() + " 销售单位：" + shop_goods_params.getSaleUnit();
            try {
                classVo = huoshanDeepseekCategory.classifyProductWithCache(query, true);
            } catch (Exception ex) {
                throw new ParameterException("ai解析标准分类异常", ex);
            }

            standard_cls = this.yphStandardClassService.getOne(new LambdaQueryWrapperX<YphStandardClassEntity>().eq(YphStandardClassEntity::getClassName, classVo.getThirdCategory()).eq(YphStandardClassEntity::getClassLevel, 3).last("LIMIT 1"));
            if (standard_cls == null) {
                throw new ParameterException("查询标准分类异常,{},{},{}", classVo.getThirdCategory(), classVo.getSecondCategory(), classVo.getFirstCategory());
            }
        }
        ShopSupplierClass supplierClass = classSrv.selectOneBySupAndClass(shop_goods_params.getSupplierCode(), goodsClass);

        String[] codes = standard_cls.getClassPath().split(",");
        LambdaQueryWrapperX<YphStandardClassEntity> query = new LambdaQueryWrapperX<>();
        query.in(YphStandardClassEntity::getClassCode, codes);
        List<YphStandardClassEntity> scList = scMapper.selectList(query);
        for (YphStandardClassEntity item : scList) {
            switch (item.getClassLevel()) {
                case 1:
                    shop_goods_params.setFirstClass(item.getClassCode());
                    shop_goods_params.setFirstLevelGcid(String.valueOf(item.getStandardClassId()));
                    shop_goods_params.setFirstClassName(item.getClassName());
                    break;
                case 2:
                    shop_goods_params.setSecondClass(item.getClassCode());
                    shop_goods_params.setSecondLevelGcid(String.valueOf(item.getStandardClassId()));
                    shop_goods_params.setSecondClassName(item.getClassName());
                    break;
                case 3:
                    shop_goods_params.setThirdClass(item.getClassCode());
                    shop_goods_params.setThirdLevelGcid(String.valueOf(item.getStandardClassId()));
                    shop_goods_params.setThirdClassName(item.getClassName());

                    break;
                default:
                    break;
            }
        }
        shop_goods_params.setSupplierClass(standard_cls.getClassCode());
        if (supplierClass != null) {
            shop_goods_params.setSupplierClassId(String.valueOf(supplierClass.getSupplierClassId()));
        } else {
            shop_goods_params.setSupplierClassId(null);
        }

        shop_goods_params.setSupplierClassName(standard_cls.getClassName());
    }

    private void setSupplierInfo(ShopGoods se, String supplierCode) {
        SystemOrganization organization = this.validateSuppler(supplierCode);
        se.setOrganizationId(organization.getId());
        se.setSupplierCode(organization.getCode());
        ShopSupplier supplier = supplierSrv.selectByCode(organization.getCode());
        se.setSupplierName(supplier.getSupplierShortName());
        se.setSupplierType(supplier.getSupplierType());
    }

    private ShopGoods createShopGoodsInfo(ProductCreateReq params) {
        ShopGoods shop_goods = GoodsConvert.INSTANCE.convertOpenShopGoods(params);
        shop_goods.setGoodsCode(this.codeGeneral.getProductCode(params.getSupplierCode()));
        shop_goods.setGoodsSku(params.getGoodsSku());
        setGoodsBrandByAi(shop_goods,params.getSpecArray(), params.getBrandName());
        setGoodsClass(shop_goods, params.getGoodsClass());
        setSupplierInfo(shop_goods, params.getSupplierCode());
        this.setIntroduce(shop_goods, params.getGoodsBody());
        if (customizationSupplierCodeList.contains(shop_goods.getSupplierCode())) {
            shop_goods.setAuditState(0);
        } else {
            shop_goods.setAuditState(1);
        }

        // 接口平台过来的商品统一设置成电商平台
        this.save(shop_goods);
        return shop_goods;
    }

    private void setGoodsBrandByAi(ShopGoods shopGoods, String specArray, String brandName) {
        Boolean brandError = false;
        try {
            setGoodsBrand(shopGoods, brandName);
        } catch (Exception e) {
            log.error("设置品牌异常,{}" ,e.getMessage());
            brandError = true;
        }
        //处理异常
        if (brandError){
            // 来来来 ai来弄
            HuoshanArkApiResultDTO brandDto;
            StringBuilder description = new StringBuilder();
            // 添加商品名称
            description.append(shopGoods.getGoodsName()).append(" ");
            // 添加商品描述
            description.append(shopGoods.getGoodsDesc()).append(" ");
            // 添加材料编码
            description.append(shopGoods.getMaterialsCode()).append(" ");
            // 添加商品型号
            description.append(shopGoods.getGoodsModel()).append(" ");
            // 添加商品规格
            description.append(specArray).append(" ");
            // 添加品牌名称
            description.append(brandName).append(" ");
            // 添加售卖单位
            description.append("售卖单位:").append(shopGoods.getSaleUnit());

            try {
                brandDto = huoshanDeepseekProduct.processProductDescription(description.toString().trim(), null);
            } catch (Exception e) {
                throw new ParameterException("ai解析标准品牌异常", e);
            }
            if (StrUtil.isBlank(brandDto.getBrand())) {
                throw new ParameterException("ai解析标准品牌异常,[{}]", description);
            }
            ShopBrand brand = new ShopBrand();
            brand.setBrandName(brandDto.getBrand());
            shopBrandService.saveShopBrand(brand);
            shopGoods.setBrandId(String.valueOf(brand.getBrandId()));
            shopGoods.setBrandName(brandDto.getBrand());
        }
    }

    /*
     * 创建商品价格，4个价格不再需要供应商传递，本系统自动计算
     */
    private ShopGoodsPrice createShopGoodsPrice(ProductCreateReq reqVo, ShopGoods se) {
        ShopGoodsPrice price = new ShopGoodsPrice();
        BeanUtil.copyProperties(reqVo, price);
        BigDecimal taxRate = new BigDecimal(reqVo.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));

        price.setGoodsCode(se.getGoodsCode())
                .setGoodsPactPrice(reqVo.getGoodsPactPrice().setScale(2, RoundingMode.HALF_UP))
                .setGoodsOriginalPrice(reqVo.getGoodsOriginalPrice().setScale(2, RoundingMode.HALF_UP))
                .setGoodsPactNakedPrice(reqVo.getGoodsPactPrice().divide(taxRate, 2, RoundingMode.HALF_UP))
                .setGoodsOriginalNakedPrice(reqVo.getGoodsOriginalPrice().divide(taxRate, 2, RoundingMode.HALF_UP))
                .setGoodsSku(se.getGoodsSku())
                .setOrganizationId(se.getOrganizationId());

        this.priceSrv.saveItem(price);
        return price;
    }

    private ShopGoodsDetail createShopGoodsDetail(ProductCreateReq reqVo, ShopGoods se) {
        ShopGoodsDetail detail = new ShopGoodsDetail();
        BeanUtil.copyProperties(reqVo, detail);
        detail.setGoodsSku(se.getGoodsSku()).setGoodsCode(se.getGoodsCode());
        StringBuilder specStr = new StringBuilder();
        BeanUtil.copyProperties(reqVo, detail);
        String specArray = reqVo.getSpecArray();
        try {
            JSONObject.parseObject(specArray).forEach((key, value) -> specStr.append(key).append(" ").append(value).append(" "));
        } catch (Exception ex) {
            throw HttpException.exception(ErrorCodeConstants.ATTR_ERROR);
        }
        if (StringUtils.isNotBlank(reqVo.getSameCode())) {
            specArray = JsonUtils.parseObjectToArray(reqVo.getSpecArray()).toString();
        }
        detail.setGoodsImage(replaceAmp(StrUtil.subBefore(reqVo.getImageArray(), ";", false)))
                .setGoodsImageMore(replaceAmp(StrUtil.subAfter(reqVo.getImageArray(), ";", false)))
                .setGoodsSpec(specStr.toString())
                .setGoodsSpecArray(specArray)
                .setOrganizationId(se.getOrganizationId());
        this.shopGoodsDetailService.save(detail);
        return detail;
    }

    private ShopGoodsStock createShopGoodsStock(ProductCreateReq reqVo, ShopGoods se) {
        ShopGoodsStock stock = new ShopGoodsStock()
                .setOrganizationId(se.getOrganizationId())
                .setGoodsSku(se.getGoodsSku())
                .setStockAlert(reqVo.getGoodsMoq())
                .setGoodsCode(se.getGoodsCode())
                .setStockAvailable(reqVo.getStockAvailable());
        this.shopGoodsStockService.save(stock);
        return stock;
    }

//    public List<ShopGoodsForEsEntityExt> selectEsExtend(List<String> goodsIds) {
//        return shopGoodsMapper.selectEsExtend(goodsIds);
//    }

    public PageResp<ContractGoodsVO> queryContractGoods(PageReq pageReq, ContractGoodsQueryDto queryDto, Integer sourceType) {
        IPage<ContractGoodsVO> page = this.getBaseMapper().queryContractGoods(DataAdapter.adapterPageReq(pageReq), queryDto, sourceType);
        return DataAdapter.adapterPage(page, ContractGoodsVO.class);
    }

    public Map queryPoolNameByGoodsIdsNew(List<Long> goodsIds) {
        try {
            if (CollectionUtil.isEmpty(goodsIds)) {
                return new HashMap();
            }

            Map<Long, Set<String>> goodsIdPoolName = Maps.newHashMap();
            // 获取用户关联的商品池
            List<Long> userGoodsPoolIdList = shopProjectUsersService.getUserConnAllGoodsPoolIds(LocalUserHolder.get().getId(), LocalUserHolder.get().getEntityOrganizationId());
            if (CollectionUtil.isEmpty(userGoodsPoolIdList)) {
                return goodsIdPoolName;
            }

            QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().select(ShopGoods::getGoodsId, ShopGoods::getSupplierCode, ShopGoods::getThirdLevelGcid).in(ShopGoods::getGoodsId, goodsIds);
            List<ShopGoods> shopGoodsList = this.list(queryWrapper);
            // 获取商品关联的商品池
            for (ShopGoods shopGoods : shopGoodsList) {
                List<DfmallGoodsPoolSubEntity> dfmallGoodsPoolSubEntityList = dfmallGoodsPoolSubService.queryGoodsPools(String.valueOf(shopGoods.getGoodsId()), shopGoods.getThirdLevelGcid(), shopGoods.getSupplierCode(), null);
                if (CollectionUtil.isNotEmpty(dfmallGoodsPoolSubEntityList)) {
                    Set<String> goodsPoolNameSet = new HashSet<>();
                    for (DfmallGoodsPoolSubEntity subEntity : dfmallGoodsPoolSubEntityList) {
                        if (userGoodsPoolIdList.contains(subEntity.getGoodsPoolId())) {
                            goodsPoolNameSet.add(subEntity.getGoodsPoolName());
                        }
                    }
                    if (CollectionUtil.isNotEmpty(goodsPoolNameSet)) {
                        goodsIdPoolName.put(shopGoods.getGoodsId(), goodsPoolNameSet);
                    }
                }
            }
            return goodsIdPoolName;
        } catch (Exception ex) {
            log.error("【queryPoolNameByGoodsIds】查询商品所在商品池错误：ex:" + ex.getMessage());
        }
        return new HashMap();
    }

    public Map queryPoolNameByGoodsIds(List<Long> goodsIds) {
        try {
            if (CollectionUtil.isEmpty(goodsIds)) {
                return new HashMap();
            }
            //商品维度
            QueryWrapper<ShopGoodsPoolGoodsEntity> poolGoodsWrapper = new QueryWrapper<>();
            poolGoodsWrapper.lambda().in(ShopGoodsPoolGoodsEntity::getGoodsId, goodsIds);
            List<ShopGoodsPoolGoodsEntity> shopGoodsPoolGoodsEntities = goodsPoolGoodsService.list(poolGoodsWrapper);

            //分类维度
            QueryWrapper<ShopGoods> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().select(ShopGoods::getGoodsId, ShopGoods::getThirdLevelGcid, ShopGoods::getSupplierCode).in(ShopGoods::getGoodsId, goodsIds);
            List<ShopGoods> shopGoods = this.list(queryWrapper);
            List<ShopGoodsPoolClassEntity> classEntities = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(shopGoods)) {
                List<Long> classIds = shopGoods.stream().map(item -> Convert.toLong(item.getThirdLevelGcid())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(classIds)) {
                    QueryWrapper<ShopGoodsPoolClassEntity> classEntityQuery = new QueryWrapper<>();
                    classEntityQuery.lambda().select(ShopGoodsPoolClassEntity::getGoodsPoolId, ShopGoodsPoolClassEntity::getStandardClassId)
                            .in(ShopGoodsPoolClassEntity::getStandardClassId, classIds);
                    classEntities = shopGoodsPoolClassService.list(classEntityQuery);
                }
            }

            //供应商维度
            Set<String> supplierCode = shopGoods.stream().map(ShopGoods::getSupplierCode).collect(Collectors.toSet());
            List<ShopSupplier> suppliers = supplierSrv.getAllSupplier();
            List<ShopSupplier> supplierList = suppliers.stream().filter(item -> supplierCode.contains(item.getSupplierCode())).collect(Collectors.toList());

            //所有商品的供应商组织ID
            Map<String, Long> filterSupp = supplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, ShopSupplier::getOrganizationId));
            //查询供应商绑定的商品池
            Set<Long> supplierPoolIds = new HashSet<>();
            List<ShopGoodsPoolSupplierEntity> shopGoodsPoolSupplierEntities = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(filterSupp)) {
                QueryWrapper<ShopGoodsPoolSupplierEntity> supplierEntityQuery = new QueryWrapper<>();
                supplierEntityQuery.lambda().in(ShopGoodsPoolSupplierEntity::getSupplierOrgId, filterSupp.entrySet().stream().map(item -> item.getValue()).collect(Collectors.toList()));
                shopGoodsPoolSupplierEntities = shopGoodsPoolSupplierService.list(supplierEntityQuery);
                supplierPoolIds = shopGoodsPoolSupplierEntities.stream().map(ShopGoodsPoolSupplierEntity::getGoodsPoolId).collect(Collectors.toSet());
            }

            Set<Long> poolIds = new HashSet<>();

            Set<Long> poolIdGoods = new HashSet<>();
            if (CollectionUtil.isNotEmpty(classEntities)) {
                poolIds.addAll(classEntities.stream().map(ShopGoodsPoolClassEntity::getGoodsPoolId).collect(Collectors.toSet()));
            }
            if (CollectionUtil.isNotEmpty(shopGoodsPoolGoodsEntities)) {
                poolIdGoods.addAll(shopGoodsPoolGoodsEntities.stream().map(ShopGoodsPoolGoodsEntity::getGoodsPoolId).collect(Collectors.toSet()));
            }

            //只用取供应商和分类的商品池交集
            Collection<Long> poolCls = CollectionUtil.intersectionDistinct(poolIds, supplierPoolIds);

            //商品分类的商品池需要单独查询
            poolCls.addAll(poolIdGoods);

            if (CollectionUtil.isEmpty(poolCls)) {
                return new HashMap();
            }
            List<ShopGoodsPoolEntity> shopGoodsPoolEntities = shopGoodsPoolService.listByIds(poolCls);
            Map<Long, String> shopGoodsPoolNames = shopGoodsPoolEntities.stream().collect(Collectors.toMap(ShopGoodsPoolEntity::getId, ShopGoodsPoolEntity::getGoodsPoolName));

            Map<Long, Set<String>> goodsIdPoolName = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(shopGoodsPoolEntities)) {
                for (ShopGoodsPoolGoodsEntity shopGoodsPoolGoodsEntity : shopGoodsPoolGoodsEntities) {
                    String poolName = shopGoodsPoolNames.get(shopGoodsPoolGoodsEntity.getGoodsPoolId());
                    if (CollectionUtil.isEmpty(goodsIdPoolName.get(shopGoodsPoolGoodsEntity.getGoodsId()))) {
                        goodsIdPoolName.put(shopGoodsPoolGoodsEntity.getGoodsId(), Sets.newHashSet(poolName));
                    } else {
                        Set<String> names = goodsIdPoolName.get(shopGoodsPoolGoodsEntity.getGoodsId());
                        names.add(poolName);
                        goodsIdPoolName.put(shopGoodsPoolGoodsEntity.getGoodsId(), names);
                    }
                }
                for (ShopGoods shopGoods1 : shopGoods) {
//                    3840372'
                    Long goodsId = shopGoods1.getGoodsId();
                    Long supplierOrgId = filterSupp.get(shopGoods1.getSupplierCode());
                    List<ShopGoodsPoolSupplierEntity> shopGoodsPoolSupplierEntities1 = shopGoodsPoolSupplierEntities.stream().filter(item -> item.getSupplierOrgId().equals(supplierOrgId)).collect(Collectors.toList());
                    //如果这个商品的供应商，没有签约到这个商品池里面，就跳过，因为这个供应商的所有商品都不会和这个商品池有关系
                    if (CollectionUtil.isEmpty(shopGoodsPoolSupplierEntities1)) {
                        continue;
                    }
                    //根据供应商的商品所签约的商品池优先
                    Map<Long, String> shopGoodsPoolNamesBySupplier = new HashMap<>();
                    for (ShopGoodsPoolSupplierEntity shopGoodsPoolSupplierEntity : shopGoodsPoolSupplierEntities1) {
                        if (StrUtil.isNotBlank(shopGoodsPoolNames.get(shopGoodsPoolSupplierEntity.getGoodsPoolId()))) {
                            shopGoodsPoolNamesBySupplier.put(shopGoodsPoolSupplierEntity.getGoodsPoolId(), shopGoodsPoolNames.get(shopGoodsPoolSupplierEntity.getGoodsPoolId()));
                        }
                    }

                    //查询这个分类的所有池子
                    List<ShopGoodsPoolClassEntity> fc = classEntities.stream().filter(item -> item.getStandardClassId().equals(Convert.toLong(shopGoods1.getThirdLevelGcid()))).collect(Collectors.toList());
                    Set<String> names = Sets.newHashSet();
                    for (ShopGoodsPoolClassEntity shopGoodsPoolClassEntity : fc) {
                        names.add(shopGoodsPoolNamesBySupplier.get(shopGoodsPoolClassEntity.getGoodsPoolId()));
                    }
                    if (CollectionUtil.isEmpty(goodsIdPoolName.get(goodsId))) {
                        goodsIdPoolName.put(goodsId, names);
                    } else {
                        Set<String> names1 = goodsIdPoolName.get(goodsId);
                        names.addAll(names1);
                        goodsIdPoolName.put(goodsId, names);
                    }
                }
            }
            return goodsIdPoolName;
        } catch (Exception ex) {
            log.error("【queryPoolNameByGoodsIds】查询商品所在商品池错误：ex:" + ex.getMessage());
        }
        return new HashMap();
    }

    public List<Long> shopGoodsAutoDown(Integer day) {
        log.info("开始执行自动下架操作========");
        if (null == day || 0 == day)
            day = 3;
        if (-1 == day)
            day = 365;
        DateTime endDate = DateUtil.date();
        DateTime startDate = DateUtil.offsetDay(endDate, -day);
        List<Long> expireGoodsIds = shopGoodsMapper.getExpireGoodsCode(startDate, endDate);
        goodsUpDownManage.goodsIdDown(expireGoodsIds, "商品自动下架", null);
        log.info("自动下架操作完成========");
        return expireGoodsIds;
    }

    /**
     * 独立供应商下架单个商品
     *
     * @param dto
     */
    public void supplierProductDown(SupplierProductDownDto dto) {
        //查商品是否存在
        QueryWrapper<ShopGoods> shopGoodsQueryWrapper = new QueryWrapper<>();
        shopGoodsQueryWrapper.lambda().eq(ShopGoods::getGoodsSku, dto.getGoodsSku())
                .eq(ShopGoods::getSupplierCode, dto.getSupplierCode()).eq(ShopGoods::getIsEnable, 1);
        ShopGoods goods = this.getOne(shopGoodsQueryWrapper);
        AssertUtil.npeIsShit(goods, "未查到该商品信息");

        List<Long> goodsIds = new ArrayList<>();
        goodsIds.add(goods.getGoodsId());

        DfmallGoodsPoolEntity dfmallGoodsPool = dfmallGoodsPoolService.queryPlatformPool();

        //记录下架记录
        goodsUpDownManage.goodsIdDown(goodsIds, dto.getRemark(), dfmallGoodsPool.getId());

        //记录商品变动记录
        ShopGoodsChangeLogEntity changLog = new ShopGoodsChangeLogEntity();

        changLog.setGoodsSku(goods.getGoodsSku());
        changLog.setOperType(GoodsChangeLogEnum.GOODS_DOWN.getCode());
        changLog.setSupplier(goods.getSupplierCode());
        String remark = dto.getRemark();

        changLog.setRemark(remark);
        this.changeLogSrv.save(changLog);
    }

    public List<JdSimilarSkuGoodsRespVo> getSimilarSkuList(String skuId, String supplierCode, String goodsPoolIds) {
        List<GetSimilarSkuGoodsResp> list = rmtMng.getSimilarSkuList(skuId, supplierCode);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        Set<String> skuIdSet = new HashSet<>();
        List<JdSimilarSkuGoodsRespVo> resultList = new ArrayList<>();
        //将京东返回的sku映射成商城goodsId
        list.forEach(resp -> {
            JdSimilarSkuGoodsRespVo vo = new JdSimilarSkuGoodsRespVo();
            cn.hutool.core.bean.BeanUtil.copyProperties(resp, vo);
            Map<String, JdSimilarSkuGoodsRespVo.SaleLabelValue> saleLabelMap = vo.getSaleLabelMap();
            saleLabelMap.forEach((key, value) -> skuIdSet.addAll(value.getSkuIdSet()));
            resultList.add(vo);
        });
        if (CollectionUtil.isEmpty(skuIdSet)) {
            return resultList;
        }

        List<ShopGoods> goodsList = new LambdaQueryChainWrapper<ShopGoods>(shopGoodsMapper)
                .in(ShopGoods::getGoodsSku, skuIdSet)
                .eq(ShopGoods::getSupplierCode, supplierCode)
                .eq(BaseEntity::getIsEnable, 1)
                .list();

        if (StrUtil.isNotBlank(goodsPoolIds)) {
            goodsList = filterGoodsPools(goodsPoolIds, goodsList);
        }
        Map<String, List<ShopGoods>> listMap = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getGoodsSku));
        resultList.forEach(goods -> {
            Map<String, JdSimilarSkuGoodsRespVo.SaleLabelValue> saleLabelMap = goods.getSaleLabelMap();
            saleLabelMap.forEach((key, value) -> {
                List<String> goodsIds = new ArrayList<>();
                value.getSkuIdSet().forEach(i -> {
                    if (listMap.get(i) != null) {
                        goodsIds.add(listMap.get(i).get(0).getGoodsId().toString());
                    }
                });
                value.setGoodsIds(goodsIds);
            });
        });
        return resultList;
    }

    @NotNull
    private List<ShopGoods> filterGoodsPools(String goodsPoolIds, List<ShopGoods> goodsList) {
        List<String> goodsCodes = CollStreamUtil.toList(goodsList, ShopGoods::getGoodsCode);
        Set<Long> goodsIds = new HashSet<>();
        List<String> split = StrUtil.split(goodsPoolIds, StrUtil.C_COMMA);
        split.forEach(poolId -> {
            List<ShopGoods> poolGoodsList = dfmallGoodsPoolSubService.getUpShopGoodsByGoodsCode(Long.valueOf(poolId), goodsCodes);
            Set<Long> collect = poolGoodsList.stream().map(ShopGoods::getGoodsId).collect(Collectors.toSet());
            goodsIds.addAll(collect);
        });
        return goodsList.stream().filter(item -> goodsIds.contains(item.getGoodsId())).collect(Collectors.toList());
    }


    public List<Long> queryGoodsIdByClassSup(String classId, String supplierCode) {
        return shopGoodsMapper.queryGoodsIdByClassSup(classId, supplierCode);
    }

    public void shopGoodsDownByGoodsCodeList(Long goodsPoolId, List<ShopGoodsUpDownExcelVo> list) {
        if (CollectionUtil.isEmpty(list)) return;
        DfmallGoodsPoolVo dfmallGoodsPoolVo = dfmallGoodsPoolService.queryGoodsPoolById(goodsPoolId);
        if (dfmallGoodsPoolVo == null) throw new NotFoundException("商品池未找到");
        //获取所有供应商
        List<ShopSupplier> allSupplier = supplierSrv.list(new LambdaQueryWrapperX<ShopSupplier>().eq(ShopSupplier::getApproveState, 1)
                .eq(BaseEntity::getIsEnable, 1)
                .select(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));
        Map<String, String> supplierMap = allSupplier.stream().collect(Collectors.toMap(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode, (a, b) -> a));
        List<ShopGoods> goodsList = this.list(new LambdaQueryWrapperX<ShopGoods>()
                .select(ShopGoods::getGoodsSku, ShopGoods::getSupplierCode, ShopGoods::getGoodsCode)
                .in(ShopGoods::getGoodsSku, list.stream().map(ShopGoodsUpDownExcelVo::getGoodsSku).collect(Collectors.toList()))
        );

        Map<String, ShopGoods> goodsMap = goodsList.stream().collect(Collectors.toMap(e -> e.getSupplierCode() + e.getGoodsSku(), Function.identity()));

        GoodsPushDownDto goodsPushDownDto = new GoodsPushDownDto();
        goodsPushDownDto.setGoodsPoolId(goodsPoolId);
        List<Long> goodsIds = new ArrayList<>();
        list.forEach(e -> {
            String supplierCode = supplierMap.get(e.getSupplierName());
            if (StringUtils.isBlank(supplierCode)) return;
            ShopGoods shopGoods = goodsMap.get(supplierCode + e.getGoodsSku());
            if (shopGoods == null) return;
            goodsIds.add(shopGoods.getGoodsId());
            goodsPushDownDto.setDownReason(e.getDownReason());
        });
        goodsPushDownDto.setGoodsIds(StringUtils.join(goodsIds, ","));
        goodsZoneService.goodsPushDown(goodsPushDownDto);

        log.info("已处理下架商品数量 :{}", goodsIds.size());
    }

    public List<RemoteMoqInfoResp> queryGoodsMoqBatch(QueryGoodsMoqBatchDto queryGoodsMoqBatchDto) {
        List<RemoteMoqInfoResp> remoteMoqInfoResps = new ArrayList<>();
        List<QueryGoodsMoqBatchDto.GoodsInfo> skuInfoList = queryGoodsMoqBatchDto.getSkuInfoList();
        Map<String, List<String>> map = skuInfoList.stream().collect(Collectors.groupingBy(QueryGoodsMoqBatchDto.GoodsInfo::getSupplierCode, Collectors.mapping(QueryGoodsMoqBatchDto.GoodsInfo::getGoodsSku, Collectors.toList())));
        map.keySet().forEach(key -> {
            List<String> value = map.get(key);
            List<RemoteMoqInfoResp> skusMoq = rmtMng.getSkusMoq(value, key);
            remoteMoqInfoResps.addAll(skusMoq);
        });
        return remoteMoqInfoResps;
    }

    public List<JdSimilarSkuGoodsRespVo> getSimilarListByGoodsCode(String goodsCode, String goodsPoolIds) {
        ShopGoods shopGoods = this.selectOneByGoodsCode(goodsCode);
        if (shopGoods == null) {
            return new ArrayList<>();
        }
        return this.getSimilarSkuList(shopGoods.getGoodsSku(), shopGoods.getSupplierCode(),goodsPoolIds);
    }

    public List<ShopGoodsSupplierVo> queryShopGoodsSupplierByGoodCodes(Collection<String> goodCodes) {
        return shopGoodsMapper.queryShopGoodsSupplierByGoodCodes(goodCodes);
    }

    /**
     * 为商品添加可售标签
     *
     * @param addLabelDto 打标签入参
     */
    public void goodsAddLabel(GoodsAddLabelDto addLabelDto) {
        if (addLabelDto.getGoodsIds().size() > 1000) {
            throw new ParameterException("单次修改商品数量不得大于1000");
        }
        List<ShopGoodsSaleLabelEntity> saleLabelList = new ArrayList<>();

        List<ShopGoodsSaleLabelVo> oldLabelList = goodsSaleLabelService.queryLabelByGoodsIds(addLabelDto.getGoodsIds());
        if (addLabelDto.getSaveOrUpdate() == 1) {
            // 1. 提前处理目标标签集合（去重+Hash加速）
            Set<String> targetLabels = Optional.ofNullable(addLabelDto.getLabelCode())
                    .map(code -> code.split(","))
                    .map(Arrays::asList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 2. 筛选待删除的 ID（流式处理避免显式循环）
            List<Long> deletedIds = oldLabelList.stream()
                    .filter(vo -> !targetLabels.contains(vo.getGoodsLabel()))
                    .map(ShopGoodsSaleLabelVo::getId)
                    .collect(Collectors.toList());

            // 3. 批量删除（避免空操作）
            if (!deletedIds.isEmpty()) {
                goodsSaleLabelService.delByIds(deletedIds);
            }
        }

        Map<Long, List<ShopGoodsSaleLabelVo>> groupList = oldLabelList.stream().filter(vo -> vo.getOrganizationId() == 0L).collect(Collectors.groupingBy(ShopGoodsSaleLabelVo::getGoodsId));


        if (StringUtils.isNotBlank(addLabelDto.getLabelCode())) {
            List<Map<String, String>> authCodes = getAuthCodes(addLabelDto);
            addLabelDto.getGoodsIds().forEach(goodsId -> {
                List<String> oldLabel = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(groupList) && CollectionUtil.isNotEmpty(groupList.get(goodsId))) {
                    oldLabel = groupList.get(goodsId).stream().map(ShopGoodsSaleLabelVo::getGoodsLabel).collect(Collectors.toList());
                }

                List<String> finalOldLabel = oldLabel;

                authCodes.forEach(map -> {
                    ShopGoodsSaleLabelEntity goodsSaleLabel = new ShopGoodsSaleLabelEntity();
                    goodsSaleLabel.setGoodsId(goodsId);
                    goodsSaleLabel.setOrganizationId(Objects.isNull(addLabelDto.getOrgId()) ? 0L : addLabelDto.getOrgId());
                    for (Map.Entry<String, String> en : map.entrySet()) {
                        if (finalOldLabel.contains(en.getValue())) {
                            continue;
                        }
                        goodsSaleLabel.setAuthMode(en.getKey());
                        goodsSaleLabel.setGoodsLabel(en.getValue());
                        goodsSaleLabel.setIsGlobal(en.getValue().equals(GoodsLabelEnum.JI_CAI.getCode()) ? 1 : addLabelDto.getIsGlobal());
                        saleLabelList.add(goodsSaleLabel);
                    }
                });


            });
            goodsSaleLabelService.saveBatch(saleLabelList);
        }


    }

    /**
     * 根据标签获取认证方式
     *
     * @param addLabelDto 标签入参
     * @return 返回认证方式
     */
    @NotNull
    private List<Map<String, String>> getAuthCodes(GoodsAddLabelDto addLabelDto) {
        String[] labelCodes = addLabelDto.getLabelCode().split(",");

        List<Map<String, String>> authLabel = new ArrayList<>();
        for (String labelCode : labelCodes) {
            Map<String, String> code = new HashMap<>();
            switch (GoodsLabelEnum.toCode(labelCode)) {
                case JI_CAI:
                case HELP_POOR:
                case HELP_POVERTY:
                    code.put(GoodsAuthMode.HEAD_OFFICE.getCode(), labelCode);
                    break;
                case SYS_SRM_SOURCE:
                case XIAN_XIA_BI_JIA:
                case COMPANY_PURCHASE_AUDIT:
                case COMPANY_BI_JIA:
                case COMPANY_FRAMEWORK:
                case DF_AUTONOMY_PRODUCT:
                case VOYAH_PURCHASE:
                case HONDA_PURCHASE:
                    code.put(GoodsAuthMode.PURCHASE_ALLIANCE.getCode(), labelCode);
                    break;
                case XIAN_SHANG_BI_JIA:
                case JD_ZI_YING:
                case DS_ZI_YING:
                case DS_OEM:
                case PING_TAI_AUDIT:
                case AUTONOMY_PRODUCT:
                    // 平台认证
                    code.put(GoodsAuthMode.PLATFORM_AUTH.getCode(), labelCode);
                default:
                    break;
            }
            authLabel.add(code);
        }

        return authLabel;
    }

    public void goodsDelLabel(GoodsAddLabelDto addLabelDto) {
        goodsSaleLabelService.delLabelCodeByGoodsId(addLabelDto.getGoodsIds(), addLabelDto.getLabelCode());
    }

    public void goodsFlowCheck(ShopGoodsUpDownDto goodsUpDownDto) {
        List<String> suppluerList = new ArrayList<>();
        if (StringUtils.isNotBlank(goodsUpDownDto.getSupplierCode())) {
            suppluerList = Arrays.stream(goodsUpDownDto.getSupplierCode().split(",")).collect(Collectors.toList());
        }
        goodsFlowManage.goodsFlowCheck(suppluerList);
    }

    public void goodsPoolShelvesCheck() {
        TenantUtils.execute(TenantContextHolder.getTenantId(), () -> {
            LambdaQueryWrapperX<DfmallGoodsPoolEntity> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eq(DfmallGoodsPoolEntity::getGoodsPoolLevel, GoodsPoolLevelEnum.POOL_LEVEL_PLATFORM.getValue());
            DfmallGoodsPoolEntity pool = dfmallGoodsPoolService.getOne(queryWrapper);
            List<Long> ids = dfmallGoodsPoolService.queryGoodsPoolGoodsId(pool.getId());
            goodsUpDownManage.goodsIdUpToLiteFlow(ids);
        });
    }


    public List<ShopGoodsVo> queryHondaGoodsMaterials(List<String> goodsCodeList) {
        return shopGoodsMapper.queryHondaGoodsMaterials(goodsCodeList);
    }

    /**
     * 更新单个商品
     *
     * @param goodsCode 商品编码
     */
    public void goodsCodeUpToLiteFlow(List<String> goodsCode) {
        List<ShopGoods> goodsList = this.getBaseMapper().selectList(new LambdaQueryWrapperX<ShopGoods>()
                .select(ShopGoods::getGoodsId,
                        ShopGoods::getGoodsCode,
                        ShopGoods::getGoodsSku,
                        ShopGoods::getSupplierCode,
                        ShopGoods::getTenantId)
                .in(ShopGoods::getGoodsCode, goodsCode)
                .eq(ShopGoods::getAuditState, 1));
        Map<Long,List<ShopGoods>> tenantGroup = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getTenantId));
        tenantGroup.forEach( (tenantId,item)-> TenantUtils.execute(tenantId, () -> goodsFlowManage.poolPushProcess(item)));
    }

    /**
     * 查询商品合同申请记录
     *
     * @param goodsId 商品ID
     * @return 返回结果
     */
    public List<GoodsContractApprovalVo> queryGoodsContractList(Long goodsId) {
        return goodsContractService.queryGoodsContractList(goodsId);
    }

    /**
     * 查询商品合同申请记录
     *
     * @param goodsId 商品ID
     * @return 返回结果
     */
    public List<GoodsContractApprovalVo> queryGoodsContractApproval(Long goodsId) {
        return goodsContractService.queryGoodsApprovalList(goodsId);
    }

    public GoodsMasterDetailVo queryGoodsByGoodsId(Long goodsId) {
        ShopGoods shopGoods = this.getById(goodsId);
        if (shopGoods == null) {
            throw new NotFoundException("商品[" + goodsId + "]不存在");
        }
        GoodsMasterDetailVo goodsMasterDetailVo = this.queryGoodsMasterDetailByCode(shopGoods.getGoodsCode());

        //获取商品对应合同信息
        List<ShopGoodsContractVo> shopGoodsContractVos = goodsContractService.queryVoByGoodsIds(Arrays.asList(goodsId), null);

        List<ShopGoodsContractVo> goodsContractVos = shopGoodsContractVos.stream().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())) // 倒序排序
                .collect(Collectors.toList());
        goodsMasterDetailVo.setContractInfoList(DataAdapter.convertList(goodsContractVos, ShopSupplierContractDto.class));

        //展示附加信息
        this.keepIncidentalDetail(goodsMasterDetailVo, shopGoods.getGoodsCode());

        return goodsMasterDetailVo;
    }

    public void keepIncidentalDetail(GoodsMasterDetailVo goodsMasterDetailVo, String goodsCode) {
        if (StringUtils.isBlank(goodsCode)) return;
        ShopGoodsIncidentalDetail shopGoodsIncidentalDetail = shopGoodsIncidentalDetailService.selectOneByGoodsCode(goodsCode);
        if (shopGoodsIncidentalDetail != null) {
            goodsMasterDetailVo.setFileUrl(shopGoodsIncidentalDetail.getFileUrl())
                    .setGoodsSource(shopGoodsIncidentalDetail.getGoodsSource())
                    .setSeekPriceNumbers(shopGoodsIncidentalDetail.getSeekPriceNumbers())
                    .setMarketReference(shopGoodsIncidentalDetail.getMarketReference());
        }
    }

    public List<GoodsSaleLabelVo> getGoodsSaleLabel(List<Long> goodsIds, String companyCode) {
        List<GoodsSaleLabelVo> goodsSaleLabelVos = new ArrayList<>();
        //获取用户所属的组织ID
        LoginUser user = LocalUserHolder.get();
        for (Long goodsId : goodsIds) {
            GoodsSaleLabelVo labelVo = new GoodsSaleLabelVo();
            labelVo.setGoodsId(goodsId);

            int goodsSale = 0;
            String label = "";
            String auth = "";
            String labelName = "";
            String authName = "";


            ShopGoods goods = goodsSrv.getByGoodsId(String.valueOf(goodsId));
            if (Objects.isNull(goods)) {
                labelVo.setAuthMode(auth);
                labelVo.setGoodsAuthName(authName);
                labelVo.setGoodsLabel(label);
                labelVo.setGoodsLabelName(labelName);
                labelVo.setIsSale(StringHelper.IsEmptyOrNull(label) ? 0 : goodsSale);
                goodsSaleLabelVos.add(labelVo);
                continue;
            }
            ShopSupplier supplier = supplierSrv.selectByCode(goods.getSupplierCode());
            //如果是独立供应商商品，需要校验合同有效性
            boolean checkContract = goodsContractService.checkGoodsOrgContract(supplier, goodsId, user.getEntityOrganizationId());

            //校验商品是否有可售标签
            List<ShopGoodsSaleLabelVo> goodsLabelList = goodsSaleLabelService.queryLabelByGoodsIds(Collections.singletonList(goodsId));
            DfmallGoodsPoolEntity dfmallGoodsPool = dfmallGoodsPoolService.queryCompanyPool(LocalUserHolder.get().getEntityOrganizationCode());
            if (StrUtil.isNotBlank(companyCode)) {
                dfmallGoodsPool = dfmallGoodsPoolService.queryCompanyPool(companyCode);
            }

            if (CollectionUtil.isNotEmpty(goodsLabelList) && !Objects.isNull(dfmallGoodsPool) && checkContract) {
                if (Objects.isNull(dfmallGoodsPool.getSaleLiteFlow())) {
                    // 平台规则查询可售： 平台可售标签、 其他企业认证全平台可售 如日产 乘用车 等通过比价认证可售、 企业自己通过其他方式认证的企业可售标签
                    List<ShopGoodsSaleLabelVo> platformList = goodsLabelList.stream()
                            .filter(labelEntity -> labelEntity.getOrganizationId().equals(0L)
                                    || labelEntity.getIsGlobal() == 1
                                    || labelEntity.getOrganizationId().equals(LocalUserHolder.get().getEntityOrganizationId()))
                            .collect(Collectors.toList());

                    if(!LocalUserHolder.get().getOrgRangeMark().equals("DFS") && !"VOYAH".equals(LocalUserHolder.get().getEntityOrganizationCode())){
                        platformList.removeIf( l ->  l.getGoodsLabel().equals("LB012")  );
                    }
                    //平台可售规则
                    if (CollectionUtil.isNotEmpty(platformList)) {
                        label = platformList.stream().map(ShopGoodsSaleLabelVo::getGoodsLabel).collect(Collectors.joining(","));
                        auth = platformList.stream().map(ShopGoodsSaleLabelVo::getAuthMode).collect(Collectors.joining(","));
                        labelName = platformList.stream().map(ShopGoodsSaleLabelVo::getLabelName).collect(Collectors.joining(","));
                        authName = platformList.stream().map(ShopGoodsSaleLabelVo::getAuthName).collect(Collectors.joining(","));
                    }
                } else {
                    //企业有自己可售规则的：集团认证可售的必须可售、 本企业认证的可售标签
                    List<ShopGoodsSaleLabelVo> companyLabel = goodsLabelList.stream()
                            .filter(labelEntity -> labelEntity.getOrganizationId().equals(user.getEntityOrganizationId())
                                    || labelEntity.getAuthMode().equals(GoodsAuthMode.HEAD_OFFICE.getCode()))
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(companyLabel)) {
                        label = companyLabel.stream().map(ShopGoodsSaleLabelVo::getGoodsLabel).collect(Collectors.joining(","));
                        auth = companyLabel.stream().map(ShopGoodsSaleLabelVo::getAuthMode).collect(Collectors.joining(","));
                        labelName = companyLabel.stream().map(ShopGoodsSaleLabelVo::getLabelName).collect(Collectors.joining(","));
                        authName = companyLabel.stream().map(ShopGoodsSaleLabelVo::getAuthName).collect(Collectors.joining(","));
                    }
                }
            }

            //商品在企业是否可售
            if (StrUtil.isNotBlank(companyCode)) {
                SystemOrganization organization = organizationService.getOrganizationByCode(companyCode);
                Long isSale = goodsZoneService.queryCompanyGoodsIsSale(goodsId, organization.getId());
                goodsSale = isSale.intValue();
            } else {
                Long isSale = goodsZoneService.queryUserGoodIsSale(goodsId, user.getId());
                goodsSale = isSale.intValue();
            }

            // 获取商品的远程价格信息
            RemotePriceInfoResp remotePriceInfoResp = priceGetter.getSalePrice(supplier.getSupplierCode(),
                    goods.getGoodsSku());

            //实时获取商品的可售(电商自营，比质比价)
            ShopGoodsDetailVo shopGoodsDetailVo = new ShopGoodsDetailVo();
            shopGoodsDetailVo.setGoodsSku(goods.getGoodsSku());
            // 设置商品的销售价格和未税价
            shopGoodsDetailVo.setGoodsSalePrice(remotePriceInfoResp.getGoodsSalePrice());
            shopGoodsDetailVo.setGoodsNakedSalePrice(remotePriceInfoResp.getGoodsNakedSalePrice());
            // 商品含税协议价何未税协议价
            shopGoodsDetailVo.setGoodsPactPrice(remotePriceInfoResp.getGoodsPactPrice());
            shopGoodsDetailVo.setGoodsPactNakedPrice(remotePriceInfoResp.getGoodsPactNakedPrice());
            shopGoodsDetailVo.setAuthMode(auth);
            shopGoodsDetailVo.setGoodsAuthName(authName);
            shopGoodsDetailVo.setGoodsLabel(label);
            shopGoodsDetailVo.setGoodsLabelName(labelName);
            shopGoodsDetailVo.setSupplierType(supplier.getSupplierType());
            shopGoodsDetailVo.setSupplierCode(supplier.getSupplierCode());
            this.queryLiteFlowParams(dfmallGoodsPool, shopGoodsDetailVo);

            labelVo.setAuthMode(shopGoodsDetailVo.getAuthMode());
            labelVo.setGoodsAuthName(shopGoodsDetailVo.getGoodsAuthName());
            labelVo.setGoodsLabel(shopGoodsDetailVo.getGoodsLabel());
            labelVo.setGoodsLabelName(shopGoodsDetailVo.getGoodsLabelName());
            labelVo.setIsSale(StringHelper.IsEmptyOrNull(shopGoodsDetailVo.getGoodsLabel()) || !checkContract ? 0 : goodsSale);
            goodsSaleLabelVos.add(labelVo);
        }
        return goodsSaleLabelVos;
    }

    public List<GoodsProcessVo> queryGoodsTrack(String goodsSku, String supplierCode) {
        ShopSupplier supplier = supplierSrv.selectByCode(supplierCode);
        List<GoodsProcessVo> processVos = new ArrayList<>();
        GoodsProcessVo goodsProcessVo;
        if (supplier.getSupplierType() == 0) {
            //查询消息
            goodsProcessVo = goodsProcessMonitorSrv.queryStep1(supplierCode, goodsSku);
            goodsProcessVo.setNodeName("消息拉取");
            processVos.add(goodsProcessVo);
            //消息详情
            goodsProcessVo = goodsProcessMonitorSrv.queryStep2(supplierCode, goodsSku);
            goodsProcessVo.setNodeName("消息详情");
            processVos.add(goodsProcessVo);
            //商品校验
            goodsProcessVo = goodsProcessMonitorSrv.queryStep3(supplierCode, goodsSku);
            goodsProcessVo.setNodeName("商品校验");
            processVos.add(goodsProcessVo);
            //商品转换
            goodsProcessVo = goodsProcessMonitorSrv.queryStep4(supplierCode, goodsSku);
            goodsProcessVo.setNodeName("商品转换");
            processVos.add(goodsProcessVo);
        }
        //商品上架
        goodsProcessVo = new GoodsProcessVo();
        goodsProcessVo.setNodeName("商品上架");
        ShopGoods goods = shopGoodsMapper.selectBySkuAndSupplier(goodsSku, supplierCode);
        if (null != goods) {
            DfmallGoodsPoolEntity pool = dfmallGoodsPoolService.queryPlatformPool();
            DfmallGoodsPoolSubEntity poolSub = dfmallGoodsPoolSubService.getPoolByGoodsId(goods.getGoodsId(), pool.getId());
            if (poolSub != null) {
                goodsProcessVo.setStatus(1);
                goodsProcessVo.setCreateTime(goods.getCreateTime());
                goodsProcessVo.setProcessTime(poolSub.getCreateTime());
            } else {
                goodsProcessVo.setContent(goods.getShelvesReason());
            }
        }
        processVos.add(goodsProcessVo);
        return processVos;
    }

    public List<GoodsProcessVo> queryCompanyGoodsTrack(Long goodsId) {
        ShopGoods goods = shopGoodsMapper.selectByGoodsId(goodsId);
        List<GoodsProcessVo> processVos = new ArrayList<>();
        if (openApiConfig.getSuppliers().contains(goods.getSupplierCode()) || goods.getSupplierType() == 0) {
            List<GoodsRelationPoolVo> goodsPoolVos = dfmallGoodsPoolSubService.queryGoodsRelationPool(goodsId, Collections.singletonList(2L));
            goodsPoolVos.forEach(goodsPool -> {
                GoodsProcessVo goodsProcessVo = new GoodsProcessVo();
                goodsProcessVo.setNodeName(goodsPool.getCompanyName());
                goodsProcessVo.setStatus(1);
                goodsProcessVo.setCreateTime(goodsPool.getCreateTime());
                goodsProcessVo.setProcessTime(goodsPool.getCreateTime());
                goodsProcessVo.setGoodsCode(goods.getGoodsCode());
                goodsProcessVo.setShelvesState(1);
                goodsProcessVo.setCompanyCode(goodsPool.getCompanyCode());
                processVos.add(goodsProcessVo);
            });
        } else {
            List<GoodsContractApprovalVo> goodsContractApprovalVos = this.queryGoodsContractApproval(goodsId);
            goodsContractApprovalVos.forEach(goodsContract -> {
                GoodsProcessVo goodsProcessVo = new GoodsProcessVo();
                goodsProcessVo.setNodeName(goodsContract.getSignCompanyName());
                goodsProcessVo.setStatus(goodsContract.getApprovalState());
                goodsProcessVo.setCreateTime(goodsContract.getCreateTime());
                goodsProcessVo.setProcessTime(goodsContract.getUpdateTime());
                goodsProcessVo.setGoodsCode(goods.getGoodsCode());
                goodsProcessVo.setShelvesState(goodsContract.getShelvesState());
                goodsProcessVo.setCompanyCode(goodsContract.getSignCompanyCode());
                processVos.add(goodsProcessVo);
            });
        }
        return processVos;
    }


    /**
     * 同步商品池商品
     *
     * @param goodsPoolId 商品池ID
     */
    @Async
    public void goodsPoolSync(Long goodsPoolId) {
        DfmallGoodsPoolVo goodsPool = dfmallGoodsPoolService.getBaseMapper().queryLiteFlowInfoByPool(null, null, goodsPoolId).get(0);
        long parentPoolId = goodsPoolId;
        if (goodsPool.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_COMPANY.getValue())) {
            //企业池同步，查询平台池的全部商品
            parentPoolId = dfmallGoodsPoolService.queryPlatformPool().getId();
        }
        if (goodsPool.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_ZONE.getValue()) && goodsPool.getLiteFlowId() != null) {
            if (StringUtils.isNotBlank(goodsPool.getCompanyCode())) {
                //企业专区池同步，查询企业的全部商品
                parentPoolId = dfmallGoodsPoolService.queryCompanyPool(goodsPool.getCompanyCode()).getId();
            } else {
                //平台专区池同步，查询平台池的全部商品
                parentPoolId = dfmallGoodsPoolService.queryPlatformPool().getId();
            }

        }

        long goodsId = 0L;
        int page = 1000;

        long count = 0L;

        for (; ; ) {
            //查询
            List<ShopGoods> goodsList = dfmallGoodsPoolSubService.queryAllByGoodsId(parentPoolId, goodsId, page);

            goodsFlowManage.poolPushProcess(goodsList);

            count += goodsList.size();
            if (goodsList.size() < page) {
                log.info("goodsPoolSync 结束,最后一次校验商品数量:{}", goodsList.size());
                break;
            }
            goodsId = goodsList.get(goodsList.size() - 1).getGoodsId();

        }
        log.info("goodsPoolSync 结束,处理商品总数量:{}", count);
    }


    public Boolean checkHasSale(Long goodsId) {
        Integer count = this.getBaseMapper().selectCountByGoodsId(goodsId);
        return count > 0;
    }

    public List<GoodsMasterDetailVo> queryGoodsDetailByCodes(List<String> goodsCodes, Long goodsPoolId) {
        if (goodsCodes.isEmpty()) {
            return new ArrayList<>();
        }
        List<GoodsMasterDetailVo> result = new ArrayList<>();
        return this.shopGoodsMapper.queryGoodsDetailByCodes(goodsCodes, goodsPoolId);
    }

    /**
     * 查询商品详情.
     *
     * @return 商品详情
     */
    @DataPermission(enable = false)
    public List<ShareCompanyListVO> selectByCompanyFilter() {
        return this.getBaseMapper().selectByCompanyFilter(customLy);
    }

    public List<Map<String,String>> goodsSyncTenantId(String goodsIds) {
        List<Long> ids = Arrays.stream(goodsIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        // 获取商品列表
        List<ShopGoods> goodsList = this.getBaseMapper().selectList(
                new LambdaQueryWrapperX<ShopGoods>().in(ShopGoods::getGoodsId, ids)
        );
        List<String> codes = goodsList.stream()
                .map(ShopGoods::getGoodsCode)
                .collect(Collectors.toList());

        // 将商品列表转换为Map
        Map<Long, ShopGoods> goodsMap = toMap(goodsList, ShopGoods::getGoodsId);

        // 获取分类列表并转换为Map
        List<String> firstLevelGcids = goodsList.stream()
                .map(ShopGoods::getFirstLevelGcid)
                .collect(Collectors.toList());
        Map<Long, YphStandardClassEntity> classMap = toMap(
                yphStandardClassService.getBaseMapper().selectList(
                        new LambdaQueryWrapperX<YphStandardClassEntity>()
                                .in(YphStandardClassEntity::getStandardClassId, firstLevelGcids.stream().map(Long::valueOf).collect(Collectors.toList()))
                ),
                YphStandardClassEntity::getStandardClassId
        );

        // 获取商品价格列表并转换为Map
        Map<String, ShopGoodsPrice> goodsPriceMap = toMap(
                priceSrv.getBaseMapper().selectList(
                        new LambdaQueryWrapperX<ShopGoodsPrice>().in(ShopGoodsPrice::getGoodsCode, codes)
                ),
                ShopGoodsPrice::getGoodsCode
        );

        // 获取商品价格列表并转换为Map
        Map<String, ShopGoodsDetail> goodsDetailMap = toMap(
                shopGoodsDetailService.getBaseMapper().selectList(
                        new LambdaQueryWrapperX<ShopGoodsDetail>().in(ShopGoodsDetail::getGoodsCode, codes)
                ),
                ShopGoodsDetail::getGoodsCode
        );

        // 获取商品附加信息列表并转换为Map
        Map<String, ShopGoodsIncidentalDetail> goodsIncidentalMap = toMap(
                shopGoodsIncidentalDetailService.getBaseMapper().selectList(
                        new LambdaQueryWrapperX<ShopGoodsIncidentalDetail>()
                                .in(ShopGoodsIncidentalDetail::getGoodsCode, codes)
                ),
                ShopGoodsIncidentalDetail::getGoodsCode
        );

        Map<String, SystemContractEntity> goodsContractEntityMap = new HashMap<>();
        List<ShopGoodsContractEntity> goodsContract = goodsContractService.getBaseMapper().selectList(
                new LambdaQueryWrapperX<ShopGoodsContractEntity>()
                        .in(ShopGoodsContractEntity::getGoodsCode, codes)
                        .eq(ShopGoodsContractEntity::getOrgType,3));
        List<Long> cIds = goodsContract.stream().map(ShopGoodsContractEntity::getContractId).collect(Collectors.toList());
        // 根据 contractId 列表查询 SystemContractEntity 列表
        if(!cIds.isEmpty()){
            Map<Long, SystemContractEntity> contractMap = systemContractService.listByIds(cIds)
                    .stream()
                    .collect(Collectors.toMap(SystemContractEntity::getId, Function.identity()));

            goodsContract.forEach(gc -> {
                SystemContractEntity contract = contractMap.get(gc.getContractId());
                if (contract != null) {
                    goodsContractEntityMap.put(gc.getGoodsCode(), contract);
                }
            });
        }

        return this.syncGoods(ids, goodsMap, classMap, goodsPriceMap, goodsDetailMap, goodsIncidentalMap,goodsContractEntityMap);


    }

    private List<Map<String,String>> syncGoods(List<Long> ids,
                           Map<Long, ShopGoods> goodsMap,
                           Map<Long, YphStandardClassEntity> classMap,
                           Map<String, ShopGoodsPrice> goodsPriceMap,
                           Map<String, ShopGoodsDetail> goodsDetailMap,
                           Map<String, ShopGoodsIncidentalDetail> goodsIncidentalMap,
                           Map<String, SystemContractEntity> goodsContractEntityMap) {

        // 获取当前租户ID，避免重复调用
        final Long localTenantId = TenantContextHolder.getRequiredTenantId();
        List<Map<String,String>> resultList = new ArrayList<>();
        ids.stream()
                .map(goodsMap::get) // 转换为商品流
                .filter(Objects::nonNull) // 过滤空值
                .forEach(shopGoods -> {
                    Map<String,String> result = new HashMap<>();
                    try {
                        processSingleGoods(
                                shopGoods,
                                localTenantId,
                                classMap,
                                goodsPriceMap,
                                goodsDetailMap,
                                goodsIncidentalMap,
                                goodsContractEntityMap
                        );
                        result.put(shopGoods.getGoodsCode(),"同步成功");
                    }catch (Exception e){
                        log.error("商品["+shopGoods.getGoodsCode()+"]同步失败:{}",e.getMessage());
                        result.put(shopGoods.getGoodsCode(),"同步失败:"+e.getMessage());
                    }
                    resultList.add(result);
                });
        return resultList;
    }

    private void processSingleGoods(ShopGoods shopGoods,
                                    Long localTenantId,
                                    Map<Long, YphStandardClassEntity> classMap,
                                    Map<String, ShopGoodsPrice> goodsPriceMap,
                                    Map<String, ShopGoodsDetail> goodsDetailMap,
                                    Map<String, ShopGoodsIncidentalDetail> goodsIncidentalMap,
                                    Map<String, SystemContractEntity> goodsContractEntityMap) {
        String tenantName = localTenantId == 1 ? "东风商城(积分)" : "东风商城";
        // 获取供应商信息（修复初始化问题）
        ShopSupplier supplier = supplierSrv.selectByCode(shopGoods.getSupplierCode());
        if (supplier == null) throw new ParameterException("此商品暂未取得["+tenantName+"]上架授权，请联系供应链经理");;
//
//        // 提取租户处理逻辑
        List<Long> syncTenants = parseSyncTenants(systemTenantService.getTenantIds(), localTenantId);
        if (syncTenants.isEmpty()) throw new ParameterException("此商品暂未取得["+tenantName+"]上架授权，请联系供应链经理");
        // 分类信息同步
        syncClassInfo(shopGoods, syncTenants, classMap);

        // 商品信息同步
        syncGoodsInfo(
                shopGoods,
                syncTenants,
                goodsPriceMap.get(shopGoods.getGoodsCode()),
                goodsDetailMap.get(shopGoods.getGoodsCode()),
                goodsIncidentalMap.get(shopGoods.getGoodsCode()),
                goodsContractEntityMap.get(shopGoods.getGoodsCode())
        );
    }

    // 租户ID解析方法
    private List<Long> parseSyncTenants(List<Long> tenantIds, Long localTenantId) {
        return tenantIds.stream()
//                .map(String::trim)
//                .filter(tenant -> !tenant.isEmpty())
//                .map(Long::valueOf)
                .filter(tenantId -> !tenantId.equals(localTenantId))
                .collect(Collectors.toList());
    }

    // 分类信息同步
    private void syncClassInfo(ShopGoods shopGoods,
                               List<Long> syncTenants,
                               Map<Long, YphStandardClassEntity> classMap) {
        YphStandardClassEntity goodsClass = classMap.get(
                Long.valueOf(shopGoods.getFirstLevelGcid())
        );

        if (goodsClass != null) {
            YphStandardClassEntity classClone = cloneClassEntity(goodsClass);
            syncTenants.forEach(tenant ->
                    TenantUtils.execute(tenant, () ->
                            this.checkTenantClass(shopGoods, classClone)
                    )
            );
        }
    }

    // 实体克隆方法（防止原始对象被修改）
    private YphStandardClassEntity cloneClassEntity(YphStandardClassEntity original) {
        YphStandardClassEntity clone = new YphStandardClassEntity();
        BeanUtils.copyProperties(original, clone);
        clone.setStandardClassId(null);  // 明确清除特定字段
        return clone;
    }

    // 商品信息同步
    private void syncGoodsInfo(ShopGoods shopGoods,
                               List<Long> syncTenants,
                               ShopGoodsPrice price,
                               ShopGoodsDetail detail,
                               ShopGoodsIncidentalDetail incidental,
                               SystemContractEntity contract) {
        SupplierGoodsReleaseDto params = new SupplierGoodsReleaseDto();

        // 使用组合方法处理不同对象
        handleGoodsDetails(syncTenants,params, shopGoods, price, detail, incidental, contract);

        // 多租户同步
        syncTenants.forEach(tenant ->
                TenantUtils.execute(tenant, () -> {
                    params.setSaleClient(tenant.equals(yflTenantId) ? 2 : 1);
                    this.toSaveShopGoods(params);
                })
        );
    }

    // 构建分类代码
    private String buildClassCode(ShopGoods goods) {
        return String.join(",",
                goods.getFirstClass(),
                goods.getSecondClass(),
                goods.getThirdClass()
        );
    }

    // 处理商品明细
    private void handleGoodsDetails(List<Long> syncTenants,
                                    SupplierGoodsReleaseDto params,
                                    ShopGoods goods,
                                    ShopGoodsPrice goodsPrice,
                                    ShopGoodsDetail goodsDetail,
                                    ShopGoodsIncidentalDetail incidental,
                                    SystemContractEntity contract) {
        BeanUtil.copyProperties(goods, params);
        params.setGoodsClassCode(this.buildClassCode(goods));
        BeanUtil.copyProperties(goodsDetail, params);
        if (Objects.nonNull(goodsPrice)) {
            BeanUtil.copyProperties(goodsPrice, params);
        }
        if (Objects.nonNull(incidental)) {
            BeanUtil.copyProperties(incidental, params);
        }
        params.setGoodsBody(goods.getGoodsBoydUrl());
        params.setSpecArray(goodsDetail.getGoodsSpecArray());
        params.setGoodsId(null);
        params.setGoodsCode(null);
        params.setBrandId(null);
        String imageArray = goodsDetail.getGoodsImage();
        if(StrUtil.isNotBlank(goodsDetail.getGoodsImageMore())) imageArray = imageArray+","+goodsDetail.getGoodsImageMore();
        params.setImageArray(imageArray);

        if(Objects.nonNull(contract)){
            AtomicReference<Long> companyId = new AtomicReference<>(0L);
            //同步合同信息
            syncTenants.forEach(tenant ->
                    TenantUtils.execute(tenant, () -> companyId.set(organizationService.getOrganizationByCode(contract.getSignCompanyCode()).getId()))
            );
            SaveGoodsContractDto contractDto = new SaveGoodsContractDto();
            BeanUtil.copyProperties(goodsPrice, contractDto);
            contractDto.setContractCode(contract.getContractCode());
            contractDto.setGoodsId(goods.getGoodsId());
            contractDto.setCompanyOrgId(companyId.get());
            contractDto.setGoodsPlatformNakedPrice(goodsPrice.getGoodsSaleNakedPrice());
            contractDto.setGoodsPlatformPrice(goodsPrice.getGoodsSalePrice());
            contractDto.setOrgType(3);
            contractDto.setId(null);
            params.setSaveGoodsContactDto(Collections.singletonList(contractDto));
        }
    }

    /**
     * 通用方法：将List转换为Map，处理重复Key
     */
    private <K, V> Map<K, V> toMap(List<V> list, Function<V, K> keyMapper) {
        return list.stream()
                .collect(Collectors.toMap(
                        keyMapper,
                        Function.identity(),
                        (existing, replacement) -> existing // 处理重复Key
                ));
    }

    private void checkTenantClass(ShopGoods goods, YphStandardClassEntity localClass) {
        // 统一处理三级分类
        handleClassCreation(goods.getFirstClass(), goods.getFirstClassName(), localClass);
        handleClassCreation(goods.getSecondClass(), goods.getSecondClassName(), localClass);
        handleClassCreation(goods.getThirdClass(), goods.getThirdClassName(), localClass);
    }

    /**
     * 封装分类创建/更新的公共逻辑
     */
    private void handleClassCreation(String classCode, String className, YphStandardClassEntity localClass) {
        // 检查分类是否存在
        YphStandardClassEntity classEntity = yphStandardClassService.selectByCode(classCode);
        if (Objects.nonNull(classEntity)) {
            return;
        }

        // 不存在则创建
        YphStandardClassSaveDto classSaveDto = new YphStandardClassSaveDto();
        classSaveDto.setRequiredTenantId(TenantContextHolder.getTenantId());
        BeanUtil.copyProperties(localClass, classSaveDto);
        classSaveDto.setClassCode(classCode);
        classSaveDto.setClassName(className);
        yphStandardClassService.createOrUpdateClass(classSaveDto);
    }

    public void exportTenantSyncModel(HttpServletResponse response) throws IOException {
        // 构造导入模板数据
        List<ImportTenantSyncGoodsDto> list = Collections.singletonList(ImportTenantSyncGoodsDto.builder()
                .supplierName("震坤行工业超市（上海）有限公司")
                .goodsCode("DSZKH00UISDNBUSIGR")
                .build());

        // 导出 Excel
        ExcelUtils.write(response, "租户同步商品导入模板.xls", "商品列表", ImportTenantSyncGoodsDto.class, list);
    }

    public List<Map<String,String>> importTenantSyncGoods(List<ImportTenantSyncGoodsDto> importList) {
        List<Map<String,String>> resultList = new ArrayList<>();
        CollectionUtil.split(importList, 100).forEach(dtoList -> {
            List<String> codeList = dtoList.stream().map(ImportTenantSyncGoodsDto::getGoodsCode).collect(Collectors.toList());
            List<ShopGoods> goodsList = shopGoodsMapper.selectList(new LambdaQueryWrapperX<ShopGoods>()
                    .select(ShopGoods::getGoodsId)
                    .in(ShopGoods::getGoodsCode, codeList));
            List<Map<String,String>> itemResult = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(goodsList)){
                List<Long> goodsIds = goodsList.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList());
                itemResult = this.goodsSyncTenantId(goodsIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            resultList.addAll(itemResult);
        });
        return resultList;
    }

    public List<FileValidationResult> validateAndUploadFiles(MultipartFile[] files, String path) throws IOException {

        // 1. 提取商品编码并校验文件名格式
        Map<String, List<MultipartFile>> validFilesMap = Arrays.stream(files)
                .filter(f -> !f.isEmpty() && validateFileNameFormat(getFileNameWithoutExtension(f.getOriginalFilename())))
                .collect(Collectors.groupingBy(
                        f -> getFileNameWithoutExtension(f.getOriginalFilename()).split("-")[0]
                        ));

        // 2. 批量查询商品是否存在
        Set<String> existingCodes;
        if (!validFilesMap.isEmpty()) {
            List<ShopGoods> goodsList = this.baseMapper.selectList(
                    new LambdaQueryWrapper<ShopGoods>()
                            .in(ShopGoods::getGoodsCode, validFilesMap.keySet())
            );
            if (goodsList.isEmpty()) {
                throw new ParameterException("未找到任何商品，请检查数据");
            }
            existingCodes = goodsList.stream()
                    .map(ShopGoods::getGoodsCode)
                    .collect(Collectors.toSet());
        } else {
            existingCodes = new HashSet<>();
        }

        // 3. 生成校验结果
        List<FileValidationResult> results = Arrays.stream(files)
                .map(f -> {
                    if (f.isEmpty()) {
                        return new FileValidationResult("未知文件", false, "空文件", "");
                    }
                    String fileName = f.getOriginalFilename();
                    boolean isValidFormat = validateFileNameFormat(getFileNameWithoutExtension(fileName));
                    if (!isValidFormat) {
                        return new FileValidationResult(fileName, false, "格式错误", "");
                    }
                    String goodsCode = getFileNameWithoutExtension(fileName).split("-")[0];
                    boolean exists = existingCodes.contains(goodsCode);
                    String message = exists ? "校验通过" : "商品不存在";
                    return new FileValidationResult(fileName, exists, message, "");
                })
                .collect(Collectors.toList());

        // 4. 筛选出校验通过的文件进行上传
        List<MultipartFile> filesToUpload = results.stream()
                .filter(FileValidationResult::isValid) // 过滤出有效的文件验证结果
                .distinct()
                .flatMap(r -> {
                    // 获取文件名（不包含扩展名）并分割
                    String fileNameWithoutExtension = getFileNameWithoutExtension(r.getFileName());
                    String key = fileNameWithoutExtension.split("-")[0]; // 获取分组的 key

                    // 从 validFilesMap 中获取对应的 List<MultipartFile>
                    List<MultipartFile> fileLists = validFilesMap.get(key);

                    // 如果 fileLists 不为空，将其转换为 Stream<MultipartFile>，否则返回空的 Stream
                    return fileLists != null ? fileLists.stream() : Stream.empty();
                })
                .filter(Objects::nonNull) // 过滤掉 null 值
                .collect(Collectors.toList()); // 收集到 List 中

        // 5. 上传文件并合并结果
        if (!filesToUpload.isEmpty()) {

            MultipartFile[] filesArray = filesToUpload.stream()
                    .collect(Collectors.toMap(
                            MultipartFile::getOriginalFilename, // 以文件名作为 key
                            file -> file,                       // 保留文件对象
                            (existing, replacement) -> existing // 如果重复，保留已存在的文件
                    ))
                    .values() // 获取去重后的文件集合
                    .toArray(new MultipartFile[0]); // 转换为数组
            List<FileValidationResult> uploadResults = fileService.uploadBatchFilesWithNames(filesArray, path);
            results.addAll(uploadResults);
        }


        Map<String, String> goodsCodeToUrlMap = results.stream()
                .filter(f -> f.isValid() && StrUtil.isNotBlank(f.getUrl()))
                .collect(Collectors.toMap(
                        result -> getFileNameWithoutExtension(result.getFileName()).split("-")[0],
                        FileValidationResult::getUrl,
                        ( existingUrl , newUrl ) -> existingUrl + ";" + newUrl // 处理重复键
                ));

        goodsCodeToUrlMap.forEach((goodsCode, url) ->{
                String[] urls = url.split(";");

                String image = urls[0]; // 第一个图片地址
                String more = urls.length > 1 ?
                        String.join(";", Arrays.stream(Arrays.copyOfRange(urls, 1, urls.length))
                                .distinct()
                                .toArray(String[]::new)) :
                        "";
                LambdaUpdateWrapper<ShopGoodsDetail> updateWrapper = new LambdaUpdateWrapper<ShopGoodsDetail>()
                        .eq(ShopGoodsDetail::getGoodsCode, goodsCode)
                        .set(ShopGoodsDetail::getGoodsImage, image);
                if(StrUtil.isNotBlank(more)){
                    updateWrapper.set(ShopGoodsDetail::getGoodsImageMore,more);
                }
                shopGoodsDetailService.update(updateWrapper);
            }
        );
        return results;
    }

    // 辅助方法：提取文件名（不含扩展名）
    private String getFileNameWithoutExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDot = fileName.lastIndexOf('.');
        return (lastDot == -1) ? fileName : fileName.substring(0, lastDot);
    }

    private boolean validateFileNameFormat(String fileName) {
        return fileName != null && fileName.matches("^.*-\\d+$");
    }


    public List<Dfnissan01FactoryPriceVo> getDfnissan01FactoryPrice(String goodsSku) {
        return dfnissan01ContractMapper.getDfnissan01FactoryPrice(goodsSku);
    }

    /**
     * 独立供应商-新增商品-同款竞价-需要保存竞价数据
     * @param shopSupplier
     * @param params
     * @param shopGoods
     */
    private void _toMakeBiddingRecord(ShopSupplier shopSupplier, SupplierGoodsReleaseDto params, ShopGoods shopGoods, ShopGoodsDetail shopGoodsDetail) {
        // 竞价先不考虑有福利
        Long currentTenantId = TenantContextHolder.getTenantId();
        if(shopSupplier.getSupplierType()!=null
                && shopSupplier.getSupplierType()==1
                && params.getBiddingHotSaleGoodsId()!=null
                && !yflTenantId.equals(currentTenantId)) {
            BiddingSupplierGoodsSaveDto biddingSaveDto = getBiddingSupplierGoodsSaveDto(params, shopGoods, shopGoodsDetail);
            ServiceResult result = biddingSupplierGoodsService.goodsBidding(biddingSaveDto);
            if(result.getCode()!=Code.SUCCESS.getCode()){
                log.info("独立供应商【{}】参与同款竞价【{}】，商品【{}】失败：{}", shopSupplier.getSupplierCode(), params.getBiddingHotSaleGoodsId(), shopGoods.getGoodsSku(), result.getMsg());
                throw new ParameterException(result.getMsg());
            }
        }
    }

    private  BiddingSupplierGoodsSaveDto getBiddingSupplierGoodsSaveDto(SupplierGoodsReleaseDto params, ShopGoods shopGoods, ShopGoodsDetail shopGoodsDetail) {
        BiddingSupplierGoodsSaveDto biddingSaveDto = new BiddingSupplierGoodsSaveDto();
        biddingSaveDto.setFrom("create");
        biddingSaveDto.setGoodsId(shopGoods.getGoodsId());
        biddingSaveDto.setGoodsDesc(shopGoods.getGoodsDesc());
        biddingSaveDto.setGoodsSku(shopGoods.getGoodsSku());
        biddingSaveDto.setGoodsCode(shopGoods.getGoodsCode());
        biddingSaveDto.setSupplierName(shopGoods.getSupplierName());
        biddingSaveDto.setSupplierCode(shopGoods.getSupplierCode());
        biddingSaveDto.setBrandName(shopGoods.getBrandName());
        biddingSaveDto.setSaleUnit(shopGoods.getSaleUnit());
        biddingSaveDto.setGoodsImage(shopGoodsDetail.getGoodsImage());
        biddingSaveDto.setMaterialsCode(shopGoods.getMaterialsCode());
        biddingSaveDto.setBiddingHotSaleGoodsId(params.getBiddingHotSaleGoodsId());
        return biddingSaveDto;
    }

    public void cutPriceNoApproval(List<CutPriceNoApprovalDto> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        ShopGoods goods = this.selectOneByGoodsCode(dtoList.get(0).getGoodsCode());
        if (goods == null) {
            throw new ParameterException("商品不存在!");
        }
        List<ShopGoodsPrice> priceList = new ArrayList<>();
        List<YphGoodsPriceStrategy> priceStrategyList = new ArrayList<>();
        dtoList.forEach(dto -> {
            if (dto.getCompanyOrgId() == null) {
                // 平台合同
                ShopGoodsPrice price = priceSrv.selectOneByGoodsCode(dto.getGoodsCode());
                if (price == null) {
                    throw new ParameterException("平台价格不存在!");
                }
                if (dto.getGoodsPactNakedPrice().compareTo(price.getGoodsPactNakedPrice()) > 0 ||
                        dto.getGoodsSaleNakedPrice().compareTo(price.getGoodsSaleNakedPrice()) > 0) {
                    throw new ParameterException("此功能只允许降价!");
                }
                if (dto.getGoodsPactNakedPrice().compareTo(dto.getGoodsSaleNakedPrice()) > 0) {
                    throw new ParameterException("协议价不允许大于销售价!");
                }

                BigDecimal taxRate = new BigDecimal(goods.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));
                //含税协议价
                BigDecimal goodsPactPrice = dto.getGoodsPactNakedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
                //含税销售价
                BigDecimal goodsSalePrice = dto.getGoodsSaleNakedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
                price.setGoodsPactNakedPrice(dto.getGoodsPactNakedPrice());
                price.setGoodsPactPrice(goodsPactPrice);
                price.setGoodsSaleNakedPrice(dto.getGoodsSaleNakedPrice());
                price.setGoodsSalePrice(goodsSalePrice);
                price.setGoodsOriginalNakedPrice(dto.getGoodsSaleNakedPrice());
                price.setGoodsOriginalPrice(goodsSalePrice);
                price.setUpdateTime(null);
                priceList.add(price);
                this.syncTenantGoodsPrice(goods,price);
            } else {
                // 企业合同
                YphGoodsPriceStrategy priceStrategy = yphGoodsPriceStrategyService.getPriceStrategyByCompany(dto.getCompanyOrgId(), goods.getGoodsId());
                if (priceStrategy == null) {
                    throw new ParameterException("企业价格策略不存在!");
                }
                // 企业合同协议价=销售价
                if (dto.getGoodsSaleNakedPrice().compareTo(priceStrategy.getGoodsPactNakedPrice()) > 0) {
                    throw new ParameterException("此功能只允许降价!");
                }
                BigDecimal taxRate = new BigDecimal(goods.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));
                //含税协议价
                BigDecimal goodsSalePrice = dto.getGoodsSaleNakedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
                priceStrategy.setBeforePrice(priceStrategy.getAfterPrice());
                priceStrategy.setAfterPrice(goodsSalePrice);
                priceStrategy.setGoodsPactNakedPrice(dto.getGoodsSaleNakedPrice());
                priceStrategy.setGoodsPactPrice(goodsSalePrice);
                priceStrategy.setUpdateTime(null);
                priceStrategyList.add(priceStrategy);
            }
        });
        if (CollUtil.isNotEmpty(priceList)) {
            priceSrv.updateBatchById(priceList);
        }
        if (CollUtil.isNotEmpty(priceStrategyList)) {
            yphGoodsPriceStrategyService.updateBatchById(priceStrategyList);
        }
    }

    private void syncTenantGoodsPrice(ShopGoods goods,ShopGoodsPrice price){
        try {
            Long tenantId = TenantContextHolder.getTenantId().equals(yflTenantId) ? 1 : yflTenantId;
            if(null != goods.getSaleClient() && goods.getSaleClient() == 0){
                TenantUtils.execute(tenantId, () -> {
                    ShopGoods otherGoods = goodsSrv.getOne(new LambdaQueryWrapperX<ShopGoods>().eq(ShopGoods::getGoodsSku,goods.getGoodsSku())
                            .eq(ShopGoods::getSupplierCode,goods.getSupplierCode()));
                    ShopGoodsPrice finalShopGoodsPrice = this.priceSrv.selectOneByGoodsCode(otherGoods.getGoodsCode());
                    long priceId = 0L;
                    if (null == finalShopGoodsPrice) {
                        finalShopGoodsPrice = new ShopGoodsPrice();
                    }else {
                        priceId = finalShopGoodsPrice.getId();
                    }
                    BeanUtil.copyProperties(price, finalShopGoodsPrice);
                    finalShopGoodsPrice.setGoodsCode(otherGoods.getGoodsCode());
                    finalShopGoodsPrice.setId(priceId);
                    this.priceSrv.updateById(finalShopGoodsPrice);
                });
            }
        }catch (Exception e ){
            log.error("同步商品价格异常",e);
        }
    }

    public Map<Long, Object> queryBatchGoodsSettleType(List<QuerySettleTypeDto> querySettleType) {
        Map<String, List<QuerySettleTypeDto>> group = querySettleType.stream()
                .collect(Collectors.groupingBy(QuerySettleTypeDto::getSupplierCode));

        Map<Long, Object> result = new HashMap<>();
        String nowDate = LocalDate.now().toString();

        group.forEach((supplierCode, queryList) -> {
            ShopSupplier supplier = supplierSrv.selectByCode(supplierCode);
            if ("dfmall".equals(supplier.getDataSource())) {
                List<Long> ids = queryList.stream()
                        .map(QuerySettleTypeDto::getGoodsId)
                        .collect(Collectors.toList());

                if (supplier.getSupplierType() == 0 || openApiConfig.getSuppliers().contains(supplier.getSupplierCode())) {
                    queryList.forEach( q-> result.put(q.getGoodsId(), 1));
                }else {
                    List<ShopGoodsContractVo> settlementList = goodsContractService.queryVoByGoodsIds(ids, 1);
                    toGoodsSettlement(supplier, settlementList, LocalUserHolder.get().getEntityOrganizationId(), result, nowDate);
                }
            } else {
                queryList.forEach(dto -> result.put(dto.getGoodsId(), 0));
            }
        });
        return result;
    }

    private void toGoodsSettlement(ShopSupplier supplier, List<ShopGoodsContractVo> settlementList,
                                   Long companyId, Map<Long, Object> result, String nowDate) {
        if (CollUtil.isEmpty(settlementList)) {
            settlementList.forEach(s -> result.put(s.getGoodsId(), 0));
            return;
        }

        Map<Long, List<ShopGoodsContractVo>> groupGoods = settlementList.stream()
                .collect(Collectors.groupingBy(ShopGoodsContractVo::getGoodsId));

        groupGoods.forEach((goodsId, contracts) -> {

            boolean hasValidContract = contracts.stream()
                    .anyMatch(c -> isValidContract(c, companyId, nowDate));
            if (hasValidContract) {
                result.put(goodsId, 0);
                return;
            }

            boolean hasPlatformContract = contracts.stream()
                    .anyMatch(c -> c.getOrgType() == 3 && c.getAuditState() == 1);
            result.put(goodsId, hasPlatformContract ? 1 : 0);
        });
    }

    private boolean isValidContract(ShopGoodsContractVo contract, Long companyId, String nowDate) {
        return contract.getSignCompanyId().equals(companyId)
                && contract.getContractStartDate().compareTo(nowDate) <= 0
                && contract.getContractEndDate().compareTo(nowDate) >= 0
                && contract.getAuditState() == 1;
    }

    public void queryLiteFlowParams(DfmallGoodsPoolEntity dfmallGoodsPool, ShopGoodsDetailVo goodsDetailVo) {
        // 1. 实时查询商品在企业是否可售
        List<String> labels = Optional.ofNullable(goodsDetailVo.getGoodsLabel())
                .map(label -> Arrays.asList(label.split(",")))
                .orElse(Collections.emptyList());

        if(Objects.isNull(dfmallGoodsPool)){
            return;
        }

        // 2. 处理电商自营直购逻辑
        handleDirectPurchase(dfmallGoodsPool, goodsDetailVo, labels);

        if(Objects.isNull(dfmallGoodsPool.getSaleLiteFlow())){
            // 3. 处理比质比价逻辑
            handlePriceComparison(goodsDetailVo, labels);
        }

    }

    /**
     * 处理电商自营直购逻辑
     */
    private void handleDirectPurchase(DfmallGoodsPoolEntity dfmallGoodsPool,
                                      ShopGoodsDetailVo goodsDetailVo,
                                      List<String> existingLabels) {
        if (existingLabels.contains(GoodsLabelEnum.DS_ZI_YING.getCode())) {
            return;
        }

        if (Objects.isNull(dfmallGoodsPool.getSaleLiteFlow()) && (goodsDetailVo.getSupplierType() == 0 || openApiConfig.getSuppliers().contains(goodsDetailVo.getSupplierCode())) ) {
            try {
                LiteFlowsParamDto paramDto = new LiteFlowsParamDto()
                        .setParamsKey("max_price")
                        .setFlowScriptName("rule_max_goods_price")
                        .setFlowChainId(1L);

                LiteFlowsParamVo paramVo = dfmallGoodsPoolService.queryLiteFlowParams(paramDto);
                BigDecimal limitPrice = new BigDecimal(paramVo.getParamsValue());

                if (goodsDetailVo.getGoodsPactNakedPrice().compareTo(limitPrice) <= 0) {
                    updateGoodsLabels(goodsDetailVo,
                            GoodsLabelEnum.DS_ZI_YING,
                            GoodsAuthMode.PLATFORM_AUTH,
                            null);
                }
            } catch (Exception e) {
                log.error("校验电商自营直购失败", e);
            }
        }
    }

    /**
     * 处理比质比价逻辑
     */
    private void handlePriceComparison(ShopGoodsDetailVo goodsDetailVo, List<String> existingLabels) {
        if (existingLabels.contains(GoodsLabelEnum.COMPANY_BI_JIA.getCode())) {
            return;
        }

        List<String> supplierCodes = Stream.of("DSJD002", "JDIOP00")
                .filter(code -> code.equals(goodsDetailVo.getSupplierCode()))
                .collect(Collectors.toList());

        if (supplierCodes.isEmpty()) {
            supplierCodes = Collections.singletonList(goodsDetailVo.getSupplierCode());
        }

        List<ShopGoodsUpNewEntity> upNewList = goodsUpNewService.list(
                new LambdaQueryWrapperX<ShopGoodsUpNewEntity>()
                        .eq(ShopGoodsUpNewEntity::getGoodsSku, goodsDetailVo.getGoodsSku())
                        .in(ShopGoodsUpNewEntity::getSupplierCode, supplierCodes)
                        .groupBy(ShopGoodsUpNewEntity::getOrganizationId));

        Set<String> companyCodes = upNewList.stream()
                .map(ShopGoodsUpNewEntity::getCompanyCode)
                .collect(Collectors.toSet());
        if(CollectionUtil.isEmpty(companyCodes)){
            return;
        }

        Map<String, String> orgNameMap = organizationContractService.list(
                new LambdaQueryWrapperX<SystemOrganizationPurchaseContractEntity>()
                        .in(SystemOrganizationPurchaseContractEntity::getOrganizationCode, companyCodes)
        ).stream().collect(Collectors.toMap(
                SystemOrganizationPurchaseContractEntity::getOrganizationCode,
                SystemOrganizationPurchaseContractEntity::getOrganizationShortName
        ));

        upNewList.forEach(upNew -> {
            // 拼接企业名称到标签名称
            String companyNameLabel = String.format("%s%s",
                    orgNameMap.get(upNew.getCompanyCode()),
                    GoodsLabelEnum.COMPANY_BI_JIA.getName());

            updateGoodsLabels(goodsDetailVo,
                    GoodsLabelEnum.COMPANY_BI_JIA,
                    GoodsAuthMode.PURCHASE_ALLIANCE,
                    companyNameLabel);
        });
    }

    /**
     * 更新商品标签和认证信息（支持自定义标签名称）
     */
    private void updateGoodsLabels(ShopGoodsDetailVo goodsDetailVo,
                                   GoodsLabelEnum labelEnum,
                                   GoodsAuthMode authMode,
                                   String customLabelName) {
        // 使用自定义标签名称（如包含企业名称），否则使用默认名称
        String labelNameToUse = customLabelName != null ? customLabelName : labelEnum.getName();

        goodsDetailVo.setGoodsLabel(mergeAndDeduplicate(goodsDetailVo.getGoodsLabel(), labelEnum.getCode()));
        goodsDetailVo.setGoodsLabelName(mergeAndDeduplicate(goodsDetailVo.getGoodsLabelName(), labelNameToUse));
        goodsDetailVo.setAuthLabel(mergeAndDeduplicate(goodsDetailVo.getAuthLabel(), authMode.getCode()));
        goodsDetailVo.setGoodsAuthName(mergeAndDeduplicate(goodsDetailVo.getGoodsAuthName(), authMode.getName()));
    }

    /**
     * 合并并去重字符串值
     */
    private String mergeAndDeduplicate(String original, String newValue) {
        if (StringUtils.isBlank(original)) {
            return newValue;
        }

        Set<String> values = new LinkedHashSet<>(Arrays.asList(original.split(",")));
        values.add(newValue);
        return String.join(",", values);
    }

}
