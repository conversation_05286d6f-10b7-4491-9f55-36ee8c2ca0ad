package com.ly.yph.api.product.ext.jd.general.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.product.ext.common.BaseDataProcessor;
import com.ly.yph.api.product.ext.jd.dto.reponse.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.ly.yph.api.product.ext.jd.general.config.JDGeneralConfig;
import com.ly.yph.api.product.ext.jd.general.entity.BackupJdGeneralGoodsEntity;
import com.ly.yph.api.product.ext.jd.general.service.BackupJdGeneralGoodsService;
import com.ly.yph.api.product.ext.zkh.entity.QueueMsgSupplierProductProcessInfo;
import com.ly.yph.api.product.ext.zkh.service.QueueMsgSupplierProductProcessInfoService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * step4 商品详情入库
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Service("GeneralInstoreProcessor")
@Slf4j
public class GeneralInstoreProcessor extends BaseDataProcessor<Long> {
    @Getter
    protected String processName = "JDGeneral入备份库";
    @Resource
    private JDGeneralConfig config;
    @Resource
    private QueueMsgSupplierProductProcessInfoService msgSrv;
    @Resource
    private BackupJdGeneralGoodsService backupSrv;
    @Resource
    private CodeGeneral cg;
    @Resource
    private SystemTenantService tenantSrv;


    @Override
    public List<Long> supplier(final int c) {
        List<String> sup = config.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList());
        return msgSrv.listBySups(c, sup).stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
    }

    @Override
    @DistributedLock(value = "jd_general_in_process_locker", key = "#id", waitLock = false)
    public void processItem(final Long id) {
        // 获取所有租户 id
        QueueMsgSupplierProductProcessInfo msg = msgSrv.getById(id);
        if (msg == null || msg.getInStoreStatus() == 1) {
            return;
        }
        List<JDGeneralConfig.GeneralConfig> configList = config.getConfigList();
        Map<String, JDGeneralConfig.GeneralConfig> map = configList.stream().collect(Collectors.toMap(JDGeneralConfig.GeneralConfig::getCode, Function.identity()));
        JDGeneralConfig.GeneralConfig generalConfig = map.get(msg.getSupplier());
        if (generalConfig == null) return;
        //商品只能维护进配置里的租户
        TenantUtils.execute(generalConfig.getTenantIds(), () -> {
            try {
                //记录商品进度
                var backup = backupSrv.selectBySku(Long.valueOf(msg.getSkuId()), msg.getSupplier());
                BackupJdGeneralGoodsEntity backupJdGeneralGoodsEntity = doExec(msg, backup, generalConfig);
                if (backupJdGeneralGoodsEntity.getId() == null) {
                    backupSrv.save(backupJdGeneralGoodsEntity);
                } else {
                    backupSrv.getBaseMapper().updateByPrimaryKeySelective(backupJdGeneralGoodsEntity);
                }
                msgSrv.updateInStoreSuccess(id);
            } catch (Exception ex) {
                log.error("JD General to backup error, skuId:{}", msg.getSkuId(), ex);
                msgSrv.updateInStoreError(id, ex.getMessage());
            }
        });
    }

    @Override
    @DistributedLock(value = "jd_General_in_process_locker_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Long> ids) {
        val msg = msgSrv.getBaseMapper().selectSimple(ids);

        //分组
        Map<String, List<QueueMsgSupplierProductProcessInfo>> map = msg.stream().collect(Collectors.groupingBy(QueueMsgSupplierProductProcessInfo::getSupplier));

        List<JDGeneralConfig.GeneralConfig> configList = config.getConfigList();
        Map<String, JDGeneralConfig.GeneralConfig> configMap = configList.stream().collect(Collectors.toMap(JDGeneralConfig.GeneralConfig::getCode, Function.identity()));
        map.keySet().forEach(key -> {
            processOne(configMap.get(key), map.get(key), key);
        });


    }

    public void processOne(JDGeneralConfig.GeneralConfig config, List<QueueMsgSupplierProductProcessInfo> infoList, String supplierCode) {

        val failArray = new ArrayList<String>();
        val succArray = new ArrayList<String>();

        TenantUtils.execute(config.getTenantIds(), () -> {
            val entities = new ArrayList<BackupJdGeneralGoodsEntity>();
            var backups = backupSrv.getBaseMapper().selectBySkuIdIn(infoList.stream().map(QueueMsgSupplierProductProcessInfo::getSkuId).map(Long::valueOf).collect(Collectors.toList()), supplierCode);
            // supplierCode + skuId和实体的映射，生成map
            var backupMap = backups.stream().collect(Collectors.toMap(e -> e.getSupplierCode() + e.getSkuId(), item -> item));
            infoList.forEach(item -> {
                try {
                    //记录商品进度
                    entities.add(doExec(item, backupMap.get(item.getSupplier() + item.getSkuId()), config));
                    if (!failArray.contains(item.getSkuId())) {
                        succArray.add(item.getSkuId());
                    }
                } catch (Exception ex) {
                    log.error("JD General to backup error, skuId:{}", item.getSkuId(), ex);
                    failArray.add(item.getSkuId());
                    succArray.remove(item.getSkuId());
                }
            });

            var entities_list = entities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BackupJdGeneralGoodsEntity::getSkuId))), ArrayList::new));
            if (!entities_list.isEmpty())
                backupSrv.saveBatch(entities_list.stream().filter(item -> item.getId() == null).collect(Collectors.toList()));
            if (!entities_list.isEmpty())
                backupSrv.getBaseMapper().updateBatchByIdx(entities_list.stream().filter(item -> item.getId() != null).collect(Collectors.toList()));
        });
        //queue_msg_supplier_product_process_info只存租户1的
        if (!succArray.isEmpty())
            msgSrv.getBaseMapper().updateInstoreInfo(1, new ArrayList<>(succArray), supplierCode);
        if (!failArray.isEmpty())
            msgSrv.getBaseMapper().updateInstoreInfo(2, new ArrayList<>(failArray), supplierCode);
    }

    private BackupJdGeneralGoodsEntity doExec(QueueMsgSupplierProductProcessInfo item, BackupJdGeneralGoodsEntity backup, JDGeneralConfig.GeneralConfig config) {
        var res = JSON.parseObject(item.getInfo(), GetSkuPoolInfoGoodsResp.class);
        var entity = backup == null ? new BackupJdGeneralGoodsEntity() : BeanUtil.copyProperties(backup, BackupJdGeneralGoodsEntity.class);

        BeanUtil.copyProperties(res, entity);
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setSupplierCode(item.getSupplier());
        if (backup == null) {
            // 生成good code
            entity.setGoodCode(cg.getProductCode(config.getCode()));
            entity.setSkuId(Long.valueOf(item.getSkuId()));
            entity.setCreateTime(System.currentTimeMillis());
        }

        // 分类信息
        if (CollectionUtil.isNotEmpty(res.getSkuPoolExtInfoList())) {
            entity.setContractSkuPoolExt(res.getSkuPoolExtInfoList().get(0).getPoolExtValue());
        }

        // 如果品类扩展字段不为空，这拼接上扩展的品类字段
        fillField(res, entity, config);
        return entity;
    }

    private void fillField(GetSkuPoolInfoGoodsResp r, BackupJdGeneralGoodsEntity e, JDGeneralConfig.GeneralConfig config) {
        if (!StrUtil.isBlank(e.getContractSkuPoolExt())) {
            e.setCategory(e.getCategory() + ";" + e.getContractSkuPoolExt());
        }
        e.setImagePath(config.getImagePrefix() + "/" + e.getImagePath());
        e.setCategoryAttrListStr(JSONObject.toJSONString(r.getCategoryAttrList()));
        e.setParamGroupAttrlist(JSONObject.toJSONString(r.getParamGroupAttrList()));
        e.setBookExtInfo(JSONObject.toJSONString(r.getBookExtInfo()));
        e.setSoundExtInfo(JSONObject.toJSONString(r.getSoundExtInfo()));

        // 更新和添加统一设置成未同步状态，标志位全部重设
        e.setSynchronize((byte) 0);
        e.setValidateFlag((byte) 0);
        // 这里不要
        e.setPriceProcFlag((byte) 0);
        e.setShiftProcFlag((byte) 0);
        e.setStockProcFlag((byte) 0);
        e.setImageProcFlag((byte) 0);
        e.setCanSaleProcFlag((byte) 0);
        e.setCategoryProcFlag((byte) 0);
        e.setIsEnable(1);

        String introduct = e.getIntroduce();

        if (StrUtil.isBlank(introduct)) {
            if (StrUtil.isNotBlank(e.getBookExtInfo())) {
                try {
                    JSONObject bookExt = JSON.parseObject(e.getBookExtInfo());
                    String productFeatures = bookExt.getString("productFeatures");
                    introduct += productFeatures;
                } catch (Exception ex) {
                    log.error("jd_General_json_ext_info_parse_error:{}", ex.getMessage());
                }
            }
        }

        e.setIntroduce(introduct);
        if (StrUtil.isNotBlank(e.getCategory())) {
            final var ca = e.getCategory().split(";");
            e.setCategory(ca[ca.length - 1]);
        }
    }
}
