package com.ly.yph.api.settlement.supplier.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierInvoicePostageDetailExcelDto {

    @ExcelProperty("对账年度")
    private Integer checkYear;

    @ExcelProperty("对账周期")
    private Integer checkMonth;

    @ExcelProperty("下单人")
    private String applyUserName;

    @ExcelProperty("订单号")
    private String orderNumber;

    @ExcelProperty("供应商订单号")
    private String supplierOrderNumber;

    @ExcelProperty("邮费")
    private BigDecimal postage;

    @ExcelProperty("税率")
    private Integer taxRate;

}
