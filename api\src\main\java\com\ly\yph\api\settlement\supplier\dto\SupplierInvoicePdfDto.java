package com.ly.yph.api.settlement.supplier.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel
@AllArgsConstructor
public class SupplierInvoicePdfDto {

    @ApiModelProperty("发票号码")
    private String invoiceNumber;

    @ApiModelProperty("发票开票时间")
    private LocalDate supplierInvoiceDate;

    @ApiModelProperty("发票含税金额")
    private BigDecimal invoiceAmountTax;

}
