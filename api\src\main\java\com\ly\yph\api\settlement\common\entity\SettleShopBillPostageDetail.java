package com.ly.yph.api.settlement.common.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("结算中心-账单邮费表")
@TableName("settle_shop_bill_postage_detail")
@EqualsAndHashCode(callSuper = true)
public class SettleShopBillPostageDetail extends BaseEntity {

    private static final long serialVersionUID = -2897042675330837839L;

    @TableId(type = IdType.AUTO, value = "bill_detail_postage_id")
    private Long billDetailPostageId;

    @ApiModelProperty("账单主体id")
    private Long billId;

    @ApiModelProperty("账单主体编号")
    private String billSn;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("供应商订单号")
    private String supplierOrderNumber;

    @ApiModelProperty("下单人id")
    private Long applyUserId;

    @ApiModelProperty("下单人名称")
    private String applyUserName;

    @ApiModelProperty("下单人部门id")
    private Long applyDeptId;

    @ApiModelProperty("下单人部门名称")
    private String applyDeptName;

    @ApiModelProperty("邮费")
    private BigDecimal postage;

    @ApiModelProperty("税率")
    private Integer taxRate;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("下单时间")
    private Date auditTime;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名字")
    private String customerName;

    @ApiModelProperty("企业/供应商来源编码")
    private String storeCode;

    @ApiModelProperty("企业/供应商来源名称")
    private String storeName;

    @ApiModelProperty("邮费类型 0：积分，1：现金,默认积分")
    private Integer freightType;

    @ApiModelProperty("开票状态 -1:无需开票 0:已出账 2：已确认 4.待开票，5.开票驳回，6.已开票 ")
    private Integer invoiceFlag;

    @ApiModelProperty("是否暂存 0：未暂存  1已暂存")
    private Integer postageStagingFlag;
}
