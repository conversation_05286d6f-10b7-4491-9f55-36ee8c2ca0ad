package com.ly.yph.api.settlement.schedule.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.yph.api.bill.service.invoiceplan.MizdInvoicePlanService;
import com.ly.yph.api.order.mapper.ShopReturnMapper;
import com.ly.yph.api.order.service.DeliverySignMessageService;
import com.ly.yph.api.organization.service.SystemOrganizationPurchaseContractService;
import com.ly.yph.api.settlement.common.config.SettlementConfig;
import com.ly.yph.api.settlement.common.dto.bill.BillCompanyInfoDto;
import com.ly.yph.api.settlement.common.dto.bill.CustomerSourceAndAreaTypeDto;
import com.ly.yph.api.settlement.common.dto.invoice.InvoiceBillDto;
import com.ly.yph.api.settlement.common.dto.invoice.InvoiceBillInsertDto;
import com.ly.yph.api.settlement.common.dto.invoice.InvoiceDetailBillInsertDto;
import com.ly.yph.api.settlement.common.entity.SettleCompanyBillMessage;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.enums.BillCustomerTypeEnum;
import com.ly.yph.api.settlement.common.enums.PlatformReconciliationEnum;
import com.ly.yph.api.settlement.common.enums.ReconciliationStatusEnum;
import com.ly.yph.api.settlement.common.factory.InvoiceBillFactory;
import com.ly.yph.api.settlement.common.factory.InvoiceBillStrategy;
import com.ly.yph.api.settlement.common.mapper.SettleShopBillDetailMapper;
import com.ly.yph.api.settlement.common.service.SettleBillPoolService;
import com.ly.yph.api.settlement.common.service.SettleCompanyBillMessageService;
import com.ly.yph.api.settlement.common.service.SettleShopBillService;
import com.ly.yph.api.settlement.schedule.dto.PurchaseForBillDto;
import com.ly.yph.api.settlement.schedule.dto.PurchaseOrderDetailDto;
import com.ly.yph.api.settlement.schedule.dto.PurchaseReturnDetailDto;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SettleScheduleService {

    @Resource
    private SettlementConfig settlementConfig;
    @Resource
    private SettleShopBillDetailMapper settleShopBillDetailMapper;
    @Resource
    private ShopReturnMapper shopReturnMapper;
    @Resource
    private SettleScheduleService settleScheduleService;
    @Resource
    private SystemOrganizationPurchaseContractService systemOrganizationPurchaseContractService;
    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private InvoiceBillFactory invoiceBillFactory;
    @Resource
    private SettleCompanyBillMessageService settleCompanyBillMessageService;
    @Resource
    private SettleBillPoolService settleBillPoolService;
    @Resource
    private MizdInvoicePlanService mizdInvoicePlanService;




    /**
     * 配置了自动开票的企业 凌晨2：00执行自动开票定时任务
     * 1.遍历企业 目前只有东风商城租户企业的数据
     * 2.查询账单里面满足开票条件的&自动开票标记的账单明细
     * 3.查询所有订单明细
     * 4.进行匹配判断
     * 5.满足条件的进行开票
     */
    @Transactional
    public void autoInvoiceJob() {
        log.info("开始进行自动开票定时任务");

        List<String> autoInvoiceCompanyCode = settlementConfig.getAutoInvoiceCompanyCode();
        log.info("autoInvoiceCompanyCode:{}", JSONUtil.toJsonStr(autoInvoiceCompanyCode));

        if (CollectionUtil.isEmpty(autoInvoiceCompanyCode)) {
            log.info("暂无配置自动开票企业，定时任务不执行！");
            return;
        }


        autoInvoiceCompanyCode.forEach(companyCode ->
                TenantUtils.execute(1L, () -> {
                            settleScheduleService.processAutoInvoiceForCompany(companyCode);
                        }
                ));

    }


    @Transactional
    public void processAutoInvoiceForCompany(String companyCode) {
        log.info("开始处理企业：{}的自动开票", companyCode);

        // 查询企业的满足开票的采购单号
        List<String> purchaseNumberList = settleShopBillDetailMapper.getAutoInvoicePurchaseNumberForCompanyCode(companyCode);
        if (CollectionUtil.isEmpty(purchaseNumberList)) {
            log.info("企业：{}暂无需要自动开票的单据", companyCode);
            return;
        }
        log.info("企业：{}需要处理的采购单个数为：{}", companyCode, purchaseNumberList);

        /**--组装要使用的数据结构--**/
        Map<String, List<PurchaseOrderDetailDto>> shopPurchaseSubOrderDetailMap = getOrderDetailMap(purchaseNumberList);

        // 根据采购单号查询所有的账单明细
        Map<String, List<PurchaseForBillDto>> purchaseForBillMap = getPurchaseForBillMap(purchaseNumberList, companyCode);

        //查询售后
        Map<String, List<PurchaseReturnDetailDto>> purchaseReturnDetailMap = getReturnDetailMap(purchaseNumberList);

        //发票主体数据准备
        Map<String, InvoiceBillDto> stringInvoiceBillDtoMap = preLoadInvoiceBills(purchaseNumberList);


        //按采购单并行处理开票 需要注意事务 待验证
        purchaseNumberList.parallelStream().forEach(
                purchaseNumber ->
                        settleScheduleService.processSinglePurchase(
                                purchaseNumber,
                                shopPurchaseSubOrderDetailMap.get(purchaseNumber),
                                purchaseForBillMap,
                                purchaseReturnDetailMap,
                                stringInvoiceBillDtoMap.get(purchaseNumber))

        );

    }

    /**
     * 对单个采购单号的数据进行开票处理
     *
     * @param purchaseNumber          采购单号
     * @param purchaseOrderDetailDtos 采购单明细
     * @param purchaseForBillDtoMap   账单明细map
     * @param purchaseReturnDetailMap 售后明细map
     * @param invoiceBillDto          开票主体
     */
    @Transactional
    public void processSinglePurchase(String purchaseNumber,
                                      List<PurchaseOrderDetailDto> purchaseOrderDetailDtos,
                                      Map<String, List<PurchaseForBillDto>> purchaseForBillDtoMap,
                                      Map<String, List<PurchaseReturnDetailDto>> purchaseReturnDetailMap,
                                      InvoiceBillDto invoiceBillDto) {


        // 前置校验
        if (!validatePurchaseData(
                purchaseNumber,
                purchaseOrderDetailDtos,
                purchaseForBillDtoMap,
                invoiceBillDto)) {
            return;
        }


        boolean canInvoiceEntirePurchase = purchaseOrderDetailDtos.stream()
                .allMatch(detail ->
                        isDetailInvoiceable(
                                detail,
                                purchaseForBillDtoMap.getOrDefault(detail.getOrderDetailId(), Collections.emptyList()),
                                purchaseReturnDetailMap.getOrDefault(detail.getOrderDetailId(), Collections.emptyList()),
                                invoiceBillDto

                        )
                );

        if (!canInvoiceEntirePurchase) {
            log.info("采购单[{}]不满足开票条件", purchaseNumber);
            return;
        }

        Map<Integer, List<InvoiceDetailBillInsertDto>> invoiceDetailTaxRateMap = collectInvoiceableDetails(purchaseOrderDetailDtos, purchaseForBillDtoMap);

        if (invoiceDetailTaxRateMap.isEmpty()) {
            log.info("采购单[{}]无可开票明细", purchaseNumber);
            return;
        }

        settleScheduleService.submitInvoiceApplication(invoiceBillDto, invoiceDetailTaxRateMap);

    }


    private boolean validatePurchaseData(String purchaseNumber,
                                      List<PurchaseOrderDetailDto> purchaseOrderDetailDtos,
                                      Map<String, List<PurchaseForBillDto>> purchaseForBillDtoMap,
                                      InvoiceBillDto invoiceBillDto) {

        if (CollectionUtil.isEmpty(purchaseOrderDetailDtos)) {
          log.info("采购单订单明细不存在:" + purchaseNumber);
          return false;
        }

        if (invoiceBillDto == null) {
          log.info("发票主体信息未查询到:" + purchaseNumber);
          return false;
        }


        List<PurchaseOrderDetailDto> notFoundBillDetail = purchaseOrderDetailDtos.stream().filter(e -> {
            if (!purchaseForBillDtoMap.containsKey(e.getOrderDetailId())) {
                // 可能存在售后
                if (e.getOrderDetailAfterSaleState() == 50 || e.getOrderDetailState() == 0) {
                    return false;
                }
                return true;
            }else{
                return false;
            }
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(notFoundBillDetail)) {
            log.info("采购单:{}存在明细未出账:{}", purchaseNumber,
                    JSONUtil.toJsonStr(
                            notFoundBillDetail
                                    .stream()
                                    .map(PurchaseOrderDetailDto::getOrderDetailId).collect(Collectors.toList())
                    )
            );
            return false;
        }
        return true;
    }

    /**
     * 申请开票
     *
     * @param invoiceBillDto          开票主体
     * @param invoiceDetailTaxRateMap 按税率分组的开票准备明细数据
     */
    @Transactional
    public void submitInvoiceApplication(InvoiceBillDto invoiceBillDto,
                                         Map<Integer, List<InvoiceDetailBillInsertDto>> invoiceDetailTaxRateMap) {

        invoiceBillDto.setTicketType(1);
        invoiceBillDto.setCustomType(2);
        invoiceBillDto.setRemark("自动开票");
        List<BillCompanyInfoDto> billCompanyInfoDtos = systemOrganizationPurchaseContractService.getBaseMapper()
                .queryBillCompanyInfoDto(Collections.singletonList(invoiceBillDto.getCompanyCode()), 1L);
        CustomerSourceAndAreaTypeDto customerSourceAndAreaTypeDto = settleShopBillService.getCustomerSourceAndAreaTypeDto(billCompanyInfoDtos.get(0).getOrgRangMark());
        invoiceBillDto.setCompanyType(customerSourceAndAreaTypeDto.getCustomerSourceType());

        // 按税率拆分
        invoiceDetailTaxRateMap.keySet().forEach(e -> {
            InvoiceBillInsertDto invoiceBillInsertDto = new InvoiceBillInsertDto();
            invoiceBillInsertDto.setInvoiceDetailBillInsertDtos(invoiceDetailTaxRateMap.get(e));
            invoiceBillInsertDto.setInvoiceBillDto(invoiceBillDto);
            invoiceBillInsertDto.setApplyUserId(invoiceBillDto.getReconciliationUserId());
            invoiceBillInsertDto.setApplyUserName(invoiceBillDto.getReconciliationUserName());
            InvoiceBillStrategy invoiceBillStrategy = invoiceBillFactory.getInvoiceBillStrategy(invoiceBillInsertDto.getInvoiceBillDto().getCompanyType(), invoiceBillInsertDto.getBillInvoiceType());
            invoiceBillStrategy.billDetailToInvoice(invoiceBillInsertDto);
        });
    }

    /**
     * 对满足条件的账单明细按税率分组 组装开票数据
     *
     * @param purchaseOrderDetailDtos 订单明细
     * @param purchaseForBillDtoMap   账单明细
     * @return
     */
    private Map<Integer, List<InvoiceDetailBillInsertDto>> collectInvoiceableDetails(List<PurchaseOrderDetailDto> purchaseOrderDetailDtos,
                                                                                     Map<String, List<PurchaseForBillDto>> purchaseForBillDtoMap) {

        return purchaseOrderDetailDtos.stream()
                .flatMap(orderDetail ->
                        purchaseForBillDtoMap.getOrDefault(orderDetail.getOrderDetailId(), Collections.emptyList())
                                .stream()
                                .filter(bill -> bill.getInvoicableQuantity().compareTo(BigDecimal.ZERO) > 0)
                                .map(this::buildInvoiceDetail)
                )
                .collect(Collectors.groupingBy(
                        InvoiceDetailBillInsertDto::getTaxRate,
                        () -> new TreeMap<>(Integer::compareTo), // 确保税率按数值分组
                        Collectors.toList()
                ));
    }

    private InvoiceDetailBillInsertDto buildInvoiceDetail(PurchaseForBillDto purchaseForBillDto) {
        return InvoiceDetailBillInsertDto.builder()
                .billDetailId(purchaseForBillDto.getDetailId())
                .billId(purchaseForBillDto.getBillId())
                .billSn(purchaseForBillDto.getBillSn())
                .invoiceNum(purchaseForBillDto.getInvoicableQuantity())
                .taxRate(purchaseForBillDto.getTaxRate())
                .build();
    }

    /**
     * 校验采购单是否满足开票条件
     *
     * @param orderDetailDto         订单明细
     * @param purchaseForBillDtoList 订单明细对应的账单明细集合
     * @param returnDetailDtoList    订单明细对应的售后明细集合
     * @return
     */
    private boolean isDetailInvoiceable(PurchaseOrderDetailDto orderDetailDto,
                                        List<PurchaseForBillDto> purchaseForBillDtoList,
                                        List<PurchaseReturnDetailDto> returnDetailDtoList,
                                        InvoiceBillDto invoiceBillDto) {
        // 情况1：无账单数据（需检查是否全售后）
        if (purchaseForBillDtoList.isEmpty()) {
            return validateReturnFullMatch(orderDetailDto, returnDetailDtoList);
        }

        // 情况2：存在无效账单状态
        if (hasInvalidBillStatus(purchaseForBillDtoList)) {
            log.info("明细[{}]存在无效账单状态", orderDetailDto.getOrderDetailId());
            return false;
        }

        // 情况3：校验数量一致性
        if (!validateQuantityConsistency(orderDetailDto, purchaseForBillDtoList, returnDetailDtoList)) {
            return false;
        }
        //校验对账人唯一
        List<Long> reconciliationUserIdList = purchaseForBillDtoList.stream()
                .map(PurchaseForBillDto::getReconciliationUserId).distinct().collect(Collectors.toList());
        if(reconciliationUserIdList.size()>1){
            log.info("采购单的账单对账人存在多个：{}",invoiceBillDto.getPurchaseNumber());
            return false;
        }
        invoiceBillDto.setReconciliationUserId(reconciliationUserIdList.get(0));
        invoiceBillDto.setReconciliationUserName(purchaseForBillDtoList.stream()
                .map(PurchaseForBillDto::getReconciliationUserName).collect(Collectors.toList()).get(0));

        return true;

    }

    /**
     * 存在账单的情况下 且对账状态正确的
     * 比较 出账数量+售后数量 = 下单数量？
     * == 满足开票条件
     * 否则是部分出账 不满足开票跳进
     *
     * @param orderDetailDto         订单明细
     * @param purchaseForBillDtoList 账单明细
     * @param returnDetailDtoList    售后明细
     * @return
     */
    private boolean validateQuantityConsistency(PurchaseOrderDetailDto orderDetailDto,
                                                List<PurchaseForBillDto> purchaseForBillDtoList,
                                                List<PurchaseReturnDetailDto> returnDetailDtoList) {
        BigDecimal totalChecked = purchaseForBillDtoList.stream()
                .map(PurchaseForBillDto::getCheckedNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalReturn = returnDetailDtoList.stream()
                .map(PurchaseReturnDetailDto::getReturnNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal expectedTotal = orderDetailDto.getApplyNum();
        BigDecimal actualTotal = totalChecked.add(totalReturn);

        if (actualTotal.compareTo(expectedTotal) != 0) {
            log.info("数量不一致: 明细[{}] 预期={}, 实际=出账{} + 退货{}",
                    orderDetailDto.getOrderDetailId(), expectedTotal, totalChecked, totalReturn);
            return false;
        }

        return purchaseForBillDtoList.stream()
                .map(PurchaseForBillDto::getInvoicableQuantity)
                .anyMatch(qty -> qty.compareTo(BigDecimal.ZERO) > 0);

    }

    /**
     * 有账单明细的情况下
     * 存在已出账/待确认的明细 不满足开票条件
     *
     * @param purchaseForBillDtolist 账单明细
     * @return
     */
    private boolean hasInvalidBillStatus(List<PurchaseForBillDto> purchaseForBillDtolist) {
        return purchaseForBillDtolist.stream()
                .anyMatch(bill ->
                        bill.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode()) ||
                                bill.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode())
                );
    }

    /**
     * 没有账单的情况下
     * 全部售后 此订单明细算满足开票条件
     *
     * @param orderDetailDto      订单明细
     * @param returnDetailDtoList 售后明细
     * @return
     */
    private boolean validateReturnFullMatch(PurchaseOrderDetailDto orderDetailDto,
                                            List<PurchaseReturnDetailDto> returnDetailDtoList) {
        if (orderDetailDto.getOrderDetailState() == 0) {
            log.info("订单明细：{}是已取消，且无账单明细,正常过滤",orderDetailDto.getOrderDetailId());
            return true;
        }
        BigDecimal totalReturn = returnDetailDtoList.stream()
                .map(PurchaseReturnDetailDto::getReturnNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("无账单数据,进行售后数据进行统计,订单号：{},订单明细：{},退货数量：{}",
                orderDetailDto.getPurchaseNumber(),orderDetailDto.getOrderDetailId(),totalReturn);
        return orderDetailDto.getApplyNum().compareTo(totalReturn) == 0;

    }

    private Map<String, List<PurchaseOrderDetailDto>> getOrderDetailMap(List<String> purchaseNumberList) {
        return settleShopBillDetailMapper.getOrderDetailForPurchaseNumberList(purchaseNumberList)
                .stream()
                .collect(Collectors.groupingBy(PurchaseOrderDetailDto::getPurchaseNumber));
    }

    private Map<String, InvoiceBillDto> preLoadInvoiceBills(List<String> purchaseNumbers) {
        return settleShopBillDetailMapper.getPurchaseInvoiceByPurchaseNumber(purchaseNumbers)
                .stream()
                .collect(Collectors.toMap(InvoiceBillDto::getPurchaseNumber, Function.identity(), (key1, key2) -> key1));
    }


    private Map<String, List<PurchaseReturnDetailDto>> getReturnDetailMap(List<String> purchaseNumberList) {
        return shopReturnMapper.getReturnDetailForPurchaseNumberList(purchaseNumberList)
                .stream()
                .collect(Collectors.groupingBy(PurchaseReturnDetailDto::getOrderDetailId));

    }

    private Map<String, List<PurchaseForBillDto>> getPurchaseForBillMap(List<String> purchaseNumberList, String companyCode) {
        return settleShopBillDetailMapper.getAllDetailForPurchaseNumberAndCompanyCode(purchaseNumberList, companyCode)
                .stream()
                .collect(Collectors.groupingBy(PurchaseForBillDto::getOrderDetailId));

    }

    public List<String> getAutoInvoiceCompany() {
        return settlementConfig.getAutoInvoiceCompanyCode();
    }

    @Transactional
    public void autoCompanyBillOut(Integer limit) {
        log.info("企业验收出账定时任务启动");
        /**
         *  客户未验收  供应商不允许出帐
         *  1.客户验收 供应商也妥投/签收单 ->一起出账
         *  2.客户验收 供应商没有妥投/签收单 -> 客户先出
         *  3.客户未验收 供应商没有妥投/签收单 -> 都不出
         *  4.客户未验收 供应商妥投/签收单 -> 福利才出
         */
        //处理企业的 友福利不用处理

        List<SettleCompanyBillMessage> settleCompanyBillMessages = getQueryMessage(Boolean.TRUE, limit);

        if (CollectionUtil.isEmpty(settleCompanyBillMessages)) {
            return;
        }

        //开始批量处理
        for (SettleCompanyBillMessage message : settleCompanyBillMessages) {
            try {
                settleBillPoolService.companyBillMessageProcess(message);
            } catch (Exception e) {
                log.error("消息:[{}]处理失败,错误信息：{}", message.getId(), e);
            }
        }
    }


    @Transactional
    public void autoSupplierBillOut(Integer limit) {
        log.info("供应商出账定时任务启动");
        //福利的 客户侧 默认是处理成功的

        List<SettleCompanyBillMessage> settleCompanyBillMessages = getQueryMessage(Boolean.FALSE, limit);

        if (CollectionUtil.isEmpty(settleCompanyBillMessages)) {
            return;
        }

        for (SettleCompanyBillMessage message : settleCompanyBillMessages) {
            try {
                settleBillPoolService.supplierBillMessageProcess(message);
            } catch (Exception e) {
                log.error("消息[{}]处理失败，错误信息：{}",message.getId(),e);
            }
        }
    }

    private List<SettleCompanyBillMessage> getQueryMessage(Boolean isCompany, Integer limit) {
        QueryWrapper<SettleCompanyBillMessage> queryWrapper = new QueryWrapper<>();
        if (isCompany) {
            queryWrapper.lambda().eq(SettleCompanyBillMessage::getCompanyCheckState, 1)
                    .eq(SettleCompanyBillMessage::getCompanyProcessState, 0)
                    .eq(SettleCompanyBillMessage::getTenantId, 1)
                    .ne(SettleCompanyBillMessage::getDeliveryDetailId, 0)
                    .orderByAsc(BaseEntity::getCreateTime)
                    .last("limit " + limit)
                    .select(SettleCompanyBillMessage::getId,
                            SettleCompanyBillMessage::getDeliveryDetailId);
        } else {
            queryWrapper.lambda().eq(SettleCompanyBillMessage::getCompanyCheckState, 1)
                    .eq(SettleCompanyBillMessage::getSupplierCheckState, 1)
                    .eq(SettleCompanyBillMessage::getSupplierProcessState, 0)
                    .ne(SettleCompanyBillMessage::getDeliveryDetailId, 0)
                    .orderByAsc(BaseEntity::getCreateTime)
                    .last("limit " + limit)
                    .select(SettleCompanyBillMessage::getId,
                            SettleCompanyBillMessage::getDeliveryDetailId);
        }
        return settleCompanyBillMessageService.list(queryWrapper);
    }

    public String creatSupplierPay() {

        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getCustomerType, BillCustomerTypeEnum.SUPPLIER.getCode())
                .eq(SettleShopBill::getIsPlatformReconciliation, PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode())
                .eq(SettleShopBill::getPayFlag, 1)
                .orderByAsc(BaseEntity::getCreateTime)
                .last("limit 1");

        SettleShopBill settleShopBill = settleShopBillService.getOne(queryWrapper);
        if (settleShopBill == null) {
            return "暂无支付计划生成！";
        }

        try {
            mizdInvoicePlanService.createInvoicePlanByBill(settleShopBill.getBillId());
            UpdateWrapper<SettleShopBill> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SettleShopBill::getBillId, settleShopBill.getBillId())
                    .set(SettleShopBill::getPayFlag, 2);
            settleShopBillService.update(updateWrapper);
            return "账单"+settleShopBill.getBillSn()+"生成支付计划完成！";
        } catch (Exception e) {
            log.error("自动生成支付计划异常",e);
            UpdateWrapper<SettleShopBill> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SettleShopBill::getBillId, settleShopBill.getBillId())
                    .set(SettleShopBill::getPayFlag, 3);
            settleShopBillService.update(updateWrapper);
            return "账单"+settleShopBill.getBillSn()+"生成支付计划失败！";
        }
    }
}
