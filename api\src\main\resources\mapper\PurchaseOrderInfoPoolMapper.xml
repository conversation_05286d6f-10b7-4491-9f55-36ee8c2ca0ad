<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.orderlifecycle.mapper.PurchaseOrderInfoPoolMapper">
    <insert id="insetBatchFixDto">
        insert into bill_detail_fix(purchase_number,goods_sku,check_num,order_sales_channel,company_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.purchaseNumber},#{item.goodsSku},#{item.checkNum},#{orderSalesChannel},#{companyCode})
        </foreach>
    </insert>
    <insert id="saveSupplierSettleDataList">
        insert into supplier_settle_data(order_number,goods_sku,confirm_num,checked_num)
        values
        <foreach collection="sublist" item="item" separator=",">
            (#{item.orderNumber},#{item.goodsSku},#{item.confirmNum},#{item.checkedNum})
        </foreach>
    </insert>
    <update id="updateBillDetailFix">
        update bill_detail_fix set process_state =1 where id in
        <foreach collection="fixIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="getPurchaseOrderInfoDto" resultType="com.ly.yph.api.orderlifecycle.dto.PurchaseOrderInfoDto">
        select spo.purchase_number,
               spo.purchase_state,
               spo.approval_opinion,
               spo.purchase_goods_price as purchaseGoodsPriceTax,
               spo.purchase_goods_price_naked,
               spo.purchase_freight_price,
               spo.purchase_total_price as purchaseTotalPriceTax,
               spo.other_relation_number,
               spo.activity_id,
               spo.apply_user_name,
               spo.apply_emp_code,
               spo.apply_user_phone,
               spo.apply_dept_name,
               spo.budget_code,
               spo.budget_apply_code,
               spo.sap_order_type,
               spo.company_code,
               spo.company_name,
               spo.tenant_id,
               sa.activity_name,
               soa.address_name         as receiverName,
               soa.mob_phone            as receiverPhone,
               soa.province             as receiverProvince,
               soa.city                 as receiverCity,
               soa.district             as receiverDistrict,
               soa.address              as receiverAddress,
               soi.invoice_subject,
               spo.create_time          as applyTime,
               soa.address_type         as addressType,
               spo.order_sales_channel
        from shop_purchase_order spo
                 left join system_activity sa on spo.activity_id = sa.id
                 left join shop_order_address soa on soa.purchase_id = spo.purchase_id
                 left join shop_order_invoice soi on soi.purchase_id = spo.purchase_id
        where spo.purchase_number = #{purchaseNumber}
          and spo.is_enable =1
          and spo.order_sales_channel !=4
    </select>


    <select id="getOrderDetailPoolPreOrderList"
            resultType="com.ly.yph.api.orderlifecycle.dto.OrderDetailPoolPreOrderDto">
        select spso.supplier_code,
               spso.supplier_name,
               spso.purchase_number,
               spso.is_pre_pay,
               spso.order_number,
               spso.supplier_order_number,
               spsod.goods_sku,
               spsod.goods_code,
               spsod.order_detail_id,
               spsod.goods_image,
               spsod.goods_desc,
               spsod.goods_unit_price_naked,
               spsod.goods_unit_price_tax,
               spsod.goods_total_price_tax,
               spsod.goods_total_price_naked,
               spsod.supplier_unit_price_naked,
               spsod.supplier_unit_price_tax,
               spsod.supplier_total_price_tax,
               spsod.supplier_total_price_naked,
               spsod.goods_pay_integral,
               spsod.goods_pay_money,
               spsod.tax_rate,
               spsod.tax_code,
               spsod.confirm_num,
               spsod.order_detail_state,
               spsod.third_level_gc_name as threeClassName,
               spsod.row_serial_number,
               sb.brand_name
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spso.order_id = spsod.order_id
                 left join shop_brand sb on sb.brand_id = spsod.brand_id
        where spso.purchase_number = #{purchaseNumber}
              and spso.is_platform_reconciliation =1
              and spso.is_enable =1
    </select>

    <select id="getPurchaseOrderInfoDetailVo"
            resultType="com.ly.yph.api.orderlifecycle.vo.PurchaseOrderDetailInfoVo">
        select
        poip.order_sales_channel,
        odp.order_placement_scenario,
        poip.purchase_number,
        poip.purchase_state,
        poip.other_relation_number,
        poip.apply_user_name,
        poip.apply_emp_code,
        poip.receiver_name,
        poip.is_out_settle,
        poip.company_code,
        poip.company_name,
        poip.apply_time,
        odp.supplier_code,
        odp.supplier_name,
        odp.order_number,
        odp.supplier_order_number,
        odp.tax_rate,
        odp.goods_code,
        odp.goods_sku,
        odp.goods_image,
        odp.goods_desc,
        odp.goods_unit_price_naked,
        odp.goods_unit_price_tax,
        odp.goods_total_price_naked,
        odp.goods_total_price_tax,
        odp.three_class_name,
        odp.goods_pay_integral,
        odp.goods_pay_money,
        odp.tax_code,
        odp.order_detail_state,
        odp.confirm_num,
        odp.delivery_status,
        odp.delivery_num,
        odp.proper_delivery_num,
        odp.after_sale_status,
        odp.after_sale_num,
        odp.customer_receipt_status,
        odp.customer_receipt_num,
        odp.customer_acceptance_status,
        odp.customer_acceptance_num,
        odp.customer_checkout_status,
        odp.customer_checkout_num,
        odp.customer_reconciliation_status,
        odp.customer_reconciliation_num,
        odp.customer_invoice_status,
        odp.customer_invoiced_num,
        odp.customer_invoicing_num,
        odp.customer_invoiced_money,
        odp.customer_settlement_num,
        odp.customer_settlement_status,
        odp.customer_settlement_money,
        odp.supplier_invoice_status,
        odp.supplier_invoiced_num,
        odp.supplier_invoiced_money,
        odp.supplier_settlement_status,
        odp.supplier_settlement_num,
        odp.supplier_settlement_money
        from order_detail_pool odp
        left join purchase_order_info_pool poip on poip.id = odp.purchase_info_id
        where poip.is_enable = 1
        <if test="query.orderSalesChannel !=null">
            and poip.order_sales_channel =#{query.orderSalesChannel}
        </if>
        <if test="query.orderPlacementScenario !=null">
            and odp.order_placement_scenario =#{query.orderPlacementScenario}
        </if>
        <if test="query.purchaseNumber !=null and query.purchaseNumber !=''">
            and poip.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.orderNumber !=null and query.orderNumber !=''">
            and odp.order_number =#{query.orderNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber !=''">
            and odp.supplier_order_number =#{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode !=null and query.goodsCode !=''">
            and odp.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku !=null and query.goodsSku !=''">
            and odp.goods_sku =#{query.goodsSku}
        </if>
        <if test="query.applyUserName !=null and query.applyUserName !=''">
            and poip.apply_user_name =#{query.applyUserName}
        </if>
        <if test="query.companyCode !=null and query.companyCode !=''">
            and poip.company_code =#{query.companyCode}
        </if>
        <if test="query.supplierCode !=null and query.supplierCode !=''">
            and odp.supplier_code =#{query.supplierCode}
        </if>
        <if test="query.applyTimeStart !=null and query.applyTimeStart !=''">
            and poip.apply_time &gt;= #{query.applyTimeStart}
        </if>
        <if test="query.applyTimeEnd !=null and query.applyTimeEnd !=''">
            and poip.apply_time &lt;=#{query.applyTimeEnd}
        </if>
        <if test="query.deliveryStatus !=null">
            and odp.delivery_status =#{query.deliveryStatus}
        </if>
        <if test="query.afterSaleStatus !=null">
            and odp.after_sale_status =#{query.afterSaleStatus}
        </if>
        <if test="query.customerReceiptStatus !=null">
            and odp.customer_receipt_status =#{query.customerReceiptStatus}
        </if>
        <if test="query.customerAcceptanceStatus !=null">
            and odp.customer_acceptance_status =#{query.customerAcceptanceStatus}
        </if>
        <if test="query.customerCheckoutStatus !=null">
            and odp.customer_checkout_status =#{query.customerCheckoutStatus}
        </if>
        <if test="query.customerReconciliationStatus !=null">
            and odp.customer_reconciliation_status =#{query.customerReconciliationStatus}
        </if>
        <if test="query.customerInvoiceStatus !=null">
            and odp.customer_invoice_status =#{query.customerInvoiceStatus}
        </if>
        <if test="query.customerSettlementStatus !=null">
            and odp.customer_settlement_status =#{query.customerSettlementStatus}
        </if>
        <if test="query.supplierInvoiceStatus !=null">
            and odp.supplier_invoice_status =#{query.supplierInvoiceStatus}
        </if>
        <if test="query.supplierSettlementStatus !=null">
            and odp.supplier_settlement_status =#{query.supplierSettlementStatus}
        </if>
        order by poip.apply_time desc
    </select>

    <select id="getFixOrderPoolCompanyList" resultType="java.lang.String">
        select a.company_code
        from shop_purchase_order a
                 left join shop_purchase_sub_order b on a.purchase_number = b.purchase_number
        where a.is_enable = 1
          and a.tenant_id = #{tenantId}
          and b.supplier_data_source = 'dfmall'
          and b.is_platform_reconciliation =1
          and a.company_code not in ('honda', 'honda_sale', '东风本田汽车有限公司', '东风本田汽车销售有限公司')
          and a.create_time &lt;= #{endTime}
        group by a.company_code
    </select>

    <select id="getFixPurchaseOrderInfo"
            resultType="com.ly.yph.api.orderlifecycle.dto.PurchaseOrderInfoDto">
        select spo.purchase_number,
               spo.purchase_state,
               spo.approval_opinion,
               spo.purchase_goods_price as purchaseGoodsPriceTax,
               spo.purchase_goods_price_naked,
               spo.purchase_freight_price,
               spo.purchase_total_price as purchaseTotalPriceTax,
               spo.other_relation_number,
               spo.activity_id,
               spo.apply_user_name,
               spo.apply_emp_code,
               spo.apply_user_phone,
               spo.apply_dept_name,
               spo.budget_code,
               spo.budget_apply_code,
               spo.sap_order_type,
               spo.company_code,
               spo.company_name,
               spo.tenant_id,
               sa.activity_name,
               soa.address_name         as receiverName,
               soa.mob_phone            as receiverPhone,
               soa.province             as receiverProvince,
               soa.city                 as receiverCity,
               soa.district             as receiverDistrict,
               soa.address              as receiverAddress,
               soi.invoice_subject,
               spo.create_time          as applyTime
        from shop_purchase_order spo
                 left join system_activity sa on spo.activity_id = sa.id
                 left join shop_order_address soa on soa.purchase_id = spo.purchase_id
                 left join shop_order_invoice soi on soi.purchase_id = spo.purchase_id
        where spo.is_enable = 1
          and spo.tenant_id = #{tenantId}
          and spo.company_code = #{companyCode}
          and spo.create_time &lt;= #{endTime}
    </select>

    <select id="getFixOrderDetailPoolInfo" resultType="com.ly.yph.api.orderlifecycle.entity.OrderDetailPool">
        select b.order_detail_id,
               b.row_serial_number,
               b.goods_code,
               b.goods_sku,
               b.goods_image,
               b.goods_desc,
               b.goods_unit_price_naked,
               b.goods_unit_price_tax,
               b.goods_total_price_naked,
               b.goods_total_price_tax,
               b.supplier_unit_price_tax,
               b.supplier_unit_price_naked,
               b.supplier_total_price_tax,
               b.supplier_total_price_naked,
               b.third_level_gc_name as three_class_name,
               b.goods_pay_integral,
               b.goods_pay_money,
               b.tax_code,
               b.tax_rate,
               b.order_detail_state,
               b.confirm_num,
               a.supplier_code,
               a.supplier_name,
               a.purchase_number,
               a.is_pre_pay,
               a.order_number,
               a.supplier_order_number
        from shop_purchase_sub_order a
                 left join shop_purchase_sub_order_detail b on a.order_id = b.order_id
                 left join shop_purchase_order c on a.purchase_number = c.purchase_number
        where c.is_enable = 1
          and a.supplier_data_source = 'dfmall'
          and a.is_platform_reconciliation =1
          and c.company_code = #{companyCode}
          and a.tenant_id = #{tenantId}
          and c.create_time &lt;= #{endTime}
    </select>

    <select id="getDeliveryCompanyCode" resultType="java.lang.String">
        select company_code
        from purchase_order_info_pool
        where is_enable = 1
          and order_sales_channel = #{orderSalesChannel}
          and is_fix = 1
        group by company_code
    </select>

    <select id="getDeliveryDetailForFixPurchaseOrderInfo"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.DeliveryDetailFix">
        select d.order_detail_id,
               b.delivery_num + b.delivery_num_decimal   as deliveryNum,
               b.receiving_num + b.receiving_num_decimal as receivingNum,
               a.sup_delivery_state
        from shop_delivery a
                 left join shop_delivery_detail b on a.id = b.delivery_id
                 left join shop_purchase_sub_order c on c.order_number = a.order_number
                 left join shop_purchase_sub_order_detail d
                           on d.order_number = c.order_number and d.goods_code = b.goods_code
                 left join shop_purchase_order e on e.purchase_number = c.purchase_number
        where a.is_enable = 1
          and b.is_enable = 1
          and e.company_code = #{companyCode}
          and a.tenant_id = #{tenantId}
          and c.supplier_data_source = 'dfmall'
          and d.order_detail_id is not null
    </select>

    <select id="getOrderDetailForCompanyAndOrderSaleChannel"
            resultType="com.ly.yph.api.orderlifecycle.entity.OrderDetailPool">
        select b.id,
        b.order_detail_id,
        b.order_number,
        b.confirm_num,
        b.after_sale_num
        from purchase_order_info_pool a
        left join order_detail_pool b on a.id = b.purchase_info_id
        where a.is_enable = 1
        and a.is_fix = 1
        and a.company_code = #{companyCode}
        and a.order_sales_channel = #{orderSalesChannel}
        and b.id is not null
        <if test="orderDetailList != null and orderDetailList.size()>0">
            and b.order_detail_id in
            <foreach collection="orderDetailList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAfterSaleForFixPurchaseOrderInfo"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.AfterSaleFix">
        select c.order_detail_id,
               sum(b.return_num) as returnNum
        From shop_return a
                 left join shop_return_detail b on a.id = b.return_id
                 left join shop_purchase_sub_order_detail c on c.order_id = a.order_id and c.goods_code = b.goods_code
        where a.is_enable = 1
          and a.tenant_id = #{tenantId}
          and a.return_state in (1, 3)
          and c.order_detail_id is not null
        group by c.order_detail_id
    </select>

    <select id="getallSapDeliveryData" resultType="com.ly.yph.api.order.vo.check.OutCheckSapDetailVo">
        select a.order_sn as purchaseNumber,
               a.item_no,
               a.mblnr,
               a.menge,
               a.index_state,
               b.company_code
        From sap_delivery_detail a
                 left join sap_delivery b on a.mblnr = b.mblnr
    </select>
    <select id="getallDfpvSap" resultType="com.ly.yph.api.order.vo.check.OutCheckSapDetailVo">
        select purchase_number,
               item_no,
               receipt_sn as mblnr,
               rceipt_num as menge,
               company_code
        from dfpv_sap_delivery
    </select>
    <select id="getallDfcvSap" resultType="com.ly.yph.api.order.vo.check.OutCheckSapDetailVo">
        select purchase_number,
               item_no,
               delivery_num as menge,
               company_code
        from import_ext_bill
        where company_code = 'dfcv' and goods_check_status =1
    </select>
    <select id="getallDhec" resultType="com.ly.yph.api.order.vo.check.OutCheckSapDetailVo">
        select a.purchase_number,
               c.row_serial_number as itemNo,
               a.check_num         as menge,
               a.company_code
        from external_check a
                 left join shop_purchase_sub_order b on b.purchase_number = a.purchase_number
                 left join shop_purchase_sub_order_detail c
                           on c.order_number = b.order_number and a.goods_code = c.goods_code
        where a.is_enable = 1
          and c.row_serial_number is not null
          and a.company_code = 'DHEC'
    </select>

    <select id="getDfmallBillDetailFixList"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailFixDto">
        select b.detail_id,
               c.order_detail_id,
               c.id            as settle_bill_pool_id,
               b.checked_num as checkNum,
               a.customer_code as companyCode,
               b.reconciliation_status
        from settle_shop_bill_detail b
                 left join settle_shop_bill a on a.bill_id = b.bill_id
                 left join settle_bill_pool c on c.id = b.settle_bill_pool_id
        where a.customer_type = 0
          and a.is_platform_reconciliation = 1
          and customer_source_type in (1, 3)
          and a.customer_code = #{companyCode}
          and b.is_enable = 1
          and b.pool_type = 1
          and c.sap_order_type in (0, 4)
    </select>

    <select id="getDfmallBillDetailFixCompanyList" resultType="java.lang.String">
        select a.customer_code
        from settle_shop_bill_detail b
                 left join settle_shop_bill a on a.bill_id = b.bill_id
                 left join settle_bill_pool c on c.id = b.settle_bill_pool_id
        where a.customer_type = 0
          and a.is_platform_reconciliation = 1
          and a.customer_source_type in (1, 3)
          and a.customer_code not in ('HONDA_SALE', 'HONDA')
          and b.is_enable = 1
          and b.pool_type = 1
          and c.sap_order_type in (0, 4)
        group by a.customer_code
    </select>

    <select id="getYflBillDetailFixCompanyList" resultType="java.lang.String">
        select a.customer_code
        from settle_shop_bill_detail b
                 left join settle_shop_bill a on a.bill_id = b.bill_id
        where a.customer_type = 0
          and a.is_platform_reconciliation = 1
          and a.customer_source_type = 2
          and b.is_enable = 1
          and b.pool_type = 2
        group by a.customer_code
    </select>

    <select id="getYflBillDetailFixList" resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailFixDto">
        select b.detail_id,
               c.order_detail_id,
               c.bill_pool_yfl_id as settleBillPoolId,
               b.checked_num as checkNum,
               a.customer_code    as companyCode,
               b.reconciliation_status
        from settle_shop_bill_detail b
                 left join settle_shop_bill a on a.bill_id = b.bill_id
                 left join settle_bill_pool_yfl_customer c on c.bill_pool_yfl_id = b.settle_bill_pool_id
        where a.customer_type = 0
          and a.is_platform_reconciliation = 1
          and a.customer_source_type = 2
          and b.is_enable = 1
          and b.pool_type = 2
          and a.customer_code = #{companyCode}
    </select>

    <select id="getDfmallInvoiceDetailForCompany"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailInvoiceFixDto">
        select d.order_detail_id,
               b.invoice_num,
               a.state                 as invoiceState,
               b.goods_total_price_tax as invoicedMoney,
               a.repayment_status
        from invoice_bill a
                 left join invoice_detail_bill b on a.id = b.invoice_id
                 left join settle_shop_bill_detail c on b.bill_detail_id = c.detail_id
                 left join settle_bill_pool d on d.id = c.settle_bill_pool_id
        where a.state in (4, 6, 7)
          and a.bill_invoice_type = 0
          and a.company_code = #{companyCode}
          and a.company_type in (1, 3);
    </select>
    <select id="getYflInvoiceDetailForCompany"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailInvoiceFixDto">
        select d.order_detail_id,
               b.invoice_num,
               a.state                 as invoiceState,
               b.goods_total_price_tax as invoicedMoney,
               a.repayment_status
        from invoice_bill a
                 left join invoice_detail_bill b on a.id = b.invoice_id
                 left join settle_shop_bill_detail c on b.bill_detail_id = c.detail_id
                 left join settle_bill_pool_yfl_customer d on d.bill_pool_yfl_id = c.settle_bill_pool_id
        where a.state in (4, 6, 7)
          and a.bill_invoice_type = 0
          and a.company_code = #{companyCode}
          and a.company_type = 2
    </select>

    <select id="getDfmallSapBillDetailFixList"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailFixDto">
        select b.detail_id,
               c.order_detail_id,
               c.id            as settle_bill_pool_id,
               b.checked_num as checkNum,
               a.customer_code as companyCode,
               b.reconciliation_status
        from settle_shop_bill_detail b
                 left join settle_shop_bill a on a.bill_id = b.bill_id
                 left join settle_bill_pool c on c.id = b.settle_bill_pool_id
        where a.customer_type = 0
          and a.is_platform_reconciliation = 1
          and customer_source_type in (1, 3)
          and a.customer_code not in ('honda', 'honda_sale')
          and b.is_enable = 1
          and b.pool_type = 1
          and c.sap_order_type not in (0, 4);
    </select>

    <select id="getOrderDetailStateByIds" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail">
        select order_detail_id,
        order_detail_state
        from shop_purchase_sub_order_detail
        where is_enable =1 and order_detail_id in
        <foreach collection="orderDetailIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getFixDeliveryTs" resultType="com.ly.yph.api.orderlifecycle.dto.fix.DeliveryDetailFix">
        select d.order_detail_id,
               b.delivery_num + b.delivery_num_decimal   as deliveryNum,
               a.sup_delivery_state,
               d.order_detail_state
        from shop_delivery a
                 left join shop_delivery_detail b on a.id = b.delivery_id
                 left join shop_purchase_sub_order c on c.order_number = a.order_number
                 left join shop_purchase_sub_order_detail d
                           on d.order_number = c.order_number and d.goods_code = b.goods_code
                 left join shop_purchase_order e on e.purchase_number = c.purchase_number
        where a.is_enable = 1
          and b.is_enable = 1
          and a.id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getFixGoodsCodeDelivery" resultType="com.ly.yph.api.orderlifecycle.dto.fix.DeliveryDetailFix">
        select d.order_detail_id,
               a.order_number,
               b.goods_sku,
               b.delivery_num + b.delivery_num_decimal   as deliveryNum,
               b.receiving_num + b.receiving_num_decimal as receivingNum,
               a.sup_delivery_state,
               c.tenant_id
        from shop_delivery a
                 left join shop_delivery_detail b on a.id = b.delivery_id
                 left join shop_purchase_sub_order c on c.order_number = a.order_number
                 left join shop_purchase_sub_order_detail d
                           on d.order_number = c.order_number and d.goods_code = b.goods_code
                 left join shop_purchase_order e on e.purchase_number = c.purchase_number
        where a.is_enable = 1
          and b.is_enable = 1
          and c.supplier_data_source = 'dfmall'
          and d.order_detail_id is null;
    </select>
    <select id="getBillDetailFixListForPurchaseNumber"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailFix">
        select id,
        purchase_number,
        goods_sku,
        check_num
        from bill_detail_fix
        where process_state = 0
        and purchase_number in
        <foreach collection="purchaseNumberList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getYflOfflineBillForActivityCode"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.YfllineBillDto">
        select id,
               purchase_number,
               order_number,
               goods_sku,
               check_num
        from yfl_offline_bill
        where process_state = 0
          and activity_code = #{activityCode}
    </select>
    <select id="getLeaveGoods" resultType="com.ly.yph.api.goods.entity.ShopGoods">
        select
        goods_sku,
        third_class_name
        from shop_goods
        where is_enable =1 and tenant_id =#{tenantId} and goods_sku in
        <foreach collection="goodsSkuList" index="index" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
        group by goods_sku
    </select>

    <select id="getInvoicedNoSettle" resultType="com.ly.yph.api.orderlifecycle.dto.fix.HistorySettleBillDto">
        select id, order_number, goods_sku, status, process_status, settle_num, settle_money
        from history_settle_bill
        where process_status = 0
          and status = #{status}
    </select>

    <update id="updateYfloffline">
        update yfl_offline_bill set process_state =#{state}
        where id in
        <foreach collection="yflOfflineIdList" item="item" index="index" separator="," close=")" open="(">
            #{item}
        </foreach>
    </update>
    <update id="updateHistorySettleBillList">
        update history_settle_bill set process_status =#{status} where id in
        <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateSupplierSettleDataStatus">
        update supplier_settle_data set process_status =#{status} where id in
        <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="fixSupplierBillDetail">
        <foreach collection="billDetailList" item="item" separator=";">
            update settle_shop_bill_detail
            set reconciliation_status=6,
            invoiced_quantity=#{item.invoiceNum},
            invoicable_quantity =0
            where detail_id =#{item.billDetailId}
        </foreach>
    </update>

    <update id="fixSupplierLifeCycle">
        <foreach collection="lifeCycleList" item="item" separator=";">
            update settle_bill_life_cycle
            set supplier_invoicing_status =6,
            supplier_invoicing_num =#{item.invoiceNum},
            supplier_invoice_money=#{item.invoiceMoney},
            supplier_invoicing_time ='2025-07-31 00:00:00'
            where id =#{item.lifeCycleId}
        </foreach>
    </update>

    <select id="getYflNewMallSettleBillList"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.YflNemMallSettleBillDto">
        select a.detail_id,
               b.order_detail_id,
               a.confirm_num,
               a.checked_num,
               a.reconciliation_status,
               b.goods_pay_integral
        from settle_shop_bill_detail a
                 left join settle_bill_pool_yfl_customer b on a.settle_bill_pool_id = b.bill_pool_yfl_id
        where a.is_enable = 1
          and b.is_enable = 1
          and a.pool_type = 2
          and a.bill_id =#{billId}
    </select>
    <select id="getFixSupplierSettleDataList"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.SupplierSettleDataDto">
        select id,
               order_number,
               goods_sku,
               checked_num
        from supplier_settle_data
        where process_status = 0 limit 20000
    </select>

    <select id="getFixPurchaseTypeList" resultType="com.ly.yph.api.orderlifecycle.dto.fix.PurchaseTypeFixDto">
        select a.id,
               a.goods_sku,
               a.confirm_num,
               b.company_code,
               b.company_name,
               a.goods_desc,
               d.address_type
        From order_detail_pool a
                 left join purchase_order_info_pool b on a.purchase_info_id = b.id
                 left join shop_purchase_order c on b.purchase_number = c.purchase_number
                 left join shop_order_address d on c.purchase_id = d.purchase_id
        where a.is_enable = 1
          and b.order_sales_channel = #{orderSalesChannel}
          and a.purchase_channel = 0
          and a.order_placement_scenario = 0
        order by b.company_code, b.purchase_number limit 10000;
    </select>

    <select id="getHondaHistorySettle" resultType="com.ly.yph.api.orderlifecycle.entity.OrderDetailPool">
        select a.id,
               a.order_number,
               a.goods_sku,
               a.confirm_num,
               a.customer_reconciliation_num,
               a.customer_reconciliation_status,
               a.customer_invoiced_num,
               a.customer_invoiced_money,
               a.customer_settlement_num,
               a.customer_settlement_money,
               a.tax_rate,
               a.goods_unit_price_naked
        from order_detail_pool a
                 left join purchase_order_info_pool b on a.purchase_info_id = b.id
        where b.is_fix = 1
          and b.order_sales_channel = 1
          and b.company_code in ('honda', 'honda_sale')
          and a.history_bill_fix_flag = 1
          and a.customer_reconciliation_status != 0
  and a.customer_invoiced_num =0;
    </select>

    <select id="getHondaNewMallBill" resultType="com.ly.yph.api.orderlifecycle.dto.fix.BillDetailFixDto">
        select b.detail_id,
               c.order_detail_id,
               c.id            as settle_bill_pool_id,
               b.checked_num as checkNum,
               a.customer_code as companyCode,
               b.reconciliation_status
        from settle_shop_bill_detail b
                 left join settle_shop_bill a on a.bill_id = b.bill_id
                 left join settle_bill_pool c on c.id = b.settle_bill_pool_id
        where a.customer_type = 0
          and a.is_platform_reconciliation = 1
          and a.customer_code = #{companyCode}
          and b.is_enable = 1
          and b.pool_type = 1
    </select>
    <select id="getyflHistoryInvoiceBill"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.YflHistoryInvoiceDto">
        select *From yfl_history_invoice where process_state =0
    </select>
    <select id="getDfmallHistoryInvoice"
            resultType="com.ly.yph.api.orderlifecycle.dto.fix.DfmallHistoryInvoiceDto">
        select *From dfmall_history_invoice where process_state =0
    </select>

    <select id="getHondaOwnerOrderInfo" resultType="com.ly.yph.api.settlement.common.entity.ImportExtBill">
        select
        b.purchase_number,
        b.order_number,
        b.goods_sku,
        b.goods_code,
        b.confirm_num,
        b.goods_unit_price_naked,
        b.goods_total_price_tax,
        b.row_serial_number as itemNo,
        b.tax_rate,
        a.other_relation_number,
        b.order_detail_id,
        b.goods_desc,
        b.goods_unit_price_tax,
        b.goods_total_price_naked
        from purchase_order_info_pool a
        left join order_detail_pool b on a.id = b.purchase_info_id
        where a.order_sales_channel = 1
        and a.company_code = #{companyCode}
        and a.is_enable = 1
        and a.other_relation_number in
        <foreach collection="otherRelationNumberList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getFixSupplierInvoice"
            resultType="com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto">
        select b.order_detail_id,
               a.checked_num as invoice_num,
               a.total_price_tax as invoiceMoney,
               a.detail_id as billDetailId,
               c.id as lifeCycleId,
               b.order_Sales_Channel
        from settle_shop_bill_detail a
                 left join settle_bill_pool b on a.settle_bill_pool_id = b.id
                 left join settle_bill_life_cycle c on c.settle_bill_pool_id = b.id
        where a.bill_id =#{billId} and a.is_enable=1
    </select>


</mapper>

