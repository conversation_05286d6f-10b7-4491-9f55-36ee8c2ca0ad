package com.ly.yph.api.supplier.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商合同查询对象
 *
 * <AUTHOR>
 * @date 2025/1/18 17:23
 */
@Data
@ApiModel("供应商合同查询对象")
public class SystemContractQueryDto implements Serializable {

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商Id")
    private Long supplierId;

    @ApiModelProperty("签约企业id")
    private Long signCompanyId;

    @ApiModelProperty("合同文件名")
    private String contractName;

    @ApiModelProperty("合同编码")
    private String contractCode;

    @ApiModelProperty("合同截止日期 开始")
    private String contractStartDate;

    @ApiModelProperty("合同截止日期 结束")
    private String contractEndDate;

    @ApiModelProperty("签约状态 0 未到期 1 即将到期  2 已到期")
    private Integer signStatus;

    @ApiModelProperty("续签状态 0待续签 1续签中 2已续签 3未到续签时期 ")
    private Integer contractRenewalState;

    @ApiModelProperty("签约类型 0新增合同 1合同续签")
    private Integer signType;

    @ApiModelProperty("审批流范围:合同签约审批流程")
    private String handler = "contractWorkFlowHandler";

    private String userIds;

    @ApiModelProperty("合同审批状态")
    private Integer approveStatus;
}
