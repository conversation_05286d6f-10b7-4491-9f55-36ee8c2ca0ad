package com.ly.yph.api.orderlifecycle.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.vo.check.OutCheckSapDetailVo;
import com.ly.yph.api.orderlifecycle.dto.OrderDetailPoolPreOrderDto;
import com.ly.yph.api.orderlifecycle.dto.PurchaseOrderInfoDto;
import com.ly.yph.api.orderlifecycle.dto.PurchaseOrderInfoPageReqDto;
import com.ly.yph.api.orderlifecycle.dto.fix.*;
import com.ly.yph.api.orderlifecycle.entity.OrderDetailPool;
import com.ly.yph.api.orderlifecycle.entity.PurchaseOrderInfoPool;
import com.ly.yph.api.orderlifecycle.vo.PurchaseOrderDetailInfoVo;
import com.ly.yph.api.settlement.common.entity.ImportExtBill;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.tenant.core.aop.TenantIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface PurchaseOrderInfoPoolMapper extends BaseMapperX<PurchaseOrderInfoPool> {

    PurchaseOrderInfoDto getPurchaseOrderInfoDto(@Param("purchaseNumber") String purchaseNumber);

    List<OrderDetailPoolPreOrderDto> getOrderDetailPoolPreOrderList(@Param("purchaseNumber") String purchaseNumber);

    IPage<PurchaseOrderDetailInfoVo> getPurchaseOrderInfoDetailVo(Page<Object> adapterPageReq, @Param("query") PurchaseOrderInfoPageReqDto purchaseOrderInfoPageReqDto);

    List<PurchaseOrderDetailInfoVo> getPurchaseOrderInfoDetailVo(@Param("query") PurchaseOrderInfoPageReqDto purchaseOrderInfoPageReqDto);

    @TenantIgnore
    List<String> getFixOrderPoolCompanyList(@Param("tenantId") Long tenantId, @Param("endTime") String endTime);

    @TenantIgnore
    List<PurchaseOrderInfoDto> getFixPurchaseOrderInfo(@Param("tenantId") Long tenantId, @Param("companyCode") String companyCode, @Param("endTime") String endTime);

    @TenantIgnore
    List<OrderDetailPool> getFixOrderDetailPoolInfo(@Param("tenantId") Long tenantId, @Param("companyCode") String companyCode, @Param("endTime") String endTime);

    List<String> getDeliveryCompanyCode(@Param("orderSalesChannel") Integer orderSalesChannel);

    @TenantIgnore
    List<DeliveryDetailFix> getDeliveryDetailForFixPurchaseOrderInfo(@Param("companyCode") String companyCode, @Param("tenantId") Long tenantId);

    List<OrderDetailPool> getOrderDetailForCompanyAndOrderSaleChannel(@Param("companyCode") String companyCode, @Param("orderSalesChannel") Integer orderSalesChannel,@Param("orderDetailList") Set<String> orderDetailList);

    @TenantIgnore
    List<AfterSaleFix> getAfterSaleForFixPurchaseOrderInfo(@Param("tenantId") Long tenantId);

    List<OutCheckSapDetailVo> getallSapDeliveryData();

    List<OutCheckSapDetailVo> getallDfpvSap();

    List<OutCheckSapDetailVo> getallDfcvSap();

    List<OutCheckSapDetailVo> getallDhec();

    List<BillDetailFixDto> getDfmallBillDetailFixList(@Param("companyCode") String companyCode);

    List<BillDetailFixDto> getYflBillDetailFixList(@Param("companyCode") String companyCode);

    List<String> getDfmallBillDetailFixCompanyList();

    List<String> getYflBillDetailFixCompanyList();

    List<BillDetailInvoiceFixDto> getDfmallInvoiceDetailForCompany(@Param("companyCode")String companyCode);

    List<BillDetailInvoiceFixDto> getYflInvoiceDetailForCompany(@Param("companyCode") String companyCode);

    List<BillDetailFixDto> getDfmallSapBillDetailFixList();

    void insetBatchFixDto(@Param("list") List<FixOldBillDetailDto> fixOldBillDetailDtoList, @Param("companyCode") String companyCode, @Param("orderSalesChannel") Integer orderSalesChannel);

    @TenantIgnore
    List<ShopPurchaseSubOrderDetail> getOrderDetailStateByIds(@Param("orderDetailIds") List<String> orderDetailIds);

    @TenantIgnore
    List<DeliveryDetailFix> getFixDeliveryTs(@Param("list") List<Long> deliveryIds);

    @TenantIgnore
    List<DeliveryDetailFix> getFixGoodsCodeDelivery();

    List<BillDetailFix> getBillDetailFixListForPurchaseNumber(@Param("purchaseNumberList") Set<String> keySet);

    void updateBillDetailFix(@Param("fixIds") List<Long> fixIds);

    List<YfllineBillDto> getYflOfflineBillForActivityCode(@Param("activityCode") String activityCode);

    void updateYfloffline(@Param("yflOfflineIdList") List<Long> yflOfflineIdList, @Param("state") int state);

    @TenantIgnore
    List<ShopGoods> getLeaveGoods(@Param("goodsSkuList") List<String> goodsSkuList,@Param("tenantId") Long tenantId);

    List<HistorySettleBillDto> getInvoicedNoSettle(@Param("status") Integer status);

    void updateHistorySettleBillList(@Param("idList") List<Long> idList, @Param("status") Integer status);

    List<YflNemMallSettleBillDto> getYflNewMallSettleBillList(@Param("billId") Long billId);

    void saveSupplierSettleDataList(@Param("sublist") List<SupplierSettleDataDto> sublist);

    List<SupplierSettleDataDto> getFixSupplierSettleDataList();

    void updateSupplierSettleDataStatus(@Param("idList") List<Long> idList,@Param("status") Integer status);

    List<PurchaseTypeFixDto> getFixPurchaseTypeList(@Param("orderSalesChannel") Integer orderSalesChannel);

    List<OrderDetailPool> getHondaHistorySettle();

    List<BillDetailFixDto> getHondaNewMallBill(@Param("companyCode") String companyCode);

    List<YflHistoryInvoiceDto> getyflHistoryInvoiceBill();

    List<DfmallHistoryInvoiceDto> getDfmallHistoryInvoice();

    List<ImportExtBill> getHondaOwnerOrderInfo(@Param("otherRelationNumberList") List<String> otherRelationNumberList, @Param("companyCode") String companyCode);

    List<SupplierInvoiceDelDto> getFixSupplierInvoice(@Param("billId") Long billId);

    void fixSupplierBillDetail(@Param("billDetailList") List<SupplierInvoiceDelDto> billDetailList);

    void fixSupplierLifeCycle(@Param("lifeCycleList") List<SupplierInvoiceDelDto> lifeCycleList);
}
