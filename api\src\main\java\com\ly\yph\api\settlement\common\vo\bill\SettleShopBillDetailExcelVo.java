package com.ly.yph.api.settlement.common.vo.bill;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("对账单明细导出 vo")
public class SettleShopBillDetailExcelVo implements Serializable {

    @ApiModelProperty("序号")
    @ExcelProperty("序号")
    private Integer indexNum;

    @ApiModelProperty(value = "创建时间", dataType = "Date")
    @ExcelProperty("出账时间")
    private Date createTime;

    @ApiModelProperty("商城订单号")
    @ExcelProperty("商城订单号")
    private String orderNumber;

    @ApiModelProperty("申请单号")
    @ExcelProperty("申请单号")
    private String purchaseNumber;

    @ApiModelProperty("电商订单号")
    @ExcelProperty("电商订单号")
    private String supplierOrderNumber;

    @ApiModelProperty("外部系统单号")
    @ExcelProperty("SAP订单号")
    private String otherRelationNumber;

    @ApiModelProperty("电商sku")
    @ExcelProperty("电商sku")
    private String goodsSku;

    @ApiModelProperty("客户名称")
    @ExcelProperty("供应商名称")
    private String storeName;

    @ApiModelProperty("申请时间")
    @ExcelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("下单时间")
    @ExcelProperty("下单时间")
    private Date auditTime;

    @ApiModelProperty("下单人")
    @ExcelProperty("下单人")
    private String applyUserName;

    @ApiModelProperty("工号")
    @ExcelIgnore
    private String applyEmpCode;

    @ApiModelProperty("部门/科室")
    @ExcelProperty("部门/科室")
    private String applyDeptName;

    @ApiModelProperty("商品描述")
    @ExcelProperty("商品描述")
    private String goodsDesc;

    @ApiModelProperty("商城编码")
    @ExcelProperty("商城编码")
    private String goodsCode;

    @ApiModelProperty("销售单位")
    @ExcelProperty("单位")
    private String saleUnit;

    @ApiModelProperty("供应商型号")
    @ExcelProperty("型号")
    private String materialsCode;

    @ApiModelProperty("订单数量")
    @ExcelProperty("订单数量")
    private BigDecimal confirmNum;

    @ApiModelProperty("出账数量")
    @ExcelProperty("出账数量")
    private BigDecimal checkedNum;

    @ApiModelProperty("未税单价")
    @ExcelProperty("未税单价")
    private BigDecimal unitPriceNaked;

    @ApiModelProperty("含税单价")
    @ExcelProperty("含税单价")
    private BigDecimal unitPriceTax;

    @ApiModelProperty("未税总价")
    @ExcelProperty("未税总价")
    private BigDecimal totalPriceNaked;

    @ApiModelProperty("含税总价")
    @ExcelProperty("含税总价")
    private BigDecimal totalPriceTax;

    @ApiModelProperty("税率")
    @ExcelProperty("税率")
    private Integer taxRate;

    @ApiModelProperty("税务编码")
    @ExcelProperty("税务编码")
    private String taxCode;

    @ApiModelProperty("收货人")
    @ExcelProperty("收货人")
    private String addressName;

    @ApiModelProperty("收货人电话")
    @ExcelProperty("收货人电话")
    private String mobPhone;

    @ApiModelProperty("收货地址")
    @ExcelProperty("收货地址")
    private String address;

    @ApiModelProperty("物流妥投时间")
    @ExcelProperty("物流妥投时间")
    private Date supReceivingTime;

    @ApiModelProperty("签收时间")
    @ExcelProperty("签收时间")
    private Date cusReceivingTime;

    @ApiModelProperty("订单状态")
//    @ExcelProperty(value = "订单状态", converter = )
    @ExcelIgnore
    private Integer orderDetailState;

    @ApiModelProperty("物流单号")
    @ExcelProperty("物流单号")
    private String deliveryCode;

    @ApiModelProperty("下单开票主体")
    @ExcelProperty("下单开票主体")
    private String invoiceSubject;

    @ApiModelProperty("对账人")
    @ExcelProperty("对账人")
    private String reconciliationUserName;

    @ApiModelProperty("对账状态0.已出帐，1.待确认，2：已确认 3.已驳回 4.待开票，5.开票驳回，6.已开票，7.部分开票")
//    @ExcelProperty("对账状态")
    @ExcelIgnore
    private Integer reconciliationStatus;

    @ApiModelProperty("备注")
    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty(value = "订单状态")
    private String orderStatusStr;

    public String getOrderStatusStr() {
        String orderStatusStr = "";
        orderDetailState = (orderDetailState == null ? -1 : orderDetailState);
        switch (orderDetailState) {
            case 0: {
                orderStatusStr = "已取消";
                break;
            }
            case 10: {
                orderStatusStr = "提交";
                break;
            }
            case 20: {
                orderStatusStr = "待发货";
                break;
            }
            case 30: {
                orderStatusStr = "待收货";
                break;
            }
            case 40: {
                orderStatusStr = "收货完成";
                break;
            }
            default: {
                orderStatusStr = "失败";
            }
        }
        return orderStatusStr;
    }


    @ExcelProperty("对账状态")
    private String reconciliationStateStr;

    public String getReconciliationStateStr() {
        String reconciliationStateStr = "";
        reconciliationStatus = (reconciliationStatus == null ? 0 : reconciliationStatus);
        switch (reconciliationStatus) {
            case 0: {
                reconciliationStateStr = "已出帐";
                break;
            }
            case 1: {
                reconciliationStateStr = "待确认";
                break;
            }
            case 2: {
                reconciliationStateStr = "已确认";
                break;
            }
            case 3: {
                reconciliationStateStr = "已驳回";
                break;
            }
            case 4: {
                reconciliationStateStr = "待开票";
                break;
            }
            case 5: {
                reconciliationStateStr = "开票驳回";
                break;
            }
            case 6: {
                reconciliationStateStr = "已开票";
                break;
            }
            case 7: {
                reconciliationStateStr = "部分开票";
                break;
            }
            default: {
                reconciliationStateStr = "";
                break;
            }
        }
        return reconciliationStateStr;
    }

    @ApiModelProperty("大积分类别")
    @ExcelProperty("大积分类别")
    private String transformersDistrictName;

    @ApiModelProperty("积分类别")
    @ExcelProperty("积分类别")
    private String integralName;

    @ApiModelProperty("积分支付")
    @ExcelProperty("积分支付")
    private BigDecimal goodsPayIntegral;

    @ApiModelProperty("个人支付")
    @ExcelProperty("个人支付")
    private BigDecimal goodsPayMoney;

    @ApiModelProperty("是否预付款")
    @ExcelIgnore
    private Integer isPrePay;

    @ApiModelProperty("是否预付款")
    @ExcelIgnore
    private String isPrePayStr;

    public String getIsPrePayStr() {
        String isPrePayStr = "";
        isPrePay = isPrePay == null ? 0 : isPrePay;
        switch (isPrePay) {
            case 0: {
                isPrePayStr = "否";
                break;
            }
            case 1: {
                isPrePayStr = "是";
                break;
            }
            default: {
                isPrePayStr = "其他";
                break;
            }
        }
        return isPrePayStr;
    }

}
