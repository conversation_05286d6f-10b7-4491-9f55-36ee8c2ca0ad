-- 分支 settle_aftersale_0307
ALTER TABLE `settle_bill_pool`
    ADD COLUMN `supplier_checked_num` DECIMAL(14,2) DEFAULT 0.00 NOT NULL   COMMENT '供应商出账数量' AFTER `checked_num`;

ALTER TABLE `settle_bill_pool`
DROP COLUMN `delivery_name`;

ALTER TABLE `settle_shop_bill_detail`
    ADD COLUMN `bill_detail_type` TINYINT(1) DEFAULT 0  NULL   COMMENT '账单类型 0：正数账单 1：负向账单' AFTER `is_enable`;

ALTER TABLE `settle_shop_bill_detail`
DROP COLUMN `bill_match_flag`;

ALTER TABLE `settle_bill_pool`
    ADD  INDEX `createTimeIndex` (`create_time`);

ALTER TABLE `settle_bill_pool`
    ADD  INDEX `orderSalesChannelIndex` (`order_sales_channel`, `order_detail_id`);

ALTER TABLE `invoice_bill`
    ADD  INDEX `invoice_number_index` (`invoice_number`);

ALTER TABLE `invoice_bill`
    ADD COLUMN `tolerance_amount` DECIMAL(14,4) DEFAULT 0.00  NULL   COMMENT '容差金额' AFTER `price_mode`;

ALTER TABLE `repayment`
    ADD COLUMN `tolerance_amount` DECIMAL(14,4) DEFAULT 0.00  NULL   COMMENT '容差金额' AFTER `amount_of_repayment`;

-- 自动开票需求
ALTER TABLE `settle_bill_pool`
    CHANGE `budget_id` `auto_invoice_flag` BIGINT DEFAULT 0  NOT NULL   COMMENT '自动开票标记 0:不自动 1:自动';

ALTER TABLE `shop_purchase_order` add `auto_invoice_flag` BIGINT DEFAULT 0  NOT NULL COMMENT '自动开票标记 0:不自动 1:自动';

ALTER TABLE `mizd_invoice_plan`
    ADD COLUMN `external_business_order_number` VARCHAR(100) DEFAULT ''  NOT NULL   COMMENT '外部业务单号' AFTER `routing_number`,
  ADD COLUMN `current_step_name` VARCHAR(100) DEFAULT ''  NOT NULL   COMMENT '当前步骤名称' AFTER `external_business_order_number`;

-- 入池提前，客户供应商出账按验收和签收单规则出账
CREATE TABLE `settle_company_bill_message` (
                                               `id` bigint NOT NULL AUTO_INCREMENT,
                                               `check_form_detail_id` bigint NOT NULL DEFAULT '0',
                                               `delivery_detail_id` bigint NOT NULL DEFAULT '0',
                                               `company_check_state` int NOT NULL DEFAULT '0' COMMENT '企业验收状态 0:未验收 1：已验收',
                                               `company_process_state` int NOT NULL DEFAULT '0' COMMENT '处理状态 0：未处理 1：处理成功 2：处理失败',
                                               `supplier_check_state` int NOT NULL DEFAULT '0' COMMENT '供应商验收状态 0:未验收 1：已验收',
                                               `supplier_check_type` int NOT NULL DEFAULT '0' COMMENT '供应商验收类型 0：妥投 1：签收单',
                                               `supplier_process_state` int NOT NULL DEFAULT '0' COMMENT '供应商处理状态 0：未处理 1：处理成功 2：处理失败',
                                               `tenant_id` bigint DEFAULT NULL COMMENT '租户',
                                               `company_remark` text COLLATE utf8mb4_unicode_ci COMMENT '客户备注',
                                               `supplier_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '供应商备注',
                                               `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                               `modifier` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                               `is_enable` tinyint(1) DEFAULT '1',
                                               PRIMARY KEY (`id`),
                                               KEY `deliveryIndex` (`delivery_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

-- 等宣贯后 再将默认值改为1 然后开始按新标准执行
ALTER TABLE `shop_delivery`
    ADD COLUMN `is_history` TINYINT(1) DEFAULT 0  NOT NULL   COMMENT '是否历史数据 0:历史 1:非历史' AFTER `delivery_note_no`;

-- 需求：福利客户账单 用户成员管理 加部门字段
ALTER TABLE `settle_shop_bill_activity_member`
    ADD COLUMN `apply_dept_name` VARCHAR(100) DEFAULT ''  NULL AFTER `activity_name`;

-- 需求：支付计划加筛选字段
ALTER TABLE `mizd_invoice_plan`
    ADD COLUMN `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间' AFTER `current_step_name`,
  ADD COLUMN `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间' AFTER `create_time`,
    ADD COLUMN `is_enable` TINYINT(1) DEFAULT 1 NOT NULL AFTER `update_time`;

--  发票加发票明细是否存在合并项标记字段
ALTER TABLE `invoice_bill`
    ADD COLUMN `consolidation_needed` TINYINT(1) DEFAULT 0  NULL   COMMENT '是否存在发票明细合并项 0：不存在 1：存在';

-- 供应商入账 新包裹默认值
ALTER TABLE `shop_delivery`
    CHANGE `is_history` `is_history` TINYINT(1) DEFAULT 1  NOT NULL   COMMENT '是否历史数据 0:历史 1:非历史';


-- 供应商结算
ALTER TABLE `settle_shop_bill_detail`
    ADD COLUMN `bill_staging_flag` TINYINT(1) DEFAULT 0 NULL
COMMENT '是否暂存 0：未暂存  1已暂存',
ALGORITHM=INSTANT;

ALTER TABLE `settle_shop_bill_postage_detail`
    ADD COLUMN `postage_staging_flag` TINYINT(1) DEFAULT 0  NULL   COMMENT '是否暂存 0：未暂存  1已暂存';

ALTER TABLE `settle_shop_bill`
    ADD COLUMN `check_url` VARCHAR(500) DEFAULT '' NULL   COMMENT '验收单url' AFTER `is_platform_reconciliation`,
 ADD COLUMN `excel_url` VARCHAR(500) NULL   COMMENT 'excel url' AFTER `check_url`,
      ADD COLUMN `pay_flag` TINYINT(1) DEFAULT 0  NULL   COMMENT '0:初始状态 1：待生成 2：已生成' AFTER `excel_url`,
  ADD COLUMN `pay_money` DECIMAL(14,2) DEFAULT 0.00  NULL   COMMENT '支付计划完成支付金额' AFTER `pay_flag`,
CHANGE `remark` `remark` TEXT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci NULL   COMMENT '备注';


CREATE TABLE `settle_budget` (
                                 `id` bigint NOT NULL AUTO_INCREMENT,
                                 `project_no` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '项目号',
                                 `project_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '项目名称',
                                 `budget_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '预算名称',
                                 `supplier_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '供应商编码',
                                 `supplier_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '供应商名称',
                                 `is_enable` tinyint(1) DEFAULT '1',
                                 `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                 `modifier` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                 PRIMARY KEY (`id`),
                                 KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

CREATE TABLE `supplier_invoice_bill` (
                                         `id` bigint NOT NULL AUTO_INCREMENT,
                                         `invoice_apply_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票申请单号',
                                         `invoice_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '电子发票号',
                                         `bill_id` bigint NOT NULL COMMENT '账单id',
                                         `bill_sn` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '账单编号',
                                         `apply_id` bigint NOT NULL DEFAULT '0' COMMENT '申请人id',
                                         `apply_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '申请人姓名',
                                         `amount_naked` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '发票申请单未税总金额',
                                         `amount_tax` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '发票申请的含税总金额',
                                         `invoice_amount_tax` decimal(10,2) DEFAULT NULL COMMENT '供应商发票金额',
                                         `state` int NOT NULL COMMENT '供应商发票状态',
                                         `inconsistent_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '不一致说明',
                                         `confirm_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '确认者',
                                         `supplier_invoice_time` date DEFAULT NULL COMMENT '供应商发票开票时间',
                                         `invoice_upload_time` datetime DEFAULT NULL COMMENT '供应商上传发票的时间',
                                         `invoice_confirm_time` datetime DEFAULT NULL COMMENT '确认开票的时间',
                                         `supplier_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商编码',
                                         `supplier_name` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '供应商名称',
                                         `bill_invoice_type` tinyint(1) DEFAULT '0' COMMENT '发票明细类型 默认：0 账单明细类型 ;1:邮费明细类型',
                                         `tolerance_amount` decimal(14,4) DEFAULT '0.0000' COMMENT '容差金额',
                                         `invoice_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票路径',
                                         `inconsistent_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '不一致说明url',
                                         `approve_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '审批意见',
                                         `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                         `modifier` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                         `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                         `is_enable` tinyint(1) DEFAULT '1',
                                         PRIMARY KEY (`id`),
                                         KEY `bill_id` (`bill_id`),
                                         KEY `apply_id` (`apply_id`),
                                         KEY `state` (`state`),
                                         KEY `supplier_code` (`supplier_code`),
                                         KEY `invoice_apply_number` (`invoice_apply_number`),
                                         KEY `invoice_number` (`invoice_number`)
) ENGINE=InnoDB AUTO_INCREMENT=87 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `supplier_invoice_bill_detail` (
                                                `id` bigint NOT NULL AUTO_INCREMENT,
                                                `invoice_id` bigint NOT NULL,
                                                `bill_detail_id` bigint NOT NULL,
                                                `invoice_num` decimal(10,2) NOT NULL,
                                                `goods_unit_price_naked` decimal(14,4) DEFAULT NULL,
                                                `goods_unit_price_tax` decimal(14,4) DEFAULT NULL,
                                                `goods_total_price_naked` decimal(14,4) DEFAULT NULL,
                                                `goods_total_price_tax` decimal(14,4) DEFAULT NULL,
                                                `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                                `modifier` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                `is_enable` tinyint(1) NOT NULL DEFAULT '1',
                                                PRIMARY KEY (`id`),
                                                KEY `invoice_id` (`invoice_id`),
                                                KEY `bill_detail_id` (`bill_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=203 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `supplier_invoice_detail_postage` (
                                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                                   `invoice_id` bigint NOT NULL,
                                                   `postage_detail_id` bigint NOT NULL,
                                                   `postage` decimal(14,2) NOT NULL DEFAULT '0.00',
                                                   `creator` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                                   `modifier` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '',
                                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                   `is_enable` tinyint(1) DEFAULT '1',
                                                   PRIMARY KEY (`id`),
                                                   KEY `invoice_id` (`invoice_id`),
                                                   KEY `postage_detail_id` (`postage_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


ALTER TABLE `mizd_invoice_plan`
    ADD COLUMN `bill_url` VARCHAR(500) DEFAULT '' NULL   COMMENT '账单附件路径' AFTER `is_enable`,
  ADD COLUMN `invoice_url` TEXT NULL   COMMENT '发票路径' AFTER `bill_url`,
  ADD COLUMN `check_url` VARCHAR(500) DEFAULT ''  NULL   COMMENT '验收单路径' AFTER `invoice_url`,
  ADD COLUMN `contract_url` VARCHAR(500) DEFAULT ''  NULL   COMMENT '合同路径' AFTER `check_url`,
  ADD COLUMN `other_url` TEXT NULL   COMMENT '额外的附件' AFTER `contract_url`;


-- 加忽略租户的表
- supplier_invoice_bill
- supplier_invoice_bill_detail
- supplier_invoice_detail_postage
- settle_budget

-- 历史供应商邮费需要改成已出账状态
-- 京东的支付计划 部分已支付的 需要更新到账单的已支付金额
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-02','DSDLJS0','得力','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-03','DSZKH00','震坤行工业超市（上海）有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-04','DSXY000','西域智慧供应链（上海）股份公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-05','DSOFS00','欧菲斯','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-06','DSQXKJ0','深圳齐心集团股份有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-07','DSSNDQ0','苏宁','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-09','GYSYZH0','深圳市云中鹤科技股份有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-10','DSXFS00','鑫方盛数智科技股份有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-11','DSKLP00','晨光-科力普','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-12','A45093','柳州市联阳数控刀具设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-13','045192','柳州市广瑞机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-14','A45022','柳州市力茂机电工业设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-16','A45020','柳州市意强机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-17','A45004','柳州市宗元机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-18','045239','柳州市中大五金工具有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-23','WHTD001','武汉拓达科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-24','WHWD001','武汉微联有道科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-25','WHBC','武汉市丙成科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-26','3M0001','湖北小优科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-27','WHAYK','武汉安亿可安全科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-28','WHHCD','武汉恒创达汽车零部件有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-29','HBRLWY','湖北润联物业管理有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-30','ZZYW','枝江市天茂农业开发有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-31','ZZGAT','中智关爱通（上海）科技股份有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-32','DSQMY','四川德申庆贸易集团有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-33','WHFYQCBJ','武汉方元汽车部件制造有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-34','SUP_91420100796337636C','武汉东荣致远科技发展有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-35','SUP_91420100059196474U','武汉行芝达自动化科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-36','SUP_914201003472364692','武汉进度科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-37','SUP_911101017002402592','北京金隅天坛家具股份有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-38','SUP_91420100695309476T','湖北骏合机电设备工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-39','SUP_9142010356233685XF','霹雳马工业设备（武汉）有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-40','SUP_91440101304723478B','广东本来网电子商务有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-41','SUP_91420100MACJ57YN7P','东风汽车集团股份有限公司人事共享服务中心','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-42','SUPlanyou','深圳联友科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-43','SUP_91440113732974062P','广州市进禾机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-44','SUP_914201116727977539','武汉朝阳世纪电子有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-45','SUP_91440101MA9UKH8742','广州瑞函智能装备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-46','SUP_91420103663476237A','武汉海丰清洁系统有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-47','SUP_91420115744765030U','武汉银龙通达科贸有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-48','SUP_914201066854460715','武汉兴圣源化工设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-49','SUP_91420114MA4K4X1L23','湖北精本造智能科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-50','SUP_91420106672791300H','武汉聚毅信息科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-51','SUP_91420111672781348X','武汉全富科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-52','SUP_91420303MA49JFUX8C','湖北十鲜荟电子商务有限责任公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-53','SUP_91420300MA491QYM5X','十堰泰罗源贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-54','SUP_91440106797374164B','广州景驰机电工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-55','SUP_91440112MA59D16A8T','广州达宝文机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-56','SUP_914209845971885788','湖北凌睿自动化设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-57','SUP_91420100094387938E','湖北泰鑫和工业工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-58','SUP_91420100MA4KM28F1R','武汉和承机电工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-59','SUP_91420104706806049A','湖北中安安全设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-60','SUP_91420100MAC8PRXR7Q','正大卜蜂食品（湖北）有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-61','SUP_914201066918861291','湖北中合誉品粮油贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-62','SUP_91320621138543200C','南通科星化工股份有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-63','SUP_91320281MA22KK3B5H','江阴澜韵实业有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-64','SUP_91110113101199653R','北京鑫航泰科技发展有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-65','SUP_91350125050336784R','福建中海创自动化科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-66','SUP_91440101MA5CBLAM8G','广州井口机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-67','DSLTQC0','岚图汽车销售服务有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-68','SUP_91429021MA49FEYM9U','神农架晨兴农业发展有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-69','SUP_91429021MA49CQ1131','神农架绿禾农业有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-70','SUP_91330106574371572Y','杭州兑吧网络科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-71','SUP_914201127831739867','武汉晋鼎商贸有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-72','SUP_91420600MA496PFJ4H','湖北省东尔道机电技术有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-73','SUP_9132058307637771XP','江苏比高机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-74','SUP_911309827356257060','任丘市中亚自动焊接设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-75','SUP_91429021784481307J','神农架汇野生态食品有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-76','SUP_914201077483169964','武汉利丰达贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-77','SUP_91420104MA4KLRP85C','武汉科林机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-78','SUP_914206007510190364','襄阳益科机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-79','SUP_9142010266675321X3','武汉睿科达科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-80','SUP_91420100755145240R','武汉天视云畅广告传播有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-82','SUP_9142010278199350XP','武汉鑫瑞德机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-83','SUP_91440101783777544W','广州市川浦工业技术有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-84','SUP_914401015721996921','广州金道机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-85','SUP_91420100744787918U','武汉顺联机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-86','SUP_91420112685402239Y','武汉斯诸特机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-87','SUP_91440101583388755A','广州成业机电设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-88','SUP_9142010672830906XG','湖北有为自控设备工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-89','SUP_91420103669534135Q','武汉金润机电重工有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-90','SUP_91420600667685415A','襄阳市正工机电工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-91','SUP_914201007581553055','武汉英特玛机电工程有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-92','SUP_91420107303543635U','武汉亿中天设备有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-93','SUP_914201005945314305','武汉华荣天林科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-94','SUP_91460000MAA98FNG5N','海南昱林智造科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-95','SUP_91420103MA49AQ5K3E','武汉桑汇商贸有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-96','SUP_91420100055708081C','武汉海润德科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-97','SUP_91130296MA07QR6159','嘉利信得家具有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-98','SUP_91420100081959327A','武汉东运科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-99','SUP_91440106749937911J','广州兴图科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-100','SUP_91110108MA004CPN95','大象慧云信息技术有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-101','SUP_91420300714669152G','十堰中腾科工贸有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-102','SUP_914201113037240225','湖北鸿润云丰科技有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-103','SUP_91420529MA487DKE76','五峰万绿电子商务有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-104','SUP_91420100695318057M','东风融媒文化发展（湖北）有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-01','DSJD000','广州晶东贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-01','DSJD001','广州晶东贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2234','2022年 DFN 3SM商城采购合同','ZLGRM12-2234-01','DSJD005','广州晶东贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2234','2022年 DFN 3SM商城采购合同','ZLGRM12-2234-01','DSJD006','广州晶东贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-08','DSJD000_WH','武汉京东世纪贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-08','DSJD002','武汉京东世纪贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-08','DSJD003','武汉京东世纪贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2241','东风商城项目（M事业部）','ZLGRM12-2241-08','JDIOP00','武汉京东世纪贸易有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2234','2022年 DFN 3SM商城采购合同','ZLGRM12-2234-03','DSZKH01','震坤行工业超市（上海）有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2234','2022年 DFN 3SM商城采购合同','ZLGRM12-2234-03','ZKH_SW','震坤行工业超市（上海）有限公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2234','2022年 DFN 3SM商城采购合同','ZLGRM12-2234-04','DSXY001','西域智慧供应链（上海）股份公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
insert into `settle_budget` (`project_no`, `project_name`, `budget_number`, `supplier_code`, `supplier_name`, `is_enable`, `create_time`, `update_time`) values('GRM12-2234','2022年 DFN 3SM商城采购合同','ZLGRM12-2234-07','DSOB000','上海欧冶采购信息科技有限责任公司','1','2025-07-31 15:43:31','2025-07-31 15:43:31');
