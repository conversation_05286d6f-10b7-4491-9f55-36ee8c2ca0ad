package com.ly.yph.api.order.controller;


import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.honda.service.HondaOrderDealService;
import com.ly.yph.api.openapi.v1.dto.SrmUsePriceDto;
import com.ly.yph.api.order.dto.*;
import com.ly.yph.api.order.dto.srm.*;
import com.ly.yph.api.order.dto.youServiceOrder.YouServiceOrderPageQueryDto;
import com.ly.yph.api.order.entity.ShopOrderAddress;
import com.ly.yph.api.order.entity.ShopPurchaseOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.service.*;
import com.ly.yph.api.order.service.orderconfirmcondition.OrderConfirmConditionServiceImpl;
import com.ly.yph.api.order.vo.*;
import com.ly.yph.api.order.vo.youServiceOrder.YouServiceOrderPageVo;
import com.ly.yph.api.settlement.common.service.SettleCheckService;
import com.ly.yph.api.system.annotations.OperateLog;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.excel.util.ExcelUtils;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.idempotent.core.annotation.Idempotent;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.*;

import static com.ly.yph.api.order.enums.ErrorCodeConstants.PRE_NO_NOT_CONFIRM;
import static com.ly.yph.api.system.enums.OperateTypeEnum.EXPORT;

/**
 * (ShopPurchaseOrder)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-13 15:44:00
 */
@Api(tags = "商城 - 采购订单")
@RestController
@RefreshScope
@RequestMapping("shop/shopPurchaseOrder")
public class ShopPurchaseOrderController {

    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;

    @Resource
    private ShopPurchaseSubOrderService purSubOrderSrv;
    @Resource
    private OrderConfirmConditionServiceImpl conditionService;

    @Resource
    private DfsDoaPayService dfsDoaPayService;

    @Resource
    private SettleCheckService settleCheckService;

    @Resource
    private SrmMaterialInfoService srmMaterialInfoService;

    @Resource
    private HondaOrderDealService hondaOrderDealService;

    @Resource
    private ShopReturnService shopReturnService;

    @Value("${yfl.tenant-id}")
    private Long yflTenantId;

    /**
     * 商品校验
     *
     * @return 新增结果
     */
    @ApiOperation(value = "商品校验", httpMethod = "POST")
    @PostMapping("/orderGoodsCheck")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<List<Map<String, Object>>> orderGoodsCheck(@RequestBody GoodsCheckQueryDto goodsCheckQueryDto) {
        List<String> goodsCodes = Arrays.asList(goodsCheckQueryDto.getGoodsCode().split(","));
        List<String> goodsNumArray = Arrays.asList(goodsCheckQueryDto.getGoodsNum().split(","));
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> goodsMap = new HashMap<>();
        for (int i = 0; i < goodsCodes.size(); i++) {
            goodsMap.put(goodsCodes.get(i), Integer.valueOf(goodsNumArray.get(i)));
        }
        Map<String, String> checkMap = shopPurchaseOrderService.goodsCheck(goodsMap, goodsCheckQueryDto.getRegionDto(), goodsCheckQueryDto.getAddress(), null);

        goodsCodes.forEach(item -> {
            if (checkMap.containsKey(item)) {
                Map<String, Object> goodsCheck = new HashMap<>(8);
                goodsCheck.put("goodsCode", item);
                goodsCheck.put("checkState", false);
                goodsCheck.put("msg", checkMap.get(item));
                result.add(goodsCheck);
            } else {
                Map<String, Object> goodsCheck = new HashMap<>(8);
                goodsCheck.put("goodsCode", item);
                goodsCheck.put("checkState", true);
                goodsCheck.put("msg", "校验通过");
                result.add(goodsCheck);
            }
        });

        return ServiceResult.succ(result);
    }

    @ApiOperation("判断是否需要验收")
    @GetMapping("isNeedCheck")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult<Boolean> isNeedCheck(String supplierTypeArray, Integer isInvest) {
        return ServiceResult.succ(shopPurchaseOrderService.isNeedCheck(supplierTypeArray, isInvest));
    }

    /**
     * 启动采购审批流程
     *
     * @param getWay 启动流程变量对象
     * @return 新增结果
     */
    @ApiOperation(value = "提交采购申请", httpMethod = "POST")
    @PostMapping("/startPurchaseOrderWorkflow")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult<?> startPurchaseOrderWorkflow(@RequestBody final JSONObject getWay) {
        return ServiceResult.succ(this.shopPurchaseOrderService.startPurchaseOrderWorkflow(getWay));
    }

    /**
     * 提交采购申请
     *
     * @param purchaseOrderSaveDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "提交采购申请", httpMethod = "POST")
    @PostMapping("/savePurchaseOrder")
    @SaCheckPermission("business:purchaseOrder:update")
    @DistributedLock(value = "save_purchaseOrder_lock", key = "#purchaseOrderSaveDto.getApplyUserId() + #purchaseOrderSaveDto.getAddressId() ", waitLock = false, needThrow = true, throwMessage = "当前申请正在执行中,请耐心等待~")
    public ServiceResult<?> savePurchaseOrder(@RequestBody @Validated final PurchaseOrderSaveDto purchaseOrderSaveDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.savePurchaseOrder(purchaseOrderSaveDto));
    }

    /**
     * 提交采购申请
     *
     * @param purchaseOrderSaveDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "提交采购申请新", httpMethod = "POST")
    @PostMapping("/savePreOrder")
    @SaCheckPermission("business:purchaseOrder:update")
    @DistributedLock(value = "save_purchaseOrder_lock", key = "#purchaseOrderSaveDto.getApplyUserId() + #purchaseOrderSaveDto.getAddressId() ", waitLock = false, needThrow = true, throwMessage = "当前申请正在执行中,请耐心等待~")
    public ServiceResult<?> savePreOrder(@RequestBody @Validated final PurchaseOrderSaveDto purchaseOrderSaveDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.savePreOrder(purchaseOrderSaveDto));
    }

    /**
     * 查询预下单结果
     *
     * @param purchaseNumber 采购单号
     * @return 新增结果
     */
    @ApiOperation(value = "查询预下单结果", httpMethod = "GET")
    @GetMapping("/checkPreOrder")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult<?> checkPreOrder(final String purchaseNumber) {
        return this.shopPurchaseOrderService.checkPreOrder(purchaseNumber);
    }

    @ApiOperation(value = "检查是否可以开始确认订单流程")
    @GetMapping("/checkConditionDone")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<Boolean> checkConditionDone(final String purchaseNumber) {
        if (!this.conditionService.orderCanConfirm(purchaseNumber)) {
            throw HttpException.exception(PRE_NO_NOT_CONFIRM, purchaseNumber);
        }
        return ServiceResult.succ(true);
    }


    /**
     * 查询订单需要支付的现金
     *
     * @param orderType   0:查询采购单 1:查询订单
     * @param orderNumber 采购单号 或 订单号
     * @return 新增结果
     */
    @ApiOperation(value = "查询订单需要支付的现金", httpMethod = "GET")
    @GetMapping("/getPurchasePayPrice")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<Long> getPurchasePayPrice(String orderNumber, Integer orderType, Long activityId,
                                                   @RequestParam(value = "appType", required = false) String appType) {
        return ServiceResult.succ(shopPurchaseOrderService.getPurchasePayPrice(orderNumber, orderType, activityId, appType));
    }

    /**
     * 查询订单运费
     *
     * @param purchaseDeliveryPriceDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "获取采购订单运费", httpMethod = "POST")
    @PostMapping("/getPurchaseDeliveryPrice")
    @SaCheckPermission("business:purchaseOrder:query")
    @OperateLog(enable = false)
    public ServiceResult<PurchaseDeliveryPriceVo> getPurchaseDeliveryPrice(@RequestBody @Validated final PurchaseDeliveryPriceDto purchaseDeliveryPriceDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.getPurchaseDeliveryPrice(purchaseDeliveryPriceDto));
    }


    /**
     * 确认采购申请
     *
     * @param purchaseNumber 采购单号
     * @return 新增结果
     */
    @ApiOperation(value = "确认采购申请", httpMethod = "POST")
    @PostMapping("/confirmPurchaseOrder")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult<?> confirmPurchaseOrder(final String purchaseNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.confirmPurchaseOrder(purchaseNumber, ""));
    }

    /**
     * 确认供应商订单
     *
     * @param orderNumber 采购单号
     * @return 新增结果
     */
    @ApiOperation(value = "确认供应商订单", httpMethod = "POST")
    @PostMapping("/confirmSubOrder")
    @SaCheckPermission("business:purchaseOrder:update")
    @Idempotent(timeout = 10, message = "10秒内不能重复支付订单")
    public ServiceResult<?> confirmSubOrder(final String orderNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.confirmSubOrder(orderNumber));
    }

    @ApiOperation("分页查询采购申请单列表")
    @GetMapping("queryPurchaseOrderPage")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<ShopPurchaseOrderPageVo>> queryPurchaseOrderPage(final PageReq pageReq, final PurchaseOrderQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryPurchaseOrderPage(pageReq, queryDto));
    }

    /**
     * 避免影响商城销服看采购单数据乱
     *
     * @param pageReq
     * @param queryDto
     * @return
     */
    @ApiOperation("分页查询采购申请单列表yfw")
    @GetMapping("queryPurchaseOrderPageYfw")
    @SaCheckPermission("business:purchaseOrder:yflquery")
    @SaCheckLogin
    public ServiceResult<PageResp<ShopPurchaseOrderPageVo>> queryPurchaseOrderPageYfw(final PageReq pageReq, final PurchaseOrderQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryPurchaseOrderPageYfw(pageReq, queryDto));
    }

    @ApiOperation("商城订单 - 我的订单")
    @GetMapping("queryMyOrderPage")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<MyOrderFrontVo>> queryMyOrderPage(final PageReq pageReq, final MyOrderQueryDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryMyOrderPage(pageReq, queryDto));
    }

    @GetMapping("exportMyOrder")
    @ApiOperation("商城 - 我的订单导出")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportMyOrder(final MyOrderQueryDto queryDto, final HttpServletResponse response) throws IOException {
        final List<MyOrderFrontExcelVo> exportList = this.shopPurchaseOrderService.exportMyOrder(queryDto);
        ExcelUtils.write(response, "我的订单详情.xls", "我的订单详情列表", MyOrderFrontExcelVo.class, exportList);
    }

    /**
     * 商城 - 查询采购明细列表
     *
     * @param pageReq
     * @param purchaseOrderQueryDto
     * @return
     */
    @ApiOperation(value = "查询采购申请单明细列表", httpMethod = "GET")
    @GetMapping("/queryPurchaseOrderDetailPage")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<?> queryPurchaseOrderDetailPage(final PageReq pageReq, final PurchaseOrderQueryDto purchaseOrderQueryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryPurchaseOrderDetailPage(pageReq, purchaseOrderQueryDto));
    }

    /**
     * 商城 - 查询采购订单详情信息
     *
     * @param purchaseOrderQueryDto 采购单查询参数
     * @return 新增结果
     */
    @ApiOperation(value = "查询采购申请单详情信息", httpMethod = "POST")
    @PostMapping("/queryPurchaseOrderVoByNumber")
    @SaCheckPermission("business:purchaseOrder:query")
    @OperateLog(enable = false)
    public ServiceResult<ShopPurchaseOrderVo> queryPurchaseOrderVoByNumber(@RequestBody final PurchaseOrderQueryDto purchaseOrderQueryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryPurchaseOrderVoByNumber(purchaseOrderQueryDto));
    }

    /**
     * 商城 - 采购申请单商品详情
     *
     * @return
     */
    @ApiOperation(value = "采购申请单商品详情", httpMethod = "GET")
    @GetMapping("/queryPurchaseOrder")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult queryPurchaseOrder(final PurchaseOrderQueryDto purchaseOrderQueryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryPurchaseOrder(purchaseOrderQueryDto));
    }

    /**
     * 商城 - 采购申请单修改是否常购
     *
     * @return
     */
    @ApiOperation(value = "商城采购订单修改是否常购", httpMethod = "GET")
    @GetMapping("/updateByIsUrgent")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult updateByIsUrgent(final ShopPurchaseOrder shopPurchaseOrder) {
        this.shopPurchaseOrderService.updateById(shopPurchaseOrder);
        return ServiceResult.succ();
    }

    @GetMapping("exportPurchaseOrderByUser")
    @ApiOperation("商城 - 我的采购申请单导出")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportPurchaseOrderByUser(final PurchaseOrderQueryPageDto queryDto, final HttpServletResponse response) throws IOException {
        final List<ShopPurchaseOrderPageVo> queryList = this.shopPurchaseOrderService.queryPurchaseOrderList(queryDto);
        shopPurchaseOrderService.extractedCurrency(queryList);
        final List<PurchaseOrderByUserExcelVo> exportList = new ArrayList<>();
        queryList.forEach(item -> {
            final PurchaseOrderByUserExcelVo convert = DataAdapter.convert(item, PurchaseOrderByUserExcelVo.class);
            final Integer purchaseState = item.getPurchaseState();
            convert.setPurchaseState(purchaseState == 10 ? "审批中" : purchaseState == 20 ? "审批驳回" : "审批通过");
            exportList.add(convert);
        });
        ExcelUtils.write(response, "我的采购申请单.xls", "采购申请单列表", PurchaseOrderByUserExcelVo.class, exportList);
    }

    @GetMapping("exportPurchaseOrder")
    @ApiOperation("商城 - 采购申请单导出")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportPurchaseOrder(PurchaseOrderQueryPageDto queryDto, HttpServletResponse response) throws IOException {
        List<ShopPurchaseOrderPageVo> queryList = shopPurchaseOrderService.queryPurchaseOrderList(queryDto);
        if (TenantContextHolder.getRequiredTenantId().equals(yflTenantId)) {
            List<PurchaseOrderYflExcelVo> exportList = new ArrayList<>();
            queryList.forEach(item -> {
                PurchaseOrderYflExcelVo convert = DataAdapter.convert(item, PurchaseOrderYflExcelVo.class);
                Integer purchaseState = item.getPurchaseState();
                convert.setPurchaseState(purchaseState == 10 ? "审批中" : purchaseState == 20 ? "审批驳回" : "审批通过");
                exportList.add(convert);
            });
            ExcelUtils.write(response, "采购申请单.xls", "采购申请单列表", PurchaseOrderYflExcelVo.class, exportList);
        } else {
            List<PurchaseOrderExcelVo> exportList = new ArrayList<>();
            queryList.forEach(item -> {
                PurchaseOrderExcelVo convert = DataAdapter.convert(item, PurchaseOrderExcelVo.class);
                Integer purchaseState = item.getPurchaseState();
                convert.setPurchaseState(purchaseState == 10 ? "审批中" : purchaseState == 20 ? "审批驳回" : "审批通过");
                exportList.add(convert);
            });
            ExcelUtils.write(response, "采购申请单.xls", "采购申请单列表", PurchaseOrderExcelVo.class, exportList);
        }
    }


    /**
     * 商城 - 查询物流轨迹
     *
     * @return 新增结果
     */
    @ApiOperation(value = "商城查询物流轨迹", httpMethod = "POST")
    @PostMapping("/queryLogisticsTrack")
    @SaCheckPermission("business:purchaseOrder:track")
    @OperateLog(enable = false)
    public ServiceResult queryLogisticsTrack(@RequestBody final ArrayList<ShopPurchaseWlDto> list) {
        return this.shopPurchaseOrderService.queryLogisticsTrack(list);
    }

    @ApiOperation("订单管理 - 商城订单列表查询")
    @GetMapping("querySubOrderPage")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ShopOrderDetailPageVo.class)})
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<ShopOrderPageVo>> querySubOrderPage(final PageReq pageReq, final ShopOrderQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.querySubOrderPage(pageReq, queryDto));
    }

    @ApiOperation(value = "友服务订单列表查询", notes = "查询商城订单的订单列表信息,展现全部、待发货、待收货、已完成状态的订单信息列表。")
    @SaCheckLogin
    @GetMapping("queryYouServiceOrderPage")
    @SaCheckPermission("business:purchaseOrder:yflquery")
    public ServiceResult<PageResp<YouServiceOrderPageVo>> queryYouServiceSubOrderPage(final PageReq pageReq, final YouServiceOrderPageQueryDto youServiceOrderPageQueryDto) {
        return ServiceResult.succ(shopPurchaseOrderService.queryYouServiceSubOrderPage(pageReq, youServiceOrderPageQueryDto));
    }

    // region 东财查询商品订单

    @ApiOperation("订单管理 - 【东财】商城订单列表查询")
    @GetMapping("querySubOrderPageForDC")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ShopOrderDetailPageVo.class)})
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<ShopOrderPageVo>> querySubOrderPageForDC(final PageReq pageReq, final ShopOrderQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.querySubOrderPageForDC(pageReq, queryDto));
    }

    @ApiOperation("订单管理 - 【东财】商城订单金额统计信息")
    @GetMapping("subOrderPriceSumInfoForDC")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopOrderTotalPriceVo> subOrderPriceSumInfoForDC(final ShopOrderQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.subOrderPriceSumInfoForDC(queryDto));
    }

    @ApiOperation("订单管理 - 【东财】商城订单列表导出")
    @GetMapping("orderManagement-xlsxForDC")
    @SaCheckPermission("business:purchaseOrder:export")
    public void exportOrderManagementExcelForDC(final ShopOrderQueryPageDto queryDto, final HttpServletResponse response) throws IOException {
        this.shopPurchaseOrderService.exportOrderManagementExcelForDC(queryDto, response);
    }


    @ApiOperation("订单管理 - 【东财】商城订单明细列表查询")
    @GetMapping("querySubOrderDetailPageForDC")
    @ApiResponses({@ApiResponse(code = 200, message = "success", response = ShopOrderDetailPageVo.class)})
    @SaCheckPermission("business:OrderDetail:queryOrderDetailForDC")
    public ServiceResult<PageResp<ShopOrderDetailPageVo>> querySubOrderDetailPageForDC(final PageReq pageReq, final ShopOrderDetailQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.querySubOrderDetailPageForDC(pageReq, queryDto));
    }

    @ApiOperation("订单管理 - 【东财】商城订单列表导出")
    @GetMapping("orderDetailManagement-xlsxForDC")
    @OperateLog(type = EXPORT)
    @SaCheckPermission("business:OrderDetail:orderDetailExportForDC")
    public void orderDetailManagementExcelForDC(ShopOrderDetailQueryPageDto queryDto, HttpServletResponse response) throws IOException {
        List<ShopOrderDetailPageVo> pageList = shopPurchaseOrderService.exportOrderDetailsExcel(queryDto);
        if (TenantContextHolder.getRequiredTenantId().equals(yflTenantId)) {
            List<ShopOrderDetailYflExcelForDCVo> exportYflList = new ArrayList<>();
            pageList.stream().forEach(ShopOrderDetailPageVo -> {
                ShopOrderDetailYflExcelForDCVo orderDetailYflExcelVo = DataAdapter.convert(ShopOrderDetailPageVo, ShopOrderDetailYflExcelForDCVo.class);
                exportYflList.add(orderDetailYflExcelVo);
            });
            ExcelUtils.write(response, "订单详情.xls", "订单详情列表", ShopOrderDetailYflExcelForDCVo.class, exportYflList);
        } else {
            List<ShopOrderDetailExcelForDCVo> exportList = new ArrayList<>();
            pageList.stream().forEach(ShopOrderDetailPageVo -> {
                ShopOrderDetailExcelForDCVo orderDetailExcelVo = DataAdapter.convert(ShopOrderDetailPageVo, ShopOrderDetailExcelForDCVo.class);
                exportList.add(orderDetailExcelVo);
            });
            ExcelUtils.write(response, "订单详情.xls", "订单详情列表", ShopOrderDetailExcelForDCVo.class, exportList);
        }
    }


    /**
     * 订单管理 - 【东财】查询商城订单信息(供应商订单维度)
     *
     * @param orderNumber
     * @return
     */
    @ApiOperation(value = "【东财】查询商城订单信息", httpMethod = "GET")
    @GetMapping("/querySubOrderInfoForDC")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopPurchaseSubOrderVo> querySubOrderInfoForDC(@Param(value = "orderNumber") final String orderNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.querySubOrderInfoForDC(orderNumber));
    }

    // endregion

    @ApiOperation("友福利商城 - 订单列表查询")
    @GetMapping("queryYflSubOrderPage")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<ShopYflOrderVo>> queryYflSubOrderPage(final PageReq pageReq, final ShopYflOrderQueryDto yflOrderQueryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryYflSubOrderPage(pageReq, yflOrderQueryDto));
    }

    @ApiOperation("友福利商城 - 订单详细信息查询")
    @GetMapping("queryYflSubOrderInfo")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopYflOrderVo> queryYflSubOrderInfo(final String orderNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryYflSubOrderInfo(orderNumber));
    }

    @ApiOperation("友福利商城 - 虚拟订单查询卡密")
    @GetMapping("queryYflVirtualSubOrderCardPwd")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<VirtualOrderKMInfoVo> queryYflVirtualSubOrderCardPwd(final String orderNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryYflVirtualSubOrderCardPwd(orderNumber));
    }

    @ApiOperation("友福利商城 - 不同状态订单数量查询")
    @GetMapping("queryYflSubOrderStateCount")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<List<Map<String, Object>>> queryYflSubOrderStateCount() {
        LoginUser user = LocalUserHolder.get();
        return ServiceResult.succ(shopPurchaseOrderService.queryYflSubOrderStateCount(user.getUsername()));
    }

    /**
     * 友品汇订单中心 - 查询商城订单信息(供应商订单维度)
     *
     * @param orderNumber
     * @return
     */
    @ApiOperation(value = "查询商城订单信息", httpMethod = "GET")
    @GetMapping("/querySubOrderInfo")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopPurchaseSubOrderVo> querySubOrderInfo(@Param(value = "orderNumber") final String orderNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.querySubOrderInfo(orderNumber));
    }

    @ApiOperation("订单管理 - 商城订单金额统计信息")
    @GetMapping("subOrderPriceSumInfo")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopOrderTotalPriceVo> subOrderPriceSumInfo(final ShopOrderQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.subOrderPriceSumInfo(queryDto));
    }

    @ApiOperation("订单管理 - 商城订单明细列表查询")
    @GetMapping("querySubOrderDetailPage")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<ShopOrderDetailPageVo>> querySubOrderDetailPage(final PageReq pageReq, final ShopOrderDetailQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.querySubOrderDetailPage(pageReq, queryDto));
    }

    @ApiOperation("订单管理 - 商城订单明细金额统计信息")
    @GetMapping("subOrderDetailPriceSumInfo")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopOrderDetailTotalPriceVo> subOrderDetailPriceSumInfo(final ShopOrderDetailQueryPageDto queryDto) {
        return ServiceResult.succ(this.shopPurchaseOrderService.subOrderDetailPriceSumInfo(queryDto));
    }

    /**
     * 查询订单失败原因
     *
     * @param orderNumber 订单号
     */
    @ApiOperation(value = "订单取消原因查询", httpMethod = "GET")
    @GetMapping("/queryOrderFailReason")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult queryOrderFailReason(final String orderNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryOrderFailReason(orderNumber));
    }

    /**
     * 订单取消
     *
     * @param orderNumber 订单号
     * @param failReason  失败原因
     * @return {@link ServiceResult}
     */
    @ApiOperation(value = "订单取消", httpMethod = "GET")
    @GetMapping("/orderCancel")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult orderCancel(@RequestParam(value = "orderNumber") String orderNumber, @RequestParam(value = "failReason", required = false) String failReason) {
        shopPurchaseOrderService.orderCancel(orderNumber, failReason);
        return ServiceResult.succ();
    }

    /**
     * 采购商品导出
     *
     * @param response
     * @param shopPurchaseWlDto
     * @return
     */
    @GetMapping("exportGoods-xlsx")
    @SaCheckPermission("business:purchaseOrder:export")
    public void exportGoodsExcel(final ShopPurchaseWlDto shopPurchaseWlDto, final HttpServletResponse response) throws IOException, ClassNotFoundException {
        this.shopPurchaseOrderService.exportGoodsByExcel(shopPurchaseWlDto, response);
    }


    /**
     * 导出excel细节
     *
     * @param purchaseOrderQueryDto 采购订单查询dto
     * @param response              响应
     * @throws IOException ioexception
     */
    @GetMapping("exportDetail-xlsx")
    @SaCheckPermission("business:purchaseOrder:export")
    public void exportDetailExcel(final PurchaseOrderQueryDto purchaseOrderQueryDto, final HttpServletResponse response) throws IOException {
        this.shopPurchaseOrderService.exportDetailExcel(purchaseOrderQueryDto, response);
    }

    @ApiOperation("订单管理 - 商城订单列表导出")
    @GetMapping("orderManagement-xlsx")
    @SaCheckPermission("business:purchaseOrder:export")
    public void exportOrderManagementExcel(final ShopOrderQueryPageDto queryDto, final HttpServletResponse response) throws IOException {
        this.shopPurchaseOrderService.exportOrderManagementExcel(queryDto, response);
    }

    @ApiOperation("订单管理 - 商城订单详情列表导出")
    @GetMapping("orderDetails-xlsx")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportOrderDetailsExcel(ShopOrderDetailQueryPageDto queryDto, HttpServletResponse response) throws IOException {
        List<ShopOrderDetailPageVo> pageList = shopPurchaseOrderService.exportOrderDetailsExcel(queryDto);
        if (TenantContextHolder.getRequiredTenantId().equals(yflTenantId)) {
            List<ShopOrderDetailYflExcelVo> exportYflList = new ArrayList<>();
            pageList.stream().forEach(ShopOrderDetailPageVo -> {
                ShopOrderDetailYflExcelVo orderDetailYflExcelVo = DataAdapter.convert(ShopOrderDetailPageVo, ShopOrderDetailYflExcelVo.class);
                exportYflList.add(orderDetailYflExcelVo);
            });
            ExcelUtils.write(response, "订单详情.xls", "订单详情列表", ShopOrderDetailYflExcelVo.class, exportYflList);
        } else {
            List<ShopOrderDetailExcelVo> exportList = new ArrayList<>();
            pageList.stream().forEach(ShopOrderDetailPageVo -> {
                ShopOrderDetailExcelVo orderDetailExcelVo = DataAdapter.convert(ShopOrderDetailPageVo, ShopOrderDetailExcelVo.class);
                exportList.add(orderDetailExcelVo);
            });
            ExcelUtils.write(response, "订单详情.xls", "订单详情列表", ShopOrderDetailExcelVo.class, exportList);
        }

    }


    @ApiOperation(value = "按商品退货")
    @PostMapping("returnByGoods")
    @SaCheckPermission("business:purchaseOrder:update")
    public ServiceResult<?> returnByGoods(@RequestBody ReturnGoodsDto returnGoodsDto) {
        shopPurchaseOrderService.returnByGoods(returnGoodsDto);
        return ServiceResult.succ();
    }

    @ApiOperation(value = "按商品退货(商品可多选)")
    @PostMapping("returnBatchByGoods")
    @SaCheckPermission("business:purchaseOrder:returnBatchByGoods")
    public ServiceResult<?> returnBatchByGoods(@Validated @RequestBody ReturnBatchGoodsDto returnBatchGoodsDto) {
        if (returnBatchGoodsDto.getReturnTypeYfl() == 0) {
            shopPurchaseOrderService.returnBatchByGoods(returnBatchGoodsDto);
        } else {
            shopReturnService.returnBatchByGoodsOffline(returnBatchGoodsDto);
        }
        return ServiceResult.succ();
    }

    @ApiOperation(value = "获取订单购买地址")
    @GetMapping("queryOrderAddress")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopOrderAddress> selectOrderAddress(String purchaseNumber) {
        return ServiceResult.succ(shopPurchaseOrderService.selectOrderAddress(purchaseNumber));
    }

    /**
     * 出口采购细节excel
     *
     * @param purchaseOrderQueryDto 采购订单查询dto
     * @param response              响应
     * @throws IOException ioexception
     */
    @GetMapping("purchaseDetails-xlsx")
    @SaCheckPermission("business:purchaseOrder:export")
    public void exportPurchaseDetailsExcel(PurchaseOrderQueryDto purchaseOrderQueryDto, HttpServletResponse response) throws IOException {
        shopPurchaseOrderService.exportPurchaseDetailsExcel(purchaseOrderQueryDto, response);
    }

    /**
     * 超时订单取消
     *
     * @param timeOutDay 超时天数
     */
    @ApiOperation(value = "超时订单取消", httpMethod = "GET")
    @GetMapping("/cancelTimeOutOrderJob")
    public ServiceResult<?> cancelPurchaseOrderJob(@Param("timeOutDay") Integer timeOutDay) {
        shopPurchaseOrderService.cancelPurchaseOrderJob(timeOutDay);
        return ServiceResult.succ();
    }

    @ApiOperation(value = "临近超期订单通知", httpMethod = "GET")
    @GetMapping("/approachingExpirationJob")
    public ServiceResult<?> approachingExpirationJob(@Param("timeOutDay") Integer timeOutDay) {
        shopPurchaseOrderService.approachingExpirationJob(timeOutDay);
        return ServiceResult.succ();
    }

    /**
     * 发送订单到外部测试
     *
     * @param companyCode
     * @param purchaseNumber
     * @return
     */
    @ApiOperation(value = "发送订单到外部测试", httpMethod = "GET")
    @GetMapping("/sendExtPurchaseOrderInfo")
    public ServiceResult<?> sendExtPurchaseOrderInfo(@Param("companyCode") String companyCode, @Param("purchaseNumber") String purchaseNumber) {
        shopPurchaseOrderService.sendExtPurchaseOrderInfo(companyCode, purchaseNumber);
        return ServiceResult.succ();
    }

    @ApiOperation(value = "发送预订单到发动机", httpMethod = "GET")
    @GetMapping("/sendExtPurchaseOrderInfoToDhec")
    public ServiceResult<?> sendExtPurchaseOrderInfo(@Param("params") String params) {
        shopPurchaseOrderService.sendExtPurchaseOrderInfoToDhec(params);
        return ServiceResult.succ();
    }

    /**
     * 取消订单到外部测试 测试
     *
     * @param companyCode
     * @param purchaseNumber
     * @return
     */
    @ApiOperation(value = "取消订单到外部测试", httpMethod = "GET")
    @GetMapping("/cancelExtApprove")
    public ServiceResult<?> cancelExtApprove(@Param("companyCode") String companyCode, @Param("purchaseNumber") String purchaseNumber) {
        shopPurchaseOrderService.cancelExtApprove(companyCode, purchaseNumber);
        return ServiceResult.succ();
    }

    @ApiOperation(value = "发送收货信息到外部SAP", httpMethod = "POST")
    @GetMapping("/sendDeliveryInfoToDPCASap")
    public ServiceResult<?> sendDeliveryInfoToDPCASap(@Param("deliveryCode") String deliveryCode, @Param("supplierCode") String supplierCode) {
        shopPurchaseOrderService.sendDeliveryInfoToDPCASap(deliveryCode, supplierCode);
        return ServiceResult.succ();
    }

    /**
     * 发送收货信息到外部SAP  测试
     *
     * @param receiptGoodsDtoList
     * @return
     */
    @ApiOperation(value = "发送收货信息到外部SAP", httpMethod = "POST")
    @PostMapping("/sendDeliveryInfoToSap")
    public ServiceResult<?> sendDeliveryInfoToSap(@RequestBody List<ReceiptGoodsDto> receiptGoodsDtoList) {
        shopPurchaseOrderService.sendDeliveryInfoToSap(receiptGoodsDtoList);
        return ServiceResult.succ();
    }

    @ApiOperation(value = "发送收货信息到外部SAP（验收）", httpMethod = "POST")
    @PostMapping("/sendCheckInfoToSap")
    public ServiceResult<?> sendCheckInfoToSap(@RequestBody Long checkId) {
        settleCheckService.getCheckDetailByCheckId(checkId);
        return ServiceResult.succ();
    }


    /**
     * 商城-我的订单-订单详情商品导出
     *
     * @param orderNumber
     */
    @GetMapping("myOrderDetailGoodsExport")
    @SaCheckPermission("business:purchaseOrder:export")
    public void myOrderDetailGoodsExport(@RequestParam(value = "orderNumber") String orderNumber,
                                         HttpServletResponse response) throws IOException {
        shopPurchaseOrderService.myOrderDetailGoodsExport(orderNumber, response);
    }


    /**
     * 常购清单获取接口
     *
     * @param oftenBuyListDto 经常买dto列表
     * @return {@code ServiceResult<List<OftenBuyListVo>>}
     */
    @GetMapping("oftenBuyList")
    @ApiOperation(value = "常购清单获取接口", httpMethod = "GET")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<T>> getOftenBuyList(PageReq pageReq, OftenBuyListDto oftenBuyListDto) {
        return ServiceResult.succ(shopPurchaseOrderService.getOftenBuyList(pageReq, oftenBuyListDto));
    }


    @GetMapping("oftenBuyListExport")
    @ApiOperation(value = "常购清单导出", httpMethod = "GET")
    @SaCheckPermission("business:purchaseOrder:export")
    public void oftenBuyListExport(HttpServletResponse response, OftenBuyListDto oftenBuyListDto) throws IOException {
        shopPurchaseOrderService.oftenBuyListExport(response, oftenBuyListDto);
    }

    @GetMapping("queryOrderByOrderNum")
    @ApiOperation(value = "根据订单号查询订单", httpMethod = "GET")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<ShopPurchaseSubOrder> queryOrderByOrderNum(String orderNumber) {
        return ServiceResult.succ(purSubOrderSrv.getByOrderNumber(orderNumber));
    }

    @ApiOperation("前台商城订单")
    @GetMapping("queryMallOrderPage")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<MallOrderQueryVo>> queryMallOrderPage(PageReq pageReq, MallOrderQueryDto queryDto) {
        queryDto.setEntityOrganizationId(LocalUserHolder.get().getEntityOrganizationId());
        return ServiceResult.succ(shopPurchaseOrderService.queryMallOrderPage(pageReq, queryDto));
    }

    @ApiOperation("前台商城订单导出")
    @GetMapping("exportMallOrder")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportMallOrder(MallOrderQueryDto queryDto, HttpServletResponse response) throws IOException {
        queryDto.setEntityOrganizationId(LocalUserHolder.get().getEntityOrganizationId());
        List<MyOrderFrontExcelVo> exportList = shopPurchaseOrderService.exportMallOrder(queryDto);
        ExcelUtils.write(response, "商城订单详情.xls", "商城订单详情列表", MyOrderFrontExcelVo.class, exportList);
    }

    @ApiOperation("前台履约查询")
    @GetMapping("queryAgreement")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<AgreementQueryVo>> queryAgreement(PageReq pageReq, AgreementQueryDto queryDto) {
        queryDto.setEntityOrganizationId(LocalUserHolder.get().getEntityOrganizationId());
        return ServiceResult.succ(shopPurchaseOrderService.queryAgreement(pageReq, queryDto));
    }

    @ApiOperation("前台履约导出")
    @GetMapping("exportAgreementOrder")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportAgreement(AgreementQueryDto queryDto, HttpServletResponse response) throws IOException {
        queryDto.setEntityOrganizationId(LocalUserHolder.get().getEntityOrganizationId());
        PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        List<AgreementQueryVo> exportList = shopPurchaseOrderService.queryAgreement(pageReq, queryDto).getData();
        ExcelUtils.write(response, "履约管理详情.xls", "履约管理详情列表", AgreementQueryVo.class, exportList);
    }


    /**
     * 运营管理-履约管理-发货提醒查询
     * 超下单时间15天以上未全部发货的商品
     * 按照超下单时间倒序，根据超下单时间分三阶段打标签：
     * 15-30天，30-45天，大于45天  -超出时间overlapDays(0-15:1,15-30:2,30-45:3,大于45:4)
     *
     * @return
     * <AUTHOR>
     * @since 2024-01-22 10:35:00
     */
    @ApiOperation("运营管理-履约管理-发货提醒列表")
    @GetMapping("queryAgreementDelivery")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<AgreementQueryVo>> queryAgreementDelivery(PageReq pageReq, AgreementQueryDto queryDto) {
//        queryDto.setEntityOrganizationId(LocalUserHolder.get().getEntityOrganizationId());
        return ServiceResult.succ(shopPurchaseOrderService.queryAgreementDelivery(pageReq, queryDto));
    }

    /**
     * 运营管理-履约管理-妥投提醒查询
     * 已发货 7天无妥投信息的商品
     * 按照超发货时间倒序，根据超发货时间分三阶段打标签：
     * 7-15天，15-30天，大于30天  -超出时间overlapDays(0-7:1,7-15:2,15-30:3,大于30:4)
     *
     * @return
     * <AUTHOR>
     * @since 2024-01-22 10:35:00
     */
    @ApiOperation("运营管理-履约管理-妥投提醒列表")
    @GetMapping("queryAgreementReceiving")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<AgreementQueryVo>> queryAgreementReceiving(PageReq pageReq, AgreementQueryDto queryDto) {
//        queryDto.setEntityOrganizationId(LocalUserHolder.get().getEntityOrganizationId());
        return ServiceResult.succ(shopPurchaseOrderService.queryAgreementReceiving(pageReq, queryDto));
    }


    @ApiOperation("后台履约跟踪")
    @GetMapping("goodsToBeShipped")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<PageResp<AgreementQueryVo>> goodsToBeShipped(PageReq pageReq, AgreementQueryDto queryDto) {
        return ServiceResult.succ(shopPurchaseOrderService.goodsToBeShipped(pageReq, queryDto));
    }

    @ApiOperation("后台履约跟踪-已确认待发货数量统计")
    @GetMapping("queryUnshippedGoods")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<Map<String, Long>> queryUnshippedGoods(AgreementQueryDto queryDto) {
        return ServiceResult.succ(shopPurchaseOrderService.queryUnshippedGoods(queryDto));
    }

    @ApiOperation("后台履约跟踪-已妥投待收货数量统计")
    @GetMapping("queryUnreceivedGoods")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<Map<String, Long>> queryUnreceivedGoods(AgreementQueryDto queryDto) {
        return ServiceResult.succ(shopPurchaseOrderService.queryUnreceivedGoods(queryDto));
    }

    @ApiOperation("后台履约跟踪-已确认待发货导出")
    @GetMapping("exportUndeliveredOrder")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportUndeliveredOrder(AgreementQueryDto queryDto, HttpServletResponse response) throws IOException {
        PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        List<AgreementQueryVo> list = shopPurchaseOrderService.goodsToBeShipped(pageReq, queryDto).getData();
        List<GoodsToBeShippedExcelVo> exportList = JSON.parseArray(JSON.toJSONString(list), GoodsToBeShippedExcelVo.class);
        ExcelUtils.write(response, "履约跟踪详情.xls", "已确认待发货详情列表", GoodsToBeShippedExcelVo.class, exportList);
    }

    @ApiOperation("后台履约跟踪-妥投待收货导出")
    @GetMapping("exportAlreadyInvested")
    @SaCheckPermission("business:purchaseOrder:export")
    @OperateLog(type = EXPORT)
    public void exportAlreadyInvested(AgreementQueryDto queryDto, HttpServletResponse response) throws IOException {
        PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        List<AgreementQueryVo> list = shopPurchaseOrderService.goodsToBeShipped(pageReq, queryDto).getData();
        List<GoodsToReceivedExcelVo> exportList = JSON.parseArray(JSON.toJSONString(list), GoodsToReceivedExcelVo.class);
        ExcelUtils.write(response, "履约管理详情.xls", "履约管理详情列表", GoodsToReceivedExcelVo.class, exportList);
    }

    @ApiOperation("同步并更新东风南方DOA订单付款状态")
    @GetMapping("syncDfsDoaPayOrder")
    public void syncDfsDoaPayOrder() {
        dfsDoaPayService.syncDfsDoaPayOrder();
        dfsDoaPayService.syncDfsDoaPaySettle();
    }

    @ApiOperation("测试获取南方DOA付款状态")
    @GetMapping("syncDfsDoaPayOrderTest")
    public void syncDfsDoaPayOrderTest(String purchaseNumber) {
        dfsDoaPayService.syncDfsDoaPayOrderTest(purchaseNumber);
    }

    /**
     * 查询采购单信息
     *
     * @param purchaseNumber
     * @return
     */
    @ApiOperation(value = "查询采购单信息", httpMethod = "GET")
    @GetMapping("/queryOrderInfo")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<List<ShopPurchaseSubOrderVo>> queryOrderInfo(@Param(value = "purchaseNumber") final String purchaseNumber) {
        return ServiceResult.succ(this.shopPurchaseOrderService.queryOrderInfo(purchaseNumber));
    }

    /**
     * 查询SRM物料信息
     *
     * @param srmPurchaseMaterialDto
     * @return
     */
    @ApiOperation(value = "查询SRM物料信息", httpMethod = "GET")
    @GetMapping("/querySrmPurchaseMaterial")
    @SaCheckPermission("business:purchaseOrder:queryMaterial")
    public ServiceResult<PageResp<SrmMaterialVo>> querySrmPurchaseMaterial(@Validated SrmPurchaseMaterialDto srmPurchaseMaterialDto) {
        return ServiceResult.succ(srmMaterialInfoService.querySrmPurchaseMaterial(srmPurchaseMaterialDto));
    }

    @ApiOperation("南方用户订单超时支付提醒查询")
    @GetMapping("queryDfsOrderPayTimeOut")
    @SaCheckPermission("business:purchaseOrder:query")
    public void queryDfsOrderPayTimeOut() {
        purSubOrderSrv.queryDfsOrderPayTimeOut();
    }

    @ApiOperation("联友用户查询SRM申请单信息")
    @GetMapping("querySrmApplyInfo")
    @SaCheckPermission("business:purchaseOrder:querySrmApplyInfo")
    public ServiceResult<SrmApplyInfoVo> querySrmApplyInfo(@Param(value = "purchaseNumber") String purchaseNumber) {
        return ServiceResult.succ(purSubOrderSrv.querySrmApplyInfo(purchaseNumber));
    }

    @ApiOperation("修复采购单未税金额")
    @GetMapping("fixPurchaseNakedPrice")
    public ServiceResult<String> fixPurchaseNakedPrice() {
        return ServiceResult.succ(purSubOrderSrv.fixPurchaseNakedPrice());
    }

    @ApiOperation("srm占用金额")
    @PostMapping("srmUsePrice")
    @SaCheckPermission("business:purchaseOrder:srmUsePrice")
    public ServiceResult<?> srmUsePrice(@RequestBody SrmUsePriceDto dto) {
        srmMaterialInfoService.srmUsePrice(dto);
        return ServiceResult.succ();
    }

    @ApiOperation("srm成本预估")
    @GetMapping("/querySrmCostEstimate")
    @SaCheckPermission("business:purchaseOrder:srmCostEstimate")
    public ServiceResult<List<SrmCostEstimateVo>> querySrmCostEstimate(String projectNo) {
        return ServiceResult.succ(srmMaterialInfoService.querySrmCostEstimate(projectNo));
    }

    @ApiOperation("srm采购组织")
    @GetMapping("/querySrmPurchaseOrg")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<List<SrmPurchaseOrgVo>> querySrmPurchaseOrg() {
        return ServiceResult.succ(srmMaterialInfoService.querySrmPurchaseOrg());
    }

    @ApiOperation("srm申请类型")
    @GetMapping("/querySrmApplicationType")
    @SaCheckPermission("business:purchaseOrder:query")
    public ServiceResult<List<SrmApplicationTypeVo>> querySrmApplicationType(String purchOrgId) {
        return ServiceResult.succ(srmMaterialInfoService.querySrmApplicationType(purchOrgId));
    }

//    @ApiOperation("srm成本预估--查询工联单下物料信息")
//    @GetMapping("/querySrmCostEstimateMaterial")
//    public ServiceResult<SrmCostEstimateVo> querySrmCostEstimateMaterial(String projectNo,String unionPurchaseNo){
//        return ServiceResult.succ(srmMaterialInfoService.querySrmCostEstimate(projectNo,unionPurchaseNo));
//    }

    @ApiOperation("srm查询项目")
    @PostMapping("/querySrmProject")
    @SaCheckPermission("business:purchaseOrder:querySrmProject")
    public ServiceResult<PageResp<SrmProjectVo>> querySrmProject(@RequestBody SrmProjectDto srmProjectDto) {
        return ServiceResult.succ(srmMaterialInfoService.querySrmProject(srmProjectDto));
    }

    @ApiOperation("srm查询投资列表")
    @GetMapping("/querySrmInvest")
    @SaCheckPermission("business:purchaseOrder:srmQuery")
    public ServiceResult<List<SrmInvestProjectResp>> querySrmInvest(@Validated SrmInvestProjectReq req) {
        return ServiceResult.succ(srmMaterialInfoService.querySrmInvestProject(req));
    }

    @ApiOperation("下单结算方式提醒")
    @GetMapping("/orderSettlementReminders")
    @SaCheckPermission("business:purchaseOrder:settlementReminders")
    public ServiceResult<Set<OrderSettlementRemindersDto>> orderSettlementReminders(@RequestParam("goodsCodes") List<String> goodsCodes) {
        return ServiceResult.succ(shopPurchaseOrderService.orderSettlementReminders(goodsCodes));
    }

    /**
     * 查询东本发动机物料用途基础数据列表
     * 采购申请 ->电商商品用途
     */
    @ApiOperation("查询东本发动机物料用途基础数据列表")
    @PostMapping("/dhecUses")
    @SaCheckPermission("business:purchaseOrder:dhecUses")
    public ServiceResult<?> dhecUseList(@RequestBody PageReq pageReq) {
        return ServiceResult.succ(shopPurchaseOrderService.dhecUseList(pageReq));
    }

    /**
     * 查看东本发动机预配置链接
     * 采购单详情 ->采购单状态 查看东本发动机预订单审批进度画面链接
     * 商城首页 ->无选择商品  商城选择不到具体的物品时，画面链接回到采购系统进行编码申请,需要单点登陆
     *
     * @param key   shopflow  预订单审批链接  approve 采购系统进行编码申请链接
     * @param value
     */
    @ApiOperation("查看东本发动机预配置链接")
    @GetMapping("/dhecAllocationUrl")
    @SaCheckPermission("business:purchaseOrder:dhecAllocationUrl")
    public ServiceResult<?> dhecAllocationUrl(@Param(value = "key") @NotEmpty(message = "预配置链接key不能为空") String key, String value) {
        return ServiceResult.succ(shopPurchaseOrderService.dhecAllocationUrl(key, value));
    }

    /**
     * 超时未制单成功订单取消
     *
     * @param timeOutMinute 超时分钟
     */
    @ApiOperation(value = "超时未制单成功订单取消", httpMethod = "GET")
    @GetMapping("/cancelNotMakePurchaseOrderJob")
    public ServiceResult<?> cancelNotMakePurchaseOrderJob(@RequestParam("timeOutMinute") Integer timeOutMinute, @RequestParam("isPlatformReconciliation") Integer isPlatformReconciliation) {
        shopPurchaseOrderService.cancelNotMakePurchaseOrderJob(timeOutMinute, isPlatformReconciliation);
        return ServiceResult.succ();
    }

    /**
     * 30分钟内自动手动取消积分类订单
     *
     * @param purchaseNumber 采购单号
     */
    @ApiOperation(value = "30分钟内自动手动取消积分类订单", httpMethod = "GET")
    @GetMapping("/cancelHondaIntegralPurchaseOrder")
    public ServiceResult<?> cancelHondaIntegralPurchaseOrder(@RequestParam("purchaseNumber") String purchaseNumber) {
       return hondaOrderDealService.hondaCancelPurchaseOrder(purchaseNumber);
    }



    @ApiOperation("获取联友配置开票主体")
    @GetMapping("/querySzlyInvoiceSubject")
    @SaCheckPermission("business:purchaseOrder:szlyInvoiceSubject")
    public ServiceResult<?> querySzlyInvoiceSubject() {
        return ServiceResult.succ(shopPurchaseOrderService.querySzlyInvoiceSubject());
    }

    @ApiOperation("财务中心预付款参数封装")
    @GetMapping("/dfsDoaPayData")
    @SaCheckPermission("business:purchaseOrder:dfsDoaPayData")
    public ServiceResult<?> dfsDoaPayData(@RequestParam(value = "purchaseNumber") String purchaseNumber) {
        return ServiceResult.succ(dfsDoaPayService.dfsDoaPayData(purchaseNumber));
    }

    @ApiOperation("财务中心报销和一般付款参数封装")
    @GetMapping("/dfsDoaCheckData")
    @SaCheckPermission("business:purchaseOrder:dfsDoaCheckData")
    public ServiceResult<?> dfsDoaCheckData(@RequestParam(value = "checkNumbers") List<String> checkNumbers, @RequestParam(value = "billType") String billType) {
        return ServiceResult.succ(dfsDoaPayService.dfsDoaCheckData(checkNumbers, billType));
    }

    @ApiOperation("获取Srm采购组织")
    @GetMapping("/getUserRolePurchOrgId")
    @SaCheckPermission("business:purchaseOrder:getUserRolePurchOrgId")
    public ServiceResult<?> getUserRolePurchOrgId(@RequestParam(value = "userName") String userName) {
        return ServiceResult.succ(srmMaterialInfoService.getUserRolePurchaseOrgId(userName));
    }

    @ApiOperation("获取Srm采购名称校验")
    @GetMapping("/queryIsExistsProjectName")
    @SaCheckPermission("business:purchaseOrder:queryIsExistsProjectName")
    public ServiceResult<?> queryIsExistsProjectName(@RequestParam(value = "purchaseApplyTitle") String purchaseApplyTitle) {
        return ServiceResult.succ(srmMaterialInfoService.queryIsExistsProjectName(purchaseApplyTitle));
    }


    @ApiOperation("查询岚图门店信息")
    @GetMapping("/queryVoyahStoreInfos")
    @SaCheckPermission("business:purchaseOrder:queryVoyahStoreInfos")
    public ServiceResult<?> queryVoyahStoreInfos() {
        return ServiceResult.succ(shopPurchaseOrderService.queryVoyahStoreInfos());
    }
    @ApiOperation(value = "有福利自定活动商品是否已下过单", httpMethod = "GET")
    @GetMapping("checkGoodsIsBuyed")
    @SaCheckPermission("business:purchaseOrder:yflCheckGoodsIsBuyed")
    public ServiceResult<?>  checkGoodsIsBuyed(String activityId, Long goodsId) throws IOException {
        return shopPurchaseOrderService.checkGoodsIsBuyed(activityId, goodsId);
    }
}


