package com.ly.yph.api.virtualgoods.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年07月11日
 */
@Data
public class SaveVirtualGoodsDto implements Serializable {
    private static final long serialVersionUID = -4014234574225180493L;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", dataType = "String")
    @NotBlank(message = "商品名称不能为空")
    @Length(max = 128, message = "商品名称过长")
    private String goodsName;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述", dataType = "String")
    @Length(max = 100000, message = "商品描述过长(100000)")
    @NotBlank(message = "商品描述不能为空")
    private String goodsDesc;

    /**
     * 商品SKU
     */
    @TableField(value = "goods_sku")
    @ApiModelProperty(value = "商品SKU")
    @NotBlank(message = "商品SKU不能为空")
    private String goodsSku;

    @ApiModelProperty(value = "品牌名称", dataType = "String")
    @NotBlank(message = "品牌名称不能为空")
    @Length(max = 64, message = "品牌名称过长(64)")
    private String brandName;

    /**
     * 供应商型号
     */
    @ApiModelProperty(value = "供应商型号", dataType = "String")
    @Length(max = 128, message = "供应商型号过长(128)")
    private String materialsCode;

    /**
     * 生产厂家制造型号
     */
    @TableField(value = "manufacturer_material_no")
    @ApiModelProperty(value = "生产厂家制造型号")
    @Length(max = 128, message = "生产厂家制造型号过长(128)")
    private String manufacturerMaterialNo;

    /**
     * 商品关键字
     */
    @TableField(value = "goods_keywords")
    @ApiModelProperty(value = "商品关键字")
    @Length(max = 300, message = "关键字过长(300)")
    private String goodsKeywords;

    /**
     * 包装规格
     */
    @ApiModelProperty(value = "包装规格", dataType = "String")
    @Length(max = 128, message = "包装规格过长")
    private String specGoodsWareQd;

    /**
     * 含税协议价
     */
    @ApiModelProperty(value = "含税协议价", dataType = "Decimal")
    @NotNull(message = "含税协议价不能为空")
    @DecimalMin(value = "0.01", message = "含税协议价超出范围")
    @DecimalMax(value = "99999999.99", message = "含税协议价超出范围")
    private BigDecimal goodsPactPrice;
    /**
     * 商品原价
     */
    @ApiModelProperty(value = "商品原价", dataType = "Decimal")
    @NotNull(message = "建议零售不能为空")
    @DecimalMin(value = "0.01", message = "建议零售价超出范围")
    @DecimalMax(value = "99999999.99", message = "建议零售价超出范围")
    private BigDecimal goodsOriginalPrice;
    /**
     * 不含税协议价
     */
    @ApiModelProperty(value = "商品不含税原价", dataType = "Decimal")
    @NotNull(message = "建议零售不能为空")
    @DecimalMin(value = "0.01", message = "建议零售价超出范围")
    @DecimalMax(value = "99999999.99", message = "建议零售价超出范围")
    private BigDecimal goodsOriginalNakedPrice;
    /**
     * 不含税协议价
     */
    @ApiModelProperty(value = "不含税协议价", dataType = "Decimal")
    @NotNull(message = "不含税协议价不能为空")
    @DecimalMin(value = "0.01", message = "不含税协议价超出范围")
    @DecimalMax(value = "99999999.99", message = "不含税协议价超出范围")
    private BigDecimal goodsPactNakedPrice;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率，整数表示，多少个百分点", dataType = "Integer")
    @Positive
    @Max(value = 50, message = "税率超过了50%")
    @NotNull(message = "税率不能为空")
    private Integer taxRate;

    /**
     * 税收分类编码
     */
    @ApiModelProperty(value = "税收分类编码", dataType = "String")
    @Length(max = 512, message = "销售单位过长（>512）")
    private String taxCode;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位", dataType = "String")
    @NotBlank(message = "销售单位不能为空")
    @Length(max = 10, message = "销售单位过长")
    private String saleUnit;

    /**
     * 起订量
     */
    @ApiModelProperty(value = "起订量", dataType = "Integer")
    @Positive
    @Max(value = 99999999, message = "库存数量不能超过99999999")
    @NotNull(message = "起订量不能为空")
    private Integer goodsMoq;

    /**
     * 产地
     */
    @TableField(value = "production_place")
    @ApiModelProperty(value = "产地")
    @Length(max = 64, message = "产地信息过长")
    private String productionPlace;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量", dataType = "Integer")
    @Positive
    @Max(value = 99999999, message = "库存数量不能超过99999999")
    @NotNull(message = "库存数量不能为空")
    private Integer stockAvailable;

    /**
     * deliveryTime
     */
    @ApiModelProperty(value = "发货天数", example = "7", dataType = "Integer")
    @Positive
    @Max(value = 180, message = "发货时间不能超过180天")
    @NotNull(message = "发货时间不能为空")
    private Integer deliveryTime;


    /**
     * 略缩图信息
     */
    @ApiModelProperty(value = "略缩图信息,用多图分割的url地址,第一张为主图", example = "http://xx.com/image1,http://xx.com/image2", dataType = "String")
    @NotBlank(message = "略缩图不能为空")
    private String imageArray;

    /**
     * 商品详情
     */
    @ApiModelProperty(value = "商品详情", dataType = "String")
    @NotBlank(message = "商品详情不能为空")
    private String goodsBody;

    /**
     * 商品分类(三级分类请逗号分隔)
     */
    @ApiModelProperty(value = "三级分类", dataType = "String")
    @NotBlank(message = "商品分类不能为空")
    @Length(max = 40, message = "商品分类长度不能超过40位")
    private String goodsClass;

    @ApiModelProperty(value = "供应商三级分类", dataType = "String")
    private String supplierClass;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商TYPE：1、云中鹤  2、兑吧  3...", dataType = "String")
    @NotBlank(message = "供应商TYPE不能为空")
    private String supplierType;

    /**
     * 供应商侧商品详情页地址
     */
    @ApiModelProperty(value = "供应商侧商品详情页地址，若能提供使用sku进入详情的统一地址，该项可不传", example = "https://b2b.nbdeli.com/pcweb/detail?sku=100079845", dataType = "String")
    @Length(max = 500, message = "供应商侧商品详情页地址过长")
    private String supplierDetailPath;

    // 系统用
    @ApiModelProperty(hidden = true)
    private Long goodsId;

    // 系统用
    @ApiModelProperty(hidden = true)
    private String supplierCode;

    @ApiModelProperty(value = "销售客户端 0:全端 1:B端 2:C端")
    private Integer saleClient;
}
