<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.settlement.common.mapper.SettleShopBillDetailMapper">

    <resultMap id="SettleShopBillDetailExcelVoMap"
               type="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailExcelVo">
        <result column="create_time" property="createTime"/>
        <result column="order_number" property="orderNumber"/>
        <result column="purchase_number" property="purchaseNumber"/>
        <result column="supplier_order_number" property="supplierOrderNumber"/>
        <result column="goods_sku" property="goodsSku"/>
        <result column="store_name" property="storeName"/>
        <result column="apply_time" property="applyTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="apply_user_name" property="applyUserName"/>
        <result column="apply_dept_name" property="applyDeptName"/>
        <result column="goods_Desc" property="goodsDesc"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="sale_unit" property="saleUnit"/>
        <result column="materials_code" property="materialsCode"/>
        <result column="confirm_num" property="confirmNum"/>
        <result column="checked_num" property="checkedNum"/>
        <result column="unit_price_naked" property="unitPriceNaked"/>
        <result column="unit_price_tax" property="unitPriceTax"/>
        <result column="total_price_naked" property="totalPriceNaked"/>
        <result column="total_price_tax" property="totalPriceTax"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="tax_code" property="taxCode"/>
        <result column="address_name" property="addressName"/>
        <result column="mob_phone" property="mobPhone"/>
        <result column="address" property="address"/>
        <result column="sup_receiving_time" property="supReceivingTime"/>
        <result column="cus_receiving_time" property="cusReceivingTime"/>
        <result column="order_detail_state" property="orderDetailState"/>
        <result column="delivery_code" property="deliveryCode"/>
        <result column="invoice_subject" property="invoiceSubject"/>
        <result column="reconciliation_user_name" property="reconciliationUserName"/>
        <result column="reconciliation_status" property="reconciliationStatus"/>
        <result column="remark" property="remark"/>
        <result column="transformers_district_name" property="transformersDistrictName"/>
        <result column="integral_name" property="integralName"/>
        <result column="goods_pay_integral" property="goodsPayIntegral"/>
        <result column="goods_pay_money" property="goodsPayMoney"/>
        <result column="is_pre_pay" property="isPrePay"/>
        <result column="apply_emp_code" property="applyEmpCode"/>
    </resultMap>

    <select id="querySettleShopBillDetailPage"
            resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailVo">
        select
        ssbd.detail_id,
        ssbd.create_time,
        ssbd.check_year,
        ssbd.check_month,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        sbp.goods_Desc,
        ssbd.goods_code,
        ssbd.goods_sku,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        sbp.budget_type_name,
        sbp.budget_number,
        sbp.invoice_subject,
        sbp.invoice_type_name,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.apply_time,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.delivery_code,
        sbp.cus_receiving_time,
        sbp.remark,
        sbp.goods_image,
        ssb.bill_sn,
        ssbd.store_code,
        ssbd.store_name,
        sbp.sale_unit,
        sbp.materials_code,
        ssb.customer_name,
        ssb.customer_code,
        ssbd.reconciliation_confirm_user_name,
        ssbd.reconciliation_confirm_time,
        ssbd.reconciliation_status,
        ssbd.reconciliation_user_name,
        sbp.order_detail_state,
        ssbd.reconciliation_remark,
        sbp.other_relation_number,
        sbp.is_pre_pay,
        sbp.create_time as poolTime
        from
        settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool sbp on ssbd.settle_bill_pool_id = sbp.id
        where
        1=1
        <if test="query.orderNumber !=null and query.orderNumber !=''">
            and ssbd.order_number =#{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and ssbd.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber !=''">
            and ssbd.supplier_order_number =#{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode !=''">
            and ssbd.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku !=''">
            and ssbd.goods_Sku =#{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject !=''">
            and ssbd.invoice_subject =#{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName !=''">
            and ssbd.apply_dept_name =#{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName !=''">
            and ssbd.apply_user_name =#{query.applyUserName}
        </if>
        <if test="query.applyDeptId != null">
            and ssbd.apply_dept_id =#{query.applyDeptId}
        </if>
        <if test="query.applyUserId !=null">
            and ssbd.apply_user_id =#{query.applyUserId}
        </if>
        <if test="query.billId != null">
            and ssb.bill_id =#{query.billId}
        </if>
        <if test="query.billSn != null and query.billSn !=''">
            and ssb.bill_sn =#{query.billSn}
        </if>
        <if test="query.reconciliationStatus !=null">
            and ssbd.reconciliation_status =#{query.reconciliationStatus}
        </if>
        <if test="query.reconciliationUserId != null and query.reconciliationUserId !=''">
            and ssbd.reconciliation_user_id =#{query.reconciliationUserId}
        </if>
        <if test="query.reconciliationUserName != null and query.reconciliationUserName !=''">
            and ssbd.reconciliation_user_name =#{query.reconciliationUserName}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
        <if test="query.checkYear != null">
            and ssb.check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth != null">
            and ssb.check_month =#{query.checkMonth}
        </if>
        <if test="query.checkMonthList !=null and query.checkMonthList.size() !=0 ">
            and ssb.check_month in
            <foreach collection="query.checkMonthList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.billCustomerType !=null">
            and ssb.customer_type =#{query.billCustomerType}
        </if>
        <if test="query.customerCode !=null and query.customerCode !=''">
            and ssb.customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation !=null">
            and ssb.is_platform_reconciliation =#{query.isPlatformReconciliation}
        </if>
        <if test="query.reconciliationStatusList != null and query.reconciliationStatusList.size()>0">
            and ssbd.reconciliation_status in
            <foreach collection="query.reconciliationStatusList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.addressName != null and query.addressName !=''">
            and sbp.address_name =#{query.addressName}
        </if>
        <if test="query.billOutTimeStart !=null and query.billOutTimeStart !=''">
            and ssbd.create_time &gt;=#{query.billOutTimeStart}
        </if>
        <if test="query.billOutTimeEnd !=null and query.billOutTimeEnd !=''">
            and ssbd.create_time &lt;=#{query.billOutTimeEnd}
        </if>
        <if test="query.customerSourceType !=null">
          <if test="query.customerSourceType ==4">
              and ssb.customer_source_type =#{query.customerSourceType}
          </if>
          <if test="query.customerSourceType==1">
              and ssb.customer_source_type in (1,3)
          </if>
        </if>
        <if test="query.billDetailType !=null">
            and ssbd.bill_detail_type =#{query.billDetailType}
        </if>
        <if test="query.billStagingFlag !=null">
            and ssbd.bill_staging_flag =#{query.billStagingFlag}
        </if>
        <if test="query.taxRate !=null">
            and sbp.tax_rate =#{query.taxRate}
        </if>
        and ssbd.is_enable = 1 and ssbd.pool_type =1
        order by ssbd.detail_id desc
    </select>

    <select id="querySettleShopBillDetailYflPage"
            resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailVo">
        select
        ssbd.detail_id,
        ssbd.create_time,
        ssbd.check_year,
        ssbd.check_month,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        ssbd.store_code,
        ssbd.store_name,
        ssbd.goods_code,
        ssbd.goods_sku,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        ssbd.reconciliation_confirm_user_name,
        ssbd.reconciliation_confirm_time,
        ssbd.reconciliation_status,
        ssbd.reconciliation_user_name,
        sbp.goods_desc,
        sbp.invoice_subject,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.apply_time,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.remark,
        sbp.order_detail_state,
        sbp.sale_unit,
        sbp.materials_code,
        ssb.customer_name,
        ssb.customer_code,
        ssb.bill_sn
        from
        settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool_yfl_customer sbp on ssbd.settle_bill_pool_id = sbp.bill_pool_yfl_id
        where
        1=1
        <if test="query.orderNumber !=null and query.orderNumber !=''">
            and ssbd.order_number =#{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and ssbd.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber !=''">
            and ssbd.supplier_order_number =#{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode !=''">
            and ssbd.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku !=''">
            and ssbd.goods_sku =#{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject !=''">
            and ssbd.invoice_subject =#{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName !=''">
            and ssbd.apply_dept_name =#{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName !=''">
            and ssbd.apply_user_name =#{query.applyUserName}
        </if>
        <if test="query.applyDeptId != null">
            and ssbd.apply_dept_id =#{query.applyDeptId}
        </if>
        <if test="query.applyUserId !=null">
            and ssbd.apply_user_id =#{query.applyUserId}
        </if>
        <if test="query.billId != null">
            and ssb.bill_id =#{query.billId}
        </if>
        <if test="query.billSn != null and query.billSn !=''">
            and ssb.bill_sn =#{query.billSn}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
        <if test="query.billCustomerType !=null">
            and ssb.customer_type =#{query.billCustomerType}
        </if>
        <if test="query.customerCode !=null and query.customerCode !=''">
            and ssb.customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation !=null">
            and ssb.is_platform_reconciliation =#{query.isPlatformReconciliation}
        </if>
        <if test="query.reconciliationStatusList != null and query.reconciliationStatusList.size()>0">
            and ssbd.reconciliation_status in
            <foreach collection="query.reconciliationStatusList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.reconciliationStatus !=null">
            and ssbd.reconciliation_status =#{query.reconciliationStatus}
        </if>
        <if test="query.checkYear !=null">
            and ssb.check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth !=null">
            and ssb.check_month =#{query.checkMonth}
        </if>
        <if test="query.checkMonthList !=null and query.checkMonthList.size() !=0 ">
            and ssb.check_month in
            <foreach collection="query.checkMonthList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.billOutTimeStart !=null and query.billOutTimeStart !=''">
            and ssbd.create_time &gt;=#{query.billOutTimeStart}
        </if>
        <if test="query.billOutTimeEnd !=null and query.billOutTimeEnd !=''">
            and ssbd.create_time &lt;=#{query.billOutTimeEnd}
        </if>
        <if test="query.billDetailType !=null">
            and ssbd.bill_detail_type =#{query.billDetailType}
        </if>
        and ssbd.is_enable =1 and ssbd.pool_type =2
        order by ssbd.detail_id desc
    </select>

    <select id="querySettleShopBillDetailYfl"
            resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailVo">
        select
        ssbd.detail_id,
        ssbd.create_time,
        ssbd.check_year,
        ssbd.check_month,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        ssbd.store_code,
        ssbd.store_name,
        ssbd.goods_code,
        ssbd.goods_sku,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        ssbd.reconciliation_confirm_user_name,
        ssbd.reconciliation_confirm_time,
        ssbd.reconciliation_status,
        sbp.goods_desc,
        sbp.invoice_subject,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.apply_time,
        sbp.audit_time,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.remark,
        sbp.order_detail_state,
        sbp.sale_unit,
        sbp.materials_code,
        ssb.customer_name,
        ssb.customer_code,
        ssb.bill_sn,
        group_concat(ib.invoice_apply_number,':',IFNULL(ib.msdp_invoice_time,'开票中') separator '|') as "invoiceInfo"
        from
        settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool_yfl_customer sbp on ssbd.settle_bill_pool_id = sbp.bill_pool_yfl_id
        left join invoice_detail_bill idb on ssbd.detail_id = idb.bill_detail_id
        left join invoice_bill ib on ib.id = idb.invoice_id and ib.company_type =2 and ib.state !=5
        where
        ssbd.pool_type =2 and ssbd.is_enable =1
        <if test="query.orderNumber !=null and query.orderNumber !=''">
            and ssbd.order_number =#{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and ssbd.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber !=''">
            and ssbd.supplier_order_number =#{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode !=''">
            and ssbd.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku !=''">
            and ssbd.goods_sku =#{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject !=''">
            and ssbd.invoice_subject =#{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName !=''">
            and ssbd.apply_dept_name =#{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName !=''">
            and ssbd.apply_user_name =#{query.applyUserName}
        </if>
        <if test="query.applyDeptId != null">
            and ssbd.apply_dept_id =#{query.applyDeptId}
        </if>
        <if test="query.applyUserId !=null">
            and ssbd.apply_user_id =#{query.applyUserId}
        </if>
        <if test="query.billId != null">
            and ssb.bill_id =#{query.billId}
        </if>
        <if test="query.billSn != null and query.billSn !=''">
            and ssb.bill_sn =#{query.billSn}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
        <if test="query.billCustomerType !=null">
            and ssb.customer_type =#{query.billCustomerType}
        </if>
        <if test="query.customerCode !=null and query.customerCode !=''">
            and ssb.customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation !=null">
            and ssb.is_platform_reconciliation =#{query.isPlatformReconciliation}
        </if>
        <if test="query.reconciliationStatusList != null and query.reconciliationStatusList.size()>0">
            and ssbd.reconciliation_status in
            <foreach collection="query.reconciliationStatusList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.checkYear !=null">
            and ssb.check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth !=null">
            and ssb.check_month =#{query.checkMonth}
        </if>
        <if test="query.checkMonthList !=null and query.checkMonthList.size() !=0 ">
            and ssb.check_month in
            <foreach collection="query.checkMonthList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.billOutTimeStart !=null and query.billOutTimeStart !=''">
            and ssb.create_time &gt;=#{query.billOutTimeStart}
        </if>
        <if test="query.billOutTimeEnd !=null and query.billOutTimeEnd !=''">
            and ssbd.create_time &lt;=#{query.billOutTimeEnd}
        </if>
        group by ssbd.detail_id
    </select>

    <select id="querySettleShopBillDetail" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailVo">
        select
        ssbd.detail_id,
        ssbd.create_time,
        ssbd.check_year,
        ssbd.check_month,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        sbp.goods_Desc,
        ssbd.goods_code,
        ssbd.goods_sku,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.invoicable_quantity,
        ssbd.invoiced_quantity,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        sbp.budget_type_name,
        sbp.budget_number,
        sbp.invoice_subject,
        sbp.invoice_type_name,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.apply_time,
        sbp.audit_time,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.delivery_code,
        sbp.cus_receiving_time,
        sbp.remark,
        sbp.goods_image,
        ssb.bill_sn,
        ssbd.store_code,
        ssbd.store_name,
        sbp.sale_unit,
        sbp.materials_code,
        ssb.customer_name,
        ssb.customer_code,
        ssbd.reconciliation_confirm_user_name,
        ssbd.reconciliation_confirm_time,
        ssbd.reconciliation_status,
        ssbd.reconciliation_user_name,
        sbp.order_detail_state,
        sbp.other_relation_number,
        sbp.is_pre_pay,
        sbp.create_time as poolTime,
        group_concat(ib.invoice_apply_number,':',IFNULL(ib.msdp_invoice_time,'开票中') separator '|') as "invoiceInfo"
        from
        settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool sbp on ssbd.settle_bill_pool_id = sbp.id
        left join invoice_detail_bill idb on idb.bill_detail_id = ssbd.detail_id
        left join invoice_bill ib on ib.id = idb.invoice_id and ib.state !=5 and ib.company_type !=2
        where
        ssbd.pool_type =1 and ssbd.is_enable =1
        <if test="query.orderNumber != null and query.orderNumber !=''">
            and ssbd.order_number =#{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and ssbd.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber !=''">
            and ssbd.supplier_order_number =#{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode !=''">
            and ssbd.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku !=''">
            and ssbd.goods_sku =#{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject !=''">
            and ssbd.invoice_subject =#{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName !=''">
            and ssbd.apply_dept_name =#{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName !=''">
            and ssbd.apply_user_name =#{query.applyUserName}
        </if>
        <if test="query.billId != null">
            and ssb.bill_id =#{query.billId}
        </if>
        <if test="query.billSn != null and query.billSn !=''">
            and ssb.bill_sn =#{query.billSn}
        </if>
        <if test="query.reconciliationUserId != null and query.reconciliationUserId !=''">
            and ssbd.reconciliation_user_id =#{query.reconciliationUserId}
        </if>
        <if test="query.reconciliationStatus !=null">
            and ssbd.reconciliation_status =#{query.reconciliationStatus}
        </if>
        <if test="query.reconciliationUserName != null and query.reconciliationUserName !=''">
            and ssbd.reconciliation_user_name =#{query.reconciliationUserName}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
        <if test="query.checkYear != null">
            and ssb.check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth != null">
            and ssb.check_month =#{query.checkMonth}
        </if>
        <if test="query.checkMonthList !=null and query.checkMonthList.size() !=0 ">
            and ssb.check_month in
            <foreach collection="query.checkMonthList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.billCustomerType !=null">
            and ssb.customer_type =#{query.billCustomerType}
        </if>
        <if test="query.customerCode !=null and query.customerCode !=''">
            and ssb.customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation !=null">
            and ssb.is_platform_reconciliation =#{query.isPlatformReconciliation}
        </if>
        <if test="query.reconciliationStatusList != null and query.reconciliationStatusList.size()>0">
            and ssbd.reconciliation_status in
            <foreach collection="query.reconciliationStatusList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.addressName !=null and query.addressName !=''">
            and sbp.address_name =#{query.addressName}
        </if>
        <if test="query.billOutTimeStart !=null and query.billOutTimeStart !=''">
            and ssbd.create_time &gt;=#{query.billOutTimeStart}
        </if>
        <if test="query.billOutTimeEnd !=null and query.billOutTimeEnd !=''">
            and ssbd.create_time &lt;=#{query.billOutTimeEnd}
        </if>
        <if test="query.customerSourceType !=null">
            <if test="query.customerSourceType ==4">
                and ssb.customer_source_type =#{query.customerSourceType}
            </if>
            <if test="query.customerSourceType==1">
                and ssb.customer_source_type in (1,3)
            </if>
        </if>
        group by ssbd.detail_id
    </select>

    <select id="querySettleShopBillDetailFront" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailExcelFrontVo">
        select
        ssbd.detail_id,
        ssbd.create_time,
        ssbd.check_year,
        ssbd.check_month,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        sbp.goods_Desc,
        ssbd.goods_code,
        ssbd.goods_sku,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.invoicable_quantity,
        ssbd.invoiced_quantity,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        sbp.budget_type_name,
        sbp.budget_number,
        sbp.invoice_subject,
        sbp.invoice_type_name,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.apply_time,
        sbp.audit_time,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.delivery_code,
        sbp.cus_receiving_time,
        sbp.remark,
        sbp.goods_image,
        ssb.bill_sn,
        ssbd.store_code,
        ssbd.store_name,
        sbp.sale_unit,
        sbp.materials_code,
        ssb.customer_name,
        ssb.customer_code,
        ssbd.reconciliation_confirm_user_name,
        ssbd.reconciliation_confirm_time,
        ssbd.reconciliation_status,
        ssbd.reconciliation_user_name,
        sbp.order_detail_state,
        sbp.other_relation_number,
        sbp.is_pre_pay,
        sbp.create_time as poolTime
        from
        settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool sbp on ssbd.settle_bill_pool_id = sbp.id
        where
        ssbd.pool_type =1 and ssbd.is_enable =1
        <if test="query.orderNumber != null and query.orderNumber !=''">
            and ssbd.order_number =#{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and ssbd.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber !=''">
            and ssbd.supplier_order_number =#{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode !=''">
            and ssbd.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku !=''">
            and ssbd.goods_sku =#{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject !=''">
            and ssbd.invoice_subject =#{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName !=''">
            and ssbd.apply_dept_name =#{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName !=''">
            and ssbd.apply_user_name =#{query.applyUserName}
        </if>
        <if test="query.billId != null">
            and ssb.bill_id =#{query.billId}
        </if>
        <if test="query.billSn != null and query.billSn !=''">
            and ssb.bill_sn =#{query.billSn}
        </if>
        <if test="query.reconciliationUserId != null and query.reconciliationUserId !=''">
            and ssbd.reconciliation_user_id =#{query.reconciliationUserId}
        </if>
        <if test="query.reconciliationStatus !=null">
            and ssbd.reconciliation_status =#{query.reconciliationStatus}
        </if>
        <if test="query.reconciliationUserName != null and query.reconciliationUserName !=''">
            and ssbd.reconciliation_user_name =#{query.reconciliationUserName}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
        <if test="query.checkYear != null">
            and ssb.check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth != null">
            and ssb.check_month =#{query.checkMonth}
        </if>
        <if test="query.billCustomerType !=null">
            and ssb.customer_type =#{query.billCustomerType}
        </if>
        <if test="query.customerCode !=null and query.customerCode !=''">
            and ssb.customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation !=null">
            and ssb.is_platform_reconciliation =#{query.isPlatformReconciliation}
        </if>
        <if test="query.reconciliationStatusList != null and query.reconciliationStatusList.size()>0">
            and ssbd.reconciliation_status in
            <foreach collection="query.reconciliationStatusList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.addressName !=null and query.addressName !=''">
            and sbp.address_name =#{query.addressName}
        </if>
    </select>

    <select id="getBillDetailExecl" resultMap="SettleShopBillDetailExcelVoMap">
        select (@i:=@i+1) as indexNum,
        ssbd.create_time,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        sbp.other_relation_number,
        ssbd.goods_sku,
        ssbd.store_name,
        sbp.apply_time,
        sbp.audit_time,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        sbp.goods_Desc,
        ssbd.goods_code,
        sbp.sale_unit,
        sbp.materials_code,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.sup_receiving_time,
        sbp.cus_receiving_time,
        sbp.order_detail_state,
        sbp.delivery_code,
        ssbd.invoice_subject,
        ssbd.reconciliation_user_name,
        ssbd.reconciliation_status,
        sbp.remark,
        sbp.is_pre_pay
        from settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool sbp on ssbd.settle_bill_pool_id = sbp.id,
        (select @i:=0) as itable
        where ssbd.is_enable = 1 and ssbd.bill_id =#{query.billId} and ssbd.pool_type =1
        <if test="query.orderNumber != null and query.orderNumber != ''">
            and ssbd.order_number = #{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber != ''">
            and ssbd.purchase_number = #{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber != ''">
            and ssbd.supplier_order_number = #{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode != ''">
            and ssbd.goods_code = #{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku != ''">
            and ssbd.goods_sku = #{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject != ''">
            and ssbd.invoice_subject = #{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName != ''">
            and ssbd.apply_dept_name = #{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName != ''">
            and ssbd.apply_user_name = #{query.applyUserName}
        </if>
        <if test="query.reconciliationUserId != null and query.reconciliationUserId !=''">
            and ssbd.reconciliation_user_id =#{query.reconciliationUserId}
        </if>
        <if test="query.reconciliationStatus !=null">
            and ssbd.reconciliation_status =#{query.reconciliationStatus}
        </if>
        <if test="query.reconciliationUserName != null and query.reconciliationUserName !=''">
            and ssbd.reconciliation_user_name =#{query.reconciliationUserName}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
        <if test="query.taxRate !=null">
            and sbp.tax_rate =#{query.taxRate}
        </if>
        <if test="query.billStagingFlag !=null">
            and ssbd.bill_staging_flag =#{query.billStagingFlag}
        </if>

    </select>

    <select id="getBillDetailYflExcel" resultMap="SettleShopBillDetailExcelVoMap">
        select
        (@i:=@i+1) as indexNum,
        ssbd.create_time,
        ssbd.order_number,
        ssbd.purchase_number,
        ssbd.supplier_order_number,
        ssbd.goods_sku,
        ssbd.invoice_subject,
        ssbd.apply_user_name,
        ssbd.apply_dept_name,
        ssbd.goods_code,
        ssbd.confirm_num,
        ssbd.checked_num,
        ssbd.unit_price_naked,
        ssbd.unit_price_tax,
        ssbd.total_price_naked,
        ssbd.total_price_tax,
        ssbd.store_name,
        sbp.goods_desc,
        sbp.sale_unit,
        sbp.materials_code,
        sbp.tax_rate,
        sbp.tax_code,
        sbp.address_name,
        sbp.mob_phone,
        sbp.address,
        sbp.order_detail_state,
        sbp.apply_time,
        sbp.audit_time,
        sbp.integral_name,
        sbp.goods_pay_integral,
        sbp.goods_pay_money,
        sbp.apply_emp_code
        from settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        left join settle_bill_pool_yfl_customer sbp on ssbd.settle_bill_pool_id = sbp.bill_pool_yfl_id,
        (select @i:=0) as itable
        where ssbd.is_enable = 1 and ssbd.bill_id =#{query.billId} and ssbd.pool_type =2
        <if test="query.orderNumber != null and query.orderNumber != ''">
            and ssbd.order_number = #{query.orderNumber}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber != ''">
            and ssbd.purchase_number = #{query.purchaseNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber != ''">
            and ssbd.supplier_order_number = #{query.supplierOrderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode != ''">
            and ssbd.goods_code = #{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku != ''">
            and ssbd.goods_sku = #{query.goodsSku}
        </if>
        <if test="query.invoiceSubject != null and query.invoiceSubject != ''">
            and ssbd.invoice_subject = #{query.invoiceSubject}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName != ''">
            and ssbd.apply_dept_name = #{query.applyDeptName}
        </if>
        <if test="query.applyUserName != null and query.applyUserName != ''">
            and ssbd.apply_user_name = #{query.applyUserName}
        </if>
        <if test="query.reconciliationUserId != null and query.reconciliationUserId !=''">
            and ssbd.reconciliation_user_id =#{query.reconciliationUserId}
        </if>
        <if test="query.reconciliationStatus !=null">
            and ssbd.reconciliation_status =#{query.reconciliationStatus}
        </if>
        <if test="query.reconciliationUserName != null and query.reconciliationUserName !=''">
            and ssbd.reconciliation_user_name =#{query.reconciliationUserName}
        </if>
        <if test="query.storeDataSource != null and query.storeDataSource !=''">
            and ssbd.store_data_source =#{query.storeDataSource}
        </if>
    </select>

    <select id="getConditionData" resultType="com.ly.yph.api.settlement.common.vo.bill.BillConditionDataVo">
        select
        distinct
        <if test="billConditionName != null and billConditionName !=''">
            <if test="billConditionName =='invoice_subject'">
                invoice_subject as name
            </if>
            <if test="billConditionName =='apply_user_name'">
                apply_user_id as code,
                apply_user_name as name
            </if>
            <if test="billConditionName =='apply_dept_name'">
                apply_dept_id as code,
                apply_dept_name as name
            </if>
        </if>
        from settle_shop_bill_detail
        where bill_id =#{billId} and is_enable=1
        <if test="billConditionName != null and billConditionName !=''">
            <if test="billConditionName =='invoice_subject'">
                group by invoice_subject
            </if>
            <if test="billConditionName =='apply_user_name'">
                group by apply_user_id
            </if>
            <if test="billConditionName =='apply_dept_name'">
                group by apply_dept_id
            </if>
        </if>
    </select>

    <select id="getReconciliationConditionData"
            resultType="com.ly.yph.api.settlement.common.vo.bill.BillConditionDataVo">
        select
        distinct
        <if test="billConditionName != null and billConditionName !=''">
            <if test="billConditionName =='invoice_subject'">
                invoice_subject as name
            </if>
            <if test="billConditionName =='apply_user_name'">
                apply_user_id as code,
                apply_user_name as name
            </if>
            <if test="billConditionName =='apply_dept_name'">
                apply_dept_id as code,
                apply_dept_name as name
            </if>
        </if>
        from
        settle_shop_bill_detail
        where is_enable = 1
        and reconciliation_user_id =#{reconciliationUserId}
        <if test="billConditionName != null and billConditionName !=''">
            <if test="billConditionName =='invoice_subject'">
                group by invoice_subject
            </if>
            <if test="billConditionName =='apply_user_name'">
                group by apply_user_id
            </if>
            <if test="billConditionName =='apply_dept_name'">
                group by apply_dept_id
            </if>
        </if>
    </select>

    <select id="getSumIntegral" resultType="java.util.Map">
        select sum(sbpyc.goods_pay_integral) as goodsPayIntegral,
               sum(sbpyc.goods_pay_money)    as goodsPayMoney
        from settle_shop_bill_detail ssbd
                 left join settle_bill_pool_yfl_customer sbpyc
                           on ssbd.settle_Bill_Pool_Id = sbpyc.bill_pool_yfl_id
        where ssbd.bill_id = #{billId}
          and ssbd.pool_type = 2
          and ssbd.is_enable = 1
          and sbpyc.is_enable = 1
    </select>

    <select id="getBillInvoiceDetailDtos" resultType="com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto">
        select
        a.detail_id,
        a.reconciliation_status,
        a.bill_id,
        a.bill_sn,
        a.store_code,
        a.checked_num,
        a.invoicable_quantity,
        a.invoiced_quantity,
        a.unit_price_naked,
        a.unit_Price_tax,
        b.tax_rate,
        b.id as 'settlePoolId',
        c.id as 'lifeCycleId',
        c.customer_invoice_money as 'lifeInvoiceMoney',
        c.customer_invoicing_num as 'lifeInvoiceNum',
        a.contract_number,
        a.price_mode,
        a.reconciliation_user_id,
        b.company_code,
        b.company_name,
        b.order_detail_id,
        a.bill_detail_type
        from
        settle_shop_bill_detail a
        left join settle_bill_pool b on b.id = a.settle_bill_Pool_Id and a.is_enable = 1
        left join settle_bill_life_cycle c on c.settle_bill_Pool_Id = a.settle_bill_Pool_Id and c.pool_type =1
        where a.detail_id in
        <foreach collection="query" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getBillInvoiceDetailDtoListForYfl"
            resultType="com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto">
        select
        a.detail_id,
        a.pool_type,
        a.reconciliation_status,
        a.bill_id,
        a.bill_sn,
        a.store_code,
        a.checked_num,
        a.invoicable_quantity,
        a.invoiced_quantity,
        a.reconciliation_user_id,
        ROUND(b.goods_pay_integral/a.checked_num/(1+b.tax_rate/100),4) as unit_price_naked,
        ROUND(b.goods_pay_integral/a.checked_num,2) as unit_Price_tax,
        b.tax_rate,
        b.goods_pay_integral as totalPriceTax,
        b.company_code,
        b.company_name,
        b.activity_id,
        a.order_number,
        a.goods_sku,
        b.tax_code,
        b.order_detail_id,
        a.bill_detail_type
        from
        settle_shop_bill_detail a
        left join settle_bill_pool_yfl_customer b on a.settle_bill_pool_id = b.bill_pool_yfl_id
        where a.is_enable =1 and a.detail_id in
        <foreach collection="query" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getInvoiceRejectBillDetail" resultType="com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto">
        select b.detail_id,
               b.reconciliation_status,
               b.store_code,
               b.bill_id,
               b.bill_sn,
               b.checked_num,
               b.invoicable_quantity,
               b.invoiced_quantity,
               b.unit_price_naked,
               b.unit_Price_tax,
               a.invoice_num,
               e.id as 'settlePoolId',
               e.tax_rate,
               c.id as 'lifeCycleId',
               c.customer_invoice_money as 'lifeInvoiceMoney',
               c.customer_invoicing_num as 'lifeInvoiceNum',
               e.order_detail_id,
               a.goods_total_Price_tax
        from invoice_detail_bill a
                 left join settle_shop_bill_detail b on a.bill_detail_id = b.detail_id and b.pool_type = 1
                 left join settle_bill_pool e on b.settle_bill_pool_id = e.id
                 left join settle_bill_life_cycle c on b.settle_bill_pool_id = c.settle_bill_pool_id
        where a.invoice_id = #{invoiceId}
    </select>

    <select id="getInvoiceDetailForApproveYfl"
            resultType="com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto">
        select b.detail_id,
               b.reconciliation_status,
               b.checked_num,
               b.invoicable_quantity,
               b.invoiced_quantity,
               a.invoice_num,
               a.goods_total_price_tax,
               c.order_detail_id
        from invoice_detail_bill a
                 left join settle_shop_bill_detail b on a.bill_detail_id = b.detail_id and b.pool_type = 2
                 left join settle_bill_pool_yfl_customer c on c.bill_pool_yfl_id = b.settle_bill_pool_id
        where a.invoice_id = #{invoiceId}
    </select>

    <update id="updateSettleBillDetails">
        <foreach collection="list" item="item" separator=";">
            update settle_shop_bill_detail
            <set>
                <if test="item.reconciliationStatus != null">
                    reconciliation_status =#{item.reconciliationStatus},
                </if>
                <if test="item.reconciliationUserId != null">
                    reconciliation_user_id=#{item.reconciliationUserId},
                </if>
                <if test="item.reconciliationUserName != null and item.reconciliationUserName !=''">
                    reconciliation_user_name=#{item.reconciliationUserName},
                </if>
                <if test="item.reconciliationConfirmUserId != null">
                    reconciliation_confirm_user_id=#{item.reconciliationConfirmUserId},
                </if>
                <if test="item.reconciliationConfirmUserName != null ">
                    reconciliation_confirm_user_name=#{item.reconciliationConfirmUserName},
                    reconciliation_confirm_time=#{item.reconciliationConfirmTime},
                </if>
                <if test="item.modifier != null and item.modifier !=''">
                    modifier=#{item.modifier},
                </if>
                <if test="item.updateTime != null and item.updateTime">
                    update_time=#{item.updateTime},
                </if>
                <if test="item.reconciliationRemark != null and item.reconciliationRemark !=''">
                    reconciliation_remark=#{item.reconciliationRemark},
                </if>
                <if test="item.invoicableQuantity != null">
                    invoicable_quantity =#{item.invoicableQuantity},
                </if>
            </set>
            where detail_id =#{item.detailId}
        </foreach>
    </update>

    <select id="queryDetailMatchSapData" resultType="com.ly.yph.api.settlement.common.dto.bill.DetailMatchSapDataDto">
        select ssbd.detail_id,
               ssbd.purchase_number,
               ssbd.goods_code,
               ssbd.checked_num
        from settle_shop_bill_detail ssbd
                 left join settle_bill_pool sbp on ssbd.settle_bill_pool_id = sbp.id
        where ssbd.is_enable = 1
          and ssbd.bill_id =#{billId}
          and ssbd.pool_type = 1
          and sbp.sap_order_type in (1, 2)
        </select>

    <update id="updateMatchSapData">
        <foreach collection="list" item="item" separator=";">
            update settle_shop_bill_detail
            set bill_match_reason =#{item.reason},bill_match_flag=#{item.flag}
            where detail_id =#{item.detailId}
        </foreach>
    </update>

    <select id="queryReconciliationPdfData"
            resultType="com.ly.yph.api.settlement.common.vo.bill.ReconciliationBillDetailPdfVo">
        select
        a.detail_id,
        a.check_year,
        a.check_month,
        a.purchase_number,
        a.order_number,
        a.apply_user_name,
        b.goods_Desc,
        a.goods_code,
        a.goods_sku,
        a.confirm_num,
        a.checked_num,
        a.unit_price_naked,
        a.unit_price_tax,
        a.total_price_naked,
        a.total_price_tax,
        b.address_name,
        b.delivery_code,
        b.remark
        from
            settle_shop_bill_detail a
        left join settle_bill_pool b on a.settle_bill_pool_id = b.id and a.pool_type = 1
        where a.is_enable = 1 and a.reconciliation_user_id = #{query.reconciliationUserId}
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and a.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.orderNumber != null and query.orderNumber != ''">
            and a.order_number = #{query.orderNumber}
        </if>
        <if test="query.applyUserName != null and query.applyUserName != ''">
            and a.apply_user_name = #{query.applyUserName}
        </if>
        <if test="query.applyDeptName != null and query.applyDeptName != ''">
            and a.apply_dept_name = #{query.applyDeptName}
        </if>
        <if test="query.checkYear != null">
            and a.check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth != null">
            and a.check_month =#{query.checkMonth}
        </if>
        <if test="query.reconciliationStatus !=null">
            and a.reconciliation_status =#{query.reconciliationStatus}
        </if>
    </select>

    <select id="getOutBillDetailByPoolIds"
            resultType="com.ly.yph.api.settlement.common.dto.outChecked.BillDetailOutCheckedRelationDto">
        select
        ssbd.detail_id,
        ssbd.settle_bill_pool_id,
        ssb.customer_code as companyCode
        from
        settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id =ssb.bill_id
        where ssbd.is_enable =1 and ssb.customer_type = 0
        and ssbd.pool_type = 1 and ssbd.settle_bill_pool_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getSettleShopBillDetailMatchOutDataByDetailIds"
            resultType="com.ly.yph.api.settlement.common.dto.outChecked.BillDetailMatchDto">
        select
        ssbd.detail_id,
        ssbd.order_number,
        ssbd.goods_code,
        ssbd.checked_num,
        ssb.customer_code as companyCode,
        sbp.order_detail_id
        from settle_shop_bill_detail ssbd
        left join settle_shop_bill ssb on ssbd.bill_id =ssb.bill_id
        left join settle_bill_pool sbp on sbp.id = ssbd.settle_bill_pool_id
        where ssbd.is_enable = 1 and ssbd.detail_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getBillOrderSnsByBillId" resultType="java.lang.String">
        select distinct order_number
        from settle_shop_bill_detail
        where is_enable = 1
          and bill_id = #{billId}
    </select>

    <select id="getSettleShopBillDetailByPoolId"
            resultType="com.ly.yph.api.settlement.common.entity.SettleShopBillDetail">
        select ssbd.*
        From settle_shop_bill_detail ssbd
                 left join settle_shop_bill ssb on ssbd.bill_id = ssb.bill_id
        where ssbd.is_enable = 1
          and ssbd.pool_type = 1
          and ssbd.settle_bill_pool_id = #{settleBillPoolId}
          and ssb.customer_type = #{customerType}
    </select>

    <select id="getDetailsByOrdersAndCompany"
            resultType="com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto">
        select b.detail_id,
        b.reconciliation_status,
        b.store_code,
        b.bill_id,
        b.bill_sn,
        b.checked_num,
        b.invoicable_quantity,
        b.invoiced_quantity,
        b.unit_price_naked,
        b.unit_Price_tax,
        e.id as 'settlePoolId',
        e.tax_rate,
        c.id as 'lifeCycleId',
        c.customer_invoice_money as 'lifeInvoiceMoney',
        c.customer_invoicing_num as 'lifeInvoiceNum',
        b.order_number,
        b.goods_sku,
        b.total_price_tax,
        b.total_price_naked,
        b.reconciliation_user_id,
        b.reconciliation_user_name
        from settle_shop_bill_detail b
        left join settle_bill_pool e on b.settle_bill_pool_id = e.id
        left join settle_bill_life_cycle c on b.settle_bill_pool_id = c.settle_bill_pool_id
        left join settle_shop_bill f on f.bill_id = b.bill_id
        where f.customer_code =#{companyCode} and f.customer_type =0 and b.is_enable =1 and b.reconciliation_status=2
        and b.order_number in
        <foreach collection="orderNumbers" item="orderNumber" separator="," open="(" close=")">
            #{orderNumber}
        </foreach>
    </select>

    <select id="getbillByAfterForYfl" resultType="com.ly.yph.api.settlement.common.entity.SettleShopBill">
        select a.*
        from settle_shop_bill a
                 left join settle_shop_bill_detail b on b.bill_id = a.bill_id
        where a.is_enable = 1
          and a.customer_type = 0
          and a.customer_source_type =2
          and b.is_enable =1
          and a.activity_code = #{billAfterSaleForYflDto.activityCode}
          and a.bill_checked_type = #{billAfterSaleForYflDto.billType}
          and b.order_number =#{billAfterSaleForYflDto.orderNumber}
          and b.goods_code =#{billAfterSaleForYflDto.goodsCode}
    </select>

    <select id="getBillAfterSaleUpdateForYflDto"
            resultType="com.ly.yph.api.settlement.common.dto.bill.BillAfterSaleUpdateForYflDto">
        select b.order_number,
               b.goods_code,
               b.detail_id,
               c.bill_pool_yfl_id,
               c.order_detail_id,
               c.checked_num as poolCheckedNum,
               b.checked_num as billCheckedNum,
               c.goods_pay_integral,
               c.goods_pay_money,
               b.reconciliation_status
        from settle_shop_bill a
                 left join settle_shop_bill_detail b on a.bill_id = b.bill_id
                 left join settle_bill_pool_yfl_customer c on b.settle_bill_pool_id =c.bill_pool_yfl_id
        where a.is_enable=1
          and b.is_enable=1
          and c.is_enable=1
          and a.bill_id =#{billId}
          and b.order_number =#{orderNumber}
    </select>

    <select id="getYflUserUsedMonthIntegral" resultType="com.ly.yph.api.settlement.common.dto.settleBillPoolYfl.YflApplyUserUserMonthDto">
        select
        a.apply_user_id as applyUserId,
        sum(b.goods_pay_integral) as goodsPayIntegral
        from settle_shop_bill_detail a
        left join settle_bill_pool_yfl_customer b on a.settle_bill_pool_id = b.bill_pool_yfl_id
        where a.is_enable = 1 and a.bill_id =#{billId} and a.apply_user_id in
        <foreach collection="applyUserIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by a.apply_user_id
    </select>

    <select id="getInvoiceDetailForApproveYflPostage"
            resultType="com.ly.yph.api.settlement.common.dto.bill.BillInvoiceDetailDto">
        select b.postage_detail_id as detailId
        from invoice_bill a
                 left join invoice_detail_postage b on a.id = b.invoice_id
        where a.id = #{invoiceId}
    </select>

    <select id="getinvoicablDetailYfl"
            resultType="com.ly.yph.api.settlement.common.dto.invoice.InvoiceDetailBillInsertDto">
        select a.detail_id           as billDetailId,
               a.bill_id,
               a.bill_sn,
               a.invoicable_quantity as invoiceNum,
               b.tax_rate,
               b.order_number,
               b.goods_desc
        from settle_shop_bill_detail a
                 left join settle_bill_pool_yfl_customer b on a.settle_bill_pool_id = b.bill_pool_yfl_id
        where a.bill_id = #{billId}
          and a.is_enable = 1
          and a.reconciliation_status in (2, 5, 7)
          and b.goods_pay_integral >0
    </select>

    <select id="getDfshopReconciliationData"
            resultType="com.ly.yph.api.settlement.common.dto.bill.ReconciliationDetailDataDto">
        select
        a.detail_id,
        a.bill_id,
        a.reconciliation_status,
        a.checked_num,
        a.invoicable_quantity,
        a.settle_bill_pool_id,
        c.customer_type,
        c.customer_code,
        b.order_detail_id
        from settle_shop_bill_detail a
        left join settle_bill_pool b on a.settle_bill_pool_id = b.id
        left join settle_shop_bill c on c.bill_id = a.bill_id
        where a.is_enable =1 and a.pool_type =1 and a.detail_id in
        <foreach collection="detailIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getYflShopReconciliationData"
            resultType="com.ly.yph.api.settlement.common.dto.bill.ReconciliationDetailDataDto">
        select
        a.detail_id,
        a.bill_id,
        a.reconciliation_status,
        a.checked_num,
        a.invoicable_quantity,
        a.settle_bill_pool_id,
        c.customer_type,
        c.customer_code,
        b.order_detail_id
        from settle_shop_bill_detail a
        left join settle_bill_pool_yfl_customer b on a.settle_bill_pool_id = b.bill_pool_yfl_id
        left join settle_shop_bill c on c.bill_id = a.bill_id
        where a.is_enable =1 and a.pool_type =2 and a.detail_id in
        <foreach collection="detailIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getAutoInvoicePurchaseNumberForCompanyCode" resultType="java.lang.String">
        select distinct b.purchase_number
        from settle_shop_bill a
                 left join settle_shop_bill_detail b on a.bill_id = b.bill_id
                 left join settle_bill_pool c on c.id = b.settle_bill_pool_id
        where a.customer_type = 0
          and a.customer_code = #{companyCode}
          and b.is_enable = 1
          and b.reconciliation_status in (2,7)
          and c.auto_invoice_flag = 1
    </select>

    <select id="getAllDetailForPurchaseNumberAndCompanyCode"
            resultType="com.ly.yph.api.settlement.schedule.dto.PurchaseForBillDto">
        select
        a.detail_id,
        a.purchase_number,
        a.reconciliation_status,
        a.invoiced_quantity,
        a.invoicable_quantity,
        a.checked_num,
        b.bill_id,
        b.bill_sn,
        c.order_detail_id,
        c.tax_rate,
        a.reconciliation_user_id,
        a.reconciliation_user_name
        from settle_shop_bill_detail a
        left join settle_shop_bill b on a.bill_id = b.bill_id
        left join settle_bill_pool c on c.id = a.settle_bill_pool_id
        where b.customer_type = 0
        and a.is_enable = 1
        and b.customer_code = #{companyCode}
        and a.purchase_number in
        <foreach collection="purchaseNumberList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getOrderDetailForPurchaseNumberList"
            resultType="com.ly.yph.api.settlement.schedule.dto.PurchaseOrderDetailDto">
        select
        a.order_detail_id,
        a.apply_num+a.apply_num_decimal as applyNum,
        a.order_detail_state,
        a.order_detail_after_sale_state,
        b.purchase_number
        from shop_purchase_sub_order_detail a
        left join shop_purchase_sub_order b on a.order_id = b.order_id
        where b.is_enable = 1
        and a.is_enable = 1
        and b.purchase_number in
        <foreach collection="purchaseNumberList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getPurchaseInvoiceByPurchaseNumber"
            resultType="com.ly.yph.api.settlement.common.dto.invoice.InvoiceBillDto">
        select b.invoice_subject as invoiceSubjectName,
               b.taxpayer_number as invoiceTaxPayerNumber,
               b.subject_address as invoiceSubjectAddress,
               b.invoice_bank,
               b.bank_account    as invoiceBankAccount,
               b.subject_phone,
               a.company_code,
               a.company_name,
               c.label as invoiceType,
               a.purchase_number
        from shop_purchase_order a
                 left join shop_order_invoice b on a.purchase_id = b.purchase_id
                 left join system_dict_data c on c.dict_type = 'A010' and c.value = a.invoice_type
        where a.is_enable=1 and a.purchase_number in
        <foreach collection="purchaseNumbers" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


</mapper>