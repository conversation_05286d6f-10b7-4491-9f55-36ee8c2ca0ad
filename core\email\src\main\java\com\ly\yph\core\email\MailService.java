package com.ly.yph.core.email;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.email.model.ExcelAttachmentDto;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.core.enumeration.property.BodyType;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.credential.ExchangeCredentials;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.property.complex.MessageBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.mail.Address;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 邮件发送服务类
 */
@Slf4j
@Component
@RefreshScope
public class MailService {
    @Value("${notify.mail.username}")
    private String username;
    @Value("${notify.mail.password}")
    private String password;
    @Value("${notify.mail.domain}")
    private String domain;
    @Value("${notify.mail.uri}")
    private String port;
    @Value("${notify.mail.uri:#{25}}")
    private String uri;
    @Value("${notify.mail.enable}")
    private boolean enable;
    @Value("${notify.mail.loginUri}")
    private String loginUri;
    @Value("${notify.mail.loginAdminUri:#{\"http://172.26.164.93/admin/#/Order/Approval/List\"}}")
    private String login2Uri;
    @Value("${notify.mail.supplierOrderUrl:}")
    private String supplierOrderUrl;
    @Value("${notify.mail.appletLink}")
    private String appletLink;
    @Value("${notify.mail.frontUrl}")
    private String frontUrl;
    @Value("${notify.mail.adminUrl}")
    private String adminUrl;

    @Value("${notify.mail.yflName:东风有礼文创馆丨友福利}")
    private String yflName;

    @Value("${notify.mail.ignoreEmails:#{null}}")
    private String ignoreEmails;


    //采购审批邮件主题
    public static final String PURCHASE_SUBJECT = "东风商城采购审批通知";
    //友福利退款审批通知主题
    public static final String YFL_AFTERSALES = "友福利退款审批通知";
    //通知用户积分过期邮件主题
    public static final String INFORM_INTEGRAL = "友福利用户积分通知";

    public static final String REQ_UNTREATED_NOTIFY = "需求归集待处理通知";

    public static final String APPROACHING_EXPIRATION_NOTIFY = "采购申请单邻近超期邮件通知";

    public static final String SUPPLIER_SHELVES_NOTICE = "电商上新结果反馈";

    //账单审批邮件主题
    public static final String BILL_APPROVE = "账单待审批通知";

    //东风商城-邮件通知发货信息
    public static final String SUPPLIER_DELIVERY = "东风商城采购单发货通知";

    //供应商账单推送
    public static final String PURCHASE_SUPPLIER_BILL = "账单推送通知";

    //供应商账单已结算，暂不允许供应商账单售后通知
    public static final String SUPPLIER_FORBIDDEN_AFTER_SALE = "供应商账单已结算，不允许售后通知";

    public static final String COMPANY_FORBIDDEN_AFTER_SALE = "客户已开票账单申请售后通知";

    public static final String CHECK_AFTER_SALE = "验收单售后查询失败通知";

    public static final String SUPPLIER_FORBIDDEN_AFTER_SALE_POSTAGE = "供应商账单已结算,售后邮费无法回退通知！";

    public static final String RETURN_AFTER_SALE = "生成售后单异常通知！";

    public static final String BILL_FREIGHT_MONEY_REMIND = "售后含有现金邮费操作账单通知！";

    public static final String GOODS_TASK_NOTICE = "【东风商城】商品审核通知！";

    public static final String INTERFACE_ERROR_NOTICE = "东本商城-接口异常邮件提醒";

    public static final String BILL_AFTER_SALE_YFL_TEMP = "福利订单售后账单临时不处理通知";

    public static final String DOCUMENT_PUBLISH_NOTICE = "【东风商城】采购信息报送";

    public static final String DOCUMENT_PUBLISH_CANCEL_NOTICE = "【东风商城】采购信息撤回通知";

    public static final String DOCUMENT_COUNT_NOTICE = "【东风商城】回件通知";

    public static final String HONDA_JF_GOODS_DOWN_NOTE = "东本积分商品上下架情况";

    /**
     * 邮件通知单个人
     *
     * @param subject  主题
     * @param template 模板
     * @param sendTo   发送至
     */
    @Async
    public void sendEmail(String subject, String template, String sendTo) {
        sendEmail(subject, template, Collections.singletonList(sendTo), null);
    }

    /**
     * 邮件通知多个人
     *
     * @param subject  主题
     * @param template 模板
     * @param sendTo   发送至
     */
    @Async
    public void sendEmail(String subject, String template, List<String> sendTo) {
        sendEmail(subject, template, sendTo, null);
    }

    /**
     * 邮件通知多个人
     *
     * @param subject  主题
     * @param template 模板
     * @param sendTo   发送至
     * @param cc       抄送
     */
    @Async
    public void sendEmail(String subject, String template, List<String> sendTo, List<String> cc) {
        sendEmail(subject, template, sendTo, cc, null);
    }

    /**
     * 邮件通知多个人
     *
     * @param subject  主题
     * @param template 模板
     * @param sendTo   发送至
     * @param cc       抄送
     * @param fj       附件集合
     */
    @Async
    public void sendEmail(
            String subject,
            String template,
            List<String> sendTo,
            List<String> cc,
            List<ExcelAttachmentDto> fj) {
        try {
            if (!enable) {
                log.info("邮件发送开关处于关闭状态，邮件发送失败");
                return;
            }
            log.info("准备发送邮件前过滤邮箱"+ JSONUtil.toJsonStr(sendTo));
            sendTo = removeIgnoreEmails(sendTo);
            log.info("准备发送邮件后邮箱"+ JSONUtil.toJsonStr(sendTo));
            if (CollUtil.isEmpty(sendTo)) {
                log.info("邮件主体：{}的发送人为空", subject);
                return;
            }
            log.info("准备发送邮件");
            // 新建server版本
            ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP1);
            // 用户名，密码，域名
            ExchangeCredentials credentials = new WebCredentials(username, password, domain);
            service.setCredentials(credentials);
            // 邮箱服务器地址
            service.setUrl(new URI(uri));
            EmailMessage msg = new EmailMessage(service);
            // 主题
            msg.setSubject(subject);
            // 收件人
            for (String toPerson : sendTo) {
                msg.getToRecipients().add(toPerson);
            }
            // 抄送人
            if (CollUtil.isNotEmpty(cc)) {
                for (String ccPerson : cc) {
                    msg.getCcRecipients().add(ccPerson);
                }
            }
            // 附件
            try {
                if (CollUtil.isNotEmpty(fj)) {
                    for (ExcelAttachmentDto dto : fj) {
                        // 发起 HTTP 请求并获取响应流
                        InputStream inputStream = HttpRequest.get(dto.getFileUrl())
                                .timeout(30_000) // 设置超时
                                .execute()
                                .bodyStream();

                        // 将输入流转换为 ByteArrayInputStream
                        ByteArrayInputStream byteArrayInputStream = IoUtil.toStream(IoUtil.readBytes(inputStream));
                        msg.getAttachments().addFileAttachment(dto.getFileName(), byteArrayInputStream);
                    }
                }
            } catch (Exception e) {
                log.error("哎，附件没带上", e);
            }
            MessageBody body = MessageBody.getMessageBodyFromText(template);
            body.setBodyType(BodyType.HTML);
            msg.setBody(body);
            msg.send();
            log.info(
                    "邮件发送成功，发送时间为:[{}]",
                    DateUtils.format(new Date(), FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        } catch (Exception e) {
            log.error("发送失败收件人,{}", sendTo.get(0));
            log.error("发送邮件通知异常", e);
        }
    }

    /**
     * 邮件通知多个人
     *
     * @param subject         主题
     * @param template        模板
     * @param sendTo          发送至
     * @param cc              抄送
     * @param attachmentName  附件名称
     * @param excelAttachment 附件输入流
     */
    @Async
    public void sendEmail(String subject, String template, List<String> sendTo, List<String> cc, String attachmentName, ByteArrayInputStream excelAttachment) {
        try {
            if (!enable) {
                log.info("邮件发送开关处于关闭状态，邮件发送失败");
                return;
            }
            log.info("准备发送邮件前过滤邮箱"+ JSONUtil.toJsonStr(sendTo));
            sendTo = removeIgnoreEmails(sendTo);
            log.info("准备发送邮件后邮箱"+ JSONUtil.toJsonStr(sendTo));
            if (CollUtil.isEmpty(sendTo)) {
                log.info("邮件主体：{}的发送人为空", subject);
                return;
            }
            log.info("准备发送邮件");
            // 新建server版本
            ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2010_SP1);
            // 用户名，密码，域名
            ExchangeCredentials credentials = new WebCredentials(username, password, domain);
            service.setCredentials(credentials);
            // 邮箱服务器地址
            service.setUrl(new URI(uri));
            EmailMessage msg = new EmailMessage(service);
            // 主题
            msg.setSubject(subject);
            // 收件人
            for (String toPerson : sendTo) {
                msg.getToRecipients().add(toPerson);
            }
            // 抄送人
            if (CollUtil.isNotEmpty(cc)) {
                for (String ccPerson : cc) {
                    msg.getCcRecipients().add(ccPerson);
                }
            }
            // 附件
            if (excelAttachment != null) {
                msg.getAttachments().addFileAttachment(attachmentName, excelAttachment);
            }
            MessageBody body = MessageBody.getMessageBodyFromText(template);
            body.setBodyType(BodyType.HTML);
            msg.setBody(body);
            msg.send();
            log.info("邮件发送成功，发送时间为:[{}]", DateUtils.format(new Date(), FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        } catch (Exception e) {
            log.error("发送邮件通知异常", e);
        }
    }

    /**
     * 根据ip端口给指定邮箱发送邮件
     *
     * @param subject
     * @param template
     * @param sendTo
     * @throws Exception
     */
    public void sendMailByIp(String subject, String template, String sendTo) throws Exception {
        sendMailByIp(subject, template, sendTo, null);
    }

    /**
     * 根据ip端口给指定邮箱发送邮件，并抄送给一批邮箱
     *
     * @param subject
     * @param template
     * @param sendTo
     * @throws Exception
     */
    public void sendMailByIp(String subject, String template, String sendTo, List<String> cCList) throws Exception {
        sendMailByIp(subject, template, sendTo, cCList, null, null, null);
    }

    /**
     * 根据ip端口给指定邮箱发送邮件，并抄送、密送给一批邮箱
     *
     * @param subject
     * @param template
     * @param sendTo
     * @throws Exception
     */
    public void sendMailByIp(String subject, String template, String sendTo, List<String> cCList, List<String> bCCList) throws Exception {
        sendMailByIp(subject, template, sendTo, cCList, bCCList, null, null);
    }

    /**
     * 根据ip端口给指定邮箱发送邮件，并抄送一批邮箱，并支持附件
     *
     * @param subject
     * @param template
     * @param sendTo
     * @throws Exception
     */
    public void sendMailByIp(String subject, String template, String sendTo, List<String> cCList, String excelAttachmentName, ByteArrayInputStream excelAttachment) throws Exception {
        sendMailByIp(subject, template, sendTo, cCList, null, excelAttachmentName, excelAttachment);
    }

    /**
     * 根据ip端口给指定邮箱发送邮件，并抄送、密送一批邮箱，并支持附件
     *
     * @param subject
     * @param template
     * @param sendTo
     * @throws Exception
     */
    public void sendMailByIp(String subject, String template, String sendTo, List<String> cCList, List<String> bCCList, String excelAttachmentName, ByteArrayInputStream excelAttachment) throws Exception {
        if (!enable) {
            log.info("邮件服务功能已关闭，拒绝发邮件 {}", sendTo);
            return;
        }
        // 获取邮件Session
        Session session = getSession(uri);
        // 启动Debug模式，该方法也是线程安全的
        //session.setDebug(true);
        // 获取邮件的传输对象，主要用来连接邮箱和发邮箱的
        Transport transport = session.getTransport();
        // 创建一封邮箱
        MimeMessage mimeMessage = createMimeMessage(session, sendTo, subject, template, cCList, bCCList, excelAttachmentName, excelAttachment);
        // 连接邮箱服务器
        transport.connect(username, password);
        // 发送邮件，其实这里可以自己去弄一个邮箱轰炸机（狡猾(〃'▽'〃)），大家有兴趣可以自己实现，很简单。
        transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
        // 关闭连接
        transport.close();
        log.info("邮件发送成功，接收者{}  内容：{}", sendTo, template);
    }

    /**
     * 创建邮件
     *
     * @param receiveMail
     * @param subject
     * @param template
     * @return
     * @throws Exception
     */
    private MimeMessage createMimeMessage(Session session, String receiveMail, String subject, String template, List<String> cCList, List<String> bCCList, String excelAttachmentName, ByteArrayInputStream excelAttachment) throws Exception {
        // 1. 创建一封邮件
        MimeMessage message = new MimeMessage(session);
        // 2. From: 发件人（昵称有广告嫌疑，避免被邮件服务器误认为是滥发广告以至返回失败，请修改昵称）
        message.setFrom(new InternetAddress(username, "采购商城", "UTF-8"));
        // 3. To: 收件人（可以增加多个收件人、抄送、密送）
        message.setRecipient(MimeMessage.RecipientType.TO, new InternetAddress(receiveMail, "", "UTF-8"));
        // 抄送
        addSendList(message, cCList, MimeMessage.RecipientType.CC);
        // 密送
        addSendList(message, bCCList, MimeMessage.RecipientType.BCC);
        // 4. Subject: 邮件主题（标题有广告嫌疑，避免被邮件服务器误认为是滥发广告以至返回失败，请修改标题）
        message.setSubject(subject, "UTF-8");
        // 5. Content: 邮件正文（可以使用html标签）（内容有广告嫌疑，避免被邮件服务器误认为是滥发广告以至返回失败，请修改发送内容）
        message.setContent(template, "text/html;charset=UTF-8");
        // 6. 设置发件时间
        message.setSentDate(new Date());
        // 添加附件
        addAttachment(message, template, excelAttachmentName, excelAttachment);
        // 7. 保存设置
        message.saveChanges();
        return message;
    }

    private void addAttachment(MimeMessage message, String template, String excelAttachmentName, ByteArrayInputStream excelAttachment) throws Exception {
        if (excelAttachment != null) {
            ByteArrayDataSource file = new ByteArrayDataSource(excelAttachment, "application/vnd.ms-excel;charset=UTF-8");
            MimeMessageHelper messageHelper = new MimeMessageHelper(message, true, "utf-8");
            messageHelper.addAttachment(MimeUtility.encodeWord(excelAttachmentName, "utf-8", "B"), file);
            messageHelper.setText(template, true);
        }
    }

    private void addSendList(MimeMessage message, List<String> ccList, Message.RecipientType recipientType) throws Exception {
        if (CollUtil.isNotEmpty(ccList)) {
            Address[] cCAddress = new Address[ccList.size()];
            for (int index = 0; index < ccList.size(); index++) {
                cCAddress[index] = new InternetAddress(ccList.get(index), "", "UTF-8");
            }
            message.setRecipients(recipientType, cCAddress);
        }

    }

    private Session getSession(String myEmailSMTPServer) throws Exception {
        // 创建参数配置, 用于连接邮件服务器的参数配置
        Properties props = new Properties();
        // 连接参数配置
        props.setProperty("mail.transport.protocol", "smtp");
        // 使用的协议（JavaMail规范要求）
        props.setProperty("mail.smtp.host", myEmailSMTPServer);
        // 发件人的邮箱的 SMTP 服务器地址
        props.setProperty("mail.smtp.auth", "true");
        // smtp的端口号，QQ邮箱的SMTP(SLL)端口为465或587, 其他邮箱自行去查看)，需要改为对应邮箱的 SMTP 服务器的端口, 具体可查看对应邮箱服务的帮助,
        props.setProperty("mail.smtp.port", port);
        //props.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.setProperty("mail.smtp.socketFactory.fallback", "false");
        props.setProperty("mail.smtp.socketFactory.port", port);
        // 该方法线程安全，由synchronized修饰
        return Session.getDefaultInstance(props);
        // PS: 某些邮箱服务器要求 SMTP 连接需要使用 SSL 安全认证 (为了提高安全性, 邮箱支持SSL连接, 也可以自己开启),
        // 如果无法连接邮件服务器, 仔细查看控制台打印的 log, 如果有有类似 “连接失败, 要求 SSL 安全连接” 等错误,
        // 打开下面 /* ... */ 之间的注释代码, 开启 SSL 安全连接。
        // SMTP 服务器的端口 (非 SSL 连接的端口一般默认为 25, 可以不添加, 如果开启了 SSL 连接
    }

    /**
     * 采购申请邮件模板
     *
     * @param organize
     * @param name
     * @param purchaseSn
     * @param number
     * @param money
     * @param applyTime
     * @return
     */
    public String purchaseTemplate(String organize, String name, String purchaseSn, BigDecimal number, BigDecimal money, String applyTime, String isUrgent) {
        StringBuilder content = new StringBuilder("<html><head><style>table,table tr td{border:1px solid #000;text-align:center;border-collapse:collapse;}</style></head><body><p>【东风商城】您收到一张新的采购申请单，请尽快审批。</p>");
        content.append("<table width='80%'>");
        content.append("<tr align='center'><td>申请人</td><td>部门/科室</td><td>采购单号</td><td>采购金额</td><td>数量</td><td>申请日期</td><td>是否紧急采购</td></tr>");
        content.append("<tr align='center'><td>" + name + "</td><td>" + organize + "</td><td>" + purchaseSn + "</td><td>" + money + "</td><td>" + number + "</td><td>" + applyTime + "</td><td>" + isUrgent + "</td></tr>");
        content.append("</table>");
        content.append("<p><a style='color:blue;' href='" + loginUri + "' target='_blank'>前往商城审批</a><p>");
        content.append("</body></html>");
        return content.toString();
    }

    /**
     * 积分为0不提示
     *
     * @param integralName         积分名称
     * @param integralUsableAmount 积分金额
     * @param endTime              到期时间
     * @return
     */
    public String notifyUserBudgetInformation(String integralName, BigDecimal integralUsableAmount, String endTime) {
        StringBuilder content = new StringBuilder("<p>尊敬的用户您好!</p>");
        content.append("<p> &nbsp;&nbsp; 您的「" + integralName + "」积分还剩余「" + integralUsableAmount + "」, 积分即将在「" + endTime + "」到期, 您可以搜索微信小程序“" + yflName + "“ , 或者扫描下方小程序码;</p>");
        content.append("    <div style=\"text-align: center;\">\n" +
                "        <img height=\"200\" width=\"200\" " +
                "             src=\"" + appletLink + "\"\n" +
                "             alt=\"\"/>\n" +
                "        <div style=\"font-size: 18px;font-weight: 400;\">\n" +
                "            友福利小程序\n" +
                "        </div>\n" +
                "    </div>\n");
        return content.toString();
    }


    public String notifyToGoodsDoTask(Integer num) {
        StringBuilder content = new StringBuilder("<p>尊敬的用户您好!</p>");
        content.append("<p> &nbsp;&nbsp; 您有「 " + num + " 」个商品待审核，请尽快审核</p>");
        content.append("<p>登录 https://dfmall.szlanyou.com/admin/#/Home  前往审核</p>");
        return content.toString();
    }

    /**
     * 收货单审批通知邮件模板
     *
     * @param empCode
     * @param tenantId 租户id
     * @return
     */
    public String afterSalesTemplate(String empCode, Long tenantId) {
        StringBuilder content = new StringBuilder("<html><head><style>table,table tr td{border:1px solid #000;text-align:center;border-collapse:collapse;}</style></head><body><p>&emsp;&emsp; 您好，您有一笔混合支付退款单据待审批，请点击下面的链接跳转至友福利后台进行审批链接</p>");
        content.append("<p><a style='color:blue;' href='" + login2Uri + "' target='_blank'>前往审批</a><p>");
        content.append("</body></html>");
        return content.toString();
    }

    /**
     * 对有未处理的归集需求信息用户，进行一次邮件提醒。
     *
     * @return 模板内容
     */
    public String requirementProcessTemplate(String requirementUrl) {
        StringBuilder b = new StringBuilder();
        String sendTime = LocalDateTime.now().format(DatePattern.CHINESE_DATE_FORMAT.getDateTimeFormatter());
        return b.append("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><div class=\"_email_template_style_wrapper\"><style type=\"text/css\">._email_template_style_wrapper ul,._email_template_style_wrapper ol,._email_template_style_wrapper li {margin: 0;padding: 0;}._email_template_style_wrapper ul {-webkit-margin-start: 1em;list-style: disc outside;-webkit-padding-start: 40px;}._email_template_style_wrapper ul li {list-style: disc outside;}._email_template_style_wrapper ol {-webkit-margin-start: 1em;list-style: decimal outside;-webkit-padding-start: 40px;}._email_template_style_wrapper ol li {list-style: decimal outside;}._email_template_style_wrapper img {max-width: 100%;}._email_template_style_wrapper table{border-collapse: collapse;} ._email_template_style_wrapper table td {padding: 5px 10px;border: 1px solid;}._email_template_style_wrapper p {margin: 5px 0;}</style>")
                .append("<div class=\"_email_template_style_wrapper\" style=\"\"><p></p><p style=\"padding-top: 10px;padding-bottom: 30px;white-space: normal;font-size: 28px;line-height: 40px;text-align: center\"><span style=\"color: rgb(44, 51, 56);font-family: Helvetica Neue, STHeitiSC, PingFang SC, Microsoft YaHei, Hiragino Sans GB, Tahoma, sans-serif\">" + "")
                .append(REQ_UNTREATED_NOTIFY)
                .append("</span></p><div class=\"sub-title\" style=\"text-align: left;white-space: normal;line-height: 30px;margin-bottom: 30px\"><p style=\"font-size: 18px;color: rgb(99, 108, 117)\"><span style=\"color: rgb(23, 43, 77);font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;white-space: normal\"></span></p><p style=\"text-align: left\"></p><div style=\"text-align: left\"><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">&nbsp; &nbsp;&nbsp;</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">东风</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">商城</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">提醒您，您有</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\"><</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\"><span style=\"color: rgb(0, 0, 0);font-size: 18px;white-space: normal\"><span><span><span class=\"__template_keyword\">")
                .append("#{num}")
                .append("条需求归集").append("</span></span></span><span>></span></span><span style=\"color: rgb(0, 0, 0);font-size: 18px;white-space: normal\"><span><span>事项</span></span></span></span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">未处理，</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">请尽快完成。</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">进入路径：</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">东风商城-个人中心-商城订单-需求归集</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">，也可点击下方</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">“</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">查看详情</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">”</span><span style=\"font-size: 18px;color: rgb(0, 0, 0)\">进入。</span></div><p></p></div><p style=\"text-align: center\">&nbsp;</p><p style=\"text-align: center\"></p><p style=\"text-align: center\"><a href=\"")
                .append(requirementUrl)
                .append("\" target=\"_self\" title=\"查看详情\">查看详情</a></p><p style=\"text-align: center\">&nbsp;</p><p style=\"text-align: right\">联友智连科技有限公司</p><p style=\"text-align: right\"><span><span><span class=\"__template_keyword\">")
                .append(sendTime)
                .append("</span></span></span></p><p style=\"text-align: center\">&nbsp;</p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p style=\"text-align: center\"></p><p></p><p></p><div class=\"footer\" style=\"white-space: normal;color: rgb(130, 139, 148);text-align: center\">本邮件由系统自动发出，请勿回复。</div><p></p></div></div>")
                .toString();
    }

    public String supplierOrderTemplate(String supplierOrderNumber, BigDecimal supplierOrderPriceTax, BigDecimal supplierOrderPriceNaked, String companyName, String createTime) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城订单提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的供应商，</h2><p>您好！您有一个来自「").
                append(companyName).
                append("」的待发货订单，请及时处理。</p><h3>订单详情：</h3><ul><li><span>订单号：").
                append("</span>").
                append(supplierOrderNumber).
                append("</li><li><span>含税总价：").
                append("</span>").append(supplierOrderPriceTax).append("</li><li><span>不含税总价：").append("</span>").append(supplierOrderPriceNaked).append("</li><li><span>下单日期：").append("</span>").append(createTime).append("</li></ul><p>请您核对以上信息，并确认您能够按照订单要求及时提供所需的货物/服务。</p><p>点击下方“查看详情”登录东风商城进行处理。").
                append("</p><p style=\"text-align:center\"><a href=\"").append(supplierOrderUrl).append("\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();

    }

    public String supplierOrderTemplateBySrm(String supplierOrderNumber, BigDecimal supplierOrderPriceTax, BigDecimal supplierOrderPriceNaked, String companyName, String createTime) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城订单提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的供应商，</h2><p>您好！您有一个来自「").
                append(companyName).
                append("」的待发货订单，请及时处理。</p><h3>订单详情：</h3><ul><li><span>订单号：").
                append("</span>").
                append(supplierOrderNumber).
                append("</li><li><span>含税总价：").
                append("</span>").append(supplierOrderPriceTax).append("</li><li><span>不含税总价：").append("</span>").
                append(supplierOrderPriceNaked).append("</li><li><span>下单日期：").append("</span>").append(createTime).
                append("</li></ul><p>请您核对以上信息，并确认您能够按照订单要求及时提供所需的货物/服务。</p><p>请登录SRM系统，首页门户-订单管理，跳转至商城进行订单确认及发货").
                append("</p><p style=\"text-align:center\"><a href=\"").append(supplierOrderUrl).append("\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();

    }

    public String supplierGoodsUpNewTemplate(String sku) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城上新提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的供应商，</h2><p>您好！您有一个来自东风商城的商品上新申请，请及时处理。<br>商品SKU: </p><ul><li>").
                append("</span>").
                append(sku).
                append("</li>").append("</ul><p>点击下方“查看详情”登录东风商城进行处理。").
                append("</p><p style=\"text-align:center\"><a href=\"").append(supplierOrderUrl).append("\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();

    }

    public String goodsUpNewApplyUserNotice(String sku, String supplierName) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城上新提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的用户，</h2><p>您好！您有一个来自「").
                append(supplierName).
                append("」的商品上新已完成处理。</p><ul><li><span>上新商品SKU如下：").
                append("</span>").
                append(sku).
                append("</li>").append("</ul>").
                append("<p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();

    }

    public String goodsUpNewOperateNotice(Map<String, List<String>> supplierSku) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城上新提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的用户，</h2><p>您好！来自以下供应商").
                append("的商品已经超过三天未完成上新。</p><ul><li><span>上新商品SKU如下：<br>");
        supplierSku.forEach((supplierName, skuList) ->
                sb.append(supplierName).
                        append(": <br>").
                        append(skuList.stream().collect(Collectors.joining("<br>"))).
                        append("</li>").append("</ul>")
        );
        sb.append("<p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String seekUpNewTemplate(String seekPriceNumber, String skuStr) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城上新提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的用户，</h2><p>您好！您的询价单「")
                .append(seekPriceNumber)
                .append("」已有商品上新。</p><ul><li><span>上新商品如下：</span></li><li>")
                .append(skuStr)
                .append("</li></ul><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    /**
     * SRM合同创建订单提醒模板
     *
     * @param purchaseNumber
     * @param contractNumber
     * @return
     */
    public String srmContractCreateOrderTemplate(String purchaseNumber, String contractNumber) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城订单提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的用户，</h2><p>您好！您有一个来自「").
                append(contractNumber).
                append("」的合同已生成订单，请知悉。</p><h3>订单信息：</h3><ul><li><span>采购单号：").
                append("</span>").
                append(purchaseNumber).
                append("</li><li><span>").
                append("</li></ul><p>点击下方“查看详情”登录东风商城进行处理。").
                append("</p><p style=\"text-align:center\"><a style='color:blue;' href=\"").
                append(loginUri).
                append("\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");

        return sb.toString();

    }

    /**
     * 审批结束邮件模板
     *
     * @param purchaseNumber  采购单号
     * @param approvalResults 审批结果
     * @return
     */
    public String approvalResultsTemplate(String purchaseNumber, String approvalResults) {
        //获取年月日
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int mouth = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DATE);

        StringBuilder content = new StringBuilder("<p>尊敬的客户您好！</p>");
        content.append("<p style=\"text-indent: 2em;\" >您的采购申请" + purchaseNumber + "，审批" + approvalResults + "，请登录东风商城查看详情。感谢您对我们工作的理解与支持！</p>");
        content.append("<div style=\"float: right\"> 东风商城 </div></br>");
        content.append("<div style=\"float: right\">" + year + "年" + mouth + "月" + day + "日</div>");
        return content.toString();
    }

    /**
     * 邻近超期 审批结束通知邮件模板
     *
     * @param purchaseNumber 采购单号
     * @param userName       客户名称
     * @return
     */
    public String approachingExpirationResultsTemplate(String purchaseNumber, String userName) {
        //获取年月日
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int mouth = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DATE);

        StringBuilder content = new StringBuilder("<p>" + userName + ",您好！</p>");
        content.append("<p style=\"text-indent: 2em;\" >您的采购单号【" + purchaseNumber + "采购单号】审批流程即将超期，请尽快完成审批，谢谢!</p>");
        content.append("<div style=\"float: right\"> 东风商城 </div></br>");
        content.append("<div style=\"float: right\">" + year + "年" + mouth + "月" + day + "日</div>");
        return content.toString();
    }

    public String supplierShelvesNoticeTemplate(String goodsName, Date shelveTime, String supplierName, String sku) {
        String time = DateUtil.format(shelveTime, DatePattern.NORM_DATE_PATTERN);
        String targetUrl = loginUri + "RequestNew";
        String hasSku = StringUtils.isNotBlank(sku) ? "，上架商品sku为【" + sku + "】，请留意并到商城进行查看" : "";
        String res = StringUtils.isNotBlank(sku) ? "已成功上新" : "已放弃上新";
        String template = "<p>您好：</p>" +
                "<p>您的【需求上新-{}】已在【{}】由【{}】[{}]" + hasSku + "。</p>" +
                "<p>链接：{}</p>";
        return StrUtil.format(template, goodsName, time, supplierName, res, targetUrl);
    }

    /**
     * 账单审批邮件通知模板
     *
     * @param approveMan
     * @param billSn
     * @return
     */
    public String billApproveTemplate(String approveMan, String billSn) {
        StringBuilder content = new StringBuilder("<p>" + approveMan + ",您好</p>");
        content.append("<p>您有一条账单编号为[" + billSn + "]的账单需要您及时审批，请尽快<a style='color:blue;'href='" + loginUri + "' target='_blank'>登录友品汇</a>处理</p>");
        content.append("<div style='width:200px;border-bottom:2px solid #B7C6E0'></div>");
        content.append("<p>东风商城</p>");
        return content.toString();
    }

    /**
     * 东风商城-邮件通知发货信息
     */
    public String supplierDeliveryTemplate(String orderNo, String deliveryNo, String deliverCompany) {
        StringBuilder content = new StringBuilder("<p>您的采购申请[" + orderNo + "]已发货，物流单号:[" + deliveryNo + "],物流公司:[" + deliverCompany + "],您可以登录商城查看具体的物流信息。</p>");
        return content.toString();
    }

    /**
     * 供应商账单推送邮件通知模板
     */
    public String supplierBillTemplate(String supplierName, Integer year, Integer month, String begin, String end, BigDecimal totalAmountTax, String billSn) {
        StringBuilder content = new StringBuilder("<p>致" + supplierName + ":<p>");
        content.append("<p>联采云" + year + "年" + month + "月账单已送达，信息如下：</p>");
        content.append("<p>账单编号：" + billSn + "</p>");
        content.append("<p>账单周期：[" + begin + "]至[" + end + "]</p>");
        content.append("<p>合计金额(含税):" + totalAmountTax + "</p>");
        content.append("<p>请贵司知悉并及时<a style='color:blue;'href=https://dfmall.szlanyou.com/admin/#/login target='_blank'>登录东风商城</a>确认与下载账单明细</p>");
        content.append("<p>东风商城网址：https://dfmall.szlanyou.com/admin/#/login</p>");
        content.append("<p>开票注意事项：<p>");
        content.append("<p>1.开票金额请以含税总价为准开具;<p>");
        content.append("<p>2.电子发票请回复所有人，邮件内容标注是几月账单;<p>");
        content.append("<p>联友发票信息如下：</p>");
        content.append("<p>企业名称：联友智连科技有限公司</p>");
        content.append("<p>纳税人识别号：91440101MA9W5HMKXU</p>");
        content.append("<p>开户行：招商银行广州分行风神支行</p>");
        content.append("<p>银行账号：120919107210999</p>");
        content.append("<p>注册地址:广州市花都区风神大道15号1栋一楼117房</p>");
        content.append("<p>联系电话：020-37739114<p>");
        content.append("<p> </p>");
        content.append("<p>联友收票人（注意变更）：</p>");
        content.append("<p>收票人：姚嘉文,联系方式：13560143181</p>");
        content.append("<p>收票地址：广州市花都区风神大道12号东风南方大楼</p>");
        return content.toString();
    }

    public String outInvoiceOffSetTemplate(String invoiceApplyNumber) {
        StringBuilder content = new StringBuilder("<P>尊敬的东风商城管理员：</P>");
        content.append("<p>外部开票数据：[" + invoiceApplyNumber + "]由MSDP系统发起了红冲/废弃操作，请及时做相关的回退处理！</p>");
        return content.toString();
    }

    /**
     * 供应商账单禁止售后
     */
    public String supplierForbiddenAfterSaleTemplate(String orderNumber, String goodsSku) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>订单：[" + orderNumber + "],商品SKU：[" + goodsSku + "]正在申请售后处理,但是此商品在供应商侧处于已结算状态,暂时无法剔除供应商侧账单明细,请运营及研发同事知悉！</p>");
        return content.toString();
    }

    /**
     * 客户存在开票账单明细禁止售后
     */
    public String companyForbiddenAfterSaleTemplate(String orderNumber, String goodsSku, String billSn, String reconciliationStatusName, String companyName) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>订单：[" + orderNumber + "],商品SKU:[" + goodsSku + "]在账单：[" + billSn + "]中处于[" + reconciliationStatusName + "]状态</p>");
        content.append("<p>所属企业：[" + companyName + "]<p>");
        content.append("用户正在申请售后，请运营及研发同事知悉并及时处理！");
        return content.toString();
    }

    /**
     * 部分售后现金运费邮件通知
     */
    public String billFreightMoneyRemindTemplate(String orderNumber, String returnCode) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>订单：[" + orderNumber + "],售后单:[" + returnCode + "]存在部分退现金运费的情况</p>");
        content.append("请运营及研发同事知悉并及时处理！");
        return content.toString();
    }

    /**
     * 验收单售后查询失败通知
     */
    public String checkAfterSaleTemplate(Long deliveryId, String goodsSku) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>发货单id：[" + deliveryId + "],商品SKU：[" + goodsSku + "]正在申请售后处理,但是此商品未找到唯一验收单,暂时无法系统操作,请运营及研发同事知悉！</p>");
        return content.toString();
    }

    /**
     * 供应商账单禁止售后退邮费
     */
    public String supplierForbiddenAfterSalePostageTemplate(String orderNumber) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>订单：[" + orderNumber + "],正在申请售后处理,但是此商品在供应商侧处于已结算状态,y邮费暂时无法退还,请运营及研发同事知悉！</p>");
        return content.toString();
    }

    /**
     * 供应商账单禁止售后退邮费
     */
    public String returnAfterSaleTemplate(String message, String info) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>京东消息：[" + message + "],申请售后处理异常:[" + info + "],研发同事知悉！</p>");
        return content.toString();
    }

    /**
     * 接口异常
     *
     * @param param
     * @return
     */
    public String interfaceErrorTemplate(Map<String, String> param) {
        StringBuilder content = new StringBuilder("<p>尊敬的东本商城系统管理员：</p>");
        content.append("<p></p><p>&emsp;&emsp; 东本商城有订单处理中接口异常</p>");
        content.append("<p>&emsp;&emsp;时间：").append(param.get("createTime")).append("</p>");
        content.append("<p>&emsp;&emsp;接口名：").append(param.get("url")).append("</p>");
        content.append("<p>&emsp;&emsp;请求参数：").append(param.get("reqParam")).append("</p>");
        content.append("<p>&emsp;&emsp;返回结果：").append(param.get("respResp")).append("</p>");
        content.append("<p>&emsp;&emsp;请立即查看解决；</p>");
        content.append("<p>以上，请知悉，祝您工作顺利~</p>");
        return content.toString();
    }

    public String payReturnErrorTemplate(String dto, String info) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>售后单退款：[" + dto + "],退金额处理异常:[" + info + "],研发同事知悉！</p>");
        return content.toString();
    }

    /**
     * 福利订单售后临时不处理账单
     */
    public String getBillAfterSaleYflTemp(String json) {
        StringBuilder content = new StringBuilder("<p>尊敬的东风商城管理员：<p>");
        content.append("<p>福利订单申请售后,暂时未过滤账单,请研发同事知悉！</p>");
        content.append("<p>" + json + "</p>");
        return content.toString();
    }

    /**
     * 文档管理发布通知
     */
    public String getDocumentPublicTemp(String publicer, String theme) {
        StringBuilder content = new StringBuilder("<p>您好：<p>");
        content.append("<p>您有1项新任务待处理，主题：【" + theme + "】，发布人：【" + publicer + "】</p>");
        content.append("<p>请尽快前往东风商城-个人中心-采购管理-采购信息报送 查收 https://dfmall.szlanyou.com/#/Home</p>");
        return content.toString();
    }

    public String getDocumentPublicCancelTemp(String publicer, String theme) {
        StringBuilder content = new StringBuilder("<p>您好：<p>");
        content.append("<p>主题：【" + theme + "】，发布人：【" + publicer + "】，已被发布人撤回，</p>");
        content.append("<p>详情请前往东风商城-个人中心-采购管理-采购信息报送 查看 https://dfmall.szlanyou.com/#/Home</p>");
        return content.toString();
    }

    /**
     * 文档管理发布通知
     */
    public String getDocumentCountTemp(Map<String, Integer> documentMap) {
        StringBuilder content = new StringBuilder("<p>您好：<p>");
        content.append("<p>您已发布的任务今日新增回件如下: \n");
        for (String theme : documentMap.keySet()) {
            content.append("【" + theme + "】新增" + documentMap.get(theme) + "封回件；\n");
        }
        content.append("</p><p>最新回件情况以系统实际为准；请尽快前往东风商城-个人中心-采购管理-采购信息报送 查收 https://dfmall.szlanyou.com/#/Home</p>");
        return content.toString();
    }

    public String acceptProblemTemplate(String createTime, String problemDesc, String problemSolution) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><title>【东风商城】问题反馈回复</title><style>p{text-indent: 2em;font-family: '宋体', 'Arial', sans-serif;line-height: 1.5;word-wrap: break-word;overflow-wrap: break-word;}</style></head>")
                .append("<body><div><p>尊敬的用户您好，您于 ")
                .append(createTime)
                .append(" 提出的问题反馈：</p><p>")
                .append(problemDesc)
                .append("</p><p>经过详细的审查和处理，解决方案如下：</p><p>")
                .append(problemSolution)
                .append("</p><p>我们非常重视您的意见和建议，希望解决方案能够满足您的需求。如果需要进一步地帮助，请随时与我们联系，再次感谢您的支持与理解！祝您生活愉快！</p></div></body></html>");
        return sb.toString();
    }

    public String noticeSupplierSeekPublishTemplate(String companyName, String seekPriceNumber, String purchaseScene, String seekEndTime, Integer seekPriceWay) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询价单发布提醒</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商，您好！</h2>")
                .append("<p>「").append(companyName).append("」").append(seekPriceWay == 1 ? "发布了一条新的询价单</p>" : "诚挚邀请贵司参与本次询比项目报价。相关采购信息如下：</p>")
                .append("<p>询价单号「").append(seekPriceNumber).append("」</p>")
                .append("<p>采购场景:").append(purchaseScene).append("</p>")
                .append("<p>报价截止时间:").append(seekEndTime).append("</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("#/SupplierQuotation/List\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String seekEndTimeNoticeUser(String seekPriceNumber, String seekPriceTitle) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询单取消通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您的询价单号「").append(seekPriceNumber).append("」，采购标题「").append(seekPriceTitle).append("」，已自动取消</p>")
                .append("<p>取消原因：报价供应商数量不足，询单已自动作废</p>")
                .append("<p>您可前往系统查看，点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/StoreSearchSource\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String cancelCalibrationNoticeUser(String seekPriceNumber, String seekPriceTitle) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询单取消通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您的询价单「").append(seekPriceNumber).append("」，采购标题「").append(seekPriceTitle).append("」，已自动取消</p>")
                .append("<p>取消原因：根据《政府采购非招标采购方式管理办法》，报价截止后7个工作日未比价，系统已自动取消</p>")
                .append("<p>您可前往系统查看，点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/StoreSearchSource\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String cancelCalibrationNoticeSup(String seekPriceNumber, String seekPriceTitle) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询单取消通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商 您好！</h2>")
                .append("<p>您参与报价的询价单「").append(seekPriceNumber).append("」，采购标题「").append(seekPriceTitle).append("」，已自动取消</p>")
                .append("<p>取消原因：比价人未在报价截止后7个工作日内完成比价，系统已自动取消</p>")
                .append("<p>您可前往系统查看，点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("#/SupplierQuotation/List\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String promptCalibrationNoticeUser(String seekPriceNumber, String seekPriceTitle) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询单即将自动取消提醒</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您的询价单「").append(seekPriceNumber).append("」，采购标题「").append(seekPriceTitle).append("」，尚未完成比价</p>")
                .append("<p>请在3个工作日内完成比价，若超期未比价，根据《政府采购非招标采购方式管理办法》询单将自动取消</p>")
                .append("<p>您可前往系统查看，点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/StoreSearchSource\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String goodsCertificationCompletedEmail(String goodsSku, String goodsName) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】商品入池认证申请【成功】</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您好！</p>")
                .append("<p>感谢您在【东风商城】提交的商品入池认证申请。</p>")
                .append("<p>经过我们严格的审核流程，恭喜您！sku:").append(goodsSku).append("【").append(goodsName).append("】 已成功通过我们的可售认证，现已加入商品池，可前往加购。</p>")
                .append("<p>再次感谢您对我们服务的支持与信任！</p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String goodsCertificationRejectedEmail(String goodsSku, String goodsName, String approvedReason) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】商品入池认证申请【失败】</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您好！</p>")
                .append("<p>感谢您在【东风商城】提交的商品入池认证申请。</p>")
                .append("<p>经过我们严格的审核流程，很遗憾 sku:").append(goodsSku).append("【").append(goodsName).append("】 在本次可售认证中未能通过，主要原因包括【").append(approvedReason).append("】，如需重新认证，我们建议您根据上述反馈进行相应的调整和改进。</p>")
                .append("<p>再次感谢您对我们服务的支持与信任！</p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String goodsUpNewApprovalEmail(String companyName, String applyUserNickname, String createTime) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】电商上新审批</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您好，申请单位：【").append(companyName).append("】申请人：【").append(applyUserNickname).append("】于【").append(createTime).append("】已发起电商上新申请</p>")
                .append("<p>请您尽快点击下方“去审批”前往东风商城进行审批</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/ApprovalProcess\" target=\"_self\"title=\"去审批\">去审批</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String seekPriceApprovalEmail(String companyName, String purchasePeople, String createTime) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】询比服务询单发起审批</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您好，询价单位：【").append(companyName).append("】联系人：【").append(purchasePeople).append("】于【").append(createTime).append("】已发起询单</p>")
                .append("<p>请您尽快点击下方“去审批”前往东风商城进行审批</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/ApprovalProcess\" target=\"_self\"title=\"去审批\">去审批</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String comparePriceApprovalEmail(String seekPriceNumber, String calibrationPeopleName, String startTime) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】询比服务比价审批</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您好，询价单号：【").append(seekPriceNumber).append("】决策人：【").append(calibrationPeopleName).append("】于【").append(startTime).append("】已发起比价结果审批</p>")
                .append("<p>请您尽快前往东风商城审批</p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String seekEndTimeNoticeCalibration(String seekPriceNumber, String seekPriceTitle, String date) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】询比服务待处理通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您好，询价单号「").append(seekPriceNumber).append("」，采购标题「").append(seekPriceTitle).append("」，已截止报价。</p>")
                .append("<p>请您在「").append(date).append("」前，前往东风商城-询比服务-我的询价单完成本询单后续流程</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/StoreSearchSource\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String sendSeekStopToCreator(String seekPriceNumber, String seekPriceTitle, String terminationReason) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】询单取消通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户</h2>")
                .append("<p>您的询价单「").append(seekPriceNumber).append("」，采购标题「").append(seekPriceTitle).append("」，已被决策人取消。</p>")
                .append("<p>取消原因:").append(terminationReason).append("</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("#/StoreSearchSource\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String republishSeekNoticeSupplierTemplate(String companyName, String seekPriceNumber, JSONArray seekGoodsAry) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询价单发布提醒</title>")
                .append("<style>table{width:100%;border-collapse:collapse;margin:20px 0;font-size:18px;text-align:left}th,td{padding:12px;border-bottom:1px solid #ddd}th{background-color:#f4f4f4}tr:hover{background-color:#f1f1f1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商，您好！</h2>")
                .append("<p>由「").append(companyName).append("」发布，您参与了报价的询价单「").append(seekPriceNumber).append("」，企业发起议价;</p>");
        if (seekGoodsAry != null) {
            sb.append("<p>议价商品清单如下：</p>")
                    .append("<table><thead><tr><th>商品名称</th>")
                    .append("<th>品牌</th>")
                    .append("<th>规格</th>")
                    .append("<th>商品描述</th>")
                    .append("<th>需求数量</th>")
                    .append("<th>计量单位</th>")
                    .append("<th>预算未税单价(元)</th>")
                    .append("<th>要求交货期</th></tr></thead><tbody>");
            for (int i = 0; i < seekGoodsAry.size(); i++) {
                JSONObject seekGoodsObj = seekGoodsAry.getJSONObject(i);
                sb.append("<tr><td>").append(seekGoodsObj.getStr("goodsName")).append("</td><td>")
                        .append(seekGoodsObj.getStr("brandName")).append("</td><td>")
                        .append(seekGoodsObj.getStr("specModel")).append("</td><td>")
                        .append(seekGoodsObj.getStr("goodsDesc")).append("</td><td>")
                        .append(seekGoodsObj.getStr("demandNum")).append("</td><td>")
                        .append(seekGoodsObj.getStr("saleUnit")).append("</td><td>")
                        .append(seekGoodsObj.getStr("unitBudgetPriceNaked")).append("</td><td>")
                        .append(null == seekGoodsObj.getDate("desireDeliveryTime") ? "" : DatePattern.NORM_DATE_FORMAT.format(seekGoodsObj.getDate("desireDeliveryTime"))).append("</td></tr>");
            }
            sb.append("</tbody></table>");
        }
        sb.append("<p>系统已经默认匹配选择您上一轮的报价记录，您可前往系统查看并调整报价。</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("#/SupplierQuotation/List\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String noticeSupplierDelayQuotedTemplate(String seekPriceNumber, String seekPriceTitle, String oldEndTime, String newEndTime) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】询单延迟报价提醒</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商，您好！</h2>")
                .append("<p>询单号：【").append(seekPriceNumber).append("】，采购标题：【").append(seekPriceTitle).append("】已延长报价截止时间，原报价截止时间：").append(oldEndTime).append("，最新报价截止时间：").append(newEndTime).append("。</p>")
                .append("<p>尚未参与报价的商家，在此诚挚欢迎并鼓励贵司依据最新时间节点参与报价，把握此次合作契机。</p>")
                .append("<p>已参与报价的商家，烦请耐心等候至新的截止时间。</p>");
        return sb.toString();
    }

    public String noticeBiddenSuccessTemplate(String seekPriceNumber, String seekPriceTitle, List<String> biddenGoodsList) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询价单完成提醒</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商，您好！</h2>")
                .append("您参与的询价单「").append(seekPriceNumber).append("」「").append(seekPriceTitle).append("」已结束询价，您参与报价的以下商品已成交。</p>")
                .append("<ul>");
        biddenGoodsList.forEach(goodsName -> sb.append("<li><span>商品名称：</span>").append(goodsName).append("</li>"));
        sb.append("</ul>")
                .append("<p>请您核对以上信息，在询价单中填写商品SKU，以及推送或发布对应商品上架。</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("#/SupplierQuotation/List\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String noticeBiddenEndTemplate(String seekPriceNumber, String seekPriceTitle) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询价单完成提醒</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商，您好！</h2>")
                .append("您参与的询价单「").append(seekPriceNumber).append("」「").append(seekPriceTitle).append("」已结束询价，很遗憾您报价的商品未中标。</p>")
                .append("<p>感谢您的参与，期待下次合作。</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("#/SupplierQuotation/List\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String seekStopToSupplierTemplate(String seekPriceNumber, String seekPriceTitle, String terminationReason) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>东风商城询价单终止提醒</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商，您好！</h2>")
                .append("<p>您参与报价的询价单【").append(seekPriceNumber).append("】，采购标题：【").append(seekPriceTitle).append("】已被取消。</p>")
                .append("<p>取消原因：").append(terminationReason).append("</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("#/SupplierQuotation/List\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String hondaJfGoodsSupplier(int upGoodsCount, int downGoodsCount) {
        StringBuilder sb = new StringBuilder();
        // 获取邮件内容
        sb.append("<p>尊敬的电商合作伙伴：</p>");
        sb.append("<p>&nbsp;&nbsp;您好！贵司在 东风商城-东本积分专区 的在售商品为" + upGoodsCount + "个，下架商品为" + downGoodsCount + "个（已提供下架原因），详见附件，如需再次上架，请参考下述商品下架规则调整：</p>");
        sb.append("<table border='1' cellspacing='0' cellpadding='0'>");
        sb.append("    <tr>");
        sb.append("          <td rowspan ='7' style='width:150px; ' >商品下架</td>");
        sb.append("          <td style='width:280px;'><span style='font-weight: bold;'>下架原因</span></td>");
        sb.append("          <td style='width:500px;'><span style='font-weight: bold;'>处理方法</span></td>");
        sb.append("    </tr>");
        sb.append("    <tr>");
        sb.append("          <td>商品价格接口获取失败</td>");
        sb.append("          <td>如需再次上架，请将商品调整至满足系统要求。<br/>");
        sb.append("              注：请于<span style='color:red;font-weight: bold;'>该SKU下架的5个自然日</span>（含周末及节假日）内（SKU下架时间为首次发送该SKU下架邮件的时间）处理完毕，<span style='color:red;font-weight: bold;'>系统自动处理上架</span>；如逾期未处理，需重新根据寻源流程执行上架； </td>");
        sb.append("    </tr>");
        sb.append("    <tr>");
        sb.append("          <td>供应商主动推送下架消息</td>");
        sb.append("          <td>如需再次上架，需重新根据寻源流程执行上架；</td>");
        sb.append("    </tr>");
        sb.append("    <tr>");
        sb.append("          <td>商品与标杆商品实际折扣（例：0.99）小于限制折扣0.92</td>");
        sb.append("          <td>如需再次上架，请将商品调整至满足系统要求。<br/>");
        sb.append("                注：请于<span style='color:red;font-weight: bold;'>该SKU下架的5个自然日</span>（含周末及节假日）内（SKU下架时间为首次发送该SKU下架邮件的时间）处理完毕，<span style='color:red;font-weight: bold;'>系统自动处理上架</span>；如逾期未处理，需重新根据寻源流程执行上架；</td>");
        sb.append("    </tr>");
        sb.append("    <tr>");
        sb.append("          <td>因商品所绑定的标杆商品已下架或不存在导致联动下架</td>");
        sb.append("          <td>如需再次上架，请寻找新的标杆SKU，并邮件联系（<span style='color:red;font-weight: bold;'>发送邮件时需附上“下架商品列表”，填写“电商调整”列</span>）蒋升阳（<EMAIL>) 进行上架。</td>");
        sb.append("    </tr>");
        sb.append("    <tr>");
        sb.append("          <td>商品协议价高于官网价</td>");
        sb.append("          <td>该SKU不支持上架，列入商品黑名单。</td>");
        sb.append("    </tr>");
        sb.append("    <tr>");
        sb.append("          <td>价格涨幅超过<允许涨幅></td>");
        sb.append("          <td>如需再次上架，请控制商品价格涨幅不超过<允许涨幅>。<br/>");
        sb.append("                注：请于<span style='color:red;font-weight: bold;'>该SKU下架的5个自然日</span>（含周末及节假日）内（SKU下架时间为首次发送该SKU下架邮件的时间）处理完毕，<span style='color:red;font-weight: bold;'>系统自动处理上架</span>；如逾期未处理，需重新根据寻源流程执行上架；</td>");
        sb.append("    </tr>");
        sb.append("</table>");
        sb.append("<p></p><p>&nbsp;&nbsp;以上，请知悉，如有问题，请及时联系，谢谢支持！</p>");
        return sb.toString();
    }

    public String companyStoreTemplate(String storeName) {
        StringBuilder content = new StringBuilder("<html><head><style>table,table tr td{border:1px solid #000;text-align:center;border-collapse:collapse;}</style></head><body><p>&emsp;&emsp; 【" + storeName + "】派送员发起一条客户拒收申请，请尽快前往 商城后台-企业服务-拒收件处理 核实并处理！</p>");
        content.append("</body></html>");
        return content.toString();
    }

    //价格举报处理结果通知客户
    public String priceAccusationProcessResult(String createTime, String sku, String result, String desc) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】价格举报处理结果</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户您好，</h2>")
                .append("<p>您于「").append(createTime).append("」，提出的sku为「").append(sku).append("」，价格举报，经过详细的审查和处理，审核结果如下：</p>")
                .append("<p>「").append(result).append("」</p>");
        if (StringUtils.isNotBlank(desc)) {
            sb.append("<p>「").append(desc).append("」</p>");
        }
        sb.append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p>我们非常重视您的意见和建议，如果需要进一步的帮助，请随时与我们联系。</p>")
                .append("<p>再次感谢您的支持与理解！祝您生活愉快！</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(frontUrl).append("\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    /**
     * @param count 数量
     * @return 模板
     */
    public String priceAccusationRemindJob(Long count) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>【东风商城】价格举报待审核通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的用户您好，</h2>")
                .append("<p>您有[" + count + "]条价格举报待处理，请尽快前往东风商城后台查看")
                .append("<p>点击下方“查看详情”登录东风商城查看。</p>")
                .append("<p style=\"text-align:center\"><a href=\"").append(adminUrl).append("\" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p>")
                .append("<p>祝商祺！</p></div></body></html>");
        return sb.toString();
    }

    public String getContractTemplate(String contractEndDate, String supplierName, String signCompany, String contractCode, String contractName) {
        // 将字符串转换为 LocalDate 对象
        LocalDateTime endDateTime = LocalDateTime.parse(contractEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDate endDate = endDateTime.toLocalDate();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算日期差
        long differenceInDays = ChronoUnit.DAYS.between(currentDate, endDate);

        return "<html>" +
                "<body>" +
                "<p>今日起"+differenceInDays+"天后（<strong>" + contractEndDate + "</strong>），<strong>" + supplierName + "</strong>与<strong>" + signCompany + "</strong>，合同即将到期，具体信息如下：</p>" +
                "<ul>" +
                "<li>合同编码：<strong>" + contractCode + "</strong></li>" +
                "<li>合同名称：<strong>" + contractName + "</strong></li>" +
                "</ul>" +
                "<p>请务必及时确认是否续约；若有续约，签约后请尽快前往【东风商城-我的企业】进行维护更新，以确保合作的连贯性，避免业务流程因合同问题出现中断或延误。如有任何疑问，请随时与我方联系。</p>" +
                "<p>祝商祺！</p>" +
                "</body>" +
                "</html>";
    }

    public String warehouseGoodsCleanTemplate(String contractCode,String contractName,JSONArray jsonArray){
        StringBuilder stringBuilder = new StringBuilder()
                .append("<title>合同失效-暂存仓库商品清理通知</title>")
                .append("<style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\">")
                .append("<h2>尊敬的供应商：</h2>")
                .append("<p>您好！</p>")
                .append("<p>根据系统自动化管理规则，贵司与我司签订的以下合同已失效，合同相关暂存仓库商品已触发系统自动清理：</p>")
                .append("<p>合同基本信息</p>")
                .append("<ul>")
                .append("<li>合同名称：[").append(contractName).append("]</li>")
                .append("<li>合同编号：[").append(contractCode).append("]</li>")
                .append("</ul>")
                .append("<p>涉及失效SKU清单</p>");
            for (int i = 0; i < jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                stringBuilder.append("<div>")
                        .append("<span>商品SKU：").append(jsonObject.getStr("goodsSku")).append("</span>")
                        .append("<span>商品名称：").append(jsonObject.getStr("goodsName")).append("</span>")
                        .append("</div>");
            }
        stringBuilder
                .append("<p>特别说明：本次清理仅涉及暂存仓库的商品，不影响任何正式发布商品（包括已上架/待上架商品），新合同生效后可重新发布商品暂存至仓库</p>")
                .append("<p>点击下方“查看详情”登录东风商城查看。")
                .append("</p><p style=\"text-align:center\"><a href=\"")
                .append(adminUrl)
                .append(" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return stringBuilder.toString();
    }

    public String getNoDfmallContractTemplate(String contractEndDate, String supplierName, String signCompany, String contractCode, String contractName) {
        return "<html>" +
                "<body>" +
                "<p>今日起45天后（<strong>" + contractEndDate + "</strong>），<strong>" + supplierName + "</strong>与<strong>" + signCompany + "</strong>，合同即将到期，具体信息如下：</p>" +
                "<ul>" +
                "<li>合同编码：<strong>" + contractCode + "</strong></li>" +
                "<li>合同名称：<strong>" + contractName + "</strong></li>" +
                "</ul>" +
                "<p>请务必及时确认是否续约；若有续约，签约后请尽快前往【东风商城-我的企业】新增合同，以确保合作的连贯性，避免如商品下架等业务流程中断或延误问题。</p>" +
                "<p>祝商祺！</p>" +
                "</body>" +
                "</html>";
    }

    public String getContractApprovalResultTemplate(String contractCode, String contractName,String companyName, int approvalState,String startDate) {
        String approval = approvalState == 1 ? "审批通过" : "审批驳回";
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城合同审批提醒</title><style>ul{display:flex;flex-direction:column;align-items:left;padding-left:0;list-style:none}li{display:flex;align-items:left;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:left;padding:20px;\"><h2>尊敬的供应商，您好！</h2>").
                append("<p>您有一个合同签约流程已审批,请查收</p>").
                append("<ul>").
                append("<li><span>合同编码：</span>" + contractCode + "</li>").
                append("<li><span>合同名称：</span>" + contractName + "</li>").
                append("<li><span>签约企业：</span>" + companyName + "</li>").
                append("<li><span>审批结果：</span>" + approval + "</li>").
                append("<li>【自动启用时间】：</li>").
                append("<li>若合同起始时间早于审批通过时间，系统将于审批通过当日["+LocalDate.now()+"]自动启用；</li>").
                append("<li>若合同起始时间晚于当前时间，系统将在合同起始日["+startDate+"]自动启用;</li>").
                append("<li>可登录【东风商城-合同管理】查看实时状态</li>").
                append("</ul>").
                append("<p>点击下方“查看详情”登录东风商城查看。").
                append("</p><p style=\"text-align:center\"><a href=\"").
                append(adminUrl).
                append(" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String getContractSignFailTemplate(String contractCode, String contractName,String companyName, String supplierName,String failReason) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城合同签署结果通知</title><style>ul{display:flex;flex-direction:column;align-items:left;padding-left:0;list-style:none}li{display:flex;align-items:left;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:left;padding:20px;\"><h2>"+supplierName+"，您好！</h2>").
                append("<p>您与"+companyName+"的电子合同签署失败</p>").
                append("<ul>").
                append("<li><span>合同编码：</span>" + contractCode + "</li>").
                append("<li><span>合同名称：</span>" + contractName + "</li>").
                append("<li><span>签约主体：</span>" + companyName + "</li>").
                append("<li><span>失败原因：</span>" + failReason + "</li>").
                append("</ul>").
                append("<p>您可联系商城采购经理咨询相关原因。</p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String getContractSignPassTemplate(String contractCode, String contractName,String companyName, String supplierName, String contractDate) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城合同签署结果通知</title><style>ul{display:flex;flex-direction:column;align-items:left;padding-left:0;list-style:none}li{display:flex;align-items:left;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:left;padding:20px;\"><h2>"+supplierName+"，您好！</h2>").
                append("<p>您与"+companyName+"的电子合同签署成功</p>").
                append("<ul>").
                append("<li><span>合同编码：</span>" + contractCode + "</li>").
                append("<li><span>合同名称：</span>" + contractName + "</li>").
                append("<li><span>签约主体：</span>" + companyName + "</li>").
                append("<li><span>合同有效期：</span>" + contractDate + "</li>").
                append("</ul>").
                append("<p>您可登录东风商城后台查看并开展相关业务。</p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String getContractApprovalPassTemplate(String contractCode, String contractName,String companyName, String supplierName) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城电子合同签署提醒</title><style>ul{display:flex;flex-direction:column;align-items:left;padding-left:0;list-style:none}li{display:flex;align-items:left;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:left;padding:20px;\"><h2>"+supplierName+"，您好！</h2>").
                append("<p>您有一份东风商城电子合同已发送至经办人短信或邮箱, 请留意相关短信或邮件, 并尽快完成签署</p>").
                append("<ul>").
                append("<li><span>合同编码：</span>" + contractCode + "</li>").
                append("<li><span>合同名称：</span>" + contractName + "</li>").
                append("<li><span>签约主体：</span>" + companyName + "</li>").
                append("</ul>").
                append("<p>您可联系商城采购经理咨询详情。</p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String getContractApprovalRejectTemplate(String contractCode, String contractName,String companyName, String supplierName, String failReason) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城电子合同审批驳回通知</title><style>ul{display:flex;flex-direction:column;align-items:left;padding-left:0;list-style:none}li{display:flex;align-items:left;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:left;padding:20px;\"><h2>"+supplierName+"，您好！</h2>").
                append("<p>您的东风商城电子合同审批驳回</p>").
                append("<ul>").
                append("<li><span>合同编码：</span>" + contractCode + "</li>").
                append("<li><span>合同名称：</span>" + contractName + "</li>").
                append("<li><span>签约主体：</span>" + companyName + "</li>").
                append("<li><span>驳回原因：</span>" + failReason + "</li>").
                append("</ul>").
                append("<p>您可联系商城采购经理咨询详情。</p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String getContractStartApprovalTemplate(String contractCode, String contractName,String companyName, String supplierName) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">").
                append("<title>东风商城合同审批提醒</title><style>ul{display:flex;flex-direction:column;align-items:center;padding-left:0;list-style:none}li{display:flex;align-items:center;margin-bottom:10px}li span{margin-left:10px;flex-grow:1}</style></head><body><div style=\"text-align:center;padding:20px;\"><h2>尊敬的用户，您好！</h2>").
                append("<p>有供应商发起合同签约审批流程,请查收并尽快处理</p>").
                append("<ul>").
                append("<li><span>合同编码：</span>" + contractCode + "</li>").
                append("<li><span>合同名称：</span>" + contractName + "</li>").
                append("<li><span>签约企业：</span>" + companyName + "</li>").
                append("<li><span>供应商名称：</span>" + supplierName + "</li>").
                append("</ul>").
                append("<p>点击下方“查看详情”登录东风商城查看。").
                append("</p><p style=\"text-align:center\"><a href=\"").
                append(adminUrl).
                append(" target=\"_self\"title=\"查看详情\">查看详情</a></p><p></p><p>祝商祺！</p><p>东风商城</p></div></body></html>\n");
        return sb.toString();
    }

    public String getQualificationTemplate(String endDate, String qualificationType, String qualificationName) {
        return "<html>" +
                "<body>" +
                "<p>今日起20天后（<strong>" + endDate + "</strong>），【<strong>" + qualificationType + "-" + qualificationName + "</strong>】即将到期，请尽快前往【东风商城-我的企业】进行维护更新，以确保合作的连贯性，避免相关业务收到影响。如有任何疑问，请随时与我方联系。</p>" +
                "<p>祝商祺！</p>" +
                "</body>" +
                "</html>";
    }

    public String getReminderPlatformTemplate(String supplierName, String signCompany, String contractCode, String contractName) {
        return "<html>" +
                "<body>" +
                "<p>[" + supplierName + "] 与 [" + signCompany + "] 已成功签约，请尽快前往供应商档案完善相关商业条款信息。</p>" +
                "<p><strong>合同编码：</strong> " + contractCode + "</p>" +
                "<p><strong>合同名称：</strong> " + contractName + "</p>" +
                "</body>" +
                "</html>";
    }


    public String getSeekPriceMonitorTempLv1(String title, JSONArray ary) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>").append(title).append("</title>")
                .append("<style>table{width:100%;border-collapse:collapse;margin:20px 0;font-size:18px;text-align:left}td,th{padding:12px;border:1px solid #ddd}th{background-color:#f1f1f1}tr:hover{background-color:#f1f1f1}.tac{text-align:center}.color-red{color:#d83931;font-weight:700}.index{width:80px}.over-days8{color:#d83931}</style></head><body><div style=\"padding:20px;\">")
                .append("<h2 class=\"tac\">尊敬的供应商：</h2>")
                .append("<p>您好！经平台核查，贵司中标的以下商品价格超出平台协议价，请务必配合核实并修正。详细信息如下：</p>")
                .append("<table class=\"tac\"><thead><tr><th class=\"index\">序号</th>")
                .append("<th>询价单号</th>")
                .append("<th>需求单位</th>")
                .append("<th>sku</th>")
                .append("<th>履约异常天数</th></tr></thead><tbody>");
        for (int i = 0; i < ary.size(); ) {
            JSONObject obj = ary.getJSONObject(i++);
            sb.append("<tr><td>").append(i).append("</td><td>")
                    .append(obj.getStr("seekPriceNumber")).append("</td><td>")
                    .append(obj.getStr("companyName")).append("</td><td>")
                    .append(obj.getStr("goodsSku")).append("</td><td>")
                    .append(obj.getStr("warnTimes")).append("</td></tr>");
        }
        sb.append("</tbody></table>");
        sb.append("<p><span class=\"color-red\">请于7个工作日内完成修正，</span>若异常状态持续≥7天（即第8日00:00起），平台将自动下调贵司信用评级，同步触发询价资格限制，届时贵司将无法参与平台任何询价。</p>");
        sb.append("<p>常见错误类型及对应解决方案如下：</p>");
        sb.append("<table><thead><tr><th class=\"index\">序号</th><th>错误场景</th><th>解决方案</th></tr></thead>");
        sb.append("<tbody><tr><td class=\"tac\">1</td><td class=\"tac\">SKU号填写错误</td>");
        sb.append("<td>1，提供正确SKU信息（格式：需求单位+正确SKU+对应询价单号）；2，发送邮件至运维邮箱 <EMAIL> 申请修正；注：平台将根据运维申请（预计3个工作日）次数进行考核扣分，请务必核对无误后提交。</td></tr>");
        sb.append("<tr><td class=\"tac\">2</td><td class=\"tac\">SKU单位与报价单位不一致</td>");
        sb.append("<td>新建与报价单位一致的SKU，参照错误1流程申请修正。（后续注意：报价时需按实际售卖单位报价，并在报价单中明确说明价格对应的规格。）</td></tr>");
        sb.append("<tr><td class=\"tac\">3</td><td class=\"tac\">价格错误</td>");
        sb.append("<td>直接在按照中标协议价修正，确保一致</td></tr></tbody></table>");
        sb.append("<p>感谢您的配合与支持！</p></div></body></html>");
        return sb.toString();
    }

    public String getSeekPriceMonitorTempLv2(String title, JSONArray ary) {
        StringBuilder sb = new StringBuilder("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">")
                .append("<title>").append(title).append("</title>")
                .append("<style>table{width:100%;border-collapse:collapse;margin:20px 0;font-size:18px;text-align:left}td,th{padding:12px;border:1px solid #ddd}th{background-color:#f1f1f1}tr:hover{background-color:#f1f1f1}.tac{text-align:center}.color-red{color:#d83931;font-weight:700}.index{width:80px}.over-days8{color:#d83931}</style></head><body><div style=\"padding:20px;\">")
                .append("<h2 class=\"tac\">尊敬的供应商：</h2>")
                .append("<p>您好！</p>")
                .append("<p>感谢贵司一直以来对我司平台的支持与配合，现就重要事项提示如下：</p>")
                .append("<p>经系统监测显示，贵司中标商品存在未按询价单价格履约，且异常状态已持续超过7个工作日。根据平台管理规则，若不及时处理，系统将于3个工作日后冻结贵司的参与寻源比价的权限。冻结期间，贵司将无法参与新的询价及报价流程，请务必重视！</p>")
                .append("<table class=\"tac\"><thead><tr><th class=\"index\">序号</th>")
                .append("<th>询价单号</th>")
                .append("<th>需求单位</th>")
                .append("<th>sku</th>")
                .append("<th>履约异常天数</th></tr></thead><tbody>");
        for (int i = 0; i < ary.size(); ) {
            JSONObject obj = ary.getJSONObject(i++);
            if (obj.getInt("warnTimes") == 8) {
                sb.append("<tr class=\"over-days8\"><td>");
            } else {
                sb.append("<tr><td>");
            }
            sb.append(i).append("</td><td>")
                    .append(obj.getStr("seekPriceNumber")).append("</td><td>")
                    .append(obj.getStr("companyName")).append("</td><td>")
                    .append(obj.getStr("goodsSku")).append("</td><td>")
                    .append(obj.getStr("warnTimes")).append("</td></tr>");
        }
        sb.append("</tbody></table><p>如有疑问，请联系供应链：郑经理（邮箱： <EMAIL>），我们将全力协助解决。</p></div></body></html>");
        return sb.toString();
    }

    /**
     * 过滤指定邮箱
     * @param sendTo
     * @return
     */
    private List<String> removeIgnoreEmails(List<String> sendTo) {
        log.info("需过滤的邮箱" + ignoreEmails);
        //排除指定nacos配置的邮箱不发送邮件
        if(StrUtil.isNotBlank(ignoreEmails)){
            List<String> split = StrUtil.split(ignoreEmails, ",", true, true);
            if (CollUtil.isNotEmpty(split)) {
                return CollUtil.subtractToList(sendTo, split);
            }
        }
        return sendTo;
    }
}
