package com.ly.yph.api.goods.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * spu商品发布 dto
 *
 * <AUTHOR>
 * @date 2025/6/6 11:37
 */
@Data
@ApiModel("spu商品发布 dto")
public class SpuGoodsSaveDto implements Serializable {
    private static final long serialVersionUID = -7307237386444816378L;

    @ApiModelProperty("商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    @ApiModelProperty("品牌ID")
    private String brandId;

    @ApiModelProperty("品牌名称")
    @NotBlank(message = "品牌名称不能为空")
    private String brandName;

    @ApiModelProperty("供应商型号")
    private String materialsCode = "";

    @ApiModelProperty("包装规格")
    private String specGoodsWareQd;

    @ApiModelProperty("税率")
    private Integer taxRate;

    @ApiModelProperty("税收分类编码")
    private String taxCode;

    @ApiModelProperty("销售单位")
    @NotBlank(message = "销售单位不能为空")
    @Length(max = 10, message = "销售单位过长")
    private String saleUnit;

    @ApiModelProperty("商品详情")
    private String goodsBody;

    @ApiModelProperty("商品分类(三级分类请逗号分隔)")
    @NotBlank(message = "商品分类不能为空")
    private String goodsClass;

    @ApiModelProperty("商品分类编码(三级分类编码请逗号分隔)")
    private String goodsClassCode;

    @ApiModelProperty("供应商编码")
    @NotBlank(message = "供应商编码不能为空")
    private String supplierCode;

    @ApiModelProperty("是否批量导入商品 0：否 1：是")
    private Integer isImportGoods = 0;

    @ApiModelProperty("特殊事项")
    private String specialEvent;

    //商品附带信息
    @ApiModelProperty("证明附件")
    private String fileUrl;

    @ApiModelProperty("商品来源 0 自主上新 1 寻比中标 2 同款竞价 3 合同上架")
    private Integer goodsSource;

    @ApiModelProperty("询价编号 逗号分隔")
    private String seekPriceNumbers;

    @ApiModelProperty("市场参考")
    private String marketReference;

    @ApiModelProperty("商品上架来源 1 电商 2 供应商创建 3 采购导入")
    private Integer goodsFrom;

    @ApiModelProperty("同款商品sku")
    private String sameGoodsSku;

    @ApiModelProperty("销售客户端 0:全端 1:B端 2:C端")
    private Integer saleClient;

    @ApiModelProperty(value = "提交模式 0立即上架 1仓库商品")
    private Integer submitType = 0;

    @ApiModelProperty(value = "热销榜单商品Id")
    private Long biddingHotSaleGoodsId;

    @ApiModelProperty("spu商品发布中sku数据")
    private List<SpuGoodsSkuSaveDto> spuGoodsSkuList;
}
