package com.ly.yph.api.flowable.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.yph.api.flowable.components.LiteFlowConfig;
import com.ly.yph.api.flowable.context.GoodsInfo;
import com.ly.yph.api.flowable.context.GoodsRuleContext;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.enums.GoodsLabelEnum;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.product.ext.common.dto.response.RemotePriceInfoResp;
import com.ly.yph.api.product.ext.common.dto.response.RemoteShelvesInfoResp;
import com.ly.yph.api.product.ext.common.manage.BatchPriceGetter;
import com.ly.yph.api.product.ext.common.manage.RemoteInfoManage;
import com.ly.yph.api.seeksource.service.ShopSeekQuotedSkuService;
import com.ly.yph.api.seeksource.vo.SeekQuotedSkuPriceVo;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.zone.entity.GoodsBlacklistEntity;
import com.ly.yph.api.zone.service.GoodsBlacklistService;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsInfoGetter {
    @Value("${application.batch-check-price.tenant-id}")
    private String needCheckTenantId;
    @Value("${application.batch-check-price.supplier}")
    private String needCheckSupper;
    @Resource
    private LiteFlowConfig liteFlowConfig;
    @Resource
    private ShopGoodsService goodsService;
    @Resource
    private BatchPriceGetter priceGetter;
    @Resource
    private ShopPurchaseSubOrderDetailService orderDetailService;
    @Resource
    private DfmallGoodsPoolSubService goodsPoolSubService;
    @Resource
    private GoodsBlacklistService blacklistService;
    @Resource
    private ShopGoodsUpNewService goodsUpNewService;
    @Resource
    private YphStandardClassService classService;
    @Resource
    private ShopSupplierClassService supplierClassSrv;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RemoteInfoManage remoteInfoManage;
    @Resource
    private ShopGoodsSaleLabelService goodsSaleLabelService;
    @Resource
    private ShopGoodsSaleLabelHistoryService labelHistoryService;
    @Resource
    private ShopGoodsContractService goodsContractService;
    @Resource
    private SystemOrganizationService organizationService;
    @Resource
    private ShopGoodsCertificationRecordService goodsCertificationRecordService;
    @Resource
    private YphGoodsPriceStrategyService goodsPriceStrategyService;
    @Resource
    private ShopSeekQuotedSkuService seekQuotedSkuService;

    /**
     * 获取商品原始价格信息
     * @param goodsCode 商品编码
     * @return 返回结果
     */
    public RemotePriceInfoResp getRemotePrice(String goodsCode) {
        return priceGetter.getRemotePrice(goodsCode, false);
    }

    /**
     * 获取商品信息
     * @param goodsCode 商品编码
     * @return 返回结果
     */
    public ShopGoods getGoodsInfo(String goodsCode) {
        return goodsService.selectOneByGoodsCode(goodsCode);
    }

    /**
     * 查询商品在指定时间内是否有销售记录
     * @param goodsCode 商品编码
     * @param days  天数
     * @return 返回结果
     */
    public boolean hasSaleInfo(String goodsCode, int days) {
        ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailService.getBaseMapper().selectFirstByGoodsCode(goodsCode, days);
        return shopPurchaseSubOrderDetail != null;
    }

    /**
     * 查询商品企业是否有销售记录
     * @param goodsCode 商品编码
     * @param companyOrgId  企业
     * @return 返回结果
     */
    public boolean getGoodsHistoryCount(String goodsCode, long companyOrgId) {
        Long count = orderDetailService.getGoodsHistoryCount(goodsCode, companyOrgId);
        return count > 0;
    }

    /**
     * 获取供应商商品上架数量
     * @param supplierCode 供应商编码
     * @param goodsPoolId   商品池ID
     * @return 返回结果
     */
    public Long getSupplierGoodsCount(String supplierCode,String goodsPoolId){
        return goodsPoolSubService.getBaseMapper().querySupplieCodeCount(supplierCode,Long.valueOf(goodsPoolId));
    }

    /**
     * 获取黑名单商品数据
     * @param goodsCode
     * @return
     */
        public Long getBlacklistServiceCount(String goodsCode,Long poolId){
        return blacklistService.count(new LambdaQueryWrapperX<GoodsBlacklistEntity>()
                .eq(GoodsBlacklistEntity::getGoodsCode,goodsCode)
                .eq(GoodsBlacklistEntity::getGoodsPoolId,poolId));
    }

    /**
     * 商品上新来源
     * @param goodsSku 商品SKU
     * @param supplierCode 供应商编码
     * @param createTime   限时N天之内上新来源
     * @return
     */
    public List<ShopGoodsUpNewEntity> getGoodsUpSource(String goodsSku,String supplierCode,String createTime){
        List<String> codes = new ArrayList<>();
        if(supplierCode.equals("DSJD002") || supplierCode.equals("JDIOP00")){
            codes.add("DSJD002");
            codes.add("JDIOP00");
        }else {
            codes.add(supplierCode);
        }
        LambdaQueryWrapper<ShopGoodsUpNewEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopGoodsUpNewEntity::getGoodsSku,goodsSku)
                .in(ShopGoodsUpNewEntity::getSupplierCode,codes)
                .ge(ShopGoodsUpNewEntity::getCreateTime,createTime)
                .groupBy(ShopGoodsUpNewEntity::getOrganizationId);
        return goodsUpNewService.list(queryWrapper);
    }

    /**
     * 根据编码，查询企业寻源比价
     * @param goodsSku 商品SKU
     * @param supplierCode 供应商编码
     * @param companyCode   企业编码
     * @return 返回结果
     */
    public List<ShopGoodsUpNewEntity> getCompanyCodeGoodsUpSource(String goodsSku,String supplierCode,String companyCode){
        LambdaQueryWrapper<ShopGoodsUpNewEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ShopGoodsUpNewEntity::getUpNewSource,ShopGoodsUpNewEntity::getOrganizationId,ShopGoodsUpNewEntity::getCompanyCode)
                .eq(ShopGoodsUpNewEntity::getGoodsSku,goodsSku)
                .eq(ShopGoodsUpNewEntity::getSupplierCode,supplierCode)
                .eq(ShopGoodsUpNewEntity::getCompanyCode,companyCode);
        return goodsUpNewService.list(queryWrapper);
    }

    /**
     * 根据ID，查询企业寻源比价
     * @param goodsSku 商品SKU
     * @param supplierCode 供应商编码
     * @param companyOrgId   企业组织ID
     * @return 返回结果
     */
    public List<ShopGoodsUpNewEntity> getCompanyIdGoodsUpSource(String goodsSku,String supplierCode,String companyOrgId){
        LambdaQueryWrapper<ShopGoodsUpNewEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ShopGoodsUpNewEntity::getUpNewSource,ShopGoodsUpNewEntity::getOrganizationId,ShopGoodsUpNewEntity::getCompanyCode)
                .eq(ShopGoodsUpNewEntity::getGoodsSku,goodsSku)
                .eq(ShopGoodsUpNewEntity::getSupplierCode,supplierCode)
                .eq(ShopGoodsUpNewEntity::getOrganizationId,companyOrgId);
        return goodsUpNewService.list(queryWrapper);
    }

    /**
     * 获取实体组织ID
     * @param orgId 组织ID
     * @return 返回实体组织信息
     */
    public SystemOrganization getEntityOrgId(Long orgId){
        return organizationService.findCompanyByOrganizationId(orgId);
    }

    /**
     * 设置商品可售标签
     * @param context 商品编码
     * @param authCode  认证方式
     * @param goodsLabel 商品标签
     */
    public void setLabel(GoodsRuleContext context, String authCode, String goodsLabel, Boolean canSale,Long orgId){
        GoodsInfo goodsInfo = context.getGoodsInfo();
        List<ShopGoodsSaleLabelEntity> goodsLabelList = goodsSaleLabelService.queryGoodsLabelByGoodsId(goodsInfo.getGoodsId());
        if(CollectionUtil.isEmpty(goodsLabelList)){
            goodsLabelList = new ArrayList<>();
        }
        List<String> labelList = goodsLabelList.stream().filter( label -> label.getOrganizationId().equals(orgId)).map(ShopGoodsSaleLabelEntity::getGoodsLabel).collect(Collectors.toList());

        List<ShopGoodsSaleLabelEntity> insertLabel = new ArrayList<>();
        List<Long> removeLabel = new ArrayList<>();
        if(canSale){
            //可售增加新的标签
            if(CollectionUtil.isEmpty(labelList) || !labelList.contains(goodsLabel)){
                ShopGoodsSaleLabelEntity labelEntity = new ShopGoodsSaleLabelEntity();
                labelEntity.setId(null);
                labelEntity.setAuthMode(authCode);
                labelEntity.setGoodsLabel(goodsLabel);
                labelEntity.setGoodsId(goodsInfo.getGoodsId());
                labelEntity.setOrganizationId(orgId);
                labelEntity.setIsGlobal(0);
                labelEntity.setIsEnable("1");
                if(goodsLabel.equals(GoodsLabelEnum.COMPANY_BI_JIA.getCode())){
                    // 企业寻源上新的,默认全局可售
                    labelEntity.setIsGlobal(1);
                }
                insertLabel.add(labelEntity);
            }
        }else {
            //不可售，去除指定标签
            Optional<Long> id = goodsLabelList.stream().filter(label ->
                    label.getGoodsLabel().equals(goodsLabel) && label.getOrganizationId().equals(orgId)
            ).map(ShopGoodsSaleLabelEntity::getId).findAny();
            id.ifPresent(removeLabel::add);
        }
        context.getInsertLabel().addAll(insertLabel);
        context.getRemoveLabel().addAll(removeLabel);
    }

    /**
     * 直接删除全部标签
     * @param goodsId 商品ID
     * @param labelCode 标签编码
     */
    public void delLabel(Long goodsId, List<String> labelCode){
        List<ShopGoodsSaleLabelEntity> labelEntityList = goodsSaleLabelService.list(new LambdaQueryWrapperX<ShopGoodsSaleLabelEntity>()
                .eq(ShopGoodsSaleLabelEntity::getGoodsId,goodsId)
                .in(ShopGoodsSaleLabelEntity::getGoodsLabel, labelCode));
        if(CollectionUtil.isNotEmpty(labelEntityList)){
            List<Long> ids = labelEntityList.stream()
                    .map(ShopGoodsSaleLabelEntity::getId)
                    .collect(Collectors.toList());

            labelHistoryService.copyLabelHistory(ids);

            goodsSaleLabelService.delByIds(ids);
        }

    }

    /**
     * 获取分类属性
     * @param classId 分类ID
     * @return 返回分类属性编码
     */
    public String getClassTypeCode(Long classId){
        YphStandardClassEntity classEntity = classService.getById(classId);
        return classEntity.getTypeCode();
    }

    /**
     * 获取京东商品标杆信息
     * @param goodsSku 商品SKU
     * @return 返回标杆信息
     */
    public boolean getJdGoodsStandard(String goodsSku){
        return true;
    }

    /**
     * 校验商品分类折扣
     * @param goodsCode
     * @param supplier
     * @return
     */
    public Boolean checkDiscount(String goodsCode, ShopSupplier supplier){
        TenantContextHolder.setTenantId(supplier.getTenantId());
        List<String> needCheckTenantIdList = Arrays.asList(needCheckTenantId.split(","));
        // 只有指定的供应商才校验折扣信息
        if (needCheckTenantIdList.contains(TenantContextHolder.getTenantId().toString())) {
            // 自有供应商不校验，特殊的openai的供应商同样需要校验
            if (shouldCheckDiscount(supplier)) {
                String msg = isDiscountValid(goodsService.selectOneByGoodsCode(goodsCode));
                return StrUtil.isBlank(msg);
            }
        }
        return true;
    }

    public Boolean getSupplierState(String goodsCode){
        RemoteShelvesInfoResp resp = remoteInfoManage.getSkuStatus(goodsCode);
        return resp.getIsShelves();
    }

    /**
     * 哪些供应商应该使用折扣校验
     *
     * @param supplier 供应商
     * @return boolean
     */
    private boolean shouldCheckDiscount(ShopSupplier supplier) {
        if (Arrays.asList(needCheckSupper.split(",")).contains(supplier.getSupplierCode())) {
            return true;
        }
        return supplier.getSupplierType().equals(0);
    }

    private String isDiscountValid(ShopGoods goods) {
        String msg = "";
        //验证价格是否超出合同标准
        var sClass = supplierClassSrv.selectForDiscountCheck(goods.getSupplierClass(), goods.getSupplierCode());

        if (sClass == null) {
            msg = StrUtil.format("{}:{}未查询到供应商协议折扣", goods.getGoodsCode(), goods.getSupplierClass());
            log.error(msg);
            return msg;
        }

        // 同一个商品上架多个商品池，避免价格重复查询，先查缓存
        RemotePriceInfoResp remotePrice = null;
        String remotePriceString = stringRedisTemplate.opsForValue().get("nowPrice_" + goods.getGoodsCode());
        if (StringUtils.isNotBlank(remotePriceString)) {
            remotePrice = JSONObject.parseObject(remotePriceString, RemotePriceInfoResp.class);
        }
        if (remotePrice == null) {
            try {
                remotePrice = priceGetter.getRemotePrice(goods.getGoodsCode());
            } catch (Exception e) {
                log.error("商品价格查询异常:" + e.getMessage());
                return "商品[" + goods.getGoodsCode() + "]实时价格获取失败";
            }
        }

        if (remotePrice == null || remotePrice.getGoodsPactPrice() == null || remotePrice.getGoodsPactPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return "商品[" + goods.getGoodsCode() + "]无价格";
        }

        stringRedisTemplate.opsForValue().set("nowPrice_" + goods.getGoodsCode(), JSONObject.toJSONString(remotePrice), 2, TimeUnit.SECONDS);

        BigDecimal discount = remotePrice.getGoodsPactPrice().divide(remotePrice.getGoodsOriginalPrice(), 2, RoundingMode.HALF_UP);
        if (discount.compareTo(sClass.getAgreementDiscount()) > 0) {
            //实际折扣超过分类上设置的折扣
            msg = StrUtil.format("超出分类设置折扣 实时折扣:{}, 分类折扣：{}", discount, sClass.getAgreementDiscount());
            log.error(msg);
            return msg;
        }
        return msg;
    }

    /**
     * 比较限额
     * @param price 用来比较的金额
     * @param maxPrice 限制金额
     * @return 返回结果
     */
    public boolean compareToMax(BigDecimal price, BigDecimal maxPrice){
        //本金大于0，并且未超过限制金额返回 true, 反之返回 false
        return price.compareTo(BigDecimal.ZERO) > 0 && price.compareTo(maxPrice) <= 0;
    }

    /**
     * 获取商品折扣率
     * @param goodsCode
     */
    public boolean getGoodsPriceDiscount(String goodsCode,BigDecimal discountLimit){
        // 同一个商品上架多个商品池，避免价格重复查询，先查缓存
        RemotePriceInfoResp remotePrice = null;
        String remotePriceString = stringRedisTemplate.opsForValue().get("nowPrice_" + goodsCode);
        if (StringUtils.isNotBlank(remotePriceString)) {
            remotePrice = JSONObject.parseObject(remotePriceString, RemotePriceInfoResp.class);
        }
        if (remotePrice == null) {
            try {
                remotePrice = priceGetter.getSalePrice(goodsCode);
            } catch (Exception e) {
                log.error("商品价格查询异常:" + e.getMessage());
            }
        }

        if (remotePrice == null || remotePrice.getGoodsPactPrice() == null || remotePrice.getGoodsPactPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        stringRedisTemplate.opsForValue().set("nowPrice_" + goodsCode, JSONObject.toJSONString(remotePrice), 2, TimeUnit.SECONDS);

        BigDecimal discount = remotePrice.getGoodsPactPrice().divide(remotePrice.getGoodsOriginalPrice(), 4, RoundingMode.HALF_UP);
        //实际折扣
        return discount.compareTo(discountLimit) < 0;

    }

    public boolean getGoodsCompanyContract(Long goodsId){
        return !goodsContractService.getBaseMapper().selectList(new LambdaQueryWrapperX<ShopGoodsContractEntity>()
                .eq(ShopGoodsContractEntity::getGoodsId, goodsId)
                .eq(ShopGoodsContractEntity::getOrgType, 2)
                .eq(ShopGoodsContractEntity::getAuditState, 1)).isEmpty();
    }

    public List<ShopGoodsSaleLabelEntity> queryGoodsLabel(Long goodsId){
        return goodsSaleLabelService.queryGoodsLabelByGoodsId(goodsId);
    }

    public ShopGoodsCertificationRecord queryGoodsCertificationRecord(Long goodsId){
        return goodsCertificationRecordService.getOne(new LambdaQueryWrapperX<ShopGoodsCertificationRecord>()
                .eq(ShopGoodsCertificationRecord::getGoodsId,goodsId)
                .eq(ShopGoodsCertificationRecord::getApprovedState,1)
                .orderByDesc(ShopGoodsCertificationRecord::getCreateTime)
                .last("limit 1"));
    }

  /** 根据询价单增加商品 */
  public void insertPriceStrategy(GoodsInfo goodsInfo) {
        if(liteFlowConfig.getLitePriceStrategySupplier().contains(goodsInfo.getSupplierCode())){

            SeekQuotedSkuPriceVo quotedSkuPriceVo = seekQuotedSkuService.queryQuotedSkuPrice(goodsInfo.getGoodsSku(),goodsInfo.getSupplierCode());
            YphGoodsPriceStrategy goodsPriceStrategy = new YphGoodsPriceStrategy();
            YphGoodsPriceStrategyLog strategyLogEntity = new YphGoodsPriceStrategyLog();

            goodsPriceStrategy.setCompanyOrganizationId("all");
            goodsPriceStrategy.setSupplierCode(goodsInfo.getSupplierCode());
            goodsPriceStrategy.setChangeType(2);
            goodsPriceStrategy.setRelationId(goodsInfo.getGoodsId().toString());
            goodsPriceStrategy.setGoodsSku(goodsInfo.getGoodsSku());
            goodsPriceStrategy.setGoodsPactNakedPrice(quotedSkuPriceVo.getGoodsPactNakedPrice());
            goodsPriceStrategy.setGoodsPactPrice(quotedSkuPriceVo.getGoodsPactPrice());
            goodsPriceStrategy.setBeforePrice(quotedSkuPriceVo.getGoodsSalePrice());
            goodsPriceStrategy.setAfterPrice(quotedSkuPriceVo.getGoodsSalePrice());
            goodsPriceStrategy.setIsFixed(1);
            goodsPriceStrategy.setValidityStart(DateUtils.format(quotedSkuPriceVo.getValidityTimeStart(),DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            goodsPriceStrategy.setValidityEnd(DateUtils.format(quotedSkuPriceVo.getValidityTimeEnd(),DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            goodsPriceStrategyService.saveGoodsPriceStrategy(goodsPriceStrategy,strategyLogEntity,"all");

        }

    }

}
