package com.ly.yph.api.product.ext.jd.integral.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ly.yph.api.companystore.service.CompanyStoreDeliveryService;
import com.ly.yph.api.customization.service.ShopDeliveryMsgService;
import com.ly.yph.api.goods.common.SupplierConstants;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.miniapp.miniappnotify.MiniAppNotifyService;
import com.ly.yph.api.openapi.v1.service.OpenDeliveryService;
import com.ly.yph.api.order.common.GoodsReturnServiceTypeEnum;
import com.ly.yph.api.order.common.GoodsReturnStateEnum;
import com.ly.yph.api.order.common.OrderNumberGenerator;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.dto.DeliveryValidatedDto;
import com.ly.yph.api.order.dto.ShopReturnDetailSaveDto;
import com.ly.yph.api.order.dto.ShopReturnSaveDto;
import com.ly.yph.api.order.entity.*;
import com.ly.yph.api.order.enums.DeliveryStateEnum;
import com.ly.yph.api.order.service.*;
import com.ly.yph.api.order.vo.DeliveryVo;
import com.ly.yph.api.order.vo.ShopPurchaseSubOrderVo;
import com.ly.yph.api.order.vo.ShopReturnDetailVo;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoPoolService;
import com.ly.yph.api.organization.controller.organization.vo.integral.IntegralReturnRespVO;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.enums.SmsSceneEnum;
import com.ly.yph.api.organization.service.SystemUsersService;
import com.ly.yph.api.product.ext.common.enums.MessageStatusEnum;
import com.ly.yph.api.product.ext.jd.dto.reponse.*;
import com.ly.yph.api.product.ext.jd.dto.request.GetSkuDetailInfoRequest;
import com.ly.yph.api.product.ext.jd.entity.JdShopAreaEntity;
import com.ly.yph.api.product.ext.jd.integral.config.JDIntegralConfig;
import com.ly.yph.api.product.ext.jd.integral.entity.BackupJdIntegralSkuidInfoEntity;
import com.ly.yph.api.product.ext.jd.service.JDRemoteProxy;
import com.ly.yph.api.product.ext.jd.service.JdGoodsPoolService;
import com.ly.yph.api.product.ext.jd.service.JdShopAreaService;
import com.ly.yph.api.product.ext.zkh.service.QueueMsgSupplierProductProcessInfoService;
import com.ly.yph.api.settlement.common.service.DeliveryForBillProcessService;
import com.ly.yph.api.settlement.common.service.SettleBillPoolService;
import com.ly.yph.api.system.entity.SystemSmsLogEntity;
import com.ly.yph.api.system.enums.ActProcTypeEnum;
import com.ly.yph.api.system.service.SystemSmsSendService;
import com.ly.yph.core.base.exception.types.DataRequestException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.mapper.SystemDictDataMapper;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * jd消息服务[福利专属京东账号]
 *
 * <AUTHOR>
 * @date 2022/03/08
 */
@Service
@Slf4j
@RefreshScope
public class JdIntegralMessageService {
    private static final String SUPPLIER_DELIVERY_TEMP = "admin-sms-supplier-delivery";
    @Resource
    private JDRemoteProxy proxy;
    @Value("${jd01.message.process.flag:0}")
    private Integer jd01MessageProcessFlag;
    @Resource
    private JDIntegralConfig config;
    @Resource
    private JdGoodsPoolService jdGoodsPoolService;
    @Resource
    private QueueMsgSupplierProductProcessInfoService queueSrv;
    @Resource
    private BackupJdIntegralGoodsService backSrv;
    @Resource
    private BackupJdIntegralSkuidInfoService skuSrv;
    @Resource
    private ShopDeliveryService shopDeliveryService;
    @Resource
    private ShopGoodsService goodSrv;
    @Resource
    @Lazy
    private JdIntegralMessageService self;
    @Resource
    private ShopPurchaseSubOrderDetailService shopPurchaseSubOrderDetailService;
    @Resource
    private ShopPurchaseSubOrderService shopPurchaseSubOrderService;
    @Resource
    private ShopReturnService returnService;
    @Resource
    private ShopDeliveryDetailService deliverySkuService;
    @Resource
    private ShopReturnDetailService returnDetailService;
    @Resource
    private ShopPurchaseSubOrderDetailService purchaseSubOrderDetailService;
    @Resource
    private ShopPurchaseOrderService purOrderSrv;
    @Resource
    private SystemUsersService usersSrv;
    @Resource
    private SystemSmsSendService smsSrv;
    @Resource
    private YflYamlConfig yflYamlConfig;
    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;
    @Resource
    private MiniAppNotifyService miniAppNotifyService;
    @Resource
    private JdShopAreaService jdShopAreaService;
    @Resource
    private ShopReturnService shopReturnService;
    @Resource
    private ShopReturnDetailService shopReturnDetailService;
    @Resource
    private OrderNumberGenerator orderNumberGenerator;
    @Resource
    private SettleBillPoolService billPoolSrv;
    @Resource
    private SystemDictDataMapper systemDictDataMapper;
    @Resource
    private MailService mailService;
    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;
    @Resource
    private ShopDeliveryMsgService shopDeliveryMsgService;
    @Resource
    private CompanyStoreDeliveryService companyStoreDelivererService;
    @Resource
    private OpenDeliveryService openDeliverySrv;
    @Resource
    private DeliveryForBillProcessService deliveryForBillProcessService;

    public void afterSaleServiceOrderStatus(final String msgContent) {
        final JSONObject msg = JSONUtil.parseObj(msgContent);
        final String orderId = msg.getStr("orderId");
        final String skuId = msg.getStr("skuId");
        // 服务单状态
        final Integer state = msg.getInt("state");
        // state状态：1：创建；2：审核不通过；3：审核取消；4：完成 5：待用户确认（调用服务单确认接口使服务单状态从用户确认状态变成完成）
        List<ShopReturn> shopReturnsList = this.returnService.selectBySupplierOrderId(orderId);
        if (CollectionUtil.isEmpty(shopReturnsList)) {
            JdIntegralMessageService.log.error("积分-在YPH未查询到京东退货数据，jd订单号：{}", orderId);
            return;
        }
        if (state == 2 || state == 3 || state == 4 || state == 5) {
            List<Long> shopReturnsIdList = shopReturnsList.stream().map(ShopReturn::getId).collect(Collectors.toList());
            Map<Long, ShopReturn> collect = shopReturnsList.stream().collect(Collectors.toMap(ShopReturn::getId, ShopReturn -> ShopReturn));
            List<ShopReturnDetail> shopReturnDetailList = this.returnDetailService.selectDetailByReturnId(shopReturnsIdList);
            for (ShopReturnDetail shopReturnDetail : shopReturnDetailList) {
                if (skuId.equals(shopReturnDetail.getGoodsSku())) {
                    ShopReturn returnEntity = collect.get(shopReturnDetail.getReturnId());
                    if (state == 5) {
                        //调用确认接口确认
                        proxy.confirmAfsOrder(returnEntity.getReturnCode(), Long.parseLong(orderId));
                        continue;
                    }
                    Integer status = 0;
                    ShopReturn shopReturn = new ShopReturn();
                    shopReturn.setId(shopReturnDetail.getReturnId());
                    shopReturn.setUpdateTime(new Date());
                    // 退货
                    if (shopReturnDetail.getServiceType() == GoodsReturnServiceTypeEnum.RETURN_GOODS.getCode()) {
                        if (state == 2 || state == 3) {
                            //退货失败
                            status = GoodsReturnStateEnum.RETURN_OF_FAILURE.getCode();
                        } else if (state == 4) {
                            //退货完成
                            status = GoodsReturnStateEnum.RETURN_COMPLETE.getCode();
                        }
                        //换货
                    } else if (shopReturnDetail.getServiceType() == GoodsReturnServiceTypeEnum.EXCHANGE_GOODS.getCode()) {
                        if (state == 2 || state == 3) {
                            //换货失败
                            status = GoodsReturnStateEnum.REPLACEMENT_FAILURE.getCode();
                        } else if (state == 4) {
                            //换货完成
                            status = GoodsReturnStateEnum.REPLACEMENT_COMPLETE.getCode();
                        }
                    }
                    shopReturn.setReturnState(status);
                    //修改退货表
                    this.returnService.updateById(shopReturn);
                    //修改订单详情表

                    UpdateWrapper<ShopPurchaseSubOrderDetail> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().set(ShopPurchaseSubOrderDetail::getDetailReturnState, status).eq(ShopPurchaseSubOrderDetail::getOrderId, returnEntity.getOrderId()).eq(ShopPurchaseSubOrderDetail::getGoodsSku, shopReturnDetail.getGoodsSku());
                    purchaseSubOrderDetailService.update(updateWrapper);
                }
            }
        }
    }

    /**
     * 处理消息，最多锁3分钟，一次执行3分钟后，另外的一个poll msg请求才能被处理
     *
     * @param msgTypes msg类型
     */
    @DistributedLock(value = "jd_integral_msg_poll", leaseTime = 180, waitLock = false)
    public void pollMessage(final List<Long> msgTypes) {
        val msgList = this.proxy.getMessage(msgTypes);
        // 根据消息类型，进行分组
        val msgMap = msgList.stream().collect(Collectors.groupingBy(JdGetMsgResp::getType));
        try {
            JdIntegralMessageService.log.info("integral_jd_message_push_log，msg_type：{}，msg_content：{}", JSON.toJSONString(msgMap.keySet()), JSON.toJSONString(msgMap));
        } catch (Exception ex) {
            log.error("integral_jdProcessMessageError:", JSON.toJSONString(msgList));
        }

        msgMap.forEach((key, value) -> {
            switch (key) {
                case 2:
                    // 价格变化消息
                    TenantUtils.executeIgnore(() -> {
                        try {
                            // 获取value中所有的sku
                            val skuList = value.stream().map(item -> JSONUtil.parseObj(item.getContent()).getStr("skuId")).collect(Collectors.toList());
                            if (skuList.size() == 0) {
                                return;
                            }
                            this.backSrv.getBaseMapper().updatePriceProcFlagAndSynchronizeAndValidateFlagBySkuIdIn(skuList);
                            val goodsCodeList = this.backSrv.getBaseMapper().selectGoodCodeBySkuIdIn(skuList);
                            if (goodsCodeList.size() == 0) {
                                return;
                            }
                            this.goodSrv.shopGoodsDown(goodsCodeList, "价格变化");
                        } catch (final Exception ex) {
                            log.error("JD integral Process_Price_Msg_Error", ex);
                        }
                    });
                    break;
                case 4:
                    // 商品上下架消息
                    TenantUtils.executeIgnore(() -> {
                        try {
                            // value 中数据根据状态类型进行分组
                            val skuMap = value.stream().collect(Collectors.groupingBy(item -> JSONUtil.parseObj(item.getContent()).getStr("state")));
                            skuMap.forEach((k, v) -> {
                                List<String> skuList = v.stream().map(item -> JSONUtil.parseObj(item.getContent()).getStr("skuId")).collect(Collectors.toList());
                                val goodsCodeList = this.backSrv.getBaseMapper().selectGoodCodeBySkuIdIn(skuList);
                                if ("1".equals(k)) {
                                    List<ShopGoods> goodsList = goodSrv.selectListByGoodsCode(goodsCodeList);
                                    Map<Integer, List<String>> goodsCodeMap = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getAuditState, Collectors.mapping(ShopGoods::getGoodsCode, Collectors.toList())));
                                    Map<Integer, List<ShopGoods>> goodsMap = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getAuditState));
                                    goodsCodeMap.forEach((auditState, codeList) -> {
                                        if (auditState == MessageStatusEnum.DONE.getCode()) {
                                            goodSrv.shopGoodsCodeUp(codeList, null);
                                        } else {
                                            goodSrv.supplierSubmitGoods(goodsMap.get(auditState));
                                        }
                                    });
                                } else {
                                    if (goodsCodeList.size() > 0) {
                                        this.goodSrv.shopGoodsDown(goodsCodeList, "供应商推送下架");
                                    }
                                }
                            });
                        } catch (final Exception ex) {
                            JdIntegralMessageService.log.error("JD integral Process_Shelves_Msg_Error", ex);
                        }
                    });
                    break;
                case 6:
                    skuSrv.getBaseMapper().deleteBySkuIdIn(value.stream().map(item -> JSONUtil.parseObj(item.getContent()).getStr("skuId")).collect(Collectors.toList()));
                    // 新增、删除商品
                    try {
                        val skuMap = value.stream().collect(Collectors.groupingBy(item -> JSONUtil.parseObj(item.getContent()).getStr("state")));
                        skuMap.forEach((k, v) -> {
                            List<String> skuList = v.stream().map(item -> JSONUtil.parseObj(item.getContent()).getStr("skuId")).collect(Collectors.toList());
                            if (skuList.size() == 0) {
                                return;
                            }
                            if ("1".equals(k)) {
                                if (jd01MessageProcessFlag == 1) {
                                    this.skuSrv.saveBatch(skuList.stream().map(item -> {
                                        val sEnt = new BackupJdIntegralSkuidInfoEntity();
                                        sEnt.setSkuId(item);
                                        sEnt.setIsProcess(false);
                                        return sEnt;
                                    }).collect(Collectors.toList()));
                                }
                            } else {
                                this.skuSrv.cleanGoods(skuList.toArray(new String[0]));
                            }
                        });
                    } catch (final Exception ex) {
                        JdIntegralMessageService.log.error("JD integral Process_Add_Or_Delete_Msg_Error", ex);
                    }
                    break;
                case 16:
                    val skuList = value.stream().map(item -> JSONUtil.parseObj(item.getContent()).getStr("skuId")).collect(Collectors.toList());
                    if (skuList.isEmpty()) {
                        return;
                    }
                    skuList.forEach(item -> {
                        try {
                            GetSkuDetailInfoRequest req = new GetSkuDetailInfoRequest();
                            req.setSkuId(Long.parseLong(item));
                            val skuDetail = jdGoodsPoolService.getSkuDetail(req);
                            queueSrv.getBaseMapper().updateInStorePendingBySku(item, JSONUtil.toJsonStr(skuDetail), config.getCode());
                        } catch (Exception ex) {
                            log.error("JD01获取商品{}详情出错:{}", item, ExceptionUtil.stacktraceToString(ex));
                        }
                    });

                    break;
                default:
                    break;
            }
        });

        for (final JdGetMsgResp msg : msgList) {
            final var mid = msg.getId();
            final var mType = msg.getType();
            final var mContent = msg.getContent();
            // 使用 log 来记录消息，方便后续查看

            boolean ignore = TenantContextHolder.isIgnore();
            switch (mType) {
                case 12:
                    // 配送单生成成功消息
                    try {
                        this.self.deliveryInfo(mContent);
                    } catch (final Exception e) {
                        JdIntegralMessageService.log.error("JD integral 处理配送单消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                    }
                    break;
                case 1:
                    break;
                case 5:
                    // 订单妥投消息
                    try {
                        this.self.jdOrderArrival(mContent);
                    } catch (Exception e) {
                        log.error("jd积分妥投处理异常 " + e );
                        JdIntegralMessageService.log.error("JD integral 处理订单妥投 消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                    }
                    break;
                case 10:
                    // 订单取消消息
                    try {
                        TenantContextHolder.setIgnore(true);
                        this.self.jdOrderCancel(mContent);
                    } catch (Exception e) {
                        List<SystemDictDataEntity> afterSaleEmails = systemDictDataMapper.selectListByDictType("after_sale_notification_emails");
                        List<String> emails = afterSaleEmails.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());
                        mailService.sendEmail(MailService.RETURN_AFTER_SALE, mailService.returnAfterSaleTemplate(mContent, ExceptionUtil.stacktraceToString(e)),emails , null);
                        JdIntegralMessageService.log.error("JD integral 处理订单取消消息 消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                    }
                    TenantContextHolder.setIgnore(ignore);
                    break;
                case 31:
                    // 订单完成消息
                    break;
                case 28:
                    //售后服务单状态变更
                    try {
                        this.self.afterSaleServiceOrderStatus(mContent);
                    } catch (Exception e) {
                        JdIntegralMessageService.log.error("JD integral 处理售后服务单状态变更 消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                    }
                    break;
                case 104:
                    //售后申请单环节变更消息
                    TenantUtils.executeIgnore(() -> {
                        try {
                            this.self.AfterSingleStatusChanges(mContent);
                        } catch (Exception e) {
                            JdIntegralMessageService.log.error("JD integral 处理售后申请单环节变更消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                        }
                    });
                    break;
                case 119:
                    //申请单维度售后退款完成消息
                    TenantUtils.executeIgnore(() -> {
                        try {
                            this.self.afterSingleResult(mContent);
                        } catch (Exception e) {
                            JdIntegralMessageService.log.error("JD integral 申请单维度售后退款完成消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                        }
                    });
                    break;
                case 50:
                    //京东地址变更消息
                    try {
                        this.self.jdAddressChanges(mContent);
                    } catch (Exception e) {
                        JdIntegralMessageService.log.error("JD integral 京东地址变更消息时失败：stack:{}", ExceptionUtil.stacktraceToString(e));
                    }
                    break;
                default:
                    break;
            }
            this.proxy.deleteMessage(new Long[]{mid});
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void jdOrderCancel(String mContent) {
        // {"orderId":300542695575,"state":0}   {"orderId":300542695575,"state":1}
        log.info("开始处理京东integral取消消息，消息内容：{}", mContent);
        final JSONObject msg = JSONUtil.parseObj(mContent);
        //客户售后申请单号
        String jdOrderId = msg.getStr("orderId");
        Integer state = msg.getInt("state");
        if (state != null && state == 0) {
            //取消失败的, 不处理后续流程
            return;
        }
        String originalJdOrderId = jdOrderId;
        List<ShopPurchaseSubOrderDetail> orderDetailByOrderId = new ArrayList<>();
        BigDecimal orderFreightPrice = BigDecimal.ZERO;
        ShopPurchaseSubOrder shopPurchaseSubOrder = shopPurchaseSubOrderService.queryOrderBySupplierOrderId(jdOrderId, SupplierConstants.JD_INTEGRAL_CODE);
        if (shopPurchaseSubOrder == null) {
            //拆过单 是子订单号
            //找对应订单信息
            List<JdOrderDetailResp> jdOrderDetailResps = proxy.orderDetail("", Long.valueOf(jdOrderId));
            if (CollectionUtil.isEmpty(jdOrderDetailResps)) {
                log.info("此积分订单不在商城内,{}", jdOrderId);
                return;
            }
            JdOrderDetailResp jdOrderDetailResp = jdOrderDetailResps.get(0);
            Long parentJdOrderId = jdOrderDetailResp.getParentJdOrderId();
            List<JdOrderDetailResp.SkuList> skuInfoList = jdOrderDetailResp.getSkuInfoList();

            if (parentJdOrderId == null) {
                return;
            }
            List<ShopReturn> shopReturns = shopReturnService.selectBySupplierOrderId(parentJdOrderId.toString());
            HashMap<String, BigDecimal> goodsNumMap = new HashMap<>();
            shopReturns.forEach(e -> {
                List<ShopReturnDetail> shopReturnDetails = shopReturnDetailService.queryReturnDetailByReturnId(e.getId());
                shopReturnDetails.forEach(data -> {
                    String goodsSku = data.getGoodsSku();
                    //已有退货数量
                    BigDecimal num = goodsNumMap.get(goodsSku) == null ? BigDecimal.ZERO : goodsNumMap.get(goodsSku);
                    goodsNumMap.put(goodsSku, data.getReturnNum().add(num));
                });
            });
            shopPurchaseSubOrder = shopPurchaseSubOrderService.queryOrderBySupplierOrderId(parentJdOrderId.toString(), SupplierConstants.JD_INTEGRAL_CODE);
            if (shopPurchaseSubOrder.getOrderFreightPrice().compareTo(BigDecimal.ZERO) > 0) {
                orderFreightPrice = jdOrderDetailResp.getOrderPrice().getOrderTotalFreight();
            }
            if (shopPurchaseSubOrder.getOrderState() <= 10) {
                log.info("此积分订单状态不能售后,{}", shopPurchaseSubOrder.getOrderNumber());
                return;
            }
            Boolean isSave = false;
            for (JdOrderDetailResp.SkuList sku : skuInfoList) {
                String goodsSku = sku.getSkuId().toString();
                ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = shopPurchaseSubOrderDetailService.getByOrderNumberAndGoodsSku(shopPurchaseSubOrder.getOrderNumber(), goodsSku);
                if (shopPurchaseSubOrderDetail == null){
                    continue;
                }
                BigDecimal confirmNum = new BigDecimal(shopPurchaseSubOrderDetail.getConfirmNum()).add(shopPurchaseSubOrderDetail.getConfirmNumDecimal());
                if (goodsNumMap.get(goodsSku) != null && goodsNumMap.get(goodsSku).compareTo(confirmNum) >= 0) {
                    isSave = true;
                }
                shopPurchaseSubOrderDetail.setConfirmNum(sku.getSkuNum().longValue());
                orderDetailByOrderId.add(shopPurchaseSubOrderDetail);
            }
            if (isSave) {
                log.info("此积分明细已售后完成,不能重复售后");
                return;
            }
            //子订单号替换为父订单号
            jdOrderId = parentJdOrderId.toString();
        } else {
            //未拆单
            if (shopPurchaseSubOrder.getOrderState() <= 10) {
                //已提交的单子走超时审核取消,这边不走售后
                log.info("此积分订单状态不能售后,{}", shopPurchaseSubOrder.getOrderNumber());
                return;
            }
            orderFreightPrice = shopPurchaseSubOrder.getOrderFreightPrice();
            orderDetailByOrderId = shopPurchaseSubOrderDetailService.getOrderDetailByOrderId(shopPurchaseSubOrder.getOrderId());
            List<ShopReturn> shopReturns = shopReturnService.selectBySupplierOrderId(jdOrderId);
            if (CollectionUtil.isNotEmpty(shopReturns)) {
                log.info("积分已存在售后单,{}", jdOrderId);
                return;
            }
        }
        final String returnCode = orderNumberGenerator.make();
        ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(shopPurchaseSubOrder.getPurchaseNumber());
        ArrayList<ShopReturnSaveDto> shopReturnSaveDtos = new ArrayList<>();
        ShopReturnSaveDto saveDto = DataAdapter.convert(shopPurchaseSubOrder, ShopReturnSaveDto.class);
        //查询包裹
        ShopDelivery shopDelivery = shopDeliveryService.selectByPackageId(originalJdOrderId);
        if (shopDelivery != null) {
            saveDto.setDeliveryId(shopDelivery.getId());
        }

        Long tenantId = shopPurchaseSubOrder.getTenantId();
        if (tenantId.equals(yflYamlConfig.getTenantId())) {
            saveDto.setReturnType(1);
        } else {
            saveDto.setReturnType(0);
        }
        ArrayList<ShopReturnDetailSaveDto> shopReturnDetailSaveDtos = new ArrayList<>();
        orderDetailByOrderId.stream().forEach(e -> {
            ShopReturnDetailSaveDto shopReturnDetailSaveDto = DataAdapter.convert(e, ShopReturnDetailSaveDto.class);
            BigDecimal confirmNum = new BigDecimal(e.getConfirmNum()).add(e.getConfirmNumDecimal());
            shopReturnDetailSaveDto.setReturnNum(confirmNum);
            shopReturnDetailSaveDto.setConfirmNum(new BigDecimal(e.getApplyNum()));
            shopReturnDetailSaveDto.setConfirmNumDecimal(e.getConfirmNumDecimal());
            shopReturnDetailSaveDto.setRemark("京东订单取消,仅退款");
            shopReturnDetailSaveDtos.add(shopReturnDetailSaveDto);
        });
        BigDecimal returnAllNum = shopReturnDetailSaveDtos.stream().map(ShopReturnDetailSaveDto::getReturnNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        saveDto.setReturnCode(returnCode);
        saveDto.setReturnFreightState(orderFreightPrice.compareTo(BigDecimal.ZERO) > 0 ? 1 : 0);
        saveDto.setReturnReason("京东订单取消,仅退款");
        saveDto.setReturnFreight(orderFreightPrice.compareTo(BigDecimal.ZERO) > 0 ? orderFreightPrice : BigDecimal.ZERO);
        saveDto.setReturnAllPriceTax(shopPurchaseSubOrder.getOrderPriceTax().add(orderFreightPrice));
        saveDto.setReturnAllNum(returnAllNum);
        saveDto.setPurchaseOrderDetailList(shopReturnDetailSaveDtos);
        saveDto.setPurchaseCreateTime(shopPurchaseSubOrder.getCreateTime());
        saveDto.setSupplierOrderId(jdOrderId);
        saveDto.setApplyUserId(shopPurchaseOrder.getApplyUserId());
        saveDto.setApplyUserName(shopPurchaseOrder.getApplyUserName());
        saveDto.setCompanyName(shopPurchaseOrder.getCompanyName());
        saveDto.setCreateTime(new Date());
        saveDto.setUpdateTime(new Date());
        shopReturnSaveDtos.add(saveDto);
        TenantUtils.execute(tenantId, () -> {
            shopReturnService.returnSave(shopReturnSaveDtos);
            if (tenantId.equals(yflYamlConfig.getTenantId())) {
                shopReturnService.confirmRefund(returnCode);
                ShopReturn shopReturn = shopReturnService.queryByReturnCode(returnCode);
                if (shopReturn.getReturnAllMoneyTax().compareTo(BigDecimal.ZERO) > 0) {
                    shopReturnService.createNewActTask(returnCode, ActProcTypeEnum.AFTER_SALES_REQUEST.getCode());
                }
            }
        });
    }

    private void afterSingleResult(final String mContent) {
        log.info("积分开始处理申请单维度售后退款完成消息，消息内容：{}", mContent);
        final JSONObject msg = JSONUtil.parseObj(mContent);
        //客户售后申请单号
        final String thirdApplyId = msg.getStr("outApplyId");

        //退款总额
        final BigDecimal refundAmount = msg.getBigDecimal("refundAmount");
        if (StrUtil.isBlank(thirdApplyId)) {
            return;
        }
        if (refundAmount == null) {
            return;
        }
        //更新售后单
        returnService.systemSalesSupplierAmount(thirdApplyId, refundAmount);
    }

    public void jdAddressChanges(final String mContent) {
        JdAddressChangeResp jdAddressChangeResp = JSONUtil.toBean(mContent, JdAddressChangeResp.class);
        log.info("开始处理京东积分发货消息，消息内容：{}", jdAddressChangeResp);
        Integer operateType = jdAddressChangeResp.getOperateType();
        JdShopAreaEntity jdShopAreaEntity = DataAdapter.convert(jdAddressChangeResp, JdShopAreaEntity.class);
        jdShopAreaEntity.setAreaParentId(jdAddressChangeResp.getParentId()).setAreaDeep(jdAddressChangeResp.getAreaLevel() - 1).setAreaSort(0).setTenantId(1).setIsEnable("1").setCreator("system").setCreateTime(new Date()).setModifier("system").setUpdateTime(new Date());
        switch (operateType) {
            case 1:
                //插入
                this.jdShopAreaService.save(jdShopAreaEntity);
                break;
            case 2:
                //更新
                this.jdShopAreaService.update(jdShopAreaEntity);
                break;
            case 3:
                //删除
                this.jdShopAreaService.delete(jdAddressChangeResp.getAreaId());
                break;
            default:
                JdIntegralMessageService.log.error("积分暂时不支持此操作类型处理");
        }
    }

    /**
     * 配送单生成成功消息
     *
     * @param msgContent 配送单消息
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(value = "DSJD001_msg_delivery", leaseTime = 120)
    public void deliveryInfo(String msgContent) {
        final JSONObject jsonObject = JSONUtil.parseObj(msgContent);
        log.info("开始处理京东积分发货消息，消息内容：{}", jsonObject);
        TenantUtils.executeIgnore(() -> {
            final Long jdOrderId = Long.parseLong(jsonObject.get("orderId").toString());
            // 获取订单详情
            final List<JdOrderDetailResp> jdOrderDetailResps = this.proxy.orderDetail("", jdOrderId);
            if (CollectionUtils.isEmpty(jdOrderDetailResps)) {
                JdIntegralMessageService.log.error("jd积分获取订单详情失败，京东订单号：{}", jdOrderId);
                return;
            }
            // 获取物流单号
            final JdLogisticsResp jdDeliveryInfoQueryOpenResp = this.proxy.deliveryDetail("", jdOrderId);
            if (jdDeliveryInfoQueryOpenResp == null) {
                JdIntegralMessageService.log.error("积分jd获取物流单号失败，京东订单号：{}", jdOrderId);
                return;
            }
            final JdOrderDetailResp orderOpenResp = jdOrderDetailResps.get(0);
            //批量修改订单状态
            Set<ShopPurchaseSubOrder> updateOrderSet = new HashSet<>();
            Set<ShopDelivery> shopDeliverySet = new HashSet<>();
            List<Long> deliveryIds = new ArrayList<>();
            //批量修改商品详情状态
            Set<ShopPurchaseSubOrderDetail> updateOrderDetailSet = new HashSet<>();
            String dsOrderId;
            if (orderOpenResp.getParentJdOrderId() == null || orderOpenResp.getParentJdOrderId() == 0) {
                //没有拆单
                dsOrderId = orderOpenResp.getJdOrderId().toString();
            } else {
                dsOrderId = orderOpenResp.getParentJdOrderId().toString();
            }
            //根据供应商单号查询订单号
            final ShopPurchaseSubOrder orderEntity = this.shopPurchaseSubOrderService.queryOrderBySupplierOrderId(dsOrderId, this.config.getCode());
            if (orderEntity == null) {
                JdIntegralMessageService.log.error("积分根据供应商单号未查询到YPH订单：{}", dsOrderId);
                return;
            }
            //查询订单详情表数据
            final List<ShopPurchaseSubOrderDetail> orderDetailList = this.shopPurchaseSubOrderDetailService.getOrderDetailByOrderId(orderEntity.getOrderId());
            if (CollectionUtil.isEmpty(orderDetailList)) {
                JdIntegralMessageService.log.error("积分获取YPH订单详情失败，YPH单号：{}", orderEntity.getOrderId());
                return;
            }

            //查询已经发货的商品数量
            Map<String,Integer> oldDeliveryNumMap = shopDeliveryService.queryDeliverySkuNum(orderEntity.getOrderId());

            final Map<String, ShopPurchaseSubOrderDetail> orderDetailEntity = orderDetailList.stream().collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getGoodsSku, ShopPurchaseSubOrderDetailEntity -> ShopPurchaseSubOrderDetailEntity));
            final List<JdOrderDetailResp.SkuList> skuInfoList = orderOpenResp.getSkuInfoList();
            if (jdDeliveryInfoQueryOpenResp.getLogisticInfoList().size() > 0) {
                jdDeliveryInfoQueryOpenResp.getLogisticInfoList().forEach(itm -> {
                    //子订单号
                    Long jdChildOrder = itm.getJdOrderId();
                    //父订单号
                    String parentJdOrderId;
                    if (itm.getParentJdOrderId() == null || itm.getParentJdOrderId() == 0) {
                        //没有拆单
                        parentJdOrderId = itm.getJdOrderId().toString();
                    } else {
                        parentJdOrderId = itm.getParentJdOrderId().toString();
                    }

                    List<DeliveryValidatedDto> validatedDtoList = new ArrayList<>();
                    skuInfoList.forEach(d -> {
                        if (d.getType() == 0) {
                            DeliveryValidatedDto dto = new DeliveryValidatedDto();
                            dto.setGoodsSku(String.valueOf(d.getSkuId()));
                            dto.setDeliveryNum(d.getSkuNum());
                            validatedDtoList.add(dto);
                        }
                    });
                    String errMsg = shopDeliveryService.validateDelivery(config.getCode(), jdChildOrder.toString(), orderEntity.getOrderId(), validatedDtoList);
                    if (StrUtil.isNotBlank(errMsg)) {
                        throw new DataRequestException(errMsg);
                    }

                    //根据子订单号获取
                    ShopDelivery shopDelivery = shopDeliveryService.selectByPackageId(jdChildOrder.toString());
                    //没有这个子订单物流信息就保存
                    if (shopDelivery == null) {
                        ShopDelivery deliveryEntity = new ShopDelivery();
                        String deliveryCode = itm.getDeliveryOrderId();
                        String deliveryName = itm.getDeliveryCarrier();
                        deliveryEntity.setPackageId(jdChildOrder.toString()).setSupplierOrderId(parentJdOrderId).setSupplierCode(this.config.getCode()).setSupplierShortName(this.config.getName()).setOrderId(orderEntity.getOrderId()).setOrderNumber(orderEntity.getOrderNumber()).setDeliveryTime(new Date())
                                // 物流单号为空则视为厂家直送
                                .setDeliveryCode(StrUtil.isBlank(deliveryCode) ? "厂家直送" : deliveryCode).setDeliveryName(StrUtil.isBlank(deliveryName) ? "厂家直送" : deliveryName).setSupDeliveryState(0).setCusReceivingState(0).setOrganizationId(orderEntity.getOrganizationId()).setTenantId(orderEntity.getTenantId());
                        orderEntity.setOrderState(30);
                        shopDeliverySet.add(deliveryEntity);
                        updateOrderSet.add(orderEntity);
                        //保存到物流表
                        this.shopDeliveryService.save(deliveryEntity);
                        //保存到企配仓包裹表
                        companyStoreDelivererService.saveByDelivery(deliveryEntity,true);
                        deliveryIds.add(deliveryEntity.getId());
                        // 保存业务消息供拉取
                        this.shopDeliveryService.buildDeliveryBusMes(Collections.singleton(deliveryEntity.getId()));
                        //保存物流明细
                        List<ShopDeliveryDetail> skuList = new ArrayList<>();
                        skuInfoList.forEach(skuInfo -> {
                            //赠品附件发货单不保存信息 0普通、1附件、2赠品、3延保
                            if (skuInfo.getType() != 0) {
                                return;
                            }
                            final ShopDeliveryDetail sEn = new ShopDeliveryDetail();
                            ShopPurchaseSubOrderDetail orderDetail;
                            orderDetail = orderDetailEntity.get(skuInfo.getSkuId().toString());
                            if (orderDetail != null) {
                                BeanUtil.copyProperties(orderDetail, sEn);

                                if(null != oldDeliveryNumMap && null != oldDeliveryNumMap.get(skuInfo.getSkuId().toString())){
                                    int oldDeliveryNum = oldDeliveryNumMap.get(skuInfo.getSkuId().toString());
                                    if(oldDeliveryNum >= orderDetail.getConfirmNum().intValue() ){
                                        log.error("商品 {} 已经发货,此单重复推送 {}", skuInfo.getSkuId() , deliveryEntity.getDeliveryCode());
                                        return;
                                    }
                                }

                                sEn.setDeliveryId(deliveryEntity.getId()).setGoodsSku(skuInfo.getSkuId().toString()).setDeliveryNum(skuInfo.getSkuNum()).setTenantId(orderDetail.getTenantId());

                                sEn.setSupplierTotalPriceTax(orderDetail.getSupplierUnitPriceTax().multiply(new BigDecimal(skuInfo.getSkuNum())).setScale(2, RoundingMode.HALF_UP));
                                sEn.setSupplierTotalPriceNaked(orderDetail.getSupplierUnitPriceNaked().multiply(new BigDecimal(skuInfo.getSkuNum())).setScale(2, RoundingMode.HALF_UP));
                                //未税单价 * 数量
                                BigDecimal goodsTotalPriceNaked = orderDetail.getGoodsUnitPriceNaked().multiply(new BigDecimal(skuInfo.getSkuNum())).setScale(2, RoundingMode.HALF_UP);
                                sEn.setGoodsTotalPriceNaked(goodsTotalPriceNaked);
                                //税率
                                BigDecimal taxRate = new BigDecimal(orderDetail.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));
                                //未税单价 * 税率 * 数量
                                BigDecimal goodsTotalPriceTax = orderDetail.getGoodsUnitPriceNaked().multiply(taxRate).multiply(new BigDecimal(skuInfo.getSkuNum())).setScale(2, RoundingMode.HALF_UP);
                                sEn.setGoodsTotalPriceTax(goodsTotalPriceTax);

                                orderDetail.setOrderDetailState(30);
                                skuList.add(sEn);
                                updateOrderDetailSet.add(orderDetail);
                            }
                        });
                        if (CollectionUtil.isEmpty(skuList)) {
                            throw new ParameterException("此京东消息为赠品或附件商品不保存发货单！");
                        }
                        this.deliverySkuService.saveBatch(skuList);

                        //发送订阅消息
                        if (yflYamlConfig.getTenantId().equals(orderEntity.getTenantId())) {
                            try {
                                ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(orderEntity.getPurchaseNumber());
                                String applyUserId = shopPurchaseOrder.getApplyUserId();
                                SystemUsers user = usersSrv.getUser(Convert.toLong(applyUserId));
                                if (user != null) {
                                    Integer sum = skuList.stream().mapToInt(ShopDeliveryDetail::getDeliveryNum).sum();
                                    TenantUtils.execute(orderEntity.getTenantId(), () -> {
                                        miniAppNotifyService.sendOrderDeliveryNotice(applyUserId, user.getNotifyInfo(), orderEntity.getOrderNumber(), deliveryEntity.getDeliveryCode(), sum, deliveryEntity.getDeliveryTime());
                                    });
                                }
                            } catch (Exception e) {
                                log.error("积分发送订阅消息异常", e);
                            }
                        }
                        skuList.clear();
                        shopDeliveryMsgService.saveDeliveryMsg(Collections.singletonList(deliveryEntity.getId()));
                    }
                });
                //修改订单状态
                this.shopPurchaseSubOrderService.updateBatchById(updateOrderSet);
                this.shopPurchaseSubOrderDetailService.updateBatchById(updateOrderDetailSet);
                //更新订单明细生命周期
                deliveryForBillProcessService.deliveryInfoProcess(deliveryIds);
                // 接口平台推送发货消息
                deliveryIds.forEach(id-> openDeliverySrv.pushDelivery(id));
                if (yflYamlConfig.getTenantId().equals(orderEntity.getTenantId())) {
                    try {
                        sendNoteMsg(orderEntity.getPurchaseNumber(), orderEntity.getOrderNumber(), orderEntity.getOrderId());
                    } catch (Exception e) {
                        log.error("积分发送短信异常", e);
                    }
                } else {
                    sendMsg(shopDeliverySet, orderEntity);
                }
            }
        });
    }

    /**
     * 友福利发货信息提醒 收货人与申请人电话不一致,发给收货人,只发一次提醒
     *
     * @param purchaseNumber
     * @param orderNumber
     * @param orderId
     */
    private void sendNoteMsg(String purchaseNumber, String orderNumber, String orderId) {
        //获取申请人电话
        ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(purchaseNumber);
        String applyUserPhone = shopPurchaseOrder.getApplyUserPhone();
        //获取收货人电话
        ShopOrderAddress shopOrderAddress = shopPurchaseOrderService.selectOrderAddress(purchaseNumber);
        String mobPhone = shopOrderAddress.getMobPhone();
        if (applyUserPhone.equals(mobPhone)) {
            return;
        }
        String templateCode = SmsSceneEnum.getCodeByScene(40).getTemplateCode();
        //是否需要发送短信
        List<SystemSmsLogEntity> list = smsSrv.querySendLog(templateCode, mobPhone, orderNumber);
        if (CollectionUtil.isNotEmpty(list)) {
            return;
        }
        List<DeliveryVo> deliveryVos = shopDeliveryService.queryDeliveryByOrderId(orderId);
        if (CollectionUtil.isEmpty(deliveryVos)) {
            return;
        }
        String deliveryCode = deliveryVos.get(0).getDeliveryCode();
        Map<String, Object> templateParams = new HashMap<>(3);
        templateParams.put("orderNum", orderNumber);
        templateParams.put("trackNum", deliveryCode);
        // 短信功能已切换联麓,这些模板当前禁用状态,若需启用需重新开发
//        smsSrv.sendSingleSms(mobPhone, Convert.toLong(shopPurchaseOrder.getApplyUserId()), UserTypeEnum.MEMBER.getValue(), templateCode, templateParams);
    }

    // 短信通知
    private void sendMsg(Set<ShopDelivery> shopDeliverySet, ShopPurchaseSubOrder orderEntity) {
        shopDeliverySet.forEach(delivery -> {
            TenantUtils.execute(orderEntity.getTenantId(), () -> {
                String purchaseNumber = orderEntity.getPurchaseNumber();
                ShopPurchaseOrder shopPurchaseOrder = purOrderSrv.queryPurOrderByPurNumber(purchaseNumber);
                if (shopPurchaseOrder == null) {
                    return;
                }
                try {
                    SystemUsers user = usersSrv.getUserByUsername(shopPurchaseOrder.getApplyEmpCode());
                    Map<String, Object> templateParams = new HashMap<>(3);
                    templateParams.put("orderNo", purchaseNumber);
                    templateParams.put("deliveryNo", delivery.getDeliveryCode());
                    templateParams.put("deliverCompany", delivery.getDeliveryName());
                    // 短信功能已切换联麓,这些模板当前禁用状态,若需启用需重新开发
//                    smsSrv.sendSingleSmsToAdmin(user.getMobile(), user.getId(), SUPPLIER_DELIVERY_TEMP, templateParams);
                    smsSrv.emailForSupplierDelivery(user, templateParams);
                } catch (Exception e) {
                    log.error("短信通知失败", e);
                }
            });
        });
    }

    public void jdOrderArrival(final String msgContent) {
        JdIntegralMessageService.log.info("积分 开始处理京东妥投消息：消息内容:{}", msgContent);
        final JSONObject jsonObject = JSONUtil.parseObj(msgContent);
        final String jdOrderId = jsonObject.getStr("orderId");
        final Integer state = jsonObject.getInt("state");

        TenantUtils.executeIgnore(() -> {
            final QueryWrapper<ShopDelivery> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopDelivery::getPackageId, jdOrderId).eq(ShopDelivery::getSupplierCode, this.config.getCode());
            //妥投是子订单维度，查询包裹号（子订单号）
            final ShopDelivery beanDeliveryEntity = this.shopDeliveryService.getOne(queryWrapper);
            if (beanDeliveryEntity == null) {
                JdIntegralMessageService.log.error("积分发货表中没有这条京东数据：京东子订单号:{}", jdOrderId);
                return;
            }
            ShopPurchaseSubOrderVo shopPurchaseSubOrderVo = shopPurchaseOrderService.querySubOrderInfo(beanDeliveryEntity.getOrderNumber());
            //1是妥投，2是拒收
            if (state == 1) {
                if(beanDeliveryEntity.getRealDeliveryState().equals(DeliveryStateEnum.DELIVERY_OK.getCode())){
                    JdIntegralMessageService.log.error("重复推送,单据已经妥投：京东子订单号:{}", jdOrderId);
                    return;
                }

                beanDeliveryEntity.setUpdateTime(new Date());
                //查询订单详情，获取订单完成时间
                final List<JdOrderDetailResp> jdOrderDetailResps = this.proxy.orderDetail(beanDeliveryEntity.getOrderNumber(), Long.parseLong(jdOrderId));
                if (CollectionUtil.isEmpty(jdOrderDetailResps)) {
                    JdIntegralMessageService.log.error("JD 积分 获取订单详情时失败：YPH订单号:{}", beanDeliveryEntity.getOrderNumber());
                    return;
                }
                final JdOrderDetailResp jdOrderDetailResp = jdOrderDetailResps.get(0);
                final Date orderFinishTime = jdOrderDetailResp.getOrderFinishTime();
                log.info("JD 积分获取订单详情 {}", jdOrderDetailResps);
                //企配仓来咯 走企配仓服务的不维护这俩个字段, 不走企配仓服务还是原来逻辑
                if (shopPurchaseSubOrderVo.getIsCompanyStore() == 0) {
                    beanDeliveryEntity.setSupReceivingTime(orderFinishTime == null ? new Date() : orderFinishTime);
                    beanDeliveryEntity.setSupDeliveryState(1);
                }
                beanDeliveryEntity.setRealDeliveryState(1)
                        .setRealReceivingTime(orderFinishTime == null ? new Date() : orderFinishTime);
                this.shopDeliveryService.updateById(beanDeliveryEntity);
                //更新订单明细生命周期
                deliveryForBillProcessService.deliveryOrderArriveProcess(beanDeliveryEntity.getId());
                // 开放平台妥投消息
                openDeliverySrv.pushDeliveryRealReceivingTime(beanDeliveryEntity.getId(), beanDeliveryEntity.getRealReceivingTime());
                //发送微信通知
                try {
                    String applyUserId = shopPurchaseSubOrderVo.getApplyUserId();
                    SystemUsers user = usersSrv.getUser(Long.valueOf(applyUserId));
                    if (user != null) {
                        //发送微信通知和确认京东订单
                        TenantUtils.execute(user.getTenantId(), () -> {
                            if (this.yflYamlConfig.getTenantId().equals(user.getTenantId())) {
                                this.proxy.confirmReceive(beanDeliveryEntity.getPackageId(), beanDeliveryEntity.getOrderNumber());
                            }
                            miniAppNotifyService.sendOrderSignNotice(applyUserId, user.getNotifyInfo(), beanDeliveryEntity.getOrderNumber(), beanDeliveryEntity.getDeliveryName(), beanDeliveryEntity.getDeliveryCode(), orderFinishTime, shopPurchaseSubOrderVo.getAddressUserName());
                        });
                    }
                } catch (Exception e) {
                    log.error("积分发送订阅消息异常", e);
                }
            } else if (state == 2) {
                shopDeliveryService.cusRejection(CollectionUtil.toList(beanDeliveryEntity.getId()));
            }
        });
    }

    public void AfterSingleStatusChanges(final String msgContent) {
        final JSONObject msg = JSONUtil.parseObj(msgContent);
        final String orderId = msg.getStr("orderId");
        //状态
        final Integer state = msg.getInt("applyStep");
        //售后申请单号
        final String thirdApplyId = msg.getStr("thirdApplyId");
        //获取这条售后数据
        IntegralReturnRespVO integralReturnRespVO = returnService.returnInfo(thirdApplyId);
        if (integralReturnRespVO == null) {
            log.error("售后单不存在,京东售后申请单号：{} , 京东积分消息：{}", thirdApplyId, msgContent);
            return;
        }
        Integer oldReturnState = integralReturnRespVO.getReturnState();
        //消息可能无序消费,过滤流程之前的状态覆盖流程之后的状态的情况
        List<Integer> list = new ArrayList<>();
        list.add(GoodsReturnStateEnum.RETURN_COMPLETE.getCode());
        list.add(GoodsReturnStateEnum.RETURN_OF_FAILURE.getCode());
        list.add(GoodsReturnStateEnum.REPLACEMENT_COMPLETE.getCode());
        list.add(GoodsReturnStateEnum.REPLACEMENT_FAILURE.getCode());
        list.add(GoodsReturnStateEnum.CANCEL_COMPLETE.getCode());
        if (state == 10) {
            ShopReturn shopReturn = new ShopReturn();
            shopReturn.setId(integralReturnRespVO.getReturnId());
            shopReturn.setAuditTime(new Date());
            //修改退货表
            returnService.updateById(shopReturn);
        }
        if (oldReturnState > state || list.contains(oldReturnState)) {
            return;
        }
        //获取售后详情
        List<ShopReturnDetailVo> shopReturnDetailList = integralReturnRespVO.getShopReturnDetailList();
        Integer returnState = 0;
        for (ShopReturnDetailVo shopReturnDetailVo : shopReturnDetailList) {
            //10:申请 20:审核 30:收货 40:处理 50:待用户确认 60:完成 70:取消
            if (state == 50) {
                //确认售后完成
                proxy.confirmAfsOrder(thirdApplyId, Long.parseLong(orderId));
                return;
            }
            //退货
            if (shopReturnDetailVo.getServiceType() == GoodsReturnServiceTypeEnum.RETURN_GOODS.getCode()) {
                if (state == 60) {
                    Boolean result = false;
                    HashMap<String, BigDecimal> map = new HashMap<>();
                    for (int i = 0; i < shopReturnDetailList.size(); i++) {
                        ShopReturnDetailVo shopReturnDetail = shopReturnDetailList.get(i);
                        Long skuId = Long.valueOf(shopReturnDetail.getGoodsSku());
                        //查询售后概述,判断是否全部通过
                        var res = this.proxy.afterSalesProgress(thirdApplyId, Long.valueOf(orderId), skuId);
                        if (res == null || res.size() == 0) {
                            return;
                        }
                        JdWareSummaryInfoOpenResp.WareSummaryInfo wareSummaryInfo = res.getJSONObject(0).getBean("wareSummaryInfo", JdWareSummaryInfoOpenResp.WareSummaryInfo.class);
                        BigDecimal wareCompleteNum = new BigDecimal(wareSummaryInfo.getWareCompleteNum());
                        BigDecimal applyNum = new BigDecimal(wareSummaryInfo.getApplyNum());
                        BigDecimal realRefundAmount = wareSummaryInfo.getRealRefundAmount();


                        if (wareCompleteNum.compareTo(BigDecimal.ZERO) == 0 || wareSummaryInfo.getAfsServiceType() != 10 || realRefundAmount.compareTo(BigDecimal.ZERO)  == 0) {
                            //完成数量为0 退款金额为0 最新期望售后类型不是退款
                            //创建审批流
                            returnService.createNewActTask(thirdApplyId, ActProcTypeEnum.AFTER_SALES_REQUEST_RECONFIRM.getCode());
                            return;
                        }

                        //更新售后单
                        if (realRefundAmount != null) {
                            returnService.systemSalesSupplierAmount(thirdApplyId, realRefundAmount);
                        }

                        if (wareCompleteNum.compareTo(applyNum) < 0) {
                            result = true;
                        }
                        map.put(skuId + "", wareCompleteNum);
                    }
                    if (result) {
                        returnService.updateReturnNum(map, thirdApplyId);
                    }
                    returnState = GoodsReturnStateEnum.RETURN_COMPLETE.getCode();
                    //开始退积分退微信
                    returnService.confirmRefund(thirdApplyId);
                    if (shopReturnDetailVo.getReturnTotalMoneyTax().compareTo(BigDecimal.ZERO) > 0) {
                        returnService.createNewActTask(thirdApplyId,ActProcTypeEnum.AFTER_SALES_REQUEST.getCode());
                    }
                } else if (state == 70) {
                    returnState = GoodsReturnStateEnum.RETURN_OF_FAILURE.getCode();
                } else {
                    returnState = state;
                }
                //换货
            } else if (shopReturnDetailVo.getServiceType() == GoodsReturnServiceTypeEnum.EXCHANGE_GOODS.getCode()) {
                if (state == 60) {
                    returnState = GoodsReturnStateEnum.REPLACEMENT_COMPLETE.getCode();
                } else if (state == 70) {
                    returnState = GoodsReturnStateEnum.REPLACEMENT_FAILURE.getCode();
                } else {
                    returnState = state;
                }
            }
            //修改订单详情表
            UpdateWrapper<ShopPurchaseSubOrderDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(ShopPurchaseSubOrderDetail::getDetailReturnState, returnState).eq(ShopPurchaseSubOrderDetail::getOrderId, integralReturnRespVO.getOrderId()).eq(ShopPurchaseSubOrderDetail::getGoodsSku, shopReturnDetailVo.getGoodsSku());
            purchaseSubOrderDetailService.update(updateWrapper);
        }
        ShopReturn shopReturn = new ShopReturn();
        shopReturn.setId(integralReturnRespVO.getReturnId());
        shopReturn.setUpdateTime(new Date());
        shopReturn.setReturnState(returnState);
        //修改退货表
        returnService.updateById(shopReturn);
    }
}
