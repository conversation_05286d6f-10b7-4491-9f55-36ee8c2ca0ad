package com.ly.yph.api.openapi.v1;

import cn.hutool.json.JSONObject;
import com.ly.yph.api.goods.workflow.ContractWorkFlowHandler;
import com.ly.yph.api.honda.aop.HondaInterfaceLog;
import com.ly.yph.api.openapi.v1.dto.ESignCallBackDto;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.organization.service.SystemContractRenewalService;
import com.ly.yph.api.system.service.FileService;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;

@Slf4j
@Api(value = "电子签约回调接口", tags = {"电子签约回调接口"})
@RestController
@RequestMapping("/v1/eSign")
public class ESignCallBackController {

    @Resource
    private SystemContractRenewalService systemContractRenewalService;

    @Resource
    private ContractWorkFlowHandler contractWorkFlowHandler;

    @Resource
    private FileService fileService;

    @RequestMapping("/callBack")
    @DataPermission(enable = false)
    @ApiOperation(value = "电子签约回调接口", httpMethod ="POST")
    @HondaInterfaceLog(url = "v1/eSign/callBack")
    public JSONObject callBack(@RequestBody ESignCallBackDto dto){

        log.info("生态聚合中心合同签署结果回调数据：{}", dto);
        JSONObject result = new JSONObject();
        result.set("errCode", 1);
        result.set("msg", "未找到合同数据");

        if("SIGN_FLOW_FINISH".equalsIgnoreCase(dto.getAction())){
            SystemContractRenewalEntity entity = systemContractRenewalService.getById(Long.valueOf(dto.getBizNo()));
            if(entity!=null){
                entity.setSignTime(new Date());
                if(dto.getStatus()==2){
                    String eFilePath = StringEscapeUtils.unescapeHtml4(dto.getFinishDocUrlBeans().get(0).getDownloadDocUrl());
                    String filePath = getFileUrl(eFilePath, entity.getSupplierName()+"_"+entity.getContractCode());
                    entity.setFinishFileKey(dto.getFinishDocUrlBeans().get(0).getFinishFileKey());
                    entity.setAttachmentUrl(filePath);
                    entity.setIsSync(1);
                }
                entity.setSignDesc(dto.getResultDescription());
                entity.setSignStatus(dto.getStatus());

                systemContractRenewalService.updateById(entity);
                systemContractRenewalService.sendSignEmail(entity);
                if(dto.getStatus()==2){
                    contractWorkFlowHandler.saveContract(entity);
                }
                // 保存
                result.set("errCode", 0);
                result.set("code", 0);
                result.set("msg", "回调成功");
            }
        }
        return result;
    }

    private String getFileUrl(String fileUrl, String fileName){
        try{
            fileName = "平台入驻商家通则协议_" + fileName+".pdf";
            URL url = new URL(fileUrl);
            HttpURLConnection httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            try(InputStream is = httpUrl.getInputStream();
                    ByteArrayOutputStream outStream  = new ByteArrayOutputStream();){
                byte[] buffer = new byte[1024];
                //每次读取的字符串长度，如果为-1，代表全部读取完毕
                int len = 0;
                //使用输入流从buffer里把数据读取出来
                while ((len = is.read(buffer)) != -1) {
                    //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                    outStream.write(buffer, 0, len);
                }
                return fileService.uploadFileByByte(fileName,  outStream.toByteArray());
            }catch (Exception e){
                log.error("文件上传失败", e);
                return fileUrl;
            }
        }catch (Exception e){
            log.error("文件上传失败", e);
            return fileUrl;
        }
    }
}
