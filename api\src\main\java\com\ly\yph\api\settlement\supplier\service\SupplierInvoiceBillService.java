package com.ly.yph.api.settlement.supplier.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.settlement.common.entity.SettleBillLifeCycle;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.entity.SettleShopBillDetail;
import com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail;
import com.ly.yph.api.settlement.common.enums.*;
import com.ly.yph.api.settlement.common.service.SettleShopBillDetailService;
import com.ly.yph.api.settlement.common.service.SettleShopBillPostageDetailService;
import com.ly.yph.api.settlement.common.service.SettleShopBillService;
import com.ly.yph.api.settlement.supplier.dto.*;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBillDetail;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceDetailPostage;
import com.ly.yph.api.settlement.supplier.factory.SupplierInvoiceFactory;
import com.ly.yph.api.settlement.supplier.factory.SupplierInvoiceStrategy;
import com.ly.yph.api.settlement.supplier.mapper.SupplierInvoiceBillMapper;
import com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceBillVo;
import com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceQueryVo;
import com.ly.yph.api.system.enums.ActProcTypeEnum;
import com.ly.yph.api.system.feign.ActTaskFeign;
import com.ly.yph.api.system.service.FileService;
import com.ly.yph.api.system.util.WorkflowUtils;
import com.ly.yph.api.utils.PdfboxUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupplierInvoiceBillService extends ServiceImpl<SupplierInvoiceBillMapper, SupplierInvoiceBill> {

    @Resource
    private SupplierInvoiceBillDetailService supplierInvoiceBillDetailService;
    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private FileService fileService;
    @Resource
    private SupplierInvoiceDetailPostageService supplierInvoiceDetailPostageService;
    @Resource
    private SupplierInvoiceFactory supplierInvoiceFactory;
    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;
    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;
    @Resource
    private ActTaskFeign actTaskFeign;
    @Resource
    private WorkflowUtils workflowUtils;
    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;


    @Transactional
    public String excelInvoice(MultipartFile file, SupplierInvoiceApplyDto supplierInvoiceApplyDto) throws IOException {
        SettleShopBill settleShopBill = checkSupplierBillAndOperator(supplierInvoiceApplyDto.getBillId());
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceApplyDto.getBillInvoiceType());
        return supplierInvoiceStrategy.excelInvoice(file, settleShopBill);
    }


    @Transactional
    public String allDetailInvoice(SupplierInvoiceApplyDto supplierInvoiceApplyDto) {
        SettleShopBill settleShopBill = checkSupplierBillAndOperator(supplierInvoiceApplyDto.getBillId());
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceApplyDto.getBillInvoiceType());
        return supplierInvoiceStrategy.allDetailInvoice(settleShopBill);
    }

    @Transactional
    public String supplierInvoiceByDetailIds(SupplierInvoiceByDetailIdDto supplierInvoiceByDetailIdDto) {
        SettleShopBill settleShopBill = checkSupplierBillAndOperator(supplierInvoiceByDetailIdDto.getBillId());
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceByDetailIdDto.getBillInvoiceType());
        return supplierInvoiceStrategy.supplierInvoiceByDetailIds(supplierInvoiceByDetailIdDto, settleShopBill);
    }

    /**
     * 校验供应商和账单的匹配
     *
     * @param billId 账单id
     * @return
     */
    public SettleShopBill checkSupplierBillAndOperator(Long billId) {
        SettleShopBill settleShopBill = settleShopBillService.getById(billId);
        if (settleShopBill == null) {
            throw new ParameterException("未查询到账单！");
        }

        LoginUser loginUser = LocalUserHolder.get();
        if (!settleShopBill.getCustomerCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())) {
            throw new ParameterException("您无权限处理此供应商账单数据！");
        }
        return settleShopBill;
    }

    /**
     * 生成供应商发票数据
     *
     * @param supplierInvoiceDetailDtoList
     */
    @Transactional
    public void doSupplierInvoiceBill(List<SupplierInvoiceDetailDto> supplierInvoiceDetailDtoList, SettleShopBill settleShopBill) {

        List<SupplierInvoiceBillDetail> supplierInvoiceBillDetailList = DataAdapter.convertList(supplierInvoiceDetailDtoList, SupplierInvoiceBillDetail.class);
        BigDecimal totalPriceNaked = supplierInvoiceBillDetailList.stream().map(SupplierInvoiceBillDetail::getGoodsTotalPriceNaked).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPriceTax = supplierInvoiceBillDetailList.stream().map(SupplierInvoiceBillDetail::getGoodsTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add);

        String invoiceApplyNumber = "SFP" + DateUtil.format(new Date(), "yyMMddHHmmssSSS") + RandomUtil.randomInt(10000, 99999);

        SupplierInvoiceBill supplierInvoiceBill = getSupplierInvoiceBill(totalPriceNaked, totalPriceTax, invoiceApplyNumber,
                settleShopBill, BillInvoiceTypeEnum.BILL_DETAIL.getCode());


        supplierInvoiceBillDetailList.forEach(e -> {
            e.setInvoiceId(supplierInvoiceBill.getId());
            e.setCreator(supplierInvoiceBill.getCreator());
        });

        supplierInvoiceBillDetailService.saveBatch(supplierInvoiceBillDetailList);

        List<SettleShopBillDetail> settleShopBillDetailList = new ArrayList<>();
        List<SettleBillLifeCycle> lifeCycleList = new ArrayList<>();
        for (SupplierInvoiceDetailDto supplierInvoiceDetailDto : supplierInvoiceDetailDtoList) {

            SettleShopBillDetail settleShopBillDetail = new SettleShopBillDetail();
            settleShopBillDetail.setDetailId(supplierInvoiceDetailDto.getBillDetailId());
            settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode());
            settleShopBillDetail.setInvoicedQuantity(supplierInvoiceDetailDto.getInvoiceNum());
            settleShopBillDetail.setInvoicableQuantity(BigDecimal.ZERO);
            settleShopBillDetailList.add(settleShopBillDetail);

            SettleBillLifeCycle settleBillLifeCycle = new SettleBillLifeCycle();

            settleBillLifeCycle.setId(supplierInvoiceDetailDto.getLifeCycleId());
            settleBillLifeCycle.setSupplierInvoiceMoney(supplierInvoiceDetailDto.getGoodsTotalPriceTax());
            settleBillLifeCycle.setSupplierInvoicingNum(supplierInvoiceDetailDto.getInvoiceNum());
            settleBillLifeCycle.setSupplierInvoicingTime(new Date());
            lifeCycleList.add(settleBillLifeCycle);
        }

        // 更新账单明细账单对账状态,账单生命周期
        Lists.partition(settleShopBillDetailList, 1000).forEach(
                subList -> supplierInvoiceBillDetailService.getBaseMapper().updateSupplierBillDetailForInvoiceApply(subList)
        );

        Lists.partition(lifeCycleList, 1000).forEach(
                subList -> supplierInvoiceBillDetailService.getBaseMapper().updateSupplierBillLifeCycleForInvoiceApply(subList)
        );
    }

    private SupplierInvoiceBill getSupplierInvoiceBill(BigDecimal totalPriceNaked,
                                                       BigDecimal totalPriceTax,
                                                       String invoiceApplyNumber,
                                                       SettleShopBill settleShopBill,
                                                       Integer billInvoiceType) {

        SupplierInvoiceBill supplierInvoiceBill = new SupplierInvoiceBill();
        supplierInvoiceBill.setAmountTax(totalPriceTax);
        supplierInvoiceBill.setAmountNaked(totalPriceNaked);
        supplierInvoiceBill.setSupplierCode(settleShopBill.getCustomerCode());
        supplierInvoiceBill.setSupplierName(settleShopBill.getCustomerName());
        supplierInvoiceBill.setCreator(LocalUserHolder.get().getUsername());
        supplierInvoiceBill.setCreateTime(new Date());
        supplierInvoiceBill.setBillId(settleShopBill.getBillId());
        supplierInvoiceBill.setBillSn(settleShopBill.getBillSn());
        supplierInvoiceBill.setState(SupplierInvoiceStateEnum.NOT_INVOICED.getCode());
        supplierInvoiceBill.setInvoiceApplyNumber(invoiceApplyNumber);
        supplierInvoiceBill.setBillInvoiceType(billInvoiceType);
        supplierInvoiceBill.setApplyId(LocalUserHolder.get().getId());
        supplierInvoiceBill.setApplyName(LocalUserHolder.get().getNickname());
        save(supplierInvoiceBill);
        return supplierInvoiceBill;
    }


    @Transactional
    public void doSupplierInvoiceBillForPostage(List<SettleShopBillPostageDetail> settleShopBillPostageDetailList, SettleShopBill settleShopBill) {

        List<SupplierInvoiceDetailPostage> supplierInvoiceDetailPostageList = new ArrayList<>();

        BigDecimal amountTax = BigDecimal.ZERO;
        BigDecimal amount = BigDecimal.ZERO;
        for (SettleShopBillPostageDetail shopBillPostageDetail : settleShopBillPostageDetailList) {

            SupplierInvoiceDetailPostage supplierInvoiceDetailPostage = new SupplierInvoiceDetailPostage();
            supplierInvoiceDetailPostage.setPostageDetailId(shopBillPostageDetail.getBillDetailPostageId());
            supplierInvoiceDetailPostage.setPostage(shopBillPostageDetail.getPostage());

            amountTax = amountTax.add(shopBillPostageDetail.getPostage());
            BigDecimal reallyTaxRate = new BigDecimal(shopBillPostageDetail.getTaxRate()).multiply(new BigDecimal("0.01")).add(BigDecimal.ONE);

            amount = amount.add(shopBillPostageDetail.getPostage().divide(reallyTaxRate, 2, BigDecimal.ROUND_HALF_UP));
            supplierInvoiceDetailPostageList.add(supplierInvoiceDetailPostage);
        }

        String invoiceApplyNumber = "SFPP" + DateUtil.format(new Date(), "yyMMddHHmmssSSS") + RandomUtil.randomInt(10000, 99999);

        SupplierInvoiceBill supplierInvoiceBill = getSupplierInvoiceBill(amount, amountTax, invoiceApplyNumber, settleShopBill, BillInvoiceTypeEnum.POSTAGE_DETAIL.getCode());


        supplierInvoiceDetailPostageList.forEach(e -> {
            e.setInvoiceId(supplierInvoiceBill.getId());
            e.setCreateTime(new Date());
            e.setCreator(LocalUserHolder.get().getUsername());
        });

        supplierInvoiceDetailPostageService.saveBatch(supplierInvoiceDetailPostageList);

        Lists.partition(settleShopBillPostageDetailList.stream().map(SettleShopBillPostageDetail::getBillDetailPostageId).collect(Collectors.toList()), 1000)
                .forEach(subList -> {
                            UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
                            updateWrapper.lambda().in(SettleShopBillPostageDetail::getBillDetailPostageId,
                                    subList)
                                    .set(SettleShopBillPostageDetail::getInvoiceFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode());
                            settleShopBillPostageDetailService.update(updateWrapper);
                        }
                );
    }

    /**
     * 供应商上传发票
     *
     * @param invoice
     * @param inconsistent
     * @param id
     * @param toleranceAmount
     * @param inconsistentRemark
     * @return
     * @throws IOException
     */
    @Transactional
    public String supplierInvoiceUpload(MultipartFile invoice,
                                        MultipartFile inconsistent,
                                        Long id,
                                        BigDecimal toleranceAmount,
                                        String inconsistentRemark
    ) throws IOException {

        SupplierInvoiceBill supplierInvoiceBill = getById(id);

        if (supplierInvoiceBill == null) {
            throw new ParameterException("未查询到发票申请单");
        }

        if (!SupplierInvoiceStateEnum.NOT_INVOICED.getCode().equals(supplierInvoiceBill.getState())) {
            throw new ParameterException("此发票暂不允许上传电子发票");
        }

        SupplierInvoicePdfDto supplierInvoicePdfDto = PdfboxUtils.parseInvoice(invoice);
        SettleShopBill settleShopBill = settleShopBillService.getById(supplierInvoiceBill.getBillId());
        if (settleShopBill == null) {
            throw new ParameterException("未查询到账单信息");
        }

        String newFileName = settleShopBill.getCustomerName() + settleShopBill.getCheckYear() + "年" + settleShopBill.getCheckMonth() +"月"+ supplierInvoicePdfDto.getInvoiceNumber();

        MultipartFile multipartFile = renameMultipartFile(invoice, newFileName);

        String invoiceUrl = fileService.uploadFileWithName(multipartFile, "");
        log.info("invoiceUrl:{}", invoiceUrl);

        SupplierInvoiceBill updateInvoice = new SupplierInvoiceBill();
        updateInvoice.setId(supplierInvoiceBill.getId());
        updateInvoice.setInvoiceUrl(invoiceUrl);
        updateInvoice.setModifier(LocalUserHolder.get().getUsername());
        updateInvoice.setUpdateTime(new Date());
        updateInvoice.setInvoiceNumber(supplierInvoicePdfDto.getInvoiceNumber());
        updateInvoice.setInvoiceAmountTax(supplierInvoicePdfDto.getInvoiceAmountTax());
        updateInvoice.setSupplierInvoiceTime(supplierInvoicePdfDto.getSupplierInvoiceDate());
        updateInvoice.setInvoiceUploadTime(new Date());
        updateInvoice.setBillId(supplierInvoiceBill.getBillId());

        if (supplierInvoiceBill.getAmountTax().compareTo(supplierInvoicePdfDto.getInvoiceAmountTax()) != 0) {
            // 存在发票与申请单金额不一致的情况
            if (StrUtil.isBlank(inconsistentRemark)) {
                throw new ParameterException("请输入不一致说明！");
            }

            String newInConsistentName = supplierInvoicePdfDto.getInvoiceNumber() + "不一致说明";
            MultipartFile newInconsistent = renameMultipartFile(inconsistent, newInConsistentName);

            String inconsistentUrl = fileService.uploadFile(newInconsistent,  "");
            log.info("inconsistentUrl:{}", inconsistentUrl);

            updateInvoice.setInconsistentUrl(inconsistentUrl);
            updateInvoice.setInconsistentRemark(inconsistentRemark);
            updateInvoice.setState(SupplierInvoiceStateEnum.PENDING_REVIEW.getCode());
            updateInvoice.setToleranceAmount(toleranceAmount.negate());
            updateById(updateInvoice);
        } else {
            SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceBill.getBillInvoiceType());
            supplierInvoiceStrategy.supplierInvoicePass(updateInvoice);

        }

        return "上传电子发票成功！";
    }

    public PageResp<SupplierInvoiceBillVo> queryPageVo(PageReq pageReq, SupplierInvoiceQueryVo supplierInvoiceQueryVo) {

        return DataAdapter.adapterPage(
                getBaseMapper().queryPage(
                        DataAdapter.adapterPageReq(pageReq)
                        , supplierInvoiceQueryVo
                )
                , SupplierInvoiceBillVo.class);
    }


    @Transactional
    public String invoiceDel(Long id) {

        SupplierInvoiceBill supplierInvoiceBill = getById(id);
        if (supplierInvoiceBill == null) {
            throw new ParameterException("未查询到发票申请单信息！");
        }

        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceBill.getBillInvoiceType());
        supplierInvoiceStrategy.invoiceDel(supplierInvoiceBill);

        return "发票删除成功！";

    }


    @Transactional
    public String approveInvoice(SupplierInvoiceApproveDto supplierInvoiceApproveDto) {

        SupplierInvoiceBill supplierInvoiceBill = getById(supplierInvoiceApproveDto.getId());
        if (supplierInvoiceBill == null) {
            throw new ParameterException("未查询到发票申请单信息！");
        }

        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceBill.getBillInvoiceType());
        supplierInvoiceBill.setModifier(LocalUserHolder.get().getUsername());
        supplierInvoiceBill.setUpdateTime(new Date());

        if (supplierInvoiceApproveDto.getApproveType() == 0) {
            //通过
            supplierInvoiceStrategy.supplierInvoicePass(supplierInvoiceBill);
        } else if (supplierInvoiceApproveDto.getApproveType() == 1) {
            // 驳回
            supplierInvoiceStrategy.supplierInvoiceReject(supplierInvoiceBill, supplierInvoiceApproveDto.getRejectReason());
        } else {
            throw new ParameterException("操作异常！");
        }

        return "发票申请单审批完成！";

    }


    @Transactional
    public void checkIsToApproveBill(SupplierInvoiceBill supplierInvoiceBill) {
        SettleShopBill settleShopBill = settleShopBillService.getById(supplierInvoiceBill.getBillId());
        //更新账单的容差金额
        SupplierInvoiceBill invoiceBill = supplierInvoiceBillService.getById(supplierInvoiceBill.getId());
        if (invoiceBill.getToleranceAmount().compareTo(BigDecimal.ZERO) != 0) {
            SettleShopBill updateBillForTolerance = new SettleShopBill();
            updateBillForTolerance.setBillId(settleShopBill.getBillId());
            updateBillForTolerance.setToleranceAmount(settleShopBill.getToleranceAmount().add(invoiceBill.getToleranceAmount()));
            updateBillForTolerance.setSettlementAmount(settleShopBill.getSettlementAmount().add(invoiceBill.getToleranceAmount()));
            String remark = "供应商发票申请单：" + invoiceBill.getInvoiceApplyNumber() + "容差金额:" + invoiceBill.getToleranceAmount();
            if (StrUtil.isBlank(settleShopBill.getRemark())) {
                updateBillForTolerance.setRemark(remark);
            } else {
                updateBillForTolerance.setRemark(settleShopBill.getRemark() + ";" + remark);
            }
            settleShopBillService.updateById(updateBillForTolerance);
        }

        //查询是否全部开票完成 全部开票完成 即可发起审批流
        QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBillDetail::getBillId, settleShopBill.getBillId())
                .ne(SettleShopBillDetail::getReconciliationStatus,
                        ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode())
                .select(SettleShopBillDetail::getDetailId);
        List<SettleShopBillDetail> shopBillDetails = settleShopBillDetailService.list(queryWrapper);

        QueryWrapper<SettleShopBillPostageDetail> postageDetailQueryWrapper = new QueryWrapper<>();
        postageDetailQueryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, settleShopBill.getBillId())
                .ne(SettleShopBillPostageDetail::getInvoiceFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode());
        List<SettleShopBillPostageDetail> billPostageDetails = settleShopBillPostageDetailService.list(postageDetailQueryWrapper);

        if (CollectionUtil.isEmpty(shopBillDetails) &&
                CollectionUtil.isEmpty(billPostageDetails)) {
            //都开票了 发起审批流程
            LoginUser user = LocalUserHolder.get();
            Map<String, Object> variables = Collections.emptyMap();
            String businessKey = workflowUtils.startWorkflowNoLogin(ActProcTypeEnum.BILL_APPROVAL,
                    settleShopBill.getBillSn(), user.getUsername(), user.getNickname(), "", user.getTenantId(), variables);
            actTaskFeign.completeTaskByBusinessKey(businessKey, 1, "", "");

            SettleShopBill updateBill = new SettleShopBill();
            updateBill.setBillId(settleShopBill.getBillId());
            updateBill.setBillStatus(BillStatusEnum.TO_BE_SETTLED.getCode());
            updateBill.setBillApproveStatus(BillApproveStatusEnum.IN_APPROVAL.getCode());
            updateBill.setUpdateTime(new Date());
            updateBill.setModifier(user.getUsername());
            settleShopBillService.updateById(updateBill);
        }

    }


    public List<String> getInvoiceUrl(Long billId) {

        QueryWrapper<SupplierInvoiceBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupplierInvoiceBill::getBillId, billId)
                .eq(SupplierInvoiceBill::getState, SupplierInvoiceStateEnum.INVOICED.getCode());
        List<SupplierInvoiceBill> supplierInvoiceBills = list(queryWrapper);

        if (CollectionUtil.isEmpty(supplierInvoiceBills)) {
            return new ArrayList<>();
        } else {
            return supplierInvoiceBills.stream().map(SupplierInvoiceBill::getInvoiceUrl).collect(Collectors.toList());
        }

    }


    public void supplierInvoiceExport(HttpServletResponse response, Long id) throws IOException {
        SupplierInvoiceBill supplierInvoiceBill = getById(id);
        if (supplierInvoiceBill == null) {
            throw new ParameterException("未查询到发票信息！");
        }
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(supplierInvoiceBill.getBillInvoiceType());
        supplierInvoiceStrategy.supplierInvoiceExport(response, id);

    }

    public MultipartFile renameMultipartFile(MultipartFile originalFile, String newFilename) throws IOException {
        String originalFilename = originalFile.getOriginalFilename();
        String extName = StrUtil.subAfter(originalFilename, ".", true);
        if (StrUtil.isBlank(extName)) {
            extName = "png";
        }
        // 创建新文件对象（保留原始内容类型和字节数据）
        return new MockMultipartFile(
                "file",  // 通常与表单字段名一致（可保留原始字段名）
                newFilename+"."+extName,
                originalFile.getContentType(),
                originalFile.getBytes()
        );
    }
}
