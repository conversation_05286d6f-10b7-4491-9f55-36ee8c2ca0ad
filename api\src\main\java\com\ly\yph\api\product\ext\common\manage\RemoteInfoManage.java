package com.ly.yph.api.product.ext.common.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ly.yph.api.goods.dto.GoodsSalePriceQueryDto;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopGoodsStock;
import com.ly.yph.api.goods.manage.GoodsUpDownManage;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.goods.service.ShopGoodsStockService;
import com.ly.yph.api.goods.service.YphGoodsPriceStrategyService;
import com.ly.yph.api.goods.vo.ShopGoodsVo;
import com.ly.yph.api.honda.service.HondaPointsGoodsPriceService;
import com.ly.yph.api.openapi.v1.service.OpenCheckService;
import com.ly.yph.api.order.common.GoodsReturnStateEnum;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.order.dto.AfterSaleWayBillDto;
import com.ly.yph.api.order.entity.ShopReturn;
import com.ly.yph.api.order.service.ShopReturnService;
import com.ly.yph.api.order.service.TrackOfLogisticsService;
import com.ly.yph.api.product.ext.common.*;
import com.ly.yph.api.product.ext.common.dto.request.*;
import com.ly.yph.api.product.ext.common.dto.response.*;
import com.ly.yph.api.product.ext.common.dto.response.RemoteStockInfoResp.StockStatusEnum;
import com.ly.yph.api.product.ext.common.enums.ErrorGoodsPriceMsgEnum;
import com.ly.yph.api.product.ext.common.validator.PriceLogicConfig;
import com.ly.yph.api.product.ext.dl.config.DlConfig;
import com.ly.yph.api.product.ext.dl.service.DlRemoteProxy;
import com.ly.yph.api.product.ext.jd.config.JDConfig;
import com.ly.yph.api.product.ext.jd.context.SupplierCodeContextHolder;
import com.ly.yph.api.product.ext.jd.dto.reponse.GetSimilarSkuGoodsResp;
import com.ly.yph.api.product.ext.jd.dto.reponse.JdQueryAreaIdResp;
import com.ly.yph.api.product.ext.jd.dto.reponse.SupportedInfoOpenResp;
import com.ly.yph.api.product.ext.jd.dto.request.ReturnOrderReq;
import com.ly.yph.api.product.ext.jd.dto.request.SkuNumInfoListParam;
import com.ly.yph.api.product.ext.jd.dto.request.UpdateAfterSaleWayBillOpenReq;
import com.ly.yph.api.product.ext.jd.general.config.JDGeneralConfig;
import com.ly.yph.api.product.ext.jd.integral.config.JDIntegralConfig;
import com.ly.yph.api.product.ext.jd.service.JDRemoteProxy;
import com.ly.yph.api.product.ext.jdiop.JdIopUntil;
import com.ly.yph.api.product.ext.jdiop.config.JdIopConfig;
import com.ly.yph.api.product.ext.jdiop.config.JdIopConfigInfo;
import com.ly.yph.api.product.ext.jdiop.context.SupplierCodeIopContextHolder;
import com.ly.yph.api.product.ext.jdiop.service.JdIopRemoteProxy;
import com.ly.yph.api.product.ext.ltqc.config.LtqcConfig;
import com.ly.yph.api.product.ext.ltqc.service.LtqcRemoteProxy;
import com.ly.yph.api.product.ext.ofs.config.OfsConfig;
import com.ly.yph.api.product.ext.ofs.service.OfsRemoteProxy;
import com.ly.yph.api.product.ext.qx.config.QxConfig;
import com.ly.yph.api.product.ext.qx.service.QxRemoteProxy;
import com.ly.yph.api.product.ext.sn.config.SnConfig;
import com.ly.yph.api.product.ext.sn.service.SnRemoteProxy;
import com.ly.yph.api.product.ext.xy.config.XyConfig;
import com.ly.yph.api.product.ext.xy.service.XyRemoteProxy;
import com.ly.yph.api.product.ext.yph.service.YphRemoteProxy;
import com.ly.yph.api.product.ext.yzh.config.YZHConfig;
import com.ly.yph.api.product.ext.yzh.service.YZHRemoteProxy;
import com.ly.yph.api.product.ext.zkh.config.ZKHConfig;
import com.ly.yph.api.product.ext.zkh.dto.InventoryDto;
import com.ly.yph.api.product.ext.zkh.service.ZKHRemoteProxy;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.virtualgoods.config.YzhVirtualConfig;
import com.ly.yph.api.virtualgoods.service.YZHCenterService;
import com.ly.yph.api.zone.entity.GoodsZoneUsersEntity;
import com.ly.yph.api.zone.service.GoodsZoneUsersService;
import com.ly.yph.core.base.exception.AssertUtil;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.thread.ThreadUtil;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.core.util.StringHelper;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.exception.SystemErrorCodeConstants.SHOP_RETURN_NOT_FOUND;

/**
 * 远程信息管理. 如果是OPEN api ，则返会本地数据库中的数据
 *
 * <AUTHOR>
 * @since 2022/03/14
 */
@Service
@Slf4j
@RefreshScope
public class RemoteInfoManage {
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    @Resource
    private YphGoodsPriceStrategyService yphGoodsPriceStrategyService;
    @Resource
    private JDConfig jdConfig;
    @Resource
    private JDIntegralConfig jdIntegralConfig;
    @Resource
    private JDGeneralConfig jdGeneralConfig;
    @Resource
    private JdIopConfig jdIopConfig;
    @Resource
    private ZKHConfig zkhConfig;
    @Resource
    private OfsConfig ofsConfig;
    @Resource
    private QxConfig qxConfig;
    @Resource
    private XyConfig xyConfig;
    @Resource
    private DlConfig dlConfig;
    @Resource
    private SnConfig snConfig;
    @Resource
    private YZHConfig yzhConfig;
    @Resource
    private YzhVirtualConfig yzhVirtualConfig;
    @Resource
    private LtqcConfig ltqcConfig;
    @Value("${yph.open-api.delivery-price}")
    private BigDecimal deliveryPrice;
    @Resource
    private TrackOfLogisticsService trackOfLogisticsService;
    @Resource
    @Qualifier("JDRemoteProxy")
    private JDRemoteProxy jdProxy;
    @Resource
    @Qualifier("JdIopRemoteProxy")
    private JdIopRemoteProxy jdIopRemoteProxy;
    @Resource
    @Qualifier("ZKHRemoteProxy")
    private ZKHRemoteProxy zkhProxy;
    @Resource
    @Qualifier("OfsRemoteProxy")
    private OfsRemoteProxy ofsProxy;
    @Resource
    @Qualifier("QxRemoteProxy")
    private QxRemoteProxy qxProxy;
    @Resource
    @Qualifier("XyRemoteProxy")
    private XyRemoteProxy xyProxy;
    @Resource
    @Qualifier("DlRemoteProxy")
    private DlRemoteProxy dlProxy;
    @Resource
    @Qualifier("SnRemoteProxy")
    private SnRemoteProxy snProxy;
    @Resource
    @Qualifier("YZHRemoteProxy")
    private YZHRemoteProxy yzhProxy;



    @Resource
    @Qualifier("LtqcRemoteProxy")
    private LtqcRemoteProxy ltqcRemoteProxy;
    @Resource
    @Qualifier("YphRemoteProxy")
    private YphRemoteProxy yphRemoteProxy;
    @Resource
    private ShopGoodsService goodSrv;
    @Value("${yph.mock-remote-request}")
    private Boolean mockRemoteRequest;
    @Value("${yph.mock-remote-confirm-order:false}")
    private Boolean mockRemoteRequestOrder;
    @Value("${honda.pool.pointsPoolId}")
    private Long pointsPoolId;
    @Resource
    private ShopGoodsStockService stockService;
    @Resource
    private ShopSupplierClassService shopSupplierClassService;
    @Resource
    private PriceLogicConfig priceLogicConfig;
    @Resource
    private ShopReturnService shopReturnService;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private OpenCheckService openCheckService;
    @Resource
    private BatchPriceGetter priceGetter;
    @Resource
    private GoodsUpDownManage goodsUpDownManage;
    @Resource
    private GoodsZoneUsersService goodsZoneUsersService;
    @Resource
    private HondaPointsGoodsPriceService pointsGoodsPriceService;

    @Resource YZHCenterService yzhCenterService;

    //---------------------------------------------------------------------
    // 远程逻辑
    //---------------------------------------------------------------------
    public static GoodsSalePriceQueryDto getGoodsSalePriceQueryDto(final ShopGoods shopGoods) {
        final GoodsSalePriceQueryDto goodsSalePriceQueryDto = new GoodsSalePriceQueryDto();
        goodsSalePriceQueryDto.setGoodsId(shopGoods.getGoodsId().toString());
        goodsSalePriceQueryDto.setFirstLevelGcid(shopGoods.getFirstLevelGcid());
        goodsSalePriceQueryDto.setSupplierCode(shopGoods.getSupplierCode());
        goodsSalePriceQueryDto.setGoodsCode(shopGoods.getGoodsCode());
        goodsSalePriceQueryDto.setGoodsSku(shopGoods.getGoodsSku());
        goodsSalePriceQueryDto.setTaxRate(shopGoods.getTaxRate());
        return goodsSalePriceQueryDto;
    }

    /**
     * 根据指定企业组织获取商品价格
     *
     * @param goodCode     商品编码
     * @param companyOrgId 企业组织编码
     * @return {@code RemotePriceInfoResp}
     * @Auth wt
     */
    public RemotePriceInfoResp getRemotePriceByCompanyOrgId(final String goodCode, final String companyOrgId) {
        Assert.notEmpty(goodCode);
        Assert.notEmpty(companyOrgId);
    val g = this.goodSrv.selectOneByGoodsCode(goodCode);
        final RemotePriceInfoResp res = priceGetter.getRemotePrice(g);
        final GoodsSalePriceQueryDto goodsSalePriceQueryDto = RemoteInfoManage.getGoodsSalePriceQueryDto(g);
        goodsSalePriceQueryDto.setOrganizationId(companyOrgId);
        return this.getSalePrice(res, goodsSalePriceQueryDto);
    }

    public RemotePriceInfoResp getSalePrice(RemotePriceInfoResp remotePriceInfoResp, final GoodsSalePriceQueryDto goodsSalePriceQueryDto) {
        if (LocalUserHolder.get() == null) {
            return remotePriceInfoResp;
        }

        if (null == remotePriceInfoResp) {
            log.error("[价格查询]出现异常：[{}]", goodsSalePriceQueryDto.getGoodsCode());
            return null;
        }
        remotePriceInfoResp.setGoodsCode(goodsSalePriceQueryDto.getGoodsCode());

        final LoginUser user = LocalUserHolder.get();
        if (StringHelper.IsEmptyOrNull(goodsSalePriceQueryDto.getOrganizationId())) {
            final Long organizationId = user.getEntityOrganizationId() == null ? user.getOrganizationId() : user.getEntityOrganizationId();
            goodsSalePriceQueryDto.setOrganizationId(String.valueOf(organizationId));
        }

        // 计算税率
        final BigDecimal taxRate = new BigDecimal(goodsSalePriceQueryDto.getTaxRate())
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .add(new BigDecimal(1));

        remotePriceInfoResp.setTaxRate(taxRate);
        ShopSupplier shopSupplier = shopSupplierService.selectByCode(goodsSalePriceQueryDto.getSupplierCode());
        if (shopSupplier == null){
            log.error("[" + goodsSalePriceQueryDto.getSupplierCode() + "]未查询到供应商信息");
            remotePriceInfoResp.setGoodsSalePrice(new BigDecimal(0));
            remotePriceInfoResp.setGoodsNakedSalePrice(new BigDecimal(0));
            fillPriceMsg(remotePriceInfoResp, ErrorGoodsPriceMsgEnum.NOT_FOUND_SUPPLIER);
            return remotePriceInfoResp;
        }
        if (shopSupplier.getSupplierType() != 1 && (null == remotePriceInfoResp.getGoodsPactPrice() || remotePriceInfoResp.getGoodsPactPrice().compareTo(BigDecimal.ZERO) <= 0)) {
            log.error("商品[" + goodsSalePriceQueryDto.getGoodsCode() + "]无价格");
            remotePriceInfoResp.setGoodsSalePrice(new BigDecimal(0));
            remotePriceInfoResp.setGoodsNakedSalePrice(new BigDecimal(0));
            fillPriceMsg(remotePriceInfoResp, ErrorGoodsPriceMsgEnum.ERROR_REMOTE_ZERO_PRICE);
            return remotePriceInfoResp;
        }

        //无税率商品 不允许查询价格
        if (shopSupplier.getSupplierType() != 1 && (null == remotePriceInfoResp.getGoodsPactPrice() || remotePriceInfoResp.getTaxRate().compareTo(BigDecimal.ONE) <= 0)) {
            log.error("商品[" + goodsSalePriceQueryDto.getGoodsCode() + "]税率异常");
            remotePriceInfoResp.setGoodsSalePrice(new BigDecimal(0));
            remotePriceInfoResp.setGoodsNakedSalePrice(new BigDecimal(0));
            fillPriceMsg(remotePriceInfoResp, ErrorGoodsPriceMsgEnum.ERROR_REMOTE_ZERO_PRICE);
            return remotePriceInfoResp;
        }

        ShopSupplier supplier = shopSupplierService.selectByCode(goodsSalePriceQueryDto.getSupplierCode());

        List<GoodsZoneUsersEntity> zoneUsersEntityList = goodsZoneUsersService.queryZoneByUserId(user.getId());
        if(CollectionUtil.isNotEmpty(zoneUsersEntityList) && pointsPoolId.equals(zoneUsersEntityList.get(0).getGoodsPoolId())){
            //东本特约店积分专区价格策略
            remotePriceInfoResp = pointsGoodsPriceService.getPointsGoodsSalePrice(remotePriceInfoResp, goodsSalePriceQueryDto);
            if(remotePriceInfoResp.getMsgType() != null ){
                log.error("东本特约店积分商品[" + goodsSalePriceQueryDto.getGoodsCode() + "]价格异常");
                return remotePriceInfoResp;
            }
        }else {
            //平台获取售价逻辑
            remotePriceInfoResp = this.yphGoodsPriceStrategyService.queryGoodsSalePrice(remotePriceInfoResp, goodsSalePriceQueryDto,supplier);
        }


        // 是京东或京东积分商品;
        // 商品价格策略非固定价;
        // 不在 "规则排除范围(nacos配置)" 内的企业;
        if (!CollectionUtil.contains(this.priceLogicConfig.getExcludesCompany(), goodsSalePriceQueryDto.getOrganizationId())
                && CollectionUtil.contains(this.priceLogicConfig.getSupplier(), goodsSalePriceQueryDto.getSupplierCode())
                && remotePriceInfoResp.getIsFixed() == 0) {
            // 销售价 > 官网价
            if (remotePriceInfoResp.getGoodsNakedSalePrice().compareTo(remotePriceInfoResp.getGoodsOriginalNakedPrice()) > 0) {

                // 协议价 * 1.02
                BigDecimal tempPrice = remotePriceInfoResp.getGoodsPactNakedPrice().multiply(this.priceLogicConfig.getValidRatio()).setScale(2, RoundingMode.HALF_UP);
                if (tempPrice.compareTo(remotePriceInfoResp.getGoodsOriginalNakedPrice()) <= 0) {
                    // 小于等于官网价，销售价 = 官网价
                    remotePriceInfoResp.setGoodsNakedSalePrice(remotePriceInfoResp.getGoodsOriginalNakedPrice());
                } else {
                    // 大于官网价，直接取协议价作为销售价
                    remotePriceInfoResp.setGoodsNakedSalePrice(remotePriceInfoResp.getGoodsPactNakedPrice());
                }
            } else {
                //官网价*0.95
                BigDecimal officialNakedPrice = remotePriceInfoResp.getGoodsOriginalNakedPrice().multiply(this.priceLogicConfig.getDiscount()).setScale(2, RoundingMode.HALF_UP);
                // 销售价是否 低于 官网价*0.95
                if (remotePriceInfoResp.getGoodsNakedSalePrice().compareTo(officialNakedPrice) < 0) {
                    // 是, 销售价 = 官网价*0.95
                    remotePriceInfoResp.setGoodsNakedSalePrice(officialNakedPrice);
                }
            }
            // 终极判定，最终价格是否 > 协议价*1.08
            BigDecimal finalNakedPrice = remotePriceInfoResp.getGoodsPactNakedPrice().multiply(this.priceLogicConfig.getFinalRatio()).setScale(2, RoundingMode.HALF_UP);
            if (remotePriceInfoResp.getGoodsNakedSalePrice().compareTo(finalNakedPrice) > 0) {
                // 是，销售价 = 协议价 * 1.08
                remotePriceInfoResp.setGoodsNakedSalePrice(finalNakedPrice);
            }
        }

        if (remotePriceInfoResp.getIsFixed() == 0 && remotePriceInfoResp.getGoodsNakedSalePrice().compareTo(remotePriceInfoResp.getGoodsOriginalNakedPrice()) > 0) {
            // 兜底规则，商城销售价格 大于 供应商原价时使用商品原价
            // log.info(goodsSalePriceQueryDto.getSupplierCode() + "商品原价[" +
            // goodsSalePriceQueryDto.getGoodsSku() + ":" +
            // remotePriceInfoResp.getGoodsOriginalNakedPrice() + "]");
            if(!"srm".equals(supplier.getDataSource())){
                // 非SRM供应商，四舍五入保留2位小数
                remotePriceInfoResp.setGoodsNakedSalePrice(remotePriceInfoResp.getGoodsOriginalNakedPrice().setScale(2,RoundingMode.HALF_UP));
            }else {
                // SRM供应商
                remotePriceInfoResp.setGoodsNakedSalePrice(remotePriceInfoResp.getGoodsOriginalNakedPrice());
            }

        }

        // 计算含税单价
        if (remotePriceInfoResp.getIsFixed() == 0) {
            BigDecimal goodsSalePrice = remotePriceInfoResp.getGoodsNakedSalePrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            if (shopSupplier.getDataSource().equals("srm")) {
                goodsSalePrice = remotePriceInfoResp.getGoodsPactPrice();
            }
            remotePriceInfoResp.setGoodsSalePrice(goodsSalePrice);
        }
        //销售价低于协议价时
        if (remotePriceInfoResp.getGoodsNakedSalePrice().compareTo(remotePriceInfoResp.getGoodsPactNakedPrice()) < 0 && remotePriceInfoResp.getGoodsSalePrice().compareTo(remotePriceInfoResp.getGoodsPactPrice()) < 0) {
            log.info(
          "销售价格小于协议价,商品SKU:{},原价:{},协议价{}:",
          goodsSalePriceQueryDto.getGoodsSku(),
          remotePriceInfoResp.getGoodsOriginalNakedPrice(),
          remotePriceInfoResp.getGoodsPactNakedPrice());
            // 京东的商品，直接使用协议价销售
            if (CollectionUtil.contains(this.priceLogicConfig.getSupplier(), goodsSalePriceQueryDto.getSupplierCode())) {
                remotePriceInfoResp.setGoodsSalePrice(remotePriceInfoResp.getGoodsPactPrice());
                remotePriceInfoResp.setGoodsNakedSalePrice(remotePriceInfoResp.getGoodsPactNakedPrice());
            } else {
                log.info("商品价格异常:" + goodsSalePriceQueryDto.getGoodsSku());
                remotePriceInfoResp.setGoodsSalePrice(new BigDecimal(0));
                remotePriceInfoResp.setGoodsNakedSalePrice(new BigDecimal(0));
                fillPriceMsg(remotePriceInfoResp, ErrorGoodsPriceMsgEnum.ERROR_REMOTE_PRICE);
                return remotePriceInfoResp;
            }

        }

        BigDecimal actualDiscount = new BigDecimal(0);
        if(remotePriceInfoResp.getGoodsPactPrice().compareTo(new BigDecimal(0)) > 0){
            //实际折扣
            actualDiscount = remotePriceInfoResp.getGoodsPactPrice().divide(remotePriceInfoResp.getGoodsOriginalPrice(), 2, RoundingMode.HALF_UP);
        }

        //分类折扣
        var sClass = shopSupplierClassService.selectForDiscountCheck(null, goodsSalePriceQueryDto.getSupplierCode());

        if (sClass == null) {
            remotePriceInfoResp.setDiscountRange(0);
            log.warn("未查询到供应商协议折扣:{}", goodsSalePriceQueryDto.getGoodsCode());
            return remotePriceInfoResp;
        }

        if (actualDiscount.compareTo(sClass.getAgreementDiscount()) > 0) {
            remotePriceInfoResp.setDiscountRange(1);
            //超出折扣范围
            fillPriceMsg(remotePriceInfoResp, ErrorGoodsPriceMsgEnum.ERROR_REMOTE_PRICE_RULE);
        }
        return remotePriceInfoResp;
    }

    public List<PreOrderResp> preOrder(final List<PreOrderInfo> preOrderInfo) {
        Assert.notNull(preOrderInfo);
        final List<PreOrderResp> rest = new ArrayList<>();
        for (final PreOrderInfo orderInfo : preOrderInfo) {
      orderInfo
          .getSkuList()
          .forEach(
              item -> {
                val g = this.goodSrv.selectOneByGoodsCode(item.getGoodsCode());
                if (g == null) {
                  val pe =
                      new ParameterException(StrUtil.format("下单错误，{}不存在", item.getGoodsCode()));
                  pe.addError("goodCode", item.getGoodsCode());
                  throw pe;
                } else {
                  item.setSkuId(g.getGoodsSku());
                }
              });
            // 拆单规则：按供应商、商品类型、合同号拆单。商城的订单号才是唯一。
            PreOrderResp resp;
            if (orderInfo.getSupplierCode().equals(this.jdConfig.getCode())) {
                SupplierCodeContextHolder.clear();
                resp = this.jdProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.jdIntegralConfig.getCode())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
                try {
                    resp = this.jdProxy.preOrder(orderInfo);
                } finally {
                    SupplierCodeContextHolder.clear();
                }
            } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(orderInfo.getSupplierCode())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(orderInfo.getSupplierCode());
                try {
                    resp = this.jdProxy.preOrder(orderInfo);
                } finally {
                    SupplierCodeContextHolder.clear();
                }
            } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(orderInfo.getSupplierCode())) {
                resp = JdIopUntil.executeWithClear(
                        () -> this.jdIopRemoteProxy.preOrder(orderInfo),
                        () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(orderInfo.getSupplierCode()) // 设置上下文
                );
            }  else if (orderInfo.getSupplierCode().equals(this.zkhConfig.getCode())) {
                resp = this.zkhProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.ofsConfig.getCode())) {
                resp = this.ofsProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.qxConfig.getCode())) {
                resp = this.qxProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.xyConfig.getCode())) {
                resp = this.xyProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.dlConfig.getCode())) {
                resp = this.dlProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.snConfig.getCode())) {
                resp = this.snProxy.preOrder(orderInfo);
            } else if (orderInfo.getSupplierCode().equals(this.ltqcConfig.getCode())) {
                resp = this.ltqcRemoteProxy.preOrder(orderInfo);
            } else {
                resp = this.yphRemoteProxy.preOrder(orderInfo);
            }
            resp.setSupplierCode(orderInfo.getSupplierCode());
            rest.add(resp);
        }
        // 合并结果
        return rest;
    }

    public void openApiPreOrder(PreOrderInfo preOrderInfo) {
        if (preOrderInfo.getSupplierCode().equals(this.ltqcConfig.getCode())) {
            this.ltqcRemoteProxy.preOrder(preOrderInfo);
        }
    }
    /**
     * 确认订单
     *
     * @param yphOrder 有品汇订单
     * @param dsOrder  电商订单
     * @return boolean
     */
    public boolean confirmOrder(final String yphOrder, final String dsOrder, final String supplier) {
        log.info("confirmOrder 确认订单,yphOrder:{},dsOrder:{},supplier:{} ", yphOrder, dsOrder, supplier);
        Assert.notEmpty(yphOrder, () -> new ParameterException("yphOrder 不能为空"));
        // 如果是open api 不一定有订单号
        // Assert.notNull(dsOrder, () -> new ParameterException("dsOrder 不能为空"));
        Assert.notNull(supplier, () -> new ParameterException("supplier 不能为空"));
        if (this.mockRemoteRequestOrder) {
            log.info("下单成功 {}", yphOrder);
            return true;
        }
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            this.jdProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                this.jdProxy.confirmOrder(yphOrder, dsOrder);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                this.jdProxy.confirmOrder(yphOrder, dsOrder);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)){
            JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.confirmOrder(yphOrder, dsOrder),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (supplier.equals(this.zkhConfig.getCode())) {
            this.zkhProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.ofsConfig.getCode())) {
            this.ofsProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.qxConfig.getCode())) {
            this.qxProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.xyConfig.getCode())) {
            this.xyProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.dlConfig.getCode())) {
            this.dlProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.snConfig.getCode())) {
            this.snProxy.confirmOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.ltqcConfig.getCode())) {
            this.ltqcRemoteProxy.confirmOrder(yphOrder, dsOrder);
        } else if(supplier.equals(this.yzhVirtualConfig.getCode())){
            this.yzhCenterService.submitYzhVirtualOrder(yphOrder);
        }else {
            this.yphRemoteProxy.confirmOrder(yphOrder, dsOrder);
        }
        return true;
    }

    /**
     * 取消订单
     *
     * @param yphOrder 有品汇订单
     * @param dsOrder  jd订单
     * @return boolean
     */
    public boolean cancelOrder(final String yphOrder, final String dsOrder, final String supplier) {
        log.info("取消订单 cancelOrder ------{}，{}，{}", yphOrder, dsOrder, supplier);
        if (this.mockRemoteRequest) {
            return true;
        }
        Assert.notEmpty(yphOrder, () -> new ParameterException("yphOrder 不能为空"));
    // 接口平台的单 ，没有三方订单
    // Assert.notNull(dsOrder, () -> new ParameterException("dsOrder 不能为空"));
    Assert.notNull(
        supplier,
        () -> {
          final String paramsName = "supplier";
          val exception = new ParameterException("[" + paramsName + "]不能为空");
          exception.addError(paramsName, "[" + paramsName + "]不能为空");
          return exception;
        });

        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            this.jdProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                this.jdProxy.cancelOrder(yphOrder, dsOrder);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                this.jdProxy.cancelOrder(yphOrder, dsOrder);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)){
            JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.cancelOrder(yphOrder, dsOrder),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (supplier.equals(this.zkhConfig.getCode())) {
            this.zkhProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.ofsConfig.getCode())) {
            this.ofsProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.qxConfig.getCode())) {
            this.qxProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.xyConfig.getCode())) {
            this.xyProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.dlConfig.getCode())) {
            this.dlProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.snConfig.getCode())) {
            this.snProxy.cancelOrder(yphOrder, dsOrder);
        } else if (supplier.equals(this.ltqcConfig.getCode())) {
            this.ltqcRemoteProxy.cancelOrder(yphOrder, dsOrder);
        } else {
            this.yphRemoteProxy.cancelOrder(yphOrder, dsOrder);
        }
        return true;
    }

    /**
     * 取消部分订单
     *
     * @param
     * @return boolean
     */
    public boolean cancelItemOrder(final String orderId, final String[] skuNoList, final String supplier) {
        if (this.mockRemoteRequest) {
            return true;
        }
        Assert.notEmpty(orderId, () -> new ParameterException("orderId 不能为空"));
        Assert.notEmpty(skuNoList, () -> new ParameterException("skus 不能为空"));
        Assert.notEmpty(supplier, () -> new ParameterException("supplier 不能为空"));
        if (supplier.equals(this.zkhConfig.getCode())) {
            this.zkhProxy.cancelItemOrder(orderId, skuNoList);
        }
        return true;
    }

    /**
     * 校验商品是否可售
     *
     * @param goodCode
     * @param region
     * @param address
     * @return
     */
    public RemoteIsCanSaleInfoResp canSale(final String goodCode, final RegionDto region, final String address) {
        if (this.mockRemoteRequest) {
            final RemoteIsCanSaleInfoResp ss = new RemoteIsCanSaleInfoResp();
            ss.setIsCanSale(true);

            return ss;
        }
        Assert.notEmpty(goodCode);
        Assert.notEmpty(address);
        Assert.notNull(region, () -> new ParameterException("region"));
        Assert.notEmpty(region.getCity(), () -> new ParameterException("getCity"));
        Assert.notEmpty(region.getProvince(), () -> new ParameterException("getProvince"));
        Assert.notEmpty(region.getCounty(), () -> new ParameterException("getCounty"));

    val l = this.goodSrv.selectOneByGoodsCode(goodCode);

        if (l == null) {
      val pe = new ParameterException("查询限售时出错，没有该商品");
            pe.addError("goodCode", goodCode);
            throw pe;
        }

        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(l.getSupplierCode())) {
            final RemoteIsCanSaleInfoResp ss = new RemoteIsCanSaleInfoResp();
            ss.setIsCanSale(true);
            return ss;
        }

        final ISaleLimitGetterAble islga;
        if (this.jdConfig.getCode().equals(l.getSupplierCode())) {
            SupplierCodeContextHolder.clear();
            islga = this.jdProxy;
            // todo : 限售的时候需要更新shopgood表中的数据，不要再显示出来了
        } else if (this.jdIntegralConfig.getCode().equals(l.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            islga = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(l.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(l.getSupplierCode());
            islga = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(l.getSupplierCode())){
            SupplierCodeIopContextHolder.setSupplierCodeProxy(l.getSupplierCode());
            islga = this.jdIopRemoteProxy;
        } else if (this.zkhConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.zkhProxy;
        } else if (this.ofsConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.ofsProxy;
        } else if (this.qxConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.qxProxy;
        } else if (this.xyConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.xyProxy;
        } else if (this.dlConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.dlProxy;
        } else if (this.snConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.snProxy;
        } else {
            islga = this.yphRemoteProxy;
        }
        RemoteIsCanSaleInfoResp resp;
        try {
            final RemoteIsCanSaleReq rr = new RemoteIsCanSaleReq();
            rr.setSkuId(l.getGoodsSku());
            rr.setRegin(region);
            rr.setAddress(address);
            resp = islga.isCanSale(rr).get(0);
        } finally {
            SupplierCodeContextHolder.clear();
            SupplierCodeIopContextHolder.clear();
        }
        return resp;
    }

    /**
     * 批量商品是否可售
     *
     * @param goodCodes
     * @param region
     * @param address
     * @return
     */
    public List<RemoteIsCanSaleInfoResp> batchCheckCanSale(String goodCodes, RegionDto region, String address) {
        Assert.notEmpty(address);
        Assert.notNull(region, () -> new ParameterException("region"));
        Assert.notEmpty(region.getCity(), () -> new ParameterException("getCity"));
        Assert.notEmpty(region.getProvince(), () -> new ParameterException("getProvince"));
        Assert.notEmpty(region.getCounty(), () -> new ParameterException("getCounty"));

    val goodsList = this.goodSrv.selectListByGoodsCodes(goodCodes);
        if (CollectionUtil.isEmpty(goodsList)) {
      val pe = new ParameterException("查询限售时出错，没有该商品");
            pe.addError("goodCode", goodCodes);
            throw pe;
        }

        List<RemoteIsCanSaleInfoResp> respList = new ArrayList<>();

        Map<String, List<ShopGoods>> groupSupplier = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getSupplierCode));
        for (Map.Entry<String, List<ShopGoods>> entry : groupSupplier.entrySet()) {
            List<String> skuIds = entry.getValue().stream().map(ShopGoods::getGoodsSku).collect(Collectors.toList());

            // 如果是走API的，只要商品存在则可售
            if (openApiConfig.getSuppliers().contains(entry.getKey())) {
                for (String sku : skuIds) {
                    final RemoteIsCanSaleInfoResp resp = new RemoteIsCanSaleInfoResp();
                    resp.setIsCanSale(true);
                    resp.setSku(sku);
                    respList.add(resp);
                }
                continue;
            }

            final ISaleLimitGetterAble islga;
            if (this.jdConfig.getCode().equals(entry.getKey())) {
                SupplierCodeContextHolder.clear();
                islga = this.jdProxy;
                // todo : 限售的时候需要更新shopgood表中的数据，不要再显示出来了
            } else if (this.jdIntegralConfig.getCode().equals(entry.getKey())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
                islga = this.jdProxy;
            } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(entry.getKey())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(entry.getKey());
                islga = this.jdProxy;
            } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(entry.getKey())){
                SupplierCodeIopContextHolder.setSupplierCodeProxy(entry.getKey());
                islga = this.jdIopRemoteProxy;
            } else if (this.zkhConfig.getCode().equals(entry.getKey())) {
                islga = this.zkhProxy;
            } else if (this.ofsConfig.getCode().equals(entry.getKey())) {
                islga = this.ofsProxy;
            } else if (this.qxConfig.getCode().equals(entry.getKey())) {
                islga = this.qxProxy;
            } else if (this.xyConfig.getCode().equals(entry.getKey())) {
                islga = this.xyProxy;
            } else if (this.dlConfig.getCode().equals(entry.getKey())) {
                islga = this.dlProxy;
            } else if (this.snConfig.getCode().equals(entry.getKey())) {
                islga = this.snProxy;
            } else {
                islga = this.yphRemoteProxy;
            }

            try {
                if (skuIds.size() > 20) {
                    List<List<String>> goodsArray = new ArrayList<>(skuIds.stream().collect(Collectors.groupingBy(s -> (skuIds.indexOf(s) / 20))).values());
                    for (List<String> skus : goodsArray) {
                        final RemoteIsCanSaleReq resp = new RemoteIsCanSaleReq();
                        resp.setSkuId(String.join(",", skus));
                        resp.setRegin(region);
                        resp.setAddress(address);
                        respList.addAll(islga.isCanSale(resp));
                    }
                } else {
                    final RemoteIsCanSaleReq resp = new RemoteIsCanSaleReq();
                    resp.setSkuId(String.join(",", skuIds));
                    resp.setRegin(region);
                    resp.setAddress(address);
                    respList.addAll(islga.isCanSale(resp));
                }
                respList.forEach(item ->{
                    if (item.getSupplierCode() == null) {
                        item.setSupplierCode(entry.getKey());
                    }
                });
            } finally {
                //每次循环 要么jd  要么jdjf 不影响
                SupplierCodeContextHolder.clear();
                SupplierCodeIopContextHolder.clear();
            }
        }

        return respList;
    }

    /**
     * 查询区域购买限制
     *
     * @param goodCode
     * @param region
     * @param address
     * @return
     */
    public RemoteAreaSaleLimitResp areaSaleLimit(final String goodCode, final RegionDto region, final String address) {
        if (this.mockRemoteRequest) {
            final RemoteAreaSaleLimitResp ss = new RemoteAreaSaleLimitResp();
            ss.setIsLimit(false);
            return ss;
        }
        Assert.notEmpty(goodCode);
        Assert.notEmpty(address);
        Assert.notNull(region, () -> new ParameterException("region"));
        Assert.notEmpty(region.getCity(), () -> new ParameterException("getCity"));
        Assert.notEmpty(region.getProvince(), () -> new ParameterException("getProvince"));
        Assert.notEmpty(region.getCounty(), () -> new ParameterException("getCounty"));

    val l = this.goodSrv.selectOneByGoodsCode(goodCode);
        if (l == null) {
      val pe = new ParameterException("查询限售时出错，没有该商品");
            pe.addError("goodCode", goodCode);
            throw pe;
        }
        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(l.getSupplierCode())) {
            final RemoteAreaSaleLimitResp ss = new RemoteAreaSaleLimitResp();
            ss.setIsLimit(false);
            return ss;
        }

        final ISaleLimitGetterAble islga;
        if (this.jdConfig.getCode().equals(l.getSupplierCode())) {
            SupplierCodeContextHolder.clear();
            islga = this.jdProxy;
            // todo : 限售的时候需要更新shopgood表中的数据，不要再显示出来了
        } else if (this.jdIntegralConfig.getCode().equals(l.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            islga = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(l.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(l.getSupplierCode());
            islga = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(l.getSupplierCode())){
            SupplierCodeIopContextHolder.setSupplierCodeProxy(l.getSupplierCode());
            islga = this.jdIopRemoteProxy;
        } else if (this.zkhConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.zkhProxy;
        } else if (this.ofsConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.ofsProxy;
        } else if (this.qxConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.qxProxy;
        } else if (this.xyConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.xyProxy;
        } else if (this.dlConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.dlProxy;
        } else if (this.snConfig.getCode().equals(l.getSupplierCode())) {
            islga = this.snProxy;
        } else {
            islga = this.yphRemoteProxy;
        }
        RemoteAreaSaleLimitResp resp;
        try {
            final RemoteAreaSaleLimitInfoReq rr = new RemoteAreaSaleLimitInfoReq();
            rr.setSkuId(l.getGoodsSku());
            rr.setRegin(region);
            rr.setAddress(address);
            resp = islga.isAreaLimit(rr).get(0);
        } finally {
            SupplierCodeContextHolder.clear();
            SupplierCodeIopContextHolder.clear();

        }
        return resp;
    }

    /**
     * 批量查询区域购买限制
     *
     * @param goodCodes
     * @param region
     * @param address
     * @return
     */
    public List<RemoteAreaSaleLimitResp> batchCheckAreaLimit(final String goodCodes, final RegionDto region, final String address) {
        Assert.notEmpty(goodCodes);
        Assert.notEmpty(address);
        Assert.notNull(region, () -> new ParameterException("region"));
        Assert.notEmpty(region.getCity(), () -> new ParameterException("getCity"));
        Assert.notEmpty(region.getProvince(), () -> new ParameterException("getProvince"));
        Assert.notEmpty(region.getCounty(), () -> new ParameterException("getCounty"));

    val goodsList = this.goodSrv.selectListByGoodsCodes(goodCodes);
        if (CollectionUtil.isEmpty(goodsList)) {
      val pe = new ParameterException("查询限售时出错，没有该商品");
            pe.addError("goodCode", goodCodes);
            throw pe;
        }

        List<RemoteAreaSaleLimitResp> respList = new ArrayList<>();

        Map<String, List<ShopGoods>> groupSupplier = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getSupplierCode));
        for (Map.Entry<String, List<ShopGoods>> entry : groupSupplier.entrySet()) {
            List<String> skuIds = entry.getValue().stream().map(ShopGoods::getGoodsSku).collect(Collectors.toList());
            // 如果是走API的，只要商品存在则可售
            if (openApiConfig.getSuppliers().contains(entry.getKey())) {
                for (String sku : skuIds) {
                    final RemoteAreaSaleLimitResp resp = new RemoteAreaSaleLimitResp();
                    resp.setIsLimit(false);
                    resp.setSku(sku);
                    respList.add(resp);
                }
                continue;
            }

            final ISaleLimitGetterAble islga;
            if (this.jdConfig.getCode().equals(entry.getKey())) {
                SupplierCodeContextHolder.clear();
                islga = this.jdProxy;
                // todo : 限售的时候需要更新shopgood表中的数据，不要再显示出来了
            } else if (this.jdIntegralConfig.getCode().equals(entry.getKey())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
                islga = this.jdProxy;
            } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(entry.getKey())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(entry.getKey());
                islga = this.jdProxy;
            } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(entry.getKey())){
                SupplierCodeIopContextHolder.setSupplierCodeProxy(entry.getKey());
                islga = this.jdIopRemoteProxy;
            } else if (this.zkhConfig.getCode().equals(entry.getKey())) {
                islga = this.zkhProxy;
            } else if (this.ofsConfig.getCode().equals(entry.getKey())) {
                islga = this.ofsProxy;
            } else if (this.qxConfig.getCode().equals(entry.getKey())) {
                islga = this.qxProxy;
            } else if (this.xyConfig.getCode().equals(entry.getKey())) {
                islga = this.xyProxy;
            } else if (this.dlConfig.getCode().equals(entry.getKey())) {
                islga = this.dlProxy;
            } else if (this.snConfig.getCode().equals(entry.getKey())) {
                islga = this.snProxy;
            } else {
                islga = this.yphRemoteProxy;
            }

            try {
                if (skuIds.size() > 20) {
                    List<List<String>> goodsArray = new ArrayList<>(skuIds.stream().collect(Collectors.groupingBy(s -> (skuIds.indexOf(s) / 20))).values());
                    for (List<String> skus : goodsArray) {
                        final RemoteAreaSaleLimitInfoReq rr = new RemoteAreaSaleLimitInfoReq();
                        rr.setSkuId(String.join(",", skus));
                        rr.setRegin(region);
                        rr.setAddress(address);
                        respList.addAll(islga.isAreaLimit(rr));
                    }
                } else {
                    final RemoteAreaSaleLimitInfoReq resp = new RemoteAreaSaleLimitInfoReq();
                    resp.setSkuId(String.join(",", skuIds));
                    resp.setRegin(region);
                    resp.setAddress(address);
                    respList.addAll(islga.isAreaLimit(resp));
                }
                respList.forEach(item ->{
                    if (item.getSupplierCode() == null) {
                        item.setSupplierCode(entry.getKey());
                    }
                });
            } finally {
                //每次循环 要么jd  要么jdjf 不影响
                SupplierCodeContextHolder.clear();
                SupplierCodeIopContextHolder.clear();
            }
        }
        return respList;
    }

    /**
     * 得到运费
     * <p>
     * 注意 skuInfoList 参数中 所有的 goodscode 必须是同一个供应商，如果是多个供应商，请调用多次这个接口
     * <p>
     * 每个供应商分开调用，最后整合
     *
     * @return boolean
     */
    public DeliveryPriceResp getDeliveryPrice(final List<GoodCodeInfoListParams> skuInfoList, final RegionDto areaInfo, final String address) {

        Assert.notNull(areaInfo, () -> new ParameterException("areaInfo"));
        Assert.notEmpty(address, () -> new ParameterException("address"));
        Assert.notEmpty(areaInfo.getCity(), () -> new ParameterException("getCity"));
        Assert.notEmpty(areaInfo.getProvince(), () -> new ParameterException("getProvince"));
        Assert.notEmpty(areaInfo.getCounty(), () -> new ParameterException("getCounty"));
        Assert.notEmpty(skuInfoList, () -> new ParameterException("skuInfoList"));

        final IDeliveryAble ida;

    val id = skuInfoList.get(0);
    val g = this.goodSrv.selectOneByGoodsCode(id.getGoodsCode());
        if (g == null) {
      val pe = new ParameterException("查询运费出错:没有该商品");
            pe.addError("goodCode", id.getGoodsCode());
            throw pe;
        }

        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(g.getSupplierCode())) {
            final DeliveryPriceResp ss = new DeliveryPriceResp();
            ss.setTotalPrice(this.deliveryPrice);
            return ss;
        }


        if (this.jdConfig.getCode().equals(g.getSupplierCode())) {
            SupplierCodeContextHolder.clear();
            ida = this.jdProxy;
        } else if (this.jdIntegralConfig.getCode().equals(g.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            ida = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(g.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(g.getSupplierCode());
            ida = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(g.getSupplierCode())){
            SupplierCodeIopContextHolder.setSupplierCodeProxy(g.getSupplierCode());
            ida = this.jdIopRemoteProxy;
        } else if (this.zkhConfig.getCode().equals(g.getSupplierCode())) {
            ida = this.zkhProxy;
        } else if (this.ofsConfig.getCode().equals(g.getSupplierCode())) {
            ida = this.ofsProxy;
        } else if (this.qxConfig.getCode().equals(g.getSupplierCode())) {
            ida = this.qxProxy;
        } else if (this.xyConfig.getCode().equals(g.getSupplierCode())) {
            ida = this.xyProxy;
        } else if (this.dlConfig.getCode().equals(g.getSupplierCode())) {
            ida = this.dlProxy;
        } else if (this.snConfig.getCode().equals(g.getSupplierCode())) {
            ida = this.snProxy;
        } else {
            ida = this.yphRemoteProxy;
        }

        DeliveryPriceResp resp;
        try {
      final Set<SkuNumInfoListParam> skuList = new ConcurrentHashSet<>();
            LoginUser user = LocalUserHolder.get();
            Long tenantId = TenantContextHolder.getTenantId();
      ThreadUtil.executeArrayAsync(
          () -> skuInfoList,
          item -> {
            TenantUtils.execute(
                tenantId,
                user,
                () -> {
                  val param = new SkuNumInfoListParam();
                  val ent = this.goodSrv.selectOneByGoodsCode(item.getGoodsCode());
                  param.setSkuNumber(item.getCount());
                  param.setSkuId(ent.getGoodsSku());
                  param.setSkuPrice(ent.getPrice());
                  skuList.add(param);
                });
          },
          this.commonIoExecutors);
            resp = ida.getDeliveryPrice(CollectionUtils.convertList(skuList, item -> item), areaInfo, address);
        } finally {
            SupplierCodeContextHolder.clear();
            SupplierCodeIopContextHolder.clear();
        }
        return resp;
    }

    /**
     * 得到股票
     *
     * @param goodCode 商品编码
     * @param count    数量
     * @param region   地区信息
     * @param address  地址 全称 XX省XX市XX区 XX街道，京东商品必须要传不然取不到地址信息
     * @return {@code RemoteStockInfoResp}
     */
    public RemoteStockInfoResp getStock(final String goodCode, final Integer count, final RegionDto region, final String address) {
        Assert.notEmpty(goodCode, () -> new ParameterException("goodCode 不能为空"));

    val g = this.goodSrv.selectOneByGoodsCode(goodCode);
        if (this.mockRemoteRequest) {
            final RemoteStockInfoResp ss = new RemoteStockInfoResp();
            ss.setCount(12L);
            ss.setSkuId(g.getGoodsSku());
            ss.setArrivalDays(12);
            ss.setStockStatus(StockStatusEnum.canPreOrder);
            ss.setDeliveryTimeDesc("test");
            return ss;
        }

        if (g == null) {
      val pe = new ParameterException("没有该商品数据");
            pe.addError("goodCode", goodCode);
            throw pe;
        }

        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(g.getSupplierCode()) && !openApiConfig.getRejectSuppliersByStock().contains(g.getSupplierCode())) {
            final RemoteStockInfoResp ss = new RemoteStockInfoResp();
            ShopGoodsStock shopGoodsStock = stockService.selectOneByGoodsCode(goodCode);
            ss.setCount(Long.valueOf(shopGoodsStock.getStockAvailable()));
            ss.setArrivalDays(g.getDeliveryTime());
            ss.setSkuId(g.getGoodsSku());
            if (count > shopGoodsStock.getStockAvailable()) {
                // 库存不足
                ss.setStockStatus(StockStatusEnum.noGoods);
            } else {
                ss.setStockStatus(StockStatusEnum.hasGoods);
            }
            ss.setDeliveryTimeDesc(g.getDeliveryTime() + "天内发货");
            return ss;
        }

        final IStockGetterAble isa;
        if (this.jdConfig.getCode().equals(g.getSupplierCode())) {
            SupplierCodeContextHolder.clear();
            isa = this.jdProxy;
        } else if (this.jdIntegralConfig.getCode().equals(g.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            isa = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(g.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(g.getSupplierCode());
            isa = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(g.getSupplierCode())){
            SupplierCodeIopContextHolder.setSupplierCodeProxy(g.getSupplierCode());
            isa = this.jdIopRemoteProxy;
        } else if (this.zkhConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.zkhProxy;
        } else if (this.ofsConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.ofsProxy;
        } else if (this.qxConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.qxProxy;
        } else if (this.xyConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.xyProxy;
        } else if (this.dlConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.dlProxy;
        } else if (this.snConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.snProxy;
        } else if (this.yzhConfig.getCode().equals(g.getSupplierCode())) {
            isa = this.yzhProxy;
        } else {
            //友品汇平台商品 库存
            isa = this.yphRemoteProxy;
        }

        RemoteStockInfoResp resp;
        try {
            final RemoteStockInfoReq rr = new RemoteStockInfoReq();
            final List<InventoryDto> ids = new ArrayList<>(1);

            final InventoryDto id = new InventoryDto();
            id.setNum(count);
            id.setSkuId(g.getGoodsSku());
            id.setGoodsCode(g.getGoodsCode());
            ids.add(id);

            rr.setSkus(ids);
            rr.setRegin(region);
            rr.setAddress(address);
            resp = isa.get(rr).get(0);
        } finally {
            SupplierCodeContextHolder.clear();
            SupplierCodeIopContextHolder.clear();

        }
        return resp;
    }

    public List<String> getOrderReceipt( String supplierCode , String packageId , String supplierOrderId) {
        final IReceiptGetterAble isa;
        if (this.jdConfig.getCode().equals(supplierCode)) {
            SupplierCodeContextHolder.clear();
            isa = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplierCode);
            isa = jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            SupplierCodeIopContextHolder.setSupplierCodeProxy(supplierCode);
            isa = this.jdIopRemoteProxy;
        } else if (this.zkhConfig.getCode().equals(supplierCode)) {
            isa = this.zkhProxy;
        } else {
            //友品汇平台
            isa = this.yphRemoteProxy;
        }

        List<String> signSnList = new ArrayList<>();
        try {
            signSnList = isa.get(packageId, supplierOrderId);
        } finally {
            SupplierCodeContextHolder.clear();
            SupplierCodeIopContextHolder.clear();
        }

        return signSnList;
    }


    /**
     * 批量查询库存
     *
     * @param goodsMap 商品信息
     * @param region   地区信息
     * @param address  地址 全称 XX省XX市XX区 XX街道，京东商品必须要传不然取不到地址信息
     * @return {@code RemoteStockInfoResp}
     */
    public List<RemoteStockInfoResp> getBatchGoodsStock(final Map<String, Object> goodsMap, final RegionDto region, final String address) {
        String goodsCodes = goodsMap.entrySet().stream().map(key -> key.getKey()).collect(Collectors.joining(","));

        Assert.notEmpty(goodsCodes, () -> new ParameterException("goodCode 不能为空"));

    val goodsList = this.goodSrv.selectListByGoodsCodes(goodsCodes);

        if (CollectionUtil.isEmpty(goodsList)) {
      val pe = new ParameterException("没有该商品数据");
            pe.addError("goodCode", goodsCodes);
            throw pe;
        }
        List<RemoteStockInfoResp> respList = new ArrayList<>();

        Map<String, List<ShopGoods>> groupSupplier = goodsList.stream().collect(Collectors.groupingBy(ShopGoods::getSupplierCode));
        for (Map.Entry<String, List<ShopGoods>> entry : groupSupplier.entrySet()) {
            // 如果是走API的，只要商品存在则可售
            if (openApiConfig.getSuppliers().contains(entry.getKey()) && !openApiConfig.getRejectSuppliersByStock().contains(entry.getKey())) {
                final RemoteStockInfoResp resp = new RemoteStockInfoResp();
                for (ShopGoods goods : entry.getValue()) {
                    ShopGoodsStock shopGoodsStock = stockService.selectOneByGoodsCode(goods.getGoodsCode());
                    resp.setCount(Long.valueOf(shopGoodsStock.getStockAvailable()));
                    resp.setArrivalDays(goods.getDeliveryTime());
                    resp.setSkuId(goods.getGoodsSku());
                    resp.setSupplierCode(goods.getSupplierCode());
                    int count = Integer.parseInt(goodsMap.get(goods.getGoodsCode()).toString());
                    if (count > shopGoodsStock.getStockAvailable()) {
                        // 库存不足
                        resp.setStockStatus(StockStatusEnum.noGoods);
                    } else {
                        resp.setStockStatus(StockStatusEnum.hasGoods);
                    }
                    resp.setDeliveryTimeDesc(goods.getDeliveryTime() + "天内发货");
                    respList.add(resp);
                }
                continue;
            }

            final IStockGetterAble isa;
            if (this.jdConfig.getCode().equals(entry.getKey())) {
                SupplierCodeContextHolder.clear();
                isa = this.jdProxy;
            } else if (this.jdIntegralConfig.getCode().equals(entry.getKey())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
                isa = this.jdProxy;
            } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(entry.getKey())) {
                SupplierCodeContextHolder.setSupplierCodeProxy(entry.getKey());
                isa = this.jdProxy;
            } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(entry.getKey())){
                SupplierCodeIopContextHolder.setSupplierCodeProxy(entry.getKey());
                isa = this.jdIopRemoteProxy;
            } else if (this.zkhConfig.getCode().equals(entry.getKey())) {
                isa = this.zkhProxy;
            } else if (this.ofsConfig.getCode().equals(entry.getKey())) {
                isa = this.ofsProxy;
            } else if (this.qxConfig.getCode().equals(entry.getKey())) {
                isa = this.qxProxy;
            } else if (this.xyConfig.getCode().equals(entry.getKey())) {
                isa = this.xyProxy;
            } else if (this.dlConfig.getCode().equals(entry.getKey())) {
                isa = this.dlProxy;
            } else if (this.snConfig.getCode().equals(entry.getKey())) {
                isa = this.snProxy;
            } else if (this.yzhConfig.getCode().equals(entry.getKey())) {
                isa = this.yzhProxy;
            } else {
                //友品汇平台商品 库存
                isa = this.yphRemoteProxy;
            }

            try {
                List<InventoryDto> invQuery = new ArrayList<>(20);
                for (ShopGoods goods : entry.getValue()) {
                    final InventoryDto inv = new InventoryDto();
                    int count = Integer.valueOf(goodsMap.get(goods.getGoodsCode()).toString());
                    inv.setNum(count);
                    inv.setSkuId(goods.getGoodsSku());
                    //给yph查询库存用
                    inv.setGoodsCode(goods.getGoodsCode());
                    invQuery.add(inv);
                }
                if (invQuery.size() > 20) {
                    List<List<InventoryDto>> inventoryArray = new ArrayList<>(invQuery.stream().collect(Collectors.groupingBy(s -> (invQuery.indexOf(s) / 20))).values());
                    for (List<InventoryDto> inventoryDtos : inventoryArray) {
                        final RemoteStockInfoReq rr = new RemoteStockInfoReq();
                        rr.setSkus(inventoryDtos);
                        rr.setRegin(region);
                        rr.setAddress(address);
                        respList.addAll(isa.get(rr));
                    }
                } else {
                    final RemoteStockInfoReq resp = new RemoteStockInfoReq();
                    resp.setSkus(invQuery);
                    resp.setRegin(region);
                    resp.setAddress(address);
                    respList.addAll(isa.get(resp));
                }
                respList.forEach(item ->{
                    if (item.getSupplierCode() == null) {
                        item.setSupplierCode(entry.getKey());
                    }
                });
            } finally {
                SupplierCodeContextHolder.clear();
                SupplierCodeIopContextHolder.clear();
            }
        }

        return respList;
    }


    /**
     * 批量获取库存
     *
     * @param req 要求事情
     * @return {@link Map}<{@link String}, {@link RemoteStockInfoResp}>
     */
    public Map<String, RemoteStockInfoResp> batchGetStock(final List<BatchGetStockReq> req) {
    final Map<String, RemoteStockInfoResp> res = new ConcurrentHashMap<>();
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        LoginUser user = LocalUserHolder.get();
        ThreadUtil.executeArrayAsync(() -> req, x -> TenantUtils.execute(tenantId, user, () -> {
            try {
                RemoteStockInfoResp stock = this.getStock(x.getGoodsCode(), x.getN(), x.getRegin(), x.getAddress());
                res.put(x.getGoodsCode(), stock);
            } catch (Exception ex) {
                res.put(x.getGoodsCode(), new RemoteStockInfoResp());
            }
        }), this.commonIoExecutors);
        return res;
    }

    public List<OrderDetailResp> getOrderDetail(final String thirdOrderId, final String jdOrderId, final String supplier) {
        Assert.notEmpty(thirdOrderId);
        Assert.notEmpty(jdOrderId);
        Assert.notEmpty(supplier);

        List<OrderDetailResp> orderDetailRespList = new ArrayList<>();
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            orderDetailRespList = this.jdProxy.getOrderDetail(thirdOrderId, jdOrderId);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                orderDetailRespList = this.jdProxy.getOrderDetail(thirdOrderId, jdOrderId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                orderDetailRespList = this.jdProxy.getOrderDetail(thirdOrderId, jdOrderId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)) {
            orderDetailRespList = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.getOrderDetail(thirdOrderId, jdOrderId),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (supplier.equals(this.zkhConfig.getCode())) {
            orderDetailRespList = this.zkhProxy.getOrderDetail(thirdOrderId, jdOrderId);
        } else if (supplier.equals(this.ofsConfig.getCode())) {
            orderDetailRespList = this.ofsProxy.getOrderDetail(thirdOrderId, jdOrderId);
        } else if (supplier.equals(this.qxConfig.getCode())) {
            orderDetailRespList = this.qxProxy.getOrderDetail(thirdOrderId, jdOrderId);
        } else if (supplier.equals(this.xyConfig.getCode())) {
            orderDetailRespList = this.xyProxy.getOrderDetail(thirdOrderId, jdOrderId);
        } else if (supplier.equals(this.dlConfig.getCode())) {
            orderDetailRespList = this.dlProxy.getOrderDetail(thirdOrderId, jdOrderId);
        } else if (supplier.equals(this.snConfig.getCode())) {
            orderDetailRespList = this.snProxy.getOrderDetail(thirdOrderId, jdOrderId);
        }
        return orderDetailRespList;
    }

    public DeliveryDetailResp getDeliveryDetail(final String thirdOrderId, final String packageId, final String supplier) {
        Assert.notEmpty(thirdOrderId);
        Assert.notEmpty(packageId);
        Assert.notEmpty(supplier);
        DeliveryDetailResp deliveryDetailResp = new DeliveryDetailResp();
        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(supplier)) {
            //查询数据库
            return trackOfLogisticsService.getTrackOfLogistics(packageId, supplier);
        }
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            deliveryDetailResp = this.jdProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                deliveryDetailResp = this.jdProxy.getDeliveryDetail(thirdOrderId, packageId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        }else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                deliveryDetailResp = this.jdProxy.getDeliveryDetail(thirdOrderId, packageId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)) {
            deliveryDetailResp = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.getDeliveryDetail(thirdOrderId, packageId),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (supplier.equals(this.zkhConfig.getCode())) {
            deliveryDetailResp = this.zkhProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.ofsConfig.getCode())) {
            deliveryDetailResp = this.ofsProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.qxConfig.getCode())) {
            deliveryDetailResp = this.qxProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.xyConfig.getCode())) {
            deliveryDetailResp = this.xyProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.dlConfig.getCode())) {
            deliveryDetailResp = this.dlProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.snConfig.getCode())) {
            deliveryDetailResp = this.snProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else {
            //独立供应商查快递100
            deliveryDetailResp = this.yphRemoteProxy.getDeliveryDetail(packageId, supplier);
        }
        return deliveryDetailResp;
    }

    public DeliveryDetailResp getDeliveryDetailExcluded100(final String thirdOrderId, final String packageId, final String supplier) {
        Assert.notEmpty(thirdOrderId);
        Assert.notEmpty(packageId);
        Assert.notEmpty(supplier);
        DeliveryDetailResp deliveryDetailResp = new DeliveryDetailResp();
        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(supplier)) {
            //查询数据库
            return trackOfLogisticsService.getTrackOfLogistics(packageId, supplier);
        }
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            deliveryDetailResp = this.jdProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                deliveryDetailResp = this.jdProxy.getDeliveryDetail(thirdOrderId, packageId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                deliveryDetailResp = this.jdProxy.getDeliveryDetail(thirdOrderId, packageId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)) {
            deliveryDetailResp = JdIopUntil.executeWithClear(
                    () -> this.zkhProxy.getDeliveryDetail(thirdOrderId, packageId),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (supplier.equals(this.zkhConfig.getCode())) {
            deliveryDetailResp = this.zkhProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.ofsConfig.getCode())) {
            deliveryDetailResp = this.ofsProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.qxConfig.getCode())) {
            deliveryDetailResp = this.qxProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.xyConfig.getCode())) {
            deliveryDetailResp = this.xyProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.dlConfig.getCode())) {
            deliveryDetailResp = this.dlProxy.getDeliveryDetail(thirdOrderId, packageId);
        } else if (supplier.equals(this.snConfig.getCode())) {
            deliveryDetailResp = this.snProxy.getDeliveryDetail(thirdOrderId, packageId);
        }
        return deliveryDetailResp;
    }

    public List<RemoteMoqInfoResp> getSkusMoq(List<String>skus,String supplierCode) {
        Assert.notEmpty(supplierCode);
        Assert.notEmpty(skus);
        RemoteMoqInfoReq r = new RemoteMoqInfoReq();
        r.setSkus(skus).setSupplierCode(supplierCode);
        if (openApiConfig.getSuppliers().contains(supplierCode)) {
            List<ShopGoodsVo> shopGoodsVos = goodSrv.selectVoBySkuAndSupplier(String.join(",", skus), supplierCode);
            return DataAdapter.convertList(shopGoodsVos, RemoteMoqInfoResp.class);
        }
        List<RemoteMoqInfoResp> remoteMoqInfoResps = new ArrayList<>();
        if (this.jdConfig.getCode().equals(supplierCode)) {
            SupplierCodeContextHolder.clear();
            remoteMoqInfoResps = this.jdProxy.doGetGoodsMoq(r);
        } else if (this.jdIntegralConfig.getCode().equals(supplierCode)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                remoteMoqInfoResps = this.jdProxy.doGetGoodsMoq(r);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        }else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplierCode);
            try {
                remoteMoqInfoResps = this.jdProxy.doGetGoodsMoq(r);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            remoteMoqInfoResps = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.doGetGoodsMoq(r),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplierCode) // 设置上下文
            );
        } else if (this.zkhConfig.getCode().equals(supplierCode)) {
            remoteMoqInfoResps = this.zkhProxy.doGetGoodsMoq(r);
        } else if (this.ofsConfig.getCode().equals(supplierCode)) {
            remoteMoqInfoResps = this.ofsProxy.doGetGoodsMoq(r);
        } else if (this.qxConfig.getCode().equals(supplierCode)) {
            remoteMoqInfoResps = this.qxProxy.doGetGoodsMoq(r);
        } else if (this.xyConfig.getCode().equals(supplierCode)) {
            remoteMoqInfoResps = this.xyProxy.doGetGoodsMoq(r);
        } else if (this.dlConfig.getCode().equals(supplierCode)) {
            remoteMoqInfoResps = this.dlProxy.doGetGoodsMoq(r);
        } else if (this.snConfig.getCode().equals(supplierCode)) {
            remoteMoqInfoResps = this.snProxy.doGetGoodsMoq(r);
        } else {
            remoteMoqInfoResps = this.yphRemoteProxy.doGetGoodsMoq(r);
        }

        return remoteMoqInfoResps;
    }

    public RemoteShelvesInfoResp getSkuStatus(String goodsCode) {
        IShelvesGetterAble shelvesGetter;
    val goods = this.goodSrv.selectOneByGoodsCode(goodsCode);
        AssertUtil.npeIsShit(goods, "未查到商品信息");

        // 如果是走API的，只要商品存在则可售
        if (openApiConfig.getSuppliers().contains(goods.getSupplierCode())) {
            List<RemoteShelvesInfoResp> remoteShelvesInfoResps = openCheckService.doGetShelves(goods.getSupplierCode(), new ArrayList<>(Collections.singletonList(goods.getGoodsSku())));
            return remoteShelvesInfoResps.get(0);
        }

        if (this.jdConfig.getCode().equals(goods.getSupplierCode())) {
            SupplierCodeContextHolder.clear();
            shelvesGetter = this.jdProxy;
        }  else if (this.jdIntegralConfig.getCode().equals(goods.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            shelvesGetter = this.jdProxy;
        }else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(goods.getSupplierCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(goods.getSupplierCode());
            shelvesGetter = this.jdProxy;
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(goods.getSupplierCode())){
            SupplierCodeIopContextHolder.setSupplierCodeProxy(goods.getSupplierCode());
            shelvesGetter = this.jdIopRemoteProxy;
        } else if (this.zkhConfig.getCode().equals(goods.getSupplierCode())) {
            shelvesGetter = this.zkhProxy;
        } else if (this.ofsConfig.getCode().equals(goods.getSupplierCode())) {
            shelvesGetter = this.ofsProxy;
        } else if (this.qxConfig.getCode().equals(goods.getSupplierCode())) {
            shelvesGetter = this.qxProxy;
        } else if (this.xyConfig.getCode().equals(goods.getSupplierCode())) {
            shelvesGetter = this.xyProxy;
        } else if (this.dlConfig.getCode().equals(goods.getSupplierCode())) {
            shelvesGetter = this.dlProxy;
        } else if (this.snConfig.getCode().equals(goods.getSupplierCode())) {
            shelvesGetter = this.snProxy;
        } else {
            List<RemoteShelvesInfoResp> remoteShelvesInfoResps = yphRemoteProxy.doGetShelves(goods.getSupplierCode(), new ArrayList<>(Collections.singletonList(goods.getGoodsSku())));
            return remoteShelvesInfoResps.get(0);
        }
        RemoteShelvesInfoResp infoResp;
        try {
            RemoteShelvesInfoReq req = new RemoteShelvesInfoReq();
            req.setSkuId(goods.getGoodsSku());
            val resp = shelvesGetter.get(req);

            //  // 更新上下架，不能每次查询就更新，因为会更新手动下架的商品
            //  if (!(shelvesGetter instanceof YphRemoteProxy)) {
            //      // 独立供应商不更新，因为独立供应商的上下架状态是手动处理的
            //      shelvesUpdate.updateStatus(goodsCode, resp.get(0).getIsShelves() ? 1 : 0);
            //  }
            infoResp = resp.get(0);
        } finally {
            SupplierCodeContextHolder.clear();
            SupplierCodeIopContextHolder.clear();
        }
        return infoResp;
    }

    /**
     * 批量获取供应商上下架状态
     *
     * @param goodsCodes
     * @return
     */
    public List<RemoteShelvesInfoResp> getBatchGoodsStatus(final String goodsCodes) {
        List<RemoteShelvesInfoResp> resp = new ArrayList<RemoteShelvesInfoResp>();
    val gs = this.goodSrv.selectListByGoodsCodes(goodsCodes);

        Map<String, List<ShopGoods>> groupSupplier = gs.stream().collect(Collectors.groupingBy(ShopGoods::getSupplierCode));
        for (Map.Entry<String, List<ShopGoods>> key : groupSupplier.entrySet()) {
            try {
                List<String> skuIds = key.getValue().stream().map(ShopGoods::getGoodsSku).collect(Collectors.toList());

                final IShelvesGetterAble idga;
                if (openApiConfig.getSuppliers().contains(key.getKey())) {
                    return openCheckService.doGetShelves(key.getKey(), skuIds);
                }
                if (this.jdConfig.getCode().equals(key.getKey())) {
                    SupplierCodeContextHolder.clear();
                    idga = this.jdProxy;
                } else if (this.jdIntegralConfig.getCode().equals(key.getKey())) {
                    SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
                    idga = this.jdProxy;
                } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(key.getKey())) {
                    SupplierCodeContextHolder.setSupplierCodeProxy(key.getKey());
                    idga = this.jdProxy;
                } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(key.getKey())){
                    SupplierCodeIopContextHolder.setSupplierCodeProxy(key.getKey());
                    idga = this.jdIopRemoteProxy;
                } else if (this.zkhConfig.getCode().equals(key.getKey())) {
                    idga = this.zkhProxy;
                } else if (this.ofsConfig.getCode().equals(key.getKey())) {
                    idga = this.ofsProxy;
                } else if (this.qxConfig.getCode().equals(key.getKey())) {
                    idga = this.qxProxy;
                } else if (this.xyConfig.getCode().equals(key.getKey())) {
                    idga = this.xyProxy;
                } else if (this.dlConfig.getCode().equals(key.getKey())) {
                    idga = this.dlProxy;
                } else if (this.snConfig.getCode().equals(key.getKey())) {
                    idga = this.snProxy;
                } else {
                    //独立供应商
                    return yphRemoteProxy.doGetShelves(key.getKey(), skuIds);
                }

                //因为部分供应商仅支持最大20个商品的批量查询，所以超过20的商品需要拆分
                if (skuIds.size() > 20) {
                    List<List<String>> skuArray = new ArrayList<>(skuIds.stream().collect(Collectors.groupingBy(s -> (skuIds.indexOf(s) / 20))).values());
                    for (List<String> skuId : skuArray) {
                        final RemoteShelvesInfoReq req = new RemoteShelvesInfoReq();
                        req.setSkuId(String.join(",", skuId));
                        resp.addAll(idga.get(req));
                    }

                } else {
                    final RemoteShelvesInfoReq req = new RemoteShelvesInfoReq();
                    req.setSkuId(String.join(",", skuIds));
                    resp.addAll(idga.get(req));
                }
                resp.forEach(item -> {
                    if (item.getSupplierCode() == null) {
                        item.setSupplierCode(key.getKey());
                    }
                });
            } finally {
                //每次循环 要么jd  要么jdjf 不影响
                SupplierCodeContextHolder.clear();
                SupplierCodeIopContextHolder.clear();
            }
        }

        return resp;
    }

    /**
     * 批量获取商品上下架状态
     *
     * @param goodCode
     *            好代码
     * @return {@code Map<String, RemotePriceInfoResp>}
     */
    public Map<String, RemoteShelvesInfoResp> batchGetSkuStatus(String[] goodCode) {
        AssertUtil.npeIsShit(goodCode, "商品编码不能为空");
        val goods = this.goodSrv.selectAllByGoodCodes(goodCode);
        AssertUtil.npeIsShit(goods, "查询出的商品信息不能为空");

        Map<String, RemoteShelvesInfoResp> res = new ConcurrentHashMap<>();
        LoginUser user = LocalUserHolder.get();
        //批量获取商品状态
        Long tenantId = TenantContextHolder.getTenantId();
        List<String> downCodes = new ArrayList<>();
        ThreadUtil.executeArrayAsync(() -> CollectionUtil.toList(goodCode), code -> {
            TenantUtils.execute(tenantId, user, () -> {
                val remoteStatus = this.getSkuStatus(code);
                res.put(code, remoteStatus);
                //供应商下架的商品，全平台下架
                if(!remoteStatus.getIsShelves() && remoteStatus.getSystemResult()) downCodes.add(code);
            });
        }, this.commonIoExecutors);
        downGoods(downCodes);
        return res;
    }

    @Async
    public void downGoods(List<String> codes){
        if(CollectionUtil.isNotEmpty(codes)){
            goodsUpDownManage.goodsCodeDown(codes,"供应商商品下架状态",null);
        }
    }

    /**
     * 申请售后单
     *
     * @param returnOrderDto
     * @param supplier
     * @return
     */
    public String applyForAfterSale(final ReturnOrderDto returnOrderDto, final String supplier) {
        Assert.notEmpty(supplier);
        Assert.notEmpty(returnOrderDto.getYphOrderId());
        Assert.notNull(returnOrderDto.getGoodsInfo());
        String afterSaleNo = null;
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            afterSaleNo = this.jdProxy.afterSale(returnOrderDto);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                afterSaleNo = this.jdProxy.afterSale(returnOrderDto);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                afterSaleNo = this.jdProxy.afterSale(returnOrderDto);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)) {
            afterSaleNo = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.afterSale(returnOrderDto),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (supplier.equals(this.zkhConfig.getCode())) {
            afterSaleNo = this.zkhProxy.afterSale(returnOrderDto);
        } else if (supplier.equals(this.xyConfig.getCode())) {
            afterSaleNo = this.xyProxy.afterSale(returnOrderDto);
        } else if (supplier.equals(this.ofsConfig.getCode())) {
            afterSaleNo = this.ofsProxy.afterSale(returnOrderDto);
        } else if (supplier.equals(this.qxConfig.getCode())) {
            afterSaleNo = this.qxProxy.afterSale(returnOrderDto);
        } else if (supplier.equals(this.dlConfig.getCode())) {
            afterSaleNo = this.dlProxy.afterSale(returnOrderDto);
        } else if (supplier.equals(this.snConfig.getCode())) {
            afterSaleNo = this.snProxy.afterSale(returnOrderDto);
        } else {
            throw new ParameterException("申请售后单失败");
        }
        return afterSaleNo;
    }

    /**
     * 取消售后申请单接口
     *
     * @param dsOrderId
     * @param returnOrderId
     * @param remark
     * @return
     */
    public CancelApplyResp cancelApply(final String dsOrderId, final String returnOrderId, final String remark, final String supplier) {
        Assert.notEmpty(supplier);
        Assert.notEmpty(dsOrderId);
        Assert.notEmpty(returnOrderId);
        ShopReturn shopReturn = shopReturnService.queryByReturnCode(returnOrderId);
        if (shopReturn == null) {
            throw new HttpException(SHOP_RETURN_NOT_FOUND);
        }
        final CancelApplyResp cancelApplyResp;
        if (this.jdConfig.getCode().equals(supplier)) {
            SupplierCodeContextHolder.clear();
            cancelApplyResp = this.jdProxy.cancelApply(dsOrderId, returnOrderId, remark);
        } else if (this.jdIntegralConfig.getCode().equals(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                cancelApplyResp = this.jdProxy.cancelApply(dsOrderId, returnOrderId, remark);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                cancelApplyResp = this.jdProxy.cancelApply(dsOrderId, returnOrderId, remark);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)) {
            cancelApplyResp = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.cancelApply(dsOrderId, returnOrderId, remark),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
//        } else if (this.zkhConfig.getCode().equals(supplier)) {
//            cancelApplyResp = this.zkhProxy.cancelApply(dsOrderId, returnOrderId, remark);
//        } else if (this.xyConfig.getCode().equals(supplier)) {
//            cancelApplyResp = this.xyProxy.cancelApply(dsOrderId, returnOrderId, remark);
//        } else if (this.ofsConfig.getCode().equals(supplier)) {
//            cancelApplyResp = this.ofsProxy.cancelApply(dsOrderId, returnOrderId, remark);
//        } else if (this.qxConfig.getCode().equals(supplier)) {
//            cancelApplyResp = this.qxProxy.cancelApply(dsOrderId, returnOrderId, remark);
//        } else if (this.dlConfig.getCode().equals(supplier)) {
//            cancelApplyResp = this.dlProxy.cancelApply(dsOrderId, returnOrderId, remark);
//        } else if (this.snConfig.getCode().equals(supplier)) {
//            cancelApplyResp = this.snProxy.cancelApply(dsOrderId, returnOrderId, remark);
        } else {
            //走线下售后取消
            shopReturnService.cancelApplyByUser(returnOrderId,remark);

            CancelApplyResp cancelApply = new CancelApplyResp();
            cancelApply.setCancelType(1);
            return cancelApply;
        }
        ShopReturn shopReturnNew = new ShopReturn();
        shopReturnNew.setId(shopReturn.getId()).setReturnState(GoodsReturnStateEnum.CANCEL_RETURN.getCode());
        shopReturnService.updateById(shopReturnNew);

        return cancelApplyResp;
    }


    /**
     * 电商-确认收货接口
     *
     * @param supplierOrderId (只对接了京东)供应商子订单号
     * @param orderNumber
     * @param supplier
     */
    public Boolean confirmReceive(final String supplierOrderId, final String orderNumber, final String supplier) {
        Assert.notEmpty(supplier);
        Assert.notEmpty(supplierOrderId);
        Assert.notEmpty(orderNumber);
        final Boolean result;
        if (this.jdConfig.getCode().equals(supplier)) {
            //暂时京东对接
            SupplierCodeContextHolder.clear();
            result = this.jdProxy.confirmReceive(supplierOrderId, orderNumber);
        } else if (this.jdIntegralConfig.getCode().equals(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                result = this.jdProxy.confirmReceive(supplierOrderId, orderNumber);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                result = this.jdProxy.confirmReceive(supplierOrderId, orderNumber);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)) {
            result = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.confirmReceive(supplierOrderId, orderNumber),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (this.zkhConfig.getCode().equals(supplier)) {
            result = this.zkhProxy.confirmReceive(supplierOrderId, orderNumber);
        } else if (this.xyConfig.getCode().equals(supplier)) {
            result = this.xyProxy.confirmReceive(supplierOrderId, orderNumber);
        } else if (this.ofsConfig.getCode().equals(supplier)) {
            result = this.ofsProxy.confirmReceive(supplierOrderId, orderNumber);
        } else if (this.qxConfig.getCode().equals(supplier)) {
            result = this.qxProxy.confirmReceive(supplierOrderId, orderNumber);
        } else if (this.dlConfig.getCode().equals(supplier)) {
            result = this.dlProxy.confirmReceive(supplierOrderId, orderNumber);
        } else if (this.snConfig.getCode().equals(supplier)) {
            result = this.snProxy.confirmReceive(supplierOrderId, orderNumber);
        } else {
            return false;
        }
        return result;
    }

    /**
     * 电商-批量查询订单下商品售后权益
     *
     * @param supplierOrderId
     * @param orderNumber
     * @param skuIds
     */
    public List<SupportedInfoOpenResp> getGoodsAttributes(final String supplierOrderId, final String orderNumber, final List<Long> skuIds, final String supplier) {
        Assert.notEmpty(supplierOrderId);
        Assert.notEmpty(orderNumber);
        Assert.notNull(skuIds);
        Assert.notEmpty(supplier);
        final List<SupportedInfoOpenResp> resp;
        if (this.jdConfig.getCode().equals(supplier)) {
            //暂时京东对接
            SupplierCodeContextHolder.clear();
            resp = this.jdProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else if (this.jdIntegralConfig.getCode().equals(supplier)){
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                resp = this.jdProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                resp = this.jdProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplier)){
            resp= JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplier) // 设置上下文
            );
        } else if (this.zkhConfig.getCode().equals(supplier)) {
            resp = this.zkhProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else if (this.xyConfig.getCode().equals(supplier)) {
            resp = this.xyProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else if (this.ofsConfig.getCode().equals(supplier)) {
            resp = this.ofsProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else if (this.qxConfig.getCode().equals(supplier)) {
            resp = this.qxProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else if (this.dlConfig.getCode().equals(supplier)) {
            resp = this.dlProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else if (this.snConfig.getCode().equals(supplier)) {
            resp = this.snProxy.getGoodsAttributes(supplierOrderId, orderNumber, skuIds);
        } else {
            //接口平台 或者独立供应商
            resp = new ArrayList<>();
        }
        return resp;
    }

    /**
     * 京东-批量查询同类商品
     *
     * @param skuId
     * @param supplierCode
     */
    public List<GetSimilarSkuGoodsResp> getSimilarSkuList(String skuId, String supplierCode) {
        Assert.notEmpty(skuId);
        Assert.notEmpty(supplierCode);
        List<GetSimilarSkuGoodsResp> list = new ArrayList<>();
        if (supplierCode.equals(jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                list = jdProxy.getSimilarSkuList(skuId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplierCode);
            try {
                list = jdProxy.getSimilarSkuList(skuId);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (this.jdConfig.getCode().equals(supplierCode)) {
            SupplierCodeContextHolder.clear();
            list = jdProxy.getSimilarSkuList(skuId);
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            list = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.getSimilarSkuList(skuId),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplierCode) // 设置上下文
            );
        }
        return list;
    }


    public SupplierAreaResp queryAreaInfo(final String addStr, final String supplier) {
        Assert.notEmpty(addStr);
        Assert.notEmpty(supplier);
        final SupplierAreaResp resp;
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            resp = areaInfo(addStr);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                resp = areaInfo(addStr);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                resp = areaInfo(addStr);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else {
            throw new ParameterException("供应商地址失败");
        }
        return resp;
    }

    public SupplierAreaResp areaInfo(String addStr){
        final JdQueryAreaIdResp jdQueryAreaIdResp = this.jdProxy.queryAreaId(addStr);
        final SupplierAreaResp resp = DataAdapter.convert(jdQueryAreaIdResp, SupplierAreaResp.class);
        resp.setProvinceId(jdQueryAreaIdResp.getProvinceId().intValue()).setCityId(jdQueryAreaIdResp.getCityId().intValue()).setCountyId(jdQueryAreaIdResp.getCountyId().intValue());
        Optional.ofNullable(jdQueryAreaIdResp.getTownId()).ifPresent(s -> {
            resp.setTownId(jdQueryAreaIdResp.getTownId().intValue());
        });
        return resp;
    }

    public void updateSendInfo(final AfterSaleWayBillDto dto) {
        final String supplier = dto.getSourceType();
        final UpdateAfterSaleWayBillOpenReq req = new UpdateAfterSaleWayBillOpenReq();
        final List<AfterSaleWayBillDto.WaybillInfoVoOpenReq> waybillInfoVoOpenReqList = dto.getWaybillInfoVoOpenReqList();
        final List<UpdateAfterSaleWayBillOpenReq.WaybillInfoVoOpenReq> list = DataAdapter.convertList(waybillInfoVoOpenReqList, UpdateAfterSaleWayBillOpenReq.WaybillInfoVoOpenReq.class);
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            req.setOrderId(Long.valueOf(dto.getPackageId())).setThirdApplyId(dto.getThirdApplyId()).setCustomerPin(this.jdConfig.getUsername()).setWaybillInfoVoOpenReqList(list);
            this.jdProxy.updateSendInfo(req);
        } else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            req.setOrderId(Long.valueOf(dto.getPackageId())).setThirdApplyId(dto.getThirdApplyId()).setCustomerPin(this.jdIntegralConfig.getUsername()).setWaybillInfoVoOpenReqList(list);
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                this.jdProxy.updateSendInfo(req);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            Map<String, JDGeneralConfig.GeneralConfig> map = jdGeneralConfig.getConfigList().stream().collect(Collectors.toMap(JDGeneralConfig.GeneralConfig::getCode, e -> e));
            req.setOrderId(Long.valueOf(dto.getPackageId())).setThirdApplyId(dto.getThirdApplyId()).setCustomerPin(map.get(supplier).getUsername()).setWaybillInfoVoOpenReqList(list);
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                this.jdProxy.updateSendInfo(req);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else {
            throw new ParameterException("填写运单信息失败");
        }
    }

    /**
     * 填充价格小于0 带询价 状态 提示友好提示
     *
     * @param remotePriceInfoResp
     * @param errorGoodsPriceMsgEnum
     */
    private void fillPriceMsg(RemotePriceInfoResp remotePriceInfoResp, ErrorGoodsPriceMsgEnum errorGoodsPriceMsgEnum) {
        remotePriceInfoResp.setMsg(errorGoodsPriceMsgEnum.getMessage());
        remotePriceInfoResp.setMsgType(errorGoodsPriceMsgEnum.getType());
    }

    public void applyForAfterSaleByGoodsList(final ReturnOrderReq returnOrderReq, final String supplier) {
        Assert.notEmpty(supplier);
        Assert.notNull(returnOrderReq);
        if (supplier.equals(this.jdConfig.getCode())) {
            SupplierCodeContextHolder.clear();
            this.jdProxy.afterSale(returnOrderReq);
        }  else if (supplier.equals(this.jdIntegralConfig.getCode())) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                this.jdProxy.afterSale(returnOrderReq);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplier)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplier);
            try {
                this.jdProxy.afterSale(returnOrderReq);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else {
            throw new ParameterException("申请售后失败");
        }
    }

    public Boolean checkSup(String supplier) {
        if (openApiConfig.getSuppliers().contains(supplier)
                || supplier.equals(this.jdConfig.getCode())
                || supplier.equals(this.zkhConfig.getCode())
                || supplier.equals(this.ofsConfig.getCode())
                || supplier.equals(this.qxConfig.getCode())
                || supplier.equals(this.xyConfig.getCode())
                || supplier.equals(this.dlConfig.getCode())
                || supplier.equals(this.snConfig.getCode())) {
            //查询电商
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取供应商提供的商品咨询客服链接
     * 暂只对接京东iop 和京东vop  todo 可支持电商编码:(待补充)
     * @param supplierCode
     * @param goodsSku
     * @param type 类型，默认值为0, 0 商品 1 订单
     * @return
     */
    public String goodsSupportStaff(String supplierCode, String goodsSku,Integer type,String unique) {
        String chatUrl = "";
        if (this.jdConfig.getCode().equals(supplierCode)) {
            SupplierCodeContextHolder.clear();
            chatUrl = this.jdProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.jdIntegralConfig.getCode().equals(supplierCode)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(this.jdIntegralConfig.getCode());
            try {
                chatUrl = this.jdProxy.goodsSupportStaff(goodsSku,unique,type);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdGeneralConfig.getConfigList()) && jdGeneralConfig.getConfigList().stream().map(JDGeneralConfig.GeneralConfig::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            SupplierCodeContextHolder.setSupplierCodeProxy(supplierCode);
            try {
                chatUrl = this.jdProxy.goodsSupportStaff(goodsSku,unique,type);
            } finally {
                SupplierCodeContextHolder.clear();
            }
        } else if (CollectionUtil.isNotEmpty(jdIopConfig.getConfigList()) && jdIopConfig.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList()).contains(supplierCode)) {
            chatUrl = JdIopUntil.executeWithClear(
                    () -> this.jdIopRemoteProxy.goodsSupportStaff(goodsSku,unique,type),
                    () -> SupplierCodeIopContextHolder.setSupplierCodeProxy(supplierCode) // 设置上下文
            );
        } else if (this.zkhConfig.getCode().equals(supplierCode)) {
            chatUrl = this.zkhProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.ofsConfig.getCode().equals(supplierCode)) {
            chatUrl = this.ofsProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.qxConfig.getCode().equals(supplierCode)) {
            chatUrl = this.qxProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.xyConfig.getCode().equals(supplierCode)) {
            chatUrl = this.xyProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.dlConfig.getCode().equals(supplierCode)) {
            chatUrl = this.dlProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.snConfig.getCode().equals(supplierCode)) {
            chatUrl = this.snProxy.goodsSupportStaff(goodsSku,unique,type);
        } else if (this.yzhConfig.getCode().equals(supplierCode)) {
            chatUrl = this.yzhProxy.goodsSupportStaff(goodsSku,unique,type);
        } else {
            //友品汇平台商品 库存
            chatUrl = this.yphRemoteProxy.goodsSupportStaff(goodsSku,unique,type);
        }
        return chatUrl;
    }
}
