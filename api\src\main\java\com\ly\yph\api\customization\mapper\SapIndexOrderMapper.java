package com.ly.yph.api.customization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.yph.api.customization.dto.IndexOrderListParamDto;
import com.ly.yph.api.customization.dto.SapIndexOrderDto;
import com.ly.yph.api.customization.dto.WaitIndexListParamDto;
import com.ly.yph.api.customization.entity.SapIndexOrderEntity;
import com.ly.yph.api.customization.vo.SapDeliveryVo;
import com.ly.yph.api.customization.vo.SapIndexOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * sap索引单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 09:37:12
 */
@Mapper
public interface SapIndexOrderMapper extends BaseMapper<SapIndexOrderEntity> {
    /**
     * SQL方式保存
     *
     * @param sapIndexOrderEntity
     * @return
     */
    Integer saveSapIndexOrder(SapIndexOrderEntity sapIndexOrderEntity);

    /**
     * SQL方式更新
     *
     * @param sapIndexOrderEntity
     * @return
     */
    Integer updateSapIndexOrder(SapIndexOrderEntity sapIndexOrderEntity);

    /**
     * 分页查询Vo集合
     */
    IPage<SapIndexOrderVo> queryPageVo(IPage<SapIndexOrderVo> page, @Param("query") IndexOrderListParamDto indexOrderListParamDto);

    List<SapIndexOrderDto> exportIndexOrderList(@Param("query") IndexOrderListParamDto indexOrderListParamDto);

    List<SapDeliveryVo> querySapIndexDetalExport(@Param("query") WaitIndexListParamDto waitIndexListParamDto);

    /**
     * 查询待结算清单
     * @param page
     * @param waitIndexListParamDto
     * @return
     */
    IPage<SapDeliveryVo> waitIndexList(IPage<SapIndexOrderVo> page, @Param("query") WaitIndexListParamDto waitIndexListParamDto);

    List<String> getIndexOrderSnRank(@Param("indexOrderSn") String orderSnRank);
}
