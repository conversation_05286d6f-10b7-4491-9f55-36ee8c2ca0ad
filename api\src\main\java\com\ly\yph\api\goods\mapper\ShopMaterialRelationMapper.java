package com.ly.yph.api.goods.mapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.yph.api.goods.dto.MaraQueryDto;
import com.ly.yph.api.goods.dto.QuerySeekMaterialDto;
import com.ly.yph.api.goods.entity.ShopMaterialRelationEntity;
import com.ly.yph.api.goods.vo.ShopMaterialRelationVo;
import com.ly.yph.core.base.database.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ShopMaterialRelationMapper extends BaseMapperX<ShopMaterialRelationEntity> {
    IPage<ShopMaterialRelationVo> queryPage(
            IPage<ShopMaterialRelationVo> pageReq,
            @Param("queryDto") MaraQueryDto maraQueryDto
    );

    List<String> selectByGoodsCode(@Param(value = "goodsCodes") Collection<String> list, @Param(value = "entityOrganizationCode") String entityOrganizationCode);

    default List<ShopMaterialRelationEntity> selectSimpleList(Collection<String> goodsCodeCollection, String companyCode) {
        if(goodsCodeCollection.isEmpty()){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ShopMaterialRelationEntity> myQuery = Wrappers.lambdaQuery(ShopMaterialRelationEntity.class);
        myQuery.in(ShopMaterialRelationEntity::getGoodsCode, goodsCodeCollection);
        myQuery.eq(ShopMaterialRelationEntity::getCompanyCode, companyCode);
        myQuery.eq(ShopMaterialRelationEntity::getIsDel, 0);
        myQuery.select(ShopMaterialRelationEntity::getMaraId,
                ShopMaterialRelationEntity::getGoodsCode,
                ShopMaterialRelationEntity::getCompanyCode);
        return selectList(myQuery);
    }

    List<QuerySeekMaterialDto> queryNoCreateMaterial();

}
