<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.settlement.common.mapper.SettleCheckDetailMapper">

    <delete id="deleteByIds">
        DELETE FROM settle_check_form_detail WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </delete>
    <select id="getCheckFormDetailVo" resultType="com.ly.yph.api.settlement.common.vo.check.CheckFormDetailVo">
        select scfd.*,
               spso.purchase_number,
               spso.order_number
        from settle_check_form_detail scfd
                 left join shop_delivery_detail sdd on sdd.id = scfd.delivery_detail_id
                 left join shop_delivery sd on sd.id = sdd.delivery_id
                 left join shop_purchase_sub_order spso on spso.order_id = sd.order_id
        where scfd.check_form_id = #{checkFormId}
    </select>

    <select id="getUpdateOrderDetailForCheckForm"
            resultType="com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto">
        select spsod.order_detail_id,
               spso.order_number,
               sum(scfd.check_num + scfd.check_num_decimal) as customerAcceptanceNum,
               spo.company_code,
               spo.order_sales_channel
        from settle_check_form scf
                 left join settle_check_form_detail scfd on scf.id = scfd.check_form_id
                 left join shop_delivery_detail sdd on sdd.id = scfd.delivery_detail_id
                 left join shop_delivery sd on sd.id = sdd.delivery_id
                 left join shop_purchase_sub_order_detail spsod
                           on spsod.order_number = sd.order_number and scfd.goods_code = spsod.goods_code
                 left join shop_purchase_sub_order spso on spso.order_id = spsod.order_id
                 left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        where scf.id = #{checkFormId}
          and spso.supplier_data_source = 'dfmall'
          and spso.is_platform_reconciliation =1
          and spo.sap_order_type in (0, 4)
        group by spsod.order_detail_id
    </select>

    <select id="getCompanyBillMessageForCheckFormId"
            resultType="com.ly.yph.api.settlement.common.dto.check.PreSupplierBillOutMessageDto">
        select scfd.id as checkFormDetailId,
               sdd.id  as deliveryDetailId,
               sd.is_history as isHistory,
               spo.company_code,
               spo.sap_order_type
        from settle_check_form_detail scfd
                 left join shop_delivery_detail sdd on sdd.id = scfd.delivery_detail_id
                 left join shop_delivery sd on sd.id = sdd.delivery_id
                 left join shop_purchase_sub_order_detail spsod
                           on spsod.order_number = sd.order_number and scfd.goods_code = spsod.goods_code
                 left join shop_purchase_sub_order spso on spso.order_id = spsod.order_id
                 left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        where scfd.check_form_id = #{checkFormId}
          and spso.supplier_data_source = 'dfmall'
          and spso.is_platform_reconciliation = 1
          and spo.sap_order_type not in (1,3,5)
    </select>

</mapper>
