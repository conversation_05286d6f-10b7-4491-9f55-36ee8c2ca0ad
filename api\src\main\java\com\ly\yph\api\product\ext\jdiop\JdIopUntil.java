package com.ly.yph.api.product.ext.jdiop;

import com.ly.yph.api.product.ext.jdiop.config.JdIopConfig;
import com.ly.yph.api.product.ext.jdiop.config.JdIopConfigInfo;
import com.ly.yph.api.product.ext.jdiop.context.SupplierCodeIopContextHolder;
import com.ly.yph.core.base.exception.types.DataRequestException;
import com.ly.yph.core.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 囧东工具
 *
 * <AUTHOR>
 * @date 2022/02/18
 */
@Component
@Slf4j
public class JdIopUntil {
    @Resource
    private JdIopConfig config;

    public JdIopConfigInfo resetConfig(String supplierCodeProxy) {
        Map<String, JdIopConfigInfo> configMap = config.getConfigList().stream().collect(Collectors.toMap(JdIopConfigInfo::getCode, Function.identity()));
        return  configMap.get(supplierCodeProxy);
    }
    /**
     * gnerate标志
     *
     * @param map 地图
     * @return {@code String}
     */
    public String gnerateSign(Map<String, String> map) {
        Set set = map.keySet();
        Object[] arr = set.toArray();
        Arrays.sort(arr);
        // 拼接参数
        // 头部拼接appSecret
        StringBuilder stringBuild = new StringBuilder();

        stringBuild.append(map.get("client_secret"))
                .append(map.get("timestamp"))
                .append(map.get("client_id"))
                .append(map.get("username"))
                .append(map.get("password"))
                .append(map.get("grant_type"))
                .append(map.get("client_secret"));

        return MD5Util.MD5Encode(stringBuild.toString(), "UTF-8");
    }

    public static void executeWithClear(Runnable action, Runnable contextSetter) {
        try {
            contextSetter.run(); // 设置上下文
            action.run(); // 执行主逻辑
        } finally {
            SupplierCodeIopContextHolder.clear(); // 清理上下文
        }
    }
    public static <T> T executeWithClear(Callable<T> action, Runnable contextSetter) {
        try {
            contextSetter.run(); // 设置上下文
            return action.call(); // 执行主逻辑并返回结果
        } catch (DataRequestException ex) {
            throw new DataRequestException(ex.getResult());
        } catch (Exception e) {
            // 处理异常情况，比如记录日志等
            throw new RuntimeException("执行过程中出现异常", e);
        } finally {
            SupplierCodeIopContextHolder.clear(); // 清理上下文
        }
    }
}
