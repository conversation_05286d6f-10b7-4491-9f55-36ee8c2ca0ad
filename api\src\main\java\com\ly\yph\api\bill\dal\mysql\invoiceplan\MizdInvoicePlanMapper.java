package com.ly.yph.api.bill.dal.mysql.invoiceplan;

import java.time.LocalDate;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.yph.api.bill.controller.plan.vo.MizdInvoicePlanPageReqExtVO;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.math.BigDecimal;

import com.ly.yph.api.bill.controller.plan.vo.MizdInvoicePlanPageReqVO;
import com.ly.yph.api.bill.dal.dataobject.invoiceplan.MizdInvoicePlanDO;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import cn.hutool.core.util.StrUtil;

/**
 * 账单计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MizdInvoicePlanMapper extends BaseMapperX<MizdInvoicePlanDO> {

  default PageResp<MizdInvoicePlanDO> selectPage(PageReq pager, MizdInvoicePlanPageReqVO reqVO) {
    return selectPage(
        pager,
        new LambdaQueryWrapperX<MizdInvoicePlanDO>()
            .eqIfPresent(MizdInvoicePlanDO::getPayNo, reqVO.getPayNo())
            .eqIfPresent(MizdInvoicePlanDO::getPayPlanStatus, reqVO.getPayPlanStatus())
            .betweenIfPresent(
                MizdInvoicePlanDO::getApprovedCompletedTime, reqVO.getApprovedCompletedTime())
            .eqIfPresent(MizdInvoicePlanDO::getInvoiceNumber, reqVO.getInvoiceNumber())
            .likeIfPresent(MizdInvoicePlanDO::getInvoiceName, reqVO.getInvoiceName())
            .eqIfPresent(MizdInvoicePlanDO::getPayType, reqVO.getPayType())
            .eqIfPresent(MizdInvoicePlanDO::getPayEntity, reqVO.getPayEntity())
            .eqIfPresent(MizdInvoicePlanDO::getPurchasingManager, reqVO.getPurchasingManager())
            .betweenIfPresent(MizdInvoicePlanDO::getPayDate, reqVO.getPayDate())
            .eqIfPresent(MizdInvoicePlanDO::getProjectNo, reqVO.getProjectNo())
            .likeIfPresent(MizdInvoicePlanDO::getProjectName, reqVO.getProjectName())
            .eqIfPresent(MizdInvoicePlanDO::getBusinessContractId, reqVO.getBusinessContractId())
            .likeIfPresent(MizdInvoicePlanDO::getContractName, reqVO.getContractName())
            .eqIfPresent(MizdInvoicePlanDO::getBank, reqVO.getBank())
            .eqIfPresent(MizdInvoicePlanDO::getReceiveCompany, reqVO.getReceiveCompany())
            .eqIfPresent(MizdInvoicePlanDO::getReceiveAccount, reqVO.getReceiveAccount())
            .eqIfPresent(MizdInvoicePlanDO::getBudgetNumber, reqVO.getBudgetNumber())
            .eqIfPresent(MizdInvoicePlanDO::getHedge, reqVO.getHedge())
            .eqIfPresent(MizdInvoicePlanDO::getPayMethod, reqVO.getPayMethod())
            .eqIfPresent(MizdInvoicePlanDO::getPaymentRroperty, reqVO.getPaymentRroperty())
            .eqIfPresent(MizdInvoicePlanDO::getProjectBelongModule, reqVO.getProjectBelongModule())
            .eqIfPresent(
                MizdInvoicePlanDO::getPurchaseApplyCreatorDeptName,
                reqVO.getPurchaseApplyCreatorDeptName())
            .eqIfPresent(
                MizdInvoicePlanDO::getPurchasingManagerCode, reqVO.getPurchasingManagerCode())
           .eqIfPresent(
                        MizdInvoicePlanDO::getExternalBusinessOrderNumber, reqVO.getExternalBusinessOrderNumber())
            .orderByDesc(MizdInvoicePlanDO::getId),
        MizdInvoicePlanDO.class);
  }

  default PageResp<MizdInvoicePlanDO> selectPageExt(
      PageReq pager, MizdInvoicePlanPageReqExtVO reqVO) {
    final List<String> status = StrUtil.split(reqVO.getPayPlanStatus(), ",");
    return selectPage(
        pager,
        new LambdaQueryWrapperX<MizdInvoicePlanDO>()
            .eqIfPresent(MizdInvoicePlanDO::getInvoiceNumber, reqVO.getInvoiceNumber())
            .eqIfPresent(MizdInvoicePlanDO::getProjectNo, reqVO.getProjectNo())
            .likeIfPresent(MizdInvoicePlanDO::getProjectName, reqVO.getProjectName())
            .eqIfPresent(MizdInvoicePlanDO::getBusinessContractId, reqVO.getBusinessContractId())
            .likeIfPresent(MizdInvoicePlanDO::getContractName, reqVO.getContractName())
            .eqIfPresent(MizdInvoicePlanDO::getReceiveCompany, reqVO.getReceiveCompany())
            .betweenIfPresent(MizdInvoicePlanDO::getContractAmount, reqVO.getContractAmount())
            .inIfPresent(MizdInvoicePlanDO::getPayPlanStatus, status)
            .eqIfPresent(
                MizdInvoicePlanDO::getPurchasingManagerCode, reqVO.getPurchasingManagerCode())
            .orderByDesc(MizdInvoicePlanDO::getId),
        MizdInvoicePlanDO.class);
  }

  default int updatePayPlanStatusByPayNo(String updatedPayPlanStatus, String payNo,
                                         String externalBusinessOrderNumber,String currentStepName) {
    LambdaUpdateWrapper<MizdInvoicePlanDO> myQuery = Wrappers.lambdaUpdate(MizdInvoicePlanDO.class);
    myQuery.eq(MizdInvoicePlanDO::getPayNo, payNo);
    myQuery.set(MizdInvoicePlanDO::getPayPlanStatus, updatedPayPlanStatus);
    myQuery.set(MizdInvoicePlanDO::getApprovedCompletedTime, LocalDate.now());
    if(StringUtils.isNotBlank(externalBusinessOrderNumber)){
        myQuery.set(MizdInvoicePlanDO::getExternalBusinessOrderNumber,externalBusinessOrderNumber);
    }
    if(StringUtils.isNotBlank(currentStepName)){
        myQuery.set(MizdInvoicePlanDO::getCurrentStepName,currentStepName);
    }
    return update(null, myQuery);
  }

  /**
   * 计算指定账单编号列表的已导入有效支付金额总和
   *
   * @param invoiceNumbers 账单编号列表
   * @return 已导入有效支付金额总和
   */
  BigDecimal sumValidImportedAmount(@Param("invoiceNumbers") List<String> invoiceNumbers);

  default List<MizdInvoicePlanDO> selectByPayNo(String payNo) {
    LambdaQueryWrapper<MizdInvoicePlanDO> myQuery = Wrappers.lambdaQuery(MizdInvoicePlanDO.class);
    myQuery.eq(MizdInvoicePlanDO::getPayNo, payNo);
    return selectList(myQuery);
  }

    List<Long> getMizdInvoicePlanTimes(@Param("invoiceNumber") String invoiceNumber);
}
