<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ly.yph.api.supplier.mapper.SystemContractMapper">
    <select id="queryList"
            resultType="com.ly.yph.api.organization.controller.organization.vo.organization.SystemContractVO">
        SELECT *
        FROM system_contract c
                 LEFT JOIN supplier_contract_real b ON c.id = b.contract_id
        WHERE c.sign_company_id = #{reqVO.signCompanyId}
          AND b.supplier_id = #{reqVO.supplierId}
          AND b.is_start = 1
    </select>

    <select id="queryInfo"
            resultType="com.ly.yph.api.organization.controller.organization.vo.organization.SystemContractVO">
        SELECT *
        FROM system_contract c
                 LEFT JOIN supplier_contract_real b ON c.id = b.contract_id
        WHERE c.sign_company_id = #{reqVO.signCompanyId}
          AND b.supplier_id = #{reqVO.supplierId}
          AND c.contract_code = #{reqVO.contractCode}
          AND b.is_start = 1
    </select>
    <select id="queryInfoByContractId"
            resultType="com.ly.yph.api.organization.controller.organization.vo.organization.SystemContractVO">
        SELECT *
        FROM system_contract c
                 LEFT JOIN supplier_contract_real b ON c.id = b.contract_id
        WHERE c.id = #{contractId}
    </select>
    <select id="queryBySupplierCode"
            resultType="com.ly.yph.api.organization.controller.organization.vo.organization.SystemContractVO">
        SELECT
            c.sign_company_id,
            c.sign_company,
            c.sign_company_code,
            c.contract_name,
            c.contract_code,
            b.contract_scanned_url,
            b.contract_start_date,
            b.contract_end_date,
            b.is_start
        FROM
            system_contract c
                LEFT JOIN supplier_contract_real b ON c.id = b.contract_id
                LEFT JOIN shop_supplier ss ON ss.supplier_id = b.supplier_id
        WHERE
            ss.supplier_code = #{supplierCode}
          AND b.is_start = 1
    </select>
    <select id="queryGoodsListByContract" resultType="com.ly.yph.api.goods.vo.ShopGoodsContractVo">
        SELECT
            sgs.goods_id,
            sgs.goods_code,
            sgs.contract_id,
            sgs.supplier_id,
            sc.sign_company_code,
            sc.sign_company_id,
            sc.sign_company,
            sc.contract_name,
            sgs.org_type,
            sc.contract_code,
            g.shelves_state
        FROM system_contract sc
            LEFT JOIN shop_goods_contract sgs on sc.id = sgs.contract_id and sgs.is_enable = 1
            LEFT JOIN shop_goods g on g.goods_id = sgs.goods_id
        WHERE sc.id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryVoByGoodsIdCompanyCode" resultType="com.ly.yph.api.goods.vo.ShopGoodsContractVo">
        SELECT sgs.goods_id,
               sgs.contract_id,
               sc.sign_company_code,
               sc.sign_company_id,
               sc.sign_company,
               sc.contract_name,
               sc.contract_code
        from shop_goods_contract sgs
                 LEFT JOIN supplier_contract_real scr ON scr.supplier_id = sgs.supplier_id
            AND sgs.contract_id = scr.contract_id
                 LEFT JOIN system_contract sc ON sc.id = sgs.contract_id
        WHERE scr.is_start = 1
          and sgs.goods_id = #{goodsId}
    </select>

    <select id="queryVoBySupplierId" resultType="com.ly.yph.api.supplier.vo.ShopSupplierContractVo">
        select *
        from system_contract c
                 left join supplier_contract_real cr on c.id = cr.contract_id
        where cr.supplier_id = #{supplierId}
          and c.is_enable = 1
    </select>

    <select id="queryValidateContractBySupplierId" resultType="com.ly.yph.api.supplier.vo.ShopSupplierContractVo">
        select *
        from system_contract c
        inner join supplier_contract_real cr on c.id = cr.contract_id
        where cr.supplier_id = #{supplierId}
        and is_start = 1
        and c.is_enable = 1
    </select>
    <select id="queryVoyahContractPage" resultType="com.ly.yph.api.customization.vo.ExtContractInfoVo">
        select sc.id,
            sc.contract_code,
               sc.contract_name,
               ss.supplier_full_name as supplier_name,
               ss.supplier_code,
               ssc.contract_start_date,
               ssc.contract_end_date,
               group_concat(so.name SEPARATOR ',') as deptName,
               sc.contract_limit,ssc.is_start,ssc.contract_real_end_date
        from system_contract sc
                 left join supplier_contract_real ssc on ssc.contract_id = sc.id
                 left join shop_supplier ss on ss.supplier_id = ssc.supplier_id
                 left join system_contract_permission scp on sc.id = scp.contract_id
                 left join system_organization so on scp.dept_id = so.id
        where sc.is_enable = 1 and ss.data_source = #{reqVO.companyCode}
        <if test="reqVO.contractCode != null and reqVO.contractCode != ''">
            and sc.contract_code = #{reqVO.contractCode}
        </if>
        <if test="reqVO.contractName != null and reqVO.contractName != ''">
            and sc.contract_name like concat('%', #{reqVO.contractName}, '%')
        </if>
        <if test="reqVO.supplierName != null and reqVO.supplierName != ''">
            and ss.supplier_full_name like concat('%', #{reqVO.supplierName}, '%')
        </if>
        <if test="reqVO.isStart != null and reqVO.isStart != ''">
            and ssc.is_start = #{reqVO.isStart}
        </if>
        group by sc.id;
    </select>
    <select id="queryContractMaterPage" resultType="com.ly.yph.api.customization.vo.ExtContractGoodsInfoVo">
        select sg.goods_sku,
        smr.mara_matnr,
        sg.goods_name,
        sg.brand_name,
        sg.supplier_code,
        sg.materials_code,
        sg.spec_goods_ware_qd,
        sgd.goods_moq,
        ygps.goods_pact_naked_price,
        ygps.goods_pact_price,
        sg.tax_rate,
        sc.contract_code,
        ssc.contract_start_date,
        ssc.contract_end_date,
        ss.supplier_full_name as supplier_name,
        sg.goods_code,sg.sale_unit
        from shop_goods_contract sgc
        left join system_contract sc on sgc.contract_id = sc.id
        left join supplier_contract_real ssc on ssc.contract_id = sc.id
        left join shop_supplier ss on ss.supplier_id = ssc.supplier_id
        left join shop_goods sg on sgc.goods_id = sg.goods_id
        left join shop_goods_detail sgd on sgd.goods_code = sg.goods_code
        left join yph_goods_price_strategy ygps
        on ygps.goods_sku = sg.goods_sku and ygps.supplier_code = ss.supplier_code
        left join shop_material_relation smr on sg.goods_code = smr.goods_code
        where sg.tenant_id = sgc.tenant_id
        <if test="reqVO.companyCode != null and reqVO.companyCode.equalsIgnoreCase('voyah')">
            and smr.mara_werks = '1226'
        </if>
        and sg.is_enable = 1
        and sgd.is_enable = 1
        and ygps.is_enable = 1
        and sgc.audit_state = 1
        and smr.company_code = #{reqVO.companyCode}
        and ss.data_source = #{reqVO.dataSource}
        <if test="reqVO.contractCode != null and reqVO.contractCode != ''">
            and sc.contract_code = #{reqVO.contractCode}
        </if>
        <if test="reqVO.goodsSku != null and reqVO.goodsSku != ''">
            and sg.goods_sku = #{reqVO.goodsSku}
        </if>
        <if test="reqVO.maraMatnr != null and reqVO.maraMatnr != ''">
            and smr.mara_matnr = #{reqVO.maraMatnr}
        </if>
        <if test="reqVO.supplierName != null and reqVO.supplierName != ''">
            and ss.supplier_full_name = #{reqVO.supplierName}
        </if>
        <if test="reqVO.goodsName != null and reqVO.goodsName != ''">
            and sg.goods_name like CONCAT('%',#{reqVO.goodsName}, '%')
        </if>
        <if test="reqVO.brandName != null and reqVO.brandName != ''">
            and sg.brand_name like CONCAT('%',#{reqVO.brandName}, '%')
        </if>
        <if test="reqVO.materialsCode != null and reqVO.materialsCode != ''">
            and sg.materials_code like CONCAT('%',#{reqVO.materialsCode}, '%')
        </if>
    </select>
    <select id="exportContractMater" resultType="com.ly.yph.api.customization.dto.VoyahContractMaterExcelDto">
        select sg.goods_sku,
        smr.mara_matnr,
        sg.goods_name,
        sg.brand_name,
        sg.supplier_code,
        sg.materials_code,
        sg.spec_goods_ware_qd,
        sgd.goods_moq,
        ygps.goods_pact_naked_price,
        ygps.goods_pact_price,
        sg.tax_rate,
        sc.contract_code,sc.contract_name,sc.id,
        ssc.contract_start_date,
        ssc.contract_end_date,
        ss.supplier_full_name as supplier_name,sg.sale_unit
        from shop_goods_contract sgc
        left join system_contract sc on sgc.contract_id = sc.id
        left join supplier_contract_real ssc on ssc.contract_id = sc.id
        left join shop_supplier ss on ss.supplier_id = ssc.supplier_id
        left join shop_goods sg on sgc.goods_id = sg.goods_id
        left join shop_goods_detail sgd on sgd.goods_code = sg.goods_code
        left join yph_goods_price_strategy ygps
        on ygps.goods_sku = sg.goods_sku and ygps.supplier_code = ss.supplier_code
        left join shop_material_relation smr on sg.goods_code = smr.goods_code
        where sg.tenant_id = sgc.tenant_id
        <if test="reqVO.companyCode != null and reqVO.companyCode.equalsIgnoreCase('voyah')">
            and smr.mara_werks = '1226'
        </if>
        and sg.is_enable = 1
        and sgd.is_enable = 1
        and ygps.is_enable = 1
        and smr.company_code = #{reqVO.companyCode}
        and ss.data_source = #{reqVO.dataSource}
        <if test="reqVO.contractCode != null and reqVO.contractCode != ''">
            and sc.contract_code = #{reqVO.contractCode}
        </if>
        <if test="reqVO.goodsSku != null and reqVO.goodsSku != ''">
            and sg.goods_sku = #{reqVO.goodsSku}
        </if>
        <if test="reqVO.maraMatnr != null and reqVO.maraMatnr != ''">
            and smr.mara_matnr = #{reqVO.maraMatnr}
        </if>
        <if test="reqVO.supplierName != null and reqVO.supplierName != ''">
            and ss.supplier_full_name = #{reqVO.supplierName}
        </if>
        <if test="reqVO.goodsName != null and reqVO.goodsName != ''">
            and sg.goods_name like CONCAT('%',#{reqVO.goodsName}, '%')
        </if>
        <if test="reqVO.brandName != null and reqVO.brandName != ''">
            and sg.brand_name like CONCAT('%',#{reqVO.brandName}, '%')
        </if>
        <if test="reqVO.materialsCode != null and reqVO.materialsCode != ''">
            and sg.materials_code like CONCAT('%',#{reqVO.materialsCode}, '%')
        </if>

    </select>
    <select id="queryVoBySupplierIdContract" resultType="com.ly.yph.api.supplier.vo.ShopSupplierContractVo">
        select *
        from system_contract c
        left join supplier_contract_real cr on c.id = cr.contract_id
        where cr.supplier_id = #{supplierId}
          and c.sign_company_id = #{signCompanyId}
          and c.contract_code = #{contractCode}
          and cr.is_start = 1
          and c.is_enable = 1
    </select>
    <select id="queryPage" resultType="com.ly.yph.api.supplier.vo.ShopSupplierContractVo">
        SELECT  c.id as contractId,
                c.sign_company_id,
                c.sign_company,
                c.sign_company_code,
                c.contract_name,
                c.contract_code,
                c.contract_limit,
                c.payment_conditions,
                c.cash_ratio,
                c.delivery_date,
                c.contract_renewal_id,
                cr.*,
                ss.supplier_code,
                ss.supplier_full_name,
                CASE
                    WHEN cr.sign_status = 0 THEN 3  -- 状态0固定返回3
                    WHEN cr.sign_status = 1 THEN
                        COALESCE(
                            CASE
                            WHEN r.contract_approval_state = 0 THEN 1
                            WHEN DATEDIFF(cr.contract_end_date, CURDATE()) BETWEEN 0 AND 45 THEN 0
                            WHEN r.contract_approval_state = 1 THEN 2
                            WHEN r.contract_approval_state = 2 THEN 0 END,
                            0
                        )
                    WHEN cr.sign_status = 2 THEN IF(r.contract_approval_state = 1, 2, NULL)
                END AS contractRenewalState,
                O.organization_type,
                max(r.id)
        FROM system_contract c
        LEFT join supplier_contract_real cr on c.id = cr.contract_id and cr.is_enable = 1
        LEFT join shop_supplier ss on ss.supplier_id = cr.supplier_id and ss.is_enable = 1
        INNER JOIN system_organization o on o.id = c.sign_company_id
        LEFT JOIN system_contract_renewal r on r.old_contract_id = c.id
        WHERE  c.is_enable = 1
        <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
            and ss.supplier_code = #{queryDto.supplierCode}
        </if>
        <if test="queryDto.supplierId != null">
            and ss.supplier_id = #{queryDto.supplierId}
        </if>
        <if test="queryDto.signCompanyId != null">
            and c.sign_company_id = #{queryDto.signCompanyId}
        </if>
        <if test="queryDto.contractCode != null and queryDto.contractCode != ''">
            and c.contract_code =  #{queryDto.contractCode}
        </if>
        <if test="queryDto.contractName != null and queryDto.contractName != ''">
            and c.contract_name like CONCAT('%', #{queryDto.contractName}, '%')
        </if>
        <if test="queryDto.signStatus != null">
            and cr.sign_status = #{queryDto.signStatus}
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 0">
            and cr.sign_status = 1 AND (r.contract_approval_state is null or r.contract_approval_state = 2)
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 1">
            and cr.sign_status = 1 AND r.contract_approval_state = 0
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 2">
            and cr.sign_status IN(1,2) AND r.contract_approval_state = 1
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 3">
            and cr.sign_status = 0
        </if>
        <if test="queryDto.contractStartDate != null and queryDto.contractStartDate != '' and queryDto.contractEndDate and queryDto.contractEndDate != '' ">
            AND cr.contract_end_date BETWEEN #{queryDto.contractStartDate}
            AND #{queryDto.contractEndDate}
        </if>
        group by c.id
    </select>
    <select id="querySystemContractExportList"
            resultType="com.ly.yph.api.supplier.vo.ShopSupplierContractExcelVo">
        SELECT
        ss.supplier_full_name,
        c.sign_company,
        c.contract_code,
        c.contract_name,
        cr.contract_start_date,
        cr.contract_end_date,
        CASE cr.sign_status
        WHEN 0 THEN
        '未到期'
        WHEN 1 THEN
        '即将到期'
        WHEN 2 THEN
        '已到期'
        ELSE ""
        END as signStatusName,
        CASE cr.is_start
        WHEN 0 THEN
        '未启用'
        WHEN 1 THEN
        '已启用'
        ELSE ''
        END as isStartName,
        CASE
        WHEN cr.sign_status = 0 THEN '未到续签时期'  -- 状态0固定返回3
        WHEN cr.sign_status = 1 THEN
        COALESCE(
        CASE
        WHEN r.contract_approval_state = 0 THEN '续签中'
        WHEN r.contract_approval_state = 1 THEN '已续签'
        WHEN r.contract_approval_state = 2 THEN '待续签' END,
        '待续签'
        )
        WHEN cr.sign_status = 2 THEN IF(r.contract_approval_state = 1, '已续签', '')
        END AS contractRenewalStateName
        FROM system_contract c
        LEFT join supplier_contract_real cr on c.id = cr.contract_id and cr.is_enable = 1
        LEFT join shop_supplier ss on ss.supplier_id = cr.supplier_id and ss.is_enable = 1
        INNER JOIN system_organization o on o.id = c.sign_company_id
        LEFT JOIN system_contract_renewal r on r.old_contract_id = c.id
        WHERE  c.is_enable = 1
        <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
            and ss.supplier_code = #{queryDto.supplierCode}
        </if>
        <if test="queryDto.signCompanyId != null">
            and c.sign_company_id = #{queryDto.signCompanyId}
        </if>
        <if test="queryDto.supplierId != null">
            and ss.supplier_id = #{queryDto.supplierId}
        </if>
        <if test="queryDto.contractName != null and queryDto.contractName != ''">
            and c.contract_name like CONCAT('%', #{queryDto.contractName}, '%')
        </if>
        <if test="queryDto.signStatus != null">
            and cr.sign_status = #{queryDto.signStatus}
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 0">
            and cr.sign_status = 1 AND (r.contract_approval_state is null or r.contract_approval_state = 2)
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 1">
            and cr.sign_status = 1 AND r.contract_approval_state = 0
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 2">
            and cr.sign_status IN(1,2) AND r.contract_approval_state = 1
        </if>
        <if test="queryDto.contractRenewalState != null and queryDto.contractRenewalState == 3">
            and cr.sign_status = 0
        </if>
        <if test="queryDto.contractStartDate != null and queryDto.contractStartDate != '' and queryDto.contractEndDate and queryDto.contractEndDate != '' ">
            AND cr.contract_end_date BETWEEN #{queryDto.contractStartDate}
            AND #{queryDto.contractEndDate}
        </if>
    </select>
    <select id="queryBySignCompanyIdContractCodeSupplierId"
            resultType="com.ly.yph.api.supplier.entity.SystemContractEntity">
        select sc.*
        from system_contract sc
                 left join supplier_contract_real scr on scr.contract_id = sc.id
        where sc.contract_code = #{contractCode}
          and sc.sign_company_id = #{signCompanyId}
          and scr.supplier_id = #{supplierId}
    </select>
    <select id="queryStartListBySignCompanyIdContractCode"
            resultType="com.ly.yph.api.supplier.vo.SupplierContractRealVo">
        select scr.id,
               scr.supplier_id,
               sc.id as contractId,
               sc.contract_code,
               scr.contract_scanned_url,
               scr.contract_start_date,
               scr.contract_end_date,
               scr.sign_status,
               scr.is_start,
               sc.sign_company_id
        from system_contract sc
                 left join supplier_contract_real scr on scr.contract_id = sc.id
        where sc.contract_code = #{contractCode}
          and sc.sign_company_id = #{signCompanyId}
          and scr.is_start = 1
          and scr.contract_start_date &lt; now()
          and scr.contract_end_date &gt; now()
    </select>
    <select id="queryBySignCompanyCodeContractCodeSupplierId"
            resultType="com.ly.yph.api.supplier.entity.SystemContractEntity">
        select sc.*
        from system_contract sc
                 left join supplier_contract_real scr on scr.contract_id = sc.id
        where sc.contract_code = #{contractCode}
          and sc.sign_company_code = #{signCompanyCode}
          and scr.supplier_id = #{supplierId}
    </select>
</mapper>
