package com.ly.yph.api.settlement.supplier.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.settlement.supplier.dto.*;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBillDetail;
import com.ly.yph.api.settlement.supplier.mapper.SupplierInvoiceBillDetailMapper;
import com.ly.yph.core.base.exception.types.ParameterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupplierInvoiceBillDetailService extends ServiceImpl<SupplierInvoiceBillDetailMapper, SupplierInvoiceBillDetail> {

    public List<SupplierInvoiceDetailDto> getSupplierInvoiceDetailListForExcel(Long billId, List<SupplierInvoiceDetailExcelDto> supplierInvoiceDetailExcelDtoList) {
        if (CollectionUtil.isEmpty(supplierInvoiceDetailExcelDtoList)) {
            throw new ParameterException("导入数据为空！");
        }

        List<String> orderNumberList = supplierInvoiceDetailExcelDtoList.stream().map(SupplierInvoiceDetailExcelDto::getOrderNumber).distinct().collect(Collectors.toList());

        List<SupplierInvoiceDetailDto> supplierInvoiceDetailDtoList = new ArrayList<>();

        Lists.partition(orderNumberList, 1000).forEach(subList -> {
            supplierInvoiceDetailDtoList.addAll(getBaseMapper().getSupplierSettleInvoiceDetailDto(subList, billId, null));
        });

        if (CollectionUtil.isEmpty(supplierInvoiceDetailDtoList)) {
            throw new ParameterException("未查询到账单数据！");
        }


        Map<OrderSkuInvoiceNumDto, List<SupplierInvoiceDetailDto>> supplierInvoiceDetailMap = supplierInvoiceDetailDtoList.stream().collect(Collectors.groupingBy(
                dto -> new OrderSkuInvoiceNumDto(dto.getOrderNumber(), dto.getGoodsSku(), dto.getInvoiceNum())
        ));


        List<SupplierInvoiceDetailDto> validDetailList = new ArrayList<>();

        //校验要开票的数据
        for (SupplierInvoiceDetailExcelDto supplierInvoiceDetailExcelDto : supplierInvoiceDetailExcelDtoList) {
            if (StrUtil.isBlank(supplierInvoiceDetailExcelDto.getOrderNumber()) ||
                    StrUtil.isBlank(supplierInvoiceDetailExcelDto.getGoodsSku()) ||
                    supplierInvoiceDetailExcelDto.getCheckedNum() == null) {
                throw new ParameterException("存在为空数据！");
            }

            final OrderSkuInvoiceNumDto tmpKey = new OrderSkuInvoiceNumDto();
            tmpKey.setOrderNumber(supplierInvoiceDetailExcelDto.getOrderNumber());
            tmpKey.setGoodsSku(supplierInvoiceDetailExcelDto.getGoodsSku());
            tmpKey.setCheckedNum(supplierInvoiceDetailExcelDto.getCheckedNum());

            List<SupplierInvoiceDetailDto> billDetailList = supplierInvoiceDetailMap.get(tmpKey);

            if (CollectionUtil.isEmpty(billDetailList)) {
                throw new ParameterException("未查询到订单号：[{}],sku:[{}],出账数量:[{}]的账单明细数据！",
                        supplierInvoiceDetailExcelDto.getOrderNumber(),
                        supplierInvoiceDetailExcelDto.getGoodsSku(),
                        supplierInvoiceDetailExcelDto.getCheckedNum());
            }

            // 进行明细匹配
            List<SupplierInvoiceDetailDto> validDataList = billDetailList.stream().filter(e -> e.getMatchState() == 0).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(validDataList)) {
                throw new ParameterException("未查询到订单号：[{}],sku:[{}],出账数量:[{}]的可开票数据！",
                        supplierInvoiceDetailExcelDto.getOrderNumber(),
                        supplierInvoiceDetailExcelDto.getGoodsSku(),
                        supplierInvoiceDetailExcelDto.getCheckedNum());
            }

            validDetailList.add(validDataList.get(0).setMatchState(1));

        }

        return validDetailList;
    }

    @Transactional
    public void invoicePassForUpdate(List<Long> billDetailList, List<Long> lifeCycleList) {
        Lists.partition(
                billDetailList, 1000
        ).forEach(subList -> getBaseMapper().updateBillDetailForInvoicePass(subList));

        Lists.partition(
                lifeCycleList, 1000
        ).forEach(subList -> getBaseMapper().updateBillLifecycleForInvoicePass(subList));
    }


}
