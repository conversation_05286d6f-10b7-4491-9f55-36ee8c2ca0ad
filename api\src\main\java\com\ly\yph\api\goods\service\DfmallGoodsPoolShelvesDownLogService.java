package com.ly.yph.api.goods.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolShelvesDownQueryDto;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolEntity;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolShelvesDownEntity;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolShelvesDownLogEntity;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.mapper.DfmallGoodsPoolShelvesDownLogMapper;
import com.ly.yph.api.goods.vo.DfmallGoodsPoolShelvesDownLogVo;
import com.ly.yph.api.product.ext.common.dto.response.RemotePriceInfoResp;
import com.ly.yph.api.product.ext.common.manage.BatchPriceGetter;
import com.ly.yph.api.product.ext.common.manage.RemoteInfoManage;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.tenant.core.aop.TenantIgnore;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 商品池商品下架(DfmallGoodsPoolShelvesDown)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-21 10:18:39
 */
@Slf4j
@Service
public class DfmallGoodsPoolShelvesDownLogService extends ServiceImpl<DfmallGoodsPoolShelvesDownLogMapper, DfmallGoodsPoolShelvesDownLogEntity> {

    @Resource
    private DfmallGoodsPoolShelvesDownLogMapper dfmallGoodsPoolShelvesDownLogMapper;

    @Resource
    private DfmallGoodsPoolService dfmallGoodsPoolService;

    @Lazy
    @Resource
    private ShopGoodsService shopGoodsService;

    @Resource
    private RemoteInfoManage remoteInfoManage;

    @Resource
    private ThreadPoolExecutor commonIoExecutors;

    @Resource
    private BatchPriceGetter priceGetter;

    @Value("${shelves-down-log.clean-day:30}")
    private Integer shelvesDownLogCleanDay;

    /**
     * 分页查询商品下架记录
     *
     * @param goodsPoolShelvesDownQueryDto
     * @return
     */
    public PageResp<DfmallGoodsPoolShelvesDownLogVo> queryPage(PageReq pageReq, DfmallGoodsPoolShelvesDownQueryDto goodsPoolShelvesDownQueryDto) {
        IPage<DfmallGoodsPoolShelvesDownLogVo> iPage = dfmallGoodsPoolShelvesDownLogMapper.queryPage(DataAdapter.adapterPageReq(pageReq), goodsPoolShelvesDownQueryDto);
        return DataAdapter.adapterPage(iPage, DfmallGoodsPoolShelvesDownLogVo.class);
    }

    /**
     * 单个异步记录商品上下架记录
     *
     * @param goodsPoolId 商品池id
     * @param goodsId     商品id
     * @param optType     0:下架 1:上架
     * @param downReason  下架原因
     */
    public void saveShelvesDownLog(Long goodsPoolId, Long goodsId, int optType, String downReason) {
        ShopGoods goods = shopGoodsService.getById(goodsId);
        if (goods == null) {
            return;
        }
        batchSaveShelvesDownLog(goodsPoolId, goods, optType, downReason);
    }

    /**
     * 批量异步记录商品上下架记录
     *
     * @param goodsPoolId 商品池id
     * @param goods       商品
     * @param optType     0:下架 1:上架
     * @param downReason  下架原因
     */
    public void batchSaveShelvesDownLog(Long goodsPoolId, ShopGoods goods, Integer optType, String downReason) {
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        LoginUser user = LocalUserHolder.get();
        commonIoExecutors.execute(() -> TenantUtils.execute(tenantId, user, () -> {
            try {
                DfmallGoodsPoolEntity dfmallGoodsPoolEntity = getDfmallGoodsPool(goodsPoolId);
//                RemotePriceInfoResp salePrice = priceGetter.getSalePrice(goods, false);
                RemotePriceInfoResp salePrice = null;

                List<DfmallGoodsPoolShelvesDownLogEntity> dfmallGoodsPoolShelvesDownLogEntityList = new ArrayList<>();
                dfmallGoodsPoolShelvesDownLogEntityList.add(createShelvesDownLogEneity(goods, dfmallGoodsPoolEntity, salePrice, downReason, tenantId, optType));
                dfmallGoodsPoolShelvesDownLogMapper.batchSaveDfmallGoodsPoolShelvesDownLog(dfmallGoodsPoolShelvesDownLogEntityList);
            } catch (Exception e) {
                log.info("上下架日志记录差商品价格失败：{}", e.getMessage());
            }
        }));
    }

    /**
     * 异步记录商品上下架记录
     *
     * @param dfmallGoodsPoolShelvesDownList
     * @param shopGoodsMap
     * @param optType                        0:下架 1:上架
     */
    public void batchSaveShelvesDownLog(List<DfmallGoodsPoolShelvesDownEntity> dfmallGoodsPoolShelvesDownList, Map<Long, ShopGoods> shopGoodsMap, Integer optType) {
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        LoginUser user = LocalUserHolder.get();
        commonIoExecutors.execute(() -> {
            TenantUtils.execute(tenantId, user, () -> {
                try {

                    List<Long> goodsPoolIdList = dfmallGoodsPoolShelvesDownList.stream().map(DfmallGoodsPoolShelvesDownEntity::getGoodsPoolId).filter(e -> e != null && e != 0L).distinct().collect(Collectors.toList());
                    Map<Long, DfmallGoodsPoolEntity> dfmallGoodsPoolEntityMap = getDfmallGoodsPools(goodsPoolIdList);

                    Set<String> goodsCodeSet = shopGoodsMap.values().stream().map(ShopGoods::getGoodsCode).collect(Collectors.toSet());
                    // todo：暂时关闭
                    Map<String, RemotePriceInfoResp> remotePriceInfoMap = priceGetter.batchGetSalePriceZero(goodsCodeSet);

                    List<DfmallGoodsPoolShelvesDownLogEntity> dfmallGoodsPoolShelvesDownLogEntityList = new ArrayList<>();
                    for (DfmallGoodsPoolShelvesDownEntity dfmallGoodsPoolShelvesDownEntity : dfmallGoodsPoolShelvesDownList) {
                        ShopGoods shopGoods = shopGoodsMap.get(dfmallGoodsPoolShelvesDownEntity.getGoodsId());
                        DfmallGoodsPoolEntity dfmallGoodsPoolEntity = dfmallGoodsPoolEntityMap.get(dfmallGoodsPoolShelvesDownEntity.getGoodsPoolId());
                        RemotePriceInfoResp remotePriceInfoResp = remotePriceInfoMap.get(shopGoods.getGoodsCode());
                        dfmallGoodsPoolShelvesDownLogEntityList.add(createShelvesDownLogEneity(shopGoods, dfmallGoodsPoolEntity, remotePriceInfoResp, dfmallGoodsPoolShelvesDownEntity.getDownReason(), tenantId, optType));
                    }
                    dfmallGoodsPoolShelvesDownLogMapper.batchSaveDfmallGoodsPoolShelvesDownLog(dfmallGoodsPoolShelvesDownLogEntityList);

                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("上下架日志记录差商品价格失败：{}", e.getMessage());
                }
            });
        });
    }

    private DfmallGoodsPoolEntity getDfmallGoodsPool(Long goodsPoolId) {
        if (goodsPoolId != null) {
            return dfmallGoodsPoolService.getById(goodsPoolId);
        }
        return null;
    }

    private Map<Long, DfmallGoodsPoolEntity> getDfmallGoodsPools(List<Long> goodsPoolIdList) {
        Map<Long, DfmallGoodsPoolEntity> map = new HashMap();
        if (CollectionUtil.isNotEmpty(goodsPoolIdList)) {
            List<DfmallGoodsPoolEntity> list = dfmallGoodsPoolService.listByIds(goodsPoolIdList);
            if (CollectionUtil.isNotEmpty(list)) {
                map = list.stream().collect(Collectors.toMap(DfmallGoodsPoolEntity::getId, Function.identity(), (key1, key2) -> key2));
            }
        }
        return map;
    }

    private DfmallGoodsPoolShelvesDownLogEntity createShelvesDownLogEneity(ShopGoods shopGoods, DfmallGoodsPoolEntity dfmallGoodsPoolEntity, RemotePriceInfoResp remotePriceInfoResp, String downReason, Long tenantId, int optType) {
        DfmallGoodsPoolShelvesDownLogEntity dfmallGoodsPoolShelvesDownLogEntity = new DfmallGoodsPoolShelvesDownLogEntity();
        dfmallGoodsPoolShelvesDownLogEntity.setGoodsId(shopGoods.getGoodsId());
        dfmallGoodsPoolShelvesDownLogEntity.setGoodsCode(shopGoods.getGoodsCode());
        dfmallGoodsPoolShelvesDownLogEntity.setGoodsName(shopGoods.getGoodsName());
        dfmallGoodsPoolShelvesDownLogEntity.setGoodsSku(shopGoods.getGoodsSku());
        dfmallGoodsPoolShelvesDownLogEntity.setSupplierCode(shopGoods.getSupplierCode());
        dfmallGoodsPoolShelvesDownLogEntity.setDownReason(downReason);
        dfmallGoodsPoolShelvesDownLogEntity.setOptType(optType);
        dfmallGoodsPoolShelvesDownLogEntity.setIsEnable(1);
        dfmallGoodsPoolShelvesDownLogEntity.setTenantId(tenantId);
        dfmallGoodsPoolShelvesDownLogEntity.setModifier(LocalUserHolder.get() == null ? "sys" : LocalUserHolder.get().getUsername());
        dfmallGoodsPoolShelvesDownLogEntity.setCreator(LocalUserHolder.get() == null ? "sys" : LocalUserHolder.get().getUsername());
        dfmallGoodsPoolShelvesDownLogEntity.setGoodsPoolId(dfmallGoodsPoolEntity == null ? 0L : dfmallGoodsPoolEntity.getId());
        dfmallGoodsPoolShelvesDownLogEntity.setCompanyCode(dfmallGoodsPoolEntity == null ? "" : dfmallGoodsPoolEntity.getCompanyCode());
        if (remotePriceInfoResp != null) {
            dfmallGoodsPoolShelvesDownLogEntity.setGoodsPactNakedPrice(remotePriceInfoResp.getGoodsPactNakedPrice());
            dfmallGoodsPoolShelvesDownLogEntity.setGoodsPactPrice(remotePriceInfoResp.getGoodsPactPrice());
            dfmallGoodsPoolShelvesDownLogEntity.setGoodsOriginalPrice(remotePriceInfoResp.getGoodsOriginalPrice());
            dfmallGoodsPoolShelvesDownLogEntity.setGoodsOriginalNakedPrice(remotePriceInfoResp.getGoodsOriginalNakedPrice());
        }
        log.info("saveDfmallGoodsPoolShelvesDownLog：{}", dfmallGoodsPoolShelvesDownLogEntity);
        return dfmallGoodsPoolShelvesDownLogEntity;
    }


    /**
     * 每天凌晨两点23分26秒执行删除日志任务
     */
    @Scheduled(cron = "26 23 2 * * ?")
    public void scheduldShelvesDownLogClean() {
        log.info("shelvesDownLogClean scheduled is run ...");
        deleteByTime();
    }

    /**
     * 删除指定时间前的上下架记录
     *
     * @return
     */
    @DistributedLock(value = "shelvesDownLogClean", leaseTime = 3600, waitLock = false, throwMessage = "shelvesDownLogClean is runing.......")
    @TenantIgnore
    public void deleteByTime() {
        Calendar calendar = Calendar.getInstance();
        TenantUtils.executeIgnore(() -> {
            calendar.add(Calendar.DAY_OF_MONTH, -shelvesDownLogCleanDay);
            log.info("shelvesDownLogClean is begin ... {}", DateUtils.format(calendar.getTime(), DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            dfmallGoodsPoolShelvesDownLogMapper.deleteByTime(calendar.getTime());
            log.info("shelvesDownLogClean is end");
        });
    }
}
