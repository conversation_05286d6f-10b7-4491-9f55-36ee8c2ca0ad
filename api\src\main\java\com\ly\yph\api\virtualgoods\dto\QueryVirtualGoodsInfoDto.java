package com.ly.yph.api.virtualgoods.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年07月14日
 */
@Data
public class QueryVirtualGoodsInfoDto {
    /**
     * 虚拟商品 spu 编码
     */
    String goodsCode;
    /**
     * 虚拟商品 spu 名称
     */
    String goodsName;
    /**
     * 虚拟商品 Sku 编码
     */
    String goodsSkuCode;
    /**
     * 虚拟商品 Sku 名称
     */
    String goodsSkuName;
    /**
     * 商品主图
     */
    String imgUrl;
    /**
     * 商品图文详情描述信息 内容比较大，建议使用 text 字段类型来存储
     */
    String goodsDescribe;
    /**
     * 规格
     */
    String spec;
    /**
     * 面值
     */
    String face;
    /**
     * 末级分类编码
     */
    String lastCategoryCode;
    /**
     * 末级分类名称
     */
    String lastCategoryName;
    /**
     * 商品销售价
     */
    Double sellPrice;
    /**
     * 商品市场价
     */
    Double marketPrice;

    /**
     * 税率
     */
    Integer taxRate;

    /**
     * 税收编码
     */
    String taxCode;

    /**
     * 商品类型 1：直冲 2：卡密
     */
    Integer goodsType;
}
