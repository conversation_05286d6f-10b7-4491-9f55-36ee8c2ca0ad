package com.ly.yph.api.virtualgoods.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("虚拟商品订单扩展表")
@TableName("shop_virtual_purchase_extended")
public class ShopVirtualPurchaseExtended extends BaseEntity {
    @TableId(value = "virtual_purchase_id", type = IdType.INPUT)
    private String virtualPurchaseId;
    private String purchaseNumber;
    private String orderNumber;
    private String rechargeAccount;
    /**
     * 虚拟商品订单类型 1：直冲 2：卡密
     */
    private String virtualType;
    private String virtualLink;
    private String virtualCode;
    private String virtualAccount;
    private String virtualPassword;
    private String virtualOverdue;
    private Long tenantId;
}
