package com.ly.yph.api.settlement.supplier.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("供应商账单明细开票DTO")
public class SupplierInvoiceDetailDto {

    private Long billDetailId;

    private BigDecimal invoiceNum;

    private BigDecimal goodsUnitPriceNaked;

    private BigDecimal goodsUnitPriceTax;

    private BigDecimal goodsTotalPriceNaked;

    private BigDecimal goodsTotalPriceTax;

    private String orderNumber;

    private String goodsSku;

    //匹配状态 0：未匹配 1：已匹配
    private Integer matchState = 0;

    // 账单生命周期id
    private Long lifeCycleId;

}
