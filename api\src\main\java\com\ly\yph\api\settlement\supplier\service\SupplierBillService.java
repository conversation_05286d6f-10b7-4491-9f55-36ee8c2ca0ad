package com.ly.yph.api.settlement.supplier.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.settlement.common.dto.bill.TotalAmountForTaxRate;
import com.ly.yph.api.settlement.common.entity.SettleBillLifeCycle;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.entity.SettleShopBillDetail;
import com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail;
import com.ly.yph.api.settlement.common.enums.*;
import com.ly.yph.api.settlement.common.service.SettleBillLifeCycleService;
import com.ly.yph.api.settlement.common.service.SettleShopBillDetailService;
import com.ly.yph.api.settlement.common.service.SettleShopBillPostageDetailService;
import com.ly.yph.api.settlement.common.service.SettleShopBillService;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailExcelVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailPageReqVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillPostageExcelVo;
import com.ly.yph.api.settlement.supplier.dto.MockMultipartFileDto;
import com.ly.yph.api.settlement.supplier.dto.RemoveToNextMonthDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierReconciliationSumDto;
import com.ly.yph.api.settlement.supplier.factory.SupplierInvoiceFactory;
import com.ly.yph.api.settlement.supplier.factory.SupplierInvoiceStrategy;
import com.ly.yph.api.system.service.FileService;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupplierBillService {

    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;
    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;
    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;
    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private SettleBillLifeCycleService settleBillLifeCycleService;
    @Resource
    private SupplierInvoiceFactory supplierInvoiceFactory;
    @Resource
    private FileService fileService;

    @Transactional
    public String supplierBillReconciliation(Long billId) {
        SettleShopBill settleShopBill = supplierInvoiceBillService.checkSupplierBillAndOperator(billId);

        if (!BillStatusEnum.TO_BE_RECONCILED.getCode().equals(settleShopBill.getBillStatus())) {
            throw new ParameterException("账单已确认过，请勿重复确认！");
        }

        //更新账单明细状态（移动账单和更新本月账单状态，账单生命周期）
        doSupplierBillDetailForReconciliation(settleShopBill);

        // 邮费
        doSupplierPostageDetailForReconciliation(settleShopBill);

        //更新账单状态
        settleShopBillService.updateShopBillMoney(settleShopBill);

        UpdateWrapper<SettleShopBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SettleShopBill::getBillId, settleShopBill.getBillId())
                .set(SettleShopBill::getBillStatus, BillStatusEnum.TO_BE_INVOICED.getCode());
        settleShopBillService.update(updateWrapper);

        //生成excel上传至文件服务器
        supplierExcelToObs(billId);

        return "对账成功！";

    }

    public void supplierExcelToObs(Long billId) {
        SettleShopBill settleShopBill = settleShopBillService.getById(billId);
        SettleShopBillDetailPageReqVo vo = new SettleShopBillDetailPageReqVo();
        vo.setBillId(billId);
        List<SettleShopBillDetailExcelVo> shopBillDetails = settleShopBillDetailService.getBillDetailExecl(vo, PoolTypeEnum.DFSHOP.getCode());
        if (CollectionUtil.isEmpty(shopBillDetails)) {
            throw new ParameterException("账单明细数据为空！");
        }


        List<TotalAmountForTaxRate> totalAmountForTaxRateList = new ArrayList<>();
        Map<Integer, List<SettleShopBillDetailExcelVo>> taxRateMap = shopBillDetails.stream().collect(Collectors.groupingBy(SettleShopBillDetailExcelVo::getTaxRate));

        taxRateMap.forEach((taxRate, detailList) -> {
            TotalAmountForTaxRate totalAmountForTaxRate = new TotalAmountForTaxRate();
            totalAmountForTaxRate.setTaxRate(taxRate);
            totalAmountForTaxRate.setAmountTax(detailList.stream().map(SettleShopBillDetailExcelVo::getTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalAmountForTaxRate.setAmountNaked(detailList.stream().map(SettleShopBillDetailExcelVo::getTotalPriceNaked).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalAmountForTaxRateList.add(totalAmountForTaxRate);
        });

        //获取邮费
        List<SettleShopBillPostageExcelVo> postageExcelVoList = settleShopBillPostageDetailService.getPostageExcel(vo);
        Map<String, Object> map = new HashMap<>();
        map.put("billSn", settleShopBill.getBillSn());
        map.put("customerName", settleShopBill.getCustomerName());
        map.put("createTime", DateUtils.format(settleShopBill.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        map.put("checkYear", settleShopBill.getCheckYear().toString());
        map.put("checkMonth", settleShopBill.getCheckMonth().toString());
        map.put("billStatus", BillStatusEnum.getBillStatusNameByCode(settleShopBill.getBillStatus()));
        map.put("totalTaxAmount", settleShopBill.getTotalTaxAmount().toString());
        map.put("postage", settleShopBill.getPostage().toString());

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        InputStream is = null;
        ExcelWriter excelWriter = null;

        try {
            is = ResourceUtil.getStream("classpath:templates/shop_bill_template.xlsx");

            // 创建ExcelWriter并写入内存
            excelWriter = EasyExcel.write(outputStream)
                    .withTemplate(is)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();

            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "对账单数据").build();
            TotalAmountForTaxRate totalAmountForTaxRate = new TotalAmountForTaxRate();
            totalAmountForTaxRate.setAmountNaked(shopBillDetails.stream().map(SettleShopBillDetailExcelVo::getTotalPriceNaked).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalAmountForTaxRate.setAmountTax(shopBillDetails.stream().map(SettleShopBillDetailExcelVo::getTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalAmountForTaxRate.setIndex("合计");
            totalAmountForTaxRateList.add(totalAmountForTaxRate);
            excelWriter.fill(new FillWrapper("totalAmountForTaxRateList", totalAmountForTaxRateList), writeSheet1);
            excelWriter.fill(map, writeSheet1);

            WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "对账单商品详情").head(SettleShopBillDetailExcelVo.class).build();
            excelWriter.fill(new FillWrapper("data", shopBillDetails), writeSheet2);
            WriteSheet writeSheet4 = EasyExcel.writerSheet(2, "对账单邮费明细").head(SettleShopBillPostageExcelVo.class).build();
            excelWriter.fill(new FillWrapper("data", postageExcelVoList), writeSheet4);
            excelWriter.finish();

            String filename = settleShopBill.getCustomerName() +
                    settleShopBill.getCheckYear() +
                    "年" + settleShopBill.getCheckMonth() +
                    "月账单.xlsx";
            log.info("fileName:{}",filename);

            // 创建MultipartFile模拟对象
            MultipartFile multipartFile = new MockMultipartFileDto(
                    "file",
                    filename,
                    "application/vnd.ms-excel",
                    outputStream.toByteArray()
            );
            log.info("multipartFile:{}",multipartFile.getOriginalFilename());
            String s = fileService.uploadFileWithName(multipartFile, "");
            log.info("供应商账单：{}的excel的路径:{}",settleShopBill.getBillSn(),s);

            UpdateWrapper<SettleShopBill> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SettleShopBill::getBillId,settleShopBill.getBillId())
                    .set(SettleShopBill::getExcelUrl,s);
            settleShopBillService.update(updateWrapper);

        } catch (Exception e) {
            log.error("生成或上传对账单失败:", e);
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("关闭流失败:", e);
                    e.printStackTrace();
                }
            }
        }
    }


    @Transactional
    public void doSupplierBillDetailForReconciliation(SettleShopBill settleShopBill) {
        QueryWrapper<SettleShopBillDetail> billDetailQueryWrapper = new QueryWrapper<>();
        billDetailQueryWrapper.lambda().eq(SettleShopBillDetail::getBillId, settleShopBill.getBillId())
                .select(SettleShopBillDetail::getDetailId,
                        SettleShopBillDetail::getOrderNumber,
                        SettleShopBillDetail::getBillStagingFlag,
                        SettleShopBillDetail::getReconciliationStatus,
                        SettleShopBillDetail::getSettleBillPoolId,
                        SettleShopBillDetail::getBillId,
                        SettleShopBillDetail::getBillSn,
                        SettleShopBillDetail::getGoodsCode,
                        SettleShopBillDetail::getGoodsSku,
                        SettleShopBillDetail::getCheckYear,
                        SettleShopBillDetail::getCheckMonth
                        );
        List<SettleShopBillDetail> detailList = settleShopBillDetailService.list(billDetailQueryWrapper);

        if (CollectionUtil.isEmpty(detailList)) {
            throw new ParameterException("无账单明细需要确认！");
        }

        List<SettleShopBillDetail> detailStagingList = detailList.stream()
                .filter(e -> SupplierBillDetailStagingFlagEnum.HAD_STAGING.getCode().equals(e.getBillStagingFlag()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(detailStagingList)) {
            //进行滚动删除操作
            settleShopBillService.deleteFromBill(settleShopBill, detailStagingList,
                    detailStagingList.stream().map(SettleShopBillDetail::getSettleBillPoolId).distinct().collect(Collectors.toList()),
                    "供应商确认账单，账单明细移月",
                    BillOperateLogTypeEnum.DELETE_BILL_ROLL.getCode());
        }

        List<SettleShopBillDetail> updateReconciliationStatusList = detailList.stream()
                .filter(e -> SupplierBillDetailStagingFlagEnum.NOT_STAGING.getCode().equals(e.getBillStagingFlag()) &&
                        (ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode().equals(e.getReconciliationStatus()) ||
                                ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode().equals(e.getReconciliationStatus()) ||
                                ReconciliationStatusEnum.RECONCILIATION_BILL_REJECT.getCode().equals(e.getReconciliationStatus()))
                ).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(updateReconciliationStatusList)) {
            return;
        }

        List<Long> settlePoolIds = updateReconciliationStatusList.stream().map(SettleShopBillDetail::getSettleBillPoolId).collect(Collectors.toList());
        //更新状态
        List<SettleBillLifeCycle> lifeCycles = settleBillLifeCycleService.getUpdateBillLifeCycleListByIds(settlePoolIds, PoolTypeEnum.DFSHOP.getCode());
        Map<Long, SettleBillLifeCycle> billLifeCycleMap = lifeCycles.stream().collect(Collectors.toMap(SettleBillLifeCycle::getSettleBillPoolId, Function.identity(), (key1, key2) -> key1));

        List<SettleShopBillDetail> updateDetails = new ArrayList<>();
        List<SettleBillLifeCycle> updateLifeCycles = new ArrayList<>();

        LoginUser user = LocalUserHolder.get();
        for (SettleShopBillDetail settleShopBillDetail : updateReconciliationStatusList) {

            SettleShopBillDetail billDetail = new SettleShopBillDetail();
            billDetail.setDetailId(settleShopBillDetail.getDetailId());
            //更新账单明细状态为已确认
            billDetail.setReconciliationConfirmUserId(user.getId());
            billDetail.setReconciliationConfirmUserName(user.getNickname());
            billDetail.setReconciliationConfirmTime(new Date());
            billDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            billDetail.setReconciliationRemark("供应商账单确认");
            billDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
            updateDetails.add(billDetail);
            //更新账单生命周期数据
            SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
            SettleBillLifeCycle updateLifeCycle = new SettleBillLifeCycle();
            updateLifeCycle.setId(settleBillLifeCycle.getId());
            updateLifeCycle.setSupplierReconciliationNum(settleShopBillDetail.getCheckedNum());
            updateLifeCycle.setSupplierReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            updateLifeCycle.setSupplierReconciliationTime(new Date());
            updateLifeCycles.add(updateLifeCycle);
        }
        //更新状态数据
        Lists.partition(updateDetails, 800).forEach(subclassList -> settleShopBillDetailService.getBaseMapper().updateSettleBillDetails(subclassList));
        Lists.partition(updateLifeCycles, 800).forEach(subclassList -> settleBillLifeCycleService.getBaseMapper().updateLifeCycleList(subclassList));
    }


    @Transactional
    public void doSupplierPostageDetailForReconciliation(SettleShopBill settleShopBill) {
        QueryWrapper<SettleShopBillPostageDetail> postageDetailQueryWrapper = new QueryWrapper<>();
        postageDetailQueryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, settleShopBill.getBillId());
        List<SettleShopBillPostageDetail> postageDetailList = settleShopBillPostageDetailService.list(postageDetailQueryWrapper);
        if (CollectionUtil.isEmpty(postageDetailList)) {
            return;
        }

        List<SettleShopBillPostageDetail> NotStagingPostageList = postageDetailList.stream()
                .filter(e -> SupplierBillDetailStagingFlagEnum.NOT_STAGING.getCode().equals(e.getPostageStagingFlag()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(NotStagingPostageList)) {
            //不用移月的
            UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(SettleShopBillPostageDetail::getBillDetailPostageId,
                    NotStagingPostageList.stream().map(SettleShopBillPostageDetail::getBillDetailPostageId)
                            .collect(Collectors.toList()))
                    .set(SettleShopBillPostageDetail::getInvoiceFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            settleShopBillPostageDetailService.update(updateWrapper);
        }

        List<SettleShopBillPostageDetail> HadStagingPostageList = postageDetailList.stream()
                .filter(e -> SupplierBillDetailStagingFlagEnum.HAD_STAGING.getCode().equals(e.getPostageStagingFlag()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(HadStagingPostageList)) {
            return;
        }

        Integer nextMonth = settleShopBillService.getNextMonth(settleShopBill.getCheckMonth());
        Integer nextYear = settleShopBillService.getNextYear(settleShopBill.getCheckYear(), nextMonth);
        String yearAndMonth = nextYear + "-" + nextMonth;

        SettleShopBill nextSettleShopBill = settleShopBillService.getSettleShopBill(settleShopBill.getCustomerCode(),
                settleShopBill.getCustomerName(),
                settleShopBill.getCustomerType(),
                yearAndMonth,
                settleShopBill.getCustomerSourceType(),
                settleShopBill.getAreaType(),
                BillTypeEnum.TO_BUSINESS.getCode(),
                settleShopBill.getIsPlatformReconciliation());


        LoginUser user = LocalUserHolder.get();
        List<SettleShopBillPostageDetail> saveList = new ArrayList<>();
        //移动至下个月
        for (SettleShopBillPostageDetail shopBillPostageDetail : HadStagingPostageList) {
            log.info("shopBillPostageDetail:{}", JSON.toJSON(shopBillPostageDetail));
            SettleShopBillPostageDetail postageNextMonth = DataAdapter.convert(shopBillPostageDetail,SettleShopBillPostageDetail.class);
            postageNextMonth.setBillId(nextSettleShopBill.getBillId());
            postageNextMonth.setBillSn(nextSettleShopBill.getBillSn());
            postageNextMonth.setInvoiceFlag(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode());
            postageNextMonth.setPostageStagingFlag(0);
            postageNextMonth.setCreator(user.getUsername());
            postageNextMonth.setCreateTime(new Date());
            postageNextMonth.setModifier(user.getUsername());
            postageNextMonth.setUpdateTime(new Date());
            postageNextMonth.setBillDetailPostageId(null);
            log.info("postageNextMonth:{}",JSON.toJSON(postageNextMonth));
            saveList.add(postageNextMonth);
        }

        settleShopBillPostageDetailService.removeBatchByIds(
                HadStagingPostageList.stream().map(SettleShopBillPostageDetail::getBillDetailPostageId)
                        .collect(Collectors.toList()));

        settleShopBillPostageDetailService.saveBatch(saveList);

        settleShopBillService.updateShopBillMoney(nextSettleShopBill);
    }

    @Transactional
    public String removeToNextMonth(RemoveToNextMonthDto removeToNextMonthDto) {
        SettleShopBill settleShopBill = supplierInvoiceBillService.checkSupplierBillAndOperator(removeToNextMonthDto.getBillId());
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(removeToNextMonthDto.getBillInvoiceType());
        return supplierInvoiceStrategy.removeToNextMonth(removeToNextMonthDto);
    }

    public void exportBillDetail(HttpServletResponse response, SettleShopBillDetailPageReqVo reqVo) throws IOException {
        SettleShopBill settleShopBill = supplierInvoiceBillService.checkSupplierBillAndOperator(reqVo.getBillId());
        SupplierInvoiceStrategy supplierInvoiceStrategy = supplierInvoiceFactory.getSupplierInvoiceStrategy(reqVo.getBillInvoiceType());
        supplierInvoiceStrategy.exportBillDetail(response, reqVo);
    }

    @Transactional
    public String uploadSupplierCheck(MultipartFile file, String billSn) throws IOException {
        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getBillSn, billSn);
        SettleShopBill settleShopBill = settleShopBillService.getOne(queryWrapper);
        if (settleShopBill == null) {
            throw new ParameterException("未查询到该账单信息！");
        }

        String s = fileService.uploadFileWithName(file, "");
        log.info("账单：{}的pdf验收单路径：{}", billSn, s);

        //更新路径到账单上
        UpdateWrapper<SettleShopBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SettleShopBill::getBillId, settleShopBill.getBillId())
                .set(SettleShopBill::getCheckUrl, s);
        settleShopBillService.update(updateWrapper);

        return "供应商验收单生成成功！";
    }

    public SupplierReconciliationSumDto supplierReconciliationSum(String billId) {
        Map<String, BigDecimal> billMap = settleShopBillService.getBaseMapper().getSupplierReconciliationSum(billId);
        Map<String, BigDecimal> postageMap = settleShopBillService.getBaseMapper().getSupplierReconciliationSumForPostage(billId);

        SupplierReconciliationSumDto dto = new SupplierReconciliationSumDto();
        dto.setAmount(billMap == null ? BigDecimal.ZERO : billMap.get("amount"));
        dto.setAmountTax(billMap == null ? BigDecimal.ZERO : billMap.get("amountTax"));
        dto.setPostage(postageMap == null ? BigDecimal.ZERO : postageMap.get("postage"));

        return dto;
    }
}
