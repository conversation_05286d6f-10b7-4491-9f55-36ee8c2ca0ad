package com.ly.yph.api.settlement.supplier.mapper;

import com.ly.yph.api.settlement.common.entity.SettleBillLifeCycle;
import com.ly.yph.api.settlement.common.entity.SettleShopBillDetail;
import com.ly.yph.api.settlement.supplier.dto.SupplerInvoiceExcelDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDetailDto;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBillDetail;
import com.ly.yph.core.base.database.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SupplierInvoiceBillDetailMapper extends BaseMapperX<SupplierInvoiceBillDetail> {

    List<SupplierInvoiceDetailDto> getSupplierSettleInvoiceDetailDto(@Param("list") List<String> orderNumberList,
                                                                     @Param("billId") Long billId,
                                                                     @Param("detailIdList") List<Long> detailIdList);

    void updateSupplierBillDetailForInvoiceApply(@Param("list") List<SettleShopBillDetail> subList);

    void updateSupplierBillLifeCycleForInvoiceApply(@Param("list") List<SettleBillLifeCycle> subList);

    List<SupplerInvoiceExcelDto> getSupplierSettleList(@Param("supplierInvoiceId") Long supplierInvoiceId);

    List<SupplierInvoiceDelDto> getSupplierInvoiceDelDetail(@Param("supplierInvoiceId") Long supplierInvoiceId);

    void updateBillDetailForSupplierInvoiceDel(@Param("list") List<Long> detailList);

    void updateBillLifecycleForInvoiceDel(@Param("list") List<Long> lifeCycleIdList);

    void updateBillDetailForInvoicePass(@Param("list") List<Long> billDetailList);

    void updateBillLifecycleForInvoicePass(@Param("list") List<Long> lifeCycleList);

}
