server:
  tomcat:
    connection-timeout: 600000
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  application:
    name: api-job
  cloud:
    loadbalancer:
      ribbon:
        enabled: true
    nacos:
      config:
        username: nacos
        password: CAEXlvsHL7EBDlp!
        server-addr: "nacos-cluster-cs.dfmall.svc:8848"
        namespace: c0cac9ce-b853-457c-a461-119aa913f0d5
        file-extension: yaml
        group: DEFAULT_GROUP
        liteflow:
          file-name: liteflow
          namespace: c0cac9ce-b853-457c-a461-119aa913f0d5
          group: DEFAULT_GROUP
          file-extension: xml
      discovery:
        username: nacos
        password: CAEXlvsHL7EBDlp!
        server-addr: "nacos-cluster-cs.dfmall.svc:8848"
        namespace: c0cac9ce-b853-457c-a461-119aa913f0d5
        group: DFMALL_PROD_GROUP