package com.ly.yph.api.system.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ly.yph.api.goods.vo.FileValidationResult;
import com.ly.yph.core.base.CommonCodeGeneral;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.oss.OSSAutoConfig;
import com.ly.yph.core.oss.OSSClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.*;
import java.util.ArrayList;
import java.util.List;

import static io.vertx.core.net.impl.URIDecoder.decodeURIComponent;

/**
 * 文件服务
 *
 * <AUTHOR>
 * @date 2022/06/21
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class FileService {
    private OSSAutoConfig ossConfig;
    private CommonCodeGeneral commonCodeGeneral;

    /**
     * 上传文件
     *
     * @param file 文件
     * @return {@link String}
     * @throws IOException ioexception
     */
    public String uploadFile(MultipartFile file, String path) throws IOException {
        String originalFilename = file.getOriginalFilename();
        String extName = StrUtil.subAfter(originalFilename, ".", true);
        if (StrUtil.isBlank(extName)) {
            extName = "png";
        }

        Assert.isTrue(!file.isEmpty(), () -> new ParameterException("上传文件不能为空"));
        byte[] fileBytes = file.getBytes();
        try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
            return decodeURIComponent(ossClient.putObject(ossConfig.getImageBuketName(), path + commonCodeGeneral.getCode36() + "." + extName, fileBytes));
        }
    }


    public String uploadFileByByte(String fileName, String path, byte[] fileBytes) throws IOException {
        String extName = "png";
        try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
            return decodeURIComponent(ossClient.putObject(ossConfig.getImageBuketName(), path + fileName + commonCodeGeneral.getCode36() + "." + extName, fileBytes));
        }

    }

    public String uploadFileByByte(String fileName, String path, String extName, byte[] fileBytes) throws IOException {
        try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
            return decodeURIComponent(ossClient.putObject(ossConfig.getImageBuketName(), path + fileName + commonCodeGeneral.getCode36() + "." + extName, fileBytes));
        }

    }

    public String uploadFileByByte(String fileName, byte[] fileBytes) throws IOException {
        try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
            return decodeURIComponent(ossClient.putObject(ossConfig.getImageBuketName(), fileName, fileBytes));
        }

    }

    public String uploadFileWithName(MultipartFile file, String path) throws IOException {
        Assert.isFalse(file.isEmpty(), () -> new ParameterException("上传文件不能为空"));
        String originalFilename = file.getOriginalFilename();
        byte[] fileBytes = file.getBytes();
        try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
            return ossClient.putObject(ossConfig.getFileBuketName(), path + originalFilename, fileBytes);
        }
    }

    public List<FileValidationResult> uploadBatchFilesWithNames(MultipartFile[] files, String path) throws IOException {
        if(StrUtil.isBlank(path)) path = "/supUploadImag/";
        List<FileValidationResult> resultPaths = new ArrayList<>();
        try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
            for (MultipartFile file : files) {
                Assert.isFalse(file.isEmpty(), () -> new ParameterException("上传文件不能为空"));
                String originalFilename = file.getOriginalFilename();
                byte[] fileBytes = file.getBytes();
                String url = ossClient.putObject(ossConfig.getFileBuketName(), path + originalFilename, fileBytes);
                FileValidationResult result = new FileValidationResult(originalFilename,true,"上传成功",url);
                resultPaths.add(result);
            }
        }
        return resultPaths;
    }

    /**
     * 转换图片路径
     */
    public String convertUrl(String imageUrl, String unique) {
        Assert.isFalse(imageUrl.isEmpty(), () -> new ParameterException("待转换图片路径不能为空"));
        HttpURLConnection httpUrl = null;
        InputStream is = null;
        ByteArrayOutputStream outStream = null;
        try {
            // 1. 自动将HTTP转换为HTTPS（如果原始URL是HTTP）
            URL originalUrl = new URL(imageUrl);
            String protocol = originalUrl.getProtocol();
            String fixedUrl = imageUrl;

            // 如果原始URL是HTTP，尝试转换为HTTPS
            if ("http".equalsIgnoreCase(protocol)) {
                fixedUrl = imageUrl.replaceFirst("(?i)^http://", "https://");
                log.info("自动转换HTTP为HTTPS: {} → {}", imageUrl, fixedUrl);
            }

            // 1. 设置HTTP连接
            URL url = new URL(fixedUrl);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.setRequestProperty("User-Agent", "Mozilla/5.0");
            httpUrl.setInstanceFollowRedirects(true);

            // 2. 验证响应状态
            int status = httpUrl.getResponseCode();
            if (status != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP error: " + status);
            }

            // 3. 获取文件扩展名（多重保障）
            String contentType = httpUrl.getContentType();
            String extName = getExtensionFromContentType(contentType);

            // 双重保障：如果内容类型无法识别，从URL路径获取扩展名
            if (extName == null || extName.isEmpty()) {
                extName = getExtensionFromUrl(imageUrl);
            }
            // 最终保障：如果仍无法确定，使用默认值
            if (extName == null || extName.isEmpty()) {
                extName = "png";
            }

            // 4. 下载图片数据
            is = httpUrl.getInputStream();
            outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            byte[] imageData = outStream.toByteArray();

            // 5. 生成安全的文件名
            String fileName = unique + commonCodeGeneral.getCode36() + "." + extName;
            String safeFileName = URLEncoder.encode(fileName, "UTF-8");

            // 6. 上传到OSS（使用try-with-resources确保客户端关闭）
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(),
                    ossConfig.getSecretKey(),
                    ossConfig.getEndpoint())) {

                String huaweiUrl = ossClient.putObject(
                        ossConfig.getImageBuketName(),
                        safeFileName,
                        new ByteArrayInputStream(imageData)
                );
                return URLDecoder.decode(huaweiUrl, "UTF-8");
            }

        } catch (Exception e) {
            log.error("图片转换失败 | URL: {} | 原因: {}", imageUrl, e.getMessage(), e);
            throw new ParameterException("图片转换失败: " + e.getMessage());
        } finally {
            // 7. 确保资源关闭
            try {
                if (is != null) is.close();
                if (outStream != null) outStream.close();
                if (httpUrl != null) httpUrl.disconnect();
            } catch (IOException e) {
                log.warn("资源关闭异常: {}", e.getMessage());
            }
        }
    }

    // 从URL路径提取扩展名
    private String getExtensionFromUrl(String imageUrl) {
        try {
            String path = new URL(imageUrl).getPath();
            int dotIndex = path.lastIndexOf('.');
            if (dotIndex > 0) {
                return path.substring(dotIndex + 1).toLowerCase();
            }
        } catch (MalformedURLException e) {
            log.warn("URL解析失败: {}", imageUrl);
        }
        return null;
    }

    // 增强版扩展名识别
    private String getExtensionFromContentType(String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            return null;
        }

        String type = contentType.toLowerCase();
        if (type.contains("jpeg") || type.contains("jpg")) {
            return "jpg";
        } else if (type.contains("png")) {
            return "png";
        } else if (type.contains("gif")) {
            return "gif";
        } else if (type.contains("webp")) {
            return "webp";
        } else if (type.contains("bmp")) {
            return "bmp";
        }

        // 尝试从Content-Type中提取
        if (type.contains("/")) {
            String[] parts = type.split("/");
            if (parts.length > 1) {
                return parts[1].split(";")[0]; // 处理类似 "image/svg+xml"
            }
        }
        return null;
    }
}
