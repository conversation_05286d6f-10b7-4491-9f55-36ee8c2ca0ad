<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.goods.mapper.ShopMaterialRelationMapper">
    <resultMap id="BaseResultMap" type="com.ly.yph.api.goods.entity.ShopMaterialRelationEntity">
        <id column="mara_id" jdbcType="BIGINT" property="maraId"/>
        <result column="mara_matnr" jdbcType="VARCHAR" property="maraMatnr"/>
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode"/>
        <result column="mara_maktx" jdbcType="VARCHAR" property="maraMaktx"/>
        <result column="mara_meins" jdbcType="VARCHAR" property="maraMeins"/>
        <result column="mara_zmein" jdbcType="VARCHAR" property="maraZmein"/>
        <result column="mara_normt" jdbcType="VARCHAR" property="maraNormt"/>
        <result column="mara_matkl" jdbcType="VARCHAR" property="maraMatkl"/>
        <result column="mara_wgbez" jdbcType="VARCHAR" property="maraWgbez"/>
        <result column="mara_mtart" jdbcType="VARCHAR" property="maraMtart"/>
        <result column="mara_unit" jdbcType="VARCHAR" property="maraUnit"/>
        <result column="mara_werks" jdbcType="VARCHAR" property="maraWerks"/>
        <result column="source_sys_id" jdbcType="VARCHAR" property="sourceSysId"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="is_del" jdbcType="INTEGER" property="isDel"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="queryPage" resultMap="BaseResultMap">
        select *
        from shop_material_relation
        <where>
            is_del = '0'
              and company_code = #{queryDto.companyCode}
            <if test="queryDto.maraMatnr != null and queryDto.maraMatnr != ''">
                and mara_matnr = #{queryDto.maraMatnr}
            </if>
            <if test="queryDto.goodsCode != null and queryDto.goodsCode != ''">
                and goods_code = #{queryDto.goodsCode}
            </if>
        </where>
    </select>

    <select id="selectByGoodsCode" resultType="java.lang.String">
        SELECT DISTINCT `goods_code`
        FROM `shop_material_relation`
        WHERE `company_code` = #{entityOrganizationCode}
          AND `is_del` = 0
          AND `goods_code` IN
        <foreach collection="goodsCodes" item="goodsCode" separator="," close=")" open="(">
            #{goodsCode}
        </foreach>
    </select>
    <select id="queryNoCreateMaterial" resultType="com.ly.yph.api.goods.dto.QuerySeekMaterialDto">
        select ssg.mara_matnr,
               ssqs.goods_sku,
               ssqs.supplier_code,
               ssqs.id
        from shop_seek_compare_price sscp
                 left join shop_seek_goods ssg on ssg.seek_price_number = sscp.seek_price_number
                 left join shop_seek_quoted_price ssqp on ssqp.seek_price_id = sscp.id and ssqp.seek_goods_id = ssg.id
                 left join shop_seek_quoted_sku ssqs on ssqs.quoted_price_id = ssqp.id and ssqs.seek_goods_id = ssg.id
        where sscp.company_code = 'VOYAH'
          and ssg.mara_matnr != ''
 and ssqp.bidden_success = 1
 and ssqs.goods_sku is not null and ssqs.create_material = 0;
    </select>
</mapper>