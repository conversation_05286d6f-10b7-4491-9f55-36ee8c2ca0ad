package com.ly.yph.api.product.ext.ofs.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopGoodsDetail;
import com.ly.yph.api.goods.entity.ShopGoodsPrice;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.product.ext.common.BaseDataStore;
import com.ly.yph.api.product.ext.common.enums.MessageStatusEnum;
import com.ly.yph.api.product.ext.ofs.config.OfsConfig;
import com.ly.yph.api.product.ext.ofs.entity.BackupOfsGoodsEntity;
import com.ly.yph.api.product.ext.ofs.service.BackupOfsGoodsService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierClass;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.oss.OSSAutoConfig;
import com.ly.yph.core.oss.OSSClient;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 写入商城shop_goods 数据库
 *
 * <AUTHOR>
 * @date 2022/03/15
 */
@Service("OfsGoodsStore")
@Slf4j
public class OfsGoodsStore extends BaseDataStore<Integer,BackupOfsGoodsEntity> {
    @Getter
    protected String processName = "OFS入标准库";
    @Resource
    private BackupOfsGoodsService backSrv;
    @Resource
    private ShopGoodsService goodSrv;
    @Resource
    private OSSAutoConfig ossConfig;
    @Resource
    private OfsConfig config;
    @Resource
    private YphStandardClassService stClassSrv;
    @Resource
    private ShopBrandService brandSrv;
    @Resource
    private ShopGoodsDetailService detailSrv;
    @Resource
    private ShopGoodsPriceService priceSrv;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;
    @Resource
    private ShopSupplierClassService supClassSrv;

    @Override
    public List<Integer> supplier(final int count) {
        return TenantUtils.executeIgnore(() -> backSrv.selectAllForStoreProcess(count));
    }

    @Override
    @DistributedLock(value = "ofs_store_process", key = "#id", waitLock = false)
    public void doStoreItem(final Integer id) {
        Assert.notNull(id);
        var uEnt = TenantUtils.executeIgnore(() -> backSrv.getById(id)) ;
        if (uEnt == null || uEnt.getSynchronize() != 0) {
            return;
        }

        TenantUtils.execute(uEnt.getTenantId(),()->{
        try {
            ShopGoods sEnt = new ShopGoods();
            //到shop_goods查询
            ShopGoods eEnt = goodSrv.getGoodsForStore(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            } else {
                // 首次转换，用ai分析标准分类和品牌
                setCategory(uEnt, sEnt);
                setBrand(uEnt, sEnt);
            }

            
            //设置商品详情
            setIntroduce(uEnt, sEnt, eEnt);
            sEnt.setGoodsName(uEnt.getName());
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSku()));
            sEnt.setBackupGoodId(uEnt.getId());
            sEnt.setSupplierCode(config.getCode());
            sEnt.setSupplierName(config.getName());
            ShopSupplier supplierEnt = shopSupplierService.selectByCode(config.getCode());
            sEnt.setOrganizationId(supplierEnt.getOrganizationId());
            sEnt.setSupplierType(supplierEnt.getSupplierType());
            sEnt.setGoodsDesc(uEnt.getName() + " 销售单位：" + uEnt.getUnit());
            sEnt.setGoodsSubtitle(uEnt.getName() + " 销售单位：" + uEnt.getUnit());
            sEnt.setMaterialsCode(uEnt.getModel());
            sEnt.setManufacturerMaterialNo(uEnt.getModel());
            sEnt.setSpecGoodsWareQd(uEnt.getWare());
            sEnt.setTaxCode(uEnt.getGoodTaxCode());
            sEnt.setSaleUnit(uEnt.getUnit());
            sEnt.setProductionPlace(uEnt.getProductArea());
            sEnt.setTaxRate(uEnt.getTaxRatePercentage());
            // 这里不知道多少天
            sEnt.setDeliveryTime(7);
            sEnt.setGoodsKeywords("");
            // 供应商档案配置不审批或者曾经已经审批通过的,无需审批,其他都需要审批
            sEnt.setAuditState(supplierEnt.getIsAudited() == 0 || (sEnt.getAuditState() != null && sEnt.getAuditState() == 1) ? 1 : 0);
            sEnt.setGoodsExplain("");
            sEnt.setGoodsFeatures("");
            sEnt.setHeedEvent("");
            sEnt.setIsSpecial(0);
            // 先让商品不可见
            sEnt.setIsEnable("1");
            sEnt.setSpecialEvent("");
            sEnt.setGoodsMobileBoydUrl("");
            uEnt.setUpdateTime(new Date());
            sEnt.setUpdateTime(new Date());
            // 填充详情表
            saveDetailInfo(uEnt);
            //填充价格表
            savePriceInfo(uEnt);
            if (eEnt != null) {
                goodSrv.updateGoods(sEnt);
                //将上下架表中删除状态改回
                dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolId(null, sEnt.getGoodsId());
//                List<ShopGoodsShelves> shopGoodsShelvesList = shopGoodsShelvesService.queryByGoodsCode(sEnt.getGoodsCode());
//                if (!CollectionUtils.isEmpty(shopGoodsShelvesList)) {
//                    shopGoodsShelvesService.updateBatchShelves(shopGoodsShelvesList);
//                }
            } else {
                goodSrv.save(sEnt);
            }
            if(sEnt.getAuditState()==1){
                //填充上下架表
                setShelvesInfo(uEnt);
            }else {
                goodSrv.supplierSubmitGoods(CollectionUtil.toList(sEnt));
            }
            uEnt.setSynchronize((byte) MessageStatusEnum.DONE.getCode());
            backSrv.updateById(uEnt);
        } catch (final Exception e) {
            uEnt.setSynchronize((byte)MessageStatusEnum.ERROR.getCode());
            backSrv.updateById(uEnt);
            log.error("ofs处理入库失败：id:{} | stack:{}", id, ExceptionUtil.stacktraceToString(e));
            goodSrv.shopGoodsDown(Collections.singletonList(uEnt.getGoodCode()), "处理入库失败:"+e.getMessage());
        }
        });
    }

    /**
     * 设置商品详情
     */
    private void setIntroduce(BackupOfsGoodsEntity uEnt, ShopGoods sEnt, ShopGoods eEnt) {
        if (eEnt != null) {
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                // 先删除原来的数据
                ossClient.delete(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), sEnt.getGoodsCode()));
                // 再次添加数据
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), sEnt.getGoodsCode()), uEnt.getIntroduction());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(config.getDefaultShopGoodBodyUrl());
            }
        } else {
            // 内容设置到oss,商品下架注意清除
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), uEnt.getGoodCode()), uEnt.getIntroduction());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(config.getDefaultShopGoodBodyUrl());
            }
        }
    }

    private void setCategory(BackupOfsGoodsEntity uEnt, ShopGoods sEnt) {
        sEnt.setSupplierClass(uEnt.getCategoryId());
        sEnt.setSupplierClassName(uEnt.getCategory());
        ShopSupplierClass supplierClass = supClassSrv.selectOneBySupAndClass(uEnt.getCategory(), config.getCode());
        if (supplierClass != null) {
            sEnt.setSupplierClassId(supplierClass.getSupplierClassId().toString());
        }
        String query = uEnt.getName() + " 销售单位：" + uEnt.getUnit();
        stClassSrv.syncClassByAi(query, sEnt);
    }

    private void setBrand(BackupOfsGoodsEntity uEnt, ShopGoods sEnt) {
        brandSrv.syncBrandByAi(uEnt.getName(), uEnt.getName() + " 销售单位：" + uEnt.getUnit(), uEnt.getModel(),
                "", uEnt.getParam().replaceAll("[\":\"{\"\"},]", " ").trim(), uEnt.getBrandName(), uEnt.getUnit(), sEnt);
    }

    /**
     * 存Shop_good_detail
     */
    private void saveDetailInfo(BackupOfsGoodsEntity uEnt) {
        //查询详情表中是否存在
        val eEnt = detailSrv.getDetailForTrans(CollectionUtil.toList(uEnt.getGoodCode())).get(uEnt.getGoodCode());
        var sEnt = new ShopGoodsDetail();
        if (eEnt != null) {
            BeanUtil.copyProperties(eEnt, sEnt);
        }
        sEnt.setGoodsCode(uEnt.getGoodCode());
        sEnt.setGoodsSku(String.valueOf(uEnt.getSku()));
        //欧菲斯没有起订量限制，默认给1;  4.16号OFS文档更新, 有了起订量
        sEnt.setGoodsMoq(uEnt.getMinBuyNum() == null ? 1 : Integer.parseInt(uEnt.getMinBuyNum()));
        sEnt.setIsEnable("1");
        sEnt.setUpdateTime(new Date());
        sEnt.setGoodsClick(0);
        sEnt.setCommentNum(0);
        sEnt.setGoodsCollect(0);
        //规格
        sEnt.setGoodsSpec(uEnt.getParam().replaceAll("[\":\"{\"\"},]", " ").trim());
        sEnt.setGoodsSpecArray(uEnt.getParam());
        sEnt.setGoodsImage(uEnt.getImagePath());
        sEnt.setGoodsImageMore(uEnt.getImagePathMore());
        if (eEnt != null) {
            detailSrv.updateDetail(sEnt);
        }else {
            detailSrv.save(sEnt);
        }
    }

    /**
     * 设置上下架信息
     */
    private void setShelvesInfo(BackupOfsGoodsEntity uEnt) {

        if (uEnt.getState() == 1) {
            // 上架
            goodSrv.shopGoodsCodeUp(Collections.singletonList(uEnt.getGoodCode()), null);
        } else {
            // 下架
            goodSrv.shopGoodsDown(Collections.singletonList(uEnt.getGoodCode()), "供应商下架");
        }

    }

    /**
     * 填充价格信息
     */
    private void savePriceInfo(BackupOfsGoodsEntity uEnt) {
        val eEnt = priceSrv.getPriceForTrans(CollectionUtil.toList(uEnt.getGoodCode())).get(uEnt.getGoodCode());
        var sEnt = new ShopGoodsPrice();
        if (eEnt != null) {
            BeanUtil.copyProperties(eEnt, sEnt);
        }
        sEnt.setGoodsCode(uEnt.getGoodCode());
        sEnt.setIsEnable("1");
        sEnt.setUpdateTime(new Date());
        sEnt.setGoodsSku(String.valueOf(uEnt.getSku()));
        sEnt.setGoodsOriginalPrice(uEnt.getMallPrice());
        BigDecimal taxRate = new BigDecimal(uEnt.getTaxRatePercentage()).movePointLeft(2);
        sEnt.setGoodsOriginalNakedPrice(uEnt.getMallPrice().divide(new BigDecimal(1).add(taxRate), 4, BigDecimal.ROUND_HALF_UP));
        sEnt.setGoodsPactPrice(uEnt.getPrice());
        sEnt.setGoodsPactNakedPrice(uEnt.getUntaxedPrice());
        if (eEnt != null) {
            priceSrv.updateGoodsPrice(sEnt);
        }else {
            priceSrv.saveItem(sEnt);
        }
    }
}
