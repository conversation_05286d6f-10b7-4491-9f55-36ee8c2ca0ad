package com.ly.yph.api.virtualgoods.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月25日
 */
@Data
public class YZHOrderDetailDto implements Serializable {
    private static final long serialVersionUID = 6236134268492824395L;

    /**
     * 客户方订单编号
     */
    private String tparOrderCode;
    /**
     * 云中鹤父订单编号
     */
    private String parentOrderCode;
    /**
     * 虚拟商品订单编号
     */
    private String virtualOrderCode;
    /**
     * 订单总金额
     */
    private Double orderTotalAmount;
    /**
     * 提交订单时间
     */
    private Date createTime;
    /**
     * 取消订单时间
     */
    private Date cancelTime;
    /**
     * 订单状态(1013:处理中,1009:已完成,1004:已取消,1012:已关闭,1014:待核销)
     */
    private Integer orderStatus;

    /**
     * 关注 1013处理中（充值中）   1009:已完成（充值完成） ，1004:已取消（充值失败）

     */
    private Integer mallOrderState;

    private List<GoodsDetail> orderDetailOutputDTOList;

    @Data
    public static class GoodsDetail implements Serializable{
        private static final long serialVersionUID = 6747879411987236788L;
        /**
         * 虚拟商品 spu 编码
         */
        String goodsCode;
        /**
         * 虚拟商品 spu 名称
         */
        String goodsName;
        /**
         * 虚拟商品 Sku 编码
         */
        String goodsSkuCode;
        /**
         * 商品数量
         */
        Integer actualQty;
        /**
         * 商品主图
         */
        String goodsImageUrl;
        /**
         * 规格
         */
        String specSizeValue;
        /**
         * 面值
         */
        String goodsFaceValue;
        /**
         * 二级分类编码
         */
        String secondCategoryCode;
        /**
         * 二级分类名称
         */
        String secondCategoryName;
        /**
         * 末级分类编码
         */
        String lastCategoryCode;
        /**
         * 末级分类名称
         */
        String lastCategoryName;
        /**
         * 商品销售价
         */
        Double salePrice;
        /**
         * 商品总价
         */
        Double totalSalePrice;
        /**
         * 商家名称
         */
        String merchantName;
        /**
         * 充值手机号或者账号
         */
        String target;

        /**
         * 商品类型 1 直充，2 卡密
         */
        Integer goodsType;

        Double taxRate;
    }
}
