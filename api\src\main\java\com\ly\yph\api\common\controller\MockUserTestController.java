package com.ly.yph.api.common.controller;

import com.ly.yph.api.common.annotation.MockUser;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.base.user.LocalUserHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dev33.satoken.annotation.SaCheckLogin;

/**
 * 模拟用户测试控制器
 * 用于测试模拟用户功能是否正常工作
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class MockUserTestController {

    /**
     * 获取模拟用户信息
     * 用于验证模拟用户功能是否生效
     * 
     * @return 当前登录用户信息
     */
    @GetMapping("/mock-user-info")
    @MockUser(userId = 1L)
    @SaCheckLogin
    public LoginUser getMockUserInfo() {
        
        return LocalUserHolder.get();
    }
}