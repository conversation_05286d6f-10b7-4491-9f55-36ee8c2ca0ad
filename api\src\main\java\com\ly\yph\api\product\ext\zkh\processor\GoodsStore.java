package com.ly.yph.api.product.ext.zkh.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopGoodsDetail;
import com.ly.yph.api.goods.entity.ShopGoodsPrice;
import com.ly.yph.api.goods.entity.ShopGoodsStock;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.product.ext.common.BaseDataStore;
import com.ly.yph.api.product.ext.zkh.config.ZKHConfig;
import com.ly.yph.api.product.ext.zkh.entity.BackupZkhGoods;
import com.ly.yph.api.product.ext.zkh.service.BackupZkhGoodsService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierClass;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.oss.OSSAutoConfig;
import com.ly.yph.core.oss.OSSClient;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * step5 写入 shop_goods 数据库
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Service("ZKHGoodsStore")
@Slf4j
public class GoodsStore extends BaseDataStore<Long, BackupZkhGoods> {
    @Getter
    protected String processName = "ZKH入标准库";
    @Resource
    private OSSAutoConfig ossConfig;
    @Resource
    private ZKHConfig config;
    @Resource
    private BackupZkhGoodsService backSrv;
    @Resource
    private ShopGoodsService goodSrv;
    @Resource
    private ShopBrandService brandSrv;
    @Resource
    private ShopGoodsStockService stockSrv;
    @Resource
    private ShopGoodsPriceService priceSrv;
    @Resource
    private ShopGoodsDetailService detailSrv;
    @Resource
    private YphStandardClassService stClassSrv;
    @Resource
    private ShopSupplierClassService supClassSrv;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;
    @Resource
    private YflYamlConfig yflYamlConfig;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;


    @Override
    public List<Long> supplier(int count) {
        return TenantUtils.executeIgnore(() -> this.backSrv.selectAllForStoreProcess(count));
    }

    @Override
    @DistributedLock(value = "zkh_store_process", key = "#id", waitLock = false)
    public void doStoreItem(Long id) {
        BackupZkhGoods uEnt = TenantUtils.executeIgnore(() -> this.backSrv.getById(id));
        if (uEnt == null || uEnt.getSynchronize() != 0) {
            return;
        }
        TenantUtils.execute(uEnt.getTenantId(), () -> {
            // 清理单条场景下的重复数据，和 batch 逻辑保持一致：仅删除 is_enable 为 0 的冗余记录
            List<ShopGoods> sameGoods = goodSrv.getBaseMapper().queryGoodsByGoodsCodeIn(Collections.singleton(uEnt.getGoodCode()));
            if (CollectionUtil.isNotEmpty(sameGoods) && sameGoods.size() > 1) {
                List<Long> dupIds = sameGoods.stream()
                        .filter(g -> "0".equals(g.getIsEnable()))
                        .map(ShopGoods::getGoodsId)
                        .collect(Collectors.toList());
                if (!dupIds.isEmpty()) {
                    goodSrv.getBaseMapper().deleteByGoodsIdIn(dupIds);
                }
            }

            ShopGoods eEnt = goodSrv.getGoodsForStore(uEnt.getGoodCode());
            doProcessItem(uEnt, eEnt);
        });
        TenantUtils.executeIgnore(() -> {
            this.saveDetailInfo(CollectionUtil.toList(uEnt));
            this.saveStockInfo(CollectionUtil.toList(uEnt));
            this.savePriceInfo(CollectionUtil.toList(uEnt));

            if (!needCreate.isEmpty()) {
                this.goodSrv.saveBatch(this.needCreate.values());
            }
            if (!needUpdate.isEmpty()) {
                this.goodSrv.getBaseMapper().updateGoodsBatch(this.needUpdate.values());
            }
            if (!needApprove.isEmpty()) {
                this.goodSrv.supplierSubmitGoods(this.needApprove);
            }

            if (!needUpdateBack.isEmpty()) this.backSrv.getBaseMapper().updateSyncInfo(this.needUpdateBack.values());
            if (!needDeleteShelves.isEmpty())
                this.dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolIdBatch(null, needDeleteShelves);
            if (!needSetShelvesInfo.isEmpty()) this.setShelvesInfo(this.needSetShelvesInfo);
        });
    }

    @Override
    @DistributedLock(value = "zkh_store_process_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Long> ids) {
        List<BackupZkhGoods> uEnts = TenantUtils.executeIgnore(() -> this.backSrv.getBaseMapper().selectAllEntityByIdIn(ids.stream().map(Math::toIntExact).collect(Collectors.toList())));
        // 生成一个goodscode到对象的map
        Map<String, BackupZkhGoods> goodsMap = uEnts.stream().collect(Collectors.toMap(BackupZkhGoods::getGoodCode, Function.identity()));

        List<ShopGoods> shopGoods = TenantUtils.executeIgnore(() -> this.goodSrv.getBaseMapper().queryGoodsByGoodsCodeIn(goodsMap.keySet()));

        // 从shopGoods列表中筛选重复的getGoodsCode，且is_enable为0的项
        Map<String, List<ShopGoods>> groupedByGoodsCode = shopGoods.stream()
                .collect(Collectors.groupingBy(ShopGoods::getGoodsCode));

        List<ShopGoods> duplicatesWithIsEnableZero = groupedByGoodsCode.values().stream()
                .filter(list -> list.size() > 1)
                .flatMap(List::stream)
                .filter(shopGood -> shopGood.getIsEnable().equals("0"))
                .collect(Collectors.toList()); // 转换为List
        shopGoods.removeAll(duplicatesWithIsEnableZero);

        if(!CollectionUtil.isEmpty(duplicatesWithIsEnableZero)){
            goodSrv.getBaseMapper().deleteByGoodsIdIn(duplicatesWithIsEnableZero.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList()));
        }

        // 生成一个goodscode到对象的map
        Map<String, ShopGoods> shopGoodsMap = shopGoods.stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, Function.identity()));

        CountDownLatch latch = new CountDownLatch(uEnts.size());
        uEnts.parallelStream().forEach(item -> {
            try {
                TenantUtils.execute(item.getTenantId(), () -> {
                    ShopGoods eEnt = shopGoodsMap.get(item.getGoodCode());
                    doProcessItem(item, eEnt);
                });
            } catch (Exception ex) {
                log.error("JdGoodsSaveError:{}", ExceptionUtil.stacktraceToString(ex, 5000));
            } finally {
                latch.countDown();
            }
        });
        try {
            latch.await(); // 等待所有任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // 处理中断异常
        }
        TenantUtils.executeIgnore(() -> {
            if (!uEnts.isEmpty()) {
                this.saveDetailInfo(uEnts);
                this.saveStockInfo(uEnts);
                this.savePriceInfo(uEnts);
            }
            if (!needCreate.isEmpty()) this.goodSrv.saveBatch(this.needCreate.values());
            if (!needUpdate.isEmpty()) this.goodSrv.getBaseMapper().updateGoodsBatch(this.needUpdate.values());
            if (!needApprove.isEmpty()) this.goodSrv.supplierSubmitGoods(this.needApprove);
            if (!this.needUpdateBack.isEmpty())
                this.backSrv.getBaseMapper().updateSyncInfo(this.needUpdateBack.values());
        });

        commonIoExecutors.execute(() -> {
            TenantUtils.executeIgnore(() -> {
                if (!needSetShelvesInfo.isEmpty()) this.setShelvesInfo(this.needSetShelvesInfo);
                if (!needDeleteShelves.isEmpty())
                    this.dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolIdBatch(null, needDeleteShelves);
            });
        });
    }

    private void doProcessItem(BackupZkhGoods uEnt, ShopGoods eEnt) {
        try {
            ShopGoods sEnt = new ShopGoods();

            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            } else {
                // 首次转换，用ai分析标准分类和品牌
                setCategory(uEnt, sEnt);
                setBrand(uEnt, sEnt);
            }

            ShopSupplier supplierEnt = shopSupplierService.selectByCode(config.getCode());
            // 供应商档案配置不审批或者曾经已经审批通过的,无需审批,其他都需要审批
            sEnt.setAuditState(supplierEnt.getIsAudited() == 0 || (sEnt.getAuditState() != null && sEnt.getAuditState() == 1) ? 1 : 0);
            Long requiredTenantId = TenantContextHolder.getRequiredTenantId();
            sEnt.setGoodsName(yflYamlConfig.getTenantId().equals(requiredTenantId) ? uEnt.getDescription() : uEnt.getName());
            sEnt.setGoodsSku(uEnt.getSku());
            sEnt.setGoodsDesc(uEnt.getDescription());
            sEnt.setSaleUnit(uEnt.getSaleUnit());

            sEnt.setTaxRate(uEnt.getTaxRate());

            if (StrUtil.isNotBlank(uEnt.getProductArea())) {
                sEnt.setProductionPlace(uEnt.getProductArea());
            }

            sEnt.setDeliveryTime(uEnt.getDeliveryTime());
            sEnt.setSpecGoodsWareQd(uEnt.getWareQd());
            sEnt.setMaterialsCode(uEnt.getManufacturerMaterialNo());
            sEnt.setManufacturerMaterialNo(uEnt.getManufacturerMaterialNo());
            sEnt.setGoodsKeywords(uEnt.getKeywords());

            sEnt.setTaxCode(uEnt.getTaxCode());
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setGoodsMobileBoydUrl("");
            sEnt.setBackupGoodId(uEnt.getId());
            sEnt.setSupplierCode(config.getCode());
            sEnt.setSupplierName(config.getName());
            sEnt.setOrganizationId(supplierEnt.getOrganizationId());
            sEnt.setSupplierType(supplierEnt.getSupplierType());
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setUpdateTime(new Date());
            sEnt.setIsEnable("1");
            this.setIntroduce(uEnt, sEnt, eEnt);
            uEnt.setSynchronize(1);
            if (eEnt != null) {
                needUpdate.put(uEnt.getGoodCode(), sEnt);
                //将上下架表中删除状态改回
                needDeleteShelves.add(sEnt.getGoodsId());
            } else {
                needCreate.put(uEnt.getGoodCode(), sEnt);
            }
            if (sEnt.getAuditState() == 1) {
                needSetShelvesInfo.add(uEnt);
            } else {
                needApprove.add(sEnt);
            }
            uEnt.setUpdateTime(LocalDateTime.now());
        } catch (Exception ex) {
            uEnt.setSynchronize(2);
            uEnt.setUpdateTime(LocalDateTime.now());
            log.error("ZKH 入库时产生错误:id:{}，ex:{}", uEnt.getId(), ExceptionUtil.stacktraceToString(ex));
        }
        needUpdateBack.put(uEnt.getGoodCode(), uEnt);
    }

    /**
     * 设置上下架信息
     */
    private void setShelvesInfo(List<BackupZkhGoods> uEnts) {
        if (!uEnts.isEmpty())
            goodSrv.shopGoodsCodeUp(uEnts.stream().filter(BackupZkhGoods::getState).map(BackupZkhGoods::getGoodCode).collect(Collectors.toList()), null);
        if (!uEnts.isEmpty())
            goodSrv.shopGoodsDown(uEnts.stream().filter(item -> !item.getState()).map(BackupZkhGoods::getGoodCode).collect(Collectors.toList()), "供应商推送下架");
    }

    /**
     * 保存价格信息
     */
    private void savePriceInfo(Collection<BackupZkhGoods> uEnts) {
        List<ShopGoodsPrice> needUpdate = new ArrayList<>(16);
        List<ShopGoodsPrice> needCreate = new ArrayList<>(16);
        val eEnts = priceSrv.getPriceForTrans(uEnts.stream().map(BackupZkhGoods::getGoodCode).collect(Collectors.toList()));

        uEnts.forEach(item -> {
            var eEnt = eEnts.get(item.getGoodCode());
            var sEnt = new ShopGoodsPrice();
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setIsEnable("1");
            sEnt.setGoodsCode(item.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(item.getSku()));
            sEnt.setGoodsOriginalPrice(item.getOriPrice());
            sEnt.setTenantId(item.getTenantId());
            sEnt.setGoodsOriginalNakedPrice(item.getOriNakedPrice());
            sEnt.setGoodsPactPrice(item.getPrice());
            sEnt.setGoodsPactNakedPrice(item.getNakedPrice());
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.priceSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.priceSrv.getBaseMapper().updateGoodsPriceBatch(needUpdate);
    }

    /**
     * 保存库存信息
     */
    private void saveStockInfo(Collection<BackupZkhGoods> uEnts) {
        List<ShopGoodsStock> needUpdate = new ArrayList<>(16);
        List<ShopGoodsStock> needCreate = new ArrayList<>(16);
        val eEnts = stockSrv.getStockForTrans(uEnts.stream().map(BackupZkhGoods::getGoodCode).collect(Collectors.toList()));
        uEnts.forEach(uEnt -> {
            var sEnt = new ShopGoodsStock();
            var eEnt = eEnts.get(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSku()));
            sEnt.setStockAvailable(0);
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setStockAlert(0);
            sEnt.setIsEnable("1");
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.stockSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.stockSrv.getBaseMapper().updateStockInfoBatch(needUpdate);
    }

    private void saveDetailInfo(Collection<BackupZkhGoods> uEnts) {
        List<ShopGoodsDetail> needUpdate = new ArrayList<>(16);
        List<ShopGoodsDetail> needCreate = new ArrayList<>(16);

        val eEnts = detailSrv.getDetailForTrans(uEnts.stream().map(BackupZkhGoods::getGoodCode).collect(Collectors.toList()));
        uEnts.forEach(uEnt -> {
            var sEnt = new ShopGoodsDetail();
            var eEnt = eEnts.get(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSku()));
            sEnt.setIsEnable("1");
            sEnt.setGoodsMoq(0);
            sEnt.setGoodsClick(0);
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setCommentNum(0);
            sEnt.setGoodsCollect(0);
            sEnt.setGoodsMoq(uEnt.getMoq());
            // 处理规格
            var s = new JSONObject();
            var gs = new StringBuffer();
            var jsonArray = JSON.parseArray(uEnt.getParam());
            for (int i = 0; i < jsonArray.size(); i++) {
                var p = jsonArray.getJSONObject(i);
                var n = p.getString("attrName");
                var v = p.getString("attrValue");
                s.set(n, v);
                gs.append(n).append(" ").append(v).append(" ");
            }
            sEnt.setGoodsSpec(gs.toString());
            sEnt.setGoodsSpecArray(s.toJSONString(0));

            sEnt.setGoodsImage(uEnt.getFullimagePath());
            sEnt.setGoodsImageMore(uEnt.getImagePathMore());
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.detailSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.detailSrv.getBaseMapper().updateByPrimaryKeySelectiveBatch(needUpdate);
    }

    private void setBrand(BackupZkhGoods uEnt, ShopGoods sEnt) {
        StringBuilder gs = new StringBuilder();
        JSONArray jsonArray = JSONUtil.parseArray(uEnt.getParam());
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObj = jsonArray.getJSONObject(i);
            String n = jsonObj.getStr("attrName");
            String v = jsonObj.getStr("attrValue");
            gs.append(n).append(" ").append(v).append(" ");
        }
        brandSrv.syncBrandByAi(uEnt.getName(), uEnt.getDescription(), uEnt.getManufacturerMaterialNo(),
                "", gs.toString(), uEnt.getBrandName(), uEnt.getSaleUnit(), sEnt);
    }

    private void setCategory(BackupZkhGoods uEnt, ShopGoods sEnt) {
        sEnt.setSupplierClass(uEnt.getCategory());
        sEnt.setSupplierClassName(uEnt.getCategoryName());
        ShopSupplierClass supplierClass = supClassSrv.selectOneBySupAndClass(uEnt.getCategory(), config.getCode());
        if (supplierClass != null) {
            sEnt.setSupplierClassId(supplierClass.getSupplierClassId().toString());
        }
        stClassSrv.syncClassByAi(uEnt.getDescription(), sEnt);
    }

    private void setIntroduce(BackupZkhGoods uEnt, ShopGoods sEnt, ShopGoods eEnt) {
        if (eEnt != null) {
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                // 先删除原来的数据
                ossClient.delete(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), sEnt.getGoodsCode()));

                // 再次添加数据
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), sEnt.getGoodsCode()), uEnt.getIntroduction());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(config.getDefaultShopGoodBodyUrl());
            }
        } else {
            // 内容设置到oss,商品下架注意清除
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), uEnt.getGoodCode()), uEnt.getIntroduction());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(config.getDefaultShopGoodBodyUrl());
            }
        }
    }
}
