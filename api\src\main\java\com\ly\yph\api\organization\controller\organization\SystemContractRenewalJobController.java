package com.ly.yph.api.organization.controller.organization;

import com.ly.yph.api.electsign.service.ContractSyncService;
import com.ly.yph.api.goods.workflow.ContractWorkFlowHandler;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.organization.service.SystemContractRenewalService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.supplier.vo.MySupplierVo;
import com.ly.yph.api.system.service.EmailSendLogService;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.email.MailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 组织
 *
 * <AUTHOR>
 * @date 2022/06/08
 */
@Api(tags = "管理后台 - 合同签约同步")
@RestController
@RequestMapping("/contractSync")
@Slf4j
public class SystemContractRenewalJobController {

    @Resource
    private ContractSyncService contractSyncService;
    @Resource
    private SystemContractRenewalService systemContractRenewalService;
    @Resource
    private ContractWorkFlowHandler contractWorkFlowHandler;

    @Resource
    private ShopSupplierService shopSupplierService;

    @Resource
    private MailService mailService;

    @Resource
    private EmailSendLogService emailSendLogService;

    @ApiOperation("合同管理-同步合同到srm")
    @GetMapping("/syncToSrm")
    @DataPermission(enable = false)
    public void queryContractDetail() {
        contractSyncService.autoSyncContractToSRM();
    }



    @RequestMapping("/senSingMail")
    @DataPermission(enable = false)
    @ApiOperation(value = "发送签署邮件", httpMethod ="POST")
    public String senSingMail(String contractId){
        SystemContractRenewalEntity entity = systemContractRenewalService.getById(contractId);
        if(entity!=null){
            systemContractRenewalService.sendSignEmail(entity);
        }
        return "ok";
    }

    @RequestMapping("/senApprovalMail")
    @DataPermission(enable = false)
    @ApiOperation(value = "发送签署邮件", httpMethod ="POST")
    public String senApprovalMail(String contractId, int approvalState){
        SystemContractRenewalEntity entity = systemContractRenewalService.getById(contractId);
        contractWorkFlowHandler.resultNotice(entity, approvalState, "test");

        return "ok";
    }

}
