package com.ly.yph.api.settlement.common.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.entity.ShopDeliveryDetail;
import com.ly.yph.api.order.mapper.ShopDeliveryDetailMapper;
import com.ly.yph.api.order.service.ShopDeliveryDetailService;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoPoolService;
import com.ly.yph.api.settlement.common.dto.check.PreSupplierBillOutMessageDto;
import com.ly.yph.api.settlement.common.entity.SettleBillPool;
import com.ly.yph.api.settlement.common.entity.SettleCompanyBillMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 包裹消息处理后 统一对账单的操作
 * 1.包裹发货后 标准商城的入帐单池操作
 * 2.包裹上传签收单后 出B端客户供应商的账单操作
 * 3.包裹的验收后 出客户的账单操作
 * 4.包裹收货后 更新池数据的订单明细状态
 * 5.包裹妥投 福利的供应商妥投出账
 */
@Slf4j
@Service
public class DeliveryForBillProcessService {

    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;
    @Resource
    private SettleBillPoolService settleBillPoolService;
    @Resource
    private SettleCheckDetailService settleCheckDetailService;
    @Resource
    private SettleCompanyBillMessageService settleCompanyBillMessageService;
    @Resource
    private ShopDeliveryDetailService shopDeliveryDetailService;
    @Resource
    private YflYamlConfig yflYamlConfig;
    @Resource
    private SettleBillLifeCycleService settleBillLifeCycleService;
    @Resource
    private ShopDeliveryDetailMapper shopDeliveryDetailMapper;
    @Resource
    private SettleShopBillService settleShopBillService;

    /**
     * 包裹发货消息之后 结算相关处理
     *
     * @param deliveryIdList 包裹ids
     */
    @Transactional
    public void deliveryInfoProcess(List<Long> deliveryIdList) {
        if (CollectionUtil.isEmpty(deliveryIdList)) {
            return;
        }
        // 处理生命周期
        deliveryIdList.forEach(e -> purchaseOrderInfoPoolService.updatePoolInfoForDelivery(e, 1));

        //处理入帐单池
        settleBillPoolService.deliveryToPool(deliveryIdList);
    }

    /**
     * 包裹收货后更新池数据订单明细状态
     *
     * @param deliveryIdList 包裹id
     */
    @Transactional
    public void deliveryReceiptProcess(List<Long> deliveryIdList) {
        if (CollectionUtil.isEmpty(deliveryIdList)) {
            return;
        }
        deliveryIdList.forEach(e -> purchaseOrderInfoPoolService.updatePoolInfoForDelivery(e, 3));
        //更新账单池中订单明细的状态为收货完成
        settleBillPoolService.deliveryReceiptUpdateOrderStateForBillPool(deliveryIdList);
    }

    /**
     * 独立供应商包裹删除同步删除账单池
     *
     * @param deliveryIdList
     */
    @Transactional
    public void deliveryDeleteProcess(List<Long> deliveryIdList) {
        if (CollectionUtil.isEmpty(deliveryIdList)) {
            return;
        }
        List<Long> deliveryDetailIdList = new ArrayList<>();
        purchaseOrderInfoPoolService.updatePoolInfoForDeliveryDelete(deliveryIdList,deliveryDetailIdList);
        // 账单池删除
        if(CollectionUtil.isNotEmpty(deliveryDetailIdList)){
            settleBillPoolService.UpdatePoolForDeliveryDelete(deliveryDetailIdList);
        }

    }

    /**
     * 东风商城下的c端商城：车主信赖商城，神龙车主商城，东本营销积分只需要妥投或者签收单一项满足 就可以存入供应商出账的消息表
     * 3sm商城,保持原有 背靠背出账
     * 其他的客户 必须要供应商签收单 才可以出账
     * 友福利：电商&独立供应商 妥投或者签收单一项 既可以存入供应商出账的消息表
     */
    @Transactional
    public void deliveryOrderArriveProcess(Long deliveryId) {

        purchaseOrderInfoPoolService.updatePoolInfoForDelivery(deliveryId, 2);

        List<PreSupplierBillOutMessageDto> preSupplierBillOutDataList = shopDeliveryDetailService.getBaseMapper().getPreSupplierBillOutData(deliveryId);
        if (CollectionUtil.isEmpty(preSupplierBillOutDataList)) {
            return;
        }

        // 历史包裹 保持以前方式
        if(preSupplierBillOutDataList.get(0).getIsHistory()==0){
            //处理历史以妥投出账的
          settleBillPoolService.sapDeliveryToBillPool(Collections.singletonList(deliveryId));
        }else {
            //处理新包裹以妥投出账的
            orderArriveNewProcess(preSupplierBillOutDataList);
        }
    }


    /**
     * 新包裹进入 消息变更
     * @param preSupplierBillOutDataList
     */
    @Transactional
    public void orderArriveNewProcess(List<PreSupplierBillOutMessageDto> preSupplierBillOutDataList) {
        if (!getIsToSaveSupplierBillMessage(preSupplierBillOutDataList.get(0))) {
            return;
        }
        //准备存入供应商出账的消息表
        List<Long> detailIds = preSupplierBillOutDataList.stream().map(PreSupplierBillOutMessageDto::getDeliveryDetailId).collect(Collectors.toList());
        //准备保存消息
        QueryWrapper<SettleCompanyBillMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleCompanyBillMessage::getDeliveryDetailId, detailIds)
                .eq(SettleCompanyBillMessage::getSupplierCheckState, 0)
                .select(SettleCompanyBillMessage::getDeliveryDetailId,
                        SettleCompanyBillMessage::getId);
        List<SettleCompanyBillMessage> settleCompanyBillMessages = settleCompanyBillMessageService.list(queryWrapper);
        if (CollectionUtil.isEmpty(settleCompanyBillMessages)) {
            return;
        }
        // 查询消息表 对表数据进行更新处理
        List<Long> updateMessageIds = new ArrayList<>();
        Map<Long, SettleCompanyBillMessage> messageMap = settleCompanyBillMessages.stream().collect(Collectors.toMap(SettleCompanyBillMessage::getDeliveryDetailId, Function.identity()));
        for (PreSupplierBillOutMessageDto preSupplierBillOutMessageDto : preSupplierBillOutDataList) {
            SettleCompanyBillMessage settleCompanyBillMessage = messageMap.get(preSupplierBillOutMessageDto.getDeliveryDetailId());
            if (settleCompanyBillMessage == null) {
                //说明可能签收单 填充了该包裹的供应商验收
                continue;
            }
            updateMessageIds.add(settleCompanyBillMessage.getId());
        }

        UpdateWrapper<SettleCompanyBillMessage> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(SettleCompanyBillMessage::getId, updateMessageIds)
                .set(SettleCompanyBillMessage::getSupplierCheckState, 1)
                .set(SettleCompanyBillMessage::getSupplierCheckType, 0);
        settleCompanyBillMessageService.update(updateWrapper);
    }


    private Boolean getIsToSaveSupplierBillMessage(PreSupplierBillOutMessageDto preSupplierBillOutData) {
        if (yflYamlConfig.getTenantId().equals(preSupplierBillOutData.getTenantId())) {
            return true;
        }
        // 东风商城的
        if (CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(preSupplierBillOutData.getCompanyCode()) &&
                "东风本田汽车销售有限公司".equals(preSupplierBillOutData.getInvoiceSubject())) {
            // 存入消息
            return true;
        }

        if (CompanyEnum.DSDB000.getCompanyCode().equalsIgnoreCase(preSupplierBillOutData.getCompanyCode()) ||
              CompanyEnum.DPCAJF.getCompanyCode().equalsIgnoreCase(preSupplierBillOutData.getCompanyCode())) {
            // 存入消息
            return true;
        }

        return false;
    }


    /**
     * B端客户验收后操作
     */
    @Transactional
    public void checkFormProcess(Long checkFormId) {

        purchaseOrderInfoPoolService.updatePoolInfoForCheckForm(checkFormId);
        //保存客户账单的出账消息(只保存在商城内验收的消息) 外部验收的数据 以外部做匹配的时候 更新外部验收状态
        // 同步数据到账单池 南方的独立供应商不再入池出账-朱慧兰-20240527
        List<PreSupplierBillOutMessageDto> settleCompanyBillMessageList = settleCheckDetailService.getBaseMapper().getCompanyBillMessageForCheckFormId(checkFormId);
        if (CollectionUtil.isEmpty(settleCompanyBillMessageList)) {
            return;
        }
        // 兼容历史包裹 标准商城的验收入账方式
        Map<Integer, List<PreSupplierBillOutMessageDto>> isHistoryMap = settleCompanyBillMessageList.stream()
                .collect(Collectors.groupingBy(PreSupplierBillOutMessageDto::getIsHistory));
        for (Integer historyFlag : isHistoryMap.keySet()) {
            if (historyFlag == 0) {
                // 历史包裹验收
                processHistoryDeliveryCheck(isHistoryMap.get(historyFlag),checkFormId);
            } else {
                // 新包裹验收
                processNewDeliveryCheck(isHistoryMap.get(historyFlag));
            }
        }
    }

    /**
     * 新包裹处理
     * @param settleCompanyBillMessages
     */
    @Transactional
    public void processNewDeliveryCheck( List<PreSupplierBillOutMessageDto> settleCompanyBillMessages) {

        List<PreSupplierBillOutMessageDto> validDatas = filterSapOrderForNewDelivery(settleCompanyBillMessages);
        if(CollectionUtil.isEmpty(validDatas)){
            return;
        }

        QueryWrapper<SettleCompanyBillMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleCompanyBillMessage::getDeliveryDetailId,
                validDatas.stream().map(PreSupplierBillOutMessageDto::getDeliveryDetailId).collect(Collectors.toList()))
                .eq(SettleCompanyBillMessage::getCompanyCheckState, 0)
                .select(SettleCompanyBillMessage::getId,
                        SettleCompanyBillMessage::getDeliveryDetailId);

        List<SettleCompanyBillMessage> billMessages = settleCompanyBillMessageService.list(queryWrapper);
        if (billMessages.isEmpty()) {
            return;
        }

        Map<Long, SettleCompanyBillMessage> messageMap = billMessages.stream()
                .collect(Collectors.toMap(SettleCompanyBillMessage::getDeliveryDetailId, Function.identity()));


        List<SettleCompanyBillMessage> updateList = new ArrayList<>();
        for (PreSupplierBillOutMessageDto settleCompanyBillMessage : validDatas) {

            SettleCompanyBillMessage message = messageMap.get(settleCompanyBillMessage.getDeliveryDetailId());
            if (message == null) {
                continue;
            }
            SettleCompanyBillMessage billMessage = new SettleCompanyBillMessage();
            billMessage.setId(message.getId());
            billMessage.setCheckFormDetailId(settleCompanyBillMessage.getCheckFormDetailId());
            billMessage.setCompanyCheckState(1);
            updateList.add(billMessage);
        }
        settleCompanyBillMessageService.updateBatchById(updateList);
    }

    private List<PreSupplierBillOutMessageDto> filterSapOrderForNewDelivery(List<PreSupplierBillOutMessageDto> settleCompanyBillMessages) {
        List<PreSupplierBillOutMessageDto> validList = new ArrayList<>();

        for (PreSupplierBillOutMessageDto settleCompanyBillMessage : settleCompanyBillMessages) {

            if (2 == settleCompanyBillMessage.getSapOrderType()) {
                continue;
            }

            if (999 == settleCompanyBillMessage.getSapOrderType() && CompanyEnum.DFCV.getCompanyCode().equals(settleCompanyBillMessage.getCompanyCode())) {
                continue;
            }

            validList.add(settleCompanyBillMessage);
        }

        return validList;
    }

    /**
     * 历史包裹验收入池 走历史方式 客户供应商两边同处
     * @param settleCompanyBillMessages
     */
    @Transactional
    public void processHistoryDeliveryCheck(List<PreSupplierBillOutMessageDto> settleCompanyBillMessages,Long checkFormId) {
        List<Long> deliveryDetailIdList = settleCompanyBillMessages.stream().map(PreSupplierBillOutMessageDto::getDeliveryDetailId).collect(Collectors.toList());

        List<SettleBillPool> billPoolList = settleBillPoolService.getBaseMapper().getToPoolByCheckData(checkFormId, deliveryDetailIdList);

        settleBillPoolService.saveBatch(billPoolList);
        settleBillLifeCycleService.saveLifeCycleFromPool(CollStreamUtil.toList(billPoolList, SettleBillPool::getId));
    }

    @Transactional
    public void deliverySignProcess(Long deliveryId){
        QueryWrapper<ShopDeliveryDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopDeliveryDetail::getDeliveryId, deliveryId);
        List<ShopDeliveryDetail> deliveryDetails = shopDeliveryDetailService.list(queryWrapper);

        QueryWrapper<SettleCompanyBillMessage> messageQueryWrapper = new QueryWrapper<>();
        messageQueryWrapper.lambda().in(SettleCompanyBillMessage::getDeliveryDetailId,
                deliveryDetails.stream().map(ShopDeliveryDetail::getId).collect(Collectors.toList()))
                .select(SettleCompanyBillMessage::getId,
                        SettleCompanyBillMessage::getDeliveryDetailId,
                        SettleCompanyBillMessage::getSupplierCheckState);

        List<SettleCompanyBillMessage> messages = settleCompanyBillMessageService.list(messageQueryWrapper);
        if (CollectionUtil.isEmpty(messages)) {
            return;
        }

        Map<Long, SettleCompanyBillMessage> messageMap = messages.stream().collect(Collectors.toMap(SettleCompanyBillMessage::getDeliveryDetailId, Function.identity()));
        List<Long> updateMessageList = new ArrayList<>();
        for (ShopDeliveryDetail deliveryDetail : deliveryDetails) {
            SettleCompanyBillMessage settleCompanyBillMessage = messageMap.get(deliveryDetail.getId());
            if (settleCompanyBillMessage == null) {
                continue;
            }

            if(settleCompanyBillMessage.getSupplierCheckState()==1){
                continue;
            }
            updateMessageList.add(settleCompanyBillMessage.getId());
        }

        if(CollectionUtil.isEmpty(updateMessageList)){
            return;
        }

        UpdateWrapper<SettleCompanyBillMessage> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(SettleCompanyBillMessage::getId, updateMessageList)
                .set(SettleCompanyBillMessage::getSupplierCheckState, 1)
                .set(SettleCompanyBillMessage::getSupplierCheckType, 1);
        settleCompanyBillMessageService.update(updateWrapper);
    }

    /**
     * 外部sap验收的数据 历史包裹实时出账供应商侧
     * 新包裹 更新消息客户出账状态
     * @param billPools
     * @param yearAndMonth
     */
    @Transactional
    public void outBillMatchProcess(List<SettleBillPool> billPools, String yearAndMonth) {
        List<SettleBillPool> old = new ArrayList<>();
        List<SettleBillPool> needToSelect = new ArrayList<>();
        List<Long> newDeliveryList = new ArrayList<>();

        for (SettleBillPool billPool : billPools) {
            if (billPool.getDeliveryDetailId() == 0) {
                old.add(billPool);
                continue;
            }
            needToSelect.add(billPool);
        }

        if (CollectionUtils.isNotEmpty(needToSelect)) {
            List<Long> deliveryDetailIds = needToSelect.stream().map(SettleBillPool::getDeliveryDetailId).collect(Collectors.toList());
            //查询包裹是否是新旧的
            List<PreSupplierBillOutMessageDto> deliveryMessage = new ArrayList<>();
            Lists.partition(deliveryDetailIds, 1000).forEach(deliveryDetailIdList -> {
                List<PreSupplierBillOutMessageDto> preSupplierBillOutDataForOutCheck = shopDeliveryDetailMapper.getPreSupplierBillOutDataForOutCheck(deliveryDetailIdList);
                deliveryMessage.addAll(preSupplierBillOutDataForOutCheck);
            });


            Map<Long, PreSupplierBillOutMessageDto> historyFlagMap = deliveryMessage.stream().collect(Collectors.toMap(PreSupplierBillOutMessageDto::getDeliveryDetailId, Function.identity()));

            for (SettleBillPool settleBillPool : needToSelect) {

                PreSupplierBillOutMessageDto preSupplierBillOutMessageDto = historyFlagMap.get(settleBillPool.getDeliveryDetailId());
                if (preSupplierBillOutMessageDto.getIsHistory() == 0) {
                    old.add(settleBillPool);
                } else {
                    newDeliveryList.add(settleBillPool.getDeliveryDetailId());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(old)) {
            //同步生成供应商账单
            settleShopBillService.realTimeCheckSupplierBill(old, 1L, yearAndMonth);
        }

        if (CollectionUtils.isEmpty(newDeliveryList)) {
            return;
        }


        //更新消息表客户验收状态
        QueryWrapper<SettleCompanyBillMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleCompanyBillMessage::getDeliveryDetailId, newDeliveryList)
                .select(SettleCompanyBillMessage::getId, SettleCompanyBillMessage::getCompanyCheckState);
        List<SettleCompanyBillMessage> list = settleCompanyBillMessageService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        list.forEach(e->e.setCompanyCheckState(1));
        settleCompanyBillMessageService.updateBatchById(list);
    }
}
