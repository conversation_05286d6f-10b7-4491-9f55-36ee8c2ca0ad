# 双远程仓库配置说明

本项目配置了两个远程仓库，用于代码的备份和同步。

## 远程仓库配置

### GitLab (主仓库)
- **名称**: `gitlab`
- **URL**: `https://devcloud.szlanyou.com/gitlab/lyshopcloud/lylcyyph.git`
- **用途**: 主要开发仓库，公司内部使用

### GitHub (备份仓库)
- **名称**: `github`
- **URL**: `https://github.com/zhangliang2198/lylcyyph.git`
- **用途**: 私有备份仓库，代码安全备份

## 创建 GitHub 私有仓库

1. 访问 [GitHub 新建仓库页面](https://github.com/new)
2. 填写仓库信息：
   - **Repository name**: `lylcyyph`
   - **Description**: `蓝云优品汇项目 - 私有备份仓库`
   - **Visibility**: 选择 **Private**
   - **不要勾选**任何初始化选项（README、.gitignore、License）
3. 点击 "Create repository"

## 常用命令

### 查看远程仓库
```bash
git remote -v
```

### 推送到指定仓库
```bash
# 推送到 GitLab
git push gitlab feature/master_for_job

# 推送到 GitHub
git push github feature/master_for_job
```

### 从指定仓库拉取
```bash
# 从 GitLab 拉取
git pull gitlab feature/master_for_job

# 从 GitHub 拉取
git pull github feature/master_for_job
```

## 便捷脚本

项目提供了几个便捷脚本来管理双仓库：

### 1. 推送到两个仓库
```powershell
.\scripts\push-to-both.ps1
```

### 2. 同步仓库
```powershell
.\scripts\sync-repos.ps1
```

### 3. 设置指南
```powershell
.\scripts\setup-github-repo.ps1
```

## 注意事项

1. **权限管理**: 确保对两个仓库都有推送权限
2. **分支同步**: 建议保持两个仓库的分支结构一致
3. **敏感信息**: GitHub 仓库虽然是私有的，但仍需注意不要提交敏感配置信息
4. **定期同步**: 建议定期将代码同步到两个仓库，避免数据丢失

## 故障排除

### GitHub 推送失败
1. 检查仓库是否已创建
2. 确认仓库 URL 是否正确
3. 验证 GitHub 账户权限
4. 检查网络连接

### GitLab 推送失败
1. 检查公司网络连接
2. 验证 GitLab 账户权限
3. 确认分支名称是否正确

## 安全建议

1. 使用 SSH 密钥而不是 HTTPS 密码认证
2. 定期更新访问令牌
3. 不要在代码中硬编码敏感信息
4. 定期备份重要分支
