package com.ly.yph.api.settlement.common.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;

import com.alibaba.fastjson2.JSON;
import com.ly.yph.api.organization.controller.organization.vo.activity.ActivityListReqVO;
import com.ly.yph.api.organization.controller.organization.vo.activity.ActivityRespVO;
import com.ly.yph.api.organization.service.SystemActivityService;
import com.ly.yph.api.settlement.common.dto.bill.*;
import com.ly.yph.api.settlement.common.dto.settleBillPool.SettleBillPoolCheckOutDto;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.enums.BillStatusEnum;
import com.ly.yph.api.settlement.common.service.SettleShopBillActivityMemberService;
import com.ly.yph.api.settlement.common.service.SettleShopBillDetailService;
import com.ly.yph.api.settlement.common.service.SettleShopBillPostageDetailService;
import com.ly.yph.api.settlement.common.service.SettleShopBillService;
import com.ly.yph.api.settlement.common.vo.bill.*;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.ServiceResult;

import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.excel.util.ExcelUtils;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.idempotent.core.annotation.Idempotent;
import com.ly.yph.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.validation.annotation.Validated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 主账单
 *
 * <AUTHOR>
 */

@Slf4j
@SaCheckLogin
@RestController
@Api(tags = "结算中心 - 账单出账")
@RequestMapping("settleShopBill")
public class SettleShopBillController {


    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;
    @Resource
    private SystemActivityService systemActivityService;
    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;
    @Resource
    private SettleShopBillActivityMemberService settleShopBillActivityMemberService;


    //后续账单相关操作都加上权限控制
    @ApiOperation(value = "客户单个提前出账", httpMethod = "POST")
    @PostMapping("/checkedBillBySingle")
    @SaCheckPermission("settleBill:customerBill:single")
    public ServiceResult<?> checkedBillBySingle(@RequestBody SettleBillPoolCheckOutDto settleBillPoolQueryDto) {

        return settleShopBillService.checkedBillBySingleReady(settleBillPoolQueryDto);

    }

    @ApiOperation(value = "月度批量出账", httpMethod = "GET")
    @GetMapping("/checkedBillAll")
    @SaCheckPermission("settleBill:customerBill:all")
    @Idempotent(timeout = 60, message = "请误重复提交！")
    public ServiceResult<?> checkedBillAll(@RequestParam("yearAndMonth") String yearAndMonth,@RequestParam("isPlatformReconciliation") Integer isPlatformReconciliation) {
        //后续加锁，防止多次提交，控制次数
        return settleShopBillService.checkedBillAll(yearAndMonth, isPlatformReconciliation);

    }

    @ApiOperation(value = "指派明细到已有帐单中", httpMethod = "POST")
    @PostMapping("/detailsToBill")
    @SaCheckPermission("settleBill:customerBill:detailsToBill")
    public ServiceResult<?> detailsToBill(@RequestBody @Validated DetailToBillDto detailToBillDto) {
        return settleShopBillService.detailsToBill(detailToBillDto);
    }

    @ApiOperation(value = "查询账单主列表", httpMethod = "GET")
    @GetMapping("page")
    @SaCheckPermission("settleBill:customerBill:query")
    public ServiceResult<PageResp<SettleShopBillVo>> getSettleShopBillPage(PageReq pageReq, SettleShopBillPageReqVo reqVo) {
        return ServiceResult.succ(settleShopBillService.querySettleShopBillPage(pageReq, reqVo));
    }

    @ApiOperation(value = "南方独立供应商账单主列表", httpMethod = "GET")
    @GetMapping("dfsBillPage")
    @SaCheckPermission("settleBill:dfsBill:query")
    public ServiceResult<PageResp<SettleShopBillVo>> getDfsSettleShopBillPage(PageReq pageReq, SettleShopBillPageReqVo reqVo) {
        return ServiceResult.succ(settleShopBillService.queryDfsSettleShopBillPage(pageReq, reqVo));
    }

    @ApiOperation(value = "导出主账单列表", httpMethod = "GET")
    @GetMapping("exportBill")
    @SaCheckPermission("settleBill:customerBill:export")
    public void exportBill(HttpServletResponse response, SettleShopBillPageReqVo reqVo) throws IOException {
        List<SettleShopBillVo> settleShopBillVos = settleShopBillService.exportBillList(reqVo);
        ExcelUtils.write(response, "账单主列表" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xls", "账单主列表数据", SettleShopBillVo.class, settleShopBillVos);
    }

    @ApiOperation(value = "导出供应商主账单列表", httpMethod = "GET")
    @GetMapping("dfsExportBill")
    @SaCheckPermission("settleBill:customerDfsBill:export")
    public void dfsExportBill(HttpServletResponse response, SettleShopBillPageReqVo reqVo) throws IOException {
        List<SupplierSettleShopBillVo> supplierSettleShopBillVos = settleShopBillService.dfsExportBillList(reqVo);
        ExcelUtils.write(response, "账单主列表" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xls", "账单主列表数据", SupplierSettleShopBillVo.class, supplierSettleShopBillVos);
    }


    @ApiOperation(value = "删除账单明细", httpMethod = "POST")
    @PostMapping("deleteDetail")
    @SaCheckPermission("settleBill:customerBill:deleteDetail")
    @Idempotent(timeout = 15, message = "请误重复提交！")
    public ServiceResult<?> deleteDetail(@RequestBody @Validated DetailRemoveFromBillDto detailRemoveFromBillDto) {
        return settleShopBillService.deleteDetail(detailRemoveFromBillDto);
    }

    @ApiOperation(value = "导出账单全部信息", httpMethod = "GET")
    @GetMapping("/exportBillDetail")
    @SaCheckPermission("settleBill:customerBill:exportBillDetail")
    public void exportBillDetail(HttpServletResponse response, SettleShopBillDetailPageReqVo vo) {
        //获取对账单数据
        SettleShopBill settleShopBill = settleShopBillService.getById(vo.getBillId());
        Integer poolType = settleShopBillService.getPoolType(settleShopBill);

        //获取对账单商品详情
        List<SettleShopBillDetailExcelVo> shopBillDetails = settleShopBillDetailService.getBillDetailExecl(vo, poolType);
        if(CollectionUtil.isEmpty(shopBillDetails)){
            throw new ParameterException("账单明细数据为空！");
        }

        List<TotalAmountForTaxRate> totalAmountForTaxRateList = new ArrayList<>();
        Map<Integer, List<SettleShopBillDetailExcelVo>> taxRateMap = shopBillDetails.stream().collect(Collectors.groupingBy(SettleShopBillDetailExcelVo::getTaxRate));

        taxRateMap.forEach((taxRate, detailList) -> {
            TotalAmountForTaxRate totalAmountForTaxRate = new TotalAmountForTaxRate();
            totalAmountForTaxRate.setTaxRate(taxRate);
            totalAmountForTaxRate.setAmountTax(detailList.stream().map(SettleShopBillDetailExcelVo::getTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalAmountForTaxRate.setAmountNaked(detailList.stream().map(SettleShopBillDetailExcelVo::getTotalPriceNaked).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalAmountForTaxRateList.add(totalAmountForTaxRate);
        });

        //获取邮费
        List<SettleShopBillPostageExcelVo> postageExcelVoList = settleShopBillPostageDetailService.getPostageExcel(vo);
        Map<String, Object> map = new HashMap<>();
        map.put("billSn", settleShopBill.getBillSn());
        map.put("customerName", settleShopBill.getCustomerName());
        map.put("createTime", DateUtils.format(settleShopBill.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        map.put("checkYear", settleShopBill.getCheckYear().toString());
        map.put("checkMonth", settleShopBill.getCheckMonth().toString());
        map.put("billStatus", BillStatusEnum.getBillStatusNameByCode(settleShopBill.getBillStatus()));
        map.put("totalTaxAmount", settleShopBill.getTotalTaxAmount().toString());
        map.put("postage", settleShopBill.getPostage().toString());


        InputStream is = null;

        try {
            //获取模板
            if (1 == poolType) {
                is = ResourceUtil.getStream("classpath:templates/shop_bill_template.xlsx");
            } else {
                is = ResourceUtil.getStream("classpath:templates/shop_bill_template_yfl.xlsx");
            }

            //文件名
            String filename = settleShopBill.getCustomerName() + settleShopBill.getCheckYear() + "年" + settleShopBill.getCheckMonth() + "月账单.xlsx";
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).excelType(ExcelTypeEnum.XLSX).build();

            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "对账单数据").build();
            TotalAmountForTaxRate totalAmountForTaxRate =new TotalAmountForTaxRate();
            totalAmountForTaxRate.setAmountNaked(shopBillDetails.stream().map(SettleShopBillDetailExcelVo::getTotalPriceNaked).reduce(BigDecimal.ZERO,BigDecimal::add));
            totalAmountForTaxRate.setAmountTax(shopBillDetails.stream().map(SettleShopBillDetailExcelVo::getTotalPriceTax).reduce(BigDecimal.ZERO,BigDecimal::add));
            totalAmountForTaxRate.setIndex("合计");
            totalAmountForTaxRateList.add(totalAmountForTaxRate);
            excelWriter.fill(new FillWrapper("totalAmountForTaxRateList", totalAmountForTaxRateList), writeSheet1);
            excelWriter.fill(map, writeSheet1);
            if (1 == poolType) {
                WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "对账单商品详情").head(SettleShopBillDetailExcelVo.class).build();
                excelWriter.fill(new FillWrapper("data", shopBillDetails), writeSheet2);
            } else {
                WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "福利账单明细").head(SettleShopBillDetailExcelVo.class).build();
                excelWriter.fill(new FillWrapper("data", shopBillDetails), writeSheet2);
                //获取福利成员管理
                List<SettleShopBillActivityMemberExcelVo> activityMemberExcelVos = settleShopBillActivityMemberService.getActivityMemberExcel(vo);
                WriteSheet writeSheet3 = EasyExcel.writerSheet(3, "福利成员管理").head(SettleShopBillActivityMemberExcelVo.class).build();
                excelWriter.fill(new FillWrapper("data", activityMemberExcelVos), writeSheet3);
            }

            WriteSheet writeSheet4 = EasyExcel.writerSheet(2, "对账单邮费明细").head(SettleShopBillPostageExcelVo.class).build();
            excelWriter.fill(new FillWrapper("data", postageExcelVoList), writeSheet4);


            excelWriter.finish();
        } catch (Exception e) {
            log.error("导出对账单明细失败:", e);
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("关闭流失败:", e);
                    e.printStackTrace();
                }
            }
        }
    }


    @ApiOperation(value = "账单维度对账", httpMethod = "POST")
    @PostMapping("/reconciliationBill")
    @SaCheckPermission("settleBill:customerBill:reconciliationBill")
    public ServiceResult<?> reconciliationBill(@RequestBody @Validated ReconciliationBillReqVo reconciliationBillReqVo) {
        return settleShopBillService.reconciliationBill(reconciliationBillReqVo);
    }


    @ApiOperation(value = "活动管理-获取列表（分页）")
    @GetMapping("/yflActivityPage")
    @SaCheckPermission("settleBill:customerBill:yflActivityQuery")
    public ServiceResult<PageResp<ActivityRespVO>> yflActivityPage(PageReq pageReqDto, ActivityListReqVO reqVO) {
        PageResp<ActivityRespVO> pageResp = systemActivityService.getActivityPage(pageReqDto, reqVO);
        return ServiceResult.succ(pageResp);
    }

    @ApiOperation(value = "友福利按活动/活动月度出账", httpMethod = "POST")
    @PostMapping("/yflActivityBillChecked")
    @SaCheckPermission("settleBill:customerBill:yflChecked")
    public ServiceResult<String> activityBillChecked(@RequestBody @Validated ActivityBillCheckedReqVo reqVo) {
        return ServiceResult.succ(settleShopBillService.activityBillChecked(reqVo));
    }

    @ApiOperation(value = "友福利补充出账", httpMethod = "GET")
    @GetMapping("/yflActivityBillAddChecked")
    @SaCheckPermission("settleBill:customerBill:yflCheckedAdd")
    public ServiceResult<String> yflActivityBillAddChecked(String activityCode) {
//        return ServiceResult.succ(settleShopBillService.yflActivityBillAddChecked(activityCode));
        return ServiceResult.succ();
    }

    @ApiOperation(value = "获取PDF基础数据", httpMethod = "GET")
    @GetMapping("/pdfData")
    public ServiceResult<BillPdfDataDto> getBillPdfData(Integer billId) {
        return ServiceResult.succ(settleShopBillService.getBillPdfData(billId));
    }

    @ApiOperation(value = "恢复账单数据，便于测试使用", httpMethod = "GET")
//    @GetMapping("/rollBackBill")
    public ServiceResult<String> rollBackBill() {
        return ServiceResult.succ(settleShopBillService.rollBackBill());
    }

    @ApiOperation(value = "修复电商计算逻辑数据",httpMethod = "POST")
    @PostMapping("/fixSupplierBill")
    public ServiceResult<?> fixSupplierBill(@RequestBody List<Long> ids){
        return ServiceResult.succ(settleShopBillService.fixSupplierBill(ids));
    }

    @ApiOperation(value = "将南方客户账单独立供应商账单剔除",httpMethod = "GET")
    @GetMapping("filterData")
    public ServiceResult<?> filterData(Long id){
        return ServiceResult.succ(settleShopBillService.filterData(id));
    }

    @ApiOperation(value = "一键推送电商账单")
    @PostMapping("pushSupplierBill")
    public ServiceResult<String> pushSupplierBill(@RequestBody @Validated PushSupplierBillDto pushSupplierBillDto) {
        return ServiceResult.succ(settleShopBillService.pushSupplierBill(pushSupplierBillDto));
    }

    @ApiOperation(value = "供应商账单付款完成")
    @GetMapping("supplierSettlement")
    public ServiceResult<String> supplierSettlement(String billSn){
        return ServiceResult.succ(settleShopBillService.supplierSettlement(billSn));
    }
}
