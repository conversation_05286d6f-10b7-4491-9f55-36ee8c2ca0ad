package com.ly.yph.api.goods.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.goods.controller.vo.ImportGoodsPoolFailVO;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolImportDownSkuDto;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolImportSkuDto;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolQueryDto;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolSave;
import com.ly.yph.api.goods.enums.GoodsLabelEnum;
import com.ly.yph.api.goods.service.DfmallGoodsPoolService;
import com.ly.yph.api.goods.vo.DfmallGoodsPoolVo;
import com.ly.yph.api.order.common.SpinnerWriteHandler;
import com.ly.yph.api.zone.entity.GoodsZoneClassEntity;
import com.ly.yph.api.zone.service.GoodsZoneClassService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.excel.util.ExcelUtils;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.StringHelper;
import com.ly.yph.core.util.ValidatorUtils;
import com.ly.yph.idempotent.core.annotation.Idempotent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 商品池(DfmallGoodsPool)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-19 14:57:33
 */
@RestController
@RequestMapping("goods/mallGoodsPool")
@Validated
@Log4j2
@Api(tags = "商品池管理")
@SaCheckLogin
public class DfmallGoodsPoolController {

    @Resource
    private DfmallGoodsPoolService dfmallGoodsPoolService;

    @Resource
    private ThreadPoolExecutor commonIoExecutors;

    @Resource
    private GoodsZoneClassService goodsZoneClassService;
    /**
     * 分页查询
     *
     * @param pageReq 分页数据
     * @param goodsPoolQueryDto 查询入参
     * @return 分页数据
     */
    @GetMapping("/queryPage")
    @ApiOperation("商品池分页查询")
    @SaCheckPermission("business:goodsPool:query")
    public ServiceResult<?> queryPage(PageReq pageReq,DfmallGoodsPoolQueryDto goodsPoolQueryDto) {
        return ServiceResult.succ(this.dfmallGoodsPoolService.queryPage(pageReq,goodsPoolQueryDto));
    }

    /**
     * 精简信息查询
     *
     * @param goodsPoolQueryDto 查询入参
     * @return 分页数据
     */
    @GetMapping("/querySimpleList")
    @ApiOperation("商品池分页查询")
    @SaCheckPermission("business:goodsPool:query")
    public ServiceResult<?> querySimpleList(DfmallGoodsPoolQueryDto goodsPoolQueryDto) {
        return ServiceResult.succ(this.dfmallGoodsPoolService.querySimpleList(goodsPoolQueryDto));
    }

    @GetMapping("/queryGoodsPoolById")
    @ApiOperation("查询单个商品池数据")
    @SaCheckPermission("business:goodsPool:query")
    public ServiceResult<DfmallGoodsPoolVo> queryGoodsPoolById(@RequestParam(value = "id")Long id) {
        return ServiceResult.succ(this.dfmallGoodsPoolService.queryGoodsPoolById(id));
    }


    /**
     * 商品池保存
     *
     * @param dfmallGoodsPool 商品池保存对象
     * @return 返回结果
     */
    @PostMapping("/save")
    @ApiOperation("商品池保存")
    @SaCheckPermission("business:goodsPool:update")
    public ServiceResult<?> save(@RequestParam(value = "dfmallGoodsPool")String dfmallGoodsPool,
                                 @RequestParam(value = "file",required = false) MultipartFile file) {
        DfmallGoodsPoolSave dfmallGoodsPoolSave = JSONObject.parseObject(dfmallGoodsPool,DfmallGoodsPoolSave.class);
        try {
            //校验参数
            ValidatorUtils.validateEntity(dfmallGoodsPoolSave, Default.class);
        }catch (Exception exception){
      throw new ParameterException(exception.getMessage(), exception);
        }

        dfmallGoodsPoolService.saveGoodsPool(dfmallGoodsPoolSave,file);
        return ServiceResult.succ();
    }

    /**
     * 商品池加入商品
     * @param goodsPoolId 商品池ID
     * @param goodsIds  商品ID
     * @return 返回结果
     */
    @PostMapping("/saveGoodsId")
    @ApiOperation("商品池加入商品")
    @SaCheckPermission("business:goodsPool:update")
    public ServiceResult<?> saveGoodsId(@RequestParam(value = "goodsPoolId")Long goodsPoolId,
                                 @RequestParam(value = "goodsId") String goodsIds) {
        if(StringHelper.IsEmptyOrNull(goodsIds)){
            throw new ParameterException("请选择商品");
        }
        dfmallGoodsPoolService.saveGoodsId(goodsPoolId,goodsIds);
        return ServiceResult.succ();
    }

    /**
     * 商品池导入模板下载
     *
     */
    @GetMapping("/downGoodsSkuTemplate")
    @ApiOperation("商品池导入模板下载")
    @SaCheckPermission("business:goodsPool:update")
    public void downGoodsSkuTemplate(HttpServletResponse response,@RequestParam(value = "zoneId",required = false)Long zoneId) throws IOException {
        // 手动创建导出 demo
        List<DfmallGoodsPoolImportSkuDto> list = Collections.singletonList(
                DfmallGoodsPoolImportSkuDto.builder().goodsSku("DSZKH00BTTALES796GW")
                        .goodsDesc("得力（deli） 无线鼠标（黑色），3738 单位：个")
                        .supplierName("震坤行工业超市（上海）有限公司")
                        .goodsLabel("平台审核")
                        .build()
        );
        List<String> goodsLabel = Arrays.stream(GoodsLabelEnum.values()).map(GoodsLabelEnum::getName).collect(Collectors.toList());

        List<Map<Integer, List<String>>> dropList = new ArrayList<>();
        HashMap<Integer, List<String>> integerListHashMap = new HashMap<>();
        // 3: 第四列  goodsLabel：下拉列表的值
        integerListHashMap.put(3, goodsLabel);
        if(Objects.nonNull(zoneId)){
            List<GoodsZoneClassEntity> classList = goodsZoneClassService.queryZoneClassByZoneId(zoneId);
            if(CollectionUtil.isNotEmpty(classList)){
                List<String> dictLabel = classList.stream().map(GoodsZoneClassEntity::getLabel).collect(Collectors.toList());
                integerListHashMap.put(4, dictLabel);
            }
        }
        dropList.add(integerListHashMap);
        ExcelUtils.write(response, "商品池商品导入模板.xls", "Sheet1", DfmallGoodsPoolImportSkuDto.class, list, new SpinnerWriteHandler(dropList));
    }

    /**
     * 商品池导入SKU
     *
     * @param file excel文件
     * @return 返回结果
     */
    @PostMapping("/importSku")
    @ApiOperation("商品池导入SKU")
    @SaCheckPermission("business:goodsPool:update")
    @DistributedLock(value = "import_sku_lock", key = "#goodsPoolId" ,waitLock = false,needThrow = true,throwMessage = "当前操作正在执行中,请耐心等待~")
    public ServiceResult<ImportGoodsPoolFailVO> importSku(@RequestParam(value = "file") MultipartFile file,
                                                          @RequestParam(value = "goodsPoolId")Long goodsPoolId,
                                                          @RequestParam(value = "zoneId",required = false)Long zoneId) {
        // 非平台级别的商品池，创建时必须选择上级商品池
        return ServiceResult.succ(dfmallGoodsPoolService.goodsPoolImportGoods(file,goodsPoolId,zoneId));
    }

    @PostMapping("/goodsPoolExport")
    @ApiOperation("商品池导出SKU")
    @SaCheckPermission("business:goodsPool:query")
    public ServiceResult<?> goodsPoolExport(@RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        dfmallGoodsPoolService.goodsPoolExport(goodsPoolId);
        return ServiceResult.succ();
    }

    /**
     * 商品池SKU下架模板
     *
     */
    @GetMapping("/poolSkuDownTemplate")
    @ApiOperation("商品池下架模板下载")
    @SaCheckPermission("business:goodsPool:update")
    public void poolSkuDownTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<DfmallGoodsPoolImportDownSkuDto> list = Collections.singletonList(
                DfmallGoodsPoolImportDownSkuDto.builder().goodsSku("DSZKH00BTTALES796GW")
                        .supplierName("震坤行工业超市（上海）有限公司")
                        .build()
        );

        ExcelUtils.write(response, "商品池商品下架模板.xls", "Sheet1", DfmallGoodsPoolImportDownSkuDto.class, list);
    }

    /**
     * 商品池导入下架SKU
     *
     * @param file excel文件
     * @return 返回结果
     */
    @PostMapping("/importDownSku")
    @ApiOperation("商品池导入下架SKU")
    @SaCheckPermission("business:goodsPool:update")
    @DistributedLock(value = "importDown_sku_lock", key = "#goodsPoolId" ,waitLock = false,needThrow = true,throwMessage = "当前操作正在执行中,请耐心等待~")
    public ServiceResult<ImportGoodsPoolFailVO> importDownSku(@RequestParam(value = "file") MultipartFile file,
                                                          @RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        // 非平台级别的商品池，创建时必须选择上级商品池
        return ServiceResult.succ(dfmallGoodsPoolService.importDownSku(file,goodsPoolId));
    }

    /**
     * 商品池更新索引
     *
     * @return 返回结果
     */
    @PostMapping("/goodsPoolUpdateIndex")
    @ApiOperation("商品池更新索引")
    @SaCheckPermission("business:goodsPool:update")
    @Idempotent()
    public ServiceResult<String> goodsPoolUpdateIndex(@RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        commonIoExecutors.execute(() -> dfmallGoodsPoolService.goodsPoolUpdateIndex(goodsPoolId));
        // 非平台级别的商品池，创建时必须选择上级商品池
        return ServiceResult.succ("商品池更新索引成功，请稍后查看");
    }

    /**
     * 商品池更新索引
     *
     * @return 返回结果
     */
    @PostMapping("/goodsTop100InPool")
    @ApiOperation("Top100商品入池")
    @SaIgnore
    public ServiceResult<?> goodsTop100InPool(@RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        dfmallGoodsPoolService.goodsTop100InPool(goodsPoolId);
        return ServiceResult.succ();
    }

    /**
     * 商品池更新索引
     *
     * @return 返回结果
     */
    @PostMapping("/goods519InPool")
    @ApiOperation("暑你清凉商品入池")
    @SaIgnore
    public ServiceResult<?> goods519InPool(@RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        dfmallGoodsPoolService.goods519InPool(goodsPoolId);
        return ServiceResult.succ();
    }

    /**
     * 指定商品池校验供应商上架状态
     *
     * @return 返回结果
     */
    @PostMapping("/goodsPoolCheckStatus")
    @ApiOperation("商品池校验供应商上架状态")
    @SaIgnore
    public ServiceResult<?> goodsPoolCheckStatus(@RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        dfmallGoodsPoolService.goodsPoolCheckStatus(goodsPoolId);
        return ServiceResult.succ();
    }

    /**
     * 定时任务：扶贫商品池商品设置专区子分类
     *
     * @return 返回结果
     */
    @PostMapping("/goodsPoolSetZoneClass")
    @ApiOperation("扶贫商品池商品设置专区子分类")
    @SaIgnore
    public ServiceResult<?> goodsPoolSetZoneClassJob(@RequestParam(value = "goodsPoolId")Long goodsPoolId) {
        dfmallGoodsPoolService.goodsPoolSetZoneClassJob(goodsPoolId);
        return ServiceResult.succ();
    }

    /**
     * 定时任务：友福利通用商品池入池
     *
     * @return 返回结果
     */
    @PostMapping("/yflGeneralPoolRuleJob")
    @ApiOperation("友福利通用商品池入池")
    public ServiceResult<?> yflGeneralPoolRuleJob(@RequestParam(value = "goodsPoolId")Long goodsPoolId,
                                                  @RequestParam(value = "excludePoolIds")String excludePoolIds) {
        List<Long> goodsIds = new ArrayList<>();
        if(!StringHelper.IsEmptyOrNull(excludePoolIds)){
            goodsIds = Arrays.stream(excludePoolIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        dfmallGoodsPoolService.yflGeneralPoolRuleJob(goodsPoolId,goodsIds);
        return ServiceResult.succ();
    }

}

