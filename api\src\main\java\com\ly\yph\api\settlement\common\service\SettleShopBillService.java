package com.ly.yph.api.settlement.common.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.bill.service.ShopBillDetailService;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.order.service.ShopReturnService;
import com.ly.yph.api.order.vo.ShopReturnVo;
import com.ly.yph.api.orderlifecycle.entity.OrderDetailPool;
import com.ly.yph.api.orderlifecycle.enums.LifeCycleNodeStatusEnum;
import com.ly.yph.api.orderlifecycle.enums.OrderSalesChannelEnum;
import com.ly.yph.api.orderlifecycle.service.OrderDetailPoolService;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoPoolService;
import com.ly.yph.api.organization.entity.SystemActivity;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemOrganizationPurchaseContractEntity;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.enums.OrganizationTypeEnum;
import com.ly.yph.api.organization.service.*;
import com.ly.yph.api.settlement.common.dto.bill.*;
import com.ly.yph.api.settlement.common.dto.settleBillPool.SettleBillPoolCheckOutDto;
import com.ly.yph.api.settlement.common.entity.*;
import com.ly.yph.api.settlement.common.enums.*;
import com.ly.yph.api.settlement.common.factory.SettleBillFactory;
import com.ly.yph.api.settlement.common.factory.SettleShopBillDetailStrategy;
import com.ly.yph.api.settlement.common.mapper.SettleBillPoolMapper;
import com.ly.yph.api.settlement.common.mapper.SettleShopBillMapper;
import com.ly.yph.api.settlement.common.vo.bill.*;
import com.ly.yph.api.settlement.supplier.dto.SupplierContractInfoDto;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.system.dto.OrderExportExcelVO;
import com.ly.yph.api.system.enums.ActProcTypeEnum;
import com.ly.yph.api.system.feign.ActTaskFeign;
import com.ly.yph.api.system.util.WorkflowUtils;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.SystemErrorCodeConstants;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.mapper.SystemDictDataMapper;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.util.JsonUtils;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettleShopBillService extends ServiceImpl<SettleShopBillMapper, SettleShopBill> {

    @Resource
    private SettleBillPoolMapper settleBillPoolMapper;
    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;
    @Resource
    private SettleShopBillMapper settleShopBillMapper;
    @Resource
    private SettleBillPoolService settleBillPoolService;
    @Resource
    private SystemOrganizationService systemOrganizationService;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private ReconciliationRuleService reconciliationRuleService;
    @Resource
    private SystemUsersService usersSrv;
    @Resource
    private SettleBillLifeCycleService settleBillLifeCycleService;
    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;
    @Resource
    private SettleBillOperateLogService settleBillOperateLogService;
    @Resource
    private SystemIntegralService systemIntegralService;
    @Resource
    private ShopPurchaseSubOrderService shopPurchaseSubOrderService;
    @Resource
    private ShopBillDetailService shopBillDetailService;
    @Resource
    private SystemActivityService systemActivityService;
    @Autowired
    YflYamlConfig yflYamlConfig;
    @Resource
    private SettleBillPoolYflCustomerService settleBillPoolYflCustomerService;
    @Resource
    private WorkflowUtils workflowUtils;
    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private SystemDictDataMapper systemDictDataMapper;
    @Resource
    private ActTaskFeign actTaskFeign;
    @Resource
    private SystemOrganizationPurchaseContractService systemOrganizationPurchaseContractService;
    @Resource
    private InvoiceDetailBillService invoiceDetailBillService;
    @Resource
    private MailService mailService;
    @Resource
    private BillDetailOutCheckedRelationService billDetailOutCheckedRelationService;
    @Resource
    private ShopReturnService shopReturnService;
    @Resource
    private SettleBillFactory settleBillFactory;
    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;
    @Resource
    private OrderDetailPoolService orderDetailPoolService;
    @Resource
    private DeliveryForBillProcessService deliveryForBillProcessService;


    public ServiceResult checkedBillBySingleReady(SettleBillPoolCheckOutDto settleBillPoolCheckOutDto) {
        Boolean isCompanyFlag = isCompany(settleBillPoolCheckOutDto.getCustomerType());
        if (isCompanyFlag) {
            //企业只生成东风商城的，不用分租户
            QueryWrapper<SystemOrganization> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SystemOrganization::getIsVirtual, 1).eq(SystemOrganization::getOrganizationType, OrganizationTypeEnum.PURCHASE.getCode())
                    .eq(SystemOrganization::getCode, settleBillPoolCheckOutDto.getCustomerCode());
            SystemOrganization systemOrganization = systemOrganizationService.getOne(queryWrapper);
            if (systemOrganization == null) {
                return ServiceResult.error("未查询到企业信息");
            }
        } else {
            //电商生成东风商城和友福利的，存在租户之分，在前端查询的接口就需要忽略租户，查询所有，以code分组
            TenantUtils.executeIgnore(() -> {
                QueryWrapper<ShopSupplier> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ShopSupplier::getSupplierCode, settleBillPoolCheckOutDto.getCustomerCode());
                List<ShopSupplier> shopSuppliers = shopSupplierService.list(queryWrapper);
                ShopSupplier shopSupplier = shopSuppliers.get(0);
                if ("srm".equals(shopSupplier.getDataSource())) {
                    settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.SOUTH_MALL.getCode());
                } else {
                    settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.STANDARD_MALL.getCode());
                }
            });

        }
        settleShopBillService.checkedBillBySingle(settleBillPoolCheckOutDto);
        return ServiceResult.succ("出账成功！");
    }

    @Transactional(rollbackFor = Exception.class)
    public void checkedBillBySingle(SettleBillPoolCheckOutDto settleBillPoolCheckOutDto) {
        //按条件查询账单池
        List<SettleBillPool> settleBillPools = settleBillPoolMapper.selectByCondition(settleBillPoolCheckOutDto);
        if (CollectionUtils.isEmpty(settleBillPools)) {
            throw new ParameterException("账单池中未查询到该客户的有效数据");
        }
        log.info("settleBillPools池个数:{}", settleBillPools.size());

        Boolean isCompanyFlag = isCompany(settleBillPoolCheckOutDto.getCustomerType());

        //南方企业账单，需要区分独立供应商/标准商城供应商
        if (isCompanyFlag) {
            Map<String, List<SettleBillPool>> listMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getSupplierDataSource));
            List<BillCompanyInfoDto> billCompanyInfoDtos = systemOrganizationPurchaseContractService.getBaseMapper().queryBillCompanyInfoDto(Collections.singletonList(settleBillPoolCheckOutDto.getCustomerCode()), 1L);
            CustomerSourceAndAreaTypeDto customerSourceAndAreaTypeDto = settleShopBillService.getCustomerSourceAndAreaTypeDto(billCompanyInfoDtos.get(0).getOrgRangMark());
            for (String supplierDataSource : listMap.keySet()) {

                settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(billCompanyInfoDtos.get(0).getCompanyCode(),
                        billCompanyInfoDtos.get(0).getCompanyName(),
                        settleBillPoolCheckOutDto.getYearAndMonth(),
                        Boolean.TRUE,
                        settleBillPoolCheckOutDto.getCustomerType(),
                        customerSourceAndAreaTypeDto.getCustomerSourceType(),
                        customerSourceAndAreaTypeDto.getBillAreaType(),
                        settleBillPoolCheckOutDto.getIsPlatformReconciliation(),
                        listMap.get(supplierDataSource)
                );
            }

        } else {
            settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(settleBillPoolCheckOutDto.getCustomerCode(),
                    settleBillPoolCheckOutDto.getCustomerName(),
                    settleBillPoolCheckOutDto.getYearAndMonth(),
                    Boolean.FALSE,
                    settleBillPoolCheckOutDto.getCustomerType(),
                    settleBillPoolCheckOutDto.getCustomerSourceType(),
                    BillAreaTypeEnum.CENTRAL_CHINA.getCode(),
                    settleBillPoolCheckOutDto.getIsPlatformReconciliation(),
                    settleBillPools);

        }


        //只有单个企业/供应商出账使用
        if (settleBillPoolCheckOutDto.getIsOutOtherBill()) {
            //同步只生成供应商侧的账单
            Map<String, List<SettleBillPool>> storeListMap = getStoreListMap(isCompanyFlag, settleBillPools);

            Map<String, SettleBillPoolCheckOutDto> storeInfo = getStoreCodeAndName(isCompanyFlag, settleBillPools, storeListMap.keySet());

            log.info("对侧客户有：{},对侧客户信息是：{}", storeListMap.keySet(), storeInfo);
            for (String storeCode : storeListMap.keySet()) {
                List<SettleBillPool> billPools = storeListMap.get(storeCode);
                SettleBillPoolCheckOutDto storeSettleBillPoolCheckOutDto = storeInfo.get(storeCode);

                Integer customerType = isCompanyFlag ? BillCustomerTypeEnum.SUPPLIER.getCode() : BillCustomerTypeEnum.COMPANY.getCode();
                List<SettleBillPool> validData = new ArrayList<>();
                if (isCompanyFlag) {
                    validData = billPools.stream().filter(e -> e.getSupplierOutAccountState() == 0).collect(Collectors.toList());
                } else {
                    validData = billPools.stream().filter(e -> e.getCompanyOutAccountState() == 0).collect(Collectors.toList());
                }

                if (CollectionUtils.isEmpty(validData)) {
                    log.info("对侧无有效池数据，storeCode:{}", storeCode);
                    return;
                }

                settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(storeCode,
                        storeSettleBillPoolCheckOutDto.getCustomerName(),
                        settleBillPoolCheckOutDto.getYearAndMonth(),
                        !isCompanyFlag,
                        customerType,
                        storeSettleBillPoolCheckOutDto.getCustomerSourceType(),
                        storeSettleBillPoolCheckOutDto.getBillArea(),
                        settleBillPoolCheckOutDto.getIsPlatformReconciliation(),
                        validData
                );

            }
        }

    }

    /**
     * 东风商城侧出账公用方法
     *
     * @param customerCode           客户编码
     * @param customerName           客户名称
     * @param yearAndMonth           出账周期
     * @param isCompanyFlag          企业/供应商标记 冗余 历史代码不想动
     * @param billCustomerType       企业/供应商
     * @param customerSourceType     客户来源
     * @param areaType               地区
     * @param platformReconciliation 平台内外对账
     * @param settleBillPoolList     账单池数据
     * @param str                    调整日志等
     */
    @Transactional(rollbackFor = Exception.class)
    public void processCheckOutBaseOnCompanyFlagAndPlatformFlag(String customerCode,
                                                                String customerName,
                                                                String yearAndMonth,
                                                                Boolean isCompanyFlag,
                                                                Integer billCustomerType,
                                                                Integer customerSourceType,
                                                                Integer areaType,
                                                                Integer platformReconciliation,
                                                                List<SettleBillPool> settleBillPoolList,
                                                                String... str) {
        SettleShopBill settleShopBill = getSettleShopBill(customerCode,
                customerName,
                billCustomerType,
                yearAndMonth,
                customerSourceType,
                areaType,
                BillTypeEnum.TO_BUSINESS.getCode(),
                platformReconciliation);
        log.info("获取账单：{}", JsonUtils.toJsonString(settleShopBill));
        if (str.length > 1) {
            settleShopBillService.checkedBill(settleShopBill, settleBillPoolList, isCompanyFlag, str);
        } else {
            settleShopBillService.checkedBill(settleShopBill, settleBillPoolList, isCompanyFlag);
        }
    }

    public Map<String, List<SettleBillPool>> getStoreListMap(Boolean isCompanyFlag, List<SettleBillPool> settleBillPools) {
        Map<String, List<SettleBillPool>> storeListMap = new HashMap<>(16);
        if (isCompanyFlag) {
            storeListMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getSupplierCode));
        } else {
            storeListMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getCompanyCode));
        }
        return storeListMap;
    }

    /**
     * 生成某个客户账单
     * 限标准商城使用
     *
     * @param settleShopBill  账单主体
     * @param settleBillPools 账单池明细
     * @param isCompanyFlag   企业or供应商
     * @param str             用于账单新增时记录日志，其他场景不传入值
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkedBill(SettleShopBill settleShopBill, List<SettleBillPool> settleBillPools, Boolean isCompanyFlag, String... str) {
        Map<Long, ReconciliationRule> companyReconciliationRuleMap = new HashMap<>(16);
        Map<String, SystemUsers> supplierReconciliationRuleMap = new HashMap<>(16);
        //邮费相关的
        Map<String, SettleBillPool> postageDetailMap = new HashMap<>(16);
        if (isCompanyFlag) {
            //获取企业的对账关系
            QueryWrapper<ReconciliationRule> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ReconciliationRule::getCompanyCode, settleShopBill.getCustomerCode())
                    .eq(ReconciliationRule::getRuleState, 1)
                    .in(ReconciliationRule::getApplyId, settleBillPools.stream().map(SettleBillPool::getApplyUserId).distinct().collect(Collectors.toList()));
            List<ReconciliationRule> reconciliationRules = reconciliationRuleService.list(queryWrapper);
            companyReconciliationRuleMap = reconciliationRules.stream().collect(Collectors.toMap(ReconciliationRule::getApplyId, Function.identity(), (key1, key2) -> key1));
            postageDetailMap = settleBillPools.stream().filter(e -> e.getCompanyPostage().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toMap(SettleBillPool::getOrderNumber, Function.identity(), (key1, key2) -> key1));

        } else {
            //获取供应商的对账人
            List<SystemUsers> reconciler = usersSrv.getUserByOrganizationCode(settleShopBill.getCustomerCode(), "Reconciler");
            supplierReconciliationRuleMap = reconciler.stream().collect(Collectors.toMap(SystemUsers::getEntityOrganizationCode, Function.identity(), (key1, key2) -> (key1)));
            postageDetailMap = settleBillPools.stream().filter(e -> e.getSupplierPostage().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toMap(SettleBillPool::getOrderNumber, Function.identity(), (key1, key2) -> key1));

        }

        List<SettleShopBillDetail> settleShopBillDetails = new ArrayList<>();
        List<Long> settleBillPoolIds = new ArrayList<>();


        //获取生命周期数据
        List<Long> billPoolIds = settleBillPools.stream().map(SettleBillPool::getId).distinct().collect(Collectors.toList());
        Integer poolType = getPoolType(settleShopBill);
        List<SettleBillLifeCycle> settleBillLifeCycles = settleBillLifeCycleService.getUpdateBillLifeCycleListByIds(billPoolIds, poolType);
        Map<Long, SettleBillLifeCycle> billLifeCycleMap = settleBillLifeCycles.stream().collect(Collectors.toMap(SettleBillLifeCycle::getSettleBillPoolId, Function.identity(), (key1, key2) -> key1));


        //获取企业的对账配置
        BillAutoReconciliationDto billAutoReconciliationDto = getCompanyAutoReconciliationDto(settleShopBill, isCompanyFlag);
        //获取外部企业运营对账人-用于外部数据出账时确认对账人
        SystemDictDataEntity outBillCompanyReconciliation = getOutBillCompanyReconciliation(isCompanyFlag, settleShopBill.getCustomerCode(),settleShopBill.getCustomerSourceType());

        log.info("查询企业：[{}],查询账单池数量：{},生命周期数量：{}", settleShopBill.getCustomerCode(), settleBillPools.size(), settleBillLifeCycles.size());
        log.info("企业类型：{},{}", isCompanyFlag, settleShopBill.getCustomerType());
        for (SettleBillPool settleBillPool : settleBillPools) {
            SettleShopBillDetail settleShopBillDetail = settleShopBillDetailService.getShopBillDetail(settleBillPool, settleShopBill, billLifeCycleMap);
            //指派对账人
            detailToReconciliationUser(settleShopBill.getCustomerType(), settleBillPool.getApplyUserId(), settleShopBill.getCustomerCode(),
                    settleShopBillDetail, companyReconciliationRuleMap, supplierReconciliationRuleMap, billLifeCycleMap,
                    outBillCompanyReconciliation,settleBillPool.getSapOrderType(),settleShopBill.getCustomerSourceType());
            //自动确认对账
            autoReconciliationBillDetail(billAutoReconciliationDto, settleBillPool, settleShopBillDetail, billLifeCycleMap, companyReconciliationRuleMap, outBillCompanyReconciliation, settleShopBill.getCustomerSourceType());

            settleShopBillDetails.add(settleShopBillDetail);
            settleBillPoolIds.add(settleBillPool.getId());
        }

        //计算邮费
        settleShopBillPostageDetailService.saveShopBillPostageDetail(settleShopBill, postageDetailMap);
        //保存明细
        settleShopBillDetailService.saveBatch(settleShopBillDetails);
        //更新整个账单数据
        settleShopBillService.updateShopBillMoney(settleShopBill);
        //更新账单池出账状态
        log.info("更新账单池入参settleBillPoolIds：{},customerType:{},customerCode:{}", settleBillPoolIds, settleShopBill.getCustomerType(), settleShopBill.getCustomerCode());
        settleBillPoolService.updateBillPoolStatus(settleBillPoolIds, settleShopBill.getCustomerType(), settleShopBill.getCustomerCode());
        //更新生命周期状态
        List<SettleBillLifeCycle> lifeCycles = billLifeCycleMap.entrySet().stream().map(item -> item.getValue()).collect(Collectors.toList());
        settleBillLifeCycleService.updateBatchById(lifeCycles);
        //更新订单明细生命周期
        purchaseOrderInfoPoolService.updateForBillOutAndReconciliation(settleShopBill,settleShopBillDetails);

        //记录日志
        if (str.length > 0) {
            settleBillOperateLogService.saveSettleBillOperateLog(settleShopBill, settleShopBillDetails, BillOperateLogTypeEnum.ADD_BILL.getCode(), str[0]);
        }


    }

    /**
     *  获取企业的外部数据对账人
     * @param isCompanyFlag 企业标记
     * @param customerCode 企业编码
     * @return
     */
    public SystemDictDataEntity getOutBillCompanyReconciliation(Boolean isCompanyFlag, String customerCode,Integer customerSourceType) {
        if(!isCompanyFlag){
            return null;
        }
        SystemDictDataEntity SystemDictDataEntity =new SystemDictDataEntity();
        if(CustomerSourceTypeEnum.NISSAN_MALL.getCode().equals(customerSourceType)){
           SystemDictDataEntity = systemDictDataMapper.selectByTypeAndLabel("out_bill_reconciliation", "3SM");
        }else {
            SystemDictDataEntity = systemDictDataMapper.selectByTypeAndLabel("out_bill_reconciliation", customerCode);
        }

        return SystemDictDataEntity;

    }

    /** 自动对账
     * @param billAutoReconciliationDto
     * @param settleBillPool
     * @param settleShopBillDetail
     * @param billLifeCycleMap
     * @param companyReconciliationRuleMap
     * @param outBillCompanyReconciliation
     * @param customerSourceType
     */
    public void autoReconciliationBillDetail(BillAutoReconciliationDto billAutoReconciliationDto,
                                             SettleBillPool settleBillPool,
                                             SettleShopBillDetail settleShopBillDetail,
                                             Map<Long, SettleBillLifeCycle> billLifeCycleMap,
                                             Map<Long, ReconciliationRule> companyReconciliationRuleMap,
                                             SystemDictDataEntity outBillCompanyReconciliation,
                                             Integer customerSourceType
    ) {
        if (!billAutoReconciliationDto.getIsCompanyFlag()) {
            //供应商 不用对账
            return;
        }
        if(CustomerSourceTypeEnum.NISSAN_MALL.getCode().equals(customerSourceType)){
            if (outBillCompanyReconciliation != null) {
                settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                settleShopBillDetail.setReconciliationConfirmUserName(settleBillPool.getOtherMallReconciliationName());
                settleShopBillDetail.setReconciliationConfirmUserId(Long.valueOf(outBillCompanyReconciliation.getValue()));
                settleShopBillDetail.setReconciliationConfirmTime(new Date());
                settleShopBillDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
                //更新生命周期
                SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                if (settleBillLifeCycle != null) {
                    settleBillLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                    settleBillLifeCycle.setCustomerReconciliationTime(new Date());
                }
            }

        }else {
            if (0 == settleBillPool.getSapOrderType() || settleBillPool.getSapOrderType() == 4) {
                //以商城对账关系
                if (BillAutoReconciliationEnum.NO_AUTO_RECONCILIATION.getCode().equals(billAutoReconciliationDto.getAutoFlag())) {
                    //需要对账 啥也不干
                    return;
                } else if (BillAutoReconciliationEnum.AUTO_RECONCILIATION.getCode().equals(billAutoReconciliationDto.getAutoFlag())) {
                    //自动对账,需要查询已维护的对账人关系去确认,明细上已有对账人了，可以直接变状态
                    if (settleShopBillDetail.getReconciliationUserId() == null) {
                        return;
                    }
                    //自动对账
                    ReconciliationRule reconciliationRule = companyReconciliationRuleMap.get(settleShopBillDetail.getApplyUserId());
                    settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                    settleShopBillDetail.setReconciliationConfirmUserName(reconciliationRule.getReconciliationEmpName());
                    settleShopBillDetail.setReconciliationConfirmUserId(reconciliationRule.getReconciliationUserId());
                    settleShopBillDetail.setReconciliationConfirmTime(new Date());
                    settleShopBillDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
                    //更新生命周期
                    SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                    if (settleBillLifeCycle != null) {
                        settleBillLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                        settleBillLifeCycle.setCustomerReconciliationTime(new Date());
                    }
                } else if (BillAutoReconciliationEnum.PARTIAL_RECONCILIATION.getCode().equals(billAutoReconciliationDto.getAutoFlag())) {
                    //部分对账,企业配置了自动化对账的对账人才可以完成自动确认
                    if (settleShopBillDetail.getReconciliationUserId() == null) {
                        return;
                    }
                    if (billAutoReconciliationDto.getReconciliationUserMap().isEmpty()) {
                        return;
                    }
                    SystemUsers systemUsers = billAutoReconciliationDto.getReconciliationUserMap().get(settleShopBillDetail.getReconciliationUserId());
                    if (systemUsers == null) {
                        return;
                    }
                    settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                    settleShopBillDetail.setReconciliationConfirmUserName(systemUsers.getNickname());
                    settleShopBillDetail.setReconciliationConfirmUserId(systemUsers.getId());
                    settleShopBillDetail.setReconciliationConfirmTime(new Date());
                    settleShopBillDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
                    //更新生命周期
                    SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                    if (settleBillLifeCycle != null) {
                        settleBillLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                        settleBillLifeCycle.setCustomerReconciliationTime(new Date());
                    }
                }
            } else {
                //外部对账数据 指派给企业运营
                if (outBillCompanyReconciliation != null) {
                    settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                    settleShopBillDetail.setReconciliationConfirmUserName(outBillCompanyReconciliation.getRemark());
                    settleShopBillDetail.setReconciliationConfirmUserId(Long.valueOf(outBillCompanyReconciliation.getValue()));
                    settleShopBillDetail.setReconciliationConfirmTime(new Date());
                    settleShopBillDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
                    //更新生命周期
                    SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                    if (settleBillLifeCycle != null) {
                        settleBillLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                        settleBillLifeCycle.setCustomerReconciliationTime(new Date());
                    }
                }
            }
        }


    }

    public BillAutoReconciliationDto getCompanyAutoReconciliationDto(SettleShopBill settleShopBill, Boolean isCompanyFlag) {
        BillAutoReconciliationDto billAutoReconciliationDto = new BillAutoReconciliationDto();
        if (!isCompanyFlag) {
            //供应商不用对账
            billAutoReconciliationDto.setIsCompanyFlag(Boolean.FALSE);
            return billAutoReconciliationDto;
        }
        SystemOrganizationPurchaseContractEntity purchaseContractEntity = systemOrganizationPurchaseContractService.getByOrgCode(settleShopBill.getCustomerCode());
        billAutoReconciliationDto.setIsCompanyFlag(Boolean.TRUE);
        billAutoReconciliationDto.setAutoFlag(purchaseContractEntity.getCompanyAutoReconciliationFlag());

        if (BillAutoReconciliationEnum.PARTIAL_RECONCILIATION.getCode().equals(purchaseContractEntity.getCompanyAutoReconciliationFlag())) {
            //查询部分自动对账的【对账人】
            List<SystemUsers> reconciliationUsers = reconciliationRuleService.getReconciliationRuleUser(settleShopBill.getCustomerCode(), "Reconcilier-DFS");
            Map<Long, SystemUsers> usersMap = reconciliationUsers.stream().collect(Collectors.toMap(SystemUsers::getId, Function.identity(), (key1, key2) -> key1));
            billAutoReconciliationDto.setReconciliationUserMap(usersMap);
        }

        return billAutoReconciliationDto;
    }

    private Map<String, Integer> getSrmPriceModeMap(SettleShopBill settleShopBill, List<SettleBillPool> settleBillPools) {
        if (!CustomerSourceTypeEnum.SOUTH_MALL.getCode().equals(settleShopBill.getCustomerSourceType())) {
            log.info("非南方账单，不进入计价模式");
            return new HashMap<>();
        }
        List<String> srmOrders = settleBillPools.stream().filter(c -> OrderSupplierDataSourceEnum.getSupplierDataSourceByCode(1).equals(c.getSupplierDataSource())).map(SettleBillPool::getOrderNumber).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(srmOrders)) {
            QueryWrapper<ShopPurchaseSubOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().select(ShopPurchaseSubOrder::getOrderNumber, ShopPurchaseSubOrder::getPricingMode).in(ShopPurchaseSubOrder::getOrderNumber, srmOrders);
            List<ShopPurchaseSubOrder> list = shopPurchaseSubOrderService.list(queryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                return new HashMap<>();
            } else {
                Map<String, Integer> priceModeMap = list.stream().collect(Collectors.toMap(ShopPurchaseSubOrder::getOrderNumber, ShopPurchaseSubOrder::getPricingMode, (key1, key2) -> key2));
                return priceModeMap;
            }
        }
        return new HashMap<>();
    }

    public Integer getPoolType(SettleShopBill settleShopBill) {
        if (settleShopBill.getCustomerSourceType().equals(CustomerSourceTypeEnum.YFL_MALL.getCode())
                && settleShopBill.getCustomerType().equals(BillCustomerTypeEnum.COMPANY.getCode())) {
            return PoolTypeEnum.YFLCOMPANY.getCode();
        } else {
            return PoolTypeEnum.DFSHOP.getCode();
        }
    }


    /**
     * 判断客户是否为企业/供应商
     *
     * @param customerType 客户类型
     * @return true 企业;false 供应商
     */
    public Boolean isCompany(Integer customerType) {
        if (BillCustomerTypeEnum.COMPANY.getCode().equals(customerType)) {
            return true;
        } else if (BillCustomerTypeEnum.SUPPLIER.getCode().equals(customerType)) {
            return false;
        } else {
            throw exception(SystemErrorCodeConstants.CUSTOMER_TYPE_IS_ERROR);
        }
    }

    /**
     *指派对账人
     * @param customerType 客户类型
     * @param applyUserId 下单人id
     * @param customerCode 客户编码
     * @param settleShopBillDetail 账单明细
     * @param companyReconciliationRuleMap 企业对账规则
     * @param supplierReconciliationRuleMap 供应商对账人
     * @param billLifeCycleMap 生命周期
     * @param outBillCompanyReconciliation 外部验收数据账单的对账人
     * @param sapOrderType 0，4 商城为准，其他外部数据
     * @param customerSourceType 商城类型 1:东风商城;2:友福利;4:3sm;
     */
    private void detailToReconciliationUser(Integer customerType,
                                            Long applyUserId,
                                            String customerCode,
                                            SettleShopBillDetail settleShopBillDetail,
                                            Map<Long, ReconciliationRule> companyReconciliationRuleMap,
                                            Map<String, SystemUsers> supplierReconciliationRuleMap,
                                            Map<Long, SettleBillLifeCycle> billLifeCycleMap,
                                            SystemDictDataEntity outBillCompanyReconciliation,
                                            Integer sapOrderType,
                                            Integer customerSourceType) {
        //客户
        if (BillCustomerTypeEnum.COMPANY.getCode().equals(customerType)) {
            if (!CustomerSourceTypeEnum.NISSAN_MALL.getCode().equals(customerSourceType)) {
                if (0 == sapOrderType || sapOrderType == 4) {
                    //以商城为准的
                    ReconciliationRule reconciliationRule = companyReconciliationRuleMap.get(applyUserId);
                    if (reconciliationRule != null) {
                        settleShopBillDetail.setReconciliationUserId(reconciliationRule.getReconciliationUserId());
                        settleShopBillDetail.setReconciliationUserName(reconciliationRule.getReconciliationEmpName());
                        settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode());

                        SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                        if (settleBillLifeCycle != null) {
                            settleBillLifeCycle.setCustomerReconciliationStatus(1);
                            settleBillLifeCycle.setCustomerReconciliationNum(settleShopBillDetail.getCheckedNum());
                        }
                    }
                } else {
                    if (outBillCompanyReconciliation != null) {
                        settleShopBillDetail.setReconciliationUserId(Long.parseLong(outBillCompanyReconciliation.getValue()));
                        settleShopBillDetail.setReconciliationUserName(outBillCompanyReconciliation.getRemark());
                        settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode());
                        SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                        if (settleBillLifeCycle != null) {
                            settleBillLifeCycle.setCustomerReconciliationStatus(1);
                            settleBillLifeCycle.setCustomerReconciliationNum(settleShopBillDetail.getCheckedNum());
                        }

                    }
                }
            } else {
                if (outBillCompanyReconciliation != null) {
                    settleShopBillDetail.setReconciliationUserId(Long.parseLong(outBillCompanyReconciliation.getValue()));
                    settleShopBillDetail.setReconciliationUserName(outBillCompanyReconciliation.getRemark());
                    settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode());
                    SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                    if (settleBillLifeCycle != null) {
                        settleBillLifeCycle.setCustomerReconciliationStatus(1);
                        settleBillLifeCycle.setCustomerReconciliationNum(settleShopBillDetail.getCheckedNum());
                    }

                }
            }

        } else {
            //电商-找对应电商的对账人关系,指派给电商对账人
            SystemUsers systemUsers = supplierReconciliationRuleMap.get(customerCode);
            if (systemUsers != null) {
                settleShopBillDetail.setReconciliationUserId(systemUsers.getId());
                settleShopBillDetail.setReconciliationUserName(systemUsers.getNickname());
                settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode());
                SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
                if (settleBillLifeCycle != null) {
                    settleBillLifeCycle.setSupplierReconciliationStatus(1);
                    settleBillLifeCycle.setSupplierReconciliationNum(settleShopBillDetail.getCheckedNum());
                }
            }
        }
    }

    /**
     * 获取客户账单主体
     *
     * @param customerCode       客户编码
     * @param customerName       客户名称
     * @param customerType       客户类型 0:企业 1:供应商
     * @param yearAndMonth       对账周期
     * @param customerSourceType 商城来源
     * @param areaType           区域类型
     * @param billType           账单类型
     * @param str                有福利客户账单传活动编号，其他客户不用
     * @return
     */
    public SettleShopBill getSettleShopBill(String customerCode, String customerName, Integer customerType, String yearAndMonth, Integer customerSourceType, Integer areaType, Integer billType, Integer isPlatformReconciliation, String... str) {
        String[] split = yearAndMonth.split("-");
        Integer year = Integer.valueOf(split[0]);
        Integer month = Integer.valueOf(split[1]);

        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getCustomerCode, customerCode)
                .eq(SettleShopBill::getCustomerType, customerType)
                .eq(SettleShopBill::getCheckYear, year)
                .eq(SettleShopBill::getCheckMonth, month)
                .eq(SettleShopBill::getCustomerSourceType, customerSourceType)
                .eq(SettleShopBill::getIsPlatformReconciliation, isPlatformReconciliation);
        if (str.length > 0) {
            queryWrapper.lambda().eq(SettleShopBill::getActivityCode, str[0]).eq(SettleShopBill::getBillCheckedType,str[1]);
        }
        SettleShopBill settleShopBill1 = getOne(queryWrapper);
        if (settleShopBill1 != null) {
            return settleShopBill1;
        }
        SettleShopBill settleShopBill = new SettleShopBill();

        String billSn = getBillSn(customerSourceType, year, month, customerCode, customerType, isPlatformReconciliation, str);
        settleShopBill.setBillSn(billSn);
        settleShopBill.setCustomerCode(customerCode);
        settleShopBill.setCustomerName(customerName == null ? customerCode : customerName);
        settleShopBill.setCustomerType(customerType);
        settleShopBill.setCustomerSourceType(customerSourceType);
        settleShopBill.setCheckYear(year);
        settleShopBill.setCheckMonth(month);
        settleShopBill.setAmountTax(BigDecimal.ZERO);
        settleShopBill.setAmount(BigDecimal.ZERO);
        settleShopBill.setPointAmount(BigDecimal.ZERO);
        settleShopBill.setIndividualPaymentAmount(BigDecimal.ZERO);
        settleShopBill.setPostage(BigDecimal.ZERO);
        settleShopBill.setTotalTaxAmount(BigDecimal.ZERO);
        settleShopBill.setToleranceAmount(BigDecimal.ZERO);
        settleShopBill.setSettlementAmount(BigDecimal.ZERO);
        settleShopBill.setBillStatus(BillStatusEnum.TO_BE_RECONCILED.getCode());
        settleShopBill.setBillApproveStatus(BillApproveStatusEnum.UNAPPROVED.getCode());
        settleShopBill.setAreaType(areaType);
        settleShopBill.setBillType(billType);
        settleShopBill.setEntityCustomerCode(customerCode);
        settleShopBill.setIsPlatformReconciliation(isPlatformReconciliation);
        settleShopBill.setIsPush(PlatformReconciliationEnum.OUTPLATFORMRECONCILIATION.getCode().equals(isPlatformReconciliation) ? 1 : 0);
        settleShopBill.setBillCheckedType(0);
        settleShopBill.setActivityName("");
        LoginUser user = LocalUserHolder.get();
        settleShopBill.setCreator(user != null ? user.getUsername() : "sys");
        if (str.length > 0) {
            settleShopBill.setActivityCode(str[0]);
            settleShopBill.setBillCheckedType(Integer.valueOf(str[1]));
            settleShopBill.setActivityName(str[2]);
        }
        settleShopBillMapper.insert(settleShopBill);
        return settleShopBill;
    }

    /**
     * 获取账单编号
     *
     * @param customerSourceType 客户来源
     * @param year               账单周期年
     * @param month              账单周期月
     * @param customerCode       客户编码
     * @param customerType       客户/供应商
     * @param str                有福利账单传入活动编码，其他不传
     * @return
     */
    private String getBillSn(Integer customerSourceType, Integer year, Integer month, String customerCode, Integer customerType, Integer isPlatformReconciliation, String... str) {
        String shortName = CustomerSourceTypeEnum.getCustomerSourceTypeShortNameByCode(customerSourceType);
        String yearMonth = DateUtils.getFirstDayOfMonth(year, month);
        log.info("shortName:{},yearMonth:{},customerCode:{},str:{},isPlatformReconciliation:{}", shortName, yearMonth, customerCode, str, isPlatformReconciliation);
        String billSn = "";
        if (CustomerSourceTypeEnum.YFL_MALL.getCode().equals(customerSourceType) && BillCustomerTypeEnum.COMPANY.getCode().equals(customerType)) {
            //有福利客户账单才加活动编号
            billSn = yearMonth + "-" + customerCode.toUpperCase(Locale.ROOT) + "-" + shortName + "-" + str[0] + "-" + str[1];
        } else {
            billSn = yearMonth + "-" + customerCode.toUpperCase(Locale.ROOT) + "-" + shortName;
        }
        if (isPlatformReconciliation == 1) {
            billSn = billSn + "-DF";
        } else {
            billSn = billSn + "-OUT";
        }
        log.info("billSn:{}", billSn);
        return billSn;
    }

    public Integer getNextMonth(Integer month) {
        month = month + 1;
        month = month > 12 ? 1 : month;
        return month;
    }

    public Integer getNextYear(Integer year, Integer month) {
        return month == 1 ? (year + 1) : year;
    }

    /**
     * 月度批量生成全部客户账单
     * 限标准商城使用
     *
     * @param yearAndMonth 账单出账周期
     * @return
     */
    public ServiceResult checkedBillAll(String yearAndMonth,Integer isPlatformReconciliation) {

        SettleBillPoolCheckOutDto settleBillPoolQueryDto = getCommonBillPoolQueryDto(yearAndMonth);
        settleBillPoolQueryDto.setIsPlatformReconciliation(isPlatformReconciliation);
        //查询有哪些企业和供应商要出账，针对性出账，不再遍历所有
        List<String> companyCodes = settleBillPoolService.getBaseMapper().getReadyCheckedBillCompany(settleBillPoolQueryDto.getCheckedEndTime());
        if (CollectionUtils.isNotEmpty(companyCodes)) {
            //生成客户账单
            log.info("准备生成的客户账单企业有：{}", companyCodes);
            QueryWrapper<SystemOrganization> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SystemOrganization::getIsVirtual, 1).eq(SystemOrganization::getOrganizationType, OrganizationTypeEnum.PURCHASE.getCode()).in(SystemOrganization::getCode, companyCodes);
            List<SystemOrganization> list = systemOrganizationService.list(queryWrapper);

            for (String companyCode : companyCodes) {

                settleBillPoolQueryDto.setCustomerType(BillCustomerTypeEnum.COMPANY.getCode());
                try {
                    log.info("出账企业 outBillCompanyCode:{},时间：{}", companyCode, DateUtils.format(new Date(), DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    checkedBillBySingle(settleBillPoolQueryDto);
                } catch (Exception e) {
                    log.info("企业出账异常：outBillCompanyCode:{}", companyCode );
                    log.error("企业出账异常", e);
                    continue;
                }
            }
        }
        //查询有哪些企业和供应商要出账，针对性出账，不再遍历所有
        List<String> supplierCodes = settleBillPoolService.getBaseMapper().getReadyCheckedBillSupplier(settleBillPoolQueryDto.getCheckedEndTime());
        if (CollectionUtils.isNotEmpty(supplierCodes)) {
            //生成供应商账单 需要忽略租户，生成标准商城和友福利的
            log.info("准备生成的客户供应商的企业有：{}", supplierCodes);
            Map<String, ShopSupplier> map = new HashMap<>();
            TenantUtils.executeIgnore(() -> {
                QueryWrapper<ShopSupplier> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().in(ShopSupplier::getSupplierCode, supplierCodes);
                List<ShopSupplier> list1 = shopSupplierService.list(queryWrapper);
                for (ShopSupplier shopSupplier : list1) {
                    map.put(shopSupplier.getSupplierCode(), shopSupplier);
                }
            });
            log.info("supplierMap:{}", map);
            for (String supplierCode : map.keySet()) {
                ShopSupplier supplier = map.get(supplierCode);
                settleBillPoolQueryDto.setCustomerCode(supplier.getSupplierCode());
                settleBillPoolQueryDto.setCustomerName(supplier.getSupplierFullName());
                settleBillPoolQueryDto.setCustomerType(BillCustomerTypeEnum.SUPPLIER.getCode());
                if ("srm".equals(supplier.getDataSource())) {
                    settleBillPoolQueryDto.setCustomerSourceType(CustomerSourceTypeEnum.SOUTH_MALL.getCode());
                } else {
                    settleBillPoolQueryDto.setCustomerSourceType(CustomerSourceTypeEnum.STANDARD_MALL.getCode());
                }
                try {
                    log.info("出账供应商outBillSupplierCode:{},供应商名称：{}，时间{}", supplier.getSupplierCode(), supplier.getSupplierFullName(), DateUtils.format(new Date(), DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                    checkedBillBySingle(settleBillPoolQueryDto);
                } catch (Exception e) {
                    log.info("供应商出账异常，outBillSupplierCode:{},名称{}", supplier.getSupplierCode(), supplier.getSupplierFullName());
                    log.error("供应商出账异常", e);
                    continue;
                }

            }
        }

        return ServiceResult.succ("月度批量账单生成完成！");
    }

    /**
     * 获取账单生成的查询条件公共对象
     *
     * @param yearAndMonth 账单周期
     * @return
     */
    private SettleBillPoolCheckOutDto getCommonBillPoolQueryDto(String yearAndMonth) {

        SettleBillPoolCheckOutDto settleBillPoolQueryDto = new SettleBillPoolCheckOutDto();

//        DateTime beginOfMonth = DateUtil.beginOfMonth(DateUtil.parse(yearAndMonth, "yyyy-MM"));
        DateTime endOfMonth = DateUtil.endOfMonth(DateUtil.parse(yearAndMonth, "yyyy-MM"));
//        String checkedStartTime = DateUtil.format(beginOfMonth, "yyyy-MM-dd HH:mm:ss");
        String checkedEndTime = DateUtil.format(endOfMonth, "yyyy-MM-dd HH:mm:ss");
        settleBillPoolQueryDto.setIsOutOtherBill(Boolean.FALSE);
//        settleBillPoolQueryDto.setCheckedStartTime(checkedStartTime);
        settleBillPoolQueryDto.setCheckedEndTime(checkedEndTime);
        settleBillPoolQueryDto.setYearAndMonth(yearAndMonth);
        return settleBillPoolQueryDto;

    }

    /**
     * 明细指派到已有账单中
     *
     * @param detailToBillDto 指派明细对象
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult detailsToBill(DetailToBillDto detailToBillDto) {
        log.info("detailToBillDto:{}", detailToBillDto);
        SettleShopBill settleShopBill = getById(detailToBillDto.getSettleShopBillId());
        if (settleShopBill == null) {
            return ServiceResult.error("未查询到账单相关数据！");
        }
        SettleShopBillDetailStrategy billDetailStrategy = settleBillFactory.getBillDetailStrategy(settleShopBill.getCustomerSourceType());

        return ServiceResult.succ(billDetailStrategy.poolToBill(detailToBillDto, settleShopBill));
    }

    /**
     * 分页查询账单
     *
     * @param pageReq 分页
     * @param reqVo   条件
     * @return
     */
    public PageResp<SettleShopBillVo> querySettleShopBillPage(PageReq pageReq, SettleShopBillPageReqVo reqVo) {
        IPage<SettleShopBillVo> shopBillVoIPage = this.getBaseMapper().querySettleShopBillPage(DataAdapter.adapterPageReq(pageReq), reqVo);
        return DataAdapter.adapterPage(shopBillVoIPage, SettleShopBillVo.class);
    }

    /**
     * 供应商查询账单
     *
     * @param pageReq
     * @param reqVo
     * @return
     */
    public PageResp<SettleShopBillVo> queryDfsSettleShopBillPage(PageReq pageReq, SettleShopBillPageReqVo reqVo) {
        LoginUser user = LocalUserHolder.get();
        log.info("独立供应商用户：{}", user);
        reqVo.setCustomerCode(user.getEntityOrganizationCode());
        reqVo.setCustomerType(BillCustomerTypeEnum.SUPPLIER.getCode());
        IPage<SettleShopBillVo> shopBillVoIPage = this.getBaseMapper().querySettleShopBillPage(DataAdapter.adapterPageReq(pageReq), reqVo);
        return DataAdapter.adapterPage(shopBillVoIPage, SettleShopBillVo.class);
    }

    /**
     * 条件查询
     *
     * @param reqVo
     * @return
     */
    public List<SettleShopBillVo> exportBillList(SettleShopBillPageReqVo reqVo) {
        List<SettleShopBillVo> list = settleShopBillMapper.querySettleShopBill(reqVo);
        return list;
    }

    /**
     * 供应商主账单列表导出
     *
     * @param reqVo
     * @return
     */
    public List<SupplierSettleShopBillVo> dfsExportBillList(SettleShopBillPageReqVo reqVo) {
        LoginUser user = LocalUserHolder.get();
        log.info("独立供应商用户：{}", user);
        reqVo.setCustomerCode(user.getOrganizationCode());
        reqVo.setCustomerType(BillCustomerTypeEnum.SUPPLIER.getCode());
        List<SettleShopBillVo> list = settleShopBillMapper.querySettleShopBill(reqVo);
        List<SupplierSettleShopBillVo> supplierSettleShopBillVos = DataAdapter.convertList(list, SupplierSettleShopBillVo.class);
        return supplierSettleShopBillVos;

    }

    /**
     * 删除账单明细
     *
     * @param detailRemoveFromBillDto dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult deleteDetail(DetailRemoveFromBillDto detailRemoveFromBillDto) {
        SettleShopBill shopBill = getById(detailRemoveFromBillDto.getBillId());
        if (shopBill == null) {
            return ServiceResult.error("未查询账单信息！");
        }

        SettleShopBillDetailStrategy billDetailStrategy = settleBillFactory.getBillDetailStrategy(shopBill.getCustomerSourceType());
        return ServiceResult.succ(billDetailStrategy.billDetailDelete(detailRemoveFromBillDto, shopBill));

    }

    /**
     * 校验要删除的账单明细
     * @param billId 账单id
     * @param detailIds  明细id集合
     * @return
     */
    public List<SettleShopBillDetail> checkDetailForDelete(Long billId, List<Long> detailIds) {
        QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(SettleShopBillDetail::getDetailId,
                SettleShopBillDetail::getSettleBillPoolId,
                SettleShopBillDetail::getBillId,
                SettleShopBillDetail::getBillSn,
                SettleShopBillDetail::getOrderNumber,
                SettleShopBillDetail::getGoodsCode,
                SettleShopBillDetail::getGoodsSku,
                SettleShopBillDetail::getCheckYear,
                SettleShopBillDetail::getCheckMonth,
                SettleShopBillDetail::getReconciliationStatus,
                SettleShopBillDetail::getPoolType,
                SettleShopBillDetail::getApplyUserId)
                .eq(SettleShopBillDetail::getBillId, billId)
                .in(SettleShopBillDetail::getDetailId, detailIds)
                .last(" for update");
        List<SettleShopBillDetail> shopBillDetails = settleShopBillDetailService.list(queryWrapper);
        if (CollectionUtils.isEmpty(shopBillDetails)) {
            throw new ParameterException("未查询到明细");
        }

        //校验状态，不是什么时候都可以删除的，已出账,待确认,已驳回，已确认，的才可以删除
        List<SettleShopBillDetail> collect = shopBillDetails.stream().filter(c -> !c.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode())
                && !c.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode())
                && !c.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_REJECT.getCode())
                && !c.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ParameterException("只允许删除[已出账],[待确认],[已驳回],[已确认]的明细");
        }

        return shopBillDetails;
    }

    /**
     * 同步删除对侧客户/供应商账单明细
     *
     * @param shopBill
     * @param billPoolIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteSynchronousOther(SettleShopBill shopBill, List<Long> billPoolIds, String deleteReason, Integer operateType) {

        QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleShopBillDetail::getSettleBillPoolId, billPoolIds).eq(SettleShopBillDetail::getStoreCode, shopBill.getCustomerCode())
                .eq(SettleShopBillDetail::getPoolType,PoolTypeEnum.DFSHOP.getCode())
                .in(SettleShopBillDetail::getReconciliationStatus,
                        ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode(),
                        ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode()
                        , ReconciliationStatusEnum.RECONCILIATION_BILL_REJECT.getCode()
                        , ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
        List<SettleShopBillDetail> settleShopBillDetails = settleShopBillDetailService.list(queryWrapper);

        if (CollectionUtils.isEmpty(settleShopBillDetails)) {
            return;
        }
        Map<Long, List<SettleShopBillDetail>> listMap = settleShopBillDetails.stream().collect(Collectors.groupingBy(SettleShopBillDetail::getBillId));
        for (Long billId : listMap.keySet()) {

            List<SettleShopBillDetail> valid = listMap.get(billId);
            SettleShopBill byId = getById(billId);

            List<Long> validPoolIds = billPoolIds.stream().filter(item -> {
                List<SettleShopBillDetail> collect = valid.stream().filter(c -> c.getSettleBillPoolId().equals(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    return true;
                } else {
                    return false;
                }
            }).collect(Collectors.toList());

            settleShopBillService.deleteFromBill(byId, valid, validPoolIds, deleteReason, operateType);
        }
    }

    /**
     * 重新计算账单金额
     *
     * @param shopBill
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateShopBillMoney(SettleShopBill shopBill) {
        Map<String, BigDecimal> sumBill = getBaseMapper().getSumBill(shopBill.getBillId());
        Map<String, BigDecimal> sumPostage = settleShopBillPostageDetailService.getBaseMapper().getSumPostage(shopBill.getBillId());
        log.info("bill_id:{},bill_Sn:{},sumBill:{},sumPostage:{}", shopBill.getBillId(), shopBill.getBillSn(), sumBill, sumPostage);
        SettleShopBill updateShopBill = new SettleShopBill();
        updateShopBill.setBillId(shopBill.getBillId());
        updateShopBill.setPostage(sumPostage == null ? BigDecimal.ZERO : sumPostage.get("postage"));
        updateShopBill.setAmount(sumBill == null ? BigDecimal.ZERO : sumBill.get("amount"));
        updateShopBill.setAmountTax(sumBill == null ? BigDecimal.ZERO : sumBill.get("amountTax"));
        if (CustomerSourceTypeEnum.YFL_MALL.getCode().equals(shopBill.getCustomerSourceType()) &&
                BillCustomerTypeEnum.COMPANY.getCode().equals(shopBill.getCustomerType())) {
            //查询积分金额
            Map<String, BigDecimal> sumIntegra = settleShopBillDetailService.getBaseMapper().getSumIntegral(shopBill.getBillId());
            log.info("sumIntegra:{}", sumIntegra);
            updateShopBill.setPointAmount(sumIntegra == null ? BigDecimal.ZERO : sumIntegra.get("goodsPayIntegral"));
            updateShopBill.setIndividualPaymentAmount(sumIntegra == null ? BigDecimal.ZERO : sumIntegra.get("goodsPayMoney"));
            updateShopBill.setTotalTaxAmount(updateShopBill.getPointAmount().add(updateShopBill.getPostage()));
        } else {
            updateShopBill.setTotalTaxAmount(updateShopBill.getPostage().add(updateShopBill.getAmountTax()));
        }

        //更新容差金额，结算金额
        updateShopBill.setToleranceAmount(shopBill.getToleranceAmount());
        updateShopBill.setSettlementAmount(updateShopBill.getToleranceAmount().add(updateShopBill.getTotalTaxAmount()));
        settleShopBillService.updateById(updateShopBill);
    }

    /**
     * 删除账单中的明细
     *
     * @param shopBill
     * @param shopBillDetails
     * @param billPoolIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteFromBill(SettleShopBill shopBill, List<SettleShopBillDetail> shopBillDetails, List<Long> billPoolIds, String deleteReason, Integer operateType) {

        Boolean company = isCompany(shopBill.getCustomerType());

        List<Long> detailIds = shopBillDetails.stream().map(SettleShopBillDetail::getDetailId).distinct().collect(Collectors.toList());

        //如果是企业的，并且是外部已经建立关系的，不允许删除或者同步删除
        if(company){
            QueryWrapper<BillDetailOutCheckedRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(BillDetailOutCheckedRelation::getDetailId,detailIds);
            List<BillDetailOutCheckedRelation> list = billDetailOutCheckedRelationService.list(queryWrapper);
            if(CollectionUtils.isNotEmpty(list)){
                log.info("list:{}",list);
                throw new ParameterException("存在以外部验收为准出账的数据,暂不允许直接删除，请联系管理员处理！");
            }
        } else {
            //如果是供应商的账单,则账单主体是已对账后的情况的数据，不允许再被删除
            if (!BillStatusEnum.TO_BE_RECONCILED.getCode().equals(shopBill.getBillStatus())
                    && BillApproveStatusEnum.APPROVED.getCode().equals(shopBill.getBillApproveStatus())) {

                if (BillOperateLogTypeEnum.DELETE_BILL.getCode().equals(operateType) ||
                        BillOperateLogTypeEnum.DELETE_BILL_ROLL.getCode().equals(operateType) ||
                        BillOperateLogTypeEnum.REVERT_BILL_POOL.getCode().equals(operateType)) {
                    throw new ParameterException("该供应商账单已完成对账和审批,不允许删除数据！");

                } else if (BillOperateLogTypeEnum.DELETE_BILL_SYNCHRONOUS.getCode().equals(operateType) ||
                        BillOperateLogTypeEnum.DELETE_BILL_SYNCHRONOUS_ROLL.getCode().equals(operateType) ||
                        BillOperateLogTypeEnum.REVERT_BILL_POOL_SYNCHRONOUS.getCode().equals(operateType)) {
                    throw new ParameterException("要删除的明细在对侧供应商账单：{}已完成对账，暂不允许同步删除明细数据!",
                            shopBill.getBillSn());
                }
            }
        }

        //处理对应订单邮费明细
        List<String> orderNumber = shopBillDetails.stream().map(SettleShopBillDetail::getOrderNumber).distinct().collect(Collectors.toList());
        QueryWrapper<SettleShopBillPostageDetail> postageDetailQueryWrapper = new QueryWrapper<>();
        postageDetailQueryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, shopBill.getBillId()).in(SettleShopBillPostageDetail::getOrderNumber, orderNumber);
        List<SettleShopBillPostageDetail> billPostageDetails = settleShopBillPostageDetailService.list(postageDetailQueryWrapper);

        if (CollectionUtils.isNotEmpty(billPostageDetails)) {
            //要删除的明细中存在有订单含有邮费
            List<String> billDetailPostageIdsDetail = new ArrayList<>();

            //判断这个订单的邮费是否保留，如果这个订单的明细全部删除了,此账单的该订单的邮费要剔除掉，否则保留继续
            List<String> postageOrderNumbers = billPostageDetails.stream().map(SettleShopBillPostageDetail::getOrderNumber).distinct().collect(Collectors.toList());

            //该账单中对应订单的所有明细
            QueryWrapper<SettleShopBillDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(SettleShopBillDetail::getBillId, shopBill.getBillId()).in(SettleShopBillDetail::getOrderNumber, postageOrderNumbers);
            List<SettleShopBillDetail> detailAndPostageList = settleShopBillDetailService.list(detailQueryWrapper);
            Map<String, List<SettleShopBillDetail>> listMap = detailAndPostageList.stream().collect(Collectors.groupingBy(SettleShopBillDetail::getOrderNumber));
            //要删除的订单明细按订单号分组
            Map<String, List<SettleShopBillDetail>> deleteMap = shopBillDetails.stream().collect(Collectors.groupingBy(SettleShopBillDetail::getOrderNumber));
            for (String orderSn : listMap.keySet()) {
                List<SettleShopBillDetail> settleShopBillDetails = listMap.get(orderSn);
                List<SettleShopBillDetail> deleteList = deleteMap.get(orderSn);
                if (deleteList.size() >= settleShopBillDetails.size()) {
                    //要删除的大于或者等于该订单的明细-->该订单全删了，邮费一起删除
                    billDetailPostageIdsDetail.add(orderSn);
                }
            }

            //删除邮费明细
            if (CollectionUtils.isNotEmpty(billDetailPostageIdsDetail)) {
                QueryWrapper<SettleShopBillPostageDetail> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.lambda().eq(SettleShopBillPostageDetail::getBillId, shopBill.getBillId()).in(SettleShopBillPostageDetail::getOrderNumber, billDetailPostageIdsDetail);
                settleShopBillPostageDetailService.remove(queryWrapper1);
            }
        }

        //【删除 滚动删除 回退账单池】更新订单生命周期
        purchaseOrderInfoPoolService.updateForBillDetailDelete(shopBill, detailIds, PoolTypeEnum.DFSHOP.getCode(), OrderSalesChannelEnum.DFMALL.getCode());

        //删除明细
        settleShopBillDetailService.removeBatchByIds(detailIds);

        //修改账单池状态,生命周期状态
        settleShopBillDetailService.updateSettleBillPoolsStatus(billPoolIds, company, operateType);

        //重新计算账单金额
        settleShopBillService.updateShopBillMoney(shopBill);

        //记录操作日志
        settleBillOperateLogService.saveSettleBillOperateLog(shopBill, shopBillDetails, operateType, deleteReason);

        //滚动删除，实时滚动至次月
        if (BillOperateLogTypeEnum.DELETE_BILL_ROLL.getCode().equals(operateType) ||
                BillOperateLogTypeEnum.DELETE_BILL_SYNCHRONOUS_ROLL.getCode().equals(operateType)) {
            settleShopBillService.deleteBillRollCheckedOut(shopBill, billPoolIds);
        }

    }

    /**
     * 滚动的账单明细 实时生成账单
     *
     * @param shopBill
     * @param billPoolIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBillRollCheckedOut(SettleShopBill shopBill, List<Long> billPoolIds) {
        Integer nextMonth = getNextMonth(shopBill.getCheckMonth());
        Integer nextYear = getNextYear(shopBill.getCheckYear(), nextMonth);
        String yearAndMonth = nextYear + "-" + nextMonth;
        //查询账单池id是否是未出账的数据，防止并发
        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleBillPool::getId, billPoolIds);
        if (isCompany(shopBill.getCustomerType())) {
            queryWrapper.lambda().eq(SettleBillPool::getCompanyOutAccountState, 0);
        } else {
            queryWrapper.lambda().eq(SettleBillPool::getSupplierOutAccountState, 0);
        }
        List<SettleBillPool> settleBillPoolList = settleBillPoolService.list(queryWrapper);
        if(CollectionUtils.isEmpty(settleBillPoolList)){
            log.info("滚动删除客户：{}，准备滚动至账单：{},账单池明细查询为空！",shopBill.getCustomerCode(),shopBill.getBillSn());
            return;
        }
        log.info("滚动要生成的账单明细池id是：{}", settleBillPoolList);

        settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(shopBill.getCustomerCode(),
                shopBill.getCustomerName(),
                yearAndMonth,
                isCompany(shopBill.getCustomerType()),
                shopBill.getCustomerType(),
                shopBill.getCustomerSourceType(),
                shopBill.getAreaType(),
                shopBill.getIsPlatformReconciliation(),
                settleBillPoolList);

    }

    /**
     * 获取对侧供应商/企业的编码-名称
     *
     * @param isCompanyFlag
     * @param settleBillPools
     * @return
     */
    public Map<String, SettleBillPoolCheckOutDto> getStoreCodeAndName(Boolean isCompanyFlag, List<SettleBillPool> settleBillPools, Set<String> storeCodes) {

        Map<String, SettleBillPoolCheckOutDto> map = new HashMap<>();
        if (isCompanyFlag) {
            QueryWrapper<ShopSupplier> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ShopSupplier::getSupplierCode, storeCodes);
            List<ShopSupplier> list = shopSupplierService.list(queryWrapper);

            list.forEach(e -> {
                SettleBillPoolCheckOutDto settleBillPoolCheckOutDto = new SettleBillPoolCheckOutDto();
                if ("srm".equals(e.getDataSource())) {
                    settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.SOUTH_MALL.getCode());
                    settleBillPoolCheckOutDto.setBillArea(BillAreaTypeEnum.SOUTH_CHINA.getCode());

                } else {
                    settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.STANDARD_MALL.getCode());
                    settleBillPoolCheckOutDto.setBillArea(BillAreaTypeEnum.CENTRAL_CHINA.getCode());

                }
                settleBillPoolCheckOutDto.setCustomerCode(e.getSupplierCode());
                settleBillPoolCheckOutDto.setCustomerName(e.getSupplierFullName());
                map.put(e.getSupplierCode(), settleBillPoolCheckOutDto);
            });
        } else {
            QueryWrapper<SystemOrganization> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SystemOrganization::getCode, storeCodes).eq(SystemOrganization::getIsVirtual, 1).eq(SystemOrganization::getOrganizationType, OrganizationTypeEnum.PURCHASE.getCode());
            List<SystemOrganization> list = systemOrganizationService.list(queryWrapper);

            list.stream().forEach(e -> {
                SettleBillPoolCheckOutDto settleBillPoolCheckOutDto = new SettleBillPoolCheckOutDto();
                if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                    settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.YFL_MALL.getCode());
                } else {
                    if ("DFS".equals(e.getOrgRangeMark())) {
                        settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.SOUTH_MALL.getCode());
                        settleBillPoolCheckOutDto.setBillArea(BillAreaTypeEnum.SOUTH_CHINA.getCode());
                    } else if("3SM".equalsIgnoreCase(e.getOrgRangeMark())){
                        settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.NISSAN_MALL.getCode());
                        settleBillPoolCheckOutDto.setBillArea(BillAreaTypeEnum.SOUTH_CHINA.getCode());
                    } else {
                        settleBillPoolCheckOutDto.setCustomerSourceType(CustomerSourceTypeEnum.STANDARD_MALL.getCode());
                        settleBillPoolCheckOutDto.setBillArea(BillAreaTypeEnum.CENTRAL_CHINA.getCode());
                    }
                }
                settleBillPoolCheckOutDto.setCustomerCode(e.getCode());
                settleBillPoolCheckOutDto.setCustomerName(e.getName());
                map.put(e.getCode(), settleBillPoolCheckOutDto);
            });
        }
        log.info("storeListMap:{}", map);
        return map;
    }

    public SettleShopBillExcelVo getBillExcelById(Long billId) {
        return this.getBaseMapper().getBillExcelById(billId);
    }

    /**
     * 友福利出账
     *
     * @param reqVo
     * @return
     */
    @Transactional
    public String activityBillChecked(ActivityBillCheckedReqVo reqVo) {

        log.info("活动出账请求参数:{},租户为：{}", reqVo, TenantContextHolder.getRequiredTenantId());

        //查询活动信息
        QueryWrapper<SystemActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SystemActivity::getActivityCode, reqVo.getActivityCode());
        SystemActivity systemActivity = systemActivityService.getOne(queryWrapper);

        if (systemActivity == null) {
            throw new ParameterException("未查询到活动信息");
        }
        reqVo.setActivityId(systemActivity.getId());

        QueryWrapper<SettleShopBill> shopBillQueryWrapper = new QueryWrapper<>();
        shopBillQueryWrapper.lambda().eq(SettleShopBill::getActivityCode, reqVo.getActivityCode());

        if (0 == systemActivity.getBillType()) {
            if (reqVo.getYear() == null || reqVo.getMonth() == null || reqVo.getCheckedStartTime() == null || reqVo.getCheckedEndTime() == null) {
                throw new ParameterException("月度出账关键参数不能为空！");
            }
            shopBillQueryWrapper.lambda().eq(SettleShopBill::getCheckYear, reqVo.getYear()).eq(SettleShopBill::getCheckMonth, reqVo.getMonth()).eq(SettleShopBill::getBillCheckedType, 0);
            reqVo.setBillCheckedType(0);
        } else if (1 == systemActivity.getBillType()) {
            shopBillQueryWrapper.lambda().eq(SettleShopBill::getBillCheckedType, 1);
            reqVo.setBillCheckedType(1);
        } else {
            throw new ParameterException("活动出账类型参数异常，请联系管理员维护！");
        }

        SettleShopBill one = getOne(shopBillQueryWrapper);
        if (one != null) {
            String billTypeName = systemActivity.getBillType().equals(1) ? "活动维度出账" : "月度出账";
            throw new ParameterException("该活动已出账,出账方式[" + billTypeName + "],请勿重复出账！");
        }
        QueryWrapper<SystemOrganization> organizationQueryWrapper = new QueryWrapper<>();
        organizationQueryWrapper.lambda().eq(SystemOrganization::getCode, systemActivity.getCompanyCode());
        SystemOrganization systemOrganization = systemOrganizationService.getOne(organizationQueryWrapper);
        if (systemOrganization == null) {
            throw new ParameterException("活动企业信息有误！");
        }

        //获取账单池数据
        List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList = settleBillPoolYflCustomerService.getBaseMapper().checkBillForYfl(reqVo);
        if(CollectionUtil.isEmpty(settleBillPoolYflCustomerList)){
            throw new ParameterException("暂无可出账数据！");
        }

        //获取成员及积分情况
        List<ActivityUserInfoDto> activityUserInfoDtoList = systemIntegralService.getBaseMapper().getUserInfoByActivityId(systemActivity.getId(),null);
        if(CollectionUtil.isEmpty(activityUserInfoDtoList)){
            throw new ParameterException("该活动下的成员信息异常");
        }

        //生产有福利活动账单
        settleShopBillService.checkedBillOfYfl(systemActivity, settleBillPoolYflCustomerList, activityUserInfoDtoList, systemOrganization, reqVo);


        return "活动账单生成成功！";
    }
    public void filterReturnedFreight(List<OrderExportExcelVO> orderExportExcelVOS) {
        List<String> orderList = com.ly.yph.core.util.CollectionUtils.convertList(orderExportExcelVOS, OrderExportExcelVO::getOrderNumber);
        if (CollectionUtil.isEmpty(orderList))return;
        List<ShopReturnVo> list = shopReturnService.queryReturnNumByOrder(orderList);
        if(CollectionUtils.isEmpty(list))return;
        Map<String, OrderExportExcelVO> map = orderExportExcelVOS.stream().collect(Collectors.toMap(OrderExportExcelVO::getOrderNumber, Function.identity()));
        list.forEach(e-> {
            String orderNumber = e.getOrderNumber();
            OrderExportExcelVO orderExportExcelVO = map.get(orderNumber);
            BigDecimal orderSubsidyFreight = orderExportExcelVO.getOrderSubsidyFreight();
            BigDecimal orderFreightPrice = orderExportExcelVO.getOrderFreightPrice();
            if (orderExportExcelVO.getFreightType()==1 && BigDecimal.ZERO.compareTo(e.getReturnFreight())<0 && e.getReturnFreight().compareTo(orderSubsidyFreight.add(orderFreightPrice))<0){
                //发邮件
                log.info("售后含有现金邮费操作账单通知, 订单号{}",orderNumber);
                List<SystemDictDataEntity> afterSaleEmails = systemDictDataMapper.selectListByDictType("after_sale_emails");
                List<String> emails = afterSaleEmails.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());
                mailService.sendEmail(MailService.BILL_FREIGHT_MONEY_REMIND,
                        mailService.billFreightMoneyRemindTemplate(orderNumber,"")
                        , emails, null);
            }
            if (orderSubsidyFreight.compareTo(BigDecimal.ZERO) > 0 || orderFreightPrice.compareTo(BigDecimal.ZERO) == 0 || e.getReturnFreightState() == 0)
                return;
            orderExportExcelVO.setOrderFreightPrice(orderFreightPrice.subtract(e.getReturnFreight()));
            log.info("update_orderS_freight,orderNumber:{},OrderFreightPrice:{} ", orderNumber, orderExportExcelVO.getOrderFreightPrice());
        });
    }

    /**
     * 友福利出账
     * @param systemActivity 活动信息
     * @param settleBillPoolYflCustomerList 可出账池数据
     * @param activityUserInfoDtoList 活动成员信息
     * @param systemOrganization 企业组织信息
     * @param reqVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkedBillOfYfl(SystemActivity systemActivity,
                                 List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList,
                                 List<ActivityUserInfoDto> activityUserInfoDtoList,
                                 SystemOrganization systemOrganization,
                                 ActivityBillCheckedReqVo reqVo) {
        String yearMonth;
        if (reqVo.getBillCheckedType() == 0) {
            yearMonth = reqVo.getYear() + "-" + reqVo.getMonth();
        } else {
            yearMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);
        }

        SettleShopBill settleShopBill = getSettleShopBill(systemActivity.getCompanyCode(), systemOrganization.getName(), BillCustomerTypeEnum.COMPANY.getCode(), yearMonth,
                CustomerSourceTypeEnum.YFL_MALL.getCode(), BillAreaTypeEnum.CENTRAL_CHINA.getCode(), BillTypeEnum.TO_CONSUMER.getCode(),
                PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode(), systemActivity.getActivityCode(), reqVo.getBillCheckedType().toString(), systemActivity.getActivityName());


        //保存有福利账单明细数据
        settleShopBillDetailService.saveYflBillDetail(settleShopBill, settleBillPoolYflCustomerList, activityUserInfoDtoList);

        //更新账单金额
        settleShopBillService.updateShopBillMoney(settleShopBill);

        //更新活动出账状态
        if (1 == reqVo.getBillCheckedType()) {
            shopBillDetailService.updateActivityChargeOffState(systemActivity.getActivityCode());
        }

    }

    /**
     * 账单对账
     *
     * @param reconciliationBillReqVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult reconciliationBill(ReconciliationBillReqVo reconciliationBillReqVo) {
        log.info("reconciliationBillReqVo：{}", reconciliationBillReqVo);
        SettleShopBill settleShopBill = getById(reconciliationBillReqVo.getBillId());
        if (settleShopBill == null) {
            throw exception(SystemErrorCodeConstants.SETTLE_SHOP_BILL_ERROR);
        }

        String billStatusName = BillStatusEnum.getBillStatusNameByCode(reconciliationBillReqVo.getBillStatus());
        if (StringUtils.isBlank(billStatusName)) {
            throw exception(SystemErrorCodeConstants.SETTLE_BILL_STATUS_ERROR);
        }
        LoginUser loginUser = LocalUserHolder.get();

        Integer billStatus = getNextBillApproveStatus(reconciliationBillReqVo.getBillStatus());
        SettleShopBill updateShopBill = new SettleShopBill();
        updateShopBill.setBillStatus(billStatus);
        updateShopBill.setBillId(settleShopBill.getBillId());
        if (billStatus.equals(BillStatusEnum.RECONCILED.getCode())) {
            //调用审批流，开启审批
            LoginUser user = LocalUserHolder.get();
            Map<String, Object> variables = Collections.emptyMap();
            String businessKey = workflowUtils.startWorkflowNoLogin(ActProcTypeEnum.BILL_APPROVAL,
                    settleShopBill.getBillSn(), user.getUsername(), user.getNickname(), "", user.getTenantId(), variables);
            log.info("settleBillId:{},settleBillSn:{},businessKey:{}", settleShopBill.getBillId(), settleShopBill.getBillSn(), businessKey);
            actTaskFeign.completeTaskByBusinessKey(businessKey, 1, "", "");
            updateShopBill.setBillApproveStatus(BillApproveStatusEnum.IN_APPROVAL.getCode());
        }
        updateShopBill.setUpdateTime(new Date());
        updateShopBill.setModifier(loginUser.getUsername());
        updateShopBill.setRemark(reconciliationBillReqVo.getRemark());
        if (reconciliationBillReqVo.getToleranceAmount() != null) {
            updateShopBill.setToleranceAmount(reconciliationBillReqVo.getToleranceAmount());
            updateShopBill.setSettlementAmount(settleShopBill.getTotalTaxAmount().add(reconciliationBillReqVo.getToleranceAmount()));
        }

        settleShopBillService.updateById(updateShopBill);
        return ServiceResult.succ("账单更新成功！");
    }

    /**
     * 获取下一步对账状态
     *
     * @param billStatus
     * @return
     */
    public Integer getNextBillApproveStatus(Integer billStatus) {
        if (BillStatusEnum.TO_BE_RECONCILED.getCode().equals(billStatus)) {
            return BillStatusEnum.RECONCILED.getCode();
        } else if (BillStatusEnum.RECONCILED.getCode().equals(billStatus)) {
            return BillStatusEnum.TO_BE_INVOICED.getCode();
        } else if (BillStatusEnum.TO_BE_INVOICED.getCode().equals(billStatus)) {
            return BillStatusEnum.TO_BE_SETTLED.getCode();
        } else if (BillStatusEnum.TO_BE_SETTLED.getCode().equals(billStatus)) {
            return BillStatusEnum.SETTLED.getCode();
        } else {
            throw exception(SystemErrorCodeConstants.SETTLE_BILL_STATUS_ERROR);
        }
    }




    public BillPdfDataDto getBillPdfData(Integer billId) {
        SettleShopBill settleShopBill = getById(billId);
        if (settleShopBill == null) {
            throw exception(SystemErrorCodeConstants.SETTLE_SHOP_BILL_ERROR);
        }

        if (BillCustomerTypeEnum.COMPANY.getCode().equals(settleShopBill.getCustomerType())) {
            throw new ParameterException("客户账单暂不支持生产PDF验收单！");
        }

        BillPdfDataDto billPdfDataDto = new BillPdfDataDto();
        billPdfDataDto.setBillSn(settleShopBill.getBillSn());
        String begin = DateUtils.format(DateUtils.getBeginDayOfMonth(settleShopBill.getCheckYear(), settleShopBill.getCheckMonth()), DateUtils.DATE_CHINESE_PATTERN);
        String end = DateUtils.format(DateUtils.getEndDayOfMonth(settleShopBill.getCheckYear(), settleShopBill.getCheckMonth()), DateUtils.DATE_CHINESE_PATTERN);
        String[] time = end.split("年");
        billPdfDataDto.setBillDate(begin + "-" + time[1]);

        //填充金额
        billPdfDataDto.setTotalTaxAmount(settleShopBill.getTotalTaxAmount());
        billPdfDataDto.setToleranceAmount(settleShopBill.getToleranceAmount());
        billPdfDataDto.setSettlementAmount(settleShopBill.getSettlementAmount());

        //填充审批人
        List<SystemDictDataEntity> billApproveRole = systemDictDataMapper.selectListByDictType("bill_approve_role");
        Map<String, List<SystemDictDataEntity>> map = billApproveRole.stream().collect(Collectors.groupingBy(SystemDictDataEntity::getValue));
        billPdfDataDto.setAcceptor(map.get("acceptor").get(0).getRemark());
        billPdfDataDto.setOperationer(map.get("operationer").get(0).getRemark());
        billPdfDataDto.setDepartmenter(map.get("departmenter").get(0).getRemark());

        //填充合同信息
        List<SupplierContractInfoDto> systemContractEntityList = settleShopBillService.getBaseMapper().getSupplierContract(settleShopBill.getCustomerCode());
        if (CollectionUtil.isEmpty(systemContractEntityList)) {
            throw new ParameterException("供应商合同信息未查询到,请联系管理员维护！");
        }
        billPdfDataDto.setContractName(systemContractEntityList.get(0).getContractName());
        billPdfDataDto.setContractNo(systemContractEntityList.get(0).getContractCode());
        billPdfDataDto.setSupplierName(settleShopBill.getCustomerName());

        BigDecimal amountNaked = settleShopBill.getAmount();
        if (settleShopBill.getPostage().compareTo(BigDecimal.ZERO) > 0) {

            BigDecimal postageNaked = settleShopBill.getPostage().divide(new BigDecimal("1.06"),2, RoundingMode.HALF_UP);
            amountNaked = amountNaked.add(postageNaked);
        }
        billPdfDataDto.setAmountNaked(amountNaked);

        //验收时间，取审批结束时间
        billPdfDataDto.setEndTime(DateUtils.format(settleShopBill.getBillApprovedTime()==null ?new Date()
                :settleShopBill.getBillApprovedTime(), "yyyy年MM月"));

        //设置商城类型
        String mallName = getMallType(settleShopBill.getCustomerCode());
        billPdfDataDto.setMallName(mallName);

        return billPdfDataDto;
    }


    private String getMallType(String customerCode) {
        QueryWrapper<SystemOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SystemOrganization::getIsVirtual, 1)
                .eq(SystemOrganization::getCode, customerCode)
                .eq(SystemOrganization::getOrgRangeMark, "3SM")
                .eq(SystemOrganization::getOrganizationType, 1);
        SystemOrganization one = systemOrganizationService.getOne(queryWrapper);

        if (one != null) {
            return "3SM商城";
        }

        return "东风商城";
    }

    /**
     * 账单审批修改账单状态
     *
     * @param billSn
     * @param code
     */
    public void approveUpdateStatus(String billSn, Integer code) {
        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getBillSn, billSn);
        SettleShopBill settleShopBill = settleShopBillService.getOne(queryWrapper);
        if (settleShopBill == null) {
            log.info("审批未查询到账单数据入参：{},{}", billSn, code);
            return;
        }
        LoginUser loginUser = LocalUserHolder.get();
        SettleShopBill updateBill = new SettleShopBill();
        updateBill.setBillId(settleShopBill.getBillId());
        updateBill.setUpdateTime(new Date());
        updateBill.setModifier(loginUser.getUsername());
        if (BillApproveStatusEnum.APPROVED.getCode().equals(code)) {
            updateBill.setBillApproveStatus(code);
            updateBill.setBillApprovedTime(new Date());

            if(BillCustomerTypeEnum.SUPPLIER.getCode().equals(settleShopBill.getCustomerType()) &&
                    PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode().equals(settleShopBill.getIsPlatformReconciliation())){
                updateBill.setPayFlag(1);
            }
        } else if (BillApproveStatusEnum.UNAPPROVED.getCode().equals(code)) {
            updateBill.setBillStatus(BillStatusEnum.TO_BE_RECONCILED.getCode());
            updateBill.setBillApproveStatus(code);
        } else {
            log.info("审批状态有误：{}，{}", billSn, code);
        }
        settleShopBillService.updateById(updateBill);
    }

    public String rollBackBill() {
//        this.getBaseMapper().rollBackBill();
        List<SettleBillPool> settleBillPools = settleBillPoolService.list();
        List<SettleBillLifeCycle> lifeCycles = new ArrayList<>();

        for (SettleBillPool billPool : settleBillPools) {
            SettleBillLifeCycle lifeCycle = new SettleBillLifeCycle();
            lifeCycle.setSettleBillPoolId(billPool.getId());
            lifeCycle.setPurchaseNumber(billPool.getPurchaseNumber());
            lifeCycle.setOrderNumber(billPool.getOrderNumber());
            lifeCycle.setSupplierOrderNumber(billPool.getSupplierOrderNumber());
            lifeCycle.setApplyUserName(billPool.getApplyUserName());
            lifeCycle.setGoodsCode(billPool.getGoodsCode());
            lifeCycle.setGoodsSku(billPool.getGoodsSku());
            lifeCycle.setCompanyCode(billPool.getCompanyCode());
            lifeCycle.setCompanyName(billPool.getCompanyName());
            lifeCycle.setSupplierCode(billPool.getSupplierCode());
            lifeCycle.setSupplierName(billPool.getSupplierName());
            lifeCycle.setPoolType(PoolTypeEnum.DFSHOP.getCode());
            lifeCycles.add(lifeCycle);
        }
        settleBillLifeCycleService.saveBatch(lifeCycles);
        return "账单数据重置成功！";
    }

    /**
     * 按配置企业实时出账来出账
     *
     * @param settleBillPools
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDfsBill(List<SettleBillPool> settleBillPools) {
        log.info("ready asytoDfsBill:{},time:{}", settleBillPools.size(), DateUtils.format(new Date()));
        //按租户区分 商城侧实时出账 要区分南方和标准商城的判断，友福利侧只用出电商的账单 不用出客户的账单
        //客户侧要考虑是否能实时出电商的账单，还要保证月初电商账单不能重复出
        Map<Long, List<SettleBillPool>> tenantMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getTenantId));
        TenantUtils.execute(new ArrayList<>(tenantMap.keySet()), () -> {
            settleShopBillService.realTimeCheckedOutBill(TenantContextHolder.getTenantId(), tenantMap.get(TenantContextHolder.getTenantId()));
        });
    }

    /**
     * 按租户生成不同账单 友福利只生成电商账单，商城要客户电商同步生成
     *
     * @param tenantId        租户
     * @param settleBillPools 池数据
     */
    @Transactional
    public void realTimeCheckedOutBill(Long tenantId, List<SettleBillPool> settleBillPools) {
        if (CollectionUtils.isEmpty(settleBillPools)) {
            log.info("实时生产账单时，传入账单池数据为空！");
            return;
        }

        String yearAndMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);

        if (yflYamlConfig.getTenantId().equals(tenantId)) {
            //友福利电商实时出电商账单
            settleShopBillService.realTimeCheckSupplierBill(settleBillPools, tenantId, yearAndMonth);

        } else {
            //标准商城&南方商城出账,同步出电商的
            List<SettleBillPool> validData = settleBillPools.stream().filter(c -> c.getCheckedNum().compareTo(BigDecimal.ZERO) != 0 && c.getCompanyOutAccountState() == 0).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(validData)){
                log.info("账单池出账数量为0，暂不出账，明细为：{}",JsonUtils.toJsonString(validData));
                return;
            }
            List<String> companyCodes = validData.stream().map(SettleBillPool::getCompanyCode).distinct().collect(Collectors.toList());
            //指定租户去查询
            List<BillCompanyInfoDto> billCompanyInfoDtos = systemOrganizationPurchaseContractService.getBaseMapper().queryBillCompanyInfoDto(companyCodes, tenantId);

            if (CollectionUtil.isEmpty(billCompanyInfoDtos)) {
                log.info("实时生成企业账单时，查询企业信息为空,入参companyCodes：{}", companyCodes);
                return;
            }

            Map<String, BillCompanyInfoDto> companyInfoDtoMap = billCompanyInfoDtos.stream().collect(Collectors.toMap(BillCompanyInfoDto::getCompanyCode, Function.identity(), (key1, key2) -> key1));

            Map<String, List<SettleBillPool>> companyListMap = validData.stream().collect(Collectors.groupingBy(SettleBillPool::getCompanyCode));
            //按企业生成账单
            for (String companyCode : companyListMap.keySet()) {
                List<SettleBillPool> billPools = companyListMap.get(companyCode);
                if (CollectionUtil.isEmpty(billPools)) {
                    log.info("实时生成账单，企业数据为空，企业编码为：{}", companyCode);
                    continue;
                }

                BillCompanyInfoDto billCompanyInfoDto = companyInfoDtoMap.get(companyCode);
                if (billCompanyInfoDto == null) {
                    log.info("实时生成账单，企业信息查询为空！企业编码为：{}", companyCode);
                    continue;
                }

                if (billCompanyInfoDto.getRealTimeCheckedFlag() == 0) {
                    //企业未开启实时出账
                    log.info("企业：{}未开启实时出账", companyCode);
                    continue;
                }

                CustomerSourceAndAreaTypeDto customerSourceAndAreaTypeDto = getCustomerSourceAndAreaTypeDto(billCompanyInfoDto.getOrgRangMark());

                settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(billCompanyInfoDto.getCompanyCode(),
                        billCompanyInfoDto.getCompanyName(),
                        yearAndMonth,
                        Boolean.TRUE,
                        BillCustomerTypeEnum.COMPANY.getCode(),
                        customerSourceAndAreaTypeDto.getCustomerSourceType(),
                        customerSourceAndAreaTypeDto.getBillAreaType(),
                        PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode(),
                        billPools);

                //同步生成供应商账单
                settleShopBillService.realTimeCheckSupplierBill(billPools, tenantId, yearAndMonth);
            }
        }
    }

    public CustomerSourceAndAreaTypeDto getCustomerSourceAndAreaTypeDto(String orgRangMark) {
        CustomerSourceAndAreaTypeDto customerSourceAndAreaTypeDto = new CustomerSourceAndAreaTypeDto();
        if ("DFS".equalsIgnoreCase(orgRangMark)) {
            customerSourceAndAreaTypeDto.setCustomerSourceType(CustomerSourceTypeEnum.SOUTH_MALL.getCode());
            customerSourceAndAreaTypeDto.setBillAreaType(BillAreaTypeEnum.SOUTH_CHINA.getCode());
        } else if ("3SM".equalsIgnoreCase(orgRangMark)) {
            customerSourceAndAreaTypeDto.setCustomerSourceType(CustomerSourceTypeEnum.NISSAN_MALL.getCode());
            customerSourceAndAreaTypeDto.setBillAreaType(BillAreaTypeEnum.SOUTH_CHINA.getCode());
        } else {
            customerSourceAndAreaTypeDto.setCustomerSourceType(CustomerSourceTypeEnum.STANDARD_MALL.getCode());
            customerSourceAndAreaTypeDto.setBillAreaType(BillAreaTypeEnum.CENTRAL_CHINA.getCode());
        }
        return customerSourceAndAreaTypeDto;
    }

    /**
     * 实时生成电商侧账单
     * @param supplierSettleBillPools 池数据
     * @param tenantId  租户
     * @param yearAndMonth 年月周期
     */
    public void realTimeCheckSupplierBill(List<SettleBillPool> supplierSettleBillPools, Long tenantId, String yearAndMonth) {
        List<SettleBillPool> settleBillPools = supplierSettleBillPools.stream().filter(c -> c.getSupplierOutAccountState() == 0 && c.getSupplierCheckedNum().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(settleBillPools)){
            log.info("电商侧账单已全部出账了，不用实时出账了");
            return;
        }
        //过滤售后邮费的池数据
        settleShopBillDetailService.filterAfterSalePostage(settleBillPools);
        List<String> supplierCodes = settleBillPools.stream().map(SettleBillPool::getSupplierCode).distinct().collect(Collectors.toList());
        //指定租户去查询
        List<ShopSupplier> supplierList = shopSupplierService.getBaseMapper().queryBillSupplierInfo(supplierCodes, tenantId);
        if (CollectionUtil.isEmpty(supplierList)) {
            log.info("实时生成账单，查询供应商信息为空，入参codes:{},租户tanantId:{}", supplierCodes, tenantId);
            return;
        }
        Map<String, ShopSupplier> supplierMap = supplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity(), (key1, key2) -> key1));

        Map<String, List<SettleBillPool>> supplierPoolsMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getSupplierCode));
        for (String supplierCode : supplierPoolsMap.keySet()) {
            ShopSupplier supplier = supplierMap.get(supplierCode);
            if (supplier == null) {
                log.info("实时生成电商账单，未发现电商信息，电商编码为：{}", supplierCode);
                continue;
            }
            List<SettleBillPool> billPoolList = supplierPoolsMap.get(supplierCode);

            //南方供应商不再实时出账 实时出账的 只有标准商城的平台内的 平台外的在确认订单即出账供应商账单了
            settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(supplierCode,
                    supplier.getSupplierFullName(),
                    yearAndMonth,
                    Boolean.FALSE,
                    BillCustomerTypeEnum.SUPPLIER.getCode(),
                    CustomerSourceTypeEnum.STANDARD_MALL.getCode(),
                    BillAreaTypeEnum.CENTRAL_CHINA.getCode(),
                    PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode(),billPoolList);
        }
    }

    /**
     * 修复客户账单金额逻辑问题
     *
     * @param ids
     * @return
     */
    @Transactional
    public String fixSupplierBill(List<Long> ids) {
        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getCustomerType, BillCustomerTypeEnum.SUPPLIER.getCode())
                .in(SettleShopBill::getBillId, ids);
        List<SettleShopBill> list = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            throw new ParameterException("未查询到账单信息");
        }

        for (SettleShopBill settleShopBill : list) {
            QueryWrapper<SettleShopBillDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(SettleShopBillDetail::getBillId, settleShopBill.getBillId());
            List<SettleShopBillDetail> settleShopBillDetails = settleShopBillDetailService.list(detailQueryWrapper);

            List<SettleShopBillDetail> updateShopBillDetails = new ArrayList<>();

            if (settleShopBill.getIsPlatformReconciliation() == 1) {
                //平台内对账，使用统一逻辑，单价*数量
                settleShopBillDetails.forEach(item -> {
                    SettleShopBillDetail updateShopBillDetail = new SettleShopBillDetail();
                    updateShopBillDetail.setDetailId(item.getDetailId());
                    updateShopBillDetail.setTotalPriceTax(item.getUnitPriceTax().multiply(item.getCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    updateShopBillDetails.add(updateShopBillDetail);

                });
            } else {
                //南方独立供应商，计算逻辑不一致 0,1 ->未税单价*税率*数量=含税总价;2 ->单价*数量
                settleShopBillDetails.forEach(item -> {
                    SettleShopBillDetail updateShopBillDetail = new SettleShopBillDetail();
                    if (item.getPriceMode() == 2) {
                        updateShopBillDetail.setTotalPriceTax(item.getUnitPriceTax().multiply(item.getCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    updateShopBillDetails.add(updateShopBillDetail);

                });

            }

            settleShopBillDetailService.updateBatchById(updateShopBillDetails);
            settleShopBillService.updateShopBillMoney(settleShopBill);

        }

        return "电商账单数据修复成功!";
    }

    @Transactional
    public String filterData(Long id) {
        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getBillId, id).eq(SettleShopBill::getCustomerSourceType, CustomerSourceTypeEnum.SOUTH_MALL.getCode())
                .eq(SettleShopBill::getCustomerType, BillCustomerTypeEnum.COMPANY.getCode());
        SettleShopBill shopBill = settleShopBillService.getOne(queryWrapper);
        if (shopBill == null) {
            throw new ParameterException("未查询到账单信息！");
        }

        QueryWrapper<SettleShopBillDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.lambda().eq(SettleShopBillDetail::getBillId, shopBill.getBillId()).eq(SettleShopBillDetail::getStoreDataSource, "srm");
        List<SettleShopBillDetail> settleShopBillDetails = settleShopBillDetailService.list(detailQueryWrapper);

        if (CollectionUtils.isEmpty(settleShopBillDetails)) {
            throw new ParameterException("该账单无独立供应商数据，无需拆分！");
        }

        //创建新账单
        String yearAndMonth = shopBill.getCheckYear() + "-" + shopBill.getCheckMonth();
        SettleShopBill settleShopBill = settleShopBillService.getSettleShopBill(shopBill.getCustomerCode(),
                shopBill.getCustomerName(), shopBill.getCustomerType(), yearAndMonth, shopBill.getCustomerSourceType(),
                shopBill.getAreaType(), shopBill.getBillType(),
                PlatformReconciliationEnum.OUTPLATFORMRECONCILIATION.getCode());
        log.info("新帐单：{}", settleShopBill);
        List<SettleShopBillDetail> updateBillDetails = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (SettleShopBillDetail settleShopBillDetail : settleShopBillDetails) {
            SettleShopBillDetail updateDetail = new SettleShopBillDetail();

            updateDetail.setDetailId(settleShopBillDetail.getDetailId());
            updateDetail.setBillSn(settleShopBill.getBillSn());
            updateDetail.setBillId(settleShopBill.getBillId());

            ids.add(settleShopBillDetail.getDetailId());
            updateBillDetails.add(updateDetail);
        }

        //更新明细到新帐单中
        settleShopBillDetailService.updateBatchById(updateBillDetails);
        //更新新旧账单
        settleShopBillService.updateShopBillMoney(settleShopBill);
        settleShopBillService.updateShopBillMoney(shopBill);

        //查询是否存在开票明细
        QueryWrapper<InvoiceDetailBill> invoiceDetailBillQueryWrapper = new QueryWrapper<>();
        invoiceDetailBillQueryWrapper.lambda().in(InvoiceDetailBill::getBillDetailId, ids);
        List<InvoiceDetailBill> list = invoiceDetailBillService.list(invoiceDetailBillQueryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            for (InvoiceDetailBill invoiceDetailBill : list) {
                invoiceDetailBill.setBillId(settleShopBill.getBillId());
                invoiceDetailBill.setBillSn(settleShopBill.getBillSn());
            }
            invoiceDetailBillService.updateBatchById(list);
        }

        return "过滤修复南方独立供应客户账单成功！";
    }

    @Transactional(rollbackFor = Exception.class)
    public String pushSupplierBill(PushSupplierBillDto pushSupplierBillDto) {
        SettleShopBill settleShopBill = this.getById(pushSupplierBillDto.getBillId());
        if (settleShopBill == null) {
            throw new ParameterException("未查询到账单信息！");
        }

        if (!BillCustomerTypeEnum.SUPPLIER.getCode().equals(settleShopBill.getCustomerType())) {
            throw new ParameterException("非供应商账单，暂不支持推送！");
        }

        if(StringUtils.isBlank(pushSupplierBillDto.getSupplierOperatorEmails())){
            throw new ParameterException("供应商运营邮箱不能为空！");
        }

        SettleShopBill updateShopBill = new SettleShopBill();
        updateShopBill.setBillId(settleShopBill.getBillId());
        updateShopBill.setIsPush(1);
        settleShopBillService.updateById(updateShopBill);

        String begin = DateUtils.format(DateUtils.getBeginDayOfMonth(settleShopBill.getCheckYear(), settleShopBill.getCheckMonth()), DateUtils.DATE_CHINESE_PATTERN);
        String end = DateUtils.format(DateUtils.getEndDayOfMonth(settleShopBill.getCheckYear(), settleShopBill.getCheckMonth()), DateUtils.DATE_CHINESE_PATTERN);

        String supplierBillTemplate = mailService.supplierBillTemplate(settleShopBill.getCustomerName(), settleShopBill.getCheckYear(), settleShopBill.getCheckMonth(), begin, end, settleShopBill.getTotalTaxAmount(), settleShopBill.getBillSn());
        List<String> sendTo =Arrays.asList(pushSupplierBillDto.getSupplierOperatorEmails().split(","))  ;


        //运营&研发抄送邮箱
        List<SystemDictDataEntity> supplierBillPushCc = systemDictDataMapper.selectListByDictType("supplierBill_push_cc");
        List<String> emailsCc = supplierBillPushCc.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());
        //供应商的抄送人
        if(StringUtils.isNotEmpty(pushSupplierBillDto.getSupplierCcEmails())){
           List<String> supplierCcEmailList = new ArrayList<>(Arrays.asList(pushSupplierBillDto.getSupplierCcEmails().split(",")));
            emailsCc.addAll(supplierCcEmailList);
        }

        mailService.sendEmail(MailService.PURCHASE_SUPPLIER_BILL, supplierBillTemplate, sendTo, emailsCc);
        return "账单推送成功！";
    }

    @Transactional
    public void checkedBillOfOutData(List<PoolAndOutDataRelationDto> poolAndOutDataRelationDtos, String yearAndMonth) {
        if (StringUtils.isBlank(yearAndMonth)) {
            yearAndMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);
        }
        log.info("匹配出账的年月：{}",yearAndMonth);
        List<Long> validBillPoolIds = poolAndOutDataRelationDtos.stream().map(PoolAndOutDataRelationDto::getBillPoolId).distinct().collect(Collectors.toList());
        List<SettleBillPool> settleBillPools = settleBillPoolService.listByIds(validBillPoolIds);

        List<String> companyCodes = settleBillPools.stream().map(SettleBillPool::getCompanyCode).distinct().collect(Collectors.toList());
        //指定租户去查询
        List<BillCompanyInfoDto> billCompanyInfoDtos = systemOrganizationPurchaseContractService.getBaseMapper().queryBillCompanyInfoDto(companyCodes, 1L);

        if (CollectionUtil.isEmpty(billCompanyInfoDtos)) {
            log.info("实时生成企业账单时，查询企业信息为空,入参companyCodes：{}", companyCodes);
            return;
        }

        Map<String, BillCompanyInfoDto> companyInfoDtoMap = billCompanyInfoDtos.stream().collect(Collectors.toMap(BillCompanyInfoDto::getCompanyCode, Function.identity(), (key1, key2) -> key1));

        Map<String, List<SettleBillPool>> companyListMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getCompanyCode));

        //按企业生成账单
        for (String companyCode : companyListMap.keySet()) {
            List<SettleBillPool> billPools = companyListMap.get(companyCode);
            if (CollectionUtil.isEmpty(billPools)) {
                log.info("实时生成账单，企业数据为空，企业编码为：{}", companyCode);
                continue;
            }

            BillCompanyInfoDto billCompanyInfoDto = companyInfoDtoMap.get(companyCode);
            if (billCompanyInfoDto == null) {
                log.info("实时生成账单，企业信息查询为空！企业编码为：{}", companyCode);
                continue;
            }
            //外部匹配出账 目前只有电商账单 都是平台内的 后续有变动 这里再改
            settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(billCompanyInfoDto.getCompanyCode(),
                    billCompanyInfoDto.getCompanyName(), yearAndMonth, Boolean.TRUE, BillCustomerTypeEnum.COMPANY.getCode(),
                    "DFS".equalsIgnoreCase(billCompanyInfoDto.getOrgRangMark()) ? CustomerSourceTypeEnum.SOUTH_MALL.getCode() : CustomerSourceTypeEnum.STANDARD_MALL.getCode(),
                    "DFS".equalsIgnoreCase(billCompanyInfoDto.getOrgRangMark()) ? BillAreaTypeEnum.SOUTH_CHINA.getCode() : BillAreaTypeEnum.CENTRAL_CHINA.getCode(),
                    PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode(),
                    billPools);
            // 供应商的账单 新包裹的需要供应商上传签收单才能出账
            deliveryForBillProcessService.outBillMatchProcess(billPools,yearAndMonth);

        }
    }

    @Transactional
    public String supplierSettlement(String billSn) {
        log.info("供应商账单：[{}]付款完成，准备修复对应的订单明细生命周期数据,操作人工号：{},昵称：{}", billSn,
                LocalUserHolder.get() != null ? LocalUserHolder.get().getUsername() : "支付计划调用",
                LocalUserHolder.get() != null ? LocalUserHolder.get().getNickname() : "支付计划调用");
        QueryWrapper<SettleShopBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBill::getBillSn, billSn)
                .eq(SettleShopBill::getCustomerType, BillCustomerTypeEnum.SUPPLIER.getCode())
                .eq(SettleShopBill::getIsPlatformReconciliation,1);
        SettleShopBill one = settleShopBillService.getOne(queryWrapper);
        if (one == null) {
            throw new ParameterException("查询数据异常！");
        }

        Integer checkYear = one.getCheckYear();
        if (checkYear < 2024) {
            throw new ParameterException("2024年前数据暂不支持系统录入！");
        }

        if (!one.getBillStatus().equals(BillStatusEnum.RECONCILED.getCode())) {
            throw new ParameterException("供应商账单不满足付款条件！");
        }

        if(!one.getBillApproveStatus().equals(BillApproveStatusEnum.APPROVED.getCode())){
            throw new ParameterException("供应商账单未审批完成！");
        }

        List<SupplierSettlementDto> supplierSettlementDtoList = settleShopBillService.getBaseMapper().getSupplierSettlementList(one.getBillId());
        if (CollectionUtils.isEmpty(supplierSettlementDtoList)) {
            throw new ParameterException("未查询到账单明细数据");
        }

        List<SupplierSettlementDto> haveSettleList = supplierSettlementDtoList.stream().filter(e -> e.getBillMatchReason().equals("supplierSettle")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(haveSettleList)) {
            throw new ParameterException("该供应商账单存在已付款数据！");
        }

        Map<Integer, List<SupplierSettlementDto>> orderSalesChannelMap = supplierSettlementDtoList.stream().collect(Collectors.groupingBy(SupplierSettlementDto::getOrderSalesChannel));
        List<OrderDetailPool> orderDetailPoolForUpdateList = new ArrayList<>();
        List<Long> billDetailIdList = new ArrayList<>();

        for (Integer orderSalesChannel : orderSalesChannelMap.keySet()) {

            List<SupplierSettlementDto> supplierSettlementDtos = orderSalesChannelMap.get(orderSalesChannel);
            List<String> orderDetailIdList = supplierSettlementDtos.stream().map(SupplierSettlementDto::getOrderDetailId).distinct().collect(Collectors.toList());

            List<OrderDetailPool> detailPoolList = new ArrayList<>();
            Lists.partition(orderDetailIdList, 1000).forEach(batch -> {
                        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
                        detailPoolQueryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, orderSalesChannel)
                                .in(OrderDetailPool::getOrderDetailId, batch)
                                .select(OrderDetailPool::getId,
                                        OrderDetailPool::getOrderDetailId,
                                        OrderDetailPool::getOrderNumber,
                                        OrderDetailPool::getGoodsSku,
                                        OrderDetailPool::getConfirmNum,
                                        OrderDetailPool::getAfterSaleNum,
                                        OrderDetailPool::getSupplierInvoicedNum,
                                        OrderDetailPool::getSupplierInvoicedMoney,
                                        OrderDetailPool::getSupplierSettlementNum,
                                        OrderDetailPool::getSupplierSettlementMoney);
                        detailPoolList.addAll(orderDetailPoolService.list(detailPoolQueryWrapper));
                    }
            );

            if (CollectionUtils.isEmpty(detailPoolList)) {
                log.info("订单未查询到明细的数据：{},销售渠道：{}", JsonUtils.toJsonString(orderDetailIdList), OrderSalesChannelEnum.get(orderSalesChannel));
                throw new ParameterException("未找到对应的订单明细数据！");
            }

            Map<String, OrderDetailPool> orderDetailMap = detailPoolList.stream().collect(Collectors.toMap(OrderDetailPool::getOrderDetailId, Function.identity()));

            Map<String, List<SupplierSettlementDto>> supplierSettlementMap = supplierSettlementDtos.stream().collect(Collectors.groupingBy(SupplierSettlementDto::getOrderDetailId));

            for (String orderDetailId : supplierSettlementMap.keySet()) {
                OrderDetailPool orderDetailPool = orderDetailMap.get(orderDetailId);
                if (orderDetailPool == null) {
                    log.info("账单明细存在，订单明细生命周期未找到，明细为：{}", JsonUtils.toJsonString(supplierSettlementMap.get(orderDetailId)));
                    throw new ParameterException("订单：{},sku:{}未查询到对应明细数据",
                            supplierSettlementMap.get(orderDetailId).get(0).getOrderNumber(),
                            supplierSettlementMap.get(orderDetailId).get(0).getGoodsSku());
                }

                List<SupplierSettlementDto> settlementDtoList = supplierSettlementMap.get(orderDetailId);

                BigDecimal invoicedNum = settlementDtoList.stream().map(SupplierSettlementDto::getCheckedNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal invoicedMoney = settlementDtoList.stream().map(SupplierSettlementDto::getTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal supplierInvoiceFlag = orderDetailPool.getConfirmNum()
                        .subtract(invoicedNum)
                        .subtract(orderDetailPool.getAfterSaleNum())
                        .subtract(orderDetailPool.getSupplierInvoicedNum());

                OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
                updateOrderDetailPool.setId(orderDetailPool.getId());
                updateOrderDetailPool.setSupplierSettlementNum(orderDetailPool.getSupplierInvoicedNum().add(invoicedNum));
                updateOrderDetailPool.setSupplierSettlementMoney(orderDetailPool.getSupplierInvoicedMoney().add(invoicedMoney));

                if (supplierInvoiceFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setSupplierSettlementStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else if (supplierInvoiceFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setSupplierSettlementStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetailPool.setSupplierSettlementStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }

                orderDetailPoolForUpdateList.add(updateOrderDetailPool);
                billDetailIdList.addAll(settlementDtoList.stream().map(SupplierSettlementDto::getDetailId).collect(Collectors.toList()));
            }
        }

        //更新数据
        Lists.partition(orderDetailPoolForUpdateList, 500).forEach(batch -> {
            orderDetailPoolService.getBaseMapper().updateForSupplierSettled(batch);
        });

        Lists.partition(billDetailIdList, 1000).forEach(batch -> {
            UpdateWrapper<SettleShopBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(SettleShopBillDetail::getBillMatchReason, "supplierSettle")
                    .in(SettleShopBillDetail::getDetailId, batch);
            settleShopBillDetailService.update(updateWrapper);
        });

        UpdateWrapper<SettleShopBill> shopBillUpdateWrapper = new UpdateWrapper<>();
        shopBillUpdateWrapper.lambda().eq(SettleShopBill::getBillId,one.getBillId())
                .set(SettleShopBill::getBillStatus,BillStatusEnum.SETTLED.getCode());
        settleShopBillService.update(shopBillUpdateWrapper);

        return "供应商账单" + one.getBillSn() + "付款结算状态更新完成！";
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void companyBillOutForMessage(SettleCompanyBillMessage message) {
        // 处理出账的操作
        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleBillPool::getDeliveryDetailId, message.getDeliveryDetailId());

        SettleBillPool billpool = settleBillPoolService.getOne(queryWrapper);
        if (billpool == null) {
            throw new ParameterException("未查询到数据");
        }

        if (BillPoolOutStatusEnum.UN_BILLED.getCode() != billpool.getCompanyOutAccountState()) {
            message.setCompanyRemark("账单状态为：" + BillPoolOutStatusEnum.getBillOutStatusNameByCode(billpool.getCompanyOutAccountState()));
            return;
        }

        if (billpool.getCheckedNum().compareTo(BigDecimal.ZERO) == 0) {
            message.setCompanyRemark("账单明细出账数量为0");
            return;
        }

        if (billpool.getSapOrderType() != 0 && billpool.getSapOrderType() != 4) {
            message.setCompanyRemark("外部验收订单，sap_order_type:"+billpool.getSapOrderType());
            return;
        }

        String yearAndMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);

        List<BillCompanyInfoDto> billCompanyInfoDtos = systemOrganizationPurchaseContractService.getBaseMapper()
                .queryBillCompanyInfoDto(Collections.singletonList(billpool.getCompanyCode()), billpool.getTenantId());
        if (CollectionUtils.isEmpty(billCompanyInfoDtos)) {
            throw new ParameterException("企业信息查询为空！");
        }

        if (billCompanyInfoDtos.get(0).getRealTimeCheckedFlag() == 0) {
            //企业未开启实时出账
            log.info("企业：{}未开启实时出账", billCompanyInfoDtos.get(0).getCompanyCode());
            message.setCompanyRemark("企业未开启实时出账");
            return;
        }

        CustomerSourceAndAreaTypeDto customerSourceAndAreaTypeDto = getCustomerSourceAndAreaTypeDto(billCompanyInfoDtos.get(0).getOrgRangMark());

        settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(billCompanyInfoDtos.get(0).getCompanyCode(),
                billCompanyInfoDtos.get(0).getCompanyName(),
                yearAndMonth,
                Boolean.TRUE,
                BillCustomerTypeEnum.COMPANY.getCode(),
                customerSourceAndAreaTypeDto.getCustomerSourceType(),
                customerSourceAndAreaTypeDto.getBillAreaType(),
                PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode(),
                Collections.singletonList(billpool));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void supplierBillOutForMessage(SettleCompanyBillMessage message) {
        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleBillPool::getDeliveryDetailId, message.getDeliveryDetailId());
        SettleBillPool billpool = settleBillPoolService.getOne(queryWrapper);

        if (billpool == null) {
            throw new ParameterException("未查询到数据");
        }

        if (BillPoolOutStatusEnum.UN_BILLED.getCode() != billpool.getSupplierOutAccountState()){
            message.setCompanyRemark("账单状态为：" + BillPoolOutStatusEnum.getBillOutStatusNameByCode(billpool.getCompanyOutAccountState()));
            return;
        }
        if (billpool.getSupplierCheckedNum().compareTo(BigDecimal.ZERO) == 0) {
            message.setCompanyRemark("账单明细出账数量为0");
            return;
        }

        //过滤售后邮费的池数据
        settleShopBillDetailService.filterAfterSalePostage(Collections.singletonList(billpool));

        //指定租户去查询
        List<ShopSupplier> supplierList = shopSupplierService.getBaseMapper()
                .queryBillSupplierInfo(Collections.singletonList(billpool.getSupplierCode()), billpool.getTenantId());
        if (CollectionUtil.isEmpty(supplierList)) {
            log.info("实时生成账单，查询供应商信息为空，入参codes:{},租户tanantId:{}", billpool.getSupplierCode(), message.getTenantId());
            message.setSupplierRemark("未查询到供应商信息");
            return;
        }
        String yearAndMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);

        //南方供应商不再实时出账 实时出账的 只有标准商城的平台内的 平台外的在确认订单即出账供应商账单了
        settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(supplierList.get(0).getSupplierCode(),
                supplierList.get(0).getSupplierFullName(),
                yearAndMonth,
                Boolean.FALSE,
                BillCustomerTypeEnum.SUPPLIER.getCode(),
                CustomerSourceTypeEnum.STANDARD_MALL.getCode(),
                BillAreaTypeEnum.CENTRAL_CHINA.getCode(),
                PlatformReconciliationEnum.INPLATFORMRECONCILIATION.getCode(),
                Collections.singletonList(billpool));
    }
}
