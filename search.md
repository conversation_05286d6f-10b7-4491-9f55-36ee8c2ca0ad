# 商品搜索引擎架构文档

## 概述

基于Elasticsearch的商品搜索系统，提供多维度商品检索、智能排序、聚合统计等功能。系统通过get方法实现前端商品搜索功能。

## 系统架构图

```mermaid
graph TB
    A[用户请求] --> B[get方法]
    
    B --> C[权限验证]
    C --> D[排序策略选择]
    D --> E{排序类型}
    
    E --> |自定义排序| F[字段排序]
    E --> |DFN福利定制| G[脚本排序]
    E --> |企业排序规则| H[数据库排序规则]
    E --> |默认| I[相关性排序]
    
    F --> J[构建查询条件]
    G --> J
    H --> J
    I --> J
    
    J --> K[分页策略]
    K --> L{分页类型}
    L --> |search_after| M[游标分页]
    L --> |传统分页| N[from+size分页]
    
    M --> O[执行ES查询]
    N --> P[执行ES查询+Collapse去重]
    
    O --> Q[结果处理]
    P --> Q
    Q --> R[聚合数据处理]
    R --> S[返回结果]
```

## 核心常量配置

| 常量名 | 值 | 说明 |
|--------|----|----|
| SEARCH_SLOP | 50 | 短语匹配的词距容忍度 |
| MAX_ITEMS | 10000 | 最大返回条数限制 |
| GOODS_DESC_BOOST | 1000.0F | 商品描述字段权重（最高优先级） |
| TEXT_SEARCH_BOOST | 1.0F | 通用搜索字段权重 |
| CATEGORY_BOOST | 0.1F | 分类匹配权重（较低优先级） |
| KEY_DEFAULT_BOOST | 6.0F | 默认关键词权重 |
| MAX_BRAND | 30 | 品牌聚合最大桶数量 |
| MAX_CATE | 30 | 分类聚合最大桶数量 |
| MAX_SUPPLIER | 30 | 供应商聚合最大桶数量 |
| MAX_ATTR | 10 | 属性聚合最大桶数量 |
| MAX_CLASS | 5 | 关键词匹配分类最大数量 |
| MAX_MODEL | 10 | 商品类型聚合最大桶数量 |
| MAX_TYPE | 2 | 供应商类型聚合最大桶数量 |

## 搜索字段权重策略

```mermaid
graph LR
    A[搜索关键词] --> B[权重分配]
    B --> C[商品描述 goodsDesc<br/>权重: 1000.0]
    B --> D[合同号 contractNumber<br/>权重: 50.0]
    B --> E[通用搜索 textForSearch<br/>权重: 1.0]
    B --> F[分类匹配<br/>权重: 0.1]
    B --> G[关键词默认权重<br/>权重: 6.0]
```

### 权重设计原则

1. **商品描述优先**: goodsDesc字段权重最高（1000.0），确保精确描述匹配排在最前
2. **合同号次优**: contractNumber字段权重中等（50.0），支持精确合同号查找
3. **通用搜索兜底**: textForSearch字段权重标准（1.0），提供全面搜索覆盖
4. **分类辅助**: 分类匹配权重较低（0.1），避免影响主要搜索结果
5. **关键词匹配**: 默认关键词权重（6.0），平衡搜索结果相关性

## 排序策略详解

### 1. 自定义字段排序

- **触发条件**: `params.getOrderBy()` 不为空
- **逻辑**: 按照指定字段和方向进行排序
- **格式**: `字段名,排序方向_字段名,排序方向`

### 2. DFN福利定制排序

- **触发条件**: `params.getDfnFlCustomSearch()` 不为空
- **实现方式**: Elasticsearch脚本排序
- **排序规则**:

```javascript
// 有自定义排序的商品返回排序号，其他商品使用相关性得分
return rules.containsKey(goodsCode) ? rules.get(goodsCode) : (1000 + (1.0 / Math.max(_score, 0.001)));
```

### 3. 企业排序规则

- **触发条件**: 从数据库查询到商品池排序规则
- **数据来源**: `DfmallGoodsPoolSortMapper.queryGoodsSortByPoolId()`
- **排序逻辑**:

```javascript
// 根据商品池ID获取排序规则
def rules = params.rules.get(params.enterpriseId);
def goodsCode = doc['goodsCode'].value;
return rules.containsKey(goodsCode) ? rules.get(goodsCode) : (1000 + (1.0 / Math.max(_score, 0.001)));
```

### 4. 默认相关性排序

- **触发条件**: 无自定义排序或存在搜索关键词
- **排序字段**: `_score` (降序) + `_id` (升序，保证分页稳定性)
- **实现逻辑**:

```java
// 如果没有自定义排序，或者有搜索关键词，添加得分排序
if (!hasCustomSort || StrUtil.isNotBlank(params.getKey())) {
    sbCommon.sort(c -> c.score(sc -> sc.order(SortOrder.Desc)));
}
// 在所有排序的最后添加文档ID排序，确保分页稳定性
sbCommon.sort(c -> c.field(fi -> fi.field("_id").order(SortOrder.Asc)));
```

## 分页策略

```mermaid
graph TD
    A[分页请求] --> B{分页方式判断}
    B --> |有lastId和lastScore| C[search_after分页]
    B --> |无游标参数| D[传统from+size分页]
    
    C --> E[优点: 深度分页性能好]
    C --> F[缺点: 不支持collapse去重]
    
    D --> G[优点: 支持collapse去重]
    D --> H[缺点: 深度分页性能差]
    
    E --> I[适用场景: 大数据量浏览]
    G --> J[适用场景: 需要去重的查询]
```

### 分页实现细节

```java
// 检查是否使用了 search_after 分页参数
boolean useSearchAfter = StrUtil.isNotBlank(params.getLastId()) && params.getLastScore() != null;

if (useSearchAfter) {
    // 使用 search_after 分页时，不能使用 collapse 功能
    // 排序值的顺序必须与排序字段的顺序一致：score(desc), _id(asc)
    sbCommon.searchAfter(String.valueOf(params.getLastScore()), params.getLastId());
} else {
    // 传统的 from + size 分页
    sbCommon.from(page.getPageSize() * (page.getCurPage() - 1));
    // 当不使用search_after分页时,使用collapse功能对相同sameCode的商品进行折叠
    sbCommon.collapse(FieldCollapse.of(c -> c.field("sameCode")));
}
```

## 查询条件构建

### 基础过滤条件

1. **租户隔离**: `tenantIds = 当前租户ID`
2. **数据权限**: 根据用户权限过滤供应商
3. **商品池过滤**: `shopGoodsPool in 指定商品池`

### 搜索条件构建流程

```mermaid
graph TD
    A[关键词搜索] --> B[构建BoolQuery]
    B --> C[商品描述匹配<br/>boost: 1000.0]
    B --> D[合同号精确匹配<br/>boost: 50.0]
    B --> E[通用搜索匹配<br/>boost: 1.0]
    
    C --> F[短语匹配 + IK分词器]
    D --> G[Term精确匹配]
    E --> H[短语匹配 + IK分词器]
    
    F --> I[minimumShouldMatch: 1]
    G --> I
    H --> I
    
    I --> J[添加到主查询must条件]
```

### 关键词搜索实现

```java
if (StrUtil.isNotBlank(params.getKey())) {
    final BoolQuery.Builder keywordQuery = QueryBuilders.bool();

    // 商品描述匹配：使用大幅权重提升确保绝对优先
    keywordQuery.should(_2 -> _2.matchPhrase(_3 -> _3.field("goodsDesc")
            .analyzer("ik_syno_smart")
            .boost(GOODS_DESC_BOOST) // 1000.0f
            .query(params.getKey().trim()).slop(SEARCH_SLOP)));

    // textForSearch匹配：权重较低
    keywordQuery.should(_2 -> _2.matchPhrase(
            _3 -> _3.field("textForSearch").analyzer("ik_syno_smart").boost(1.0f)
                    .query(params.getKey().trim()).slop(SEARCH_SLOP)));

    // 合同号精确匹配：次高权重
    keywordQuery.should(_2 -> _2.term(_3 -> _3.field("contractNumber").boost(50.0f)
            .value(params.getKey().trim())));

    keywordQuery.minimumShouldMatch("1");
    parCommon.must(keywordQuery.build()._toQuery());
}
```

### 高级过滤条件

| 过滤维度 | 字段 | 查询类型 | 说明 |
|----------|------|----------|------|
| 价格区间 | goodsPactPrice | range | 支持最小值-最大值区间 |
| 积分价格区间 | goodsIntegralPactPrice | range | 积分商品价格过滤 |
| 积分比例 | integralCeiling | range | 积分抵扣比例过滤 |
| 商品属性 | goodsSpecArray | nested | 嵌套属性key-value匹配 |
| 品牌筛选 | brandName | terms | 多品牌OR查询 |
| 供应商筛选 | supplierName | terms | 多供应商OR查询 |
| 分类筛选 | standCategoryName等 | terms | 多级分类OR查询 |
| 商品类型 | goodsModel | term | 商品模型精确匹配 |
| 供应商类型 | supplierType | term | 供应商类型筛选（0:电商平台,1:独立供应商）|
| 专区商品类型 | zoneGoodsType | terms | 商品专区类型过滤 |
| 企业组织 | contractCompanyOrganizationId | term | 导入企业筛选 |

### 属性过滤实现示例

```java
// 处理属性过滤 格式: XXXX,sfqe;fdwefw;fwew_XXXX,fwef_
if (StrUtil.isNotBlank(params.getAttrs())) {
    final var sl = params.getAttrs().split("_");
    for (int i = 0; i < sl.length; i++) {
        final var sfield = sl[i].split(",");
        if (sfield.length != 2) continue;
        
        val av = sfield[1].split(";");
        final BoolQuery.Builder subAttr = QueryBuilders.bool();
        for (final String a : av) {
            if (StrUtil.isBlank(a)) continue;
            subAttr.should(_2 -> _2.nested(_3 -> _3.path("goodsSpecArray")
                    .query(_4 -> _4.bool(_5 -> _5
                            .must(_6 -> _6.term(_7 -> _7.field("goodsSpecArray.key").value(sfield[0])))
                            .must(_6 -> _6.term(_7 -> _7.field("goodsSpecArray.value").value(a)))))));
        }
        parCommon.must(m -> m.bool(subAttr.build()));
    }
}
```

## 聚合统计功能

### 聚合维度配置

```mermaid
graph TD
    A[聚合统计] --> B[分类聚合<br/>stand_class_types]
    A --> C[品牌聚合<br/>stand_brand_types]  
    A --> D[供应商聚合<br/>supplier]
    A --> E[商品类型聚合<br/>goods_model]
    A --> F[价格统计<br/>price_area_list]
    A --> G[供应商类型聚合<br/>supplierType]
    A --> H[属性聚合<br/>attrs嵌套]
```

### 聚合结果处理

| 聚合类型 | 处理逻辑 | 返回格式 | 实现说明 |
|----------|----------|----------|----------|
| Sterms | 提取bucket的key值 | List&lt;String&gt; | 字符串类型聚合桶 |
| Stats | 统计最大、最小、平均值等 | JSONObject | 价格统计信息 |
| Nested | 嵌套属性key-value对 | Map&lt;String, List&lt;String&gt;&gt; | 商品属性聚合 |
| Lterms | 供应商类型编码转换 | Map&lt;String, String&gt; | 长整型聚合桶转换 |

### 聚合结果处理实现

```java
val aggregations = search.aggregations();
aggregations.forEach((item, value) -> {
    if (value._kind() == Aggregate.Kind.Sterms) {
        // 字符串类型聚合：提取桶的key值
        aggs.put(item, value.sterms().buckets().array().stream()
                .map(i -> i.key()).collect(Collectors.toList()));
    } else if (value._kind() == Aggregate.Kind.Stats) {
        // 统计聚合：处理价格统计信息
        final JSONObject jo = new JSONObject();
        jo.put("max", NumberUtil.decimalFormat("##0.00", value.stats().max()));
        jo.put("min", NumberUtil.decimalFormat("##0.00", value.stats().min()));
        jo.put("avg", NumberUtil.decimalFormat("##0.00", value.stats().avg()));
        jo.put("price_diff_count", NumberUtil.decimalFormat("##0.00", value.stats().count()));
        jo.put("price_diff_sum", NumberUtil.decimalFormat("##0.00", value.stats().sum()));
        aggs.put(item, jo);
    } else if (value._kind() == Aggregate.Kind.Nested) {
        // 嵌套聚合：处理商品属性
        final Map<String, List<String>> ress = new HashMap<>(16);
        val buckets = value.nested().aggregations().get("name").sterms().buckets();
        for (int i = 0; i < buckets.array().size(); i++) {
            val bi = buckets.array().get(i);
            val ha = bi.aggregations().get("value").sterms().buckets().array().stream()
                    .map(bk -> bk.key()).collect(Collectors.toList());
            ress.put(bi.key(), ha);
        }
        aggs.put(item, ress);
    } else if (value._kind() == Aggregate.Kind.Lterms && "supplierType".equals(item)) {
        // 长整型聚合：供应商类型转换
        List<String> typeCodeList = value.lterms().buckets().array().stream()
                .map(LongTermsBucket::key).collect(Collectors.toList());
        Map<String, String> typeValue = new HashMap<>(2);
        // 0：电商平台，1：独立供应商
        typeCodeList.forEach(x -> typeValue.put(x, "0".equals(x) ? "电商平台" : "独立供应商"));
        aggs.put(item, typeValue);
    }
});
```

## 去重机制

### Collapse去重

- **功能**: 基于`sameCode`字段对商品进行去重
- **限制**: 与`search_after`分页不兼容
- **适用场景**: 传统分页方式下的商品去重

```java
// 当不使用search_after分页时,使用collapse功能对相同sameCode的商品进行折叠
// collapse功能可以将具有相同sameCode值的文档合并为一个结果,用于去重
// 注意:collapse功能与search_after分页方式不兼容,因此只在使用传统分页时启用
if (!useSearchAfter) {
    sbCommon.collapse(FieldCollapse.of(c -> c.field("sameCode")));
}
```

### 分页游标信息传递

```java
// 记录最后一条记录的排序信息，用于下次分页
for (final Hit<GoodsIndex> hit : search.hits().hits()) {
    // 处理结果...
    lastId = hit.id();
    lastScore = hit.score();
}

// 将分页游标信息添加到聚合数据中，供前端下次请求使用
if (lastId != null && lastScore != null) {
    aggs.put("lastId", lastId);
    aggs.put("lastScore", lastScore);
}
```

## 高亮显示

- **高亮字段**: `textForSearch`
- **高亮标签**: `<span class='search-key-word'>关键词</span>`
- **片段大小**: 500字符
- **应用场景**: 搜索结果中关键词标红显示

### 高亮处理逻辑

```java
// 配置高亮显示
sbCommon.highlight(h -> h.fields("textForSearch",
        tag -> tag.postTags("</span>").fragmentSize(500).preTags("<span class='search-key-word'>")));

// 处理高亮结果
for (final Hit<GoodsIndex> hit : search.hits().hits()) {
    // 如果有高亮结果，替换商品描述为高亮内容
    if (hit.highlight() != null && hit.highlight().get("textForSearch") != null && hit.source() != null) {
        hit.source().setGoodsDesc(hit.highlight().get("textForSearch").get(0).split(this.descSplit)[0]);
    }
    res.add(hit.source());
}
```

## 特殊功能

### DFN福利定制搜索

- **过滤功能**: 根据分类名称过滤特定商品
- **排序功能**: 自定义商品排序规则
- **触发标识**: `dfnFlCustomSearch`参数

#### 实现逻辑

```java
// DFN福利搜索定制标识
boolean isDfnFlCustomSearch = StrUtil.isNotBlank(params.getDfnFlCustomSearch());

if (isDfnFlCustomSearch) {
    // 排序处理
    dfnFlCustomSearchSort(params.getDfnFlCustomSearch(), sbCommon);
    // 过滤处理
    dfnFlCustomSearchFilter(params.getDfnFlCustomSearch(), parCommon);
}
```

#### 自定义排序实现

```java
private void dfnFlCustomSearchSort(String dfnFlCustomSearchParam, SearchRequest.Builder sbCommon) {
    GoodQueryParams.DfnFlCustomSearchParam dfnFlCustomSearch = 
        JSONUtil.toBean(dfnFlCustomSearchParam, GoodQueryParams.DfnFlCustomSearchParam.class);
    
    Map<String, Long> dfnFlGoodsMap = dfnFlService.getDfnFlGoodsSort(dfnFlCustomSearch.getSupplierZone());
    if (CollectionUtil.isNotEmpty(dfnFlGoodsMap)) {
        // 构建脚本排序
        Script script = new Script.Builder()
                .inline(in -> in.source(
                        "def rules = params.sortRules;" +
                        "def goodsCode = doc['goodsCode'].value;" +
                        "return rules.containsKey(goodsCode) ? rules.get(goodsCode) : (1000 + (1.0 / Math.max(_score, 0.001)));"
                ))
                .build();
        sbCommon.sort(s -> s.script(ss -> ss.script(script).type(ScriptSortType.Number).order(SortOrder.Asc)));
    }
}
```

### 企业级排序

- **数据来源**: 数据库中的商品池排序配置
- **实现方式**: Elasticsearch脚本排序
- **排序策略**: 自定义排序 + 相关性兜底

#### 企业排序规则获取

```java
private Map<String,Map<String,Long>> getEnterpriseSortRules(String goodsPoolId){
    List<Long> poolIds = Arrays.stream(goodsPoolId.split(","))
            .map(Long::valueOf)
            .collect(Collectors.toList());
    List<DfmallGoodsPoolSortEntity> results = goodsPoolSortMapper.queryGoodsSortByPoolId(poolIds);
    
    if(CollectionUtil.isNotEmpty(results)){
        Map<String,Map<String,Long>> result = new HashMap<>();
        result.put(goodsPoolId, results.stream().collect(
            Collectors.toMap(DfmallGoodsPoolSortEntity::getGoodsCode, DfmallGoodsPoolSortEntity::getSortNo)));
        return result;
    }
    return null;
}
```

## 性能优化策略

### 1. 分页优化

- **浅分页**: 使用`from + size`
- **深分页**: 使用`search_after`游标分页
- **去重需求**: 优先使用`collapse`功能

### 2. 查询优化

- **权重设计**: 合理分配字段权重，避免不相关结果排前
- **分词器选择**: 使用`ik_syno_smart`支持同义词扩展
- **缓存策略**: 利用ES查询缓存提升重复查询性能

### 3. 聚合优化

- **限制聚合桶数量**: 通过`size`参数控制返回数量
- **嵌套聚合**: 对属性等复杂结构使用嵌套聚合

## 权限控制与数据隔离

### 租户隔离

```java
// 过滤租户
if (TenantContextHolder.getTenantId() != null) {
    parCommon.must(m -> m.term(_2 -> _2.field("tenantIds").value(TenantContextHolder.getTenantId())));
}
```

### 供应商数据权限

```java
// 根据供应商筛选，支持全量和受限权限
final OrganizationDataPermissionRespDTO organizationDataPermission;

if (StringUtil.isNotBlank(params.getUserType()) && params.getUserType().equals(EsUserTypeEnum.YK.getCode())) {
    // YK用户类型：全量权限
    organizationDataPermission = new OrganizationDataPermissionRespDTO();
    organizationDataPermission.setAll(true);
} else {
    // 普通用户：获取数据权限
    organizationDataPermission = systemPermissionService.getOrganizationDataPermission(user.getId());
}

if (!organizationDataPermission.getAll()) {
    // 受限权限：只能查看特定供应商的商品
    final Set<Long> tempSet = organizationDataPermission.getOrganizationIds();
    final List<FieldValue> organizationIds = CollectionUtils.convertList(tempSet, FieldValue::of);
    parCommon.must(m -> m.terms(_2 -> _2.field("supplierId").terms(_3 -> _3.value(organizationIds))));
}
```

### 商品池权限控制

```java
// 处理商品池：用户只能访问指定的商品池
if (CollectionUtil.isNotEmpty(params.getShopGoodsPoolArray())) {
    final List<FieldValue> poolIds = CollectionUtils.convertList(params.getShopGoodsPoolArray(), FieldValue::of);
    parCommon.filter(_2 -> _2.terms(_3 -> _3.field("shopGoodsPool").terms(_4 -> _4.value(poolIds))));
}
```

## get方法核心流程

### 1. 用户权限验证

```java
final LoginUser user = LocalUserHolder.get();
// 获取用户组织数据权限
final OrganizationDataPermissionRespDTO organizationDataPermission = 
    systemPermissionService.getOrganizationDataPermission(user.getId());
```

### 2. 关键词分类匹配

在get方法中，系统会根据搜索关键词从`search_key_info`索引中查找对应的商品分类：

```java
// 根据关键词从search_key_info索引中查找对应的分类
final List<String> cateList = this.getKeyCategory(params);
```

#### 查询实现

```java
private List<String> getKeyCategory(final GoodQueryParams params) throws IOException {
    final SearchRequest.Builder cq = new SearchRequest.Builder();
    cq.index("search_key_info");
    final BoolQuery.Builder sub1 = QueryBuilders.bool();

    if (StrUtil.isNotBlank(params.getKey())) {
        // 精确匹配关键词，权重为6.0
        sub1.should(_2 -> _2.term(_3 -> _3.field("s_key.keyword")
                .boost(KEY_DEFAULT_BOOST)  // 6.0f
                .value(params.getKey().trim())));
    }

    cq.query(sub1.build()._toQuery());
    final SearchResponse<SaveSearchKeyInfoReq> ss = this.client.search(cq.build(), SaveSearchKeyInfoReq.class);

    final List<String> allCate = new ArrayList<>(16);
    ss.hits().hits().forEach(item -> {
        // 解析分类列表，用逗号分隔
        allCate.addAll(CollectionUtil.toList(item.source().getS_cate_list().split(",")));
    });
    
    // 最多选取5个相关分类，优先展示
    if (allCate.size() == 0) {
        return new ArrayList<>();
    }
    return allCate.subList(0, Math.min(MAX_CLASS, allCate.size()));  // MAX_CLASS = 5
}
```

#### 数据结构

`search_key_info`索引存储关键词与分类的映射关系：

| 字段 | 类型 | 说明 |
|------|------|------|
| s_key | keyword | 搜索关键词 |
| s_cate_list | text | 相关分类列表，逗号分隔 |

#### 使用场景

- **智能分类**: 根据用户搜索的关键词自动推荐相关分类
- **搜索优化**: 提升分类相关商品的搜索排名
- **用户体验**: 为用户提供更精准的搜索结果

### 3. 排序策略处理

系统按优先级顺序处理排序策略：

1. **自定义字段排序** (`params.getOrderBy()` 不为空)
2. **DFN福利定制排序** (`params.getDfnFlCustomSearch()` 不为空)  
3. **企业排序规则** (从数据库查询商品池排序)
4. **默认相关性排序** (无自定义排序或有搜索关键词)

### 4. 分页策略选择

```java
boolean useSearchAfter = StrUtil.isNotBlank(params.getLastId()) && params.getLastScore() != null;
```

### 5. 查询条件构建

包含基础过滤、关键词搜索、高级过滤等多层条件构建。

### 6. 聚合配置

配置7种聚合维度：分类、品牌、供应商、商品类型、价格统计、供应商类型、属性。

### 7. 执行查询与结果处理

执行ES查询，处理高亮显示，构建聚合结果，返回分页数据。



## textForSearch字段构建逻辑

### 字段组成

`textForSearch`是商品搜索的核心复合字段，由多个维度的商品信息拼接而成：

```mermaid
graph TD
    A[textForSearch字段] --> B[基础商品信息]
    A --> C[标准商品信息]
    
    B --> D[商品名称 goodsName]
    B --> E[商品描述 goodsDesc]
    B --> F[品牌名称 brandName]
    B --> G[供应商名称 supplierName]
    B --> H[分类信息 categoryNames]
    B --> I[商品关键词 goodsKeywords]
    B --> J[SRM编码 goodsSrmCode]
    
    C --> K[标准品牌 standardProduct.brand]
    C --> L[标准型号 standardProduct.model]
    C --> M[标准材质 standardProduct.materialName]
    C --> N[标准规格 standardProduct.specification]
    C --> O[关键属性 keyProperties]
    C --> P[其他属性 otherProperties]
```

### 构建算法

```java
// 基础商品信息拼接
String tfs = item.getGoodsName() + " " 
           + item.getGoodsDesc() + " " 
           + item.getBrandName() + " " 
           + item.getSupplierName() + " " 
           + gi.getStandThirdCategoryName() + " " 
           + gi.getStandSecondCategoryName() + " " 
           + gi.getStandFirstCategoryName() + " " 
           + gi.getGoodsKeywords() + " " 
           + goodsSrmCode;

// 添加标准商品信息
if (standardProduct != null) {
    String stdInfo = standardProduct.getBrand() + " "
                   + standardProduct.getModel() + " "
                   + standardProduct.getMaterialName() + " "
                   + standardProduct.getSpecification() + " "
                   + String.join(" ", standardProduct.getKeyProperties().values()) + " "
                   + String.join(" ", standardProduct.getOtherProperties().values()) + " ";
    
    tfs += " " + stdInfo;
}

gi.setTextForSearch(tfs);
```

### 搜索策略

在get方法中，`textForSearch`字段作为兜底搜索字段：

- **权重**: 1.0f (标准权重)
- **分析器**: `ik_syno_smart` (支持同义词)
- **查询类型**: `matchPhrase` (短语匹配)
- **词距容忍度**: 50 (SEARCH_SLOP)
- **优先级**: 低于商品描述(1000.0f)和合同号(50.0f)

### 设计目的

1. **全面覆盖**: 包含商品的所有文本信息，确保搜索不遗漏
2. **同义词支持**: 通过ik_syno_smart分析器支持同义词扩展
3. **兜底机制**: 当商品描述和合同号都匹配不到时的备选方案
4. **标准化**: 整合标准商品库信息，提升搜索准确性

## 实际使用示例

### 搜索请求示例

```java
// 1. 基础关键词搜索
GoodQueryParams params = new GoodQueryParams();
params.setKey("苹果手机");  // 搜索关键词
params.setPageSize(20);
params.setCurPage(1;

// 2. 带过滤条件的搜索
params.setBrandNames("苹果,华为");  // 品牌过滤
params.setPriceArae("1000,5000");  // 价格区间
params.setAttrs("颜色,红色;蓝色_内存,128G;256G");  // 属性过滤

// 3. 自定义排序
params.setOrderBy("goodsPactPrice,desc_createTime,asc");

// 4. search_after分页
params.setLastId("doc_id_123");
params.setLastScore(0.8765);
```

### 典型ES查询结构

get方法生成的Elasticsearch查询大致结构：

```json
{
  "query": {
    "bool": {
      "must": [
        {
          "bool": {
            "should": [
              {
                "match_phrase": {
                  "goodsDesc": {
                    "query": "苹果手机",
                    "analyzer": "ik_syno_smart",
                    "boost": 1000.0,
                    "slop": 50
                  }
                }
              },
              {
                "match_phrase": {
                  "textForSearch": {
                    "query": "苹果手机", 
                    "analyzer": "ik_syno_smart",
                    "boost": 1.0,
                    "slop": 50
                  }
                }
              },
              {
                "term": {
                  "contractNumber": {
                    "value": "苹果手机",
                    "boost": 50.0
                  }
                }
              }
            ],
            "minimum_should_match": "1"
          }
        },
        {
          "term": {
            "tenantIds": "tenant_123"
          }
        }
      ],
      "filter": [
        {
          "terms": {
            "shopGoodsPool": ["24", "25"]
          }
        },
        {
          "range": {
            "goodsPactPrice": {
              "gte": 1000,
              "lte": 5000
            }
          }
        }
      ]
    }
  },
  "sort": [
    {
      "_score": {
        "order": "desc"
      }
    },
    {
      "_id": {
        "order": "asc"
      }
    }
  ],
  "from": 0,
  "size": 20,
  "collapse": {
    "field": "sameCode"
  },
  "aggregations": {
    "stand_brand_types": {
      "terms": {
        "field": "brandName",
        "size": 30
      }
    },
    "stand_class_types": {
      "terms": {
        "field": "standThirdCategoryName",
        "size": 30
      }
    }
  },
  "highlight": {
    "fields": {
      "textForSearch": {
        "pre_tags": ["<span class='search-key-word'>"],
        "post_tags": ["</span>"],
        "fragment_size": 500
      }
    }
  }
}
```

### 返回结果结构

```java
{
  "records": [
    {
      "goodsCode": "GOODS_001",
      "goodsName": "苹果iPhone 14",
      "goodsDesc": "苹果iPhone 14 128GB 蓝色",
      "goodsPactPrice": 4999.00,
      "brandName": "苹果",
      "supplierName": "苹果官方旗舰店",
      // ... 其他商品字段
    }
  ],
  "total": 1234,
  "pages": 62,
  "current": 1,
  "size": 20,
  "aggregateData": {
    "stand_brand_types": ["苹果", "华为", "小米"],
    "stand_class_types": ["智能手机", "平板电脑"],
    "supplier": ["苹果官方旗舰店", "华为官方店"],
    "price_area_list": {
      "max": "9999.00",
      "min": "999.00",
      "avg": "3456.78"
    },
    "lastId": "doc_id_456",  // search_after分页用
    "lastScore": 0.7654
  }
}
```
