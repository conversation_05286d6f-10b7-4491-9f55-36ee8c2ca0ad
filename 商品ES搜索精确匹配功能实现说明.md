# 商品ES搜索精确匹配功能实现说明

## 功能概述

在 `GoodsEsManager.java` 中实现了两个精确匹配的优先逻辑，用于提升商品搜索的准确性和用户体验。

## 实现的功能

### 1. 商品精确匹配逻辑

**匹配字段：**
- `goodsSku` - 商品SKU
- `goodsCode` - 商品编码  
- `maraMatnr` - SAP物料号

**匹配规则：**
- 当输入的搜索关键字能够精确匹配到以上任一字段时
- 进行完全相等的字符串匹配（不是部分匹配）
- 如果匹配成功，直接返回该精确匹配的结果，不再执行后续的模糊搜索

### 2. 合同号精确匹配逻辑

**匹配字段：**
- `contractNumber` - 合同号

**匹配规则：**
- 当输入的搜索关键字能够精确匹配到合同号字段时
- 进行完全相等的字符串匹配
- 返回该合同号对应的所有商品结果

## 执行优先级

1. **商品精确匹配** - 最高优先级
2. **合同号精确匹配** - 次高优先级  
3. **原有模糊搜索逻辑** - 当精确匹配都失败时执行

## 核心实现方法

### tryExactGoodsMatch()
- 尝试商品字段的精确匹配
- 使用 Elasticsearch 的 term 查询进行精确匹配
- 应用所有原有的过滤条件（租户、权限、商品池等）

### tryExactContractMatch()  
- 尝试合同号的精确匹配
- 同样应用所有原有的过滤条件

### buildPageResponse()
- 构建精确匹配的分页响应
- 重新执行完整搜索以获取所有匹配结果和聚合信息
- 保持与原有搜索结果格式的一致性

### buildCommonFilters()
- 提取的公共过滤条件构建方法
- 包含租户过滤、权限控制、商品池筛选等所有原有逻辑
- 确保精确匹配和模糊搜索使用相同的过滤规则

## 技术特点

1. **非侵入性设计** - 在原有搜索逻辑之前添加，不影响现有功能
2. **完整的过滤条件支持** - 精确匹配同样遵循所有业务规则
3. **高效的实现** - 精确匹配时先进行轻量级检查，确认有结果后再执行完整搜索
4. **错误处理** - 包含异常捕获和日志记录
5. **代码复用** - 提取公共方法，避免代码重复

## 使用场景

- 用户输入完整的商品SKU进行搜索
- 用户输入完整的商品编码进行搜索  
- 用户输入SAP物料号进行搜索
- 用户输入合同号查找相关商品

## 注意事项

- 精确匹配区分大小写
- 必须是完全匹配，不支持部分匹配
- 保持了原有的权限控制和业务规则
- 当精确匹配失败时，会自动降级到原有的模糊搜索逻辑
