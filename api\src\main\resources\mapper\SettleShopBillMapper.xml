<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.yph.api.settlement.common.mapper.SettleShopBillMapper">


    <select id="querySettleShopBillPage" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillVo">
        select
        bill_id,
        bill_sn,
        customer_name,
        customer_code,
        customer_type,
        customer_source_type,
        check_year,
        check_month,
        amount_tax,
        amount,
        point_amount,
        individual_payment_amount,
        postage,
        total_tax_amount,
        tolerance_amount,
        settlement_amount,
        bill_status,
        bill_approve_status,
        create_time,
        remark,
        is_platform_reconciliation,
        activity_name,
        activity_code,
        is_push,
        check_url
        FROM
        settle_shop_bill
        where
        1=1
        <if test="query.billSn != null and query.billSn !=''">
            and bill_sn =#{query.billSn}
        </if>
        <if test="query.checkYear != null">
            and check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth != null">
            and check_month =#{query.checkMonth}
        </if>
        <if test="query.billStatus !=null">
            and bill_status =#{query.billStatus}
        </if>
        <if test="query.customerType != null">
            and customer_type =#{query.customerType}
        </if>
        <if test="query.customerSourceType != null">
            and customer_source_type =#{query.customerSourceType}
        </if>
        <if test="query.checkedStartTime != null and query.checkedStartTime !=''">
            and create_time &gt;=#{query.checkedStartTime}
        </if>
        <if test="query.checkedEndTime != null and query.checkedEndTime !=''">
            and create_time &lt;=#{query.checkedEndTime}
        </if>
        <if test="query.keyWords != null and query.keyWords !=''">
            and (
            bill_sn like concat('%',#{query.keyWords},'%') or
            customer_name like concat('%',#{query.keyWords},'%')
            )
        </if>
        <if test="query.customerCode != null and query.customerCode !=''">
            and customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation != null">
            and is_platform_reconciliation=#{query.isPlatformReconciliation}
        </if>
        <if test="query.isPush != null">
            and is_push =#{query.isPush}
        </if>
        order by bill_id desc
    </select>
    <select id="querySettleShopBill" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillVo">
        select
        bill_id,
        bill_sn,
        customer_name,
        customer_code,
        customer_type,
        customer_source_type,
        check_year,
        check_month,
        amount_tax,
        amount,
        point_amount,
        individual_payment_amount,
        postage,
        total_tax_amount,
        tolerance_amount,
        settlement_amount,
        bill_status,
        bill_approve_status,
        create_time,
        activity_name
        FROM
        settle_shop_bill
        where
        1=1
        <if test="query.billSn != null and query.billSn !=''">
            and bill_sn =#{query.billSn}
        </if>
        <if test="query.checkYear != null">
            and check_year =#{query.checkYear}
        </if>
        <if test="query.checkMonth != null">
            and check_month =#{query.checkMonth}
        </if>
        <if test="query.billStatus !=null">
            and bill_status =#{query.billStatus}
        </if>
        <if test="query.customerType != null">
            and customer_type =#{query.customerType}
        </if>
        <if test="query.customerSourceType != null">
            and customer_source_type =#{query.customerSourceType}
        </if>
        <if test="query.checkedStartTime != null and query.checkedStartTime !=''">
            and create_time &gt;=#{query.checkedStartTime}
        </if>
        <if test="query.checkedEndTime != null and query.checkedEndTime !=''">
            and create_time &lt;=#{query.checkedEndTime}
        </if>
        <if test="query.keywords != null and query.keywords !=''">
            and (
            bill_sn like concat('%',#{query.keyWords},'%') or
            customer_name like concat('%',#{query.keyWords},'%')
            )
        </if>
        <if test="query.customerCode != null and query.customerCode !=''">
            and customer_code =#{query.customerCode}
        </if>
        <if test="query.isPlatformReconciliation != null">
            and is_platform_reconciliation=#{query.isPlatformReconciliation}
        </if>
    </select>

    <select id="getBillExcelById" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillExcelVo">
        select bill_sn,
               customer_name,
               check_year,
               check_month,
               bill_status,
               postage,
               total_tax_amount,
               create_time
        from settle_shop_bill
        where is_enable = 1
          and bill_id = #{billId}
    </select>

    <select id="getSumBill" resultType="java.util.Map">
        select sum(total_price_naked) as amount,
               sum(total_price_tax)   as amountTax
        from settle_shop_bill_detail
        where bill_id = #{billId}
          and is_enable = 1
    </select>

    <update id="updateSettleShopBill">
        update settle_shop_bill
        <set>
            <if test="billStatus != null">
                bill_status=#{billStatus},
            </if>
            <if test="billApproveStatus != null">
                bill_approve_status=#{billApproveStatus}
            </if>
        </set>
        where bill_id =#{billId}
    </update>

    <update id="rollBackBill">
        update settle_bill_pool
        set company_out_account_state =0,
            company_out_account_time  = null
        where company_out_account_state = 20;
        update settle_bill_pool
        set supplier_out_account_state=0,
            supplier_out_account_time = null
        where supplier_out_account_state = 20;
        truncate settle_shop_bill;
        truncate settle_shop_bill_detail;
        truncate settle_shop_bill_postage_detail;
        truncate settle_shop_bill_activity_member;
        truncate settle_bill_pool_yfl_customer;
        truncate settle_bill_life_cycle;
    </update>

    <select id="getBillAfterDto" resultType="com.ly.yph.api.settlement.common.dto.bill.BillAfterSaleDto">
        select ssb.bill_id,
               ssb.bill_status,
               ssbd.detail_id,
               ssbd.reconciliation_status,
               ssb.bill_sn,
               ssbd.checked_num,
               ssbd.invoicable_quantity
        from settle_shop_bill ssb
                 left join settle_shop_bill_detail ssbd on ssb.bill_id = ssbd.bill_id
        where ssbd.is_enable = 1
          and ssbd.pool_type = 1
          and ssb.customer_type = #{customerType}
          and ssbd.settle_bill_pool_id = #{poolId}
    </select>
    <select id="getSupplierSettlementList"
            resultType="com.ly.yph.api.settlement.common.dto.bill.SupplierSettlementDto">
        select b.order_number,
               b.goods_sku,
               b.checked_num,
               b.total_price_tax,
               c.order_sales_channel,
               c.order_detail_id,
               b.bill_match_reason,
               b.detail_id
        from settle_shop_bill_detail b
                 left join settle_bill_pool c on c.id = b.settle_bill_pool_id
        where b.bill_id = #{billId}
          and b.is_enable = 1
    </select>

    <select id="getSupplierBillDate" resultType="com.ly.yph.api.supplier.entity.ShopSupplier">
        select *
        From shop_supplier
        where supplier_code = #{supplierCode} limit 1
    </select>

    <select id="getSupplierContract" resultType="com.ly.yph.api.settlement.supplier.dto.SupplierContractInfoDto">
        select a.contract_name,
               a.contract_code,
               b.contract_scanned_url
        From system_contract a
                 left join supplier_contract_real b on a.id = b.contract_id and a.tenant_id = b.tenant_id
                 left join shop_supplier c on c.supplier_id = b.supplier_id and a.tenant_id = c.tenant_id
                 left join system_organization d on a.sign_company_code = d.code and a.tenant_id = d.tenant_id
        where c.is_enable = 1
          and c.data_source = 'dfmall'
          and d.is_virtual = 1
          and d.organization_type = 3
          and d.is_enable = 1
          and c.supplier_code = #{supplierCode}
        order by a.create_time desc
    </select>
    <select id="getSupplierReconciliationSum" resultType="java.util.Map">
        select sum(total_price_naked) as amount,
               sum(total_price_tax)   as amountTax
        from settle_shop_bill_detail
        where bill_id = #{billId}
          and is_enable = 1 and bill_staging_flag =0
    </select>

    <select id="getSupplierReconciliationSumForPostage" resultType="java.util.Map">
        select
            sum(postage) as postage
        from settle_shop_bill_postage_detail
        where bill_id =#{billId} and is_enable =1 and postage_staging_flag=0
    </select>

</mapper>