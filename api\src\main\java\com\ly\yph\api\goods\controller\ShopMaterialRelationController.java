package com.ly.yph.api.goods.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ly.yph.api.customization.service.ExtInventoryService;
import com.ly.yph.api.goods.dto.MaraQueryDto;
import com.ly.yph.api.goods.dto.ShopMaterialRelationUpdateDto;
import com.ly.yph.api.goods.service.ShopMaterialRelationService;
import com.ly.yph.api.goods.vo.MaraImportExcelVo;
import com.ly.yph.api.goods.vo.MaraImportRespVo;
import com.ly.yph.api.goods.vo.ShopMaterialRelationVo;
import com.ly.yph.api.openapi.service.SyncDhecService;
import com.ly.yph.api.openapi.v1.dto.ImportDhecMaterialDto;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.excel.util.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年11月16日
 */
@Api(tags = "管理后台 - 物料关系管理")
@RestController
@RequestMapping("goods/shopMaterialRelation")
@Slf4j
public class ShopMaterialRelationController {


    @Autowired
    private ShopMaterialRelationService shopMaterialRelationService;

    @Autowired
    private ExtInventoryService extInventoryService;

    @Autowired
    private SyncDhecService syncDhecService;

    /**
     * 查询
     */
    @ApiOperation("查询物料关系")
    @GetMapping("queryGoodsPage")
    @SaCheckPermission("business:shopMaterialRelation:query")
    public ServiceResult<PageResp<ShopMaterialRelationVo>> queryGoodsPage(PageReq pageReq, MaraQueryDto maraQueryDto) {
        return ServiceResult.succ(shopMaterialRelationService.queryPage(pageReq, maraQueryDto));
    }

    /**
     * 修改
     */
    @ApiOperation("修改物料关系")
    @PostMapping("updateMara")
    @SaCheckPermission("business:shopMaterialRelation:update")
    public ServiceResult<?> updateMara(@Valid @RequestBody ShopMaterialRelationUpdateDto updateDto) {
        shopMaterialRelationService.updateMara(updateDto);
        return ServiceResult.succ();
    }

    /**
     * 删除
     */
    @ApiOperation("删除物料关系")
    @PostMapping("deleteMara")
    @SaCheckPermission("business:shopMaterialRelation:delete")
    public ServiceResult<?> deleteMara(@Valid @RequestBody Long maraId) {
        shopMaterialRelationService.removeById(maraId);
        return ServiceResult.succ();
    }

//    /**
//     * 导出
//     */
//    @ApiOperation("excel批量导入友品汇标准品牌")
//    @SaCheckPermission("business:shopMaterialRelation:export")
//    public void exportMara(){
//
////        ExcelUtils.write(response, "采购申请单.xls", "采购申请单列表", PurchaseOrderExcelVo .class, exportList);
//    }

    /**
     * 商品固定价策略导入模板下载
     * @return
     */
    @ApiOperation("下载物料关系导入模板")
    @GetMapping("/MaraImportTemplate")
    @SaCheckPermission("business:shopMaterialRelation:query")
    public void MaraImportTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<MaraImportExcelVo> list = Arrays.asList(
                MaraImportExcelVo.builder().goodsCode("ZKH012312")
                        .companyCode("DFPV")
                        .maraMatnr("WDK00001")
                        .index(1)
                        .build()
        );
        ExcelUtils.write(response, "物料关系导入模板.xls", "Sheet1", MaraImportExcelVo.class, list);
    }

    /**
     * 导入
     */
    @ApiOperation("导入物料关系")
    @PostMapping("batchImportMara")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)})
    @SaCheckPermission("business:shopMaterialRelation:import")
    public ServiceResult<MaraImportRespVo> batchImportMara(@RequestParam(
            value = "file") MultipartFile file) throws IOException {
        List<MaraImportExcelVo> list = ExcelUtils.read(file, MaraImportExcelVo.class);
        return ServiceResult.succ(shopMaterialRelationService.batchImportMara(list));
    }

    @ApiOperation("导入发动机物料")
    @PostMapping("batchImportDhecMaterial")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)})
    public ServiceResult<?> batchImportDhecMaterial(@RequestParam(
            value = "file") MultipartFile file) throws IOException {
        List<ImportDhecMaterialDto> list = ExcelUtils.read(file, ImportDhecMaterialDto.class);
        return syncDhecService.batchImportDhecMaterial(list);
    }

    /**
     * 查询单个
     */

    /**
     * 新增
     */
    @ApiOperation("新增物料关系")
    @PostMapping("createMara")
    @SaCheckPermission("business:shopMaterialRelation:create")
    public ServiceResult<?> createMara(@Valid @RequestBody ShopMaterialRelationUpdateDto createDto) {
        shopMaterialRelationService.createMara(createDto);
        return ServiceResult.succ();
    }

    /**
     * 新增
     */
    @ApiOperation("发送订单数据到外部")
    @PostMapping("sendConfirmOrderToExt")
    public ServiceResult<?> sendConfirmOrderToExt(String companyCode, String purchaseNumber) {
        extInventoryService.sendConfirmOrderToExt(companyCode,purchaseNumber);
        return ServiceResult.succ();
    }

    @ApiOperation("监控寻源比价创建物料")
    @PostMapping("monitoringSeekMater")
    public ServiceResult<?> monitoringSeekMater() {
        shopMaterialRelationService.monitoringSeekMater();
        return ServiceResult.succ();
    }

}
