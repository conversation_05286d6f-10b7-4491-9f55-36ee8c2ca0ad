package com.ly.yph.api.orderlifecycle.factory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailForBillDto;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailForDeliveryDto;
import com.ly.yph.api.orderlifecycle.dto.UpdatePurchaseOrderInfoForConfirmDto;
import com.ly.yph.api.orderlifecycle.entity.OrderDetailPool;
import com.ly.yph.api.orderlifecycle.entity.PurchaseOrderInfoPool;
import com.ly.yph.api.orderlifecycle.enums.LifeCycleNodeStatusEnum;
import com.ly.yph.api.orderlifecycle.enums.OrderSalesChannelEnum;
import com.ly.yph.api.orderlifecycle.mapper.OrderDetailPoolMapper;
import com.ly.yph.api.orderlifecycle.mapper.PurchaseOrderInfoPoolMapper;
import com.ly.yph.api.orderlifecycle.service.CommentService;
import com.ly.yph.api.orderlifecycle.service.OrderDetailPoolService;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoPoolService;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;
import com.ly.yph.core.base.exception.types.ParameterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NissanOrderLifeCycleStrategy implements OrderLifeCycleStrategy {
    @Resource
    private PurchaseOrderInfoPoolMapper purchaseOrderInfoPoolMapper;
    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;
    @Resource
    private OrderDetailPoolMapper orderDetailPoolMapper;
    @Resource
    private OrderDetailPoolService orderDetailPoolService;
    @Resource
    private CommentService commentService;

    @Override
    public void savePurchaseOrderInfoForPreOrder(String purchaseNumber) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePurchaseOrderInfoForApprove(UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto) {
        Integer nissanPurchaseState = updatePurchaseOrderInfoForConfirmDto.getPurchaseState();
        String purchaseNumber = updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber();
        Date confirmTime = updatePurchaseOrderInfoForConfirmDto.getConfirmTime();

        PurchaseOrderInfoPool orderInfoPool = new LambdaQueryChainWrapper<>(purchaseOrderInfoPoolMapper)
                .eq(PurchaseOrderInfoPool::getOrderSalesChannel, OrderSalesChannelEnum.NISSANMALL.getCode())
                .eq(PurchaseOrderInfoPool::getPurchaseNumber, purchaseNumber)
                .oneOpt().orElseThrow(() -> new ParameterException(StrUtil.format("该采购单尚未同步:{}", purchaseNumber)));
        // 更新订单主表信息
        UpdateWrapper<PurchaseOrderInfoPool> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(PurchaseOrderInfoPool::getId, orderInfoPool.getId())
                .set(PurchaseOrderInfoPool::getApprovalOpinion, updatePurchaseOrderInfoForConfirmDto.getApprovalOpinion())
                .set(PurchaseOrderInfoPool::getPurchaseState, nissanPurchaseState == 0 ? 20 : 30);
        purchaseOrderInfoPoolService.update(updateWrapper);

        new LambdaUpdateChainWrapper<>(orderDetailPoolMapper)
                .eq(OrderDetailPool::getPurchaseInfoId, orderInfoPool.getId())
                .set(OrderDetailPool::getOrderDetailState, nissanPurchaseState == 1 ? 20 : 0)
                .set(OrderDetailPool::getConfirmTime,confirmTime)
                .update();
        if (nissanPurchaseState == 2) {
            new LambdaUpdateChainWrapper<>(orderDetailPoolMapper)
                    .eq(OrderDetailPool::getPurchaseInfoId, orderInfoPool.getId())
                    .in(OrderDetailPool::getGoodsCode, updatePurchaseOrderInfoForConfirmDto.getUpdateOrderDetailDtoList()
                            .stream().map(UpdateOrderDetailDto::getGoodsCode).collect(Collectors.toList()))
                    .set(OrderDetailPool::getOrderDetailState, 20)
                    .set(OrderDetailPool::getConfirmTime,confirmTime)
                    .update();
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePurchaseOrderInfoForDelivery(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        Map<String, List<UpdateOrderDetailDto>> orderDetailIdMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.groupingBy(UpdateOrderDetailDto::getOrderDetailId));
        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.NISSANMALL.getCode())
                .in(OrderDetailPool::getOrderDetailId, orderDetailIdMap.keySet())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getDeliveryNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtil.isEmpty(orderDetailPoolList)) {
            log.info("明细：{}", orderDetailIdMap.keySet());
            throw new ParameterException("未查询到3SM的订单明细数据");
        }

        Map<String, OrderDetailPool> orderDetailPoolMap = orderDetailPoolList.stream().collect(Collectors.toMap(OrderDetailPool::getOrderDetailId, Function.identity()));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (String orderDetailId : orderDetailIdMap.keySet()) {

            OrderDetailPool orderDetailPool = orderDetailPoolMap.get(orderDetailId);
            if (orderDetailPool == null) {
                log.info("3sms发货更新数据，未查询到明细id:{}的订单明细数据", orderDetailId);
                continue;
            }
            List<UpdateOrderDetailDto> updateOrderDetailDtoList = orderDetailIdMap.get(orderDetailId);

            BigDecimal deliveryNum = updateOrderDetailDtoList.stream().map(UpdateOrderDetailDto::getDeliveryNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            OrderDetailPool update = new OrderDetailPool();
            update.setId(orderDetailPool.getId());
            update.setDeliveryNum(orderDetailPool.getDeliveryNum().add(deliveryNum));
            update.setOrderDetailState(30);
            BigDecimal deliveryStatusFlag = orderDetailPool.getConfirmNum().subtract(update.getDeliveryNum());
            if (deliveryStatusFlag.compareTo(BigDecimal.ZERO) > 0) {
                update.setDeliveryStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
            } else if (deliveryStatusFlag.compareTo(BigDecimal.ZERO) == 0) {
                update.setDeliveryStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
            } else {
                update.setDeliveryStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateList.add(update);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseOrderInfoForProperDelivery(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        Map<String, List<UpdateOrderDetailDto>> orderDetailIdMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.groupingBy(UpdateOrderDetailDto::getOrderDetailId));

        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, OrderSalesChannelEnum.NISSANMALL.getCode())
                .in(OrderDetailPool::getOrderDetailId, orderDetailIdMap.keySet())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getProperDeliveryNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if (CollectionUtil.isEmpty(orderDetailPoolList)) {
            log.info("明细：{}", orderDetailIdMap.keySet());
            throw new ParameterException("未查询到3SM的订单明细数据");
        }

        Map<String, OrderDetailPool> orderDetailPoolMap = orderDetailPoolList.stream().collect(Collectors.toMap(OrderDetailPool::getOrderDetailId, Function.identity()));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (String orderDetailId : orderDetailIdMap.keySet()) {
            OrderDetailPool orderDetailPool = orderDetailPoolMap.get(orderDetailId);
            if (orderDetailPool == null) {
                log.info("3sms发货更新数据，未查询到明细id:{}的订单明细数据", orderDetailId);
                continue;
            }

            List<UpdateOrderDetailDto> updateOrderDetailDtoList = orderDetailIdMap.get(orderDetailId);
            OrderDetailPool update = new OrderDetailPool();
            update.setId(orderDetailPool.getId());
            BigDecimal reduce = updateOrderDetailDtoList.stream().map(UpdateOrderDetailDto::getProperNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            update.setProperDeliveryNum(orderDetailPool.getProperDeliveryNum().add(reduce));
            updateList.add(update);
        }

        orderDetailPoolService.updateBatchById(updateList);

    }

    @Override
    public void updatePurchaseOrderInfoForReceive(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {

    }

    @Override
    public void updatePurchaseOrderInfoForCheck(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {

    }

    @Override
    public void updatePurchaseOrderInfoForBillOut(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入【3sm】出账环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【出账】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getCustomerCheckoutNum,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);
        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【出账】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForBillOut(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());
    }

    @Override
    public void updatePurchaseOrderInfoForBillReconciliation(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入[3sm]账单对账环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【对账】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【对账】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForBillReconciliation(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());

    }

    @Override
    public void updatePurchaseOrderInfoForDeleteBillDetail(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入【3sm】账单明细删除环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【删除账单明细】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getCustomerCheckoutNum,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getCustomerInvoicedNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【删除账单明细】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForDeleteBillDetail(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());

    }

    @Override
    public void updatePurchaseOrderInfoForApplyInvoice(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入[3sm]发票申请环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【开票】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds, 1000).forEach(batchOrderDetailIds -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batchOrderDetailIds)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getCustomerInvoicingNum);
            List<OrderDetailPool> batchList = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(batchList);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【开票申请】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForApplyInvoice(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());

    }

    @Override
    public void updatePurchaseOrderInfoForInvoiceApprove(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入[3sm]发票审批环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【开票审批】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds,1000).forEach(batch->{
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batch)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getCustomerInvoicedNum,
                            OrderDetailPool::getCustomerInvoicedMoney,
                            OrderDetailPool::getCustomerInvoicingNum,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【开票审批】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForInvoiceApprove(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList(),
                updateOrderDetailForBillDto.getInvoiceApproveType());

    }

    @Override
    public void updatePurchaseOrderInfoForInvoiceRepayment(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入[3sm]发票结算环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【发票结算】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        List<OrderDetailPool> orderDetailPools = new ArrayList<>();
        Lists.partition(orderDetailIds, 1000).forEach(batch -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batch)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum,
                            OrderDetailPool::getCustomerSettlementNum,
                            OrderDetailPool::getCustomerSettlementMoney);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【发票结算】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForInvoiceRepayment(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());

    }

    @Override
    public void updatePurchaseOrderInfoForInvoiceOffset(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入[3sm]发票红冲环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【发票红冲】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds, 1000).forEach(batch -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batch)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum,
                            OrderDetailPool::getCustomerInvoicedNum,
                            OrderDetailPool::getCustomerInvoicingNum,
                            OrderDetailPool::getCustomerInvoicedMoney,
                            OrderDetailPool::getCustomerInvoiceStatus,
                            OrderDetailPool::getCustomerSettlementStatus,
                            OrderDetailPool::getCustomerSettlementMoney,
                            OrderDetailPool::getCustomerSettlementNum);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【发票红冲】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForInvoiceOffset(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList(),
                updateOrderDetailForBillDto.getInvoiceSettleType());

    }

    @Override
    public void updatePurchaseOrderInfoForAfterSale(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {

    }

    @Override
    public void updatePurchaseOrderInfoForDeliveryDelete(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        return;
    }

    @Override
    public void updatePurchaseOrderInfoForSupplierInvoice(List<SupplierInvoiceDelDto> supplierInvoiceDelDtos) {
        commentService.updatePurchaseOrderInfoForSupplierInvoice(supplierInvoiceDelDtos);
    }
}
