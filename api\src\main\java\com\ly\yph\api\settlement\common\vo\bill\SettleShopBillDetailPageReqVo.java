package com.ly.yph.api.settlement.common.vo.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("账单明细分页查询对象")
public class SettleShopBillDetailPageReqVo {

    @ApiModelProperty("采购单号")
    private String purchaseNumber;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("电商订单号")
    private String supplierOrderNumber;

    @ApiModelProperty("商城编码")
    private String goodsCode;

    @ApiModelProperty("电商sku")
    private String goodsSku;

    @ApiModelProperty("开票主体")
    private String invoiceSubject;

    @ApiModelProperty("下单人部门名称")
    private String applyDeptName;

    private Integer applyDeptId;

    @ApiModelProperty("下单人姓名")
    private String applyUserName;

    private Integer applyUserId;

    @ApiModelProperty("主账单id")
    private Long billId;

    @ApiModelProperty("对账状态")
    private Integer reconciliationStatus;

    @ApiModelProperty("对账人id")
    private Long reconciliationUserId;

    @ApiModelProperty("对账人")
    private String reconciliationUserName;

    @ApiModelProperty("供应商来源类型")
    private String storeDataSource;

    @ApiModelProperty("对账年度")
    private Integer checkYear;

    @ApiModelProperty("对账月度")
    private Integer checkMonth;

    @ApiModelProperty("对账月度集合")
    private List<Integer> checkMonthList;

    @ApiModelProperty("账单编号")
    private String billSn;

    @ApiModelProperty("匹配标记")
    private Integer billMatchFlag;

    private Integer customerSourceType;//客户类型 2是友福利

    private Integer isPlatformReconciliation;//平台内外对账

    private String customerCode;

    /**
     * 客户类型 0:客户，1:供应商
     */
    private Integer billCustomerType;

    private List<Integer> reconciliationStatusList;
    /**
     * 收货人
     */
    private String addressName;

    @ApiModelProperty("出账起始时间")
    private String billOutTimeStart;

    @ApiModelProperty("出账截至时间")
    private String billOutTimeEnd;

    @ApiModelProperty(" 账单类型 0：正向账单明细，1:负向账单明细")
    private Integer billDetailType;


    @ApiModelProperty("税率")
    private Integer taxRate;

    @ApiModelProperty("账单暂存标记 0:未暂存 1:已暂存")
    private Integer billStagingFlag;


    @ApiModelProperty("导出明细类型 0：账单 1：邮费")
    private Integer billInvoiceType;
}
