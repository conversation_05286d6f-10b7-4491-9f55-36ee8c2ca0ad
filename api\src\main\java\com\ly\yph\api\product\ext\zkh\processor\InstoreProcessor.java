package com.ly.yph.api.product.ext.zkh.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.product.ext.common.BaseDataProcessor;
import com.ly.yph.api.product.ext.common.enums.MessageStatusEnum;
import com.ly.yph.api.product.ext.zkh.config.ZKHConfig;
import com.ly.yph.api.product.ext.zkh.entity.BackupZkhGoods;
import com.ly.yph.api.product.ext.zkh.entity.QueueMsgSupplierProductProcessInfo;
import com.ly.yph.api.product.ext.zkh.service.BackupZkhGoodsService;
import com.ly.yph.api.product.ext.zkh.service.QueueMsgSupplierProductProcessInfoService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * step3 获得商品详情
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Slf4j
@Service("ZKHInstoreProcessor")
public class InstoreProcessor extends BaseDataProcessor<Long> {
    @Getter
    protected String processName = "ZKH入备份库";
    @Resource
    private ZKHConfig config;
    @Resource
    private BackupZkhGoodsService backSrv;
    @Resource
    private QueueMsgSupplierProductProcessInfoService queueSrv;
    @Resource
    private CodeGeneral codeGeneral;
    @Resource
    private SystemTenantService systemTenantService;

    @Override
    public List<Long> supplier(int count) {
        // 这里只保存了商品新增的数据
        return queueSrv.selectIdBySupplierAndStatus(count, MessageStatusEnum.INIT, config.getCode());
    }

    /**
     * sku id 的信息每次处理的时候可以先删除之前的，再存现在的数据
     */
    @Override
    @DistributedLock(value = "zkh_instore_process", key = "#id", waitLock = false)
    public void processItem(Long id) {
        // 从队列中获取未处理的消息
        var queueEntity = queueSrv.getById(id);
        // 所有租户都需要处理数据
        List<Long> tenantIds = systemTenantService.getTenantIds();
        TenantUtils.execute(tenantIds, () -> {
            BackupZkhGoods sEnt = backSrv.selectBySku(queueEntity.getSkuId());
            // 记录商品进度
            BackupZkhGoods backupZkhGoods = doProcess(queueEntity, sEnt);
            if (backupZkhGoods.getId() == null) {
                backSrv.save(backupZkhGoods);
            } else {
                backSrv.getBaseMapper().updateByIdx(backupZkhGoods);
            }
        });
        queueSrv.updateInStoreSuccess(id);
    }

    @Override
    @DistributedLock(value = "zkh_instore_process_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Long> ids) {
        val msg = queueSrv.getBaseMapper().selectSimple(ids);
        TenantUtils.execute(systemTenantService.getTenantIds(), () -> {
            val failArray = new ArrayList<String>();
            val succArray = new ArrayList<String>();
            val entities = new ArrayList<BackupZkhGoods>();
            var backups = backSrv.getBaseMapper().selectBySkuIn(msg.stream().map(QueueMsgSupplierProductProcessInfo::getSkuId).collect(Collectors.toList()));
            // skuId和实体的映射，生成map
            var backupMap = backups.stream().collect(Collectors.toMap(BackupZkhGoods::getSku, item -> item));
            msg.forEach(item -> {
                try {
                    // 记录商品进度
                    entities.add(doProcess(item, backupMap.get(item.getSkuId())));
                    succArray.add(item.getSkuId());
                } catch (Exception ex) {
                    log.error("zkh to backup error, skuId:{}", item.getSkuId(), ex);
                    failArray.add(item.getSkuId());
                }
            });
            var  entities_list = entities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BackupZkhGoods::getSku))), ArrayList::new));
            if (!entities_list.isEmpty()){
                // 去掉entities中sku相同的项
                backSrv.saveBatch(entities_list.stream().filter(item -> item.getId() == null).collect(Collectors.toList()));
            }
            if (!entities_list.isEmpty())
                backSrv.getBaseMapper().updateByIdxBatch(entities_list.stream().filter(item -> item.getId() != null).collect(Collectors.toList()));

            if (!succArray.isEmpty())
                queueSrv.getBaseMapper().updateInstoreInfo(1, new ArrayList<>(succArray),config.getCode());
            if (!failArray.isEmpty())
                queueSrv.getBaseMapper().updateInstoreInfo(2, new ArrayList<>(failArray),config.getCode());
        });
    }

    private BackupZkhGoods doProcess(QueueMsgSupplierProductProcessInfo queueEntity, BackupZkhGoods sEnt) {
        // 解析数据并根据skuId查询商品信息
        BackupZkhGoods resp = JSON.parseObject(queueEntity.getInfo(), BackupZkhGoods.class);
        
        if (sEnt == null) {
            sEnt = new BackupZkhGoods();
            sEnt.setGoodCode(codeGeneral.getProductCode(config.getCode()));
            sEnt.setCreateTime(LocalDateTime.now());
        }

        // 更新商品信息
        BeanUtil.copyProperties(resp, sEnt);
        sEnt.setSynchronize(0);
        sEnt.setValidateFlag((byte) 0);
        sEnt.setIsEnable("1");
        sEnt.setPriceProcFlag((byte) 0);
        sEnt.setShiftProcFlag((byte) 0);
        sEnt.setStockProcFlag((byte) 0);
        sEnt.setImageProcFlag((byte) 0);
        sEnt.setCanSaleProcFlag((byte) 0);
        sEnt.setCategoryProcFlag((byte) 0);

        // 更新商品名称和描述
        sEnt.setName(Optional.ofNullable(resp.getName()).map(name -> name.split("\\s+")[1]).orElse(""));
        sEnt.setDescription(resp.getName());

        // 更新商品分类
        JSONArray cate = JSON.parseArray(sEnt.getCategory());
        int index = cate.size() - 1;

        while (index >= 0 && StrUtil.isBlank(cate.getString(index))) {
            index--;
        }

        if (index >= 0) {
            sEnt.setCategory(cate.getString(index));
            sEnt.setCategoryName(JSON.parseArray(sEnt.getCategoryName()).getString(index));
        }

        // 更新商品分组和商品编码
        sEnt.setProductGroup(Optional.ofNullable(resp.getGroup()).orElse(""));
        sEnt.setWareQd(Optional.ofNullable(resp.getWareQD()).orElse(""));

        // 保存商品信息和更新队列状态
        sEnt.setUpdateTime(LocalDateTime.now());
        return sEnt;
    }
}