package com.ly.yph.api.goods.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.yph.api.goods.controller.vo.GoodsPoolExportVO;
import com.ly.yph.api.goods.controller.vo.ImportGoodsPoolFailVO;
import com.ly.yph.api.goods.dto.*;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolEntity;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolSortEntity;
import com.ly.yph.api.goods.entity.DfmallGoodsPoolSubEntity;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.enums.GoodsLabelEnum;
import com.ly.yph.api.goods.enums.GoodsPoolLevelEnum;
import com.ly.yph.api.goods.es.EsUpdateType;
import com.ly.yph.api.goods.es.manager.GoodsEsProcessor;
import com.ly.yph.api.goods.mapper.DfmallGoodsPoolMapper;
import com.ly.yph.api.goods.mapper.DfmallGoodsPoolSortMapper;
import com.ly.yph.api.goods.vo.DfmallGoodsPoolPageVo;
import com.ly.yph.api.goods.vo.DfmallGoodsPoolVo;
import com.ly.yph.api.goods.vo.LiteFlowsParamVo;
import com.ly.yph.api.openapi.v1.vo.mesage.MessageTypeEnum;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.enums.OrganizationTypeEnum;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.organization.service.SystemUsersService;
import com.ly.yph.api.product.ext.common.dto.request.BatchGetStockReq;
import com.ly.yph.api.product.ext.common.dto.request.RegionDto;
import com.ly.yph.api.product.ext.common.dto.response.RemoteStockInfoResp;
import com.ly.yph.api.product.ext.common.manage.RemoteInfoManage;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierDetail;
import com.ly.yph.api.supplier.exception.SupplierException;
import com.ly.yph.api.supplier.service.ShopSupplierDetailService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.utils.OpenApiMessageUtil;
import com.ly.yph.api.zone.dto.GoodsPushDownDto;
import com.ly.yph.api.zone.dto.SaveZoneDto;
import com.ly.yph.api.zone.entity.GoodsBlacklistEntity;
import com.ly.yph.api.zone.entity.GoodsZoneClassEntity;
import com.ly.yph.api.zone.entity.GoodsZoneEntity;
import com.ly.yph.api.zone.service.GoodsBlacklistService;
import com.ly.yph.api.zone.service.GoodsZoneClassService;
import com.ly.yph.api.zone.service.GoodsZoneService;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.AssertUtil;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.thread.ThreadUtil;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.core.util.StringHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品池(DfmallGoodsPool)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-19 14:57:34
 */
@Service
@Slf4j
public class DfmallGoodsPoolService extends ServiceImpl<DfmallGoodsPoolMapper, DfmallGoodsPoolEntity> {

    public static final int B_SIZE = 1000;
    @Resource
    private DfmallGoodsPoolMapper dfmallGoodsPoolMapper;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private DfmallGoodsPoolSubService dfmallGoodsPoolSubService;

    @Resource
    private ShopGoodsService shopGoodsService;

    @Resource
    private ShopSupplierService shopSupplierService;

    @Resource
    private GoodsEsProcessor goodsEsProcessor;

    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;

    @Resource
    private SystemOrganizationService organizationService;
    @Resource
    private OpenApiMessageUtil openApiMessageUtil;
    /**
     * 互相调用,所以这里延迟
     */
    @Resource
    @Lazy
    private GoodsZoneService goodsZoneService;
    /**
     * 互相调用,所以这里延迟
     */
    @Resource
    private GoodsZoneClassService goodsZoneClassService;

    @Resource
    private GoodsBlacklistService blacklistService;

    @Resource
    private MailService mailService;

    @Resource
    private SystemUsersService usersService;

    @Resource
    private DfmallGoodsPoolSortMapper goodsPoolSortMapper;

    @Resource
    private ShopPurchaseSubOrderDetailService orderDetailService;

    @Resource
    private RemoteInfoManage remoteInfoManage;

    @Resource
    private ShopSupplierDetailService supplierDetailService;


    public PageResp<DfmallGoodsPoolPageVo> queryPage(PageReq pageReq, DfmallGoodsPoolQueryDto goodsPoolQueryDto) {
        if(LocalUserHolder.get().getOrganizationType().equals(OrganizationTypeEnum.PURCHASE.getCode())){
            goodsPoolQueryDto.setCompanyCode(LocalUserHolder.get().getEntityOrganizationCode());
        }
        IPage<DfmallGoodsPoolPageVo> iPage = dfmallGoodsPoolMapper.queryPage(DataAdapter.adapterPageReq(pageReq), goodsPoolQueryDto);
        return DataAdapter.adapterPage(iPage, DfmallGoodsPoolPageVo.class);
    }

    public List<DfmallGoodsPoolPageVo> querySimpleList(DfmallGoodsPoolQueryDto goodsPoolQueryDto) {
        if(LocalUserHolder.get().getOrganizationType().equals(OrganizationTypeEnum.PURCHASE.getCode())){
            goodsPoolQueryDto.setCompanyCode(LocalUserHolder.get().getEntityOrganizationCode());
        }
        return dfmallGoodsPoolMapper.querySimpleList(goodsPoolQueryDto);
    }

    public DfmallGoodsPoolVo queryGoodsPoolById(Long id) {
        DfmallGoodsPoolEntity dfmallGoodsPool = dfmallGoodsPoolMapper.selectById(id);
        DfmallGoodsPoolVo dfmallGoodsPoolVo = new DfmallGoodsPoolVo();
        if (null != dfmallGoodsPool) {
            BeanUtil.copyProperties(dfmallGoodsPool, dfmallGoodsPoolVo);
            dfmallGoodsPoolVo.setClassTree(this.queryGoodsPoolClass(dfmallGoodsPoolVo.getId().toString()));
            dfmallGoodsPoolVo.setShopSupplierList(this.queryGoodsPoolSupplier(dfmallGoodsPoolVo.getId().toString()));
            switch (dfmallGoodsPool.getGoodsPoolLevel()) {
                case 2:
                    dfmallGoodsPoolVo.setGoodsPoolLevelName("企业");
                    break;
                case 3:
                    dfmallGoodsPoolVo.setGoodsPoolLevelName("专区");
                    break;
                default:
                    dfmallGoodsPoolVo.setGoodsPoolLevelName("平台");
            }
        }
        return dfmallGoodsPoolVo;
    }

    /**
     * 查询商品池的全部商品ID
     *
     * @param goodsPoolId 商品池ID
     * @return 返回结果
     */
    public List<Long> queryGoodsPoolGoodsId(Long goodsPoolId) {
        return dfmallGoodsPoolSubService.queryGoodsPoolGoodsId(goodsPoolId);
    }

    /**
     * 查询企业商品池
     * @param companyCode 企业编码
     * @return 返回结果
     */
    public DfmallGoodsPoolEntity queryCompanyPool(String companyCode){
        LambdaQueryWrapperX<DfmallGoodsPoolEntity> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(DfmallGoodsPoolEntity::getCompanyCode,companyCode)
                .eq(DfmallGoodsPoolEntity::getGoodsPoolLevel,GoodsPoolLevelEnum.POOL_LEVEL_COMPANY.getValue());
        return this.getOne(queryWrapperX);
    }

    public DfmallGoodsPoolEntity queryPlatformPool(){
        LambdaQueryWrapperX<DfmallGoodsPoolEntity> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(DfmallGoodsPoolEntity::getGoodsPoolLevel,GoodsPoolLevelEnum.POOL_LEVEL_PLATFORM.getValue());
        return this.getOne(queryWrapperX);
    }

    /**
     * 查询商品池的分类树
     *
     * @param goodsPoolId 商品池ID
     * @return 返回结果
     */
    public List<Tree<String>> queryGoodsPoolClass(String goodsPoolId) {
        List<Long> goodsPoolIds = Arrays.stream(goodsPoolId.split(",")).map(Long::valueOf).collect(Collectors.toList());
        return dfmallGoodsPoolSubService.queryGoodsPoolClass(goodsPoolIds);
    }

    /**
     * 查询商品池的供应商
     *
     * @param goodsPoolId 商品池ID
     * @return  返回结果
     */
    public List<ShopSupplier> queryGoodsPoolSupplier(String goodsPoolId) {
        List<Long> goodsPoolIds = Arrays.stream(goodsPoolId.split(",")).map(Long::valueOf).collect(Collectors.toList());
        return dfmallGoodsPoolSubService.queryGoodsPoolSupplier(goodsPoolIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public DfmallGoodsPoolEntity saveGoodsPool(DfmallGoodsPoolSave goodsPoolSaveDto, MultipartFile file) {
        DfmallGoodsPoolEntity goodsPoolEntity = new DfmallGoodsPoolEntity();
        BeanUtils.copyProperties(goodsPoolSaveDto, goodsPoolEntity);
        GoodsZoneEntity zone = new GoodsZoneEntity();
        if (null != goodsPoolSaveDto.getId()) {
            goodsPoolEntity.setCompanyCode(goodsPoolSaveDto.getCompanyCode());
            this.updateById(goodsPoolEntity);
            zone = goodsZoneService.getOne(new LambdaQueryWrapperX<GoodsZoneEntity>().eq(GoodsZoneEntity::getGoodsPoolId,goodsPoolSaveDto.getId()));
        } else {
            LambdaQueryWrapperX<DfmallGoodsPoolEntity> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eqIfPresent(DfmallGoodsPoolEntity::getCompanyCode, goodsPoolEntity.getCompanyCode())
                    .eqIfPresent(DfmallGoodsPoolEntity::getGoodsPoolLevel, goodsPoolSaveDto.getGoodsPoolLevel());
            boolean check = goodsPoolSaveDto.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_PLATFORM.getValue())
                    || goodsPoolSaveDto.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_COMPANY.getValue());

            if (check && this.count(queryWrapperX) >= 1 ) {
                throw new ParameterException("[" + goodsPoolEntity.getCompanyCode() + "]已存在相同商品池，不能重复新增");
            }


            this.save(goodsPoolEntity);

            if(GoodsPoolLevelEnum.POOL_LEVEL_COMPANY.getValue().equals(goodsPoolSaveDto.getGoodsPoolLevel())){
                //企业池默认新增对应的专区
                zone = this.saveZone(goodsPoolEntity);
            }
        }
        this.saveGoodsPoolSub(goodsPoolSaveDto, goodsPoolEntity.getId());
        if (null != file && !file.isEmpty()) {

            this.goodsPoolImportGoods(file, goodsPoolEntity.getId(),zone.getId());
        }

        this.refreshPool(goodsPoolSaveDto.getRefresh(),goodsPoolSaveDto.getRefreshTime(),goodsPoolEntity.getId());

        return goodsPoolEntity;

    }

    public GoodsZoneEntity saveZone(DfmallGoodsPoolEntity goodsPoolEntity){
        SaveZoneDto saveZoneDto = new SaveZoneDto();
        saveZoneDto.setZoneName(goodsPoolEntity.getGoodsPoolName()+"首页专区");
        saveZoneDto.setIsClose(1);
        saveZoneDto.setSort(1);
        saveZoneDto.setIsAllSearch(1);
        saveZoneDto.setLayoutType(1);
        saveZoneDto.setPayType(1);
        saveZoneDto.setCartMergeSubmit(1);
        saveZoneDto.setZoneType(0);
        saveZoneDto.setGoodsPoolId(goodsPoolEntity.getId());
        LoginUser user = LocalUserHolder.get();
        saveZoneDto.setCompanyOrgId(user.getEntityOrganizationId());
        if(!StringHelper.IsEmptyOrNull(goodsPoolEntity.getCompanyCode())){
            SystemOrganization organization = organizationService.getOrganizationByCode(goodsPoolEntity.getCompanyCode());
            saveZoneDto.setCompanyOrgId(organization.getId());
        }
        return goodsZoneService.saveGoodsZone(saveZoneDto);

    }

    public void saveGoodsId(Long poolId, String goodsIdLst) {
        List<DfmallGoodsPoolSubEntity> subPoolLst = new ArrayList<>();
        List<Long> ids = new ArrayList<>();

        var pool = this.getById(poolId);
        AssertUtil.npeIsShit(pool, String.valueOf(poolId));

        var query = new LambdaQueryWrapperX<ShopGoods>();
        query.select(ShopGoods::getGoodsId,
                ShopGoods::getSupplierCode,
                ShopGoods::getThirdLevelGcid
        ).in(ShopGoods::getGoodsId, goodsIdLst.split(","));
        var goodsLst = shopGoodsService.list(query);

        for (var goodsItm : goodsLst) {
            var querySub = new LambdaQueryWrapperX<DfmallGoodsPoolSubEntity>();
            querySub.eq(DfmallGoodsPoolSubEntity::getGoodsId, goodsItm.getGoodsId())
                    .eq(DfmallGoodsPoolSubEntity::getGoodsPoolId, poolId);
            long count = dfmallGoodsPoolSubService.count(querySub);
            if (count == 0) {
                var subPool = new DfmallGoodsPoolSubEntity();
                subPool.setGoodsPoolId(poolId);
                subPool.setGoodsId(goodsItm.getGoodsId());
                subPool.setSupplierCode(goodsItm.getSupplierCode());
                subPool.setStandardClassId(Long.valueOf(goodsItm.getThirdLevelGcid()));
                ids.add(goodsItm.getGoodsId());
                subPoolLst.add(subPool);
            } else {
                log.info("goods_sub_pool[{}] exist_goods [{}]", poolId, goodsItm.getGoodsId());
            }
            dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolId(poolId, goodsItm.getGoodsId());
        }
        if (CollectionUtil.isEmpty(subPoolLst)) {
            return;
        }

        dfmallGoodsPoolSubService.saveBatch(subPoolLst);
        CollUtil.split(ids, 1000).parallelStream().forEach(itemIds -> goodsEsProcessor.updateIndex(itemIds, EsUpdateType.GOODS_POOL));
    }

    /**
     * 保存商品池子表
     *
     * @param goodsPoolSaveDto 商品池入参
     * @param goodsPoolId   商品池ID
     */
    public void saveGoodsPoolSub(DfmallGoodsPoolSave goodsPoolSaveDto,
                                 Long goodsPoolId) {
        List<DfmallGoodsPoolSubEntity> saveGoodsPoolSubList = new ArrayList<>();

        if (!StringHelper.IsEmptyOrNull(goodsPoolSaveDto.getGoodsIds())) {
            List<String> goodsIds = Arrays.asList(goodsPoolSaveDto.getGoodsIds().split(","));
            LambdaQueryWrapperX<DfmallGoodsPoolSubEntity> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.in(DfmallGoodsPoolSubEntity::getGoodsId, goodsIds)
                    .eq(DfmallGoodsPoolSubEntity::getGoodsPoolId, goodsPoolId);

            List<DfmallGoodsPoolSubEntity> oldGoodsPoolList = dfmallGoodsPoolSubService.list(queryWrapperX);

            Map<Long, DfmallGoodsPoolSubEntity> goodsPoolSubMap = new HashMap<>(oldGoodsPoolList.size());
            oldGoodsPoolList.forEach(goodsPoolSubEntity -> goodsPoolSubMap.put(goodsPoolSubEntity.getGoodsId(), goodsPoolSubEntity));

            goodsIds.forEach(goodsId -> {
                if(!StringHelper.IsEmptyOrNull(goodsId)){
                    DfmallGoodsPoolSubEntity goodsPoolSubEntity = goodsPoolSubMap.get(Long.valueOf(goodsId));
                    if (null == goodsPoolSubEntity) {
                        goodsPoolSubEntity = new DfmallGoodsPoolSubEntity();
                        goodsPoolSubEntity.setGoodsId(Long.valueOf(goodsId));
                        goodsPoolSubEntity.setGoodsPoolId(goodsPoolId);

                        LambdaQueryWrapperX<ShopGoods> queryGoods = new LambdaQueryWrapperX<>();
                        queryGoods.select(ShopGoods::getThirdLevelGcid, ShopGoods::getSupplierCode).eq(ShopGoods::getGoodsId, goodsId);
                        ShopGoods shopGoods = shopGoodsService.getOne(queryGoods);
                        goodsPoolSubEntity.setStandardClassId(Long.valueOf(shopGoods.getThirdLevelGcid()));
                        goodsPoolSubEntity.setSupplierCode(shopGoods.getSupplierCode());
                        goodsPoolSubEntity.setZoneGoodsType(goodsPoolSaveDto.getZoneGoodsType());
                        saveGoodsPoolSubList.add(goodsPoolSubEntity);
                    }
                }

            });
        }

        if (CollectionUtil.isNotEmpty(saveGoodsPoolSubList)) {
            CollUtil.split(saveGoodsPoolSubList, 1000).parallelStream().forEach(saveBach -> {
                // 数据量太大分多次提交
                dfmallGoodsPoolSubService.saveBatch(saveBach);
            });
        }

        // 发送消息
        DfmallGoodsPoolEntity dfmallGoodsPoolEntity = dfmallGoodsPoolMapper.selectById(goodsPoolId);
        if(dfmallGoodsPoolEntity!=null){
            String companyCode = dfmallGoodsPoolEntity.getCompanyCode();
            if (openApiConfig.getPushProductCustomers().contains(companyCode)) {
                List<String> goodsIds = Arrays.asList(goodsPoolSaveDto.getGoodsIds().split(","));
                if(!goodsIds.isEmpty()){
                    List<ShopGoods> shopGoods = shopGoodsService.getBaseMapper().selectByGoodsIdIn(goodsIds.stream().map(Long::valueOf).collect(Collectors.toList()));
                    if(!shopGoods.isEmpty()){
                        shopGoods.forEach(item -> openApiMessageUtil.sendMessage(companyCode, MessageTypeEnum.PUSH_GOODS, MapUtil.of("goodsCode", item.getGoodsCode())));
                    }
                }
            }
        }
    }

    /**
     * 导入SKU
     *
     * @param file 导入文件
     * @param goodsPoolId   导入商品池ID
     * @return 返回结果
     */
    public ImportGoodsPoolFailVO goodsPoolImportGoods(MultipartFile file, Long goodsPoolId, Long zoneId) {
        // 1. Excel读取与基础清洗
        List<DfmallGoodsPoolImportSkuDto> importSkuDtoList = readExcelToSupplierGoods(file);
        importSkuDtoList = importSkuDtoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getGoodsSku()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DfmallGoodsPoolImportSkuDto::getGoodsSku))),
                        ArrayList::new
                ));

        ImportGoodsPoolFailVO respVO = ImportGoodsPoolFailVO.builder()
                .failureList(new LinkedHashMap<>())
                .createList(new ArrayList<>())
                .build();

        // 2. 数据准备阶段
        final Map<String, String> supplierMap = prepareSupplierMap();

        final Set<Long> existingPoolGoodsIds = new HashSet<>(
                dfmallGoodsPoolSubService.queryGoodsIdByPoolId(goodsPoolId)
        );
        final Map<String, ShopGoods> goodsMap = prepareGoodsMap(importSkuDtoList);
        final Map<String, String> zoneClassMap = prepareZoneClassMap(zoneId, importSkuDtoList);

        // 3. 商品池信息准备
        DfmallGoodsPoolEntity goodsPool = getById(goodsPoolId);
        DfmallGoodsPoolEntity companyPool = prepareCompanyPool(goodsPool);
        SystemOrganization organization = prepareOrganization(goodsPool);

        // 4. 核心处理逻辑
        List<DfmallGoodsPoolSubEntity> newPoolSubEntities = new ArrayList<>();
        List<DfmallGoodsPoolSortEntity> newSortEntities = new ArrayList<>();
        List<DfmallGoodsPoolImportSkuDto> newLabelDtos = new ArrayList<>();
        Set<Long> allGoodsIds = new HashSet<>();
        boolean requiresSortReset = false;

        // 5. 批量黑名单检查（优化性能）[5](@ref)
        Map<String, Boolean> blacklistMap = checkBlacklist(importSkuDtoList, supplierMap);

        for (DfmallGoodsPoolImportSkuDto dto : importSkuDtoList) {
            String supplierName = dto.getSupplierName();
            String goodsSku = dto.getGoodsSku();
            String labelCode = GoodsLabelEnum.getCodeByName(dto.getGoodsLabel());

            // 6. 统一校验链 [8,9](@ref)
            if (!validateGoodsRecord(dto, supplierMap, goodsMap, blacklistMap, respVO)) {
                continue;
            }

            ShopGoods shopGoods = goodsMap.get(supplierMap.get(supplierName) + goodsSku);
            Long goodsId = shopGoods.getGoodsId();
            allGoodsIds.add(goodsId);

            // 7. 跳过已存在商品
            if (existingPoolGoodsIds.contains(goodsId)) continue;

            // 8. 创建商品池条目
            createPoolSubEntity(dto, zoneClassMap, newPoolSubEntities, shopGoods, goodsPoolId);

            // 9. 排序处理
            if (dto.getSortNo() != null) {
                requiresSortReset = true;
                newSortEntities.add(createSortEntity(goodsPoolId, shopGoods, dto.getSortNo()));
            }

            // 10. 标签处理
            if (companyPool != null && companyPool.getSaleLiteFlow() != null) {
                addLabelRecord(dto, newLabelDtos, goodsId,labelCode);
            }
            respVO.getCreateList().add(goodsSku);
        }

        // 11. 持久化处理
        persistChanges(goodsPoolId, requiresSortReset, newSortEntities, newPoolSubEntities, allGoodsIds);
        processLabelUpdates(newLabelDtos, organization);
        pushGoodsMessages(goodsPool, allGoodsIds);

        // 12. 日志记录
        log.info("导入商品池商品详情:商品池{}, 成功数量: {}, 失败数量: {}",
                goodsPoolId, respVO.getCreateList().size(), respVO.getFailureList().size());
        return respVO;
    }

    // ===== 辅助方法 =====
    private void pushGoodsMessages(DfmallGoodsPoolEntity goodsPool, Set<Long> allGoodsIds) {
        if (StrUtil.isBlank(goodsPool.getCompanyCode()) ||
                !openApiConfig.getPushProductCustomers().contains(goodsPool.getCompanyCode()) ||
                CollectionUtil.isEmpty(allGoodsIds)) {
            return;
        }

        List<ShopGoods> shopGoods = shopGoodsService.getBaseMapper().selectByGoodsIdIn(allGoodsIds);
        if (CollectionUtil.isEmpty(shopGoods)) return;

        shopGoods.forEach(item ->
                openApiMessageUtil.sendMessage(
                        goodsPool.getCompanyCode(),
                        MessageTypeEnum.PUSH_GOODS,
                        MapUtil.of("goodsCode", item.getGoodsCode())
                )
        );
    }
    private void processLabelUpdates(List<DfmallGoodsPoolImportSkuDto> labelDtos, SystemOrganization organization) {
        if (CollectionUtil.isEmpty(labelDtos) || organization == null) return;

        Map<String, List<DfmallGoodsPoolImportSkuDto>> groupMap = labelDtos.stream()
                .collect(Collectors.groupingBy(DfmallGoodsPoolImportSkuDto::getGoodsLabel));

        for (Map.Entry<String, List<DfmallGoodsPoolImportSkuDto>> entry : groupMap.entrySet()) {
            GoodsAddLabelDto addLabelDto = new GoodsAddLabelDto();
            addLabelDto.setLabelCode(entry.getKey());
            addLabelDto.setGoodsIds(entry.getValue().stream()
                    .map(DfmallGoodsPoolImportSkuDto::getGoodsId)
                    .collect(Collectors.toList()));
            addLabelDto.setOrgId(organization.getId());
            shopGoodsService.goodsAddLabel(addLabelDto);
        }
    }
    private void addLabelRecord(DfmallGoodsPoolImportSkuDto dto, List<DfmallGoodsPoolImportSkuDto> labelDtos, Long goodsId, String labelCode) {
        dto.setGoodsLabel(labelCode);
        dto.setGoodsId(goodsId);
        labelDtos.add(dto);
    }
    private DfmallGoodsPoolSortEntity createSortEntity(Long poolId, ShopGoods goods, Integer sortNo) {
        DfmallGoodsPoolSortEntity entity = new DfmallGoodsPoolSortEntity();
        entity.setGoodsPoolId(poolId);
        entity.setGoodsCode(goods.getGoodsCode());
        entity.setSortNo(sortNo.longValue());
        return entity;
    }

    private DfmallGoodsPoolEntity prepareCompanyPool(DfmallGoodsPoolEntity goodsPool) {
        if (StringUtils.isNotBlank(goodsPool.getCompanyCode())) {
            return queryCompanyPool(goodsPool.getCompanyCode());
        }
        return null;
    }

    private SystemOrganization prepareOrganization(DfmallGoodsPoolEntity goodsPool) {
        if (StringUtils.isNotBlank(goodsPool.getCompanyCode())) {
            return organizationService.getOrganizationByCode(goodsPool.getCompanyCode());
        }
        return null;
    }
    private Map<String, String> prepareSupplierMap() {
        // 优化：仅查询需要的字段
        return shopSupplierService.list(new LambdaQueryWrapperX<ShopSupplier>()
                        .eq(ShopSupplier::getApproveState, 1)
                        .eq(BaseEntity::getIsEnable, 1)
                        .eq(ShopSupplier::getDataSource, "dfmall")
                        .select(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode))
                .stream()
                .collect(Collectors.toMap(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));
    }

    private Map<String, ShopGoods> prepareGoodsMap(List<DfmallGoodsPoolImportSkuDto> dtos) {
        Set<String> validSkus = dtos.stream()
                .map(DfmallGoodsPoolImportSkuDto::getGoodsSku)
                .collect(Collectors.toSet());

        // 分批查询避免内存溢出 [6](@ref)
        return CollUtil.split(validSkus, 500).stream()
                .flatMap(batch -> shopGoodsService.list(new LambdaQueryWrapperX<ShopGoods>()
                        .select(ShopGoods::getGoodsId,
                                ShopGoods::getGoodsSku,
                                ShopGoods::getSupplierCode,
                                ShopGoods::getGoodsCode,
                                ShopGoods::getThirdLevelGcid,
                                ShopGoods::getShelvesState,
                                ShopGoods::getSupplierType)
                        .in(ShopGoods::getGoodsSku, batch)).stream())
                .collect(Collectors.toMap(
                        goods -> goods.getSupplierCode() + goods.getGoodsSku(),
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));
    }

    private Map<String, String> prepareZoneClassMap(Long zoneId,
                                                    List<DfmallGoodsPoolImportSkuDto> dtos) {
        Set<String> distinctTypes = dtos.stream()
                .map(DfmallGoodsPoolImportSkuDto::getZoneGoodsType)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if(CollectionUtil.isEmpty(distinctTypes)){
            return new HashMap<>();
        }

        return goodsZoneClassService.list(new LambdaQueryWrapperX<GoodsZoneClassEntity>()
                        .eq(GoodsZoneClassEntity::getZoneId, zoneId)
                        .in(GoodsZoneClassEntity::getLabel, distinctTypes))
                .stream()
                .collect(Collectors.toMap(GoodsZoneClassEntity::getLabel, GoodsZoneClassEntity::getValue));
    }

    private Map<String, Boolean> checkBlacklist(List<DfmallGoodsPoolImportSkuDto> dtos,
                                                Map<String, String> supplierMap) {
        List<GoodsBlacklistEntity> blacklist = blacklistService.list(
                new LambdaQueryWrapperX<GoodsBlacklistEntity>()
                        .in(GoodsBlacklistEntity::getGoodsSku,
                                dtos.stream().map(DfmallGoodsPoolImportSkuDto::getGoodsSku).collect(Collectors.toList()))
                        .in(GoodsBlacklistEntity::getSupplierCode,
                                dtos.stream().map(dto -> supplierMap.get(dto.getSupplierName()))
                                        .filter(Objects::nonNull).collect(Collectors.toList()))
        );

        return blacklist.stream()
                .collect(Collectors.toMap(
                        e -> e.getSupplierCode() + ":" + e.getGoodsSku(),
                        e -> Boolean.TRUE
                ));
    }

    private boolean validateGoodsRecord(DfmallGoodsPoolImportSkuDto dto,
                                        Map<String, String> supplierMap,
                                        Map<String, ShopGoods> goodsMap,
                                        Map<String, Boolean> blacklistMap,
                                        ImportGoodsPoolFailVO respVO) {
        String supplierName = dto.getSupplierName();
        String goodsSku = dto.getGoodsSku();
        String compositeKey = supplierName + ":" + goodsSku;

        // 供应商存在性检查
        if (!supplierMap.containsKey(supplierName)) {
            respVO.getFailureList().put(compositeKey, "供应商不存在: " + supplierName);
            return false;
        }
        String supplierCode = supplierMap.get(supplierName);

        // 黑名单检查
        if (blacklistMap.containsKey(supplierCode + ":" + goodsSku)) {
            respVO.getFailureList().put(compositeKey, "商品已被加入黑名单");
            return false;
        }

        // 商品存在性检查
        String goodsKey = supplierCode + goodsSku;
        if (!goodsMap.containsKey(goodsKey)) {
            respVO.getFailureList().put(compositeKey, "商品不存在，请检查供应商和SKU");
            return false;
        }

        // 上架状态检查
        ShopGoods shopGoods = goodsMap.get(goodsKey);
        if (shopGoods.getShelvesState() <= 0 && (openApiConfig.getSuppliers().contains(supplierCode) || shopGoods.getSupplierType() == 1)) {
            respVO.getFailureList().put(compositeKey, "商品未上架，请先上架");
            return false;
        }

        return true;
    }

    private void createPoolSubEntity(DfmallGoodsPoolImportSkuDto dto,
                                     Map<String, String> zoneClassMap,
                                     List<DfmallGoodsPoolSubEntity> entities,
                                     ShopGoods goods,
                                     Long poolId) {
        DfmallGoodsPoolSubEntity entity = new DfmallGoodsPoolSubEntity();
        entity.setGoodsPoolId(poolId)
                .setGoodsId(goods.getGoodsId())
                .setSupplierCode(goods.getSupplierCode())
                .setStandardClassId(Long.valueOf(goods.getThirdLevelGcid()));

        Optional.ofNullable(dto.getZoneGoodsType())
                .map(zoneClassMap::get)
                .ifPresent(entity::setZoneGoodsType);

        entities.add(entity);
    }

    private void persistChanges(Long poolId,
                                boolean resetSort,
                                List<DfmallGoodsPoolSortEntity> sortEntities,
                                List<DfmallGoodsPoolSubEntity> subEntities,
                                Set<Long> goodsIds) {
        if (CollectionUtil.isNotEmpty(subEntities)) {
            dfmallGoodsPoolSubService.saveBatch(subEntities);
            //同步到平台
            List<Long> syncGoodsIds = subEntities.stream().map(DfmallGoodsPoolSubEntity::getGoodsId).distinct().collect(Collectors.toList());
            Long platPoolId = this.queryPlatformPool().getId();
            dfmallGoodsPoolSubService.importSkuSync(platPoolId, syncGoodsIds);


            // 分批更新ES
            CollUtil.split(new ArrayList<>(goodsIds), 1000)
                    .forEach(batch -> goodsEsProcessor.updateIndex(batch, EsUpdateType.GOODS_POOL));

            // 批量更新商品状态
            shopGoodsService.update(new LambdaUpdateWrapper<ShopGoods>()
                    .set(ShopGoods::getShelvesState, 1)
                    .in(ShopGoods::getGoodsId, goodsIds));
        }
        if (resetSort) {
            goodsPoolSortMapper.delete(new LambdaQueryWrapperX<DfmallGoodsPoolSortEntity>()
                    .eq(DfmallGoodsPoolSortEntity::getGoodsPoolId, poolId));
            goodsPoolSortMapper.insertBatch(sortEntities);
        }
    }

    private List<DfmallGoodsPoolImportSkuDto> readExcelToSupplierGoods(MultipartFile file) {
        try (ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream(), "Sheet1")) {
            if (excelReader.getRowCount() < 2) {
                throw new SupplierException("导入数据不能为空");
            }

            excelReader.addHeaderAlias("商品sku", "goodsSku");
            excelReader.addHeaderAlias("供应商全称", "supplierName");
            excelReader.addHeaderAlias("商品标签", "goodsLabel");
            excelReader.addHeaderAlias("专区分类", "zoneGoodsType");
            excelReader.addHeaderAlias("商品排序序号(填写整数)", "sortNo");

            return excelReader.readAll(DfmallGoodsPoolImportSkuDto.class);
        } catch (IOException e) {
            log.error("Excel读取失败: {}", e.getMessage());
            throw new SupplierException("Excel文件处理异常", e);
        }
    }

    /**
     * 每个商品池锁等待10分钟。10分钟后可再次执行。
     *
     * @param goodsPoolId 商品池ID
     */
    @DistributedLock(value = "goodsPoolUpdateIndex", key = "#goodsPoolId", leaseTime = 60 * 10, waitLock = false, throwMessage = "当前正在执行中,请耐心等待~")
    public void goodsPoolUpdateIndex(Long goodsPoolId) {
        log.info("商品池更新索引,goodsPoolId:{}", goodsPoolId);
        List<Long> poolSubGoodsIds = dfmallGoodsPoolSubService.queryGoodsIdByPoolId(goodsPoolId);

        CollUtil.split(poolSubGoodsIds, B_SIZE).parallelStream().forEach(itemIds -> {
            log.info("开始商品部分更新索引,itemIds:{}", JSON.toJSONString(itemIds));
            goodsEsProcessor.updateIndex(itemIds, EsUpdateType.GOODS_POOL);
        });
        log.info("商品池更新索引完成,goodsPoolId:{}", goodsPoolId);
    }

    public void goodsPoolExport(Long goodsPoolId) {
        SystemUsers users = usersService.getUser(LocalUserHolder.get().getId());
        List<GoodsPoolExportVO> list = baseMapper.goodsPoolExport(goodsPoolId);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).build();
        WriteSheet goodsSheet = EasyExcel.writerSheet("专区商品").head(GoodsPoolExportVO.class).build();
        excelWriter.write(list, goodsSheet);
        excelWriter.finish();

        ByteArrayInputStream excelAttachment = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        List<String> emailList = Arrays.stream(users.getEmail().split(","))
                .map(String::trim)
                .collect(Collectors.toList());
        mailService.sendEmail("专区商品.xlsx","",emailList, new ArrayList<>(),"专区商品.xlsx",excelAttachment);
    }


    public ImportGoodsPoolFailVO importDownSku(MultipartFile file,Long goodsPoolId){
        List<DfmallGoodsPoolImportDownSkuDto> importSkuDtoList = this.readExcelToDownSku(file);
        // 去掉sku为空的数据
        importSkuDtoList = importSkuDtoList.stream().filter(dto -> StringUtils.isNotBlank(dto.getGoodsSku())).collect(Collectors.toList());
        ImportGoodsPoolFailVO respVO = ImportGoodsPoolFailVO.builder().failureList(new LinkedHashMap<>()).createList(new ArrayList<>()).build();
        List<String> supName = importSkuDtoList.stream().map(DfmallGoodsPoolImportDownSkuDto::getSupplierName).distinct().collect(Collectors.toList());

        Map<String,String> supplierMap = shopSupplierService.getBaseMapper()
                .selectList(new LambdaQueryWrapperX<ShopSupplier>()
                        .in(ShopSupplier::getSupplierFullName,supName))
                .stream().collect(Collectors.toMap(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));

        Map<String,List<DfmallGoodsPoolImportDownSkuDto>> groupMap = importSkuDtoList.stream().collect(Collectors.groupingBy(DfmallGoodsPoolImportDownSkuDto::getSupplierName));

        for (Map.Entry<String,List<DfmallGoodsPoolImportDownSkuDto>> entry : groupMap.entrySet() ){
            String supplierCode = supplierMap.get(entry.getKey());
            if(StringUtils.isBlank(supplierCode)){
                respVO.getFailureList().put("供应商:" + entry.getKey(), "未查询到该供应商信息");
                continue;
            }
            List<String> skus = entry.getValue().stream().map(DfmallGoodsPoolImportDownSkuDto::getGoodsSku).collect(Collectors.toList());
            List<ShopGoods> goodsList = shopGoodsService.getBaseMapper().selectList(new LambdaQueryWrapperX<ShopGoods>()
                             .select(ShopGoods::getGoodsId)
                    .in(ShopGoods::getGoodsSku,skus).eq(ShopGoods::getSupplierCode,supplierCode));
            if(CollectionUtil.isNotEmpty(goodsList)){
                List<Long> goodsIds = goodsList.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList());
                String ids = goodsIds.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(","));
                GoodsPushDownDto goodsPushDownDto = new GoodsPushDownDto();
                goodsPushDownDto.setGoodsPoolId(goodsPoolId);
                goodsPushDownDto.setGoodsIds(ids);
                goodsZoneService.goodsPushDown(goodsPushDownDto);
            }

        }
        return respVO;

    }

    public List<DfmallGoodsPoolImportDownSkuDto> readExcelToDownSku(MultipartFile file){
        ExcelReader excelReader;
        try {
            excelReader = ExcelUtil.getReader(file.getInputStream(), "Sheet1");
            if (excelReader.getRowCount() < 2) {
                throw new SupplierException("导入数据不能为空");
            }
        } catch (IOException e) {
            log.error("Excel读取错误", e);
            throw new SupplierException("Excel读取错误", e);
        }
        excelReader.addHeaderAlias("商品sku", "goodsSku");
        excelReader.addHeaderAlias("供应商全称", "supplierName");
        return excelReader.readAll(DfmallGoodsPoolImportDownSkuDto.class);
    }


    /**
     * 刷新商品池商品
     * @param refresh   刷新方式 0指定时间刷新 1立即刷新
     * @param dateTime  时间 yyyy-MM-dd HH:mm:ss
     * @param poolId    商品池ID
     */
    public void refreshPool(Integer refresh, String dateTime, Long poolId){
        if(refresh == null || StrUtil.isBlank(dateTime) || poolId == null){
            return;
        }
        if(refresh == 1){
            //立即刷新商品池商品
            shopGoodsService.goodsPoolSync(poolId);
        }else {
            // 定时同步商品池商品
            LocalDateTime refreshTime = LocalDateTime.parse(dateTime,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime nowTime = LocalDateTime.now();

            long seconds = ChronoUnit.SECONDS.between(nowTime, refreshTime);

            ThreadUtil.getSeqOrScheduledExecutorService()
                    .schedule(()-> shopGoodsService.goodsPoolSync(poolId),seconds, TimeUnit.SECONDS);
        }
    }

    public void goodsTop100InPool(Long goodsPoolId) {
        // 获取 Top100 商品数据（提前获取避免后续多次调用）
        List<DfmallGoodsPoolSubEntity> top100 = orderDetailService.queryTop100();
        if (CollectionUtil.isEmpty(top100)) {
            ServiceResult.succ();
            return; // 无数据直接返回
        }

        // 合并数据库操作：删除旧数据 + 写入新数据
        try {
            // 1. 统一删除旧数据（子表 + 排序表）
            dfmallGoodsPoolSubService.deleteByPoolId(goodsPoolId);
            goodsPoolSortMapper.delete(
                    new LambdaQueryWrapperX<DfmallGoodsPoolSortEntity>()
                            .eq(DfmallGoodsPoolSortEntity::getGoodsPoolId, goodsPoolId)
            );

            // 2. 准备新数据
            List<DfmallGoodsPoolSubEntity> subEntities = new ArrayList<>(top100.size());
            List<DfmallGoodsPoolSortEntity> sortEntities = new ArrayList<>(top100.size());

            long sortNo = 1L;
            for (DfmallGoodsPoolSubEntity entity : top100) {
                entity.setGoodsPoolId(goodsPoolId);
                subEntities.add(entity);

                DfmallGoodsPoolSortEntity sortEntity = new DfmallGoodsPoolSortEntity();
                sortEntity.setGoodsPoolId(goodsPoolId);
                sortEntity.setSortNo(sortNo);
                sortEntity.setGoodsCode(entity.getGoodsCode());
                sortEntities.add(sortEntity);
                sortNo++;
            }

            // 3. 批量写入新数据
            dfmallGoodsPoolSubService.saveBatch(subEntities);
            goodsPoolSortMapper.insertBatch(sortEntities);

            // 4. 更新ES索引（仅涉及本次变更的商品）
            List<Long> affectGoodsIds = subEntities.stream()
                    .map(DfmallGoodsPoolSubEntity::getGoodsId)
                    .collect(Collectors.toList());
            goodsEsProcessor.updateIndex(affectGoodsIds, EsUpdateType.GOODS_POOL);

            ServiceResult.succ();
        } catch (Exception e) {
            log.error("更新商品池TOP100失败, goodsPoolId: {}", goodsPoolId, e);
            ServiceResult.error("操作失败");
        }
    }

    public void goodsPoolCheckStatus(Long goodsPoolId) {
        // 0. 创建固定请求模板（避免在循环内重复创建）
        BatchGetStockReq templateReq = new BatchGetStockReq();
        templateReq.setN(1);
        templateReq.setRegin(buildFixedRegion()); // 提取固定区域数据

        // 1. 查询所有商品池商品ID
        List<Long> goodsIds = dfmallGoodsPoolSubService.queryGoodsIdByPoolId(goodsPoolId);
        if (CollectionUtil.isEmpty(goodsIds)) {
            log.info("商品池[{}]无商品", goodsPoolId);
            return;
        }

        // 2. 批量查询商品信息（全量一次查询）
        Map<String, Long> fullGoodsMap = shopGoodsService.list(
                new LambdaQueryWrapperX<ShopGoods>()
                        .select(ShopGoods::getGoodsId, ShopGoods::getGoodsCode)
                        .in(ShopGoods::getGoodsId, goodsIds)
        ).stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, ShopGoods::getGoodsId));

        // 3. 分批查询库存（每100个商品一批）
        List<String> goodsCodes = new ArrayList<>(fullGoodsMap.keySet());
        List<Long> downGoodsId = CollectionUtil.split(goodsCodes, 100).stream()
                .flatMap(batchCodes -> {
                    // 构建批量请求（每个商品独立请求对象）
                    List<BatchGetStockReq> requests = batchCodes.stream()
                            .map(code -> {
                                BatchGetStockReq req = new BatchGetStockReq();
                                BeanUtils.copyProperties(templateReq, req);
                                req.setGoodsCode(code);
                                return req;
                            })
                            .collect(Collectors.toList());

                    // 批量调库存接口
                    try {
                        Map<String, RemoteStockInfoResp> stockMap = remoteInfoManage.batchGetStock(requests);
                        return stockMap.entrySet().stream()
                                .filter(entry -> isStockInvalid(entry.getValue()))
                                .map(entry -> fullGoodsMap.get(entry.getKey()));
                    } catch (Exception e) {
                        log.error("商品池[{}]库存查询失败: {}", goodsPoolId, e.getMessage());
                        return Stream.empty();
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 4. 批量下架处理
        if (CollectionUtil.isNotEmpty(downGoodsId)) {
            log.info("商品池[{}] 下架商品: {}", goodsPoolId, downGoodsId);
            performGoodsPushDown(goodsPoolId, downGoodsId);
        }
    }

    // --- 工具方法提取 ---
    private RegionDto buildFixedRegion() {
        RegionDto region = new RegionDto();
        region.setProvince("湖北省");
        region.setCity("武汉市");
        region.setCounty("蔡甸区");
        region.setTown("沌阳街道");
        region.setAddress("东风大道10号东风汽车有限公司");
        return region;
    }

    private boolean isStockInvalid(RemoteStockInfoResp resp) {
        return resp != null &&
                (RemoteStockInfoResp.StockStatusEnum.noGoods == resp.getStockStatus() ||
                        RemoteStockInfoResp.StockStatusEnum.unSale == resp.getStockStatus());
    }

    private void performGoodsPushDown(Long poolId, List<Long> goodsIds) {
        GoodsPushDownDto dto = new GoodsPushDownDto();
        dto.setGoodsPoolId(poolId);
        dto.setGoodsIds(StringUtils.join(goodsIds, ",")); // 避免stream拼接
        dto.setDownReason("商品池商品无货下架");
        goodsZoneService.goodsPushDown(dto);
    }

    public LiteFlowsParamVo queryLiteFlowParams(LiteFlowsParamDto liteFlowsParamDto) {
        return dfmallGoodsPoolMapper.queryLiteFlowParams(liteFlowsParamDto);
    }
    

    public void goodsPoolSetZoneClassJob(Long poolId) {
        // 1. 查询基础数据
        List<String> supplierCodes = dfmallGoodsPoolSubService.list(new LambdaQueryWrapperX<DfmallGoodsPoolSubEntity>()
                .eq(DfmallGoodsPoolSubEntity::getGoodsPoolId, poolId)
                .groupBy(DfmallGoodsPoolSubEntity::getSupplierCode))
                .stream().map(DfmallGoodsPoolSubEntity::getSupplierCode).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(supplierCodes)){
            return;
        }

        // 2. 获取供应商详情并转换为Map
        Map<String, ShopSupplierDetail> supplierDetailMap = supplierDetailService.list(
                        new LambdaQueryWrapperX<ShopSupplierDetail>()
                                .in(ShopSupplierDetail::getSupplierCode, supplierCodes))
                .stream()
                .collect(Collectors.toMap(
                        ShopSupplierDetail::getSupplierCode,
                        Function.identity()));

        // 3. 获取区域分类映射
        GoodsZoneEntity zone = goodsZoneService.getOne(
                new LambdaQueryWrapperX<GoodsZoneEntity>()
                        .eq(GoodsZoneEntity::getGoodsPoolId, poolId));

        Map<String, String> classMap = goodsZoneClassService.queryZoneClassByZoneId(zone.getId())
                .stream()
                .collect(Collectors.toMap(
                        GoodsZoneClassEntity::getLabel,
                        GoodsZoneClassEntity::getValue));

        // 4. 构建更新列表并执行批量更新
        supplierCodes.stream()
                .map(supplierDetailMap::get) // 转换为SupplierDetail对象
                .filter(Objects::nonNull)   // 过滤掉null值
                .map(supplier -> {
                    DfmallGoodsPoolSubEntity entity = new DfmallGoodsPoolSubEntity();
                    entity.setZoneGoodsType(getZoneType(classMap, supplier));
                    entity.setGoodsPoolId(poolId);
                    entity.setSupplierCode(supplier.getSupplierCode());
                    return entity;
                })
                .filter(entity -> !StringHelper.IsEmptyOrNull(entity.getZoneGoodsType())) // 过滤掉空值
                .forEach(entity -> dfmallGoodsPoolSubService.update(
                        entity,
                        new UpdateWrapper<DfmallGoodsPoolSubEntity>().lambda()
                                .set(DfmallGoodsPoolSubEntity::getZoneGoodsType, entity.getZoneGoodsType())
                                .eq(DfmallGoodsPoolSubEntity::getGoodsPoolId, entity.getGoodsPoolId())
                                .eq(DfmallGoodsPoolSubEntity::getSupplierCode, entity.getSupplierCode())));

        // 4. 更新ES索引（仅涉及本次变更的商品）
        List<Long> ids = dfmallGoodsPoolSubService.queryGoodsIdByPoolId(poolId);
        goodsEsProcessor.updateIndex(ids, EsUpdateType.GOODS_POOL);
    }

    /**
     * 获取区域类型（优先使用District，其次使用City）
     */
    private String getZoneType(Map<String, String> classMap, ShopSupplierDetail supplier) {
        String zoneType = classMap.get(supplier.getAntiPovertyDistrict());
        if (StringHelper.IsEmptyOrNull(zoneType)) {
            zoneType = classMap.get(supplier.getAntiPovertyCity());
        }
        return zoneType;
    }

    public void goods519InPool(Long goodsPoolId){
        List<DfmallGoodsPoolSubEntity> poolOne = orderDetailService.queryTop20By1();
        poolOne.forEach(item -> item.setZoneGoodsType("yfl_zone_xrsql_Soda"));
        List<DfmallGoodsPoolSubEntity> poolTwo = orderDetailService.queryTop20By2();
        poolTwo.forEach(item -> item.setZoneGoodsType("yfl_zone_xrsql_jelly"));
        List<DfmallGoodsPoolSubEntity> poolThird = orderDetailService.queryTop20By3();
        poolThird.forEach(item -> item.setZoneGoodsType("yfl_zone_xrsql_Sunscreen"));
        List<DfmallGoodsPoolSubEntity> poolFivw = orderDetailService.queryTop20By4();
        poolFivw.forEach(item -> item.setZoneGoodsType("yfl_zone_xrsql_Mosquito repellent"));
        poolOne.addAll(poolTwo);
        poolOne.addAll(poolThird);
        poolOne.addAll(poolFivw);
        poolOne.forEach(item -> item.setGoodsPoolId(goodsPoolId));
        dfmallGoodsPoolSubService.deleteByPoolId(goodsPoolId);
        dfmallGoodsPoolSubService.saveBatch(poolOne);

        // 4. 更新ES索引（仅涉及本次变更的商品）
        List<Long> affectGoodsIds = poolOne.stream()
                .map(DfmallGoodsPoolSubEntity::getGoodsId)
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(affectGoodsIds)){
            goodsEsProcessor.updateIndex(affectGoodsIds, EsUpdateType.GOODS_POOL);
        }
    }

    public void yflGeneralPoolRuleJob(Long goodsPoolId,List<Long> excludePoolIds) {
        dfmallGoodsPoolSubService.getBaseMapper().yflGeneralPoolRuleJob(goodsPoolId,excludePoolIds);
    }

}

