package com.ly.yph.api.settlement.supplier.factory;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleFactory;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleStrategy;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.entity.SettleShopBillDetail;
import com.ly.yph.api.settlement.common.enums.PoolTypeEnum;
import com.ly.yph.api.settlement.common.enums.SupplierBillDetailStagingFlagEnum;
import com.ly.yph.api.settlement.common.enums.SupplierInvoiceStateEnum;
import com.ly.yph.api.settlement.common.service.SettleShopBillDetailService;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailExcelVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailPageReqVo;
import com.ly.yph.api.settlement.supplier.dto.*;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillDetailService;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillService;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.excel.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupplierBillDetailStrategy implements SupplierInvoiceStrategy {

    @Resource
    private SupplierInvoiceBillDetailService supplierInvoiceBillDetailService;
    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;
    @Resource
    private OrderLifeCycleFactory orderLifeCycleFactory;
    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;

    @Override
    public void exportInvoiceTemplate(HttpServletResponse response) throws IOException {
        List<SupplierInvoiceDetailExcelDto> list = Arrays.asList(
                SupplierInvoiceDetailExcelDto.builder()
                        .orderNumber("'" +"240719135401579994")
                        .goodsSku("1000053543XXX")
                        .checkedNum(new BigDecimal("12"))
                        .build()
        );
        ExcelUtils.write(response, "账单明细开票模板.xlsx", "订单明细", SupplierInvoiceDetailExcelDto.class, list);
    }

    @Override
    @Transactional
    public String excelInvoice(MultipartFile file, SettleShopBill settleShopBill) throws IOException {

        List<SupplierInvoiceDetailExcelDto> supplierInvoiceDetailExcelDtoList = ExcelUtils.read(file, SupplierInvoiceDetailExcelDto.class);
        supplierInvoiceDetailExcelDtoList.removeIf(ExcelUtils::objCheckIsNull);

        List<SupplierInvoiceDetailDto> supplierInvoiceDetailDtoList = supplierInvoiceBillDetailService
                .getSupplierInvoiceDetailListForExcel(settleShopBill.getBillId(), supplierInvoiceDetailExcelDtoList);

        supplierInvoiceBillService.doSupplierInvoiceBill(supplierInvoiceDetailDtoList, settleShopBill);

        return "邮费开票完成！";

    }

    @Override
    @Transactional
    public String allDetailInvoice(SettleShopBill settleShopBill) {

        List<SupplierInvoiceDetailDto> supplierInvoiceDetailDtoList = supplierInvoiceBillDetailService.getBaseMapper()
                .getSupplierSettleInvoiceDetailDto(null, settleShopBill.getBillId(), null);

        if (CollectionUtil.isEmpty(supplierInvoiceDetailDtoList)) {
            throw new ParameterException("未查询到满足条件的可开票数据！");
        }

        supplierInvoiceBillService.doSupplierInvoiceBill(supplierInvoiceDetailDtoList, settleShopBill);

        return "整单开票完成！";

    }

    @Override
    @Transactional
    public String supplierInvoiceByDetailIds(SupplierInvoiceByDetailIdDto supplierInvoiceByDetailIdDto,
                                             SettleShopBill settleShopBill) {

        List<SupplierInvoiceDetailDto> supplierSettleInvoiceDetailDtoList = supplierInvoiceBillDetailService.getBaseMapper()
                .getSupplierSettleInvoiceDetailDto(null,
                        settleShopBill.getBillId(), supplierInvoiceByDetailIdDto.getDetailIdList());

        if (CollectionUtil.isEmpty(supplierSettleInvoiceDetailDtoList)) {
            throw new ParameterException("未查询到满足条件的可开票数据！");
        }

        supplierInvoiceBillService.doSupplierInvoiceBill(supplierSettleInvoiceDetailDtoList, settleShopBill);

        return "开票成功！";

    }

    @Override
    @Transactional
    public void supplierInvoicePass(SupplierInvoiceBill supplierInvoiceBill) {

        List<SupplierInvoiceDelDto> supplierInvoiceDelDtoList = supplierInvoiceBillDetailService.getBaseMapper()
                .getSupplierInvoiceDelDetail(supplierInvoiceBill.getId());

        if (CollectionUtil.isEmpty(supplierInvoiceDelDtoList)) {
            throw new ParameterException("未查询到相关明细数据！");
        }

        //更新账单生命周期 账单明细
        List<Long> billDetailList = supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getBillDetailId).collect(Collectors.toList());
        List<Long> lifeCycleList = supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getLifeCycleId).collect(Collectors.toList());
        supplierInvoiceBillDetailService.invoicePassForUpdate(billDetailList, lifeCycleList);

        //订单生命周期
        Map<Integer, List<SupplierInvoiceDelDto>> orderSaleChannelMap = supplierInvoiceDelDtoList.stream()
                .collect(Collectors.groupingBy(SupplierInvoiceDelDto::getOrderSalesChannel));

        for (Integer orderSaleChannel : orderSaleChannelMap.keySet()) {
            OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(orderSaleChannel);
            orderLifeCycleStrategy.updatePurchaseOrderInfoForSupplierInvoice(orderSaleChannelMap.get(orderSaleChannel));
        }


        // 更新供应商发票状态
        supplierInvoiceBill.setConfirmName(LocalUserHolder.get().getNickname());
        supplierInvoiceBill.setInvoiceConfirmTime(new Date());
        supplierInvoiceBill.setState(SupplierInvoiceStateEnum.INVOICED.getCode());
        supplierInvoiceBillService.updateById(supplierInvoiceBill);

        //检查账单是否全部开票，如果全部开票，则可发起验收单
        supplierInvoiceBillService.checkIsToApproveBill(supplierInvoiceBill);

    }



    @Override
    public void supplierInvoiceExport(HttpServletResponse response,Long id) throws IOException {
        List<SupplerInvoiceExcelDto> supplerInvoiceExcelDtoList = supplierInvoiceBillDetailService.getBaseMapper().getSupplierSettleList(id);
        if (CollectionUtil.isEmpty(supplerInvoiceExcelDtoList)) {
            throw new ParameterException("该发票下无明细数据");
        }
        ExcelUtils.write(response, "供应商发票" + ".xls", "明细列表",
                SupplerInvoiceExcelDto.class, supplerInvoiceExcelDtoList);
    }

    @Override
    @Transactional
    public void invoiceDel(SupplierInvoiceBill supplierInvoiceBill) {
        List<SupplierInvoiceDelDto> supplierInvoiceDelDtoList = supplierInvoiceBillDetailService.getBaseMapper().getSupplierInvoiceDelDetail(supplierInvoiceBill.getId());

        if (CollectionUtil.isEmpty(supplierInvoiceDelDtoList)) {
            throw new ParameterException("未查询到账单信息！");
        }

       supplierInvoiceBillService.removeById(supplierInvoiceBill.getId());

        supplierInvoiceBillDetailService.removeBatchByIds(
                supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getInvoiceDetailId).collect(Collectors.toList())
        );

        // 账单明细 账单生命周期
        Lists.partition(
                supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getBillDetailId).collect(Collectors.toList()), 1000
        ).forEach(detailList -> supplierInvoiceBillDetailService.getBaseMapper().updateBillDetailForSupplierInvoiceDel(detailList));

        Lists.partition(
                supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getLifeCycleId).collect(Collectors.toList()), 1000
        ).forEach(lifeCycleIdList -> supplierInvoiceBillDetailService.getBaseMapper().updateBillLifecycleForInvoiceDel(lifeCycleIdList));
    }

    @Override
    @Transactional
    public void supplierInvoiceReject(SupplierInvoiceBill supplierInvoiceBill, String rejectReason) {

        List<SupplierInvoiceDelDto> supplierInvoiceDelDtoList = supplierInvoiceBillDetailService.getBaseMapper()
                .getSupplierInvoiceDelDetail(supplierInvoiceBill.getId());

        //账单明细状态回退至已确认
        List<Long> billDetailList = supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getBillDetailId).collect(Collectors.toList());
        Lists.partition(billDetailList, 1000).forEach(subList -> supplierInvoiceBillDetailService.getBaseMapper().updateBillDetailForSupplierInvoiceDel(subList));
        //账单生命周期回退
        List<Long> lifeCycleList = supplierInvoiceDelDtoList.stream().map(SupplierInvoiceDelDto::getLifeCycleId).collect(Collectors.toList());
        Lists.partition(lifeCycleList, 1000).forEach(subList -> supplierInvoiceBillDetailService.getBaseMapper().updateBillDetailForSupplierInvoiceDel(subList));

        UpdateWrapper<SupplierInvoiceBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SupplierInvoiceBill::getId, supplierInvoiceBill.getId())
                .set(SupplierInvoiceBill::getState, SupplierInvoiceStateEnum.REJECTED.getCode())
                .set(SupplierInvoiceBill::getApproveReason, rejectReason)
                .set(BaseEntity::getModifier, LocalUserHolder.get().getNickname())
                .set(BaseEntity::getUpdateTime, new Date());

      supplierInvoiceBillService.update(updateWrapper);

    }

    @Override
    public String removeToNextMonth(RemoveToNextMonthDto removeToNextMonthDto) {

        QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBillDetail::getBillId, removeToNextMonthDto.getBillId())
                .eq(SettleShopBillDetail::getDetailId, removeToNextMonthDto.getDetailId());
        SettleShopBillDetail one = settleShopBillDetailService.getOne(queryWrapper);
        if (one == null) {
            throw new ParameterException("未查询到明细数据！");
        }

        UpdateWrapper<SettleShopBillDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SettleShopBillDetail::getDetailId, removeToNextMonthDto.getDetailId());
        if (removeToNextMonthDto.getType() == 0) {
            updateWrapper.lambda().set(SettleShopBillDetail::getBillStagingFlag, SupplierBillDetailStagingFlagEnum.HAD_STAGING.getCode());
        } else if (removeToNextMonthDto.getType() == 1) {
            updateWrapper.lambda().set(SettleShopBillDetail::getBillStagingFlag, SupplierBillDetailStagingFlagEnum.NOT_STAGING.getCode());
        } else {
            throw new ParameterException("操作类型异常！");
        }
        settleShopBillDetailService.update(updateWrapper);
        return "账单明细移月暂存成功！";
    }

    @Override
    public void exportBillDetail(HttpServletResponse response, SettleShopBillDetailPageReqVo reqVo) throws IOException {
        List<SettleShopBillDetailExcelVo> billDetailExecl = settleShopBillDetailService.getBillDetailExecl(reqVo, PoolTypeEnum.DFSHOP.getCode());
        ExcelUtils.write(response,"账单明细.xlsx","账单明细",SettleShopBillDetailExcelVo.class,billDetailExecl);
    }
}
