package com.ly.yph.api.goods.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.service.ShopGoodsContractService;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.goods.vo.GoodsContractApprovalVo;
import com.ly.yph.api.goods.vo.ShopGoodsContractVo;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.organization.enums.OrganizationTypeEnum;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.system.dto.StartInstanceDto;
import com.ly.yph.api.system.feign.ActTaskFeign;
import com.ly.yph.api.workflow.WorkflowContext;
import com.ly.yph.api.workflow.entity.Workflow;
import com.ly.yph.api.workflow.entity.WorkflowStep;
import com.ly.yph.api.workflow.service.WorkFlowService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.email.MailService;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ly.yph.api.system.enums.ActProcTypeEnum.DIRECTED_SUPPLIER_GOODS_APPROVE;

/**
 * <AUTHOR>
 * @date 2023/09/07
 */
@Service
@Slf4j
@RefreshScope
public class WorkFlowManager {
    @Resource
    private WorkFlowService workFlowService;
    @Resource
    private ShopGoodsService shopGoodsService;
    @Resource
    private ShopGoodsContractService shopGoodsContractService;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private ActTaskFeign actTaskFeign;
    @Resource
    private MailService mailService;
    @Value("${workflow.product.approver.userIds}")
    private String userIds;
    @Value("${workflow.contract.approver.userIds}")
    private String contractUserIds;
    @Resource
    private SystemOrganizationService orgSrv;
    public void submitShelves(Collection<Integer> goodsIds) {
        if (CollectionUtil.isEmpty(goodsIds)) return;
        //商品 -> 多个合同 -> 多个审批人员(每一组对应一个企业池)
        List<ShopGoods> shopGoods = shopGoodsService.listByIds(goodsIds);
        if (CollectionUtil.isEmpty(shopGoods)) {
            log.info("未查询到商品:{}", goodsIds);
            return;
        }
        List<ShopSupplier> shopSupplierList = shopSupplierService.list(new LambdaQueryWrapperX<ShopSupplier>().eq(ShopSupplier::getSupplierType, 0));
        Map<String, List<ShopGoods>> goodsMap = shopGoods.stream().collect(Collectors.groupingBy(ShopGoods::getSupplierCode));
        List<Long> otherIds = new ArrayList<>();
        for (String supplierCode : goodsMap.keySet()) {
            List<ShopGoods> shopGoodsList = goodsMap.get(supplierCode);
            List<Long> goodsIdList = shopGoodsList.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList());
            //电商/接口平台
            if (openApiConfig.getSuppliers().contains(supplierCode) || shopSupplierList.stream().map(ShopSupplier::getSupplierCode).collect(Collectors.toList()).contains(supplierCode)) {
                submitShelvesPlatform(goodsIdList.stream().map(Long::intValue).collect(Collectors.toList()));
            } else {
                otherIds.addAll(goodsIdList);
            }
        }
        //基于合同处理 有合同数据只处理合同数据 没有默认查出全部合同处理
        goodsContractOperation(otherIds,shopGoods,true,new HashMap<>());
    }

    /**
     *
     * @param goodsIds
     * @param shopGoods
     * @param consistPlatform 是否创建平台审批
     * @param contractMap 合同集合 key 合同id value 是否审核
     */
    public void goodsContractOperation( List<Long> goodsIds ,  List<ShopGoods> shopGoods  , Boolean consistPlatform , Map<Long,Integer> contractMap){
        if (CollectionUtil.isEmpty(goodsIds) || CollectionUtil.isEmpty(shopGoods)) return;
        List<Long> finalOrganizationIds = orgSrv.queryIdsByType(OrganizationTypeEnum.SYSTEM.getCode());
        //处理的合同集合
        List<ShopGoodsContractVo> shopGoodsContractVos;

        //查询商品与合同关系
        List<ShopGoodsContractVo> goodsContractVos = shopGoodsContractService.queryVoByGoodsIds(goodsIds,1);
        //只处理特定的合同
        if (CollectionUtil.isNotEmpty(contractMap)) {
            log.info("处理特定的合同数据{}",contractMap);
            shopGoodsContractVos = goodsContractVos.stream().filter(item -> contractMap.containsKey(item.getContractId()))
                    .collect(Collectors.toList());
        }else {
            //默认全部的走审批
            shopGoodsContractVos = goodsContractVos;
        }

        if (CollectionUtil.isNotEmpty(shopGoodsContractVos)) {
            log.info("不是电商的商品集合:{} ; 查询的合同信息:{}", goodsIds,shopGoodsContractVos);
            Set<Integer> platformList = new HashSet<>();
            Map<Long, ShopGoods> shopGoodsMap = shopGoods.stream().collect(Collectors.toMap(ShopGoods::getGoodsId, e -> e));
            // 各个商品的合同按对应类型审批
            shopGoodsContractVos.forEach(vo -> {
                //平台
                if (finalOrganizationIds.contains(vo.getSignCompanyId())) {
                    platformList.add(vo.getGoodsId().intValue());
                } else {
                    if (contractMap.containsKey(vo.getContractId()) && contractMap.get(vo.getContractId()) == 0) {
                        //直接调用上架
                        log.info("自动审批合同数据 :{},{}", vo.getGoodsId(), vo.getSignCompanyCode());
                        shopGoodsContractService.approvalGoods(vo.getGoodsId(), vo.getSignCompanyCode(), "导入自动审批通过");
                    } else {
                        //定向服务 (企业)
                        log.info("审批合同数据 :{},{}", vo.getGoodsId(), vo.getSignCompanyCode());
                        submitShelvesWorkflow(shopGoodsMap.get(vo.getGoodsId()), vo.getSignCompanyCode());
                    }
                }
            });
            if (CollectionUtil.isNotEmpty(platformList) && consistPlatform) {
                submitShelvesPlatform(platformList);
            }
        }
    }

    public void submitShelvesWorkflow(ShopGoods goods ,String companyCode ) {
        //不是审批中的开启流程
        GoodsContractApprovalVo vo = shopGoodsContractService.getInstanceState(goods.getGoodsId(), companyCode);
        if (vo!=null && vo.getApprovalState() == 1) {
            log.info("已经有审批流程无需再次发起,审批信息:商品id{},企业编码{}",goods.getGoodsId(),companyCode);
            return;
        }
        //从工作流获取 定向独立供应商 商品走工作流审批
        StartInstanceDto startInstanceDto = new StartInstanceDto();
        startInstanceDto.setBusinessKey(goods.getGoodsId() + "--" + companyCode + "--" + goods.getGoodsCode());
        startInstanceDto.setProcType(DIRECTED_SUPPLIER_GOODS_APPROVE.getCode());
        startInstanceDto.setApplicant(LocalUserHolder.get() == null ? "system" : LocalUserHolder.get().getUsername());
        startInstanceDto.setApplicantName(LocalUserHolder.get() == null ? "system" : LocalUserHolder.get().getNickname());
        startInstanceDto.setCompanyCode(companyCode);
        startInstanceDto.setTenantId(TenantContextHolder.getTenantId());
        ServiceResult<String> serviceResult = actTaskFeign.startInstance(startInstanceDto);
        if (serviceResult.getCode() != 0) {
            log.info("提交审批不成功,商品Code{}, 商品sku{},组织编码{}", goods.getGoodsCode(), goods.getGoodsSku(), companyCode);
            //发邮件
            mailService.sendEmail("【submitShelves】【商品审核发起定向审批流程】", "参数：goodsId：" + goods.getGoodsId() + "组织编码: CompanyCode:" + companyCode, "<EMAIL>");
        }
    }

    public void submitShelvesPlatform(Collection<Integer> goodsIds) {
        String userIds = this.userIds;
        LoginUser loginUser = LocalUserHolder.get();
        if(Objects.nonNull(loginUser)){
            // 发起人撤回功能，将申请用户加入审批人
            userIds = userIds+","+loginUser.getId();
        }
        var workflows = _createWorkFlow(goodsIds);
        _createStep(workflows, userIds);
        workFlowService.beginWorkFlow(workflows.stream().map(Workflow::getId).collect(Collectors.toList()), "即时自动开启流程");
    }

    /**
     * @param goodsId 商品ID
     * @param userIds 用逗号隔开的用户ID
     */
    public void submitShelves(Collection<Integer> goodsId, String userIds) {
        var workflows = _createWorkFlow(goodsId);
        _createStep(workflows, userIds);
        workFlowService.beginWorkFlow(workflows.stream().map(Workflow::getId).collect(Collectors.toList()), "即时自动开启流程");
    }

    /**
     * @param contractRenewal 用逗号隔开的用户ID
     */
    public void submitContractApproval(SystemContractRenewalEntity contractRenewal) {
        String userIds = this.contractUserIds;
        var workflows = createContractWorkFlow(contractRenewal);
        _createContractStep(workflows, userIds);
        workFlowService.beginWorkFlow(workflows.stream().map(Workflow::getId).collect(Collectors.toList()), "即时自动开启流程");
    }

    private List<Workflow> _createWorkFlow(Collection<Integer> goodsIds) {
        List<ShopGoods> goodls = shopGoodsService.getBaseMapper().selectByGoodsIdIn(goodsIds.stream().map(Long::valueOf).collect(Collectors.toList()));
        List<Workflow> addList = new ArrayList<>(16);
        goodls.forEach(goods->{
            Workflow workflow = new Workflow();
            workflow.setTotalStep(2);
            workflow.setName("商品上架");
            workflow.setSupplierCode(goods.getSupplierCode());
            workflow.setDescription("商品上架审核流程");
            workflow.setBusId( goods.getGoodsId());
            workflow.setBusCode(goods.getGoodsSku());
            workflow.setBusCodeX(goods.getGoodsCode());
            workflow.setTenantId(goods.getTenantId());
            _setContext(goods, workflow);
            workflow.setHandler("productWorkFlowHandler");
            addList.add(workflow);
        });
         workFlowService.createWorkFlow(addList);
         return addList;
    }

    private List<Workflow> createContractWorkFlow(SystemContractRenewalEntity contractRenewal) {
        String userIds = this.contractUserIds;
        List<Workflow> addList = new ArrayList<>(16);
        Workflow workflow = new Workflow();
        workflow.setTotalStep(userIds.split(",").length);
        workflow.setName(Objects.isNull(contractRenewal.getOldContractId()) ? "合同签约" : "合同续签");
        workflow.setSupplierCode(contractRenewal.getSupplierCode());
        workflow.setDescription("合同签约审核流程");
        workflow.setBusId( contractRenewal.getId());
        workflow.setBusCode(contractRenewal.getContractCode());
        workflow.setBusCodeX(contractRenewal.getContractName());
        workflow.setTenantId(contractRenewal.getTenantId());
        _setContractContext(contractRenewal, workflow, userIds);
        workflow.setHandler("contractWorkFlowHandler");
        addList.add(workflow);
        workFlowService.createWorkFlow(addList);
        return addList;
    }

    private static void _setContext(ShopGoods goods, Workflow workflow) {
        WorkflowContext content = new WorkflowContext();
        content.setId(goods.getGoodsId());

        content.setUserId(LocalUserHolder.get() == null ? 0 : LocalUserHolder.get().getId());
        content.setTenantId(goods.getTenantId());

        HashMap<String, String> params = new HashMap<>(1);

        // 设置需要的审核字段
        params.put("goodsId", String.valueOf(goods.getGoodsId()));
        params.put("goodsCode", String.valueOf(goods.getGoodsCode()));
        params.put("goodsName", String.valueOf(goods.getGoodsName()));
        params.put("goodsDesc", String.valueOf(goods.getGoodsDesc()));
        params.put("goodsSku", String.valueOf(goods.getGoodsSku()));
        params.put("goodsSpecGoodsWare", String.valueOf(goods.getSpecGoodsWareQd()));
        params.put("brandName", String.valueOf(goods.getBrandName()));
        params.put("goodsModel", String.valueOf(goods.getGoodsModel()));


        content.setParams(params);

        workflow.setContext(JSON.toJSONString(content));
    }
    private static void _setContractContext(SystemContractRenewalEntity contractRenewal, Workflow workflow, String userIds) {
        WorkflowContext content = new WorkflowContext();
        content.setId(contractRenewal.getId());

        content.setUserId(LocalUserHolder.get() == null ? 0 : LocalUserHolder.get().getId());
        content.setTenantId(contractRenewal.getTenantId());

        HashMap<String, String> params = new HashMap<>(1);

        // 设置需要的审核字段
        params.put("contractRenewalId", String.valueOf(contractRenewal.getId()));
        params.put("contractCode", String.valueOf(contractRenewal.getContractCode()));
        params.put("contractName", String.valueOf(contractRenewal.getContractName()));
        params.put("attachmentUrl", String.valueOf(contractRenewal.getAttachmentUrl()));
        params.put("validityStart", String.valueOf(contractRenewal.getValidityStart()));
        params.put("validityEnd", String.valueOf(contractRenewal.getValidityEnd()));
        params.put("companyCode", String.valueOf(contractRenewal.getCompanyCode()));
        params.put("companyName", String.valueOf(contractRenewal.getCompanyName()));
        params.put("supplierCode", String.valueOf(contractRenewal.getSupplierCode()));
        params.put("supplierName", String.valueOf(contractRenewal.getSupplierName()));
        params.put("workFlowStepUsers", userIds);
        content.setParams(params);
        workflow.setContext(JSON.toJSONString(content));
    }

    private void _createStep(Collection<Workflow>  workflow, String userIds) {
        List<WorkflowStep> needCreate = new ArrayList<>();
        workflow.forEach(item->{
            WorkflowStep workflowStep1 = new WorkflowStep();
            workflowStep1.setHandler("productWorkFlowStepHandlerStep0");
            workflowStep1.setWorkflowId(item.getId());
            workflowStep1.setName("商品上架AI审核");
            workflowStep1.setDescription("商品AI解析校验");
            workflowStep1.setUserIds(userIds);
            workflowStep1.setStepOrder(1);
            workflowStep1.setTenantId(item.getTenantId());
            workflowStep1.setSupplierCode(item.getSupplierCode());
            workflowStep1.setBusCode(item.getBusCode());
            workflowStep1.setBusCodeX(item.getBusCodeX());
            workflowStep1.setBusId(item.getBusId());
            needCreate.add(workflowStep1);

            WorkflowStep workflowStep2 = new WorkflowStep();
            workflowStep2.setHandler("productWorkFlowStepHandlerStep1");
            workflowStep2.setWorkflowId(item.getId());
            workflowStep2.setName("商品上架审核");
            workflowStep2.setDescription("商品上架审核");
            workflowStep2.setUserIds(userIds);
            workflowStep2.setStepOrder(2);
            workflowStep2.setTenantId(item.getTenantId());
            workflowStep2.setSupplierCode(item.getSupplierCode());
            workflowStep2.setBusCode(item.getBusCode());
            workflowStep2.setBusCodeX(item.getBusCodeX());
            workflowStep2.setBusId(item.getBusId());
            needCreate.add(workflowStep2);
        });

        workFlowService.createWorkFlowStep(needCreate);
    }

    private void _createContractStep(Collection<Workflow>  workflow, String userIds) {
        List<String> userIdList = Arrays.asList(userIds.split(","));
        List<WorkflowStep> needCreate = new ArrayList<>();
        workflow.forEach(item->{
            int index = 1;
            for(String userId : userIdList){

                WorkflowStep workflowStep = new WorkflowStep();
                workflowStep.setHandler("contractWorkFlowStepHandlerStep"+index);
                workflowStep.setWorkflowId(item.getId());
                workflowStep.setName(index==1?"采购审批":"平台审批");
                workflowStep.setDescription("新增/续签合同审批流程");
                workflowStep.setUserIds(userId);
                workflowStep.setStepOrder(index++);
                workflowStep.setTenantId(item.getTenantId());
                workflowStep.setSupplierCode(item.getSupplierCode());
                workflowStep.setBusCode(item.getBusCode());
                workflowStep.setBusCodeX(item.getBusCodeX());
                workflowStep.setBusId(item.getBusId());
                needCreate.add(workflowStep);
            }
        });

        workFlowService.createWorkFlowStep(needCreate);
    }
}
