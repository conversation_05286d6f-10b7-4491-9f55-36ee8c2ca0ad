package com.ly.yph.api.settlement.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@ApiModel("账单明细表")
@TableName("supplier_invoice_bill")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SupplierInvoiceBill extends BaseEntity {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    @ApiModelProperty("发票申请号")
    private String invoiceApplyNumber;

    @ApiModelProperty("电子发票号")
    private String invoiceNumber;

    @ApiModelProperty("账单id")
    private Long billId;

    @ApiModelProperty("账单编号")
    private String billSn;

    @ApiModelProperty("申请人id")
    private Long applyId;

    @ApiModelProperty("申请人姓名")
    private String applyName;

    @ApiModelProperty("发票申请单未税总金额")
    private BigDecimal amountNaked;

    @ApiModelProperty("发票申请的含税总金额")
    private BigDecimal amountTax;

    @ApiModelProperty("发票申请的含税总金额")
    private BigDecimal invoiceAmountTax;

    @ApiModelProperty("供应商发票状态 0：未开票 1：待审核 2：已开票 3：已驳回")
    private Integer state;

    @ApiModelProperty("不一致说明")
    private String inconsistentRemark;

    @ApiModelProperty("供应商的发票开票时间")
    private LocalDate supplierInvoiceTime;

    @ApiModelProperty("发票上传时间")
    private Date invoiceUploadTime;

    @ApiModelProperty("确认开票的时间")
    private Date invoiceConfirmTime;

    @ApiModelProperty("确认者")
    private String confirmName;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("发票明细类型 默认：0 账单明细类型 ;1:邮费明细类型")
    private Integer billInvoiceType;

    @ApiModelProperty("容差金额")
    private BigDecimal toleranceAmount;

    @ApiModelProperty("发票路径")
    private String invoiceUrl;

    @ApiModelProperty("不一致说明路径")
    private String inconsistentUrl;

    @ApiModelProperty("审批意见")
    private String approveReason;

}
