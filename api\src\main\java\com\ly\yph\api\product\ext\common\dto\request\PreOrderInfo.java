package com.ly.yph.api.product.ext.common.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 预订单信息
 *
 * <AUTHOR>
 * @date 2022/04/12
 */
@Data
@ApiModel("预订单")
public class PreOrderInfo implements Serializable {
    @NotBlank
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 订单号，这里是我们系统的订单号
     */
    @NotBlank
    @ApiModelProperty("友品汇订单号")
    private String yphOrderNo;
    /**
     * 支付方式 jd 默认5 即可
     */
    @NotNull
    @ApiModelProperty("支付方式")
    private Integer paymentType = 5;
    /**
     * 收货人
     */
    @NotBlank
    @ApiModelProperty("收货人")
    private String name;

    /**
     * 收货人地址
     */
    @NotBlank
    @ApiModelProperty("收货人地址")
    private String address;

    /**
     * 收货人电话
     */
    @ApiModelProperty("收货人电话")
    private String phone;
    /**
     * 收货人邮件
     */
    @ApiModelProperty("收货人邮件")
    private String email;
    /**
     * 收货人电话
     */
    @NotBlank
    @ApiModelProperty("收货人电话")
    private String mobile;
    /**
     * 备注
     */
    @NotBlank
    @ApiModelProperty("备注")
    private String remark = "";

    /**
     * 购买账号，不填为对接账号
     */
    @NotBlank
    @ApiModelProperty(value = "购买账号", notes = "不填为对接账号")
    private String purchaseAcount;
    /**
     * 购买人手机号
     */
    @NotBlank
    @ApiModelProperty(value = "购买人手机号")
    private String purchasePhone;
    @NotBlank

    private String province;
    @NotBlank
    private String city;
    @NotBlank
    private String county;

    private String town;
    /**
     * 邮政编码
     */
    @NotBlank
    private String zipCode;

    /**
     * 收票人
     */// 发票相关；
    @NotBlank
    private String invoiceName;
    /**
     * 收票人电话
     */
    @NotBlank
    private String invoicePhone;
    /**
     * 收票人地址
     */
    @NotBlank
    private String invoiceAddress;

    //

    /**
     * zkh
     * 1. 增值税普通发片，2. 增值税专用发票，
     * jd
     * 2增值税专用发票；3 电子票 当发票类型为2时，开票方式只支持2集中开票
     */
    @NotNull
    private Integer invoiceType = 1;

    /**
     * 开票方式
     * 如果是jd， 2为集中开票，4 订单完成后开票
     * zkh 没有这个字段
     */
    private Integer invoicePutType = 2;
    /**
     * 发票内容类型
     * 针对jd
     * 1:明细，100：大类 备注:若增值税专用发票则只能选1 明细
     * zkh 没有这个字段
     */
    private Integer invoiceContentType = 1;
    /**
     * jd:发票类型：4：个人，5：单位
     * zkh 没有这个字段
     */
    private Integer invoiceSelectedTitle = 4;
    /**
     * 发票抬头
     */
    @NotBlank
    private String invoiceTitle;
    @NotBlank
    private String invoiceContent;
    /**
     * 发票纳税人识别号
     */
    private String invoiceTaxPayer;
    /**
     * 发票纳税人开户行
     */
    private String invoiceBank;
    /**
     * 发票纳税人银行账户
     */
    private String invoiceBankAccount;
    /**
     * 发票纳税人银行电话
     */
    private String invoiceBankTel;
    /**
     * 发票纳税人银行地址
     */
    private String invoiceBankAddress;
    /**
     * 运费
     */
    private BigDecimal orderFreightPrice;
    /**
     * 供应商订单含税总价
     */
    private BigDecimal supplierOrderPriceTax;
    /**
     * 供应商订单不含税总价
     */
    private BigDecimal supplierOrderPriceNaked;
    //公司名称
    private String companyName;
    //是否企配仓 0 否 1 是
    private Integer isCompanyStore;
    //销售客户端 1:B端 2:C端
    private Integer saleClient;

    /**
     * 订单类型(1:实物订单 2:虚拟订单 3:服务) GoodsModelEnum枚举类
     */
    private Integer orderModel;
    /**
     * 商品信息
     * sku列表
     */
    @NotEmpty
    private List<OrderSkuList> skuList;
}


