package com.ly.yph.api.virtualgoods.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025年07月10日
 */
@Configuration
@ConfigurationProperties(prefix = "supplier.yzh-virtual")
@Data
@RefreshScope
public class YzhVirtualConfig {
    private String url;
    private String appKey;
    private String appSecret;
    private String code;
}
