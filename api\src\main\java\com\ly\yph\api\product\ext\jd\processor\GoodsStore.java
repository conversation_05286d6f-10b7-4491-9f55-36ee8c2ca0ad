package com.ly.yph.api.product.ext.jd.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopGoodsDetail;
import com.ly.yph.api.goods.entity.ShopGoodsPrice;
import com.ly.yph.api.goods.entity.ShopGoodsStock;
import com.ly.yph.api.goods.manage.GoodsUpDownManage;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.product.ext.common.BaseDataStore;
import com.ly.yph.api.product.ext.jd.config.JDConfig;
import com.ly.yph.api.product.ext.jd.entity.BackupJdGoodsEntity;
import com.ly.yph.api.product.ext.jd.service.BackupJdGoodsService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierClass;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.oss.OSSAutoConfig;
import com.ly.yph.core.oss.OSSClient;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.collection.CollUtil.join;

/**
 * step5 写入 shop_goods 数据库
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Service("GoodsStore")
@Slf4j
public class GoodsStore extends BaseDataStore<Integer, BackupJdGoodsEntity> {
    @Getter
    protected String processName = "JD入标准库";
    @Resource
    private OSSAutoConfig ossConfig;
    @Resource
    private JDConfig config;
    @Resource
    private BackupJdGoodsService backSrv;
    @Resource
    private ShopGoodsService goodSrv;
    @Resource
    private ShopBrandService brandSrv;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private ShopGoodsStockService stockSrv;
    @Resource
    private ShopGoodsPriceService priceSrv;
    @Resource
    private ShopGoodsDetailService detailSrv;
    @Resource
    private YphStandardClassService stClassSrv;
    @Resource
    private ShopSupplierClassService supClassSrv;
    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    @Resource
    private GoodsUpDownManage goodsUpDownManage;

    @Override
    public List<Integer> supplier(int count) {
        return TenantUtils.executeIgnore(() -> backSrv.selectAllForStoreProcess(count));
    }

    @Override
    @DistributedLock(value = "jd_store_process", key = "#id", waitLock = false)
    public void doStoreItem(Integer id) {
        var backup = TenantUtils.executeIgnore(() -> backSrv.getById(id));
        if (backup == null || backup.getSynchronize() != 0) {
            return;
        }

        TenantUtils.execute(backup.getTenantId(), () -> {
            // 清理单条场景下的重复数据，和 batch 逻辑保持一致：仅删除 is_enable 为 0 的冗余记录
            List<ShopGoods> sameGoods = goodSrv.getBaseMapper().queryGoodsByGoodsCodeIn(Collections.singleton(backup.getGoodCode()));
            if (CollectionUtil.isNotEmpty(sameGoods) && sameGoods.size() > 1) {
                List<Long> dupIds = sameGoods.stream()
                        .filter(g -> "0".equals(g.getIsEnable()))
                        .map(ShopGoods::getGoodsId)
                        .collect(Collectors.toList());
                if (!dupIds.isEmpty()) {
                    goodSrv.getBaseMapper().deleteByGoodsIdIn(dupIds);
                }
            }
            var eEnt = goodSrv.getGoodsForStore(backup.getGoodCode());
            doProcessItem(eEnt, backup);
        });

        TenantUtils.executeIgnore(() -> {
            this.saveDetailInfo(CollectionUtil.toList(backup));
            this.saveStockInfo(CollectionUtil.toList(backup));
            this.savePriceInfo(CollectionUtil.toList(backup));

            if (!needCreate.isEmpty()) {
                this.goodSrv.saveBatch(this.needCreate.values());
            }
            if (!needUpdate.isEmpty()) {
                this.goodSrv.getBaseMapper().updateGoodsBatch(this.needUpdate.values());
            }
            if (!needApprove.isEmpty()) {
                this.goodSrv.supplierSubmitGoods(this.needApprove);
            }

            if (!needUpdateBack.isEmpty()) this.backSrv.getBaseMapper().updateSyncInfo(this.needUpdateBack.values());
            if (!needDeleteShelves.isEmpty())
                this.dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolIdBatch(null, needDeleteShelves);
            if (!needSetShelvesInfo.isEmpty()) this.setShelvesInfo(this.needSetShelvesInfo);
        });
    }

    @Override
    @DistributedLock(value = "jd_store_process_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Integer> ids) {
        List<BackupJdGoodsEntity> uEnts = TenantUtils.executeIgnore(() -> this.backSrv.getBaseMapper().selectAllByIdIn(ids));
        // 生成一个goodscode到对象的map
        Map<String, BackupJdGoodsEntity> goodsMap = uEnts.stream().collect(Collectors.toMap(BackupJdGoodsEntity::getGoodCode, Function.identity()));

        List<ShopGoods> shopGoods = TenantUtils.executeIgnore(() -> this.goodSrv.getBaseMapper().queryGoodsByGoodsCodeIn(goodsMap.keySet()));

        // 从shopGoods列表中筛选重复的getGoodsCode，且is_enable为0的项
        Map<String, List<ShopGoods>> groupedByGoodsCode = shopGoods.stream()
                .collect(Collectors.groupingBy(ShopGoods::getGoodsCode));

        List<ShopGoods> duplicatesWithIsEnableZero = groupedByGoodsCode.values().stream()
                .filter(list -> list.size() > 1)
                .flatMap(List::stream)
                .filter(shopGood -> shopGood.getIsEnable().equals("0"))
                .collect(Collectors.toList());
        shopGoods.removeAll(duplicatesWithIsEnableZero);

        if(!CollectionUtil.isEmpty(duplicatesWithIsEnableZero)){
            goodSrv.getBaseMapper().deleteByGoodsIdIn(duplicatesWithIsEnableZero.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList()));
        }

        // 生成一个goodscode到对象的map
        Map<String, ShopGoods> shopGoodsMap = shopGoods.stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, Function.identity()));

        CountDownLatch latch = new CountDownLatch(uEnts.size());
        uEnts.parallelStream().forEach(item -> {
            try {
                TenantUtils.execute(item.getTenantId(), () -> {
                    ShopGoods eEnt = shopGoodsMap.get(item.getGoodCode());
                    doProcessItem(eEnt, item);
                });
            } catch (Exception ex) {
                log.error("JdGoodsSaveError:{}", ExceptionUtil.stacktraceToString(ex, 5000));
            } finally {
                latch.countDown();
            }
        });
        try {
            latch.await(); // 等待所有任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // 处理中断异常
        }
        TenantUtils.executeIgnore(() -> {
            if (!uEnts.isEmpty()) this.saveDetailInfo(uEnts);
            if (!uEnts.isEmpty()) this.saveStockInfo(uEnts);
            if (!uEnts.isEmpty()) this.savePriceInfo(uEnts);
            if (!needCreate.isEmpty()) this.goodSrv.saveBatch(this.needCreate.values());
            if (!needUpdate.isEmpty()) this.goodSrv.getBaseMapper().updateGoodsBatch(this.needUpdate.values());
            if (!needApprove.isEmpty()) this.goodSrv.supplierSubmitGoods(this.needApprove);
            if (!needUpdateBack.isEmpty()) this.backSrv.getBaseMapper().updateSyncInfo(this.needUpdateBack.values());
        });

        commonIoExecutors.execute(() -> {
            TenantUtils.executeIgnore(() -> {

                if (!needSetShelvesInfo.isEmpty()) this.setShelvesInfo(this.needSetShelvesInfo);
                if (!needDeleteShelves.isEmpty())
                    this.dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolIdBatch(null, needDeleteShelves);
            });
        });
    }

    private void doProcessItem(ShopGoods eEnt, BackupJdGoodsEntity backup) {
        try {
            var entity = new ShopGoods();
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, entity);
            } else {
                // 首次转换，用ai分析标准分类和品牌
                setCategory(backup, entity);
                setBrand(backup, entity);
            }

            setIntroduce(backup, entity, eEnt);
            fillFiled(backup, entity);
            entity.setTenantId(backup.getTenantId());
            // 存几个表的数据
            if (eEnt != null) {
                this.needUpdate.put(entity.getGoodsCode(), entity);
                this.needDeleteShelves.add(entity.getGoodsId());
            } else {
                this.needCreate.put(entity.getGoodsCode(), entity);
            }
            // 判断是否需要审核
            if (entity.getAuditState() == 1) {
                this.needSetShelvesInfo.add(backup);
            } else {
                this.needApprove.add(entity);
            }
            backup.setSynchronize((byte) 1);
        } catch (Exception ex) {
            log.error("process_save_store_error：tenant-id:{},id:{} | stack:{}", TenantContextHolder.getTenantId(), backup.getId(), ExceptionUtil.stacktraceToString(ex));
            backup.setSynchronize((byte) 2);
        }
        needUpdateBack.put(backup.getGoodCode(), backup);
    }


    private void setShelvesInfo(List<BackupJdGoodsEntity> uEnts) {
        if (!uEnts.isEmpty())
            goodsUpDownManage.goodsCodeUpToLiteFlow(uEnts.stream().filter(item -> item.getSkuState() == 1).map(BackupJdGoodsEntity::getGoodCode).collect(Collectors.toList()));

        if (!uEnts.isEmpty())
            goodsUpDownManage.goodsCodeDown(uEnts.stream().filter(item -> item.getSkuState() == 0).map(BackupJdGoodsEntity::getGoodCode).collect(Collectors.toList()), "供应商推送下架",null);

    }

    private void savePriceInfo(Collection<BackupJdGoodsEntity> uEnts) {
        List<ShopGoodsPrice> needUpdate = new ArrayList<>(16);
        List<ShopGoodsPrice> needCreate = new ArrayList<>(16);
        val eEnts = priceSrv.getPriceForTrans(uEnts.stream().map(BackupJdGoodsEntity::getGoodCode).collect(Collectors.toList()));

        uEnts.forEach(item -> {
            var eEnt = eEnts.get(item.getGoodCode());
            var sEnt = new ShopGoodsPrice();
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }

            sEnt.setIsEnable("1");
            sEnt.setGoodsCode(item.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(item.getSkuId()));
            sEnt.setGoodsOriginalPrice(item.getJdPrice());
            sEnt.setGoodsOriginalNakedPrice(item.getJdPriceNaked());
            sEnt.setGoodsPactPrice(item.getGivenPrice());
            sEnt.setGoodsPactNakedPrice(item.getGivenPriceNaked());
            sEnt.setUpdateTime(new Date());
            sEnt.setTenantId(item.getTenantId());

            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.priceSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.priceSrv.getBaseMapper().updateGoodsPriceBatch(needUpdate);
    }

    private void saveStockInfo(Collection<BackupJdGoodsEntity> uEnts) {
        List<ShopGoodsStock> needUpdate = new ArrayList<>(16);
        List<ShopGoodsStock> needCreate = new ArrayList<>(16);
        val eEnts = stockSrv.getStockForTrans(uEnts.stream().map(BackupJdGoodsEntity::getGoodCode).collect(Collectors.toList()));
        uEnts.forEach(uEnt -> {
            var sEnt = new ShopGoodsStock();
            var eEnt = eEnts.get(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSkuId()));
            sEnt.setStockAvailable(0);
            sEnt.setStockAlert(0);
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setIsEnable("1");
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.stockSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.stockSrv.getBaseMapper().updateStockInfoBatch(needUpdate);
    }

    private void saveDetailInfo(Collection<BackupJdGoodsEntity> uEnts) {
        List<ShopGoodsDetail> needUpdate = new ArrayList<>(16);
        List<ShopGoodsDetail> needCreate = new ArrayList<>(16);
        val eEnts = detailSrv.getDetailForTrans(uEnts.stream().map(BackupJdGoodsEntity::getGoodCode).collect(Collectors.toList()));
        uEnts.forEach(uEnt -> {
            var sEnt = new ShopGoodsDetail();
            var eEnt = eEnts.get(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSkuId()));
            sEnt.setIsEnable("1");
            sEnt.setGoodsMoq(0);
            sEnt.setGoodsClick(0);
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setCommentNum(0);
            sEnt.setGoodsCollect(0);
            sEnt.setGoodsMoq(uEnt.getLowestBuy());
            // 处理规格
            String spec = "";
            var specJson = new JSONObject();
            var pa = new JSONArray();
            if (!StrUtil.isBlank(uEnt.getParamGroupAttrlist())) {
                pa = JSONObject.parseArray(uEnt.getParamGroupAttrlist());
            }

            if (pa != null) {
                for (int i = 0; i < pa.size(); i++) {
                    var g = new Gson().fromJson(pa.getString(i), JSONObject.class);
                    var l = g.getJSONArray("paramAttributeList");
                    for (int k = 0; k < l.size(); k++) {
                        var json = l.getJSONObject(k);
                        var s = JSONArray.parseArray(json.getJSONArray("paramAttrValList").toString(), String.class);
                        var a = join(s, ",");
                        spec = spec + json.getString("paramAttrName") + " " + a + " ";
                        specJson.put(json.getString("paramAttrName"), a);
                    }
                }
                sEnt.setGoodsSpec(spec);
                sEnt.setGoodsSpecArray(JSON.toJSONString(specJson));
            }
            sEnt.setGoodsImage(uEnt.getImagePath());
            sEnt.setGoodsImageMore(uEnt.getImagePathMore());
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.detailSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.detailSrv.getBaseMapper().updateByPrimaryKeySelectiveBatch(needUpdate);
    }

    private void fillFiled(BackupJdGoodsEntity b, ShopGoods e) {
        e.setGoodsName(b.getSkuName());
        e.setGoodsCode(b.getGoodCode());
        e.setGoodsSku(String.valueOf(b.getSkuId()));
        e.setBackupGoodId(b.getId());
        e.setSupplierCode(config.getCode());
        e.setSupplierName(config.getName());
        ShopSupplier supplierEnt = shopSupplierService.selectByCode(config.getCode());
        e.setOrganizationId(supplierEnt.getOrganizationId());
        e.setSupplierType(supplierEnt.getSupplierType());
        e.setGoodsDesc(b.getSkuName() + " 销售单位：" + b.getSaleUnit());
        e.setGoodsSubtitle(b.getSkuName() + " 销售单位：" + b.getSaleUnit());
        e.setUpdateTime(new Date());
        e.setSaleUnit(b.getSaleUnit());
        e.setProductionPlace(b.getProductArea());
        e.setSpecGoodsWareQd(b.getWareInfo());
        e.setTaxCode(b.getTaxCode());
        e.setTaxRate(b.getTaxRatePercentage().intValue());
        // 这里不知道多少天
        e.setDeliveryTime(7);
        e.setMaterialsCode(b.getSeoModel());
        e.setManufacturerMaterialNo(b.getSeoModel());
        e.setGoodsKeywords("");
        e.setIsEnable("1");
        // 供应商档案配置不审批或者曾经已经审批通过的,无需审批,其他都需要审批
        e.setAuditState(supplierEnt.getIsAudited() == 0 || (e.getAuditState() != null && e.getAuditState() == 1) ? 1 : 0);
        e.setGoodsExplain("");
        e.setGoodsFeatures("");
        e.setHeedEvent("");
        e.setIsSpecial(0);
        // 先让商品不可见
        e.setSpecialEvent("");
        e.setGoodsMobileBoydUrl("");
    }

    private void setBrand(BackupJdGoodsEntity uEnt, ShopGoods sEnt) {
        StringBuilder spec = new StringBuilder();
        if (StrUtil.isNotBlank(uEnt.getParamGroupAttrlist()) && !"null".equals(uEnt.getParamGroupAttrlist())) {
            cn.hutool.json.JSONArray pa = JSONUtil.parseArray(uEnt.getParamGroupAttrlist());
            for (int i = 0; i < pa.size(); i++) {
                var l = pa.getJSONObject(i).getJSONArray("paramAttributeList");
                for (int k = 0; k < l.size(); k++) {
                    var json = l.getJSONObject(k);
                    var s = JSONUtil.toList(json.getJSONArray("paramAttrValList"), String.class);
                    var a = join(s, ",");
                    spec.append(json.getStr("paramAttrName")).append(" ").append(a).append(" ");
                }
            }
        }
        brandSrv.syncBrandByAi(uEnt.getSkuName(), uEnt.getSkuName() + " 销售单位：" + uEnt.getSaleUnit(), uEnt.getSeoModel(),
                "", spec.toString(), uEnt.getBrandName(), uEnt.getSaleUnit(), sEnt);
    }

    private void setCategory(BackupJdGoodsEntity uEnt, ShopGoods sEnt) {
        sEnt.setSupplierClass(uEnt.getCategory());
        sEnt.setSupplierClassName(uEnt.getCategoryName());
        ShopSupplierClass supplierClass = supClassSrv.selectOneBySupAndClass(uEnt.getCategory(), config.getCode());
        if (supplierClass != null) {
            sEnt.setSupplierClassId(supplierClass.getSupplierClassId().toString());
        }
        String query = uEnt.getSkuName() + " 销售单位：" + uEnt.getSaleUnit();
        stClassSrv.syncClassByAi(query, sEnt);
    }

    private void setIntroduce(BackupJdGoodsEntity uEnt, ShopGoods sEnt, ShopGoods eEnt) {
        if (eEnt != null) {
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                // 先删除原来的数据
                ossClient.delete(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), sEnt.getGoodsCode()));

                // 再次添加数据
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), sEnt.getGoodsCode()), uEnt.getIntroduce());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(config.getDefaultShopGoodBodyUrl());
            }
        } else {
            // 内容设置到oss,商品下架注意清除
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", config.getOssPrefix(), uEnt.getGoodCode()), uEnt.getIntroduce());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(config.getDefaultShopGoodBodyUrl());
            }
        }
    }

}
