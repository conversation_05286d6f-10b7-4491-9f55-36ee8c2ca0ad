package com.ly.yph.api.settlement.supplier.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SupplierInvoiceApproveDto {

    @NotNull(message = "基础信息不能为空！")
    private Long id;

    @NotNull(message = "操作类型不能为空！")
    @ApiModelProperty("审批类型 0：通过 1：驳回")
    private Integer approveType;

    private String rejectReason;
}
