package com.ly.yph.api.settlement.supplier.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoicePostageExcelDto;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceDetailPostage;
import com.ly.yph.api.settlement.supplier.mapper.SupplierInvoiceDetailPostageMapper;
import com.ly.yph.core.base.exception.types.ParameterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupplierInvoiceDetailPostageService extends ServiceImpl<SupplierInvoiceDetailPostageMapper, SupplierInvoiceDetailPostage> {

    public List<SettleShopBillPostageDetail> getSupplierInvoicePostageDetailListForExcel(List<SupplierInvoicePostageExcelDto> supplierInvoicePostageExcelDtoList,
                                                                                          Long billId) {
        List<String> orderNumberList = supplierInvoicePostageExcelDtoList.stream()
                .map(SupplierInvoicePostageExcelDto::getOrderNumber).distinct().collect(Collectors.toList());

        List<SettleShopBillPostageDetail> supplierInvoiceDetailPostageList = new ArrayList<>();

        Lists.partition(orderNumberList, 1000).forEach(subList ->
                supplierInvoiceDetailPostageList.addAll(getBaseMapper().getSupplierInvoicePostageDetail(subList, billId, null))
        );

        if (CollectionUtil.isEmpty(supplierInvoiceDetailPostageList)) {
            throw new ParameterException("未查询到邮费明细数据！");
        }

        Map<String, SettleShopBillPostageDetail> postageMap = supplierInvoiceDetailPostageList.stream()
                .collect(Collectors.toMap(SettleShopBillPostageDetail::getOrderNumber, Function.identity()));

        List<SettleShopBillPostageDetail> validData = new ArrayList<>();
        for (SupplierInvoicePostageExcelDto supplierInvoicePostageExcelDto : supplierInvoicePostageExcelDtoList) {

            SettleShopBillPostageDetail shopBillPostageDetail = postageMap.get(supplierInvoicePostageExcelDto.getOrderNumber());
            if (shopBillPostageDetail == null) {
                throw new ParameterException("未查询到订单号：{}的邮费明细！", supplierInvoicePostageExcelDto.getOrderNumber());
            }

            if (shopBillPostageDetail.getPostage().compareTo(supplierInvoicePostageExcelDto.getPostage()) != 0) {
                throw new ParameterException("订单号：{}的邮费存在异常，请检查数据！", supplierInvoicePostageExcelDto.getOrderNumber());
            }

            validData.add(shopBillPostageDetail);
        }

        return validData;

    }


}
