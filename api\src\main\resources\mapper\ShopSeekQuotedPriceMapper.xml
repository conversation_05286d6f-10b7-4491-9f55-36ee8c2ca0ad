<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.seeksource.mapper.ShopSeekQuotedPriceMapper">
    <select id="queryQuotedPrice" resultType="com.ly.yph.api.seeksource.vo.QuotedPriceRespVo">
        select ssqp.id,
               sss.supplier_code,
               sss.supplier_name,
               sss.liaise_people,
               sss.liaise_mobile,
               ssqp.seek_goods_id,
               ssg.goods_name,
               ssg.brand_name,
               ssg.spec_model,
               ssg.goods_desc,
               ssg.unit_budget_price_naked,
               ssg.demand_num,
               ssg.mara_matnr,
               ssg.remark,
               ssg.sale_unit,
               group_concat(ifnull(ssqs.goods_sku, '')) as goods_sku,
               group_concat(ifnull(sg.goods_code, ''))  as goods_code,
               ssqp.goods_image_url,
               ssqp.spec_info,
               ssqp.standard_sku,
               ssqp.refer_brand_name,
               ssqp.standard_sku_url,
               ssqp.goods_moq,
               ssqp.delivery_time,
               ssqp.goods_pact_price,
               ssqp.goods_pact_naked_price,
               ssqp.goods_sale_price,
               ssqp.goods_sale_naked_price,
               ssqp.tax_rate,
               ssqp.total_tax_price,
               ssqp.total_naked_price,
               ssqp.quoted_ip,
               ssqp.quoted_count,
               ssqp.is_auto_quoted,
               ssqp.bidden_success,
               ssqp.create_time
        from shop_seek_quoted_price ssqp
                 left join shop_seek_supplier sss
                           on sss.seek_price_id = ssqp.seek_price_id and sss.supplier_code = ssqp.supplier_code
                 left join shop_seek_goods ssg on ssg.id = ssqp.seek_goods_id
                 left join shop_seek_quoted_sku ssqs on ssqs.quoted_price_id = ssqp.id
                 left join shop_goods sg on sg.goods_sku = ssqs.goods_sku and sg.supplier_code = ssqs.supplier_code and sg.is_enable = 1
        where 1 = 1
        <if test="vo.seekPriceId != null">
          and ssqp.seek_price_id = #{vo.seekPriceId}
        </if>
        <if test="vo.seekGoodsId != null">
          and ssqp.seek_goods_id = #{vo.seekGoodsId}
        </if>
        <if test="vo.supplierCode != null and vo.supplierCode != ''">
          and ssqp.supplier_code = #{vo.supplierCode}
        </if>
        <if test="vo.priceState != null">
          and ssqp.price_state = #{vo.priceState}
        </if>
        <if test="vo.biddenSuccess != null">
          and ssqp.bidden_success = #{vo.biddenSuccess}
        </if>
        <if test="vo.seekPriceNumber != null and vo.seekPriceNumber != ''">
          and ssg.seek_price_number = #{vo.seekPriceNumber}
        </if>
        group by ssqp.id, ssqp.goods_sale_price
        order by ssqp.goods_sale_price
    </select>

    <select id="cusExportQuotedPriceExcel" resultType="com.ly.yph.api.seeksource.vo.ExportQuotedPriceCusExcel">
        select sscp.seek_price_number,
               ssg.goods_index,
               ssg.goods_name,
               ssg.brand_name,
               ssg.spec_model,
               ssg.goods_desc,
               ssg.unit_budget_price_naked,
               ssg.demand_num,
               ssg.mara_matnr,
               ssg.remark,
               ssqp.supplier_code,
               ssqp.supplier_name,
               ssqp.refer_brand_name,
               ssqp.spec_info,
               ssqp.standard_sku_url,
               ssqp.tax_rate,
               ssqp.delivery_time,
               ssqp.goods_sale_naked_price,
               ssqp.goods_sale_price,
               date_format(ssqp.create_time, "%Y-%m-%d %H:%i:%s") as create_time,
               group_concat(ifnull(ssqs.goods_sku, ''))           as goods_sku,
               group_concat(ifnull(sg.goods_code, ''))            as goods_code
        from shop_seek_compare_price sscp
                 left join shop_seek_goods ssg on ssg.seek_price_number = sscp.seek_price_number
                 left join shop_seek_quoted_price ssqp on ssqp.seek_goods_id = ssg.id and ssqp.seek_price_id = sscp.id
                 left join shop_seek_quoted_sku ssqs on ssqs.quoted_price_id = ssqp.id
                 left join shop_goods sg on sg.goods_sku = ssqs.goods_sku and sg.supplier_code = ssqs.supplier_code and sg.is_enable = 1
        where sscp.seek_price_number = #{seekPriceNumber}
        <if test="biddenSuccess != null">
            and ssqp.bidden_success = #{biddenSuccess}
        </if>
        group by ssg.id, ssg.goods_index, ssqp.id, ssqp.create_time
        order by ssg.goods_index, ssqp.create_time desc
    </select>

    <select id="supExportQuotedPriceExcel" resultType="com.ly.yph.api.seeksource.vo.ExportQuotedPriceSupExcel">
        select sscp.seek_price_number,
               ssg.goods_index,
               ssg.goods_name,
               ssg.brand_name,
               ssg.spec_model,
               ssg.goods_desc,
               ssg.unit_budget_price_naked,
               ssg.demand_num,
               ssg.mara_matnr,
               ssg.remark,
               ssqp.supplier_name,
               ssqp.refer_brand_name,
               ssqp.spec_info,
               ssqp.standard_sku_url,
               ssqp.tax_rate,
               ssqp.delivery_time,
               ssqp.goods_pact_naked_price,
               ssqp.goods_pact_price,
               ssqp.goods_sale_naked_price,
               ssqp.goods_sale_price,
               date_format(ssqp.create_time, "%Y-%m-%d %H:%i:%s") as create_time,
               group_concat(ifnull(ssqs.goods_sku, ''))           as goods_sku,
               group_concat(ifnull(sg.goods_code, ''))            as goods_code
        from shop_seek_compare_price sscp
                 left join shop_seek_goods ssg on ssg.seek_price_number = sscp.seek_price_number
                 left join shop_seek_quoted_price ssqp on ssqp.seek_goods_id = ssg.id and ssqp.seek_price_id = sscp.id
                 left join shop_seek_quoted_sku ssqs on ssqs.quoted_price_id = ssqp.id
                 left join shop_goods sg on sg.goods_sku = ssqs.goods_sku and sg.supplier_code = ssqs.supplier_code and sg.is_enable = 1
        where sscp.seek_price_number = #{seekPriceNumber}
          and ssqp.supplier_code = #{supplierCode}
        <if test="biddenSuccess != null">
            and ssqp.bidden_success = #{biddenSuccess}
        </if>
        group by ssg.id, ssg.goods_index, ssqp.id, ssqp.create_time
        order by ssg.goods_index, ssqp.create_time desc
    </select>

    <select id="exportQuotedPriceExcel" resultType="com.ly.yph.api.seeksource.vo.ExportAdminQuotedPriceExcel">
        select ssg.goods_index,
               ssg.goods_name,
               ssg.brand_name,
               ssg.spec_model,
               ssg.goods_desc,
               ssg.unit_budget_price_naked,
               ssg.demand_num,
               ssg.mara_matnr,
               ssg.remark,
               ssqp.supplier_name,
               ssqp.refer_brand_name,
               ssqp.spec_info,
               ssqp.standard_sku_url,
               ssqp.tax_rate,
               ssqp.delivery_time,
               ssqp.goods_pact_price,
               ssqp.goods_sale_price,
               ssqp.create_time,
               if(ssqp.bidden_success = 1, '是', '否') as bidden_success,
               sscp.seek_count
        from shop_seek_compare_price sscp
                 left join shop_seek_goods ssg on ssg.seek_price_number = sscp.seek_price_number
                 left join shop_seek_quoted_price ssqp on ssqp.seek_goods_id = ssg.id
        where sscp.seek_price_number = #{seekPriceNumber}
          and sscp.is_last = 1
        order by ssg.goods_index, ssqp.create_time desc
    </select>

    <delete id="phyDeleteCopyQuotedPrice">
        delete
        from shop_seek_quoted_price
        where seek_price_id = #{seekPriceId}
          and supplier_code = #{supplierCode}
          and is_auto_quoted = 1
          and seek_goods_id in
        <foreach collection="seekGoodsIdSet" item="seekGoodsId" open="(" close=")" separator="," >
            #{seekGoodsId}
        </foreach>
    </delete>

    <select id="getSeekPriceByGoodsCode" resultType="com.ly.yph.api.openapi.v1.vo.HondaCarSeekPriceResultVo">
        select ssqp.supplier_code,
               ssqp.supplier_name,
               #{goodsSku} as 'goods_sku',
               ssqp.goods_sale_price,
               ssqp.goods_sale_naked_price,
               ssqp.tax_rate
        from shop_seek_quoted_sku ssqs
                 left join shop_seek_quoted_price ssqp on ssqp.id = ssqs.quoted_price_id
                 left join shop_seek_compare_price sscp on sscp.id = ssqp.seek_price_id
        where ssqs.supplier_code = #{supplierCode}
          and ssqs.goods_sku = #{goodsSku}
          and ssqp.bidden_success = 1
          and sscp.company_code = 'DSDB000'
        order by sscp.create_time desc
        limit 1
    </select>

    <select id="getVoyahSeekQuotedPriceId" resultType="java.lang.Long">
        select ssqp.id
        from shop_seek_quoted_price ssqp
                 left join shop_seek_goods ssg on ssg.id = ssqp.seek_goods_id
        where ssqp.seek_price_id = #{seekPriceId}
          and ssqp.supplier_code = #{supplierCode}
          and ssqp.price_state = 1
          and ssg.mara_matnr = #{maraMatnr}
    </select>

    <select id="queryQuotedSkuDetail" resultType="com.ly.yph.api.seeksource.vo.QuotedSkuDetailVo">
        select ssqs.id,
               ssqs.supplier_code,
               ssqs.supplier_name,
               ssqs.goods_sku,
               ssqp.goods_pact_price,
               ssqp.goods_pact_naked_price,
               ssqp.goods_sale_price,
               ssqp.goods_sale_naked_price,
               ssg.goods_desc,
               sg.goods_id,
               ssqs.goods_code,
               dgps.id as goods_pool_sub_id
        from shop_seek_quoted_sku ssqs
                 left join shop_seek_quoted_price ssqp on ssqp.id = ssqs.quoted_price_id
                 left join shop_seek_compare_price sscp on sscp.id = ssqs.seek_price_id
                 left join shop_seek_goods ssg on ssg.id = ssqs.seek_goods_id
                 left join shop_goods sg on sg.goods_code = ssqs.goods_code
                 left join dfmall_goods_pool dgp on dgp.company_code = sscp.company_code and dgp.goods_pool_level = 2
                 left join dfmall_goods_pool_sub dgps on dgps.goods_pool_id = dgp.id and dgps.goods_id = sg.goods_id
        where ssqs.seek_price_id = #{seekPriceId}
        <if test="seekGoodsId != null">
            and ssqs.seek_goods_id = #{seekGoodsId}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and ssqs.supplier_code = #{supplierCode}
        </if>
    </select>
    <select id="cusExportQuotedPriceExcelPlus" resultType="java.util.Map">
        select sscp.seek_price_number,
        CAST( ssg.goods_index AS CHAR) as goods_index,
        ssg.mara_matnr,
        ssg.goods_name,
        ssg.brand_name,
        ssg.spec_model,
        ssg.goods_desc,
        CAST( ROUND(ssg.unit_budget_price_naked, 2) AS CHAR) as unit_budget_price_naked,
        CAST( ssg.demand_num AS CHAR) as demand_num,
        ssg.remark,
        (SELECT GROUP_CONCAT(sp.supplier_name SEPARATOR '、')
        FROM shop_seek_quoted_price sp
        WHERE sp.seek_goods_id = ssg.id
        AND sp.bidden_success = 1) AS biddingSupplier,
        '' as minBiddingSupplier,
        CAST( ROUND(MIN(ssqp.goods_sale_naked_price),2) AS CHAR) AS goods_naked_price,
        <!-- 动态生成每个供应商的6个固定字段 -->
        <foreach collection="supplierCodes" item="supplierCode" index="index">
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN ssqp.supplier_name END) AS `${supplierCode}_supplier_name`,
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN
            CONCAT_WS(
            CHAR ( 10 ),-- 换行符
            CONCAT( '报价品牌：', IFNULL( ssg.brand_name, '' ) ),
            CONCAT( '报价规格型号：', IFNULL( ssqp.spec_info, '' ) ),
            CONCAT( '标杆商品：', IFNULL( ssqp.standard_sku_url, '' ) ),
            CONCAT( '税率：', IFNULL( ssqp.tax_rate, '' ) ),
            CONCAT( '报价时间：', date_format( ssqp.create_time, "%Y-%m-%d %H:%i:%s" ) )
            )
            END) AS `${supplierCode}_refer_info`,
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN CAST(ssqp.delivery_time AS CHAR ) END) AS
            `${supplierCode}_delivery_time`,
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN CAST(ROUND(ssqp.goods_sale_naked_price ,2) AS CHAR )
            END) AS `${supplierCode}_naked_price`,
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN CAST( ROUND(ssqp.goods_sale_price,2) AS CHAR ) END)
            AS `${supplierCode}_price`,
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN ifnull(ssqs.goods_sku, '') END) AS `${supplierCode}_goods_sku`,
            MAX(CASE WHEN ssqp.supplier_code = #{supplierCode} THEN ifnull(sg.goods_code, '') END) AS `${supplierCode}_goods_code`,
        </foreach>
        '' AS dummy -- 处理末尾逗号
        from shop_seek_compare_price sscp
        left join shop_seek_goods ssg on ssg.seek_price_number = sscp.seek_price_number
        left join shop_seek_quoted_price ssqp on ssqp.seek_goods_id = ssg.id and ssqp.seek_price_id = sscp.id
        left join shop_seek_quoted_sku ssqs on ssqs.quoted_price_id = ssqp.id
        left join shop_goods sg on sg.goods_sku = ssqs.goods_sku and sg.supplier_code = ssqs.supplier_code and sg.is_enable = 1
        where sscp.seek_price_number = #{seekPriceNumber}
        <if test="biddenSuccess != null">
            and ssqp.bidden_success = #{biddenSuccess}
        </if>
        and ssqp.price_state = 1
        GROUP BY
        ssg.id,
        ssg.goods_index
        ORDER BY
        ssg.goods_index
    </select>
    <select id="querySupplierListBySeekPriceNumber"
            resultType="com.ly.yph.api.seeksource.entity.ShopSeekSupplier">
        SELECT
        sss.*
        FROM
        shop_seek_compare_price sscp
        LEFT JOIN shop_seek_supplier sss ON sss.seek_price_id = sscp.id
        LEFT JOIN shop_seek_quoted_price ssqp ON ssqp.seek_price_id = sscp.id
        WHERE
        sscp.seek_price_number = #{seekPriceNumber}
        AND sss.quoted_state != - 1
        <if test="biddenSuccess != null">
            and ssqp.bidden_success = #{biddenSuccess}
        </if>
        GROUP BY
        sss.supplier_code
    </select>

    <select id="getValidBiddenPrice" resultType="com.ly.yph.api.seeksource.dto.BiddenBottomPriceDto">
        select ssqs.goods_sku,
               sscp.seek_price_number,
               ssqp.goods_pact_naked_price
        from shop_seek_compare_price sscp
                 left join shop_seek_quoted_price ssqp on ssqp.seek_price_id = sscp.id
                 left join shop_seek_quoted_sku ssqs on ssqs.quoted_price_id = ssqp.id
        where now() between sscp.validity_time_start and sscp.validity_time_end
          and ssqp.supplier_code = #{supplierCode}
          and ssqp.bidden_success = 1
          and ssqs.goods_sku in
        <foreach collection="skuSet" item="sku" open="(" close=")" separator="," >
            #{sku}
        </foreach>
    </select>
</mapper>

