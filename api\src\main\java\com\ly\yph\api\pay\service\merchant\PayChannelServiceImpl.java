package com.ly.yph.api.pay.service.merchant;

import static com.ly.yph.api.pay.enums.ErrorCodeConstants.CHANNEL_EXIST_SAME_CHANNEL_ERROR;
import static com.ly.yph.api.pay.enums.ErrorCodeConstants.CHANNEL_NOT_EXISTS;

import com.ly.yph.api.pay.controller.admin.merchant.vo.channel.PayChannelCreateReqVO;
import com.ly.yph.api.pay.controller.admin.merchant.vo.channel.PayChannelExportReqVO;
import com.ly.yph.api.pay.controller.admin.merchant.vo.channel.PayChannelPageReqVO;
import com.ly.yph.api.pay.controller.admin.merchant.vo.channel.PayChannelUpdateReqVO;
import com.ly.yph.api.pay.convert.channel.PayChannelConvert;
import com.ly.yph.api.pay.dal.dataobject.merchant.PayChannelDO;
import com.ly.yph.api.pay.dal.mysql.merchant.PayChannelMapper;
import com.ly.yph.api.pay.enums.ErrorCodeConstants;
import com.ly.yph.core.base.CommonStatusEnum;
import com.ly.yph.core.base.ServiceExceptionUtil;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.payment.core.client.PayClientConfig;
import com.ly.yph.core.payment.core.client.PayClientFactory;
import com.ly.yph.core.payment.core.enums.PayChannelEnum;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.tenant.core.aop.TenantIgnore;

import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 支付渠道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PayChannelServiceImpl implements PayChannelService {

    /**
     * 定时执行 {@link #schedulePeriodicRefresh()} 的周期
     * 因为已经通过 Redis Pub/Sub 机制，所以频率不需要高
     */
    private static final long SCHEDULER_PERIOD = 5 * 60 * 1000L;

    /**
     * 缓存菜单的最大更新时间，用于后续的增量轮询，判断是否有更新
     */
    private volatile Date maxUpdateTime;

    @Resource
    private PayClientFactory payClientFactory;

    @Resource
    private PayChannelMapper channelMapper;

    @Resource
    private Validator validator;

    @Resource
    @Lazy // 注入自己，所以延迟加载
    private PayChannelService self;

    private static void validPayChannel(final PayChannelDO channel) {
        if (channel == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PAY_CHANNEL_NOT_FOUND);
        }
        if (CommonStatusEnum.DISABLE.getStatus().equals(channel.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PAY_CHANNEL_IS_DISABLE);
        }
    }

    @Override
    @PostConstruct
    @TenantIgnore // 忽略自动化租户，全局初始化本地缓存
    public void initPayClients() {
        // 获取支付渠道，如果有更新
        final List<PayChannelDO> payChannels = this.loadPayChannelIfUpdate(this.maxUpdateTime);
        if (CollUtil.isEmpty(payChannels)) {
            return;
        }

        // 创建或更新支付 Client
        payChannels.forEach(payChannel -> this.payClientFactory.createOrUpdatePayClient(payChannel.getId(), payChannel.getCode(), payChannel.getConfig()));

        // 写入缓存
        this.maxUpdateTime = CollectionUtils.getMaxValue(payChannels, PayChannelDO::getUpdateTime);
        log.info("[initPayClients][初始化 PayChannel 数量为 {}]", payChannels.size());
    }

    @Scheduled(fixedDelay = SCHEDULER_PERIOD, initialDelay = SCHEDULER_PERIOD)
    public void schedulePeriodicRefresh() {
        this.self.initPayClients();
    }

    /**
     * 如果支付渠道发生变化，从数据库中获取最新的全量支付渠道。
     * 如果未发生变化，则返回空
     *
     * @param maxUpdateTime 当前支付渠道的最大更新时间
     * @return 支付渠道列表
     */
    private List<PayChannelDO> loadPayChannelIfUpdate(final Date maxUpdateTime) {
        // 第一步，判断是否要更新。
        if (maxUpdateTime == null) { // 如果更新时间为空，说明 DB 一定有新数据
            log.info("[loadPayChannelIfUpdate][首次加载全量支付渠道]");
        } else { // 判断数据库中是否有更新的支付渠道
            if (this.channelMapper.selectCountByUpdateTimeGt(maxUpdateTime) == 0) {
                return null;
            }
            log.info("[loadPayChannelIfUpdate][增量加载全量支付渠道]");
        }
        // 第二步，如果有更新，则从数据库加载所有支付渠道
        return this.channelMapper.selectList();
    }

    @Override
    public Long createChannel(final PayChannelCreateReqVO reqVO) {
        // 断言是否有重复的
        final PayChannelDO channelDO = this.getChannelByConditions(reqVO.getMerchantId(), reqVO.getAppId(), reqVO.getCode());
        if (ObjectUtil.isNotNull(channelDO)) {
            throw HttpException.exception(CHANNEL_EXIST_SAME_CHANNEL_ERROR);
        }

        // 新增渠道
        final PayChannelDO channel = PayChannelConvert.INSTANCE.convert(reqVO);
        this.settingConfigAndCheckParam(channel, reqVO.getConfig());
        this.channelMapper.insert(channel);
        // TODO zhangl02：缺少刷新本地缓存的机制
        return channel.getId();
    }

    @Override
    public void updateChannel(final PayChannelUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateChannelExists(updateReqVO.getId());
        // 更新
        final PayChannelDO channel = PayChannelConvert.INSTANCE.convert(updateReqVO);
        this.settingConfigAndCheckParam(channel, updateReqVO.getConfig());
        this.channelMapper.updateById(channel);
        // TODO zhangl02：缺少刷新本地缓存的机制
    }

    @Override
    public void deleteChannel(final Long id) {
        // 校验存在
        this.validateChannelExists(id);
        // 删除
        this.channelMapper.deleteById(id);
        // TODO zhangl02：缺少刷新本地缓存的机制
    }

    private void validateChannelExists(final Long id) {
        if (this.channelMapper.selectById(id) == null) {
            throw HttpException.exception(CHANNEL_NOT_EXISTS);
        }
    }

    @Override
    public PayChannelDO getChannel(final Long id) {
        return this.channelMapper.selectById(id);
    }

    @Override
    public List<PayChannelDO> getChannelList(final Collection<Long> ids) {
        return this.channelMapper.selectBatchIds(ids);
    }

    @Override
    public PageResp<PayChannelDO> getChannelPage(final PageReq pager, final PayChannelPageReqVO pageReqVO) {
        return this.channelMapper.selectPage(pager, pageReqVO);
    }

    @Override
    public List<PayChannelDO> getChannelList(final PayChannelExportReqVO exportReqVO) {
        return this.channelMapper.selectList(exportReqVO);
    }

    /**
     * 根据支付应用ID集合获得支付渠道列表
     *
     * @param appIds 应用编号集合
     * @return 支付渠道列表
     */
    @Override
    public List<PayChannelDO> getChannelListByAppIds(final Collection<Long> appIds) {
        return this.channelMapper.getChannelListByAppIds(appIds);
    }

    /**
     * 根据条件获取渠道数量
     *
     * @param merchantId 商户编号
     * @param appid      应用编号
     * @param code       渠道编码
     * @return 数量
     */
    @Override
    public Integer getChannelCountByConditions(final Long merchantId, final Long appid, final String code) {
        return this.channelMapper.selectCount(merchantId, appid, code);
    }

    /**
     * 根据条件获取渠道
     *
     * @param merchantId 商户编号
     * @param appid      应用编号
     * @param code       渠道编码
     * @return 数量
     */
    @Override
    public PayChannelDO getChannelByConditions(final Long merchantId, final Long appid, final String code) {
        return this.channelMapper.selectOne(merchantId, appid, code);
    }

    /**
     * 设置渠道配置以及参数校验
     *
     * @param channel   渠道
     * @param configStr 配置
     */
    private void settingConfigAndCheckParam(final PayChannelDO channel, final String configStr) {
        // 得到这个渠道是微信的还是支付宝的
        final Class<? extends PayClientConfig> payClass = PayChannelEnum.getByCode(channel.getCode()).getConfigClass();
        if (ObjectUtil.isNull(payClass)) {
            throw HttpException.exception(CHANNEL_NOT_EXISTS);
        }
        final PayClientConfig config = JSONUtil.toBean(configStr, payClass);
        // 验证参数
        config.validate(this.validator);
        channel.setConfig(config);
    }

    @Override
    public PayChannelDO validPayChannel(final Long id) {
        final PayChannelDO channel = this.channelMapper.selectById(id);
        PayChannelServiceImpl.validPayChannel(channel);
        return channel;
    }

    @Override
    public PayChannelDO validPayChannel(final Long appId, final String code) {
        final PayChannelDO channel = this.channelMapper.selectByAppIdAndCode(appId, code);
        PayChannelServiceImpl.validPayChannel(channel);
        return channel;
    }

    @Override
    public PayChannelDO selectByCode(String code) {
        return this.channelMapper.selectByCode( code);
    }
}
