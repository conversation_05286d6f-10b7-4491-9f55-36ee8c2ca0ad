package com.ly.yph.api.virtualgoods.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.ly.yph.api.virtualgoods.config.YzhVirtualConfig;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.util.JsonUtils;
import com.ly.yph.core.util.RRException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年11月18日
 */
@Slf4j
@Service
public class YZHConnectHelper {

    @Resource
    private YzhVirtualConfig yzhVirtualConfig;


    public <T> List<T> doPost(String path, Object obj, String businessLog, Class<T> elementClass) {
        String accessToken = queryAccessToken();
        Map params = BeanUtil.toBean(obj, Map.class);
        params.put("accessToken", accessToken);
        log.info("【" + businessLog + "】 URL:" + yzhVirtualConfig.getUrl() + path);
        log.info("【" + businessLog + "】 Params:" + JsonUtils.toJsonString(params));
        String response = HttpUtil.post(yzhVirtualConfig.getUrl() + path, JsonUtils.toJsonString(params));
        log.info("【" + businessLog + "】 Response:" + response);
        Map responseMap = JSONUtil.toBean(response, Map.class);
        List<T> result;
        if ("00000".equalsIgnoreCase(Convert.toStr(responseMap.get("code")))) {
            result = JSONUtil.toList(Convert.toStr(responseMap.get("result")), elementClass);
        } else {
            throw new ParameterException("【" + businessLog + "】" + Convert.toStr(responseMap.get("desc")));
        }
        return result;
    }

    public <T> T doPostObj(String path, Object obj, String businessLog, Class<T> elementClass) {
        String accessToken = queryAccessToken();
        Map params = BeanUtil.toBean(obj, Map.class);
        params.put("accessToken", accessToken);
        log.info("【" + businessLog + "】 URL:" + yzhVirtualConfig.getUrl() + path);
        log.info("【" + businessLog + "】 Params:" + JsonUtils.toJsonString(params));
        String response = HttpUtil.post(yzhVirtualConfig.getUrl() + path, JsonUtils.toJsonString(params));
        log.info("【" + businessLog + "】 Response:" + response);
        Map responseMap = JSONUtil.toBean(response, Map.class);
        T result;
        if ("00000".equalsIgnoreCase(Convert.toStr(responseMap.get("code")))) {
            result = JSONUtil.toBean(Convert.toStr(responseMap.get("result")), elementClass);
        } else {
            throw new RRException("【" + businessLog + "】" + Convert.toStr(responseMap.get("desc")));
        }
        return result;
    }

    private String queryAccessToken() {
        String getAccessTokenUrl = "/open/api/access/queryAccessToken";
        String time = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_FORMAT);
        Map<String, String> map = MapUtil.newHashMap(3);
        map.put("appKey", yzhVirtualConfig.getAppKey());
        map.put("appSecret", DigestUtils.md5DigestAsHex(yzhVirtualConfig.getAppSecret().getBytes()).toLowerCase());
        map.put("timestamp", time);
        String sign = yzhVirtualConfig.getAppKey() + yzhVirtualConfig.getAppSecret() + time + yzhVirtualConfig.getAppSecret();
        String signStr = DigestUtils.md5DigestAsHex(sign.getBytes()).toLowerCase();
        map.put("sign", signStr);
        map.put("grantType", "MD5");
        log.info("【queryAccessToken】URL:" + yzhVirtualConfig.getUrl() + getAccessTokenUrl);
        log.info("【queryAccessToken】params:" + JsonUtils.toJsonString(map));
        String response = HttpUtil.post(yzhVirtualConfig.getUrl() + getAccessTokenUrl, JsonUtils.toJsonString(map));
        log.info("【queryAccessToken】response:" + response);
        String accessToken = "";
        Map responseMap = JSONUtil.toBean(response, Map.class);
        if (Convert.toBool(responseMap.get("success"))) {
            Map result = JSONUtil.toBean(Convert.toStr(responseMap.get("result")), Map.class);
            accessToken = Convert.toStr(result.get("accessToken"));
        }
        return accessToken;
    }
}
