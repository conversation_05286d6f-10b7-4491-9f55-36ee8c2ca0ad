package com.ly.yph.api.settlement.common.dto.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("供应商出账消息预加载信息")
@Data
public class PreSupplierBillOutMessageDto {

    private Long deliveryId;

    private String companyCode;

    private Long tenantId;

    private String invoiceSubject;

    private Long deliveryDetailId;

    @ApiModelProperty("0:历史包裹 1：新包裹")
    private Integer isHistory;

    @ApiModelProperty("验收明细id")
    private Long checkFormDetailId;

    private Integer sapOrderType;
}
