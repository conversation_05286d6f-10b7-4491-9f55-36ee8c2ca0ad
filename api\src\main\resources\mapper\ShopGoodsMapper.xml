<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.goods.mapper.ShopGoodsMapper">
    <resultMap id="BaseResultMap" type="com.ly.yph.api.goods.entity.ShopGoods">
        <!--@mbg.generated-->
        <!--@Table shop_goods-->
        <id column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode"/>
        <result column="goods_sku" jdbcType="VARCHAR" property="goodsSku"/>
        <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="brand_name" jdbcType="VARCHAR" property="brandName"/>
        <result column="sale_unit" jdbcType="VARCHAR" property="saleUnit"/>
        <result column="production_place" jdbcType="VARCHAR" property="productionPlace"/>
        <result column="delivery_time" jdbcType="INTEGER" property="deliveryTime"/>
        <result column="spec_goods_ware_qd" jdbcType="VARCHAR" property="specGoodsWareQd"/>
        <result column="materials_code" jdbcType="VARCHAR" property="materialsCode"/>
        <result column="manufacturer_material_no" jdbcType="VARCHAR" property="manufacturerMaterialNo"/>
        <result column="goods_keywords" jdbcType="VARCHAR" property="goodsKeywords"/>
        <result column="goods_boyd_url" jdbcType="VARCHAR" property="goodsBoydUrl"/>
        <result column="goods_mobile_boyd_url" jdbcType="VARCHAR" property="goodsMobileBoydUrl"/>
        <result column="supplier_class" jdbcType="VARCHAR" property="supplierClass"/>
        <result column="supplier_class_name" jdbcType="VARCHAR" property="supplierClassName"/>
        <result column="first_level_gcid" jdbcType="VARCHAR" property="firstLevelGcid"/>
        <result column="second_level_gcid" jdbcType="VARCHAR" property="secondLevelGcid"/>
        <result column="third_level_gcid" jdbcType="VARCHAR" property="thirdLevelGcid"/>
        <result column="goods_subtitle" jdbcType="VARCHAR" property="goodsSubtitle"/>
        <result column="tax_code" jdbcType="VARCHAR" property="taxCode"/>
        <result column="tax_rate" jdbcType="INTEGER" property="taxRate"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="is_enable" jdbcType="BOOLEAN" property="isEnable"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="index_status" jdbcType="TINYINT" property="indexStatus"/>
        <result column="backup_good_id" jdbcType="INTEGER" property="backupGoodId"/>
        <result column="audit_state" jdbcType="INTEGER" property="auditState"/>
        <result column="goods_explain" jdbcType="VARCHAR" property="goodsExplain"/>
        <result column="goods_features" jdbcType="VARCHAR" property="goodsFeatures"/>
        <result column="heed_event" jdbcType="VARCHAR" property="heedEvent"/>
        <result column="is_special" jdbcType="INTEGER" property="isSpecial"/>
        <result column="special_event" jdbcType="VARCHAR" property="specialEvent"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="nake_price" jdbcType="DECIMAL" property="nakePrice"/>
        <result column="stand_category_name" jdbcType="VARCHAR" property="standCategoryName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        goods_id,
        goods_name,
        goods_code,
        goods_sku,
        goods_desc,
        brand_id,
        brand_name,
        sale_unit,
        production_place,
        delivery_time,
        spec_goods_ware_qd,
        materials_code,
        manufacturer_material_no,
        goods_keywords,
        goods_boyd_url,
        goods_mobile_boyd_url,
        supplier_class,
        supplier_class_name,
        first_level_gcid,
        second_level_gcid,
        third_level_gcid,
        goods_subtitle,
        tax_code,
        tax_rate,
        supplier_code,
        supplier_name,
        goods_label,
        auth_mode,
        shelves_state,
        shelves_reason,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time,
        index_status,
        backup_good_id,
        audit_state,
        goods_explain,
        goods_features,
        heed_event,
        is_special,
        special_event,
        price,
        nake_price,
        stand_category_name
    </sql>
    <resultMap id="shopGoodsMap" type="com.ly.yph.api.goods.entity.ShopGoods">
        <!--@mbg.generated-->
        <!--@Table shop_goods-->
        <id column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode"/>
        <result column="goods_sku" jdbcType="VARCHAR" property="goodsSku"/>
        <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="brand_name" jdbcType="VARCHAR" property="brandName"/>
        <result column="sale_unit" jdbcType="VARCHAR" property="saleUnit"/>
        <result column="production_place" jdbcType="VARCHAR" property="productionPlace"/>
        <result column="delivery_time" jdbcType="INTEGER" property="deliveryTime"/>
        <result column="spec_goods_ware_qd" jdbcType="VARCHAR" property="specGoodsWareQd"/>
        <result column="materials_code" jdbcType="VARCHAR" property="materialsCode"/>
        <result column="manufacturer_material_no" jdbcType="VARCHAR" property="manufacturerMaterialNo"/>
        <result column="goods_keywords" jdbcType="VARCHAR" property="goodsKeywords"/>
        <result column="goods_boyd_url" jdbcType="VARCHAR" property="goodsBoydUrl"/>
        <result column="goods_mobile_boyd_url" jdbcType="VARCHAR" property="goodsMobileBoydUrl"/>
        <result column="supplier_class" jdbcType="VARCHAR" property="supplierClass"/>
        <result column="supplier_class_name" jdbcType="VARCHAR" property="supplierClassName"/>
        <result column="first_level_gcid" jdbcType="VARCHAR" property="firstLevelGcid"/>
        <result column="second_level_gcid" jdbcType="VARCHAR" property="secondLevelGcid"/>
        <result column="third_level_gcid" jdbcType="VARCHAR" property="thirdLevelGcid"/>
        <result column="goods_subtitle" jdbcType="VARCHAR" property="goodsSubtitle"/>
        <result column="tax_code" jdbcType="VARCHAR" property="taxCode"/>
        <result column="tax_rate" jdbcType="INTEGER" property="taxRate"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="is_enable" jdbcType="BOOLEAN" property="isEnable"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="index_status" jdbcType="TINYINT" property="indexStatus"/>
        <result column="backup_good_id" jdbcType="INTEGER" property="backupGoodId"/>
        <result column="audit_state" jdbcType="INTEGER" property="auditState"/>
        <result column="goods_explain" jdbcType="VARCHAR" property="goodsExplain"/>
        <result column="goods_features" jdbcType="VARCHAR" property="goodsFeatures"/>
        <result column="heed_event" jdbcType="VARCHAR" property="heedEvent"/>
        <result column="is_special" jdbcType="INTEGER" property="isSpecial"/>
        <result column="special_event" jdbcType="VARCHAR" property="specialEvent"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="nake_price" jdbcType="DECIMAL" property="nakePrice"/>
        <result column="stand_category_name" jdbcType="VARCHAR" property="standCategoryName"/>
    </resultMap>
    <sql id="base_column_list">
        <!--@mbg.generated-->
        goods_id,
        goods_name,
        goods_code,
        goods_sku,
        goods_desc,
        brand_id,
        brand_name,
        sale_unit,
        production_place,
        delivery_time,
        spec_goods_ware_qd,
        manufacturer_material_no,
        goods_keywords,
        goods_boyd_url,
        goods_mobile_boyd_url,
        supplier_class,
        supplier_class_name,
        first_level_gcid,
        first_class,
        first_class_name,
        second_level_gcid,
        second_class,
        second_class_name,
        third_level_gcid,
        third_class,
        third_class_name,
        goods_subtitle,
        tax_code,
        tax_rate,
        supplier_code,
        supplier_name,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time,
        index_status,
        backup_good_id,
        audit_state,
        goods_explain,
        goods_features,
        heed_event,
        is_special,
        special_event,
        price,
        nake_price,
        stand_category_name
    </sql>

    <resultMap id="shopGoodsPageMap" type="com.ly.yph.api.goods.entity.ShopGoods">
        <result column="goods_sku" property="goodsSku"/>
        <result column="goods_desc" property="goodsDesc"/>
        <result column="brand_id" property="brandId"/>
        <result column="brand_name" property="brandName"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="first_level_gcid" property="firstLevelGcid"/>
        <result column="second_level_gcid" property="secondLevelGcid"/>
        <result column="third_level_gcid" property="thirdLevelGcid"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="is_enable" property="isEnable"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier" property="modifier"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <!-- todo 删除商品时这里，如果还需要加表，注意这里也要同步增加-->
    <delete id="deleteShopGood">
        <!--shop goods-->
        update shop_goods
        set is_enable = '0',
        update_time=NOW()
        where goods_code = #{goodCode,jdbcType=VARCHAR};
        <!--detail-->
        update shop_goods_detail
        set is_enable = '0',
        update_time=NOW()
        where goods_code = #{goodCode,jdbcType=VARCHAR};
        <!--price-->
        update shop_goods_price
        set is_enable = '0',
        update_time=NOW()
        where goods_code = #{goodCode,jdbcType=VARCHAR};
        <!--stock-->
        update shop_goods_stock
        set is_enable = '0',
        update_time=NOW()
        where goods_code = #{goodCode,jdbcType=VARCHAR};
    </delete>
    <delete id="realDeleteByGoodsCode">
        <!--shop goods-->
        delete
        from shop_goods
        where goods_code = #{goodsCode,jdbcType=VARCHAR};
        <!--detail-->
        delete
        from shop_goods_detail
        where goods_code = #{goodsCode,jdbcType=VARCHAR};
        <!--price-->
        delete
        from shop_goods_price
        where goods_code = #{goodsCode,jdbcType=VARCHAR};
        <!--stock-->
        delete
        from shop_goods_stock
        where goods_code = #{goodsCode,jdbcType=VARCHAR};
    </delete>

    <delete id="deleteShopGoods">
        <!--shop goods-->
        update shop_goods
        set is_enable = '0',
        update_time=NOW()
        where goods_code IN
        <foreach collection="goodCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;
        <!--detail-->
        update shop_goods_detail
        set is_enable = '0',
        update_time=NOW()
        where goods_code IN
        <foreach collection="goodCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;
        <!--price-->
        update shop_goods_price
        set is_enable = '0',
        update_time=NOW()
        where goods_code IN
        <foreach collection="goodCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;
        <!--stock-->
        update shop_goods_stock
        set is_enable = '0',
        update_time=NOW()
        where goods_code IN
        <foreach collection="goodCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;

        <!--contract-->
        update shop_goods_contract set is_enable = 0,update_time=NOW()
        where goods_code IN
        <foreach collection="goodCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;

        <!--incidental-->
        update shop_goods_incidental_detail set is_enable = 0,update_time=NOW()
        where goods_code IN
        <foreach collection="goodCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>;
    </delete>

    <!--auto generated by MybatisCodeHelper on 2022-01-28-->
    <select id="getGoodCodeBySkuAndSupplier" resultType="java.lang.String">
        select goods_code
        from shop_goods
        where goods_sku = #{goodsSku}
          and supplier_code = #{supplierCode}
          and is_enable = 1
    </select>
    <!-- 供应商商品信息查询-列表 -->
    <select id="queryGoodsPage" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
        SELECT
               sg.goods_id,
               sg.goods_desc,
               sg.supplier_code,
               ss.supplier_full_name,
               ss.supplier_short_name AS supplierName,
               sg.goods_code,
               sg.goods_sku,
               sg.brand_name,
               sgd.goods_moq,
               sgd.goods_spec,
               sg.delivery_time,
               sg.audit_state,
               sgp.goods_original_price,
               sgp.goods_pact_price,
               1                      as shelves_state,
               sg.create_time,
               sg.third_level_gcid,
               s.stock_available,
               s.id                   as stock_id
        FROM shop_goods sg
                 LEFT JOIN shop_supplier ss ON ss.supplier_code = sg.supplier_code
                 LEFT JOIN shop_goods_detail sgd ON sgd.goods_code = sg.goods_code
                 LEFT JOIN shop_goods_price sgp ON sgp.goods_code = sg.goods_code
                 LEFT JOIN yph_standard_class c3 ON c3.standard_class_id = sg.third_level_gcid
                 LEFT JOIN shop_goods_stock s ON s.goods_code = sg.goods_code
        <where>
            <if test="shopGoodsQueryDto.supplierCode != null and shopGoodsQueryDto.supplierCode != &quot;&quot;">
                and sg.supplier_code = #{shopGoodsQueryDto.supplierCode}
            </if>
            <if test="shopGoodsQueryDto.firstLevelGcid != null and shopGoodsQueryDto.firstLevelGcid != &quot;&quot;">
                and sg.first_level_gcid = #{shopGoodsQueryDto.firstLevelGcid}
            </if>
            <if test="shopGoodsQueryDto.typeCode != null and shopGoodsQueryDto.typeCode != &quot;&quot;">
                and c3.type_code = #{shopGoodsQueryDto.typeCode}
            </if>
            <if test="shopGoodsQueryDto.goodsDesc != null and shopGoodsQueryDto.goodsDesc != &quot;&quot;">
                and sg.goods_desc like CONCAT('%', #{shopGoodsQueryDto.goodsDesc}, '%')
            </if>
            <if test="shopGoodsQueryDto.goodsCode != null and shopGoodsQueryDto.goodsCode != &quot;&quot;">
                and sg.goods_code = #{shopGoodsQueryDto.goodsCode}
            </if>
            <if test="shopGoodsQueryDto.goodsSku != null and shopGoodsQueryDto.goodsSku != &quot;&quot;">
                and sg.goods_sku = #{shopGoodsQueryDto.goodsSku}
            </if>
            <if test="shopGoodsQueryDto.supplierType != null">
                and ss.supplier_type = #{shopGoodsQueryDto.supplierType}
            </if>
            AND sg.is_enable = 1
        </where>
        order by sg.create_time desc
    </select>

    <update id="updateTaxRate">
        update shop_goods
        set tax_rate = #{taxRate,jdbcType=VARCHAR}
        where goods_code = #{goodsCode,jdbcType=INTEGER}
          AND is_enable = 1
    </update>
    <select id="selectAllForOpenApi" resultType="com.ly.yph.api.openapi.v1.vo.product.ApiShopGoodsInfoRespVO">
        select sg.goods_sku,
        sg.goods_id,
        sg.goods_code,
        sg.goods_desc,
        sg.goods_keywords,
        sg.goods_name,
        sg.goods_from,
        sg.goods_boyd_url,
        sg.third_class_name,
        sg.third_class,
        sg.third_level_gcid,
        sg.first_class_name,
        sg.first_class,
        sg.first_level_gcid,
        sg.second_class_name,
        sg.second_class,
        sg.second_level_gcid,
        sg.goods_from,
        sg.heed_event,
        sg.spec_goods_ware_qd,
        sg.special_event,
        sg.is_special,
        sg.tax_code,
        sg.supplier_code,
        sg.supplier_name,
        sg.supplier_type,
        sg.goods_explain,
        sg.goods_features,
        sg.goods_label,
        sg.tax_rate,
        sg.brand_name,
        sg.brand_id,
        sg.supplier_class,
        sg.manufacturer_material_no,
        sg.delivery_time,
        sg.sale_unit,
        sd.goods_image as spu_image,
        sd.goods_image_more as spu_imageMore,
        sd.goods_moq,
        sd.goods_spec_array,
        sd.supplier_extended
        from shop_goods as sg
        inner join shop_goods_detail as sd on sg.goods_code = sd.goods_code
        where sg.goods_code = #{goodsCode} and sg.is_enable = 1
    </select>
    <select id="queryGoodsPoolVoPage" resultType="com.ly.yph.api.goods.vo.GoodsManageVo">
        SELECT  g.goods_id,
                g.goods_code,
                g.goods_sku,
                g.goods_desc,
                g.supplier_code,
                g.supplier_type,
                g.supplier_name,
                CONCAT(g.first_class_name,'/',g.second_class_name,'/',g.third_class_name) as goodsClassNames,
                case g.shelves_state
                when -2 then -2
                when -1 then 3
                when 0 then 3
                when 1 then 2
                else 0 end as goodsState,
                g.audit_state,
                case g.tenant_id
                when 1 then '东风商城'
                else '东风商城(积分)' end as tenant_name,
                IFNULL(p.process_status,0) AS isAiStandard,
                g.sale_client,g.goods_model
        FROM shop_goods g
        LEFT JOIN standard_products p on p.goods_code = g.goods_code
        <where>
            <if test="ew.goodsCode != null and ew.goodsCode != ''">
                AND g.goods_code IN
                <foreach item="item" index="index" collection="ew.goodsCode.split(',')" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="ew.goodsSku != null and ew.goodsSku != ''">
                AND g.goods_sku IN
                <foreach item="item" index="index" collection="ew.goodsSku.split(',')" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="ew.supplierCode != null and ew.supplierCode != ''">
                AND g.supplier_code = #{ew.supplierCode}
            </if>
            <if test="ew.supplierType != null ">
                AND g.supplier_type = #{ew.supplierType}
            </if>
            <if test="ew.goodsState == 3  ">
                AND g.shelves_state IN(-1,0)
            </if>
            <if test="ew.goodsState == 2 ">
                AND g.shelves_state IN(1)
            </if>
            <if test="ew.goodsState == -2 ">
                AND g.shelves_state IN(-2)
            </if>
            <if test="ew.goodsState == 1 ">
                AND g.audit_state IN(0)
            </if>
            <if test="ew.createTime != null">
                AND g.create_time between #{ew.createTime[0]} and #{ew.createTime[1]}
            </if>
            AND g.is_enable = 1
        </where>
        order by g.goods_id desc
    </select>
    <select id="queryGoodsPoolVoPage_u" resultType="com.ly.yph.api.goods.vo.GoodsManageVo">
        SELECT  g.goods_id,
                g.goods_code,
                g.goods_sku,
                g.goods_desc,
                g.supplier_code,
                g.supplier_type,
                g.supplier_name,
                CONCAT(g.first_class_name,'/',g.second_class_name,'/',g.third_class_name) as goodsClassNames,
                case g.shelves_state
                when -1 then 3
                when 0 then 3
                when 1 then 2
                else 0 end as goodsState,
                g.audit_state
        FROM shop_goods g
        <where>
            <if test="ew.goodsCode != null and ew.goodsCode != ''">
                AND g.goods_code = #{ew.goodsCode}
            </if>
            <if test="ew.goodsSku != null and ew.goodsSku != ''">
                AND g.goods_sku = #{ew.goodsSku}
            </if>
            <if test="ew.supplierCode != null and ew.supplierCode != ''">
                AND g.supplier_code = #{ew.supplierCode}
            </if>
            <if test="ew.supplierType != null ">
                AND g.supplier_type = #{ew.supplierType}
            </if>
            <if test="ew.goodsState == 3  ">
                AND g.shelves_state IN(-1,0)
            </if>
            <if test="ew.goodsState == 2  ">
                AND g.shelves_state IN(1)
            </if>
            <if test="ew.goodsState == 1 ">
                AND g.audit_state IN(0)
            </if>
            AND g.is_enable = 1
        </where>
        GROUP BY g.goods_id order by g.goods_id desc
        LIMIT #{page.start}, #{page.pageSize}
    </select>
    <select id="queryAllSupplierBackUpGoods" resultType="com.ly.yph.api.goods.vo.GoodsManageVo">
        SELECT goodsSku,goodsDesc,supplierCode,supplierName,goodsState, 0 as saleClient
        FROM (
            SELECT ofs.sku as goodsSku, ofs.name as goodsDesc, 'DSOFS00' as supplierCode,'欧菲斯' as supplierName, 4 as goodsState
            FROM backup_ofs_goods ofs
            <where>
                <if test="ew.goodsSku != null and ew.goodsSku != ''">
                    AND ofs.sku IN
                    <foreach item="item" index="index" collection="ew.goodsSku.split(',')" open="(" separator="," close=")">
                        '${item}'
                    </foreach>
                </if>
                <if test="ew.goodsState == 4 ">
                    AND (ofs.synchronize IN(2,4) or ofs.validate_flag = 2 )
                </if>
            </where>
            UNION
            SELECT xy.sku as goodsSku,xy.name as goodsDesc,'DSXY000' as supplierCode,'西域' as supplierName, 4 as goodsState
            FROM backup_xy_goods xy
            <where>
                <if test="ew.goodsSku != null and ew.goodsSku != ''">
                    AND xy.sku IN
                    <foreach item="item" index="index" collection="ew.goodsSku.split(',')" open="(" separator="," close=")">
                        '${item}'
                    </foreach>
                </if>
                <if test="ew.goodsState == 4 ">
                    AND (xy.synchronize IN(2,4) or xy.validate_status = 2)
                </if>
            </where>
            UNION
            SELECT jd.sku_id as goodsSku,jd.sku_name as goodsDesc,'DSJD000' as supplierCode,'京东' as supplierName, 4 as goodsState
            FROM backup_jd_goods jd
            <where>
                <if test="ew.goodsSku != null and ew.goodsSku != ''">
                    AND jd.sku_id IN
                    <foreach item="item" index="index" collection="ew.goodsSku.split(',')" open="(" separator="," close=")">
                        '${item}'
                    </foreach>
                </if>
                <if test="ew.goodsState == 4 ">
                    AND (jd.synchronize IN(2,4) or jd.validate_flag = 2)
                </if>
            </where>
            UNION
            SELECT zkh.sku as goodsSku,zkh.name as goodsDesc,'DSZKH00' as supplierCode,'震坤行' as supplierName, 4 as goodsState
            FROM backup_zkh_goods zkh
            <where>
                <if test="ew.goodsSku != null and ew.goodsSku != ''">
                    AND zkh.sku IN
                    <foreach item="item" index="index" collection="ew.goodsSku.split(',')" open="(" separator="," close=")">
                        '${item}'
                    </foreach>
                </if>
                <if test="ew.goodsState == 4 ">
                    AND (zkh.synchronize IN(2,4) or zkh.validate_flag = 2)
                </if>
            </where>
            UNION
            SELECT qx.sku as goodsSku,qx.name as goodsDesc,'DSQXKJ0' as supplierCode,'齐心科技' as supplierName, 4 as goodsState
            FROM backup_qx_goods qx
            <where>
                <if test="ew.goodsSku != null and ew.goodsSku != ''">
                    AND qx.sku IN
                    <foreach item="item" index="index" collection="ew.goodsSku.split(',')" open="(" separator="," close=")">
                        '${item}'
                    </foreach>
                </if>
                <if test="ew.goodsState == 4 ">
                    AND (qx.synchronize IN(2,4) or qx.validate_flag = 2)
                </if>
            </where>
        ) t
        <where>
            <if test="ew.supplierCode != null and ew.supplierCode != ''">
                AND t.supplierCode = #{ew.supplierCode}
            </if>
        </where>

    </select>
    <select id="queryGoodsPoolVoPage_c" resultType="java.lang.Integer">
        SELECT count(*) FROM (
        SELECT goods_id
        FROM shop_goods g
        <where>
            <if test="ew.goodsCode != null and ew.goodsCode != ''">
                AND g.goods_code = #{ew.goodsCode}
            </if>
            <if test="ew.goodsSku != null and ew.goodsSku != ''">
                AND g.goods_sku = #{ew.goodsSku}
            </if>
            <if test="ew.supplierCode != null and ew.supplierCode != ''">
                AND g.supplier_code = #{ew.supplierCode}
            </if>
            <if test="ew.supplierType != null ">
                AND g.supplier_type = #{ew.supplierType}
            </if>
            <if test="ew.goodsState == 3  ">
                AND g.shelves_state IN(-1,0)
            </if>
            <if test="ew.goodsState == 2 ">
                AND g.shelves_state IN(1)
            </if>
            <if test="ew.goodsState == 1 ">
                AND g.audit_state IN(0)
            </if>
            <if test="ew.createTime != null">
                AND g.create_time between #{ew.createTime[0]} and #{ew.createTime[1]}
            </if>
            AND g.is_enable = 1
        </where>
        ) as t1
    </select>
    <select id="querySupplierGoodsExport" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
        SELECT
                g.goods_id,
                g.goods_code,
                g.goods_sku,
                g.goods_desc,
                g.supplier_code,
                g.supplier_name,
                CONCAT(g.first_class_name,'/',g.second_class_name,'/',g.third_class_name) as goodsClassNames,
                g.shelves_state,
                g.audit_state
        FROM shop_goods g
        <where>
            <if test="ew.goodsCode != null and ew.goodsCode != ''">
                AND g.goods_code = #{ew.goodsCode}
            </if>
            <if test="ew.goodsSku != null and ew.goodsSku != ''">
                AND g.goods_sku = #{ew.goodsSku}
            </if>
            <if test="ew.supplierCode != null and ew.supplierCode != ''">
                AND g.supplier_code = #{ew.supplierCode}
            </if>
            <if test="ew.supplierType != null ">
                AND g.supplier_type = #{ew.supplierType}
            </if>
            <if test="ew.goodsState == 3  ">
                AND g.shelves_state IN(-1,0)
            </if>
            <if test="ew.goodsState == 2 ">
                AND g.shelves_state IN(1)
            </if>
            <if test="ew.goodsState == 1 ">
                AND g.audit_state IN(0)
            </if>
            <if test="ew.createTime != null">
                AND g.create_time between #{ew.createTime[0]} and #{ew.createTime[1]}
            </if>
            AND g.is_enable = 1
        </where>
        GROUP BY g.goods_id order by g.goods_id desc
    </select>

    <!--自动生成代码 2022-03-08-->
    <select id="selectGoodsCodeByGoodsSkuAndSupplierCode" resultType="java.lang.String">
        select goods_code
        from shop_goods
        where goods_sku = #{goodsSku}
          and supplier_code = #{supplierCode}
          and is_enable = '1'
    </select>

    <select id="selectGoodsCodeByGoodsSkusAndSupplierCode" resultType="java.lang.String">
        select goods_code
        from shop_goods
        where goods_sku in
        <foreach collection="goodsSkus" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and supplier_code = #{supplierCode}
        and is_enable = '1'
    </select>

    <select id="selectAllByGoodCodes" resultType="com.ly.yph.api.goods.vo.SkuAndSupplierVo">
        select goods_sku as skuId, supplier_code as supplier
        from shop_goods
        where goods_code in
        <foreach close=")" collection="goodsCodes" item="gCode" open="(" separator=",">
            #{gCode,jdbcType=VARCHAR}
        </foreach>
        and is_enable = '1'
    </select>

    <!--<select id="selectEsExtend" resultType="com.ly.yph.api.goods.entity.ShopGoodsForEsEntityExt">-->
    <!--select GROUP_CONCAT(DISTINCT a.organization_id) AS organization_ids, b.goods_code, a.shelves_state-->
    <!--from shop_goods_shelves as a-->
    <!--inner join shop_goods b on a.goods_code = b.goods_code-->
    <!--where a.shelves_state = 1-->
    <!--and a.is_enable = 1-->
    <!--and b.goods_id in-->
    <!--<foreach collection="goodsIds" item="id" open="(" close=")" separator=",">-->
    <!--#{id}-->
    <!--</foreach>-->
    <!--group by b.goods_code-->
    <!--</select>-->

    <select id="selectByGoodsCodeForElasticsearchBatch" resultType="com.ly.yph.api.goods.entity.ShopGoodsForEsEntity">
        SELECT
            sg.goods_name,
            sg.goods_code,
            sg.tenant_id,
            sg.supplier_code,
            sg.supplier_name,
            sp.auth_label,
            sp.supplier_type,
            sp.data_source AS supplierDataSource,
            sg.goods_id,
            sgd.goods_sku,
            sg.goods_desc,
            sg.brand_id,
            sg.brand_name,
            sg.sale_unit,
            sg.production_place,
            sg.delivery_time,
            sg.spec_goods_ware_qd,
            sg.manufacturer_material_no,
            sg.goods_keywords,
            sg.goods_boyd_url,
            sg.third_level_gcid,
            sg.goods_subtitle,
            sg.audit_state,
            sg.goods_explain,
            sg.goods_features,
            sg.heed_event,
            sg.is_special,
            sg.special_event,
            sg.stand_category_name,
            sgd.goods_moq,
            sgd.goods_click,
            sgd.sale_num,
            sgd.comment_num,
            sgd.goods_collect,
            sgd.goods_spec,
            sgd.goods_spec_array,
            sgd.goods_image,
            sgd.goods_image_more,
            IFNULL(sgp.goods_original_price, 0) AS goods_original_price,
            IFNULL(sgp.goods_original_naked_price, 0) AS goods_original_naked_price,
            IFNULL(sgp.goods_pact_price, 0) AS goods_pact_price,
            IFNULL(sgp.goods_pact_naked_price, 0) AS goods_pact_naked_price,
            sg.goods_model,
            scd.contract_number,
            sg.second_class_name,
            sg.first_class_name,
            sg.third_class_name,
            GROUP_CONCAT(DISTINCT sgc.company_org_id) as contract_company_organization_id,
            GROUP_CONCAT(DISTINCT l.auth_mode)   AS auth_mode,
            GROUP_CONCAT(DISTINCT l.goods_label) AS goods_label,
            GROUP_CONCAT(DISTINCT dgp_sub.zone_goods_type) AS zone_goods_type,
            sg.organization_id AS supplierId,
            GROUP_CONCAT(DISTINCT sg.tenant_id) AS tenantIds,
            GROUP_CONCAT(DISTINCT zc.company_org_id) AS organization_ids,
            GROUP_CONCAT(DISTINCT dgp_sub.goods_pool_id) AS pool_id,
            IFNULL(sgs.same_code,sg.goods_code) as same_code,
            GROUP_CONCAT(DISTINCT smr.mara_matnr) AS mara_matnr
        FROM shop_goods sg
                 JOIN shop_goods_detail sgd ON sg.goods_code = sgd.goods_code
                 LEFT JOIN shop_goods_price sgp ON sgp.goods_code = sg.goods_code
                 JOIN shop_supplier sp ON sp.supplier_code = sg.supplier_code
                 LEFT JOIN shop_srm_contract_detail scd ON scd.goods_code = sg.goods_code
                 LEFT JOIN dfmall_goods_pool_sub dgp_sub ON sg.goods_id = dgp_sub.goods_id
                 LEFT JOIN goods_zone zo ON zo.goods_pool_id = dgp_sub.goods_pool_id
                 LEFT JOIN goods_zone_company zc ON zc.zone_id = zo.id
                 LEFT JOIN shop_goods_sale_label l ON l.goods_id = sg.goods_id
                 LEFT JOIN shop_goods_same sgs on sgs.goods_id = sg.goods_id
                 LEFT JOIN shop_goods_contract sgc ON sgc.goods_id = sg.goods_id
                 LEFT JOIN shop_material_relation as smr ON smr.goods_code = sg.goods_code
        WHERE sg.is_enable = 1
          AND sg.goods_id IN
            <foreach collection="goodsIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
          AND dgp_sub.id IS NOT NULL
        GROUP BY sg.goods_code
    </select>

    <!--自动生成代码 2022-04-06-->
    <select id="countByIsEnable" resultType="java.lang.Integer">
        select count(*)
        from shop_goods
        where is_enable = 1
    </select>
    <select id="select" resultType="com.ly.yph.api.goods.vo.ShopGoodsDetailsVo">
        select c.class_name as class_name,
               d.class_name as class_names,
               f.class_name as standard_name,
               c.standard_class_id
        from shop_goods a
                 join yph_standard_class c
                      on a.first_level_gcid = c.standard_class_id
                 join yph_standard_class d
                      on a.second_level_gcid = d.standard_class_id
                 join yph_standard_class f
                      on a.third_level_gcid = f.standard_class_id
        where a.goods_id = #{goodsId}
          and a.is_enable = 1
    </select>
    <select id="selelctFavorites" resultType="java.lang.Integer">
        select count(1)
        from shop_favorites
        where 1 = 1
          and is_enable = '1'
        <if test="userId != null and userId != ''">
            and member_id = #{userId}
        </if>
        <if test="goodsId != null and goodsId != ''">
            and fav_id = #{goodsId}
        </if>
        and fav_type = 'goods'
    </select>
    <select id="queryGoodsPages" resultType="com.ly.yph.api.goods.vo.ShopFavoritesVo">
        SELECT a.*,
               b.goods_id,
               b.goods_code,
               b.goods_name,
               b.goods_desc,
               b.supplier_name,
               b.supplier_code,
               s.data_source supplierDataSource,
               d.goods_image,
               d.goods_sku,
               d.goods_moq,
               f.class_name
        FROM shop_favorites a
                 JOIN shop_goods b ON a.fav_id = b.goods_id
                 JOIN shop_supplier s ON s.supplier_code = b.supplier_code
                 JOIN shop_goods_detail d ON d.goods_code = b.goods_code
                 JOIN yph_standard_class f ON b.third_level_gcid = f.standard_class_id
                 LEFT JOIN system_organization so ON so.id = a.organization_id
        WHERE a.member_id = #{userId}
          AND a.is_enable = '1'
        GROUP BY a.fav_id
        ORDER BY fav_time DESC
    </select>
    <select id="findGoodsDirectory" resultType="java.lang.String">
        select su.class_name as gc_name
        FROM shop_favorites sf
                 JOIN system_users st ON sf.member_id = st.id
                 JOIN shop_goods sg ON sf.fav_id = sg.goods_id

                 JOIN yph_standard_class su ON su.standard_class_id = sg.third_level_gcid
        where sg.is_enable = '1'
          and sf.member_id = #{userId}
    </select>

    <select id="queryGoodsMasterDetailVo" resultType="com.ly.yph.api.goods.vo.GoodsMasterDetailVo">
        select sg.goods_id,
               sg.goods_name,
               sg.goods_code,
               sg.goods_sku,
               sg.approve_desc,
               sg.goods_desc,
               sg.brand_id,
               sg.brand_name,
               sg.sale_unit,
               sg.production_place,
               sg.delivery_time,
               sg.spec_goods_ware_qd,
               sg.materials_code,
               sg.manufacturer_material_no,
               sg.goods_keywords,
               sgd.goods_image,
               sgd.goods_image_more,
               sg.goods_boyd_url,
               sg.goods_mobile_boyd_url,
               sg.supplier_class,
               sg.supplier_class_name,
               sg.first_level_gcid,
               sg.first_class_name,
               sg.first_class,
               sg.second_level_gcid,
               sg.second_class_name,
               sg.second_class,
               sg.third_level_gcid,
               sg.third_class_name,
               sg.third_class,
               sg.goods_subtitle,
               sg.tax_code,
               sg.tax_rate,
               sg.supplier_name,
               sg.supplier_code,
               sgd.goods_moq,
               sgs.id              as          stock_id,
               sgs.stock_available,
               sgd.goods_spec,
               sgd.goods_spec_array,
               sgp.goods_pact_price,
               sgp.goods_original_price,
               ssc.agreement_discount,
               sg.shelves_state,
               (case sg.audit_state
                    when 0 then '待审核'
                    when 1 then '审核通过'
                    else '驳回' end) as          audit_state_name,
               sg.goods_explain,
               sg.goods_features,
               sg.heed_event,
               sg.is_special,
               sg.special_event,
               sg.create_time,
               group_concat(DISTINCT di.label) goodsLabel,
               sa.same_code,
               sg.sale_client
        from shop_goods sg
                 left join shop_goods_detail sgd on sgd.goods_code = sg.goods_code
                 left join shop_goods_price sgp on sgp.goods_code = sg.goods_code
                 left join shop_goods_stock sgs on sgs.goods_code = sg.goods_code
                 left join shop_supplier_class ssc on sg.supplier_class_id = ssc.supplier_class_id
                 LEFT JOIN shop_goods_sale_label l on l.goods_id = sg.goods_id and l.organization_id = 0
                 LEFT JOIN system_dict_data di ON di.dict_type = l.auth_mode and di.value = l.goods_label
                 LEFT JOIN shop_goods_same sa on sa.goods_code = sg.goods_code
        where sg.goods_code = #{goodsCode}
          and sg.is_enable = 1
    </select>
    <select id="queryGoodsMasterDetailVos" resultType="com.ly.yph.api.goods.vo.GoodsMasterDetailVo">
        select sg.goods_id,
               sg.goods_name,
               sg.goods_code,
               sg.goods_sku,
               sg.approve_desc,
               sg.goods_desc,
               sg.brand_id,
               sg.brand_name,
               sg.sale_unit,
               sg.production_place,
               sg.delivery_time,
               sg.spec_goods_ware_qd,
               sg.manufacturer_material_no,
               sg.goods_keywords,
               sgd.goods_image,
               sgd.goods_image_more,
               sg.goods_boyd_url,
               sg.goods_mobile_boyd_url,
               sg.supplier_class,
               sg.supplier_class_name,
               sg.first_level_gcid,
               sg.first_class_name,
               sg.second_level_gcid,
               sg.second_class_name,
               sg.third_level_gcid,
               sg.third_class_name,
               sg.goods_subtitle,
               sg.tax_code,
               sg.tax_rate,
               sg.supplier_name,
               sg.supplier_code,
               sg.supplier_type,
               sgd.goods_moq,
               sgs.stock_available,
               sgd.goods_spec,
               sgd.goods_spec_array,
               sgp.goods_pact_price,
               sgp.goods_original_price,
               ssc.agreement_discount,
               (case sg.audit_state
                    when 0 then '待审核'
                    when 1 then '审核通过'
                    else '驳回' end) as audit_state_name,
               sg.goods_explain,
               sg.goods_features,
               sg.heed_event,
               sg.is_special,
               sg.special_event,
               sg.create_time
        from shop_goods sg
                 left join shop_goods_detail sgd on sgd.goods_code = sg.goods_code
                 left join shop_goods_price sgp on sgp.goods_code = sg.goods_code
                 left join shop_goods_stock sgs on sgs.goods_code = sg.goods_code
                 left join shop_supplier_class ssc on sg.supplier_class_id = ssc.supplier_class_id
        where sg.goods_code in
        <foreach collection="goodsCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and sg.is_enable = 1
    </select>

    <select id="queryGoodsBySupplierAndClass" resultType="com.ly.yph.api.goods.entity.ShopGoods">
        select sg.goods_id, sg.goods_code
        from shop_supplier ss
        left join shop_goods sg on ss.organization_id = #{supplierOrgId}
        and ss.supplier_code = sg.supplier_code
        where sg.is_enable = '1'
        and sg.third_level_gcid in
        <foreach item="standardClassLv3Id" open="(" collection="standardClassLv3Ids" close=")" separator=",">
            ${standardClassLv3Id}
        </foreach>
    </select>

    <select id="queryGoodsUpInfoByGoodsCode" resultMap="BaseResultMap">
        select goods_code,
               goods_sku,
               supplier_code,
               supplier_class,
               third_level_gcid
        from shop_goods
        where goods_code = #{goodsCode}
          and is_enable = 1
    </select>

    <select id="queryEsGoodsInfo" resultMap="BaseResultMap">
        SELECT goods_id,
        goods_code,
        goods_sku,
        third_level_gcid,
        supplier_code,
        supplier_class
        FROM shop_goods
        where organization_id = #{organizationId}
        AND audit_state = 1
        AND third_level_gcid IN
        <foreach collection="standardClassList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        AND is_enable = 1
        And goods_id > #{currentGoodsId}
        order by goods_id
        LIMIT 0,#{pageSize}
    </select>

    <select id="queryEsGoodsInfoById" resultMap="BaseResultMap">
        SELECT t.goods_code,
        t.goods_id,
        t.goods_sku,
        t.supplier_code,
        supplier_class
        FROM shop_goods T
        WHERE t.audit_state = 1
        AND t.is_enable = 1
        AND t.goods_id IN
        <foreach collection="goodsIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        LIMIT #{index}, #{pageSize}
    </select>

    <select id="queryEsGoodsInfoByIdx" resultMap="BaseResultMap">
        SELECT t.goods_code,
               t.goods_id,
               t.goods_sku,
               t.supplier_code,
               t.tenant_id
        FROM shop_goods T
                 left join x_elasticsearch_goods_queue q on t.goods_id = q.goods_id
        WHERE q.goods_id is null
          AND t.goods_id IN
        <foreach collection="goodsIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getGoodsPoolByCode" resultType="long">
        SELECT DISTINCT p.id
        FROM shop_goods g
        LEFT JOIN shop_goods_pool_goods pg ON pg.goods_id = g.goods_id
        LEFT JOIN shop_goods_pool_class pc ON pc.standard_class_id = g.third_level_gcid
        LEFT JOIN shop_goods_pool p ON p.id = pg.goods_pool_id OR p.id = pc.goods_pool_id
        WHERE g.is_enable = '1'
        AND g.goods_code = #{goodsCode}
    </select>
    <!--转换商品使用-->
    <select id="queryGoodsByGoodsCode" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from shop_goods
        where goods_code = #{goodsCode}
    </select>
    <select id="queryGoodsByGoodsCodeIn" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from shop_goods
        where goods_code in
        <foreach collection="goodsCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateGoods">
        update shop_goods
        set goods_name = #{sEnt.goodsName},
        goods_code = #{sEnt.goodsCode},
        goods_sku = #{sEnt.goodsSku},
        goods_desc = #{sEnt.goodsDesc},
        brand_id = #{sEnt.brandId},
        brand_name = #{sEnt.brandName},
        sale_unit = #{sEnt.saleUnit},
        production_place = #{sEnt.productionPlace},
        delivery_time = #{sEnt.deliveryTime},
        spec_goods_ware_qd = #{sEnt.specGoodsWareQd},
        materials_code = #{sEnt.materialsCode},
        manufacturer_material_no = #{sEnt.manufacturerMaterialNo},
        goods_keywords = #{sEnt.goodsKeywords},
        goods_boyd_url = #{sEnt.goodsBoydUrl},
        goods_mobile_boyd_url = #{sEnt.goodsMobileBoydUrl},
        supplier_class = #{sEnt.supplierClass},
        supplier_class_id = #{sEnt.supplierClassId},
        supplier_class_name = #{sEnt.supplierClassName},
        first_level_gcid = #{sEnt.firstLevelGcid},
        second_level_gcid = #{sEnt.secondLevelGcid},
        third_level_gcid = #{sEnt.thirdLevelGcid},
        goods_subtitle = #{sEnt.goodsSubtitle},
        tax_code = #{sEnt.taxCode},
        tax_rate = #{sEnt.taxRate},
        supplier_code = #{sEnt.supplierCode},
        supplier_name = #{sEnt.supplierName},
        is_enable = #{sEnt.isEnable},
        creator = #{sEnt.creator},
        create_time = #{sEnt.createTime},
        modifier = #{sEnt.modifier},
        update_time = #{sEnt.updateTime},
        index_status = #{sEnt.indexStatus},
        backup_good_id = #{sEnt.backupGoodId},
        audit_state = #{sEnt.auditState},
        goods_explain = #{sEnt.goodsExplain},
        goods_features = #{sEnt.goodsFeatures},
        heed_event = #{sEnt.heedEvent},
        is_special = #{sEnt.isSpecial},
        special_event = #{sEnt.specialEvent},
        price = #{sEnt.price},
        nake_price = #{sEnt.nakePrice},
        stand_category_name = #{sEnt.standCategoryName},
        organization_id = #{sEnt.organizationId},
        supplier_type = #{sEnt.supplierType},
        first_class = #{sEnt.firstClass},
        first_class_name = #{sEnt.firstClassName},
        second_class = #{sEnt.secondClass},
        second_class_name = #{sEnt.secondClassName},
        third_class = #{sEnt.thirdClass},
        third_class_name = #{sEnt.thirdClassName}
        where goods_id = #{sEnt.goodsId}
    </update>

    <!--auto generated by MybatisCodeHelper on 2022-10-20-->
    <select id="selectGoodsIdByIsEnable" resultType="java.lang.Long">
        select `goods_id`
        from shop_goods
        where `is_enable` = 1
        limit #{begin,jdbcType=BIGINT}, #{count,jdbcType=BIGINT}
    </select>
    <select id="queryGoodsClassByActivityId" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
        SELECT distinct g.second_level_gcid,
        g.third_level_gcid
        FROM system_activity_goods_pool_relation gpr
        LEFT JOIN dfmall_goods_pool_sub dgp_sub ON gpr.goods_pool_id = dgp_sub.goods_pool_id
        LEFT JOIN shop_goods g ON g.is_enable = 1 and g.goods_id = dgp_sub.goods_id
        LEFT JOIN dfmall_goods_pool_shelves_down dgp_down ON g.goods_id = dgp_down.goods_id and
        gpr.goods_pool_id = dgp_down.goods_pool_id
        WHERE gpr.is_enable = 1
        and g.second_level_gcid is not null
        AND dgp_sub.goods_id IS NOT NULL
        AND dgp_down.goods_id IS NULL
        AND gpr.activity_id = #{activityId}
        UNION
        SELECT distinct c2.standard_class_id as second_level_gcid,
        c3.standard_class_id as third_level_gcid
        FROM system_activity_goods_pool_relation gpr
        LEFT JOIN dfmall_goods_pool_sub dgp_sub ON gpr.goods_pool_id = dgp_sub.goods_pool_id
        LEFT JOIN yph_standard_class c3 ON c3.standard_class_id = dgp_sub.standard_class_id
        LEFT JOIN yph_standard_class c2 ON c2.class_code = c3.parent_class_code
        WHERE gpr.is_enable = 1
        AND dgp_sub.goods_id IS NOT NULL
        AND gpr.activity_id = #{activityId}
    </select>

    <select id="queryGoodsBySku" resultType="com.ly.yph.api.order.entity.ShopCart">
        select sg.goods_id,
        sg.goods_code,
        sg.goods_sku,
        sg.goods_name,
        sg.goods_desc,
        sg.sale_unit,
        sg.supplier_code,
        sg.supplier_name,
        sgd.goods_moq as goods_num,
        sg.tax_rate,
        sgd.goods_image,
        sg.goods_model,
        sscd.contract_number,
        ysc.class_code,
        sdd.id as goods_type_id
        from shop_goods sg
        left join shop_goods_detail sgd on sgd.goods_code = sg.goods_code
        left join yph_standard_class ysc on ysc.standard_class_id = sg.third_level_gcid
        left join shop_srm_contract_detail sscd on sscd.goods_code = sg.goods_code
        left join system_dict_data sdd on sdd.dict_type = 'A002' and sdd.value = ysc.type_code
        where sg.goods_sku in
        <foreach collection="goodsSkus" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and sg.supplier_code = #{supplierCode}
    </select>

    <select id="queryContractGoods" resultType="com.ly.yph.api.goods.vo.ContractGoodsVO">
        SELECT t2.goods_id,
               t2.goods_code,
               t2.goods_sku,
               t2.supplier_name,
               t2.supplier_code,
               t2.goods_name,
               t2.goods_desc,
               t2.delivery_time,
               dgp_sub.create_time,
               t4.organization_name,
               t4.organization_short_name,
               t5.goods_moq,
               t5.goods_image,
               t6.contract_number
        FROM system_organization_purchase_contract t4
                 JOIN shop_goods t2 ON t2.goods_code = t3.goods_code
                 JOIN shop_goods_source t1 ON t1.goods_id = t2.goods_id
                 JOIN shop_goods_detail t5 ON t2.goods_code = t5.goods_code
                 JOIN shop_srm_contract_detail t6 ON t2.goods_code = t6.goods_code
                 LEFT JOIN system_organization so ON t4.organization_id = so.id
                 LEFT JOIN dfmall_goods_pool_sub dgp_sub
                           ON t2.goods_id = dgp_sub.goods_id and so.code = dgp_sub.company_code
                 LEFT JOIN dfmall_goods_pool_sub dgp_sub1
                           ON dgp_sub1.goods_id = '' and t2.supplier_code = dgp_sub1.supplier_code
                               AND t2.third_level_gcid = dgp_sub1.standard_class_id and so.code = dgp_sub1.company_code
                 LEFT JOIN dfmall_goods_pool_shelves_down dgp_down ON t2.goods_id = dgp_down.goods_id and
                                                                      (dgp_sub.goods_pool_id = dgp_down.goods_pool_id or
                                                                       dgp_sub1.goods_pool_id = dgp_down.goods_pool_id)

        WHERE (dgp_sub.goods_id IS NOT NULL or dgp_sub1.goods_id IS NOT NULL)
          AND dgp_down.goods_id IS NULL
          AND t1.source_type = #{sourceType}
        <if test="queryDto.contractNumber != null">
            AND #{queryDto.contractNumber} LIKE CONCAT('%', t6.contract_number, '%')
        </if>
        <if test="queryDto.keyword != null">
            AND (
                #{queryDto.keyword} LIKE CONCAT('%', t2.goods_code, '%')
                    OR #{queryDto.keyword} LIKE CONCAT('%', t2.goods_sku, '%')
                    OR t2.goods_name LIKE CONCAT('%', #{queryDto.keyword}, '%')
                )
        </if>
        <if test="queryDto.supplierName != null">
            AND (
                #{queryDto.supplierName} LIKE CONCAT('%', t2.supplier_code, '%')
                    OR t2.supplier_name LIKE CONCAT('%', #{queryDto.supplierName}, '%')
                )
        </if>
        <if test="queryDto.startTime != null">
            AND dgp_sub.create_time &gt;= #{queryDto.startTime}
        </if>
        <if test="queryDto.endTime != null">
            AND dgp_sub.create_time &lt;= #{queryDto.endTime}
        </if>
        ORDER BY dgp_sub.create_time DESC
    </select>

    <select id="selectGoodsIdByGoodsCode" resultType="java.lang.Long">
        select goods_id
        from shop_goods
        where goods_code = #{goodsCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByGoodsSkuAndOrganizationCode" resultType="com.ly.yph.api.goods.entity.ShopGoods">
        select
        <include refid="Base_Column_List"/>
        from shop_goods
        where goods_sku = #{goodsSku,jdbcType=VARCHAR}
        and organization_id = #{organizationCode}
    </select>

    <update id="updateShopGoods">
    </update>

    <select id="selectGoodsIdByGoodsCodeIn" resultType="java.lang.Long">
        select goods_id
        from shop_goods
        where goods_code in
        <foreach item="item" index="index" collection="goodsCodeCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and is_enable = 1
    </select>

    <select id="queryGoodsAndDetailByGoodsIds"
            resultType="com.ly.yph.api.openapi.v1.vo.AdvGoodsDetailVo">
        SELECT sg.goods_id,
               sg.tax_rate,
               sg.manufacturer_material_no,
               sgd.goods_spec
        FROM shop_goods sg
                 LEFT JOIN shop_goods_detail sgd ON sg.goods_code = sgd.goods_code
        WHERE
        goods_id in
        <foreach close=")" collection="goodsIdList" item="item" open="(" separator=",">
            #{item}
        </foreach>
        AND sg.is_enable = 1
        AND sgd.is_enable = 1
    </select>

    <!--create on 2023-07-08-->
    <select id="selectGoodsIdAndThirdLevelGcidAndOrganizationId" resultMap="BaseResultMap">
        select goods_id,
               goods_code,
               goods_sku,
               third_level_gcid,
               supplier_code,
               supplier_class
        from shop_goods
        where goods_id > #{curr_goods_id}
          and audit_state = 1
        order by goods_id
        limit 0,#{count}
    </select>

    <select id="queryHondaGoods" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
         select distinct sg.*
        from shop_goods sg
        LEFT JOIN dfmall_goods_pool_sub dgp_sub ON sg.goods_id = dgp_sub.goods_id
        left join goods_zone_company zc on zc.goods_pool_id = dgp_sub.goods_pool_id
        left join system_organization so on so.id = zc.company_org_id
        where so.code=#{companyCode}
        and sg.supplier_code = #{supplierCode}
        and sg.is_enable = 1
        and sg.tenant_id = 1
        and sg.goods_sku in
        <foreach open="(" collection="skus" item="sku" close=")" separator=",">
            #{sku}
        </foreach>
        and dgp_sub.goods_pool_id is not null
    </select>

    <select id="queryHondaGoodsInfo" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
        select distinct sg.*,
        case
        when sg.is_enable = 0 then 0
        when sub.goods_id IS NULL then 0
        else 1 END AS shelvesState
        from shop_goods sg
        LEFT JOIN dfmall_goods_pool_sub sub ON sg.goods_id = sub.goods_id and sub.tenant_id = 1
        left join goods_zone_company zc on zc.goods_pool_id = sub.goods_pool_id and zc.tenant_id=1
        left join system_organization so on so.id = zc.company_org_id and  so.code=#{companyCode} and so.tenant_id
        where sg.supplier_code = #{supplierCode}
        and sg.goods_sku in
        <foreach open="(" collection="skus" item="sku" close=")" separator=",">
            #{sku}
        </foreach>
        and sg.tenant_id = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-08-01-->
    <update id="updateAuditStateByGoodsId">
        update shop_goods
        set audit_state=#{updatedAuditState,jdbcType=INTEGER},
            approve_desc = #{approveDesc,jdbcType=VARCHAR}
        where goods_id = #{goodsId,jdbcType=BIGINT}
    </update>

    <select id="queryGoodsIdByClassSup" resultType="java.lang.Long">
        SELECT goods_id
        FROM shop_goods
        WHERE third_level_gcid = #{classId}
          AND supplier_code = #{supplierCode}
          AND is_enable = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-10-20-->
    <select id="getBySkuAndSupplier" resultMap="shopGoodsMap">
        select
        <include refid="Base_Column_List"/>
        from shop_goods
        where goods_sku = #{goodsSku,jdbcType=VARCHAR}
          and supplier_code = #{supplierCode,jdbcType=VARCHAR}
          and is_enable = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-10-24-->
    <select id="selectGoodsId" resultType="java.lang.Long">
        select goods_id
        from shop_goods
        where is_enable = 1
          and goods_id &gt; #{goodsId}
          and not exists(select dfmall_goods_pool_shelves_down.goods_id
                         from dfmall_goods_pool_shelves_down
                         where dfmall_goods_pool_shelves_down.goods_id = shop_goods.goods_id)
        order by goods_id
        limit #{size}
    </select>
    <select id="selectBySkuAndSupplier" resultType="com.ly.yph.api.goods.entity.ShopGoods">
        select
        <include refid="Base_Column_List"/>
        from shop_goods
        where goods_sku = #{goodsSku}
        and supplier_code = #{supplierCode}
        and is_enable = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <select id="selectByGoodsIdIn" resultMap="shopGoodsMap">
        select goods_id,
               goods_sku,
               goods_code,
               tenant_id,
               supplier_code
        from shop_goods
        where goods_id in
        <foreach item="item" index="index" collection="goodsIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and is_enable = 1
    </select>

    <select id="selectVoBySkuAndSupplier" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
        SELECT sg.goods_desc,
        sg.supplier_code,
        sg.supplier_name,
        sg.goods_code,
        sg.goods_sku,
        sg.brand_name,
        sgd.goods_moq,
        sgd.goods_spec,
        sgd.goods_image,
        sg.delivery_time,
        sg.audit_state,
        sg.create_time,
        sg.third_level_gcid,
        ysc.type_code
        FROM shop_goods sg
        LEFT JOIN shop_goods_detail sgd ON sg.goods_code = sgd.goods_code
        left join yph_standard_class ysc on ysc.standard_class_id=sg.third_level_gcid
        WHERE
        sg.goods_sku IN
        <foreach item="item" index="index" collection="sku.split(',')" open="("
                 separator="," close=")">
            '${item}'
        </foreach>
        AND sg.supplier_code = #{supplier}
    </select>

    <select id="queryShopGoodsSupplierByGoodCodes" resultType="com.ly.yph.api.goods.vo.ShopGoodsSupplierVo">
        SELECT sg.goods_id,
               sg.goods_name,
               sg.goods_code,
               ss.supplier_code,
               ss.supplier_type,
               ss.data_source,
               ss.supplier_short_name,
               sc.settle_ment_method,
               sc.settle_ment_cycle,
               sc.settle_ment_date,
               ss.data_source
        FROM shop_goods sg
                 LEFT JOIN shop_supplier ss ON sg.supplier_code = ss.supplier_code
                 LEFT JOIN shop_srm_contract_detail scd ON scd.goods_code = sg.goods_code
                 LEFT JOIN shop_srm_contract sc ON sc.contract_number = scd.contract_number
        WHERE sg.goods_code in
        <foreach item="item" index="index" collection="goodCodes"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateGoodsBatch">
        <foreach collection="sEnts" item="sEnt">
            update shop_goods
            set goods_name               = #{sEnt.goodsName},
                goods_code               = #{sEnt.goodsCode},
                goods_sku                = #{sEnt.goodsSku},
                goods_desc               = #{sEnt.goodsDesc},
                brand_id                 = #{sEnt.brandId},
                brand_name               = #{sEnt.brandName},
                sale_unit                = #{sEnt.saleUnit},
                production_place         = #{sEnt.productionPlace},
                delivery_time            = #{sEnt.deliveryTime},
                spec_goods_ware_qd       = #{sEnt.specGoodsWareQd},
                materials_code = #{sEnt.materialsCode},
                manufacturer_material_no = #{sEnt.manufacturerMaterialNo},
                goods_keywords           = #{sEnt.goodsKeywords},
                goods_boyd_url           = #{sEnt.goodsBoydUrl},
                goods_mobile_boyd_url    = #{sEnt.goodsMobileBoydUrl},
                supplier_class           = #{sEnt.supplierClass},
                supplier_class_id        = #{sEnt.supplierClassId},
                supplier_class_name      = #{sEnt.supplierClassName},
                first_level_gcid         = #{sEnt.firstLevelGcid},
                second_level_gcid        = #{sEnt.secondLevelGcid},
                third_level_gcid         = #{sEnt.thirdLevelGcid},
                goods_subtitle           = #{sEnt.goodsSubtitle},
                tax_code                 = #{sEnt.taxCode},
                tax_rate                 = #{sEnt.taxRate},
                supplier_code            = #{sEnt.supplierCode},
                supplier_name            = #{sEnt.supplierName},
                is_enable                = #{sEnt.isEnable},
                creator                  = #{sEnt.creator},
                create_time              = #{sEnt.createTime},
                modifier                 = #{sEnt.modifier},
                update_time              = now(),
                index_status             = #{sEnt.indexStatus},
                backup_good_id           = #{sEnt.backupGoodId},
                audit_state              = #{sEnt.auditState},
                goods_explain            = #{sEnt.goodsExplain},
                goods_features           = #{sEnt.goodsFeatures},
                heed_event               = #{sEnt.heedEvent},
                is_special               = #{sEnt.isSpecial},
                special_event            = #{sEnt.specialEvent},
                price                    = #{sEnt.price},
                nake_price               = #{sEnt.nakePrice},
                stand_category_name      = #{sEnt.standCategoryName},
                organization_id          = #{sEnt.organizationId},
                supplier_type            = #{sEnt.supplierType},
                first_class              = #{sEnt.firstClass},
                first_class_name         = #{sEnt.firstClassName},
                second_class             = #{sEnt.secondClass},
                second_class_name        = #{sEnt.secondClassName},
                third_class              = #{sEnt.thirdClass},
                tenant_id                = #{sEnt.tenantId},
                third_class_name         = #{sEnt.thirdClassName}
            where goods_id = #{sEnt.goodsId};
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-01-10-->
    <delete id="deleteByGoodsIdIn">
        delete
        from shop_goods
        where goods_id in
        <foreach item="item" index="index" collection="goodsIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <select id="queryAllByGoodsId" resultType="com.ly.yph.api.goods.entity.ShopGoods">
        SELECT tt.goods_id,
            tt.goods_code,
            tt.goods_sku,
            tt.supplier_code,
            tt.tenant_id
        FROM shop_goods tt
        JOIN (SELECT goods_id
                FROM shop_goods
                WHERE supplier_code IN
                <foreach collection="supplierList" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
                AND is_enable = 1
                LIMIT #{count},#{page}) t
        WHERE tt.goods_id = t.goods_id
    </select>

    <select id="getLiteFlowProcessGoodsInfo" resultType="com.ly.yph.api.flowable.context.GoodsInfo">
        SELECT goods_id,
               goods_code,
               goods_sku,
               supplier_code,
               tenant_id,
               auth_mode,
               goods_label
        FROM shop_goods
        WHERE goods_code = #{goodsCode}
          AND is_enable = 1
    </select>

    <update id="updateShelvesAndSaleBatch">
        update shop_goods set
        <foreach collection="goodsList" item="goods" open="shelves_state = case goods_id" close="end">
            when #{goods.goodsId} then #{goods.shelvesState}
        </foreach>
        <foreach collection="goodsList" item="goods" open="auth_mode = case goods_id" close="end">
            when #{goods.goodsId} then #{goods.authMode}
        </foreach>
        <foreach collection="goodsList" item="goods" open="goods_label = case goods_id" close="end">
            when #{goods.goodsId} then #{goods.goodsLabel}
        </foreach>
        where goods_id IN
        <foreach collection="goodsList" item="goods" open="(" close=")" separator=",">
            when #{goods.goodsId}
        </foreach>
    </update>

    <select id="queryHondaGoodsMaterials" resultType="com.ly.yph.api.goods.vo.ShopGoodsVo">
        SELECT sg.goods_desc,
        sg.supplier_code,
        sg.supplier_name,
        sg.goods_code,
        sg.goods_sku,
        sg.brand_name,
        sgd.goods_moq,
        sgd.goods_spec,
        sg.delivery_time,
        sg.audit_state,
        sg.create_time,
        sg.third_level_gcid,
        ysc.type_code,
        sg.goods_name,
        sg.manufacturer_material_no
        FROM
        shop_goods sg
        LEFT JOIN shop_goods_detail sgd ON sg.goods_code = sgd.goods_code
        left join yph_standard_class ysc on ysc.standard_class_id=sg.third_level_gcid
        left join shop_material_relation mr on mr.goods_code=sg.goods_code and mr.company_code='HONDA' and mr.is_del=0
        WHERE
        sg.goods_code IN
        <foreach item="item" index="index" collection="goodsCodeList" open="("  separator="," close=")">
            '${item}'
        </foreach>
        and  mr.mara_id is null
    </select>



    <select id="selectAllForBigModelByCategory"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
        goods.goods_sku,
        goods.supplier_code,
        goods.brand_name,
        goods.sale_unit,
        goods.third_class_name AS category_name,
        goods.goods_code,
        CONCAT(
        '商品描述：',
        COALESCE(goods.goods_desc, ''), '，',
        COALESCE(goods.third_class_name, ''), '，',
        CASE
        WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
        WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
        ELSE ''
        END,
        '，商品品牌：', COALESCE(goods.brand_name, ''),
        '，商品销售单位：', COALESCE(goods.sale_unit, ''),
        '，商品价格：', COALESCE(price.goods_pact_price, '')
        )                      AS goods_desc,
        price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.goods_id > #{lastMaxId}
          and goods.tenant_id = 1
          and goods.is_enable = 1
          and third_class_name in
        <foreach collection="category" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order by goods.goods_id asc
        limit #{count};
    </select>

    <select id="selectAllForBigModelByBrand"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
        goods.goods_sku,
        goods.supplier_code,
        goods.brand_name,
        goods.sale_unit,
        goods.third_class_name AS category_name,
        goods.goods_code,
        CONCAT(
        '商品描述：',
        COALESCE(goods.goods_desc, ''), '，',
        COALESCE(goods.third_class_name, ''), '，',
        CASE
        WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
        WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
        ELSE ''
        END,
        '，商品品牌：', COALESCE(goods.brand_name, ''),
        '，商品销售单位：', COALESCE(goods.sale_unit, ''),
        '，商品价格：', COALESCE(price.goods_pact_price, '')
        )                      AS goods_desc,
        price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.goods_id > #{lastMaxId}
          and goods.tenant_id = 1
          and goods.is_enable = 1
          and goods.brand_name in
        <foreach collection="brand" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order by goods.goods_id asc
        limit #{count};
    </select>

    <select id="selectAllForBigModelByGoodsCode"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
        goods.goods_sku,
        goods.supplier_code,
        goods.brand_name,
        goods.sale_unit,
        goods.third_class_name AS category_name,
        goods.goods_code,
        CONCAT(
        '商品描述：',
        COALESCE(goods.goods_desc, ''), '，',
        COALESCE(goods.third_class_name, ''), '，',
        CASE
        WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
        WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
        ELSE ''
        END,
        '，商品品牌：', COALESCE(goods.brand_name, ''),
        '，商品销售单位：', COALESCE(goods.sale_unit, ''),
        '，商品价格：', COALESCE(price.goods_pact_price, '')
        )                      AS goods_desc,
        price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.goods_id > #{lastMaxId}
          and goods.tenant_id = 1
          and goods.is_enable = 1
          and goods.goods_code in
        <foreach collection="goodsCode" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order by goods.goods_id asc
        limit #{count};
    </select>

    <select id="selectForBigModelByGoodsCode"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
        goods.goods_sku,
        goods.supplier_code,
        goods.brand_name,
        goods.sale_unit,
        goods.third_class_name AS category_name,
        goods.goods_code,
        CONCAT(
        '商品描述：',
        COALESCE(goods.goods_desc, ''), '，',
        COALESCE(goods.third_class_name, ''), '，',
        CASE
        WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
        WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
        ELSE ''
        END,
        '，商品品牌：', COALESCE(goods.brand_name, ''),
        '，商品销售单位：', COALESCE(goods.sale_unit, ''),
        '，商品价格：', COALESCE(price.goods_pact_price, '')
        )                      AS goods_desc,
        price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.tenant_id = 1
          and goods.is_enable = 1
          and goods.goods_code = #{goodsCode};
    </select>
    <select id="selectForBigModelByGoodsCodeList"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
        goods.goods_sku,
        goods.supplier_code,
        goods.brand_name,
        goods.sale_unit,
        goods.third_class_name AS category_name,
        goods.goods_code,
        CONCAT(
        '商品描述：',
        COALESCE(goods.goods_desc, ''), '，',
        COALESCE(goods.third_class_name, ''), '，',
        CASE
        WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
        WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
        ELSE ''
        END,
        '，商品品牌：', COALESCE(goods.brand_name, ''),
        '，商品销售单位：', COALESCE(goods.sale_unit, ''),
        '，商品价格：', COALESCE(price.goods_pact_price, '')
        )                      AS goods_desc,
        price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.tenant_id = 1
          and goods.is_enable = 1
          and goods.goods_code in
        <foreach collection="goodsCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAllForBigModelBySupplier"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
               goods.goods_sku,
               goods.supplier_code,
               goods.brand_name,
               goods.sale_unit,
               goods.third_class_name AS category_name,
               goods.goods_code,
               CONCAT(
                       '商品描述：',
                       COALESCE(goods.goods_desc, ''), '，',
                       COALESCE(goods.third_class_name, ''), '，',
                       CASE
                           WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
                           WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
                           ELSE ''
                           END,
                       '，商品品牌：', COALESCE(goods.brand_name, ''),
                       '，商品销售单位：', COALESCE(goods.sale_unit, ''),
                       '，商品价格：', COALESCE(price.goods_pact_price, '')
               )                      AS goods_desc,
               price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.goods_id > #{lastMaxId}
          and goods.tenant_id = 1
          and goods.is_enable = 1
          and goods.supplier_code in
        <foreach collection="supplier" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order by goods.goods_id asc
        limit #{count};
    </select>

    <select id="selectAllForBigModel"
            resultType="com.ly.yph.api.samegoods.controller.admin.samegoods.vo.GoodsTransResultVO">
        SELECT goods.goods_id,
        goods.goods_sku,
        goods.supplier_code,
        goods.brand_name,
        goods.sale_unit,
        goods.third_class_name AS category_name,
        goods.goods_code,
        CONCAT(
        '商品描述：',
        COALESCE(goods.goods_desc, ''), '，',
        COALESCE(goods.third_class_name, ''), '，',
        CASE
        WHEN goods.manufacturer_material_no IS NOT NULL THEN goods.manufacturer_material_no
        WHEN goods.materials_code IS NOT NULL THEN goods.materials_code
        ELSE ''
        END,
        '，商品品牌：', COALESCE(goods.brand_name, ''),
        '，商品销售单位：', COALESCE(goods.sale_unit, ''),
        '，商品价格：', COALESCE(price.goods_pact_price, '')
        )                      AS goods_desc,
        price.goods_pact_price AS price
        FROM shop_goods AS goods
                 inner join shop_goods_price as price on goods.goods_code = price.goods_code
        where goods.goods_id > #{lastMaxId}
          and goods.tenant_id = 1
          and goods.is_enable = 1
        order by goods.goods_id asc
        limit #{count};
    </select>

    <select id="getDmsShopGoodsInfoVo" resultType="com.ly.yph.api.openapi.v1.vo.dms.DmsShopGoodsInfoVo">
        select sg.goods_code,
        sg.goods_sku,
        sg.goods_desc,
        sgd.goods_moq
        from shop_goods sg
        left join shop_goods_detail sgd on sg.goods_code = sgd.goods_code
        where sg.is_enable = 1
        and sg.goods_code in
        <foreach collection="goodsCodes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryActByGoodsId" resultType="com.ly.yph.api.goods.vo.ShopGoodsActInfoVo">
        SELECT
            SUBSTRING_INDEX( GROUP_CONCAT( id ORDER BY create_time DESC ), ',', 1 ) AS id,
            companyCode,
            MAX( create_time ) AS create_time,
            business_key,
            instance_state
        FROM
            ( SELECT *, SUBSTRING_INDEX( business_key, '--', 2 ) AS companyCode FROM act_cu_instance_info WHERE business_key LIKE concat( #{busKey}, '%' ) and is_enable = 1) AS a
        GROUP BY
            companyCode;
    </select>

    <select id="selectForCompany" resultType="com.ly.yph.api.supplier.controller.vo.CompanyGoodsQueryVO">
        select shop_goods.goods_code,
            shop_goods.goods_id,
            shop_goods.goods_name,
            shop_goods.goods_sku,
            concat(shop_goods.first_class_name,'/',shop_goods.second_class_name,'/',shop_goods.third_class_name) as class_name,
            shop_goods.brand_name,
            shop_goods.goods_from,
            shop_goods.materials_code as model,
            shop_goods.supplier_name,
            yph_goods_price_strategy.goods_pact_naked_price as price,
            shop_goods.tax_rate as taxRate
        from shop_goods
        inner join yph_goods_price_strategy on shop_goods.goods_id = yph_goods_price_strategy.relation_id
        inner join shop_supplier on shop_supplier.supplier_code = shop_goods.supplier_code
        inner join shop_goods_contract on shop_goods.goods_id = shop_goods_contract.goods_id
        where shop_supplier.data_source in('dfmall','DFNISSAN01','VOYAH')
        and shop_goods_contract.company_org_id = #{companyId}
        <if test="query.goodsCode != null and query.goodsCode != ''">
            and shop_goods.goods_code = #{query.goodsCode}
        </if>
        <if test="query.goodsSap != null and query.goodsSap != ''">
            and shop_goods.goods_sku =#{query.goodsSap}
        </if>
        <if test="query.supplierName != null and query.supplierName != ''">
            and shop_goods.supplier_name like concat('%',#{query.supplierName},'%')
        </if>
        <if test="query.goodsName != null and query.goodsName != ''">
            and shop_goods.goods_name like concat('%',#{query.goodsName},'%')
        </if>
        and shop_supplier.supplier_type = 1
        and shop_goods.is_enable = 1
    </select>

    <select id="queryAllRandom" resultMap="BaseResultMap">
        select goods_sku,goods_desc as goods_name,brand_name,supplier_class_name,price
        from shop_goods
        where first_class_name in ('行政办公')
        ORDER BY RAND()
        LIMIT #{count};
    </select>

    <select id="selectCountByGoodsId" resultType="java.lang.Integer">
        select count(*)
        from shop_purchase_sub_order_detail
        where goods_id = #{goodsId}
    </select>

    <select id="selectByCompanyFilter" resultType="com.ly.yph.api.goods.controller.vo.ShareCompanyListVO">
        select so.id                          as organization_id,
               so.name                        as organization_name,
               count(distinct ss.supplier_id) as supplier_count,
               count(distinct dgps.goods_id)  as goods_count
        from shop_goods_contract sgc
                 left join supplier_contract_real scr on scr.contract_id = sgc.contract_id and scr.supplier_id = sgc.supplier_id
                 left join system_organization so on so.id = sgc.company_org_id
                 left join shop_supplier ss on ss.supplier_id = sgc.supplier_id
                 left join dfmall_goods_pool_sub dgps on dgps.goods_id = sgc.goods_id
        where sgc.org_type = 2
          and sgc.audit_state = 1
          and scr.is_start = 1
          and now() between scr.contract_start_date and scr.contract_end_date
        group by so.id
    </select>

    <select id="abiSelectUpNewData" resultType="com.ly.yph.api.reportapi.controller.vo.UpNewRespVO">
        SELECT id                  as up_no,
               goods_sku           as goods_sku,
               create_time         as create_time,
               creator             as creator,
               supplier_code       as supplier_code,
               supplier_name       as supplier_name,
               CASE goods_up_state
                   WHEN 0 THEN '待供应商处理'
                   WHEN 2 THEN '供应商不可售卖'
                   WHEN 8 THEN '供应商已处理,商品校验失败'
                   WHEN 9 THEN '商品校验成功,上架审核中'
                   WHEN 6 THEN '平台池已上架,企业池未上架'
                   WHEN 7 THEN '企业池已上架'
                   WHEN 10 THEN '上新已终止'
                   ELSE '未知' END as status
        from shop_goods_up_new
        where company_code ='HONDA'
        order by create_time desc;
    </select>

    <select id="abiSelectUpdateDetail" resultType="com.ly.yph.api.reportapi.controller.vo.UpdateDetailRespVO">
        select distinct sscp.seek_price_number,
                        case sscp.seek_state
                            when 0 then '已取消'
                            when 1 then '询价中'
                            when 2 then '报价截止'
                            when 3 then '询价完成'
                            else '' end                                          as seek_status,
                        sscp.create_time,
                        su.nickname,
                        sss.supplier_name,
                        if(sss.quoted_state = -1, '放弃报价',
                           if(sum(ssqp.bidden_success) > 0, '已中标', '已报价')) as process_status
        from shop_seek_compare_price sscp
                 left join shop_seek_supplier sss on sss.seek_price_id = sscp.id
                 left join shop_seek_quoted_price ssqp on ssqp.seek_price_id = sscp.id and ssqp.price_state = 1
            and ssqp.supplier_code = sss.supplier_code
                 left join system_users su on su.username = sscp.creator and su.tenant_id = sscp.tenant_id
        where sscp.is_last = 1
          and sscp.company_code = 'HONDA'
        group by sscp.seek_price_number, sss.supplier_code
        ORDER BY seek_price_number;
    </select>

    <select id="queryGoodsDetailByCodes" resultType="com.ly.yph.api.goods.vo.GoodsMasterDetailVo">
        select sg.goods_id,
        sg.goods_name,
        sg.goods_code,
        sg.goods_sku,
        sg.approve_desc,
        sg.goods_desc,
        sg.brand_id,
        sg.brand_name,
        sg.sale_unit,
        sg.production_place,
        sg.delivery_time,
        sg.spec_goods_ware_qd,
        sg.manufacturer_material_no,
        sg.goods_keywords,
        sgd.goods_image,
        sgd.goods_image_more,
        sg.goods_boyd_url,
        sg.goods_mobile_boyd_url,
        sg.supplier_class,
        sg.supplier_class_name,
        sg.first_level_gcid,
        sg.first_class_name,
        sg.second_level_gcid,
        sg.second_class_name,
        sg.third_level_gcid,
        sg.third_class_name,
        sg.goods_subtitle,
        sg.tax_code,
        sg.tax_rate,
        sg.supplier_name,
        sg.supplier_code,
        sg.supplier_type,
        sgd.goods_moq,
        sgs.stock_available,
        sgd.goods_spec,
        sgd.goods_spec_array,
        sgp.goods_pact_price,
        sgp.goods_original_price,
        ssc.agreement_discount,
        (case sg.audit_state
        when 0 then '待审核'
        when 1 then '审核通过'
        else '驳回' end) as audit_state_name,
        sg.goods_explain,
        sg.goods_features,
        sg.heed_event,
        sg.is_special,
        sg.special_event,
        sg.create_time
        from shop_goods sg
        left join shop_goods_detail sgd on sgd.goods_code = sg.goods_code
        left join shop_goods_price sgp on sgp.goods_code = sg.goods_code
        left join shop_goods_stock sgs on sgs.goods_code = sg.goods_code
        left join shop_supplier_class ssc on sg.supplier_class_id = ssc.supplier_class_id
        left join dfmall_goods_pool_sub dgps on dgps.goods_id=sg.goods_id
        where sg.goods_code in
        <foreach collection="goodsCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and sg.shelves_state = 1
        and sg.is_enable = 1
        and dgps.goods_pool_id=#{goodsPoolId}
    </select>

    <select id="getDataSourceByGoodsCode" resultType="java.lang.String">
        select ss.data_source
        from shop_goods sg
                 left join shop_supplier ss on ss.supplier_code = sg.supplier_code and ss.tenant_id = sg.tenant_id
        where sg.goods_code = #{goodsCode}
          and ss.is_enable = 1
    </select>

    <select id="selectSupplierByCompany" resultType="com.ly.yph.api.goods.controller.vo.UnionSupplierRespVO">
        select distinct ss.supplier_id,
                        ss.supplier_code,
                        ss.supplier_short_name,
                        ss.supplier_full_name
        from dfmall_goods_pool_sub dgps
                 left join shop_goods_contract sgc on sgc.goods_id = dgps.goods_id
                 left join supplier_contract_real scr on scr.contract_id = sgc.contract_id
                 left join shop_supplier ss on ss.supplier_id = scr.supplier_id
        where dgps.goods_pool_id = #{goodsPoolId}
          and sgc.company_org_id = #{companyId}
        union
        select distinct ss.supplier_id,
                        ss.supplier_code,
                        ss.supplier_short_name,
                        ss.supplier_full_name
        from dfmall_goods_pool_sub dgps
                 left join shop_goods sg on sg.goods_id = dgps.goods_id
                 left join shop_srm_contract_detail sscd on sscd.goods_code = sg.goods_code
                 left join shop_srm_contract ssc on ssc.id = sscd.contract_rel_id
                 left join shop_supplier ss on ss.supplier_code = sscd.supplier_code and ss.tenant_id = sscd.tenant_id
                 left join system_organization so on so.org_range_mark = 'DFS' and so.code != #{customLy}
        where dgps.goods_pool_id = #{goodsPoolId}
          and dgps.is_enable = 1
          and sg.is_enable = 1
          and sscd.is_enable = 1
          and now() between ssc.start_contract_validity and ssc.end_contract_validity
          and so.id = #{companyId}
        union
        select distinct ss.supplier_id,
                        ss.supplier_code,
                        ss.supplier_short_name,
                        ss.supplier_full_name
        from dfmall_goods_pool_sub dgps
                 left join shop_goods sg on sg.goods_id = dgps.goods_id
                 left join shop_srm_contract_detail_ly sscdl on sscdl.goods_code = sg.goods_code
                 left join shop_srm_contract_ly sscl on sscl.id = sscdl.contract_rel_id
                 left join shop_supplier ss on ss.supplier_code = sscdl.supplier_code and ss.tenant_id = sscdl.tenant_id
                 left join system_organization so on so.code = #{customLy}
        where dgps.goods_pool_id = #{goodsPoolId}
          and dgps.is_enable = 1
          and sg.is_enable = 1
          and sscdl.is_enable = 1
          and now() between sscl.start_contract_validity and sscl.end_contract_validity
          and so.id = #{companyId}
    </select>
</mapper>