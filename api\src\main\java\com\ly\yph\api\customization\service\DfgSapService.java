package com.ly.yph.api.customization.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.yph.api.customization.common.*;
import com.ly.yph.api.customization.config.DfgSapConfig;
import com.ly.yph.api.customization.config.VoyahConfig;
import com.ly.yph.api.customization.dto.*;
import com.ly.yph.api.customization.entity.*;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.goods.service.ShopMaterialRelationService;
import com.ly.yph.api.goods.service.YphStandardClassService;
import com.ly.yph.api.order.entity.ShopOrderAddress;
import com.ly.yph.api.order.entity.ShopPurchaseOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.service.ShopPurchaseOrderService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.organization.entity.SystemBudget;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.service.SystemBudgetService;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.settlement.common.entity.SettleCheckFormDetail;
import com.ly.yph.api.settlement.common.entity.SettleCheckFormExt;
import com.ly.yph.api.settlement.common.service.SettleCheckDetailService;
import com.ly.yph.api.settlement.common.service.SettleCheckFormExtService;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.util.RRException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.util.EntityUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;

import javax.annotation.Resource;
import javax.xml.xpath.XPathConstants;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 组合webservice调用参数 调用集团sap接口
 * <AUTHOR>
 * @date 2023年02月13日
 */

@Service("dfgSapService")
@Slf4j
@RefreshScope
public class DfgSapService {
    @Resource
    DfgSapConfig dfgSapConfig;

    @Resource
    ShopGoodsService shopGoodsService;
    @Resource
    ShopMaterialRelationService shopMaterialRelationService;
    @Resource
    YphStandardClassService yphStandardClassService;
    @Resource
    SystemOrganizationService systemOrganizationService;

    @Resource
    SapCostCenterService sapCostCenterService;

    @Resource
    ShopPurchaseOrderService shopPurchaseOrderService;

    @Resource
    ShopPurchaseSubOrderService subOrderService;

    @Resource
    SapIndexOrderService sapIndexOrderService;

    @Resource
    SapIndexOrderDetailService sapIndexOrderDetailService;
    @Resource
    DfgDeliveryDetailService dfgDeliveryDetailService;
    @Resource
    DfgDeliveryService dfgDeliveryService;
    @Resource(name = "dfgSapService")
    @Lazy
    DfgSapService self;

    @Resource
    SettleCheckFormExtService settleCheckFormExtService;

    @Resource
    private VoyahConfig voyahConfig;

    @Resource
    SapInvestDeliveryService sapInvestDeliveryService;

    @Resource
    SapInvestIndexOrderService sapInvestIndexOrderService;

    @Resource
    SettleCheckDetailService settleCheckDetailService;

    @Resource
    SapInvestOrderService sapInvestOrderService;

    @Resource
    private MailService mailService;

    @Resource
    private SapNonIndexOrderService sapNonIndexOrderService;

    @Resource
    private ExternalInterfaceLogService logService;

    /**
     * 创建物料
     */
    public void createSapMara(List<ShopPurchaseSubOrderDetail> goodsEntities, ShopOrderAddress shopOrderAddress){
        LoginUser loginUser = LocalUserHolder.get();
        if (CompanyEnum.DFHS.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DFG01.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())) {
            //查询是否已存在物料关系
            log.info("开始创建DFG SAP物料关系");
            List<String> goodsCodes = goodsEntities.stream().map(ShopPurchaseSubOrderDetail::getGoodsCode).collect(Collectors.toList());
            Map<String, String> map = shopMaterialRelationService.getmaraByCodes(goodsCodes,shopOrderAddress.getFactoryCode(),loginUser.getEntityOrganizationCode());
            goodsEntities.removeIf(item -> null != map.get(item.getGoodsCode()));
            if(!goodsEntities.isEmpty()){
                createDfgSapMara(goodsEntities, shopOrderAddress);
            }
        }
    }

    public void createDfgSapMara(List<ShopPurchaseSubOrderDetail> goodsEntities, ShopOrderAddress shopAddressEntity) {
        LoginUser loginUser = LocalUserHolder.get();
        List<CallCreateDfgSapMaraParamDto> callCreateDfgSapMaraParamDtos = Lists.newArrayList();
        for (ShopPurchaseSubOrderDetail goodsEntity : goodsEntities) {
            log.info("填充创建物料参数");
            ShopGoods shopGoodsEntity = shopGoodsService.getByGoodsId(goodsEntity.getGoodsId().toString());
            ClassMapRelationEntity classMapRelationEntity = getvoyahClassRelation(shopGoodsEntity,loginUser.getEntityOrganizationCode());
            CallCreateDfgSapMaraParamDto param = new CallCreateDfgSapMaraParamDto();
            param.setZzsysid(Constant.SYSID);
            param.setZbdat(DateUtils.format(new Date(),DatePattern.PURE_DATETIME_PATTERN));
            param.setMtart(classMapRelationEntity.getMaterialType());
            param.setMaktx(StrUtil.sub(goodsEntity.getGoodsName().replaceAll("[^a-zA-Z0-9\\u4E00-\\u9FA5]", ""),0,40));
            param.setMeins("EA");
            param.setMatkl(classMapRelationEntity.getScopClassCode());

            //兼容商品长度过长 导致SAP创建不了物料
            String goodsCode = goodsEntity.getGoodsCode();
            if (goodsCode.startsWith("SUP") && goodsCode.length() > 32) {
                goodsCode = goodsCode.replace("SUP", "");
            }
            param.setGroes(goodsCode);
            param.setWerks(shopAddressEntity.getFactoryCode());
            if (CompanyEnum.VOYAH.getCompanyCode().equals(loginUser.getEntityOrganizationCode())) {
                param.setEkgrp(Constant.Z06);
            } else if (CompanyEnum.DFHS.getCompanyCode().equals(loginUser.getEntityOrganizationCode())) {
                param.setEkgrp(Constant.T09);
            } else if (CompanyEnum.DFGM.getCompanyCode().equals(loginUser.getEntityOrganizationCode())) {
                param.setEkgrp(Constant.MS4);
            } else if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())
                    || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())) {
                param.setEkgrp(Constant.DFPV603);
            }
            param.setBklas(classMapRelationEntity.getMaterialsAssessment());
            param.setPeinh("1");
            param.setStprs(goodsEntity.getGoodsUnitPriceNaked().toString());
            callCreateDfgSapMaraParamDtos.add(param);
        }
        if (CompanyEnum.VOYAH.getCompanyCode().equals(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DFHS.getCompanyCode().equals(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DFG01.getCompanyCode().equals(loginUser.getEntityOrganizationCode())) {
            self.callCreateDfgSapMara(callCreateDfgSapMaraParamDtos);
        } else if (CompanyEnum.DFGM.getCompanyCode().equals(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DFPV.getCompanyCode().equals(loginUser.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equals(loginUser.getEntityOrganizationCode())) {
            List<DFGMCreateMaraInfoDto> dfgmCreateMaraInfoDtos = BeanUtil.copyToList(callCreateDfgSapMaraParamDtos, DFGMCreateMaraInfoDto.class, new CopyOptions().setIgnoreCase(true));
            for (DFGMCreateMaraInfoDto dfgmCreateMaraInfoDto : dfgmCreateMaraInfoDtos) {
                dfgmCreateMaraInfoDto.setGRPID(DateUtils.format(new Date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomInt(10000, 99999));
                if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())) {
                    dfgmCreateMaraInfoDto.setZzsysid(Constant.DFPVSYSID);
                } else if (CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode())) {
                    dfgmCreateMaraInfoDto.setZzsysid(Constant.DNASYSID);
                } else {
                    dfgmCreateMaraInfoDto.setZzsysid(Constant.DFGMSYSID);
                }
            }
            self.callDfgmSapCreateMara(dfgmCreateMaraInfoDtos);
        }
    }


    private void callbackSaveSapMara(String content, List<CallCreateDfgSapMaraParamDto> list) {
        Map<String, List<Map<String, String>>> map = JSON.parseObject(content, Map.class);

        //sap同一个接口返回两种不同格式，无奈之举
        List<Map<String, String>> sapList = Lists.newArrayList();
        Map<String, String> sapMap = null;
        if (map.get("SapT1") instanceof List) {
            sapList = map.get("SapT1");
        } else {
            sapMap = (Map<String, String>) map.get("SapT1");
            sapList.add(sapMap);
        }
        List<SaveShopMaraParamDto> saveShopMaraParamDtos = Lists.newArrayList();
        for (Map<String, String> stringStringMap : sapList) {
            if (stringStringMap.get("Zret").equals("S") || stringStringMap.get("Zret").equals("W")) {
                CallCreateDfgSapMaraParamDto callCreateDfgSapMaraParamDto = list.stream()
                        .filter(item -> item.getGroes().equals(stringStringMap.get("Groes"))).findFirst().get();
                SaveShopMaraParamDto saveShopMaraParamDto = new SaveShopMaraParamDto();
                saveShopMaraParamDto.setMaraMatnr(stringStringMap.get("Matnr"));
                saveShopMaraParamDto.setMaktMaktx(stringStringMap.get("Maktx"));
                saveShopMaraParamDto.setMaraMatkl(callCreateDfgSapMaraParamDto.getMatkl());
                saveShopMaraParamDto.setMaraMeins(callCreateDfgSapMaraParamDto.getMeins());
                saveShopMaraParamDto.setMaraMtart(callCreateDfgSapMaraParamDto.getMtart());
                saveShopMaraParamDto.setMaraUnit(callCreateDfgSapMaraParamDto.getMeins());
                saveShopMaraParamDto.setMaraWerks(callCreateDfgSapMaraParamDto.getWerks());
                saveShopMaraParamDto.setSourceSysId(Constant.SOURCE_DFG_SAP);
                //兼容商品长度过长 导致SAP创建不了物料
                String goodsCode = callCreateDfgSapMaraParamDto.getGroes();
                if(goodsCode.startsWith("_")){
                    goodsCode = "SUP" + goodsCode;
                }
                saveShopMaraParamDto.setGoodsCode(goodsCode);
                saveShopMaraParamDto.setGoodsName(callCreateDfgSapMaraParamDto.getMaktx());
                saveShopMaraParamDto.setGoodsPrice(callCreateDfgSapMaraParamDto.getStprs());
                SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigEntity = yphStandardClassService.getOneByWerksCode(callCreateDfgSapMaraParamDto.getWerks());
                if (sapCompanyWerksBusinessConfigEntity!=null && StringUtils.isNotBlank(sapCompanyWerksBusinessConfigEntity.getCompanyCode())){
                    saveShopMaraParamDto.setCompanyCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
                }else {
                    log.info("company_code is null! werks_code:{}",callCreateDfgSapMaraParamDto.getWerks());
                    continue;
                }
                SystemOrganization systemOrganization = systemOrganizationService.getOrganizationByCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
                saveShopMaraParamDto.setOrganizationId(systemOrganization.getId());
                saveShopMaraParamDto.setCompanyCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
                saveShopMaraParamDtos.add(saveShopMaraParamDto);
            } else {
                log.error("callbackSaveSapMara error goods is:" + stringStringMap);
            }
        }
        //保存物料
        shopMaterialRelationService.saveMaterRelation(saveShopMaraParamDtos);
    }

    private Map<String,String> callbackSaveSapMaraNoReal(String content, List<CallCreateDfgSapMaraParamDto> list) {
        Map<String, List<Map<String, String>>> map = JSON.parseObject(content, Map.class);

        //sap同一个接口返回两种不同格式，无奈之举
        List<Map<String, String>> sapList = Lists.newArrayList();
        Map<String, String> sapMap = null;
        if (map.get("SapT1") instanceof List) {
            sapList = map.get("SapT1");
        } else {
            sapMap = (Map<String, String>) map.get("SapT1");
            sapList.add(sapMap);
        }
        Map<String,String> result = Maps.newHashMap();
        for (Map<String, String> stringStringMap : sapList) {
            if (stringStringMap.get("Zret").equals("S") || stringStringMap.get("Zret").equals("W")) {
                result.put(stringStringMap.get("Groes"),stringStringMap.get("Matnr"));
            } else {
                log.error("callbackSaveSapMara error goods is:" + stringStringMap);
                throw new RRException("创建SAP物料信息异常，请联系管理员！");
            }
        }
        return result;
    }

    /**
     * 猛士物料信息回传保存
     * @param content
     * @param list
     */
    private void callbackSaveDFGMSapMara(String content, List<DFGMCreateMaraInfoDto> list) {
        Map<String, List<Map<String, String>>> map = JSON.parseObject(content, Map.class);
        //sap同一个接口返回两种不同格式，无奈之举
        List<Map<String, String>> sapList = map.get("ZSDFSC001_RES");
        List<SaveShopMaraParamDto> saveShopMaraParamDtos = Lists.newArrayList();
        for (Map<String, String> stringStringMap : sapList) {
            if (stringStringMap.get("ZRET").equals("S") || stringStringMap.get("ZRET").equals("W")) {
                DFGMCreateMaraInfoDto callCreateDfgSapMaraParamDto = list.stream()
                        .filter(item -> item.getGroes().equals(stringStringMap.get("GROES"))).findFirst().get();
                SaveShopMaraParamDto saveShopMaraParamDto = new SaveShopMaraParamDto();
                saveShopMaraParamDto.setMaraMatnr(stringStringMap.get("MATNR"));
                saveShopMaraParamDto.setMaktMaktx(callCreateDfgSapMaraParamDto.getMaktx());
                saveShopMaraParamDto.setMaraMatkl(callCreateDfgSapMaraParamDto.getMatkl());
                saveShopMaraParamDto.setMaraMeins(callCreateDfgSapMaraParamDto.getMeins());
                saveShopMaraParamDto.setMaraMtart(callCreateDfgSapMaraParamDto.getMtart());
                saveShopMaraParamDto.setMaraUnit(callCreateDfgSapMaraParamDto.getMeins());
                saveShopMaraParamDto.setMaraWerks(callCreateDfgSapMaraParamDto.getWerks());
                saveShopMaraParamDto.setSourceSysId(Constant.SOURCE_DFGM_SAP);
                saveShopMaraParamDto.setGoodsCode(callCreateDfgSapMaraParamDto.getGroes());
                saveShopMaraParamDto.setGoodsName(callCreateDfgSapMaraParamDto.getMaktx());
                saveShopMaraParamDto.setGoodsPrice(callCreateDfgSapMaraParamDto.getStprs());
                SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigEntity = yphStandardClassService.getOneByWerksCode(callCreateDfgSapMaraParamDto.getWerks());
                if (sapCompanyWerksBusinessConfigEntity!=null && StringUtils.isNotBlank(sapCompanyWerksBusinessConfigEntity.getCompanyCode())){
                    saveShopMaraParamDto.setCompanyCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
                }else {
                    log.info("【callbackSaveDFGMSapMara】未匹配到工厂信息，{}",callCreateDfgSapMaraParamDto.getWerks());
                    continue;
                }
                SystemOrganization systemOrganization = systemOrganizationService.getOrganizationByCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
                saveShopMaraParamDto.setOrganizationId(systemOrganization.getId());
                saveShopMaraParamDto.setCompanyCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
                saveShopMaraParamDtos.add(saveShopMaraParamDto);
            } else {
                log.error("【callbackSaveDFGMSapMara】创建物料错误:" + JSONUtil.toJsonStr(stringStringMap));
                throw new RRException("创建SAP物料信息异常，请联系管理员！");
            }
        }
        //保存物料
        shopMaterialRelationService.saveMaterRelation(saveShopMaraParamDtos);
    }

    /**
     * 找集团SAP创建物料（适用于 岚图，华神，集团）
     * @param list
     */
    public void callCreateDfgSapMara(List<CallCreateDfgSapMaraParamDto> list) {
        try {
            String params = buildCreateDfgSapMaraParam(list);
            log.info("call buildCreateDfgSapMaraParam:" + params);
            CloseableHttpAsyncClient client = HttpAsyncClients.createDefault();
            client.start();
            final HttpPost post = new HttpPost(dfgSapConfig.getDfgEsbDfmall());
            //设置请求头    这里根据个人来定义
            post.addHeader("Content-type", "application/json; charset=utf-8");
            post.setHeader("Accept", "application/json");
            StringEntity stringEntity = new StringEntity(params, ContentType.APPLICATION_JSON);
            post.setEntity(stringEntity);
            client.execute(post, new FutureCallback<HttpResponse>() {
                @Override
                public void completed(final HttpResponse response) {
                    log.info(" callback thread id is : " + Thread.currentThread().getId());
                    log.info(post.getRequestLine() + "->" + response.getStatusLine());
                    String content = "";
                    try {
                        content = EntityUtils.toString(response.getEntity(), "UTF-8");
                        log.info("call dfg sap create mara response content is : " + content);
                        callbackSaveSapMara(content, list);
                    } catch (Exception e) {
                        log.error("create dfg sap mara error" + e.getMessage());
                    } finally {
                        log.error("create dfg sap mara finally");
                    }
                }

                @Override
                public void failed(final Exception ex) {
                    log.info(post.getRequestLine() + "->failed:" + ex);
                    log.info(" callback thread id is : " + Thread.currentThread().getId());
                }

                @Override
                public void cancelled() {
                    log.info(post.getRequestLine() + " cancelled");
                    log.info(" callback thread id is : " + Thread.currentThread().getId());
                }

            });
        } catch (Exception ex) {
            log.error("callCreateDfgSapMara error：", ex);
            throw new RRException("请求callCreateDfgSapMara异常");
        }
    }

    public Map<String,String> callCreateDfgSapMaraSync(List<CallCreateDfgSapMaraParamDto> list) {
        try {
            if(CollUtil.isEmpty(list)){
                return Maps.newHashMap();
            }
            String params = buildCreateDfgSapMaraParam(list);
            log.info("【callCreateDfgSapMaraSync】YGW创建SAP物料：参数:" + params);
            log.info("【callCreateDfgSapMaraSync】YGW创建SAP物料：URL:" + dfgSapConfig.getDfgEsbDfmall());
            Stopwatch watch = Stopwatch.createStarted();
            String content = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
            log.info("【callCreateDfgSapMaraSync】YGW创建SAP物料：返回:" + content);
            watch.stop();
            logService.saveExternalInterfaceLog(CompanyEnum.VOYAH.getCompanyCode(),
                    ExtInterfaceSystemEnum.SAP,
                    ExtInterfaceBussEnum.SAP_CREATE_MATER_SEND,
                    "",
                    dfgSapConfig.getDfgEsbDfmall(), params,
                    content,
                    watch.elapsed(TimeUnit.MILLISECONDS));
            return callbackSaveSapMaraNoReal(content, list);
        } catch (Exception ex) {
            log.error("callCreateDfgSapMaraSync error：", ex);
            throw new RRException("请求SAP系统批量创建物料异常！");
        }
    }

    /**
     * 找猛士SAP创建物料 适用于 猛士
     * @param list
     */
    public void callDfgmSapCreateMara(List<DFGMCreateMaraInfoDto> list) {
        try {
            log.info("call buildCreateDfgSapMaraParam:" + list);
            DFGMDFSC001ParamDto dfgmdfsc001ParamDto = new DFGMDFSC001ParamDto();
            dfgmdfsc001ParamDto.setZSDFSC001_REQ(list);
            Stopwatch watch = Stopwatch.createStarted();
            LoginUser loginUser = LocalUserHolder.get();
            String companyCode="System";
            if(!Objects.isNull(loginUser)){
                companyCode = loginUser.getEntityOrganizationCode();
            }
            String body = callDfgmSap(dfgmdfsc001ParamDto);
            watch.stop();
            logService.saveExternalInterfaceLog(companyCode,
                    ExtInterfaceSystemEnum.SAP,
                    ExtInterfaceBussEnum.SAP_CREATE_MATER_SEND,
                    "",
                    dfgSapConfig.getDfgmSapUrl(), JSONUtil.toJsonStr(dfgmdfsc001ParamDto),
                    body,
                    watch.elapsed(TimeUnit.MILLISECONDS));
            callbackSaveDFGMSapMara(body,list);
        } catch (Exception ex) {
            log.error("callCreateDfgSapMara error：", ex);
            throw new RRException("请求callCreateDfgSapMara异常");
        }
    }

    @Transactional
    public void getCostCenterData() {
        String result = HttpUtil.get(dfgSapConfig.getDfgSapEsbWsdl() + DfgSapInterfaceCode.getCostCenterDataUrl);
        log.info("callGetCostCenterData:" + result);
        if (StrUtil.isBlank(result)) {
            return;
        }
        List<SapCostCenterEntity> sapCostCenterEntities = JSONUtil.toList(result, SapCostCenterEntity.class);
        for (SapCostCenterEntity entity : sapCostCenterEntities) {
            if (StrUtil.isBlank(entity.getGsber())) {
                log.info("getCostCenterDataNotGsber:" + JSON.toJSONString(entity));
                continue;
            }
            UpdateWrapper<SapCostCenterEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SapCostCenterEntity::getKostl, entity.getKostl());
            sapCostCenterService.saveOrUpdate(entity, updateWrapper);
        }
    }

    private String buildCallSendPurchaseToDFGSAP(List<CallSendPurchaseToDFGSAPParamDto> callSendPurchaseToDFGSAPParamDtos,String companyCode) {
        DfgSapBaseParamDto nodesBaseParam;
        if(CompanyEnum.DFHS.getCompanyCode().equalsIgnoreCase(companyCode)){
            nodesBaseParam = buildBaseParam(dfgSapConfig.getDfgSapCondition().equals("test")?DfgSapInterfaceCode.DFG_SAP_136_QAS:DfgSapInterfaceCode.DFG_SAP_136);
        }else{
            nodesBaseParam = buildBaseParam(DfgSapInterfaceCode.DFG_SAP_173);
        }

        log.info("nodesBaseParam_136:{}",nodesBaseParam);
        StringBuffer head = buildDfgSapWsHead(JSON.toJSONString(nodesBaseParam));
        StringBuffer tail = buildcallSendPurchaseToDFGSAPBizParam(callSendPurchaseToDFGSAPParamDtos);
        return head.append(tail).toString();

    }

    public String callSendPurchaseToDFGSAP(List<CallSendPurchaseToDFGSAPParamDto> callSendPurchaseToDFGSAPParamDtos,String companyCode) {
        String params = buildCallSendPurchaseToDFGSAP(callSendPurchaseToDFGSAPParamDtos,companyCode);
        log.info("【callSendPurchaseToDFGSAP】发送费用类订单到集团SAP，参数：" + params);
        Stopwatch watch = Stopwatch.createStarted();
        String response = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
        log.info("【callSendPurchaseToDFGSAP】发送费用类订单到集团SAP，返回：" + response);
        watch.stop();
        logService.saveExternalInterfaceLog(companyCode,
                ExtInterfaceSystemEnum.SAP,
                ExtInterfaceBussEnum.SAP_CREATE_ORDER_SEND,
                "",
                dfgSapConfig.getDfgEsbDfmall(), params,
                response,
                watch.elapsed(TimeUnit.MILLISECONDS));
        Document document = XmlUtil.readXML(response);
        String value = getXpathString("return", document);
        return value;
    }

    private static String getXpathString(String node, Document document) {
        Object value = XmlUtil.getByXPath("//" + node, document, XPathConstants.STRING);
        return value.toString();
    }

    public String callSendPurchaseToDFGMSAP(List<DFGMPurchaseInfoParamDto> callSendPurchaseToDFGSAPParamDtos) {
        DFGMDFSC002ParamDto dfgmdfsc002ParamDto = new DFGMDFSC002ParamDto();
        dfgmdfsc002ParamDto.setZSDFSC002_REQ(callSendPurchaseToDFGSAPParamDtos);
        String params = JSONUtil.toJsonStr(dfgmdfsc002ParamDto);
        log.info("【callSendPurchaseToDFGMSAP】发送费用或存货类订单到集团SAP，参数：" + params);
        Stopwatch watch = Stopwatch.createStarted();
        String response = callDfgmSap(params);
        LoginUser loginUser = LocalUserHolder.get();
        String companyCode="System";
        if(!Objects.isNull(loginUser)){
            companyCode = loginUser.getEntityOrganizationCode();
        }
        watch.stop();
        logService.saveExternalInterfaceLog(companyCode,
                ExtInterfaceSystemEnum.SAP,
                ExtInterfaceBussEnum.SAP_CREATE_ORDER_SEND,
                "",
                dfgSapConfig.getDfgmSapUrl(), JSONUtil.toJsonStr(params),
                response,
                watch.elapsed(TimeUnit.MILLISECONDS));
        log.info("【callSendPurchaseToDFGMSAP】发送费用或存货类订单到集团SAP，返回：" + response);
        return response;
    }

    /**
     * 保存sap返回的sap采购凭证 （岚图，集团，华神）
     * @param value
     */
    public void saveDfgSapOrderSn(String value){
        value  = StringEscapeUtils.unescapeHtml4(value);
        Map<String, List<Map<String, String>>> map = JSON.parseObject(value, Map.class);
        List<Map<String, String>> sapList = Lists.newArrayList();
        Map<String, String> sapMap = null;
        if (map.get("SapT1") instanceof List) {
            sapList = map.get("SapT1");
        } else {
            sapMap = (Map<String, String>) map.get("SapT1");
            sapList.add(sapMap);
        }
        //因为发送订单到sap是以单个订单维度，所以发送成功只用取一个
        Map<String, String> sapT1 = sapList.get(0);
        String ebeln = sapT1.get("Ebeln");
        String purchaseNumber= sapT1.get("Zebeln");
        if(Constant.S.equals(Convert.toStr(map.get("Ov1")))){
            //保存
            UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber,purchaseNumber).set(ShopPurchaseOrder::getOtherRelationNumber,ebeln);
            shopPurchaseOrderService.update(updateWrapper);
        }else{
            log.error("【saveDfgSapOrderSn】createpurchasetodfgsaperror:purchaseNumber:" + purchaseNumber);
            mailService.sendEmail("【sendDeliveryInfoToSap】【费用订单发送到外部系统异常】", "参数：purchaseNumber：" + purchaseNumber, "<EMAIL>");
            throw new RRException("发送订单到SAP返回 ERROR：" + purchaseNumber);
        }
    }

    /**
     * 保存sap返回的sap采购凭证 （猛士）
     * @param value
     */
    public void saveDfgmSapOrderSn(String value) {
        Map<String, List<Map<String, String>>> map = JSON.parseObject(value, Map.class);
        List<Map<String, String>> sapList = map.get("ZSDFSC002_RES");
        Map<String, String> sapT1 = sapList.get(0);
        String purchaseNumber = sapT1.get("ZEBELN");
        //非平台结算已订单维度发送
        if (StrUtil.startWith(purchaseNumber,"N")) {
            for (Map<String, String> stringStringMap : sapList) {
                String ebeln = stringStringMap.get("EBELN");
                String orderNumber = stringStringMap.get("ZEBELN");
                orderNumber = StrUtil.replace(orderNumber, "N", "");
                //外部结算订单
                if (Constant.S.equals(stringStringMap.get("TYPE"))) {
                    //如果N开头就保存订单号
                    UpdateWrapper<ShopPurchaseSubOrder> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().eq(ShopPurchaseSubOrder::getOrderNumber, orderNumber).set(ShopPurchaseSubOrder::getOrderSapNumber, ebeln);
                    subOrderService.update(updateWrapper);
                } else {
                    log.error("【saveDfgmSapOrderSn】非平台结算订单已订单维度发送 :purchaseNumber:" + purchaseNumber);
                    mailService.sendEmail("【saveDfgmSapOrderSn】【非平台结算订单已订单维度发送】", "参数：purchaseNumber：" + purchaseNumber, "<EMAIL>");
                    throw new RRException("发送订单到SAP返回 ERROR：" + purchaseNumber);
                }
            }
        } else {
            //商城订单
            if (Constant.S.equals(sapT1.get("TYPE"))) {
                String ebeln = sapT1.get("EBELN");
                UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber).set(ShopPurchaseOrder::getOtherRelationNumber, ebeln);
                shopPurchaseOrderService.update(updateWrapper);
            } else {
                log.error("【saveDfgmSapOrderSn】toSAP创建采购单ERROR:purchaseNumber:" + purchaseNumber);
                mailService.sendEmail("【saveDfgmSapOrderSn】【费用订单发送到外部系统异常】", "参数：purchaseNumber：" + purchaseNumber, "<EMAIL>");
                throw new RRException("发送订单到SAP返回 ERROR：" + purchaseNumber);
            }
        }
    }

    /**
     * 根据公司名称判断公司
     * @param orgId
     * @return
     */
    @DataPermission(enable = false)
    public SapContractRealEntity upwardGetOrgName(Long orgId) {
        SystemOrganization systemOrganization = systemOrganizationService.getOrganization(orgId);
        if (Objects.isNull(systemOrganization)) {
            log.error("not found parent org orgId:" + orgId);
            return null;
        }
        List<SapContractRealEntity> sapContractRealEntities = yphStandardClassService.getSapContractAll();
        for (SapContractRealEntity sapContractRealEntity : sapContractRealEntities) {
            if (systemOrganization.getName().equals(sapContractRealEntity.getContractName())) {
                return sapContractRealEntity;
            }
        }
        if (!"1".equals(systemOrganization.getParentId().toString())) {
            return upwardGetOrgName(systemOrganization.getParentId());
        }
        return null;
    }

    private static StringBuffer buildDfgSapWsHead(String baseParams) {
        StringBuffer nodes = new StringBuffer();
        nodes.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ws=\"http://ws.esb.dawnpro.com/\">\n");
        nodes.append("\t" + "<soapenv:Header/>" + "\n");
        nodes.append("\t" + "<soapenv:Body>" + "\n");
        nodes.append("\t" + "<ws:sendMessage>" + "\n");
        nodes.append("\t" + "\t" + "<baseParams>" + "\n");
        nodes.append(baseParams);
        nodes.append("\t" + "\t" + "</baseParams>" + "\n");
        return nodes;
    }

    private String buildCreateDfgSapMaraParam(List<CallCreateDfgSapMaraParamDto> list) {
        DfgSapBaseParamDto dfgSapBaseParamDto = buildBaseParam(dfgSapConfig.getDfgSapCondition().equals("test")?DfgSapInterfaceCode.DFG_SAP_129_QAS:DfgSapInterfaceCode.DFG_SAP_129);
        log.info("DfgSapBaseParamDto_129:{}",dfgSapBaseParamDto);
        StringBuffer head = buildHead(JSON.toJSONString(dfgSapBaseParamDto));
        StringBuffer nodesBizParam = buildBizParam(list);
        return head.append(nodesBizParam).toString();
    }


    private static DfgSapBaseParamDto buildBaseParam(String tradeCode) {
        DfgSapBaseParamDto dfgSapBaseParamDto = new DfgSapBaseParamDto();
        dfgSapBaseParamDto.setReqSerialNo(UUID.randomUUID().toString().substring(2, 30));
        dfgSapBaseParamDto.setClientCode("DFG_DFSC");
        dfgSapBaseParamDto.setTradeCode(tradeCode);
        dfgSapBaseParamDto.setVersion("1");
        dfgSapBaseParamDto.setTradeDescription("");
        dfgSapBaseParamDto.setTradeTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        return dfgSapBaseParamDto;
    }

    private static StringBuffer buildHead(String baseParams) {
        StringBuffer nodes = new StringBuffer();
        nodes.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ws=\"http://ws.esb.dawnpro.com/\">\n");
        nodes.append("\t" + "<soapenv:Header/>" + "\n");
        nodes.append("\t" + "<soapenv:Body>" + "\n");
        nodes.append("\t" + "<ws:sendMessage>" + "\n");
        nodes.append("\t" + "\t" + "<baseParams>" + "\n");
        nodes.append("<![CDATA[" + baseParams + "]]>");
        nodes.append("\t" + "\t" + "</baseParams>" + "\n");
        return nodes;
    }

    private static StringBuffer buildBizParam(List<CallCreateDfgSapMaraParamDto> list) {
        String bizParams = JSON.toJSONString(list);
        return buildTail(bizParams);
    }

    private static StringBuffer buildTail(String bizParams) {
        StringBuffer nodes = new StringBuffer();
        nodes.append("<bizParams>{");
        nodes.append("\"SapZfunMm030\":{\"SapT1\":");
        nodes.append(bizParams);
        nodes.append("}}</bizParams>");
        nodes.append("</ws:sendMessage>");
        nodes.append("</soapenv:Body>");
        nodes.append("</soapenv:Envelope>");
        return nodes;
    }

    private static StringBuffer buildcallSendPurchaseToDFGSAPBizParam(List<CallSendPurchaseToDFGSAPParamDto> callSendPurchaseToDFGSAPParamDto) {
        StringBuffer nodes = new StringBuffer();
        nodes.append("<bizParams>");
        nodes.append("{\"SapZfunMm031\": { \"SapT1\":");
        String bizParams = JSON.toJSONString(callSendPurchaseToDFGSAPParamDto);
        nodes.append(bizParams);
        nodes.append(" }}</bizParams> </ws:sendMessage>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>");
        return nodes;
    }

    private static StringBuffer buildcallCreateIndexOrderBizParam(List<CallDFGSAPCreateIndexOrderParamDto> callSAPCreateIndexOrderParamDtos) {
        StringBuffer nodes = new StringBuffer();
        nodes.append(" <bizParams>{\"SapZfunMm034\": { \"SapT1\":");
        String bizParams = JSON.toJSONString(callSAPCreateIndexOrderParamDtos);
        nodes.append(bizParams);
        nodes.append(" }}</bizParams></ws:sendMessage>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>");
        return nodes;
    }

    public ClassMapRelationEntity getvoyahClassRelation(ShopGoods goodsEntity,String companyCode){
        //查询分类物料信息
        List<ClassMapRelationEntity> classMapRelationEntities = yphStandardClassService.getClassMapRelation();
        List<ClassMapRelationEntity> voyahClassMapRelationEntities = classMapRelationEntities.stream().filter(item->companyCode.equalsIgnoreCase(item.getCompanyCode())).collect(Collectors.toList());
        List<String> goodsTypeClass = Lists.newArrayList();
        goodsTypeClass.add(goodsEntity.getFirstClass());
        goodsTypeClass.add(goodsEntity.getSecondClass());
        goodsTypeClass.add(goodsEntity.getThirdClass());
        ClassMapRelationEntity returnClassType= null;
        for (ClassMapRelationEntity classItem : voyahClassMapRelationEntities) {
            if(classItem.getScClassCode().equals(goodsEntity.getFirstClass())){
                returnClassType = classItem;
                break;
            }
        }
        if(Objects.isNull(returnClassType)){
            List<ClassMapRelationEntity> classMapRelationEntities1 = voyahClassMapRelationEntities.stream()
                    .filter(item -> item.getScClassCode().equals("IT")).collect(Collectors.toList());
            if(!org.springframework.util.CollectionUtils.isEmpty(classMapRelationEntities1)){
                returnClassType = classMapRelationEntities1.get(0);
            }
        }
        return returnClassType;
    }

    /**
     * SAP发送索引单
     * @param list
     * @return
     */
    public String callSAPSYD(List<CallDFGSAPCreateIndexOrderParamDto> list,String companyCode){
        DfgSapBaseParamDto nodesBaseParam;
        if(CompanyEnum.DFHS.getCompanyCode().equalsIgnoreCase(companyCode)){
            nodesBaseParam = buildBaseParam(dfgSapConfig.getDfgSapCondition().equals("test") ? DfgSapInterfaceCode.DFG_SAP_130_QAS : DfgSapInterfaceCode.DFG_SAP_130);
        }else{
            nodesBaseParam = buildBaseParam(DfgSapInterfaceCode.DFG_SAP_178);
        }

        log.info("nodesBaseParam_130:{}",nodesBaseParam);
        StringBuffer head = buildDfgSapWsHead(JSON.toJSONString(nodesBaseParam));
        StringBuffer tail = buildcallCreateIndexOrderBizParam(list);
        log.info("call callSAPCreateIndexOrder params:" + head + tail);
        StringBuffer paramJson = head.append(tail);
        String response = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), paramJson.toString());
        log.info("callSAPCreateIndexOrder return response:" + response);
        Document document = XmlUtil.readXML(response);
        String value = getXpathString("return", document);
        return value;
    }

    /**
     * 同步索引单结算 (岚图，华神，集团)
     * @param jsonContent
     */
    public void syncIndexState(String jsonContent){
        Document document = XmlUtil.readXML(jsonContent);
        Map<String, Object> contentMap = XmlUtil.xmlToMap(document);
        Map<String, Object> SapZmme021esbMap = (Map<String, Object>) contentMap.get("NS1:SapZmme021esb");
        Map<String, Object> SapZmme021esbIDocBO = (Map<String, Object>) SapZmme021esbMap.get("SapZmme021esbIDocBO");
        Map<String, Object> SapZmme021esbDataRecord = (Map<String, Object>) SapZmme021esbIDocBO.get("SapZmme021esbDataRecord");
        Map<String, String> SapZmme021esbZe0mme021000 = (Map<String, String>) SapZmme021esbDataRecord.get("SapZmme021esbZe0mme021000");
        //{"Jszt":"X","Ekorg":"1227","Ekgrp":"T08","Zqrsyh":"F12212022062439","Jsrq":"20220624"}
        //确认索引单号
        String indexOrderSn = SapZmme021esbZe0mme021000.get("Zqrsyh");
        String state = SapZmme021esbZe0mme021000.get("Jszt");
        String ekorg = SapZmme021esbZe0mme021000.get("Ekorg");

        ServiceResult result = self.processSettlementData(indexOrderSn,state);
        log.info("syncIndexState return:" + result.toString());
    }
    /**
     * 同步索引单结算 (猛士)
     * @param jsonContent
     */
    public void dfgmSyncIndexState(String jsonContent){
        Map<String, List<Map<String, String>>> map = JSON.parseObject(jsonContent, Map.class);
        List<Map<String, String>> sapTap = map.get("ZSDFSC005_REQ");
        Map<String, String> indexOrderMap = sapTap.get(0);
        //确认索引单号
        String indexOrderSn = indexOrderMap.get("ZSYH");
        String state = indexOrderMap.get("ZJSZT") == null ? "" : indexOrderMap.get("ZJSZT");
        if(StrUtil.startWith(indexOrderSn,"PN")){
            sapNonIndexOrderService.processNonSettlementData(indexOrderSn,state);
            return;
        }
        ServiceResult result = self.processSettlementData(indexOrderSn,state);
        log.info("syncIndexState return:" + result.toString());
    }



    /**
     * 同步索引单创建状态
     *
     * @param indexOrderSn
     * @param state
     * @return
     */
    public ServiceResult processSettlementData(String indexOrderSn, String state) {
        //state = X 已结算
        //state = "" 冲销
        if(StringUtils.isBlank(indexOrderSn)){
            return ServiceResult.error("索引单号为空");
        }
        //查询索引单
        QueryWrapper<SapIndexOrderEntity> indexOrderQueryWrapper = new QueryWrapper<>();
        indexOrderQueryWrapper.lambda().eq(SapIndexOrderEntity::getIndexOrderSn,indexOrderSn).last("limit 1");

        //查询索引单明细
        QueryWrapper<SapIndexOrderDetailEntity> indexDetailQuery = new QueryWrapper<>();
        indexDetailQuery.lambda().eq(SapIndexOrderDetailEntity::getIndexOrderSn,indexOrderSn);
        List<SapIndexOrderDetailEntity> indexOrderDetailEntities = sapIndexOrderDetailService.list(indexDetailQuery);
        if(indexOrderDetailEntities.isEmpty()){
            log.error("data error sap delivery is not found IndexOrderSn："+indexOrderSn);
            return ServiceResult.error("data error sap delivery is not found IndexOrderSn："+indexOrderSn);
        }

        //查询索引单明细里面的收货行项目
        List<Integer> sddIds = indexOrderDetailEntities.stream().map(SapIndexOrderDetailEntity::getSddId).collect(Collectors.toList());
        List<SapDeliveryDetailEntity> sapDeliveryDetailEntities = dfgDeliveryDetailService.listByIds(sddIds);

        //查询收货主表
        if(sapDeliveryDetailEntities.isEmpty()){
            log.error("data error sap delivery detail is not found sddIds：" + sddIds);
            throw new RRException("data error sap delivery detail is not found sddIds:" + sddIds);
        }
        if("X".equals(state)){
            /**
             * 结算：
             *      修改索引单状态。
             *      修改收货明细状态
             */

            Integer indexState = Constant.JS;
            //修改索引单状态
            UpdateWrapper<SapIndexOrderEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(SapIndexOrderEntity::getIndexOrderSn,indexOrderSn)
                    //改为已结算
                    .set(SapIndexOrderEntity::getIndexState,indexState);
            sapIndexOrderService.update(updateWrapper);

            //修改收货明细状态
            UpdateWrapper<SapDeliveryDetailEntity> deliveryDetailUpdateWapper = new UpdateWrapper<>();

            deliveryDetailUpdateWapper.lambda()
                    .in(SapDeliveryDetailEntity::getSddId,sddIds)
                    .set(SapDeliveryDetailEntity::getIndexState,indexState);
            dfgDeliveryDetailService.update(deliveryDetailUpdateWapper);
        }else if(StringUtils.isBlank(state)){
            /**
             * 冲销：
             */
            //修改索引单状态
            UpdateWrapper<SapIndexOrderEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(SapIndexOrderEntity::getIndexOrderSn,indexOrderSn)
                    //改为已冲销
                    .set(SapIndexOrderEntity::getIndexState,Constant.CX);
            sapIndexOrderService.update(updateWrapper);

            //修改收货明细状态
            UpdateWrapper<SapDeliveryDetailEntity> deliveryDetailUpdateWapper = new UpdateWrapper<>();
            deliveryDetailUpdateWapper.lambda()
                    .in(SapDeliveryDetailEntity::getSddId,sddIds)
                    .set(SapDeliveryDetailEntity::getIndexState,Constant.CX);
            dfgDeliveryDetailService.update(deliveryDetailUpdateWapper);

            List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList = dfgDeliveryDetailService.listByIds(sddIds);
            List<String> mblnrList = sapDeliveryDetailEntityList.stream().map(SapDeliveryDetailEntity::getMblnr).distinct().collect(Collectors.toList());
            QueryWrapper<SapDeliveryEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SapDeliveryEntity::getMblnr,mblnrList);
            List<SapDeliveryEntity> sapDeliveryEntities = dfgDeliveryService.list(queryWrapper);
            sapIndexOrderService.updateDeliverySubmitState(sapDeliveryEntities.stream().map(SapDeliveryEntity::getSdId).collect(Collectors.toList()));
        }
        return ServiceResult.succ();
    }

  /**
   * 调用猛士接口平台统一入口
   *
   * @param params
   * @return
   */
  public String callDfgmSap(Object params) {
      Map reqHeaders = buildDfgmHeaders();
      try {
          log.info("【callDfgmSap】请求猛士SAP：reqHeaders:" + JSONUtil.toJsonStr(reqHeaders));
          log.info("【callDfgmSap】请求猛士SAP：参数:" + JSONUtil.toJsonStr(params));
          log.info("【callDfgmSap】请求猛士SAP：URL:" + dfgSapConfig.getDfgmSapUrl());
          String body = HttpUtil.createPost(dfgSapConfig.getDfgmSapUrl()).addHeaders(reqHeaders).body(JSONUtil.toJsonStr(params)).execute().body();
          // API请求路径
          log.info("【callDfgmSap】请求猛士SAP：返回:" + body);
          return body;
      } catch (Exception e) {
          log.error("【callDfgmSap】请求猛士SAP：异常", e);
      }
      return "";
  }

    public Map buildDfgmHeaders(){
        //请求Header参数定义
        Map<String, String> reqHeaders = new HashMap<>();
        //客户端ID 【必填】
        reqHeaders.put("X-App-Id", dfgSapConfig.getDfgmSapAppId());
        //时间戳 【必填】
        reqHeaders.put("X-Timestamp", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN));
        //消息流水号 【必填】
        reqHeaders.put("X-Sequence-No", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + NumberUtil.generateBySet(0, 99999, 5)[0]);

        reqHeaders.put("apiKey", dfgSapConfig.getDfgmSapUsername());
        return reqHeaders;
    }

    public ServiceResult queryDfgSapInvestIndexOrder(String sapCompanyCode,String beginTime,String beginEnd){
        DFGSAP156ParamsDto dfgsap156ParamsDto=new DFGSAP156ParamsDto();
        dfgsap156ParamsDto.setIv1(sapCompanyCode);
        dfgsap156ParamsDto.setIv3(beginTime);
        dfgsap156ParamsDto.setIv4(beginEnd);
        String params = buildContent(DfgSapInterfaceCode.DFG_SAP_156, DfgSapInterfaceCode.DFG_SAP_156_FUNC, dfgsap156ParamsDto, true);
        log.info("call queryDfgSapInvestIndexOrder params:" + params);
        String response = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
        log.info("queryDfgSapInvestIndexOrder return response :" + response);
        Document document = XmlUtil.readXML(response);
        String value = getXpathString("return", document);

        List<Map<String, String>> responseMap = convSapSBCode(value);
        List<SapInvestIndexOrderEntity> sapInvestIndexOrderEntities = BeanUtil.copyToList(responseMap, SapInvestIndexOrderEntity.class, new CopyOptions().setIgnoreCase(true));

        if(CollectionUtil.isEmpty(sapInvestIndexOrderEntities)){
            log.info("【queryDfgSapInvestIndexOrder】该时间段未查询到有效索引单数据");
            return ServiceResult.error("该时间段未查询到有效索引单数据！请联系SAP业务人员创建后在重试！");
        }

        List<SapInvestIndexOrderEntity> updateIndexOrder = Lists.newArrayList();
        //索引单数量少
        for (SapInvestIndexOrderEntity sapInvestIndexOrderEntity : sapInvestIndexOrderEntities) {
            QueryWrapper<SapInvestIndexOrderEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SapInvestIndexOrderEntity::getZsyh,sapInvestIndexOrderEntity.getZsyh())
                    .eq(SapInvestIndexOrderEntity::getEbeln,sapInvestIndexOrderEntity.getEbeln())
                    .eq(SapInvestIndexOrderEntity::getEbelp,sapInvestIndexOrderEntity.getEbelp());
            SapInvestIndexOrderEntity filterIndexOrder = sapInvestIndexOrderService.getOne(queryWrapper);
            if ("X".equalsIgnoreCase(sapInvestIndexOrderEntity.getJszt())) {
                sapInvestIndexOrderEntity.setIndexState(1);
            }
            if(Objects.isNull(filterIndexOrder)){
                //查询是否存在收货数据 ebeln ebelp
                QueryWrapper<SapInvestOrderEntity> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.lambda()
                        .eq(SapInvestOrderEntity::getEbeln, sapInvestIndexOrderEntity.getEbeln())
                        .eq(SapInvestOrderEntity::getEbelp, sapInvestIndexOrderEntity.getEbelp());
                List<SapInvestOrderEntity> sapInvestOrderEntities = sapInvestOrderService.list(queryWrapper1);
                if(CollectionUtil.isEmpty(sapInvestOrderEntities)){
                    log.info("【queryDfgSapInvestIndexOrder】未查询到该采购凭证，考虑该索引单不属于商城，不保存数据。ebeln:" + sapInvestIndexOrderEntity.getEbeln() + " ebelp:" + sapInvestIndexOrderEntity.getEbelp());
                    continue;
                }
                updateIndexOrder.add(sapInvestIndexOrderEntity);
            }else{
                //修改
                sapInvestIndexOrderEntity.setId(filterIndexOrder.getId());
                updateIndexOrder.add(sapInvestIndexOrderEntity);
            }
        }
        sapInvestIndexOrderService.saveOrUpdateBatch(updateIndexOrder);
        //将已结算的数据发送到岚图vhome
        sendSettleToVhome(updateIndexOrder);
        return ServiceResult.succ();
    }

    public void sendSettleToVhomeByIndexOrderSn(String indexOrderSn){
        QueryWrapper<SapInvestIndexOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SapInvestIndexOrderEntity::getZsyh,indexOrderSn).last("limit 1");
        List<SapInvestIndexOrderEntity> sapInvestIndexOrderEntities = sapInvestIndexOrderService.list(queryWrapper);
        sendSettleToVhome(sapInvestIndexOrderEntities);
    }

    private void sendSettleToVhome(List<SapInvestIndexOrderEntity> sapInvestIndexOrderEntities) {
        List<VhomeAcceptanceParamsDto> vhomeAcceptanceParamsDtos = Lists.newArrayList();
        List<SapInvestIndexOrderEntity> settleIndexOrder = sapInvestIndexOrderEntities.stream().filter(item -> "X".equalsIgnoreCase(item.getJszt())).collect(Collectors.toList());
        Map<String, List<SapInvestIndexOrderEntity>> zsyhG = settleIndexOrder.stream().collect(Collectors.groupingBy(SapInvestIndexOrderEntity::getZsyh));
        for (Map.Entry<String, List<SapInvestIndexOrderEntity>> stringListEntry : zsyhG.entrySet()) {
            String indexOrderSn = stringListEntry.getKey();
            List<SapInvestIndexOrderEntity> indexOrderEntities = stringListEntry.getValue();
            Map<String, List<SapInvestIndexOrderEntity>> ebelnG = indexOrderEntities.stream().collect(Collectors.groupingBy(SapInvestIndexOrderEntity::getEbeln));
            for (Map.Entry<String, List<SapInvestIndexOrderEntity>> listEntry : ebelnG.entrySet()) {
                String ebeln = listEntry.getKey();
                QueryWrapper<SapInvestOrderEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(SapInvestOrderEntity::getEbeln,ebeln);
                List<SapInvestOrderEntity> sapInvestOrderEntities = sapInvestOrderService.list(queryWrapper);
                if(CollectionUtil.isEmpty(sapInvestOrderEntities)){
                    log.info("【sendSettleToVhome】根据采购凭证号未获取到采购订单，数据异常。");
                    continue;
                }
                String contractCode = sapInvestOrderEntities.get(0).getContractCode();
                QueryWrapper<SettleCheckFormExt> checkExtQuery = new QueryWrapper<>();
                checkExtQuery.lambda().eq(SettleCheckFormExt::getContractSerial,contractCode);
                SettleCheckFormExt settleCheckFormExt = settleCheckFormExtService.getOne(checkExtQuery);

                List<SapInvestIndexOrderEntity> ebelnIndexs = listEntry.getValue();
                VhomeAcceptanceParamsDto vhomeAcceptanceParamsDto = new VhomeAcceptanceParamsDto();
                vhomeAcceptanceParamsDto.setZsyh(indexOrderSn);
                vhomeAcceptanceParamsDto.setBudat(ebelnIndexs.get(0).getBudat());
                vhomeAcceptanceParamsDto.setEbeln(ebeln);
                BigDecimal dmbtrHz = ebelnIndexs.stream().map(item->Convert.toBigDecimal(item.getDmbtrHz())).reduce(BigDecimal.ZERO,BigDecimal::add);
                vhomeAcceptanceParamsDto.setDmbtrHz(dmbtrHz.toString());
                vhomeAcceptanceParamsDto.setBuScale(dmbtrHz.toString());
                //马杰：商城生成的订单号：合同号+流水的那个
                vhomeAcceptanceParamsDto.setCheckApplyNo(settleCheckFormExt.getCheckNumber());
                vhomeAcceptanceParamsDtos.add(vhomeAcceptanceParamsDto);
            }
        }
        if(CollectionUtil.isEmpty(vhomeAcceptanceParamsDtos)){
            log.info("【VOYAH sendSettleToVhome】未包含有效已结算的索引单，不发送vhome");
            return;
        }
        /**
         * 岚图马杰，岚图黄富峰，李辉共同确认，循环调用外部接口
         */
        for (VhomeAcceptanceParamsDto vhomeAcceptanceParamsDto : vhomeAcceptanceParamsDtos) {
            log.info("【VOYAH sendSettleToVhome】发送结算信息到岚图OA,参数：" + JSON.toJSONString(vhomeAcceptanceParamsDto));
            log.info("【VOYAH sendSettleToVhome】发送结算信息到岚图OA,URL：" + voyahConfig.getSendCheckUrl());
            String response = post(voyahConfig.getSendSettleUrl(),JSON.toJSONString(vhomeAcceptanceParamsDto));
            log.info("【VOYAH sendSettleToVhome】发送结算信息到岚图OA,返回：" + response);
        }

    }

    public void deleteSapInvestIndexOrder(DFGSAP157ParamsDto dfgsap157ParamsDto){
        String params = buildContent(DfgSapInterfaceCode.DFG_SAP_157, DfgSapInterfaceCode.DFG_SAP_157_FUNC, dfgsap157ParamsDto);
        log.info("call deleteSapInvestIndexOrder params:" + params);
        String reponse = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
        log.info("deleteSapInvestIndexOrder return response :" + reponse);
        //删除本地索引单
        sapInvestIndexOrderService.removeById(dfgsap157ParamsDto.getId());
    }

    public void updateSapInvestIndexOrder(Long id,String loekz){
        SapInvestOrderEntity sapInvestOrderEntity = sapInvestOrderService.getById(id);
        DFGSAP154ParamsDto dfgsap154ParamsDto = new DFGSAP154ParamsDto();
        dfgsap154ParamsDto.setEbeln(sapInvestOrderEntity.getEbeln());
        dfgsap154ParamsDto.setEbelp(sapInvestOrderEntity.getEbelp());
        dfgsap154ParamsDto.setLoekz(loekz);
        String params = buildContent(DfgSapInterfaceCode.DFG_SAP_154, DfgSapInterfaceCode.DFG_SAP_154_FUNC, dfgsap154ParamsDto);
        log.info("call updateSapInvestIndexOrder params:" + params);
        String reponse = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
        log.info("updateSapInvestIndexOrder return response :" + reponse);
    }

    public ServiceResult queryDfgSapInvestOrderInfo(String contractSerial) {
        QueryWrapper<SettleCheckFormExt> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleCheckFormExt::getContractSerial,contractSerial);
        SettleCheckFormExt settleCheckFormExt = settleCheckFormExtService.getOne(queryWrapper);
        if(Objects.isNull(settleCheckFormExt)){
            return ServiceResult.error("未查询到该合同流水号！");
        }

        //取验收未税金额
        List<SettleCheckFormDetail> detailList = settleCheckDetailService.getDetailByFormId(settleCheckFormExt.getCheckFormId());
        BigDecimal totalPriceNaked = detailList.stream().map(SettleCheckFormDetail::getGoodsTotalPriceTax).reduce(BigDecimal.ZERO,BigDecimal::add);

        //合同号
        String contractCode = StrUtil.sub(contractSerial, 0, contractSerial.length() - 4);
        List<SapContractRealEntity> contractList = yphStandardClassService.getSapContractAll();
        List<SapContractRealEntity> sapContractRealEntities1 = contractList.stream()
                .filter(item -> item.getContractCode().equals(contractCode)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(sapContractRealEntities1)) {
            return ServiceResult.error("未查询到合同信息！");
        }

        String sapCompanyCode = sapContractRealEntities1.get(0).getSapCompanyCode();
        DFGSAP153ParamsDto dfgsap153ParamsDto = new DFGSAP153ParamsDto();
        dfgsap153ParamsDto.setLifnr(dfgSapConfig.getDfgSapSupplier());
        dfgsap153ParamsDto.setBukrs(sapCompanyCode);
        dfgsap153ParamsDto.setAblad(contractSerial);
        dfgsap153ParamsDto.setPreis(Convert.toStr(totalPriceNaked));
        //因为投资类订单，只有岚图有业务，所以这里直接更改 DFG_SAP_153 -> DFG_SAP_171
        String params = buildContent(DfgSapInterfaceCode.DFG_SAP_171, DfgSapInterfaceCode.DFG_SAP_153_FUNC, dfgsap153ParamsDto);
        log.info("call queryDfgSapInvestOrderInfo params:" + params);
        String response = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
        log.info("queryDfgSapInvestOrderInfo return response :" + response);
        Document document = XmlUtil.readXML(response);
        String value = getXpathString("return", document);
        List<Map<String, String>> responseMap = convSapSBCode(value);
        List<SapInvestOrderEntity> sapInvestOrderEntities = BeanUtil.copyToList(responseMap,SapInvestOrderEntity.class,new CopyOptions().setIgnoreCase(true));

        //去除失败的
        sapInvestOrderEntities.removeIf(item->!Constant.S.equalsIgnoreCase(item.getZtype()));

        if(CollectionUtil.isEmpty(sapInvestOrderEntities)){
            log.info("未获取到采购订单：合同号：" + contractSerial);
            return ServiceResult.error("根据合同号未查询到已创建的采购凭证！请联系SAP业务人员创建后在重试！");
        }

        for (SapInvestOrderEntity sapInvestOrderEntity : sapInvestOrderEntities) {
            sapInvestOrderEntity.setContractCode(contractSerial);
        }
        sapInvestOrderService.saveBatch(sapInvestOrderEntities);

        //投资类订单直接去收货，因为投资订单已经在商城收完货
        callSapInvestDelivery(sapInvestOrderEntities);
        return ServiceResult.succ();
    }

    private void callSapInvestDelivery(List<SapInvestOrderEntity> sapInvestOrderEntities) {
        //删除失败的消息
        sapInvestOrderEntities.removeIf(item -> !Constant.S.equalsIgnoreCase(item.getZtype()));
        List<DFGSAP155ParamsDto> paramsDtos = Lists.newArrayList();
        //直接根据数据去收货
        for (SapInvestOrderEntity sapInvestOrderEntity : sapInvestOrderEntities) {
            DFGSAP155ParamsDto dfgsap155ParamsDto = new DFGSAP155ParamsDto();
            dfgsap155ParamsDto.setEbeln(sapInvestOrderEntity.getEbeln());
            dfgsap155ParamsDto.setEbelp(sapInvestOrderEntity.getEbelp());
            dfgsap155ParamsDto.setBldat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            dfgsap155ParamsDto.setBudat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            dfgsap155ParamsDto.setErfmg(sapInvestOrderEntity.getMenge());
            paramsDtos.add(dfgsap155ParamsDto);
        }
        String params = buildContent(DfgSapInterfaceCode.DFG_SAP_155, DfgSapInterfaceCode.DFG_SAP_155_FUNC, paramsDtos);

        log.info("call callSapInvestDelivery params:" + params);
        String response = HttpUtil.post(dfgSapConfig.getDfgEsbDfmall(), params);
        log.info("callSapInvestDelivery return response :" + response);
        Document document = XmlUtil.readXML(response);
        String value = getXpathString("return", document);

        List<Map<String, String>> responseMap = convSapSBCode(value);
        List<SapInvestDeliveryEntity> sapInvestDeliveryEntities = BeanUtil.copyToList(responseMap, SapInvestDeliveryEntity.class, new CopyOptions().setIgnoreCase(true));

        List<SapInvestOrderEntity> updateInvestOrder = Lists.newArrayList();

        for (SapInvestDeliveryEntity sapInvestDeliveryEntity : sapInvestDeliveryEntities) {
            sapInvestDeliveryEntity.setContractCode(sapInvestOrderEntities.get(0).getContractCode());

            String ebeln = sapInvestDeliveryEntity.getEbeln();
            String ebelp = sapInvestDeliveryEntity.getEbelp();
            String zret = sapInvestDeliveryEntity.getZret();
            if (Constant.S.equalsIgnoreCase(zret)) {
                List<SapInvestOrderEntity> sapInvestOrderEntities1 = sapInvestOrderEntities.stream()
                        .filter(item -> item.getEbeln().equals(ebeln))
                        .filter(item -> item.getEbelp().equals(ebelp))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(sapInvestOrderEntities1)) {
                    SapInvestOrderEntity sapInvestOrderEntity = new SapInvestOrderEntity();
                    sapInvestOrderEntity.setId(sapInvestOrderEntities1.get(0).getId());
                    sapInvestOrderEntity.setDeliveryState(1);
                    updateInvestOrder.add(sapInvestOrderEntity);
                }
            }
        }
        sapInvestOrderService.updateBatchById(updateInvestOrder);
        sapInvestDeliveryService.saveBatch(sapInvestDeliveryEntities);
    }

    private static String buildContent(String tradeCode,String func,Object params){
        DfgSapBaseParamDto nodesBaseParam = buildBaseParam(tradeCode);
        StringBuffer head = buildDfgSapWsHead(JSON.toJSONString(nodesBaseParam));
        StringBuffer tail = buildBizParams(func,params);
        return head.append(tail).toString();
    }

    private static String buildContent(String tradeCode, String func, Object params, Boolean isSap){
        DfgSapBaseParamDto nodesBaseParam = buildBaseParam(tradeCode);
        StringBuffer head = buildDfgSapWsHead(JSON.toJSONString(nodesBaseParam));
        StringBuffer tail = buildBizParamsNoSapT1(func,params);
        return head.append(tail).toString();
    }


    private static StringBuffer buildBizParamsNoSapT1(String func,Object params){
        StringBuffer nodes = new StringBuffer();
        nodes.append("<bizParams>");
        nodes.append("{");
        nodes.append("\""+func+"\":");
//        nodes.append(": { \"SapT1\":");
        String bizParams = JSON.toJSONString(params);
        nodes.append(bizParams);
        nodes.append(" }</bizParams> </ws:sendMessage>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>");
        return nodes;
    }

    private static StringBuffer buildBizParams(String func,Object params){
        StringBuffer nodes = new StringBuffer();
        nodes.append("<bizParams>");
        nodes.append("{");
        nodes.append("\""+func+"\"");
        nodes.append(": { \"SapT1\":");
        String bizParams = JSON.toJSONString(params);
        nodes.append(bizParams);
        nodes.append(" }}</bizParams> </ws:sendMessage>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>");
        return nodes;
    }

    /**
     * 转换sap返回结构
     * @param reponse
     * @return
     */
    private List<Map<String, String>> convSapSBCode(String reponse){
        Map<String, List<Map<String, String>>> map = JSONUtil.toBean(reponse, Map.class);
        List<Map<String, String>> sapList = Lists.newArrayList();
        Map<String, String> sapMap;
        if (map.get("SapT1") instanceof List) {
            sapList = map.get("SapT1");
        } else {
            sapMap = (Map<String, String>) map.get("SapT1");
            sapList.add(sapMap);
        }
        return sapList;
    }

    public List<VoyahStoreInfoDto> queryVoyahStoreInfos() {
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("requestId", IdUtil.simpleUUID());
        String response = post(voyahConfig.getVoyahStoreInfoUrl(), JSON.toJSONString(paramMap));
        Map<String, Object> responseMap = JSONUtil.toBean(response, Map.class);
        List<VoyahStoreInfoDto> storeInfoDtos = Lists.newArrayList();
        if ("S".equals(Convert.toStr(responseMap.get("code")))) {
            JSONArray data = JSONUtil.parseArray(responseMap.get("data"));
            storeInfoDtos = JSONUtil.toList(data, VoyahStoreInfoDto.class);
        }
        return storeInfoDtos;
    }

  /**
   * 岚图使用的http请求方法
   *
   * @param url
   * @param params
   * @return
   */
  public static String post(String url, String params) {
        String result = "";
        // 创建一个post请求
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/json");
    // 设置参数
    post.setEntity(new StringEntity(params, "utf-8"));
    // todo 上线前要去掉header里面内容
    post.setHeader("Authorization", "Basic YWRtaW46MTIzNDU2");
    try (CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = httpClient.execute(post)) {

      int code = response.getStatusLine().getStatusCode();
      // 获取body
      result = EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
      e.printStackTrace();
        }
        return result;
  }
}
