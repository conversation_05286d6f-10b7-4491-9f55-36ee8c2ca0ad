package com.ly.yph.api.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2022/02/10 15:24:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "shop_supplier", autoResultMap = true)
@ApiModel("供应商基础信息表")
public class ShopSupplier extends BaseEntity {
    private static final long serialVersionUID = -267349308253681986L;

    /**
     * 系统相关字段
     */
    @TableId(value = "supplier_id", type = IdType.AUTO)
    @ApiModelProperty("主键id")
    private Long supplierId;

    @ApiModelProperty("组织id")
    private Long organizationId;

    @ApiModelProperty("订单包邮条件")
    private BigDecimal freeShippingCondition;

    @ApiModelProperty("供应商认证标签")
    private String authLabel;

    @ApiModelProperty("审批状态[0:待审批;1:已通过;2:已拒绝]")
    private Integer approveState;

    @ApiModelProperty("审批完成时间")
    private Date approveTime;

    @ApiModelProperty("数据来源")
    private String dataSource;

    @ApiModelProperty("租户编号")
    private Long tenantId;


    /**
     * 基本信息字段
     */
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商类别 0:电商平台;1:独立供应商")
    private Integer supplierType;

    @ApiModelProperty("意向合作模式 0 定向服务商 1 平台 2 全部")
    private Integer cooperationModel;

    @ApiModelProperty("认证条件")
    private String authenticationConditionUrl;

    @ApiModelProperty("是否平台 0 否 1 是")
    private Integer isPlatform;

    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("供应商全称")
    private String supplierFullName;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("纳税人识别号")
    private String taxpayerIdentification;

    @ApiModelProperty("经营范围")
    private String businessScope;

    @ApiModelProperty("供应商简介")
    private String supplierSummary;

    @ApiModelProperty("省")
    private String supplierProvince;

    @ApiModelProperty("市")
    private String supplierCity;

    @ApiModelProperty("区")
    private String supplierDistrict;

    @ApiModelProperty("街道")
    private String supplierStreet;

    @ApiModelProperty("通讯地址")
    private String supplierAddress;


    /**
     * 企业法人营业信息字段
     */
    @ApiModelProperty("营业执照注册号")
    private String businessLicenseNo;

    @ApiModelProperty("经营开始时间")
    private Date fromTime;

    @ApiModelProperty("法定代表人")
    private String legalRepresentative;

    @ApiModelProperty("税务登记证号")
    private String taxRegistrationCode;

    @ApiModelProperty("企业注册地址")
    private String registerAddress;

    @ApiModelProperty("注册资本(万)")
    private String supplierRegisteredCapital;

    @ApiModelProperty("三证合一")
    private String businessLicenseUrl;


    /**
     * 管理员信息字段
     */
    @ApiModelProperty("管理员账号")
    private String adminUsername;

    @ApiModelProperty("管理员授权证明")
    private String adminAuthorizationUrl;

    /**
     * 业绩证明字段
     */
    @ApiModelProperty("历史服务企业")
    private String serviceCompany;

    @ApiModelProperty("业绩证明(zip)")
    private String serviceProveUrl;


    /**
     * 结算信息字段
     */
    @ApiModelProperty("开户行")
    private String invoiceBank;

    @ApiModelProperty("银行账号")
    private String invoiceBankAccount;

    @ApiModelProperty("发票抬头")
    private String invoiceTitle;

    @ApiModelProperty("开户联行号")
    private String accountJointBankNumber;

    @ApiModelProperty("开票电话")
    private String invoicePhone;

    @ApiModelProperty("开票地址")
    private String invoiceAddress;

    @ApiModelProperty("开票类型 0:增值税专用发票;1:增值税普通发票")
    private Integer invoiceType;

    @ApiModelProperty("账期开始节点")
    private Integer settlementType;

    @ApiModelProperty("结算账期(天)")
    private Integer settlementPeriod;

    /**
     * 联系人信息字段
     */
    //	[{"name":"张三","phone":"***********","email":"<EMAIL>"}]
    @ApiModelProperty("寻源联系人,json格式储存联系人、电话、邮箱")
    private String searchSourcePeople;

    @ApiModelProperty("售前联系人,json格式储存联系人、电话、邮箱")
    private String preSalesPeople;

    @ApiModelProperty("售后联系人,json格式储存联系人、电话、邮箱")
    private String afterSalesPeople;

    @ApiModelProperty("订单联系人,json格式储存联系人、联系方式、邮箱")
    private String orderPeople;

    @ApiModelProperty("结算联系人,json格式储存联系人、联系方式、邮箱")
    private String clearingPeople;

    @ApiModelProperty("商品上新通知邮箱")
    private String goodsUpEmail;

    @ApiModelProperty("上新联系人,json格式储存联系人、邮箱")
    private String shelvesPeople;

    /**
     * 供应链、系统管理员配置相关字段
     */
    @ApiModelProperty("商品上架是否审核 0:否;1:是")
    private Integer isAudited;

    @ApiModelProperty("是否接收订单邮件提醒 1:是 0:否")
    private Integer canReceiveEmail;

    @ApiModelProperty("是否参与电商上新 0:否;1:是")
    private Integer isUpNew;

    @ApiModelProperty("是否参与寻源比价 0:否;1:是")
    private Integer isSeekPrice;

    @ApiModelProperty("是否加入黑名单 0:否;1:是")
    private Integer isBlacklist;

    @ApiModelProperty("是否参与竞价 0:否;1:是")
    private Integer isBidding;

    @ApiModelProperty("上架租户配置 多个逗号分隔 默认租户1")
    private String tenantIds;
    /**
     * 售后政策
     */
    @ApiModelProperty("服务时间段(如工作日/自然日及具体服务时间)")
    @TableField(exist = false)
    private String servicePeriod;

    @ApiModelProperty("支持售后原因")
    @TableField(exist = false)
    private String afterSalesReasons;

    @ApiModelProperty("退换货政策")
    @TableField(exist = false)
    private String returnAndExchangePolicy;

    @ApiModelProperty("退换货方式")
    @TableField(exist = false)
    private String returnAndExchangeMethod;

    @ApiModelProperty("运费要求")
    @TableField(exist = false)
    private String freightClaim;

    /**
     * 经办人信息
     */
    @ApiModelProperty("经办人姓名")
    private String operatorName;

    @ApiModelProperty("经办人身份证")
    private String operatorIdCard;

    @ApiModelProperty("经办人手机号")
    private String operatorPhone;

    @ApiModelProperty("经办人邮箱")
    private String operatorEmail;

    @ApiModelProperty("'经办人在生态聚合中心id'")
    private String  eAccountId;

    @ApiModelProperty("'供应商在生态中心组织id'")
    private String  eOrganizeId;

}
