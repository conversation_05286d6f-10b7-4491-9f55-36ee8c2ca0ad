package com.ly.yph.api.orderlifecycle.factory;

import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailForBillDto;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailForDeliveryDto;
import com.ly.yph.api.orderlifecycle.dto.UpdatePurchaseOrderInfoForConfirmDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;

import java.util.List;

/**
 * 订单明细生命周期策略
 */
public interface OrderLifeCycleStrategy {

    /**
     * 预订单
     *
     * @param purchaseNumber
     */
    void savePurchaseOrderInfoForPreOrder(String purchaseNumber);

    /**
     * 审批
     */
    void updatePurchaseOrderInfoForApprove(UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto);

    /**
     * 发货
     */
    void updatePurchaseOrderInfoForDelivery(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto);

    /**
     * 妥投
     */
    void updatePurchaseOrderInfoForProperDelivery(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto);

    /**
     * 收货
     */
    void updatePurchaseOrderInfoForReceive(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto);

    /**
     * 验收
     */
    void updatePurchaseOrderInfoForCheck(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto);

    /**
     * 出账
     */
     void updatePurchaseOrderInfoForBillOut(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 对账
     *
     */
    void updatePurchaseOrderInfoForBillReconciliation(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 删除账单明细
     *
     */
    void updatePurchaseOrderInfoForDeleteBillDetail(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 开票申请
     */
    void updatePurchaseOrderInfoForApplyInvoice(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 开票审批
     * @param updateOrderDetailForBillDto
     */
    void updatePurchaseOrderInfoForInvoiceApprove(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 回款
     */
    void updatePurchaseOrderInfoForInvoiceRepayment(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 发票红冲、废弃 (来源发票一定是开票确认或者已收票)
     */
    void updatePurchaseOrderInfoForInvoiceOffset(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 售后
     */
    void updatePurchaseOrderInfoForAfterSale(UpdateOrderDetailForBillDto updateOrderDetailForBillDto);

    /**
     * 独立供应商 包裹删除 更新生命周期
     * @param updateOrderDetailForDeliveryDto
     */
    void updatePurchaseOrderInfoForDeliveryDelete(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto);

    /**
     * 供应商开票
     * @param supplierInvoiceDelDtos
     */
    void updatePurchaseOrderInfoForSupplierInvoice(List<SupplierInvoiceDelDto> supplierInvoiceDelDtos);
}
