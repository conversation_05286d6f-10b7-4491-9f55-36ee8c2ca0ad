package com.ly.yph.api.settlement.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.yph.core.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplierInvoiceDetailPostage extends BaseEntity {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    private Long invoiceId;

    private Long postageDetailId;

    private BigDecimal postage;

}
