package com.ly.yph.api.order.dto;

import com.ly.yph.api.companystore.dto.CompanyStoreOrderAddressSaveDto;
import com.ly.yph.api.order.dto.srm.PurchaseCustomLySaveDto;
import com.ly.yph.api.system.entity.ShopAddress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 购买顺序保存数据对象
 *
 * <AUTHOR>
 * @Date 2022/4/15 16:11
 **/
@Data
@ApiModel("采购订单保存对象")
public class PurchaseOrderSaveDto {

    /**
     * 采购订单ID
     */
    @ApiModelProperty(value = "采购订单ID", dataType = "String")
    private String purchaseId;
    /**
     * 采购单名字
     */
    @ApiModelProperty(value = "采购单名字", dataType = "String")
    private String purchaseName;
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号", dataType = "String")
    private String purchaseNumber;

    /**
     * 采购单状态
     */
    @ApiModelProperty(value = "采购单状态 ", dataType = "Integer")
    private Integer purchaseState;

    /**
     * 采购单商品总价
     */
    @ApiModelProperty(value = "采购单商品总价", dataType = "BigDecimal")
    private BigDecimal purchaseGoodsPrice;

    /**
     * 采购单运费总价
     */
    @ApiModelProperty(value = "采购单运费总价", dataType = "BigDecimal")
    private BigDecimal purchaseFreightPrice;

    /**
     * 采购单总金额
     */
    @ApiModelProperty(value = "采购单总金额", dataType = "BigDecimal")
    private BigDecimal purchaseTotalPrice;

    /**
     * 采购单所属活动ID
     */
    @ApiModelProperty(value = "采购单所属活动ID", dataType = "Long")
    private Long activityId;

    /**
     * 采购单支付积分
     */
    @ApiModelProperty(value = "采购单支付积分", dataType = "BigDecimal")
    private BigDecimal purchasePayIntegral;

    /**
     * 支付金额(含税,包括但不限于微信支付宝)
     */
    @ApiModelProperty(value = "支付金额(含税,包括但不限于微信支付宝)", dataType = "BigDecimal")
    private BigDecimal purchasePayMoney;

    /**
     * 微信支付宝或其它第三方支付单号
     */
    @ApiModelProperty(value = "微信支付宝或其它第三方支付单号", dataType = "String")
    private String payTraceNumber;

    /**
     * 支付方式 W:微信 Z:支付宝
     */
    @ApiModelProperty(value = "支付方式 W:微信 Z:支付宝", dataType = "String")
    private String payType;

    /**
     * 企业编码
     */
    @ApiModelProperty(value = "企业编码", dataType = "String")
    @NotBlank(message = "请先加入企业")
    private String companyCode;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", dataType = "String")
    @NotBlank(message = "请先加入企业")
    private String companyName;

    /**
     * 申请部门名称
     */
    @ApiModelProperty(value = "申请部门名称", dataType = "String")
    private String applyDeptId;

    /**
     * 申请部门名称
     */
    @ApiModelProperty(value = "申请部门名称", dataType = "String")
    private String applyDeptName;

    /**
     * 申请人ID
     */
    @ApiModelProperty(value = "申请人ID", dataType = "String")
    @NotBlank(message = "未找到申请人信息")
    private String applyUserId;
    /**
     * 运费类型  0 积分 1 现金
     */
    @ApiModelProperty(value = "运费类型  0 积分 1 现金", dataType = "Integer")
    private Integer freightType = 0;


    /**
     * 申请人名称
     */
    @ApiModelProperty(value = "申请人名称", dataType = "String")
    @NotBlank(message = "未找到申请人信息")
    private String applyUserName;

    /**
     * 申请人工号
     */
    @ApiModelProperty(value = "申请人工号", dataType = "String")
    @NotBlank(message = "未找到申请人信息")
    private String applyEmpCode;

    /**
     * 申请人联系电话
     */
    @ApiModelProperty(value = "申请人联系电话", dataType = "String")
    @NotBlank(message = "请在用户信息填写您的电话")
    private String applyUserPhone;

    /**
     * 预算ID
     */
    @ApiModelProperty(value = "预算ID", dataType = "Long")
    private Long budgetId;

    /**
     * 预算申请号
     */
    @ApiModelProperty(value = "预算申请号", dataType = "String")
    private String budgetApplyCode;

    /**
     * 预算号
     */
    @ApiModelProperty(value = "预算号", dataType = "String")
    private String budgetCode;
    /**
     * 子预算号
     */
    @ApiModelProperty(value = "子预算号", dataType = "java.lang.String")
    private String subBudgetCode;
    /**
     * 预算类型
     */
    @ApiModelProperty(value = "预算类型", dataType = "String")
    private String budgetType;

    /**
     * 开票信息ID
     */
    @ApiModelProperty(value = "开票信息ID", dataType = "String")
    @NotBlank(message = "开票信息不能为空")
    private String invoiceId;

    /**
     * 开票类型
     */
    @ApiModelProperty(value = "开票类型", dataType = "Integer")
    private Integer invoiceType;

    /**
     * 开票主体
     */
    @ApiModelProperty(value = "开票主体", dataType = "String")
    private String invoiceCompanyName;

    /**
     * 订单收货地址ID
     */
    @ApiModelProperty(value = "订单收货地址ID", dataType = "String")
    @NotBlank(message = "收货地址不能为空")
    private String addressId;

    /**
     * 是否紧急采购单 0:常规采购 1:紧急采购
     */
    @ApiModelProperty(value = "是否紧急采购单 0:常规采购 1:紧急采购", dataType = "Integer")
    private Integer isUrgent;

    /**
     * 采购说明
     */
    @ApiModelProperty(value = "采购说明", dataType = "String")
    private String purchaseComment;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", dataType = "String")
    private String remark;

    /**
     * 第三方关联单号 如SAP单号等
     */
    @ApiModelProperty(value = "第三方关联单号 如SAP单号等", dataType = "String")
    private String otherRelationNumber;

    /**
     * 购物车ID
     */
    @ApiModelProperty(value = "购物车ID，提交采购单必填", dataType = "array")
    private String cartIds;

    @ApiModelProperty("验收人id")
    private Long accepterId;

    @ApiModelProperty("验收人工号")
    private String accepterCode;

    @ApiModelProperty("验收人名称")
    private String accepterName;

    @ApiModelProperty("验收人组织路径")
    private String accepterOrgPath;
    /**
     * 收货人账号
     */
    @ApiModelProperty(value = "收货人账号", dataType = "java.lang.String")
    private String addressUser;

    /**
     * 采购经理
     */
    @ApiModelProperty(value = "采购经理", dataType = "java.lang.String" )
    private String purchaseManagerNumber;
    /**
     * 采购部门总监
     */
    @ApiModelProperty(value = "采购部门总监", dataType = "java.lang.String" )
    private String purchaseDeptDirectors;
    /**
     * SRM采购申请需求人
     */
    @ApiModelProperty(value = "SRM采购申请需求人", dataType = "java.lang.String" )
    private String srmDemander;
    /**
     * 需求部门总监
     */
    @ApiModelProperty(value = "需求部门总监", dataType = "java.lang.String" )
    private String demandDeptDirectors;
    /**
     * 采购需求描述
     */
    @ApiModelProperty(value = "采购需求描述", dataType = "java.lang.String")
    private String demandDesc;
    /**
     * SRM项目编码
     */
    @ApiModelProperty(value = "SRM项目编码", dataType = "java.lang.String")
    private String srmProjectCode;
    /**
     * SRM项目名称
     */
    @ApiModelProperty(value = "SRM项目名称", dataType = "java.lang.String")
    private String srmProjectName;
    /**
     * SRM项目经理
     */
    @ApiModelProperty(value = "SRM项目经理", dataType = "java.lang.String")
    private String srmProjectManager;
    /**
     * 采购包编号
     */
    @ApiModelProperty(value = "采购包编号", dataType = "java.lang.String")
    private String pkgCode;
    /**
     * 采购包名称
     */
    @ApiModelProperty(value = "采购包名称", dataType = "java.lang.String")
    private String pkgName;


    @ApiModelProperty("是否SRM合同自动创建的订单0否 1是")
    private Integer srmAutoCreateOrder=0;
    @ApiModelProperty(value = "附件地址，多个附件地址逗号分开")
    private String attachmentUrl;

    @ApiModelProperty("是否是工会(0:否 1:是)")
    private Integer tradeUnionFlag;

    @ApiModelProperty("srm采购申请单号")
    private String srmApplyId;

    @ApiModelProperty("南方是否需要发货 0:无需收发货 1:要收发货")
    private Integer dfsNeedDelivery = 1;

    @ApiModelProperty("推进担当")
    private String promoteCommitment;

    @ApiModelProperty("预算名称")
    private String budgetName;
    @ApiModelProperty("费用承担科室编码")
    private String bearDepartmentCode;

    @ApiModelProperty("费用承担科室名称")
    private String  bearDepartmentName;

    @ApiModelProperty("订单标签")
    private Integer orderLabel;

    @ApiModelProperty("附件数据")
    private List<ShopPurchaseAttachmentDto> attachmentList;

    @ApiModelProperty("东本投掷类订单附加信息")
    private ShopPurchaseHondaInvestDto hondaInvestDto;

    /**
     * 单个订单备注  电商code:备注
     */
    @ApiModelProperty("单个订单备注")
    private List<OrderRemarkDto> orderRemark;

    /**
     * 采购订单商品明细
     */
    @ApiModelProperty(value = "采购订单商品明细", dataType = "array")
    private List<PurchaseOrderDetailSaveDto> purchaseOrderDetailList;

    @ApiModelProperty(value = "浮动价格商品明细", dataType = "array")
    private List<FloatPriceGoodsDto> floatPriceGoodsList;

    @ApiModelProperty("采购订单联友定制参数")
    private PurchaseCustomLySaveDto customLySaveDto;

    /**
     * dms临时收货地址
     */
    private ShopAddress DmsShopAddress;

    @ApiModelProperty("dms订单标记 0：正常的东风商城订单 1：dms下单")
    private Integer dmsOrderFlag = 0;

    @ApiModelProperty(value = "1：（集团岚图华神）存货订单 2：（集团岚图华神）费用订单 3：乘用车SAP入库订单 5:岚图DMS定单" )
    private Integer sapOrderType;

    @ApiModelProperty("是否门店  1:是  0:否")
    private Integer isStore;

    @ApiModelProperty("采购组织")
    private String purchaseOrgName;

    @ApiModelProperty("是否企配仓  1:是  0:否 前端不传 后端判断")
    private Integer isCompanyStore = 0;

    @ApiModelProperty("企配仓替换地址等所有信息 前端不用传")
    private CompanyStoreOrderAddressSaveDto companyStoreOrderAddressSaveDto;
    @ApiModelProperty("订单采购类别")
    private String srmPurchaseType;
    @ApiModelProperty("订单类型")
    private String srmOrderType;
    @ApiModelProperty("最晚交付日期")
    private String latestDeliveryDate;
    @ApiModelProperty("是否商务领域")
    private String businessField;
    @ApiModelProperty("销售品类")
    private String categoryOptions;
    @ApiModelProperty("自动开票标记 0:不自动 1:自动'")
    private Long autoInvoiceFlag;
    @ApiModelProperty("多预算")
    private List<MultipleBudgetDto> multipleBudget;

    /**
     * 智能柜订单标识，0：非智能柜订单，1：智能柜订单
     */
    private Integer dhecSmartOrderFlag = 0;
    @ApiModelProperty("虚拟商品充值的账号")
    private String rechargeAccount;

}
