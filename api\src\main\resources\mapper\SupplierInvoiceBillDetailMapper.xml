<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.settlement.supplier.mapper.SupplierInvoiceBillDetailMapper">

    <select id="getSupplierSettleInvoiceDetailDto"
            resultType="com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDetailDto">
        select
        ssbd.detail_id as billDetailId,
        ssbd.checked_num as invoiceNum,
        ssbd.unit_price_naked as goodsUnitPriceNaked,
        ssbd.unit_price_tax as goodsUnitPriceTax,
        ssbd.total_price_naked as goodsTotalPriceNaked,
        ssbd.total_price_tax as goodsTotalPriceTax,
        ssbd.order_number as orderNumber,
        ssbd.goods_sku as goodsSku,
        sblc.id as lifeCycleId
        from settle_shop_bill_detail ssbd
        left join settle_bill_pool sbp on sbp.id = ssbd.settle_bill_pool_id
        left join settle_bill_life_cycle sblc on sblc.settle_bill_pool_id=sbp.id
        where ssbd.bill_id = #{billId}
        and ssbd.is_enable = 1 and ssbd.reconciliation_status in (0,1,2,3)
        <if test="list !=null and list.size()>0">
            and ssbd.order_number in
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="detailIdList !=null and detailIdList.size()>0">
            and ssbd.detail_id in
            <foreach collection="detailIdList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateSupplierBillDetailForInvoiceApply">
        <foreach collection="list" item="item" separator=";">
            update settle_shop_bill_detail
            set reconciliation_status=#{item.reconciliationStatus},
            invoicable_quantity=#{item.invoicableQuantity},
            invoiced_quantity=#{item.invoicedQuantity}
            where detail_id =#{item.detailId}
        </foreach>
    </update>

    <update id="updateSupplierBillLifeCycleForInvoiceApply">
        <foreach collection="list" item="item" separator=";">
            update settle_bill_life_cycle
            set supplier_invoice_money=#{item.supplierInvoiceMoney},
            supplier_invoicing_num=#{item.supplierInvoicingNum},
            supplier_invoicing_time=#{item.supplierInvoicingTime}
            where id =#{item.id}
        </foreach>
    </update>

    <update id="updateBillDetailForSupplierInvoiceDel">
        <foreach collection="list" item="item" separator=";">
            update settle_shop_bill_detail
            set reconciliation_status =2,
            invoicable_quantity = checked_num,
            invoiced_quantity =0
            where detail_id =#{item}
        </foreach>
    </update>

    <update id="updateBillLifecycleForInvoiceDel">
        <foreach collection="list" item="item" separator=";">
            update settle_bill_life_cycle
            set supplier_invoicing_num =0,
            supplier_invoice_money =0,
            supplier_invoicing_time =null
            where id =#{item}
        </foreach>
    </update>

    <update id="updateBillDetailForInvoicePass">
        <foreach collection="list" item="item" separator=";">
            update settle_shop_bill_detail set reconciliation_status = 6 where detail_id =#{item}
        </foreach>
    </update>

    <update id="updateBillLifecycleForInvoicePass">
        <foreach collection="list" item="item" separator=";">
            update settle_bill_life_cycle set supplier_invoicing_status =6 where id =#{item}
        </foreach>
    </update>

    <select id="getSupplierSettleList"
            resultType="com.ly.yph.api.settlement.supplier.dto.SupplerInvoiceExcelDto">
        select ssbd.check_year,
               ssbd.check_month,
               ssbd.purchase_number,
               ssbd.order_number,
               ssbd.supplier_order_number,
               ssbd.goods_sku,
               ssbd.confirm_num,
               ssbd.apply_user_name,
               sibd.invoice_num,
               sibd.goods_unit_price_naked,
               sibd.goods_unit_price_tax,
               sibd.goods_total_price_naked,
               sibd.goods_total_price_tax,
               sbp.tax_rate,
               sbp.tax_code,
               sbp.materials_code,
               sbp.sale_unit
        from supplier_invoice_bill_detail sibd
                 left join supplier_invoice_bill sib on sib.id = sibd.invoice_id
                 left join settle_shop_bill_detail ssbd on ssbd.detail_id = sibd.bill_detail_id
                 left join settle_bill_pool sbp on sbp.id = ssbd.settle_bill_pool_id
        where sib.id = #{supplierInvoiceId}
    </select>

    <select id="getSupplierInvoiceDelDetail"
            resultType="com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto">
        select sibd.id                    as invoiceDetailId,
               ssbd.detail_id             as billDetailId,
               sblc.id                    as lifeCycleId,
               sbp.order_detail_id,
               sbp.order_sales_channel,
               sibd.invoice_num,
               sibd.goods_total_price_tax as invoiceMoney
        from supplier_invoice_bill_detail sibd
                 left join supplier_invoice_bill sib on sib.id = sibd.invoice_id
                 left join settle_shop_bill_detail ssbd on ssbd.detail_id = sibd.bill_detail_id
                 left join settle_bill_pool sbp on sbp.id = ssbd.settle_bill_pool_id
                 left join settle_bill_life_cycle sblc on sblc.settle_bill_pool_id = sbp.id
        where sib.id = #{supplierInvoiceId}
    </select>

</mapper>