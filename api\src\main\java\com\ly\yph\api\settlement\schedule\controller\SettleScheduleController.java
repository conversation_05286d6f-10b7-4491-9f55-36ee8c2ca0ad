package com.ly.yph.api.settlement.schedule.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ly.yph.api.settlement.schedule.service.SettleScheduleService;
import com.ly.yph.core.base.ServiceResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "结算中心 - 定时任务")
@RequestMapping("settleSchedule")
@SaCheckLogin
public class SettleScheduleController {

    @Resource
    private SettleScheduleService settleScheduleService;


    @ApiOperation("自动开票定时任务")
    @PostMapping("autoInvoiceJob")
    @SaIgnore
    public ServiceResult<String> autoInvoiceJob(){
        settleScheduleService.autoInvoiceJob();
        return ServiceResult.succ("自动开票定时任务执行完成！");
    }

    @ApiOperation("获取自动开票的企业")
    @GetMapping("AutoInvoiceCompany")
    public ServiceResult<?> getAutoInvoiceCompany(){
        return ServiceResult.succ(settleScheduleService.getAutoInvoiceCompany());
    }

    @ApiOperation("客户已验收数据进行出账")
    @GetMapping("autoCompanyBillOut")
    @SaIgnore
    public ServiceResult<?> autoCompanyBillOut(Integer limit){
        settleScheduleService.autoCompanyBillOut(limit);
        return ServiceResult.succ("客户侧出账定时任务执行成功");
    }

    @ApiOperation("供应商签收单/妥投出账")
    @GetMapping("autoSupplierBillOut")
    @SaIgnore
    public ServiceResult<?> autoSupplierBillOut(Integer limit){
        settleScheduleService.autoSupplierBillOut(limit);
        return ServiceResult.succ("供应商侧出账定时任务执行成功");
    }

    @ApiOperation("生成支付计划")
    @GetMapping("creatSupplierPay")
    @SaIgnore
    public ServiceResult<?> creatSupplierPay(){
        return ServiceResult.succ(settleScheduleService.creatSupplierPay());
    }

}
