package com.ly.yph.api.orderlifecycle.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailForDeliveryDto;
import com.ly.yph.api.orderlifecycle.dto.comment.CommentInfoDto;
import com.ly.yph.api.orderlifecycle.dto.comment.CommentPurchaseInfoDto;
import com.ly.yph.api.orderlifecycle.entity.OrderDetailPool;
import com.ly.yph.api.orderlifecycle.entity.PurchaseOrderInfoPool;
import com.ly.yph.api.orderlifecycle.enums.LifeCycleNodeStatusEnum;
import com.ly.yph.api.orderlifecycle.enums.OrderSalesChannelEnum;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleFactory;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleStrategy;
import com.ly.yph.api.orderlifecycle.utils.EnhancedIPHashSnowflakeGenerator;
import com.ly.yph.api.orderlifecycle.utils.OrderPoolPurchaseTypeUtils;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;
import com.ly.yph.api.system.entity.InvoiceSubject;
import com.ly.yph.api.system.service.InvoiceSubjectService;
import com.ly.yph.core.base.CommonCodeGeneral;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommentService {

    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;

    @Resource
    private OrderDetailPoolService orderDetailPoolService;

    @Resource
    private OrderLifeCycleFactory orderLifeCycleFactory;

    @Resource
    private InvoiceSubjectService invoiceSubjectService;

    @Resource
    private CommonCodeGeneral commonCodeGeneral;

    @Transactional
    public String synchronizeData(CommentInfoDto commentInfoDto) {

        OrderSalesChannelEnum orderSalesChannelEnum = OrderSalesChannelEnum.get(commentInfoDto.getOrderSalesChannel());
        if (orderSalesChannelEnum == null) {
            throw new ParameterException("无此商城类型！");
        }

        if(OrderSalesChannelEnum.NISSANMALL.getCode().equals(commentInfoDto.getOrderSalesChannel())){
            //3SM需要帮忙填充主体信息
            List<String> companyCodes = commentInfoDto.getCommentPurchaseInfoDtoList().stream().map(CommentPurchaseInfoDto::getCompanyCode).distinct().collect(Collectors.toList());
            QueryWrapper<InvoiceSubject> queryWrapper =new QueryWrapper<>();
            queryWrapper.lambda().in(InvoiceSubject::getOrganizationCode,companyCodes);
            List<InvoiceSubject> invoiceSubjectList = invoiceSubjectService.list(queryWrapper);
            Map<String, InvoiceSubject> invoiceSubjectMap = invoiceSubjectList.stream().collect(Collectors.toMap(InvoiceSubject::getOrganizationCode, Function.identity()));

            commentInfoDto.getCommentPurchaseInfoDtoList().forEach(e->{
                InvoiceSubject invoiceSubject = invoiceSubjectMap.get(e.getCompanyCode());
                if(invoiceSubject!=null){
                    e.setInvoiceSubject(invoiceSubject.getInvoiceSubject());
                }
            });

        }

        List<PurchaseOrderInfoPool> purchaseOrderInfoPoolListForSave = new ArrayList<>();
        List<OrderDetailPool> orderDetailPoolListForSave = new ArrayList<>();

        for (CommentPurchaseInfoDto commentPurchaseInfoDto : commentInfoDto.getCommentPurchaseInfoDtoList()) {
            log.info("purcahseNumber:{}",commentPurchaseInfoDto.getPurchaseNumber());

            PurchaseOrderInfoPool purchaseOrderInfoPool = DataAdapter.convert(commentPurchaseInfoDto, PurchaseOrderInfoPool.class);

            List<OrderDetailPool> orderDetailPoolList = DataAdapter.convertList(commentPurchaseInfoDto.getCommentOrderDetailDtoList(), OrderDetailPool.class);

            //获取id
            Long purchaseOrderInfoId = EnhancedIPHashSnowflakeGenerator.nextId();
            purchaseOrderInfoPool.setId(purchaseOrderInfoId);
            purchaseOrderInfoPool.setIsFix(1);
            purchaseOrderInfoPool.setOrderSalesChannel(commentInfoDto.getOrderSalesChannel());

            orderDetailPoolList.forEach(e -> {
                e.setId(EnhancedIPHashSnowflakeGenerator.nextId());
                e.setPurchaseInfoId(purchaseOrderInfoId);
                e.setOrderSalesChannel(commentInfoDto.getOrderSalesChannel());
                OrderPoolPurchaseTypeUtils.fillPurchaseTypeInfo(e, purchaseOrderInfoPool, commentPurchaseInfoDto.getAddressType());
            });

            purchaseOrderInfoPoolListForSave.add(purchaseOrderInfoPool);
            orderDetailPoolListForSave.addAll(orderDetailPoolList);
        }

        purchaseOrderInfoPoolService.saveBatch(purchaseOrderInfoPoolListForSave);
        orderDetailPoolService.saveBatch(orderDetailPoolListForSave);

        return "销售渠道" + OrderSalesChannelEnum.get(commentInfoDto.getOrderSalesChannel()) + "同步的采购单数据成功，采购单个数:" + commentInfoDto.getCommentPurchaseInfoDtoList().size();

    }

    @Transactional
    public String deliveryData(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {

        OrderSalesChannelEnum orderSalesChannelEnum = OrderSalesChannelEnum.get(updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
        if (orderSalesChannelEnum == null) {
            throw new ParameterException("无此商城类型！");
        }

        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(orderSalesChannelEnum.getCode());

        if (updateOrderDetailForDeliveryDto.getType() == 1) {
            //发货
            orderLifeCycleStrategy.updatePurchaseOrderInfoForDelivery(updateOrderDetailForDeliveryDto);
        } else if (updateOrderDetailForDeliveryDto.getType() == 2) {
            //妥投
            orderLifeCycleStrategy.updatePurchaseOrderInfoForProperDelivery(updateOrderDetailForDeliveryDto);
        } else if (updateOrderDetailForDeliveryDto.getType() == 3) {
            //收货
            orderLifeCycleStrategy.updatePurchaseOrderInfoForReceive(updateOrderDetailForDeliveryDto);
        } else {
            log.info("发货或妥投或收货类型异常，订单号：{}，渠道：{}", updateOrderDetailForDeliveryDto.getOrderNumber(), updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
            return "类型异常";
        }

        return "同步历史发货单相关数据完成！";
    }

    @Transactional
    public void updateForBillOut(List<OrderDetailPool> orderDetailPools, List<UpdateOrderDetailDto> updateOrderDetailDtoList) {
        //防止同一条明细 有相同sku出账数量未累加
        Map<String, BigDecimal> billOutMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getCheckNum,
                        BigDecimal::add));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            BigDecimal billCheckNum = billOutMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            //最终出账数量
            BigDecimal newCheckNum = orderDetailPool.getCustomerCheckoutNum().add(billCheckNum);
            updateOrderDetailPool.setCustomerCheckoutNum(newCheckNum);
            if (newCheckNum.compareTo(BigDecimal.ZERO) > 0) {
                //最终出账数量是正数，可能是全部出账了，也可能是部分出账了，具体看售后数量
                BigDecimal checkNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getCustomerCheckoutNum()).subtract(orderDetailPool.getAfterSaleNum());
                if (checkNum.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else if (checkNum.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }

            }else if(newCheckNum.compareTo(BigDecimal.ZERO)==0){
                updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            }else {
                //最终出账数量小于0 绝壁有问题了
                updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    @Transactional
    public void updateForBillReconciliation(List<OrderDetailPool> orderDetailPools,List<UpdateOrderDetailDto> updateOrderDetailDtoList){
        //防止同一条明细 有相同sku出账数量未累加
        Map<String, BigDecimal> reconciliationMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getReconciliationNum,
                        BigDecimal::add));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            BigDecimal billReconciliationNum = reconciliationMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            BigDecimal newReconciliationNum = orderDetailPool.getCustomerReconciliationNum().add(billReconciliationNum);
            updateOrderDetailPool.setCustomerReconciliationNum(newReconciliationNum);

            if (newReconciliationNum.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal reconciliationNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getCustomerReconciliationNum()).subtract(orderDetailPool.getAfterSaleNum());
                //可能全部对账了，也可能部分对账了
                if (reconciliationNum.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else if (reconciliationNum.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            }else if(newReconciliationNum.compareTo(BigDecimal.ZERO)==0){
                updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            }else {
                //异常了
                updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    @Transactional
    public void updateForDeleteBillDetail(List<OrderDetailPool> orderDetailPools,List<UpdateOrderDetailDto> updateOrderDetailDtoList) {
        //账单明细可能还没对账 出账数量
        Map<String, BigDecimal> detailDeleteMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getCheckNum,
                        BigDecimal::add)
        );

        //已经对账数量
        Map<String, BigDecimal> reconciliationNumMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getReconciliationNum,
                        BigDecimal::add)
        );

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());

            BigDecimal deleteNum = detailDeleteMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal reconciliationNumForDel = reconciliationNumMap.get(orderDetailPool.getOrderDetailId());

            BigDecimal newCheckOutNum;
            BigDecimal newReconciliationNum;

            if (deleteNum.compareTo(BigDecimal.ZERO) >= 0) {
                newCheckOutNum = orderDetailPool.getCustomerCheckoutNum().subtract(deleteNum);
            } else {
                newCheckOutNum = orderDetailPool.getCustomerCheckoutNum().add(deleteNum);
            }


            if (reconciliationNumForDel.compareTo(BigDecimal.ZERO) >= 0) {
                newReconciliationNum = orderDetailPool.getCustomerReconciliationNum().subtract(reconciliationNumForDel);
            } else {
                newReconciliationNum = orderDetailPool.getCustomerReconciliationNum().add(reconciliationNumForDel);
            }

            updateOrderDetailPool.setCustomerCheckoutNum(newCheckOutNum);
            updateOrderDetailPool.setCustomerReconciliationNum(newReconciliationNum);

            //出账状态
            if (BigDecimal.ZERO.compareTo(newCheckOutNum) == 0) {
                updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            } else if (BigDecimal.ZERO.compareTo(newCheckOutNum) < 0) {
                //存在售后导致的账单删除，所以这里需要判断一下最终出账数量+售后数量 = 订单数量
                BigDecimal reallyCheckOut = orderDetailPool.getConfirmNum().subtract(newCheckOutNum).subtract(orderDetailPool.getAfterSaleNum());
                if (BigDecimal.ZERO.compareTo(reallyCheckOut) == 0) {
                    updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else if (BigDecimal.ZERO.compareTo(reallyCheckOut) < 0) {
                    updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            } else {
                updateOrderDetailPool.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            //对账状态
            if (BigDecimal.ZERO.compareTo(newReconciliationNum) == 0) {
                updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            } else if (BigDecimal.ZERO.compareTo(newReconciliationNum) < 0) {
                BigDecimal subtract = orderDetailPool.getConfirmNum().subtract(newReconciliationNum).subtract(orderDetailPool.getAfterSaleNum());
                if (BigDecimal.ZERO.compareTo(subtract) == 0) {
                    updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else if (BigDecimal.ZERO.compareTo(subtract) < 0) {
                    updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }

            } else {
                updateOrderDetailPool.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            //开票状态
            BigDecimal customerInvoicedNum = orderDetailPool.getCustomerInvoicedNum();
            if (customerInvoicedNum.compareTo(BigDecimal.ZERO) > 0) {
                //证明存在其他的账单明细已经是开票的
                BigDecimal invoicedFlag = orderDetailPool.getConfirmNum().subtract(customerInvoicedNum).subtract(orderDetailPool.getAfterSaleNum());
                if (invoicedFlag.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else if (invoicedFlag.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            }

            updateList.add(updateOrderDetailPool);
        }

        orderDetailPoolService.updateBatchById(updateList);

    }

    @Transactional
    public void updateForApplyInvoice(List<OrderDetailPool> orderDetailPools, List<UpdateOrderDetailDto> updateOrderDetailDtoList) {
        Map<String, BigDecimal> billInvoiceMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getInvoiceNum,
                        BigDecimal::add
                )
        );

        List<OrderDetailPool> updateList = new ArrayList<>();

        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            BigDecimal invoicingNum = billInvoiceMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();

            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setCustomerInvoicingNum(orderDetailPool.getCustomerInvoicingNum().add(invoicingNum));

            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);

    }

    @Transactional
    public void updateForInvoiceApprove(List<OrderDetailPool> orderDetailPools,List<UpdateOrderDetailDto> updateOrderDetailDtoList,Integer invoiceApproveType ){
        Map<String, BigDecimal> invoicedNumMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getInvoiceNum,
                        BigDecimal::add));

        Map<String, BigDecimal> invoiceMoneyMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getInvoiceMoney,
                        BigDecimal::add)
        );

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            BigDecimal invoicedNum = invoicedNumMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal invoiceMoney = invoiceMoneyMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());

            updateOrderDetailPool.setCustomerInvoicingNum(orderDetailPool.getCustomerInvoicingNum().subtract(invoicedNum));

            if (invoiceApproveType == 0) {
                //审批通过 更新开票数量和开票金额,驳回的话 则只用回退开票中的数量
                updateOrderDetailPool.setCustomerInvoicedNum(orderDetailPool.getCustomerInvoicedNum().add(invoicedNum));
                updateOrderDetailPool.setCustomerInvoicedMoney(orderDetailPool.getCustomerInvoicedMoney().add(invoiceMoney));

            }else {
                updateOrderDetailPool.setCustomerInvoicedNum(orderDetailPool.getCustomerInvoicedNum());
            }

            BigDecimal haveInvoicedNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getCustomerInvoicedNum()).subtract(orderDetailPool.getAfterSaleNum());

            if (updateOrderDetailPool.getCustomerInvoicedNum().compareTo(BigDecimal.ZERO) >0) {
                if (haveInvoicedNum.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else if (haveInvoicedNum.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            } else if(updateOrderDetailPool.getCustomerInvoicedNum().compareTo(BigDecimal.ZERO) ==0){
                updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            } else {
                updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            updateList.add(updateOrderDetailPool);
        }

        orderDetailPoolService.updateBatchById(updateList);
    }

    @Transactional
    public void updateForInvoiceRepayment(List<OrderDetailPool> orderDetailPools, List<UpdateOrderDetailDto> updateOrderDetailDtoList) {
        Map<String, BigDecimal> invoiceSettleNumMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getInvoiceNum,
                        BigDecimal::add)
        );

        Map<String, BigDecimal> invoiceSettleMoneyMap = updateOrderDetailDtoList.stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getInvoiceDetailGoodsTotalPriceTax,
                        BigDecimal::add)
        );

        List<OrderDetailPool> updateList = new ArrayList<>();

        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            BigDecimal settleNum = invoiceSettleNumMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal settleMoney = invoiceSettleMoneyMap.get(orderDetailPool.getOrderDetailId());

            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setCustomerSettlementMoney(orderDetailPool.getCustomerSettlementMoney().add(settleMoney));
            updateOrderDetailPool.setCustomerSettlementNum(orderDetailPool.getCustomerSettlementNum().add(settleNum));

            BigDecimal newSettleNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getCustomerSettlementNum()).subtract(orderDetailPool.getAfterSaleNum());

            if (updateOrderDetailPool.getCustomerSettlementNum().compareTo(BigDecimal.ZERO) >= 0) {
                if (newSettleNum.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else if (newSettleNum.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            } else {
                updateOrderDetailPool.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateList.add(updateOrderDetailPool);
        }

        orderDetailPoolService.updateBatchById(updateList);
    }

    @Transactional
    public void updateForInvoiceOffset(List<OrderDetailPool> orderDetailPools, List<UpdateOrderDetailDto> updateOrderDetailDtoList,Integer invoiceApproveType){
        Map<String, BigDecimal> invoicedNumMap = updateOrderDetailDtoList.stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getInvoiceNum,
                BigDecimal::add
        ));

        Map<String, BigDecimal> invoicedMoneyMap = updateOrderDetailDtoList.stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getInvoiceMoney,
                BigDecimal::add
        ));

        List<OrderDetailPool> updateList = new ArrayList<>();

        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());

            BigDecimal invoicedNum = invoicedNumMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal invoicedMoney = invoicedMoneyMap.get(orderDetailPool.getOrderDetailId());

            BigDecimal newInvoicedNum = orderDetailPool.getCustomerInvoicedNum().subtract(invoicedNum);
            BigDecimal newInvoicedMoney = orderDetailPool.getCustomerInvoicedMoney().subtract(invoicedMoney);

            updateOrderDetailPool.setCustomerInvoicedNum(newInvoicedNum);
            updateOrderDetailPool.setCustomerInvoicedMoney(newInvoicedMoney);

            if (newInvoicedNum.compareTo(BigDecimal.ZERO) == 0) {
                updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            } else if (newInvoicedNum.compareTo(BigDecimal.ZERO) > 0) {
                updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
            } else {
                updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            //已结清的需要更新客户的结算信息
            if (invoiceApproveType == 0) {

                BigDecimal newSettleNum = orderDetailPool.getCustomerSettlementNum().subtract(invoicedNum);
                BigDecimal newSettleMoney = orderDetailPool.getCustomerSettlementMoney().subtract(invoicedMoney);

                if (newSettleNum.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else if (newSettleNum.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
                updateOrderDetailPool.setCustomerSettlementNum(newSettleNum);
                updateOrderDetailPool.setCustomerSettlementMoney(newSettleMoney);
            }

            updateList.add(updateOrderDetailPool);

        }

        orderDetailPoolService.updateBatchById(updateList);
    }

    @Transactional
    public void updatePurchaseOrderInfoForSupplierInvoice(List<SupplierInvoiceDelDto> supplierInvoiceDelDtos) {

        List<String> orderDetailIdList = supplierInvoiceDelDtos.stream().map(SupplierInvoiceDelDto::getOrderDetailId).distinct().collect(Collectors.toList());

        List<OrderDetailPool> orderDetailPoolList = new ArrayList<>();
        Lists.partition(orderDetailIdList, 1000).forEach(subList -> {
            QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel, supplierInvoiceDelDtos.get(0).getOrderSalesChannel())
                    .in(OrderDetailPool::getOrderDetailId, subList)
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum,
                            OrderDetailPool::getSupplierInvoicedNum,
                            OrderDetailPool::getSupplierInvoicedMoney,
                            OrderDetailPool::getSupplierInvoiceStatus);
            orderDetailPoolList.addAll(orderDetailPoolService.list(queryWrapper));
        });

        Map<String, BigDecimal> invoiceNumMap = supplierInvoiceDelDtos.stream().collect(Collectors.toMap(
                SupplierInvoiceDelDto::getOrderDetailId,
                SupplierInvoiceDelDto::getInvoiceNum,
                BigDecimal::add
        ));

        Map<String, BigDecimal> invoiceMoneyMap = supplierInvoiceDelDtos.stream().collect(Collectors.toMap(
                SupplierInvoiceDelDto::getOrderDetailId,
                SupplierInvoiceDelDto::getInvoiceMoney,
                BigDecimal::add
        ));

        List<OrderDetailPool> updateOrderDetailDtoList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {
            BigDecimal invoiceNum = invoiceNumMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal invoiceMoney = invoiceMoneyMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal newInvoiceNum = orderDetailPool.getSupplierInvoicedNum().add(invoiceNum);
            BigDecimal newInvoiceMoney = orderDetailPool.getSupplierInvoicedMoney().add(invoiceMoney);

            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setSupplierInvoicedNum(newInvoiceNum);
            updateOrderDetailPool.setSupplierInvoicedMoney(newInvoiceMoney);

            if (newInvoiceNum.compareTo(BigDecimal.ZERO) > 0) {

                BigDecimal hadInvoiceNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getSupplierInvoicedNum()).subtract(orderDetailPool.getAfterSaleNum());
                if (hadInvoiceNum.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetailPool.setSupplierInvoiceStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else if (hadInvoiceNum.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetailPool.setSupplierInvoiceStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                } else {
                    updateOrderDetailPool.setSupplierInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }

            }else if (newInvoiceNum.compareTo(BigDecimal.ZERO) == 0) {
                orderDetailPool.setSupplierInvoiceStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
            } else {
                updateOrderDetailPool.setSupplierInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateOrderDetailDtoList.add(updateOrderDetailPool);
        }

        orderDetailPoolService.updateBatchById(updateOrderDetailDtoList);

    }
}
