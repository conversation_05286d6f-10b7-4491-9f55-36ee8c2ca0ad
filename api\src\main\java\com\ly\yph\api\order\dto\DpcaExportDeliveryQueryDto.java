package com.ly.yph.api.order.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月19日
 */
@Data
public class DpcaExportDeliveryQueryDto implements Serializable {
    private static final long serialVersionUID = -4306624498759377568L;
    @ExcelProperty("包裹单号+物流单号")
    String logisticsOrderSn;
    @ExcelProperty("物流单号")
    String deliveryCode;
    @ExcelProperty("包裹号")
    String packageId;
    @ExcelProperty("确认收货时间")
    Date acceptanceTime;
    @ExcelProperty("采购单号")
    String purchaseNumber;
    @ExcelProperty("采购单行号")
    Integer itemNo;
    @ExcelProperty("商品编码")
    String goodsCode;
    @ExcelProperty("单位")
    String unit;
    @ExcelProperty("采购数量")
    Long goodsNum;
    @ExcelProperty("收货数量")
    Integer deliveryNum;
    @ExcelProperty("货币单位")
    String currencyUnit = "CNY";
    @ExcelProperty("未税单价")
    BigDecimal price;
    @ExcelProperty("未税收货价格")
    BigDecimal rowPrice;
    @ExcelProperty("含税单价")
    BigDecimal priceTax;
    @ExcelProperty("含税收货价格")
    BigDecimal rowPriceTax;
    @ExcelProperty("税率")
    Integer taxRate;
    @ExcelProperty("购买人")
    String buyerName;
    @ExcelProperty("是否收货完成")
    String completed;

}
