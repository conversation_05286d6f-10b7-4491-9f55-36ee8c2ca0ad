package com.ly.yph.api.settlement.common.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.customization.service.SapIndexOrderService;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.dto.youServiceOrder.BillBackAfterSaleDto;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.order.service.ShopReturnService;
import com.ly.yph.api.order.vo.ShopReturnVo;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailForBillDto;
import com.ly.yph.api.orderlifecycle.enums.OrderSalesChannelEnum;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleFactory;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleStrategy;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoPoolService;
import com.ly.yph.api.orderlifecycle.utils.OrderPoolPurchaseTypeUtils;
import com.ly.yph.api.organization.entity.SystemUserRole;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.mapper.SystemIntegralMapper;
import com.ly.yph.api.organization.mapper.SystemUsersMapper;
import com.ly.yph.api.organization.service.SystemUsersService;
import com.ly.yph.api.settlement.common.dto.bill.*;
import com.ly.yph.api.settlement.common.dto.settleBillPoolYfl.YflApplyUserUserMonthDto;
import com.ly.yph.api.settlement.common.entity.*;
import com.ly.yph.api.settlement.common.enums.*;
import com.ly.yph.api.settlement.common.factory.SettleBillFactory;
import com.ly.yph.api.settlement.common.factory.SettleShopBillDetailStrategy;
import com.ly.yph.api.settlement.common.mapper.SettleBillLifeCycleMapper;
import com.ly.yph.api.settlement.common.mapper.SettleShopBillDetailMapper;
import com.ly.yph.api.settlement.common.vo.bill.*;
import com.ly.yph.api.settlement.supplier.dto.RemoveToNextMonthDto;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillService;
import com.ly.yph.api.system.dto.OrderDetailExportExcelVO;
import com.ly.yph.api.system.dto.OrderExportExcelVO;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.SystemErrorCodeConstants;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.mapper.SystemDictDataMapper;
import com.ly.yph.core.dic.core.service.impl.DictDataServiceImpl;
import com.ly.yph.core.email.MailService;
import com.ly.yph.tenant.core.db.TenantBaseDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SettleShopBillDetailService extends ServiceImpl<SettleShopBillDetailMapper, SettleShopBillDetail> {

    @Resource
    private SettleShopBillDetailMapper settleShopBillDetailMapper;

    @Resource
    private SettleBillLifeCycleService settleBillLifeCycleService;

    @Resource
    private SettleBillPoolService settleBillPoolService;

    @Resource
    private SettleShopBillService settleShopBillService;

    @Resource
    private SettleShopBillActivityMemberService settleShopBillActivityMemberService;

    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;

    @Resource
    private SettleBillPoolYflCustomerService settleBillPoolYflCustomerService;

    @Resource
    private ShopPurchaseSubOrderDetailService shopPurchaseSubOrderDetailService;

    @Resource
    private SystemUsersMapper systemUsersMapper;

    @Resource
    private SystemUsersService systemUsersService;

    @Resource
    private SettleShopBillDetailService settleShopBillDetailService;

    @Resource
    private SettleBillLifeCycleMapper settleBillLifeCycleMapper;

    @Autowired
    YflYamlConfig yflYamlConfig;

    @Resource
    private SapIndexOrderService sapIndexOrderService;

    @Resource
    private DictDataServiceImpl dictDataSrv;

    @Resource
    private MailService mailService;

    @Resource
    private SystemDictDataMapper systemDictDataMapper;

    @Resource
    private SystemIntegralMapper systemIntegralMapper;

    @Resource
    private ShopReturnService shopReturnService;

    @Resource
    private SettleBillFactory settleBillFactory;

    @Resource
    private OrderLifeCycleFactory orderLifeCycleFactory;

    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;

    @Resource
    private InvoiceBillService invoiceBillService;
    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;


    public SettleShopBillDetail getShopBillDetail(SettleBillPool settleBillPool, SettleShopBill settleShopBill, Map<Long, SettleBillLifeCycle> billLifeCycleMap) {

        SettleShopBillDetail settleShopBillDetail = DataAdapter.convert(settleBillPool, SettleShopBillDetail.class);

        settleShopBillDetail.setBillId(settleShopBill.getBillId());
        settleShopBillDetail.setBillSn(settleShopBill.getBillSn());

        settleShopBillDetail.setCheckYear(settleShopBill.getCheckYear());
        settleShopBillDetail.setCheckMonth(settleShopBill.getCheckMonth());

        settleShopBillDetail.setSettleBillPoolId(settleBillPool.getId());
        SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());

        if(BillCustomerTypeEnum.SUPPLIER.getCode().equals(settleShopBill.getCustomerType())){
            settleShopBillDetail.setCheckedNum(settleBillPool.getSupplierCheckedNum());
            settleShopBillDetail.setStoreCode(settleBillPool.getCompanyCode());
            settleShopBillDetail.setStoreName(settleBillPool.getCompanyName());
            settleShopBillDetail.setStoreDataSource("dfmall");
            settleShopBillDetail.setUnitPriceNaked(settleBillPool.getSupplierUnitPriceNaked());
            settleShopBillDetail.setUnitPriceTax(settleBillPool.getSupplierUnitPriceTax());

            if (settleBillLifeCycle != null) {
                settleBillLifeCycle.setSupplierCheckoutStatus(20);
                settleBillLifeCycle.setSupplierCheckoutNum(settleBillPool.getSupplierCheckedNum());
                settleBillLifeCycle.setSupplierCheckoutTime(new Date());
                settleBillLifeCycle.setSupplierReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode());
            }
        }else {
            settleShopBillDetail.setCheckedNum(settleBillPool.getCheckedNum());
            settleShopBillDetail.setStoreCode(settleBillPool.getSupplierCode());
            settleShopBillDetail.setStoreName(settleBillPool.getSupplierName());
            settleShopBillDetail.setStoreDataSource(settleBillPool.getSupplierDataSource());
            settleShopBillDetail.setUnitPriceNaked(settleBillPool.getGoodsUnitPriceNaked());
            settleShopBillDetail.setUnitPriceTax(settleBillPool.getGoodsUnitPriceTax());

            if (settleBillLifeCycle != null) {
                settleBillLifeCycle.setCustomerCheckoutStatus(20);
                settleBillLifeCycle.setCustomerCheckoutNum(settleBillPool.getCheckedNum());
                settleBillLifeCycle.setCustomerCheckoutTime(new Date());
                settleBillLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode());
            }
        }
        settleShopBillDetail.setBillDetailType(settleShopBillDetail.getCheckedNum().compareTo(BigDecimal.ZERO) >= 0 ? BillDetailTypeEnum.POSITIVE_BILL.getCode() : BillDetailTypeEnum.NEGATIVE_BILL.getCode());
        settleShopBillDetail.setCreateTime(new Date());
        settleShopBillDetail.setUpdateTime(new Date());

        settleShopBillDetail.setMallType(settleBillPool.getTenantId().equals(yflYamlConfig.getTenantId()) ? 2 : 1);
        settleShopBillDetail.setPoolType(1);

        RowPriceDtoForPriceMode rowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(
                settleBillPool.getTaxRate(), settleShopBillDetail.getUnitPriceNaked(),
                settleShopBillDetail.getUnitPriceTax(), settleShopBill.getCustomerType(),
                settleBillPool.getPricingMode(), settleShopBillDetail.getCheckedNum());

        settleShopBillDetail.setTotalPriceNaked(rowPriceDtoForPriceMode.getTotalPriceNaked());
        settleShopBillDetail.setTotalPriceTax(rowPriceDtoForPriceMode.getTotalPriceTax());
        settleShopBillDetail.setPriceMode(rowPriceDtoForPriceMode.getPriceModel());

        settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode());

        return settleShopBillDetail;
    }

    /**
     * 计算账单行小计
     * 客户账单行小计计算逻辑&友福利客户&独立供应商价格模式0，1
     *
     * @param taxRate
     * @param settleShopBillDetail
     */
    public void calculateRowSubtotal(Integer taxRate, SettleShopBillDetail settleShopBillDetail) {
        BigDecimal num = settleShopBillDetail.getCheckedNum();
        Integer rate = taxRate == null ? 13 : taxRate;
        BigDecimal reallyTaxRate = new BigDecimal(rate).multiply(new BigDecimal("0.01")).add(BigDecimal.ONE);

        settleShopBillDetail.setTotalPriceNaked(num.multiply(settleShopBillDetail.getUnitPriceNaked()).setScale(2, BigDecimal.ROUND_HALF_UP));
        settleShopBillDetail.setTotalPriceTax(settleShopBillDetail.getUnitPriceNaked().multiply(reallyTaxRate).multiply(num).setScale(2, BigDecimal.ROUND_HALF_UP));
        log.info("未税总价：{}，含税总价：{}", settleShopBillDetail.getTotalPriceNaked(), settleShopBillDetail.getTotalPriceTax());
    }

    public PageResp<SettleShopBillDetailVo> querySettleShopBillDetailPage(PageReq pageReq, SettleShopBillDetailPageReqVo reqVo) {
        SettleShopBill settleShopBill = settleShopBillService.getById(reqVo.getBillId());
        if (settleShopBill == null) {
            throw new ParameterException("未查询到账单信息！");
        }
        Integer poolType = settleShopBillService.getPoolType(settleShopBill);
        if (1 == poolType) {
            IPage<SettleShopBillDetailVo> shopBillDetailVoIPage = this.getBaseMapper().querySettleShopBillDetailPage(DataAdapter.adapterPageReq(pageReq), reqVo);
            return DataAdapter.adapterPage(shopBillDetailVoIPage, SettleShopBillDetailVo.class);
        } else {
            IPage<SettleShopBillDetailVo> shopBillDetailVoIPage = this.getBaseMapper().querySettleShopBillDetailYflPage(DataAdapter.adapterPageReq(pageReq), reqVo);
            return DataAdapter.adapterPage(shopBillDetailVoIPage, SettleShopBillDetailVo.class);
        }

    }

    public PageResp<SettleShopBillDetailPageVo> queryDetailPage(PageReq pageReq, SettleShopBillDetailPageReqVo reqVo) {
        if (reqVo.getCustomerSourceType() == 2) {
            //友福利客户账单明细
            IPage<SettleShopBillDetailVo> shopBillDetailVoIPage = this.getBaseMapper().querySettleShopBillDetailYflPage(DataAdapter.adapterPageReq(pageReq), reqVo);
            return DataAdapter.adapterPage(shopBillDetailVoIPage, SettleShopBillDetailPageVo.class);
        } else {
            //东风商城客户账单明细
            IPage<SettleShopBillDetailVo> shopBillDetailVoIPage = this.getBaseMapper().querySettleShopBillDetailPage(DataAdapter.adapterPageReq(pageReq), reqVo);
            return DataAdapter.adapterPage(shopBillDetailVoIPage, SettleShopBillDetailPageVo.class);
        }
    }


    public PageResp<SettleShopBillDetailVo> queryReconciliationDetailPage(PageReq pageReq, SettleShopBillDetailPageReqVo reqVo) {
        //只查询客户数据范围,不再判断运营身份，查询到所有数据，防止因角色导致的问题
        LoginUser loginUser = LocalUserHolder.get();
        reqVo.setReconciliationUserId(loginUser.getId());
        IPage<SettleShopBillDetailVo> shopBillDetailVoIPage = this.getBaseMapper().querySettleShopBillDetailPage(DataAdapter.adapterPageReq(pageReq), reqVo);
        return DataAdapter.adapterPage(shopBillDetailVoIPage, SettleShopBillDetailVo.class);
    }

    private Boolean isOperate(SettleShopBillDetailPageReqVo reqVo) {
        LoginUser loginUser = LocalUserHolder.get();
        log.info("登录人对账人的信息:{}", loginUser);
        SystemUserRole settleBillOperate = systemUsersMapper.getUserRoleByUserId(loginUser.getId(), "SettleBillOperate");
        if (settleBillOperate == null) {
            reqVo.setReconciliationUserId(loginUser.getId());
            return false;
        } else {
            return true;
        }

    }

    public List<SettleShopBillDetailVo> exportBillDetailList(SettleShopBillDetailPageReqVo reqVo) {
        isOperate(reqVo);
        return settleShopBillDetailMapper.querySettleShopBillDetail(reqVo);
    }

    public List<SettleShopBillDetailExcelFrontVo> exportBillDetailListFront(SettleShopBillDetailPageReqVo reqVo) {
        LoginUser loginUser = LocalUserHolder.get();
        reqVo.setReconciliationUserId(loginUser.getId());
        return settleShopBillDetailMapper.querySettleShopBillDetailFront(reqVo);
    }

    public List<SettleShopBillDetailExcelVo> getBillDetailExecl(SettleShopBillDetailPageReqVo vo, Integer poolType) {
        if (1 == poolType) {
            return settleShopBillDetailMapper.getBillDetailExecl(vo);
        } else {
            return settleShopBillDetailMapper.getBillDetailYflExcel(vo);
        }

    }

    /**
     * 同步删除修改对侧账单
     *
     * @param billPoolIds
     * @param company
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSettleBillPoolsStatus(List<Long> billPoolIds, Boolean company, Integer operateType) {
        List<SettleBillPool> settleBillPools = new ArrayList<>();
        List<SettleBillLifeCycle> settleBillLifeCycles = new ArrayList<>();

        QueryWrapper<SettleBillLifeCycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleBillLifeCycle::getSettleBillPoolId, billPoolIds);
        List<SettleBillLifeCycle> list = settleBillLifeCycleService.list(queryWrapper);
        Map<Long, SettleBillLifeCycle> cycleMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(list)) {
            cycleMap = list.stream().collect(Collectors.toMap(SettleBillLifeCycle::getSettleBillPoolId, Function.identity(), (key1, key2) -> key1));
        }

        QueryWrapper<SettleBillPool> poolQueryWrapper = new QueryWrapper<>();
        poolQueryWrapper.lambda().in(SettleBillPool::getId, billPoolIds)
                .eq(company ? SettleBillPool::getCompanyOutAccountState : SettleBillPool::getSupplierOutAccountState, 20);
        List<SettleBillPool> valid = settleBillPoolService.list(poolQueryWrapper);
        if (billPoolIds.size() != valid.size()) {
            throw new ParameterException("服务繁忙，请刷新页面稍后再试！");
        }

        for (Long id : billPoolIds) {
            SettleBillPool settleBillPool = new SettleBillPool();
            SettleBillLifeCycle billLifeCycle = new SettleBillLifeCycle();
            SettleBillLifeCycle settleBillLifeCycle = cycleMap.get(id);
            settleBillPool.setId(id);
            billLifeCycle.setSettleBillPoolId(id);
            if (company) {
                settleBillPool.setCompanyOutAccountState(0);
                if (operateType == 1 || operateType == 3) {
                    settleBillPool.setCompanyOutAccountState(-99);
                    settleBillPool.setCompanyOutAccountTime(null);
                }
                if (settleBillLifeCycle != null) {
                    billLifeCycle.setCustomerCheckoutStatus(0);
                    billLifeCycle.setCustomerCheckoutNum(new BigDecimal("0"));
                    billLifeCycle.setId(settleBillLifeCycle.getId());
                    billLifeCycle.setCustomerCheckoutTime(null);

                    billLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_NOT_CHECKED.getCode());
                    billLifeCycle.setCustomerReconciliationTime(null);
                    billLifeCycle.setCustomerReconciliationNum(BigDecimal.ZERO);

                }

            } else {
                settleBillPool.setSupplierOutAccountState(0);
                if (operateType == 1 || operateType == 3) {
                    settleBillPool.setSupplierOutAccountState(-99);
                    settleBillPool.setSupplierOutAccountTime(null);
                }
                if (settleBillLifeCycle != null) {
                    billLifeCycle.setSupplierCheckoutStatus(0);
                    billLifeCycle.setSupplierCheckoutNum(new BigDecimal("0"));
                    billLifeCycle.setId(settleBillLifeCycle.getId());
                    billLifeCycle.setSupplierCheckoutTime(null);

                    billLifeCycle.setSupplierReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_NOT_CHECKED.getCode());
                    billLifeCycle.setSupplierReconciliationTime(null);
                    billLifeCycle.setSupplierReconciliationNum(BigDecimal.ZERO);

                }
            }
            settleBillPools.add(settleBillPool);
            settleBillLifeCycles.add(billLifeCycle);
        }

        Lists.partition(settleBillPools, 1000).forEach(sublist -> settleBillPoolService.getBaseMapper().updateBillPoolList(sublist));
        if (CollectionUtils.isNotEmpty(settleBillLifeCycles)) {
            Lists.partition(settleBillLifeCycles, 1000).forEach(subList -> settleBillLifeCycleMapper.updateLifeCycleList(subList));
        }
    }

    /**
     * 账单明细对账
     *
     * @param reconciliationOperateDto 对账传输对象
     * @return
     */
    public String reconciliationOperate(ReconciliationOperateDto reconciliationOperateDto) {
        log.info("reconciliationOperate：{}", reconciliationOperateDto);
        SettleShopBillDetailStrategy billDetailStrategy = settleBillFactory.getBillDetailStrategy(reconciliationOperateDto.getCompanyType());
        return billDetailStrategy.billDetailReconciliation(reconciliationOperateDto);
    }

    /**
     * 确认对账
     *
     * @param reconciliationDetailDataDtoList 账单明细
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String reconciliationConfirm(List<ReconciliationDetailDataDto> reconciliationDetailDataDtoList, Boolean companyFlag) {
        reconciliationBillUpdate(reconciliationDetailDataDtoList, companyFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode(), null);
        return "对账确认成功!";
    }


    /**
     * 驳回对账
     *
     * @param reconciliationDetailDataDtoList
     * @param reconciliationRemark
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String reconciliationReject(List<ReconciliationDetailDataDto> reconciliationDetailDataDtoList, String reconciliationRemark, Boolean companyFlag) {
        reconciliationBillUpdate(reconciliationDetailDataDtoList, companyFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_REJECT.getCode(), reconciliationRemark);
        return "对账驳回成功！";
    }

    /**
     * 更新对账明细状态
     *
     * @param reconciliationDetailDataDtoList
     * @param companyFlag
     * @param reconciliationStatus
     * @param reconciliationRemark
     */
    public void reconciliationBillUpdate(List<ReconciliationDetailDataDto> reconciliationDetailDataDtoList, Boolean companyFlag, Integer reconciliationStatus, String reconciliationRemark) {
        LoginUser user = LocalUserHolder.get();

        List<SettleShopBillDetail> updateDetails = new ArrayList<>();
        List<SettleBillLifeCycle> updateLifeCycles = new ArrayList<>();

        List<Long> settlePoolIds = reconciliationDetailDataDtoList.stream().map(ReconciliationDetailDataDto::getSettleBillPoolId).distinct().collect(Collectors.toList());
        //东风商城客户才对账
        List<SettleBillLifeCycle> lifeCycles = settleBillLifeCycleService.getUpdateBillLifeCycleListByIds(settlePoolIds, PoolTypeEnum.DFSHOP.getCode());
        Map<Long, SettleBillLifeCycle> billLifeCycleMap = lifeCycles.stream().collect(Collectors.toMap(SettleBillLifeCycle::getSettleBillPoolId, Function.identity(), (key1, key2) -> key1));

        for (ReconciliationDetailDataDto settleShopBillDetail : reconciliationDetailDataDtoList) {
            SettleShopBillDetail billDetail = new SettleShopBillDetail();
            billDetail.setDetailId(settleShopBillDetail.getDetailId());
            //更新账单明细状态为已确认
            billDetail.setReconciliationConfirmUserId(user.getId());
            billDetail.setReconciliationConfirmUserName(user.getNickname());
            billDetail.setReconciliationConfirmTime(new Date());
            billDetail.setReconciliationStatus(reconciliationStatus);
            billDetail.setReconciliationRemark(reconciliationRemark);
            //对账后更新可开票数量
            log.info("确认开票数量：{}", billDetail.getCheckedNum());
            billDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
            updateDetails.add(billDetail);

            SettleBillLifeCycle settleBillLifeCycle = billLifeCycleMap.get(settleShopBillDetail.getSettleBillPoolId());
            SettleBillLifeCycle updateLifeCycle = new SettleBillLifeCycle();
            updateLifeCycle.setId(settleBillLifeCycle.getId());
            if (companyFlag) {
                updateLifeCycle.setCustomerReconciliationNum(settleShopBillDetail.getCheckedNum());
                updateLifeCycle.setCustomerReconciliationStatus(reconciliationStatus);
                updateLifeCycle.setCustomerReconciliationTime(new Date());
            } else {
                updateLifeCycle.setSupplierReconciliationNum(settleShopBillDetail.getCheckedNum());
                updateLifeCycle.setSupplierReconciliationStatus(reconciliationStatus);
                updateLifeCycle.setSupplierReconciliationTime(new Date());
            }
            updateLifeCycles.add(updateLifeCycle);
        }

        //更新状态数据
        Lists.partition(updateDetails, 800).forEach(subclassList -> settleShopBillDetailMapper.updateSettleBillDetails(subclassList));
        Lists.partition(updateLifeCycles, 800).forEach(subclassList -> settleBillLifeCycleMapper.updateLifeCycleList(subclassList));
    }

    /**
     * 生成有福利客户账单明细，成员管理，邮费管理
     *
     * @param settleShopBill
     * @param settleBillPoolYflCustomerList
     * @param activityUserInfoDtoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveYflBillDetail(SettleShopBill settleShopBill, List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList, List<ActivityUserInfoDto> activityUserInfoDtoList) {
        LoginUser user = LocalUserHolder.get();
        log.info("activity_code :{}", settleShopBill.getActivityCode());
        //账单明细
        List<SettleShopBillDetail> settleShopBillDetailList = getSettleShopBillDetailListByYflPool(settleBillPoolYflCustomerList, settleShopBill, user);

        Map<Long, List<SettleBillPoolYflCustomer>> applyUserMap = settleBillPoolYflCustomerList.stream()
                .collect(Collectors.groupingBy(SettleBillPoolYflCustomer::getApplyUserId));

        //邮费管理
        List<SettleShopBillPostageDetail> settleShopBillPostageDetailList = settleShopBillPostageDetailService.fillYflPostageDetail(settleBillPoolYflCustomerList, settleShopBill);
        Map<Long, List<SettleShopBillPostageDetail>> applyUserPostageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(settleShopBillDetailList)) {
            applyUserPostageMap = settleShopBillPostageDetailList.stream().filter(c -> c.getFreightType() == 0)
                    .collect(Collectors.groupingBy(SettleShopBillPostageDetail::getApplyUserId));
        }

        //成员积分管理
        List<SettleShopBillActivityMember> settleShopBillActivityMemberList = new ArrayList<>();
        for (ActivityUserInfoDto activityUserInfoDto : activityUserInfoDtoList) {
            SettleShopBillActivityMember activityMember = DataAdapter.convert(activityUserInfoDto, SettleShopBillActivityMember.class);
            activityMember.setBillId(settleShopBill.getBillId());
            activityMember.setBillSn(settleShopBill.getBillSn());
            activityMember.setCreateTime(new Date());
            activityMember.setCreator(user.getUsername());
            activityMember.setActivityName(settleShopBill.getActivityName());
            activityMember.setCompanyName(settleShopBill.getCustomerName());
            activityMember.setUsedAmount(activityMember.getIntegralAmount().subtract(activityMember.getIntegralUsableAmount()));
            //算使用积分 商品积分
            if (CollectionUtils.isNotEmpty(applyUserMap.get(activityUserInfoDto.getUserId()))) {
                List<SettleBillPoolYflCustomer> poolYflCustomers = applyUserMap.get(activityUserInfoDto.getUserId());
                BigDecimal usedAmountIntegralForBillMonth = poolYflCustomers.stream().map(SettleBillPoolYflCustomer::getGoodsPayIntegral).reduce(BigDecimal.ZERO, BigDecimal::add);
                log.info("用户id:{},月度商品使用积分：{}", activityUserInfoDto.getUserId(), usedAmountIntegralForBillMonth);
                activityMember.setUsedMonthAmount(usedAmountIntegralForBillMonth);
                activityMember.setApplyDeptName(poolYflCustomers.get(0).getApplyDeptName());
            }
            //用户使用的积分邮费
            if (CollectionUtils.isNotEmpty(applyUserPostageMap.get(activityUserInfoDto.getUserId()))) {
                List<SettleShopBillPostageDetail> postagePoolList = applyUserPostageMap.get(activityUserInfoDto.getUserId());
                BigDecimal integralPostage = postagePoolList.stream().map(SettleShopBillPostageDetail::getPostage).reduce(BigDecimal.ZERO, BigDecimal::add);
                log.info("用户id:{},邮费积分：{}", activityUserInfoDto.getUserId(), integralPostage);
                activityMember.setUsedMonthAmount(activityMember.getUsedMonthAmount().add(integralPostage));
            }
            settleShopBillActivityMemberList.add(activityMember);
        }

        //存数据
        settleShopBillDetailService.saveBatch(settleShopBillDetailList, 500);
        settleShopBillActivityMemberService.saveBatch(settleShopBillActivityMemberList, 500);
        if (CollectionUtils.isNotEmpty(settleShopBillPostageDetailList)) {
            settleShopBillPostageDetailService.saveBatch(settleShopBillPostageDetailList, 500);
        }
        //更新账单生命周期出账状态
        List<Long> poolIds = settleBillPoolYflCustomerList.stream().map(SettleBillPoolYflCustomer::getBillPoolYflId).collect(Collectors.toList());
        UpdateWrapper<SettleBillPoolYflCustomer> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(SettleBillPoolYflCustomer::getBillPoolYflId, poolIds)
                .set(SettleBillPoolYflCustomer::getCompanyOutAccountState, 20)
                .set(BaseEntity::getModifier, user.getUsername());
        settleBillPoolYflCustomerService.update(updateWrapper);

        //更新订单明细生命周期
        purchaseOrderInfoPoolService.updateForBillOutAndReconciliation(settleShopBill, settleShopBillDetailList);

    }

    public List<SettleShopBillDetail> getSettleShopBillDetailListByYflPool(List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList, SettleShopBill settleShopBill, LoginUser user) {
        List<SettleShopBillDetail> settleShopBillDetailList = new ArrayList<>();
        SystemDictDataEntity SystemDictDataEntity = systemDictDataMapper.selectByTypeAndLabel("out_bill_reconciliation", "yfl");


        for (SettleBillPoolYflCustomer settleBillPoolYflCustomer : settleBillPoolYflCustomerList) {

            SettleShopBillDetail settleShopBillDetail = DataAdapter.convert(settleBillPoolYflCustomer, SettleShopBillDetail.class);
            settleShopBillDetail.setSettleBillPoolId(settleBillPoolYflCustomer.getBillPoolYflId());
            settleShopBillDetail.setBillId(settleShopBill.getBillId());
            settleShopBillDetail.setBillSn(settleShopBill.getBillSn());
            settleShopBillDetail.setCheckYear(settleShopBill.getCheckYear());
            settleShopBillDetail.setCheckMonth(settleShopBill.getCheckMonth());
            settleShopBillDetail.setConfirmNum(settleBillPoolYflCustomer.getApplyNum());
            settleShopBillDetail.setCheckedNum(Convert.toBigDecimal(settleBillPoolYflCustomer.getCheckedNum()));
            log.info("价格信息：{}，{}", settleShopBillDetail.getUnitPriceNaked(), settleShopBillDetail.getUnitPriceTax());
            calculateRowSubtotal(settleBillPoolYflCustomer.getTaxRate(), settleShopBillDetail);
            settleShopBillDetail.setCreator(user.getUsername());
            settleShopBillDetail.setCreateTime(new Date());
            settleShopBillDetail.setMallType(2);
            settleShopBillDetail.setPoolType(PoolTypeEnum.YFLCOMPANY.getCode());
            settleShopBillDetail.setStoreDataSource("dfmall");
            //设置对账人
            settleShopBillDetail.setReconciliationUserName(SystemDictDataEntity.getRemark());
            settleShopBillDetail.setReconciliationUserId(Long.valueOf(SystemDictDataEntity.getValue()));
            settleShopBillDetail.setReconciliationConfirmUserId(Long.valueOf(SystemDictDataEntity.getValue()));
            settleShopBillDetail.setReconciliationConfirmUserName(SystemDictDataEntity.getRemark());
            settleShopBillDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            settleShopBillDetail.setReconciliationConfirmTime(new Date());
            //设置可开票数量
            settleShopBillDetail.setInvoicableQuantity(settleBillPoolYflCustomer.getCheckedNum());
            settleShopBillDetail.setPriceMode(CustomerPriceModeEnum.NAKED_PRICE.getCode());
            settleShopBillDetail.setBillDetailType(settleShopBillDetail.getCheckedNum().compareTo(BigDecimal.ZERO) >= 0 ? BillDetailTypeEnum.POSITIVE_BILL.getCode() : BillDetailTypeEnum.NEGATIVE_BILL.getCode());


            settleShopBillDetailList.add(settleShopBillDetail);
        }
        return settleShopBillDetailList;
    }


    /**
     * 获取查询条件
     *
     * @param billId
     * @param type
     * @return
     */
    public List<BillConditionDataVo> getConditionData(String billId, Integer type) {
        String billConditionName = BillConditionTypeEnum.getBillConditionName(type);
        if (billConditionName == null) {
            throw new ParameterException("查询类型异常");
        }

        List<BillConditionDataVo> billConditionDataVos = getBaseMapper().getConditionData(billId, billConditionName);

        return billConditionDataVos;
    }


    public List<SettleShopBillDetail> yflCheckedAddPoolsAndDetail(List<SettleBillPoolYflCustomer> newSettleBillPoolYflCustomers, SettleShopBill settleShopBill, LoginUser user) {

        //账单明细
        List<SettleShopBillDetail> settleShopBillDetailListByYflPool = getSettleShopBillDetailListByYflPool(newSettleBillPoolYflCustomers, settleShopBill, user);
        return settleShopBillDetailListByYflPool;
    }

    /**
     * 获取补充出账的账单明细
     *
     * @param settleShopBill
     * @return
     */
    public List<OrderDetailExportExcelVO> yflGetCheckedDetail(SettleShopBill settleShopBill, List<OrderExportExcelVO> orderExportExcelVOS) {
        QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBillDetail::getBillId, settleShopBill.getBillId());
        List<SettleShopBillDetail> settleShopBillDetails = list(queryWrapper);
        /**-处理订账单单明细数据-**/
        List<Long> settleBillPoolYflIds = settleShopBillDetails.stream().map(SettleShopBillDetail::getSettleBillPoolId).distinct().collect(Collectors.toList());
        List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList = settleBillPoolYflCustomerService.listByIds(settleBillPoolYflIds);
        //获得已出账的订单明细
        List<String> orderDetailIds = settleBillPoolYflCustomerList.stream().map(SettleBillPoolYflCustomer::getOrderDetailId).distinct().collect(Collectors.toList());

        List<String> orderNumbers = orderExportExcelVOS.stream().map(OrderExportExcelVO::getOrderNumber).distinct().collect(Collectors.toList());
        //获取活动订单明细数据
        List<OrderDetailExportExcelVO> orderDetailExportExcelVOS = shopPurchaseSubOrderDetailService.queryActivityOrderDetail(settleShopBill.getActivityCode(), orderNumbers);

        List<OrderDetailExportExcelVO> newOrderDetail = orderDetailExportExcelVOS.stream().filter(item -> {
            List<String> collect = orderDetailIds.stream().filter(e -> e.equals(item.getOrderDetailId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                return true;//未出帐
            } else {
                return false;//已出账
            }
        }).collect(Collectors.toList());

        return newOrderDetail;
    }

    @Transactional(rollbackFor = Exception.class)
    public String detailToReconciler(DetailToReconcilerDto detailToReconcilerDto) {
        SystemUsers reconciler = systemUsersService.getById(detailToReconcilerDto.getReconciliationUserId());
        if (reconciler == null) {
            throw new ParameterException("未查询到对账人信息！");
        }

        List<SettleShopBillDetail> settleShopBillDetails = settleShopBillDetailService.listByIds(detailToReconcilerDto.getDetailIds());
        if (CollectionUtils.isEmpty(settleShopBillDetails)) {
            throw new ParameterException("未查询账单明细数据！");
        }
        List<SettleShopBillDetail> collect = settleShopBillDetails.stream().filter(e -> !e.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode()) &&
                !e.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_REJECT.getCode())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ParameterException("请选择[已出账]或[已驳回]的明细进行指派！");
        }

        List<Long> billIds = settleShopBillDetails.stream().map(SettleShopBillDetail::getBillId).distinct().collect(Collectors.toList());
        if (billIds.size() > 1) {
            throw new ParameterException("请勿跨账单明细指派！");
        }
        LoginUser loginUser = LocalUserHolder.get();

        SettleShopBill settleShopBill = settleShopBillService.getById(billIds.get(0));

        List<SettleShopBillDetail> updateDetails = new ArrayList<>();

        Boolean isCompanyFlag = settleShopBillService.isCompany(settleShopBill.getCustomerType());

        List<Long> settleBillPoolIds = settleShopBillDetails.stream().map(SettleShopBillDetail::getSettleBillPoolId).distinct().collect(Collectors.toList());
        List<SettleBillLifeCycle> updateBillLifeCycleListByIds = settleBillLifeCycleService.getUpdateBillLifeCycleListByIds(settleBillPoolIds, 1);
        Map<Long, SettleBillLifeCycle> billLifeCycleMap = updateBillLifeCycleListByIds.stream().collect(Collectors.toMap(SettleBillLifeCycle::getSettleBillPoolId, Function.identity(), (key1, key2) -> key1));

        BillAutoReconciliationDto billAutoReconciliationDto = settleShopBillService.getCompanyAutoReconciliationDto(settleShopBill, isCompanyFlag);
        List<Long> billDetailIdList = new ArrayList<>();
        List<SettleBillLifeCycle> updateSettleBillLifeCycleList = new ArrayList<>();
        for (SettleShopBillDetail shopBillDetail : settleShopBillDetails) {
            SettleShopBillDetail updateDetail = new SettleShopBillDetail();
            updateDetail.setDetailId(shopBillDetail.getDetailId());
            updateDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode());
            updateDetail.setReconciliationUserId(detailToReconcilerDto.getReconciliationUserId());
            updateDetail.setReconciliationUserName(reconciler.getNickname());
            updateDetail.setReconciliationConfirmUserId(0L);
            updateDetail.setReconciliationConfirmUserName("");
            updateDetail.setReconciliationConfirmTime(null);
            updateDetail.setUpdateTime(new Date());
            updateDetail.setModifier(loginUser.getUsername());
            SettleBillLifeCycle lifeCycle = billLifeCycleMap.get(shopBillDetail.getSettleBillPoolId());
            SettleBillLifeCycle updateLifeCycle = new SettleBillLifeCycle();
            updateLifeCycle.setId(lifeCycle.getId());
            if (BillCustomerTypeEnum.COMPANY.getCode().equals(settleShopBill.getCustomerType())) {
                updateLifeCycle.setCustomerReconciliationStatus(1);
                updateLifeCycle.setCustomerReconciliationNum(shopBillDetail.getCheckedNum());
                updateLifeCycle.setCustomerReconciliationTime(null);
            } else {
                updateLifeCycle.setSupplierReconciliationStatus(1);
                updateLifeCycle.setSupplierReconciliationNum(shopBillDetail.getCheckedNum());
                updateLifeCycle.setSupplierReconciliationTime(null);
            }
            //自动对账环境
            detailToReconcilerConfirm(billAutoReconciliationDto, reconciler, updateDetail, updateLifeCycle, shopBillDetail, billDetailIdList);

            updateDetails.add(updateDetail);
            updateSettleBillLifeCycleList.add(updateLifeCycle);
        }
        //更新账单明细状态
        Lists.partition(updateDetails, 100).forEach(subclassList -> settleShopBillDetailMapper.updateSettleBillDetails(subclassList));
        //更新生命周期
        Lists.partition(updateSettleBillLifeCycleList, 100).forEach(subclassList -> settleBillLifeCycleMapper.updateLifeCycleList(subclassList));

        if (isCompanyFlag && CollectionUtils.isNotEmpty(billDetailIdList)) {
            List<ReconciliationDetailDataDto> dfshopReconciliationData = settleShopBillDetailService.getBaseMapper().getDfshopReconciliationData(billDetailIdList);
            BillDetailReconciliationCheckDto billDetailReconciliationCheckDto = new BillDetailReconciliationCheckDto();
            billDetailReconciliationCheckDto.setReconciliationDetailDataDtoList(dfshopReconciliationData);
            billDetailReconciliationCheckDto.setCustomerType(settleShopBill.getCustomerType());
            billDetailReconciliationCheckDto.setCustomerCode(settleShopBill.getCustomerCode());

            //对账更新订单生命周期[客户目前只有东风商城(标准商城+南方)]
            purchaseOrderInfoPoolService.updateForBillDetailReconciliation(Boolean.TRUE, billDetailReconciliationCheckDto, OrderSalesChannelEnum.DFMALL.getCode());
        }

        return "指派账单成功！";
    }

    private void detailToReconcilerConfirm(BillAutoReconciliationDto billAutoReconciliationDto,
                                           SystemUsers reconciler,
                                           SettleShopBillDetail updateDetail,
                                           SettleBillLifeCycle updateLifeCycle,
                                           SettleShopBillDetail settleShopBillDetail,
                                           List<Long> billDetailIdList) {
        if (!billAutoReconciliationDto.getIsCompanyFlag()) {
            //供应商 不用对账
            return;
        }
        if (BillAutoReconciliationEnum.NO_AUTO_RECONCILIATION.getCode().equals(billAutoReconciliationDto.getAutoFlag())) {
            //需要对账 啥也不干
            return;
        } else if (BillAutoReconciliationEnum.AUTO_RECONCILIATION.getCode().equals(billAutoReconciliationDto.getAutoFlag())) {
            //自动对账
            updateDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            updateDetail.setReconciliationConfirmUserName(reconciler.getNickname());
            updateDetail.setReconciliationConfirmUserId(reconciler.getId());
            updateDetail.setReconciliationConfirmTime(new Date());
            updateDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
            updateLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            updateLifeCycle.setCustomerReconciliationTime(new Date());
            billDetailIdList.add(settleShopBillDetail.getDetailId());
        } else if (BillAutoReconciliationEnum.PARTIAL_RECONCILIATION.getCode().equals(billAutoReconciliationDto.getAutoFlag())) {
            //部分对账,企业配置了自动化对账的对账人才可以完成自动确认
            if (billAutoReconciliationDto.getReconciliationUserMap().isEmpty()) {
                return;
            }
            SystemUsers systemUsers = billAutoReconciliationDto.getReconciliationUserMap().get(reconciler.getId());
            if (systemUsers == null) {
                return;
            }
            updateDetail.setReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            updateDetail.setReconciliationConfirmUserName(systemUsers.getNickname());
            updateDetail.setReconciliationConfirmUserId(systemUsers.getId());
            updateDetail.setReconciliationConfirmTime(new Date());
            updateDetail.setInvoicableQuantity(settleShopBillDetail.getCheckedNum());
            //更新生命周期
            updateLifeCycle.setCustomerReconciliationStatus(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
            updateLifeCycle.setCustomerReconciliationTime(new Date());
            billDetailIdList.add(settleShopBillDetail.getDetailId());
        }
    }

    /**
     * 供应商/客户 查询下单人，部门，开票主体
     *
     * @param type
     * @return
     */
    public List<BillConditionDataVo> reconciliationConditionData(Integer type) {
        String billConditionName = BillConditionTypeEnum.getBillConditionName(type);
        if (StringUtils.isEmpty(billConditionName)) {
            throw new ParameterException("查询入参不能为空！");
        }

        LoginUser loginUser = LocalUserHolder.get();
        List<BillConditionDataVo> list = getBaseMapper().getReconciliationConditionData(loginUser.getId(), billConditionName);
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public String matchSapData(Long billId) {
        SettleShopBill settleShopBill = settleShopBillService.getById(billId);
        if (settleShopBill == null) {
            throw exception(SystemErrorCodeConstants.SETTLE_SHOP_BILL_ERROR);
        }

        List<SystemDictDataEntity> dictDataList = dictDataSrv.getDictDatasByDictType("sap_company_bill_match");
        if (CollectionUtils.isEmpty(dictDataList)) {
            throw new ParameterException("未配置需要进行外部匹配数据的企业,请检查配置项！");
        }

        List<String> companyCodes = dictDataList.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());

        List<String> collect = companyCodes.stream().filter(c -> settleShopBill.getCustomerCode().equalsIgnoreCase(c)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new ParameterException("企业未配置数据匹配配置，无需进行数据匹配！");
        }

        //查询该企业的需要去sap匹配的账单明细数据
        List<DetailMatchSapDataDto> detailMatchSapDataDtos = this.getBaseMapper().queryDetailMatchSapData(billId);
        if (CollectionUtils.isEmpty(detailMatchSapDataDtos)) {
            throw new ParameterException("无需要去匹配的明细数据！");
        }

        //调用匹配数据方法
        sapIndexOrderService.billMatchSap(detailMatchSapDataDtos);

        //更新明细匹配结果
        this.getBaseMapper().updateMatchSapData(detailMatchSapDataDtos);

        return "账单数据与外部验收数据匹配完成！";
    }

    public List<String> getBillOrderSnsByBillId(Long billId) {
        return settleShopBillDetailMapper.getBillOrderSnsByBillId(billId);
    }

    public List<ReconciliationBillDetailPdfVo> queryReconciliationPdfData(SettleShopBillDetailPageReqVo reqVo) {
        LoginUser loginUser = LocalUserHolder.get();
        reqVo.setReconciliationUserId(loginUser.getId());
        return this.getBaseMapper().queryReconciliationPdfData(reqVo);
    }

    public Integer isReconciliation() {
        LoginUser loginUser = LocalUserHolder.get();
        SystemUserRole userRole = systemUsersMapper.getUserRoleByUserId(loginUser.getId(), "Reconcilier-BZSHOP");
        if (userRole == null) {
            return 0;
        } else {
            return 1;
        }
    }

    @Transactional
    public void billBackByAfterSale(List<BillBackAfterSaleDto> billBackAfterSaleDtos) {
        log.info("售后调用入参：{}", JSONUtil.toJsonStr(billBackAfterSaleDtos));
        if (CollectionUtils.isEmpty(billBackAfterSaleDtos)) {
            log.info("售后明细为空！，时间：{}，操作人：{}", DateUtils.format(new Date(), DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND), LocalUserHolder.get() == null ? "system_yfl" : LocalUserHolder.get().getUsername());
            return;
        }

        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleBillPool::getDeliveryDetailId,billBackAfterSaleDtos.stream().map(BillBackAfterSaleDto::getDeliveryDetailId).collect(Collectors.toList()));
        List<SettleBillPool> billPools = settleBillPoolService.list(queryWrapper);

        //友服务的售后 只考虑东风商城
        if (CollectionUtils.isEmpty(billPools)) {
            log.info("未查询到账单池数据，时间：{}，入参：{}，操作人：{}", DateUtils.format(new Date(), DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND),
                    billBackAfterSaleDtos, LocalUserHolder.get() == null ? "system_yfl" : LocalUserHolder.get().getUsername());
            //发货还没入账单池 历史包裹
            Map<String, BigDecimal> returnMap = billBackAfterSaleDtos.stream().collect(Collectors.toMap(BillBackAfterSaleDto::getOrderDetailId, BillBackAfterSaleDto::getAfterSaleNum));
            shopReturnService.updateOrderDetails(returnMap);
            return;
        }

        Map<Long, List<SettleBillPool>> billMap = billPools.stream().collect(Collectors.groupingBy(SettleBillPool::getDeliveryDetailId));

        for (BillBackAfterSaleDto billBackAfterSaleDto : billBackAfterSaleDtos) {
            List<SettleBillPool> settleBillPoolList = billMap.get(billBackAfterSaleDto.getDeliveryDetailId());

            if (CollectionUtils.isEmpty(settleBillPoolList)) {
                throw new ParameterException("未查询到订单为:{},SKU:为：{}的账单数据，请联系管理员！", billBackAfterSaleDto.getOrderNumber(), billBackAfterSaleDto.getGoodsSku());
            }

            if (settleBillPoolList.size() > 1) {
                log.info("存在多条验收明细,池ids：{}", settleBillPoolList.stream().map(SettleBillPool::getId).collect(Collectors.toList()));
                throw new ParameterException("订单号为：{}的sku:{}存在多条验收数据,请联系管理员处理！", billBackAfterSaleDto.getOrderNumber(), billBackAfterSaleDto.getGoodsSku());
            }

            SettleBillPool billPool = settleBillPoolList.get(0);

            if (billBackAfterSaleDto.getAfterSaleNum().compareTo(billPool.getCheckedNum()) > 0) {
                throw new ParameterException("订单号：{}的sku:{}的售后数量大于出账数量,请检查数据!", billBackAfterSaleDto.getOrderNumber(), billBackAfterSaleDto.getGoodsSku());
            }

            BigDecimal add = billPool.getReturnNum().add(billBackAfterSaleDto.getAfterSaleNum());
            if (add.compareTo(billPool.getDeliveryNum()) > 0) {
                throw new ParameterException("订单号：{}的sku:{}的售后总数量大于包裹的发货的数量,请检查数据！", billBackAfterSaleDto.getOrderNumber(), billBackAfterSaleDto.getGoodsSku());
            }

            settleShopBillDetailService.billReturnByAfterSale(billBackAfterSaleDto, billPool);
        }


    }

    /**
     * 订单售后账单处理
     *
     * @param billBackAfterSaleDto
     * @param billPool
     */
    @Transactional
    public void billReturnByAfterSale(BillBackAfterSaleDto billBackAfterSaleDto, SettleBillPool billPool) {
        log.info("进入东风商城账单售后，入参：{}", JSONUtil.toJsonStr(billBackAfterSaleDto));

        //更新账单池
        Boolean allFlag = isAllAfterSale(billPool, billBackAfterSaleDto.getAfterSaleNum());

        if (isCompanyNotAccount(billPool)) {
            log.info("进入售后,企业未出账");
            //企业未出账
            handleCompanyNotOutAccount(billPool, allFlag, billBackAfterSaleDto.getAfterSaleNum());
        } else {
            // 企业已出账
            log.info("进入售后,企业已出账");
            handleCompanyHaveOutAccount(billPool, allFlag, billBackAfterSaleDto.getAfterSaleNum());
        }

    }

    /**
     * 企业已出账情况
     *
     * @param billPool 售后账单池数据
     * @param allFlag  整个明细售后标记
     * @param afterSaleNum 售后数量
     */
    @Transactional
    public void handleCompanyHaveOutAccount(SettleBillPool billPool, Boolean allFlag, BigDecimal afterSaleNum) {
        if (isSupplierOutAccount(billPool)) {
            //企业出账&供应商已出账
            log.info("售后进入 企业&供应商都已经出账");
            handleSupplierOutAccountCaseForCompanyHaveOut(billPool, allFlag, afterSaleNum);
        } else {
            //企业出账&供应商未出账
            log.info("售后进入 企业已经出账，供应商未出账");
            handleSupplierNotOutAccountCaseForCompanyHaveOut(billPool, allFlag, afterSaleNum);
        }
    }

    /**
     * 处理客户出账，供应商未出账的情况
     * @param billPool
     * @param allFlag
     * @param afterSaleNum
     */
    @Transactional
    public void handleSupplierNotOutAccountCaseForCompanyHaveOut(SettleBillPool billPool, Boolean allFlag, BigDecimal afterSaleNum) {
        BillAfterSaleDto companyBillAfterSaleDto = settleShopBillService.getBaseMapper().getBillAfterDto(billPool.getId(), BillCustomerTypeEnum.COMPANY.getCode());

        companyForbiddenAfterSaleValid(companyBillAfterSaleDto, billPool);

        if (isCompanyPartialInvoiceFlag(companyBillAfterSaleDto.getReconciliationStatus())) {
            log.info("企业部分开票售后");
            //账单明细部分开票，但是客户整个明细售后，不允许
            companyPartialInvoiceValid(allFlag, billPool, companyBillAfterSaleDto, afterSaleNum);

            //剩余可开票数量 >=售后数量 客户侧进行数据整单更新，供应商侧未出账，只需更新账单池数据
            companyPartialOutAndSupplierNotOutProcess(companyBillAfterSaleDto, afterSaleNum, billPool, Boolean.TRUE);

        } else {
            //剩余状态为开票前 处理客户账单和供应商账单池数据
            log.info("企业开票前售后");
            if (allFlag) {
                log.info("整单售后");
                //客户删除账单明细，供应商没出帐，只用更新池数据就可以了
                adjustBillPoolOnPartialSettlement(companyBillAfterSaleDto, billPool, afterSaleNum);
            } else {
                //客户部分售后,供应商修改池数据
                log.info("部分售后");
                companyPartialOutAndSupplierNotOutProcess(companyBillAfterSaleDto, afterSaleNum, billPool, Boolean.FALSE);
            }
        }
    }

    /**
     * 企业部分开票，对售后数据进行校验
     * @param allFlag
     * @param billPool
     * @param companyBillAfterSaleDto
     * @param afterSaleNum
     */
    private void companyPartialInvoiceValid(Boolean allFlag, SettleBillPool billPool, BillAfterSaleDto companyBillAfterSaleDto, BigDecimal afterSaleNum) {
        if (allFlag) {
            log.info("订单：{},sku：{}，账单状态：{}，账单明细部分开票，现需要售后全部，不满足售后条件", billPool.getOrderNumber(), billPool.getGoodsSku(), companyBillAfterSaleDto.getBillStatus());
            throw new ParameterException("商品sku:{}已存在部分开票数据，暂无法全部售后，请联系运营红冲发票后再进行售后！", billPool.getGoodsSku());
        }

        //部分开票的 如果剩余开票数量<剩余未开票的 不允许售后
        if (companyBillAfterSaleDto.getInvoicableQuantity().compareTo(afterSaleNum) < 0) {
            //剩余开票数量<售后数量
            log.info("订单：{},sku：{}，可开票数量：{}，售后数量：{}，不满足售后条件", billPool.getOrderNumber(), billPool.getGoodsSku(), companyBillAfterSaleDto.getAfterSaleNum(), afterSaleNum);
            throw new ParameterException("商品sku:{}存在部分开票，且未开票数量小于售后数量，请联系运营红冲发票后再进行售后！", billPool.getGoodsSku());
        }
    }

    /**
     * 客户出账情况下
     * 校验客户侧账单是否可以进行售后
     * @param companyBillAfterSaleDto
     * @param billPool
     */
    public void companyForbiddenAfterSaleValid(BillAfterSaleDto companyBillAfterSaleDto, SettleBillPool billPool) {
        if (ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus()) ||
                ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus())) {
            //待开票&部分开票 人工介入运维 不需要系统处理
            companyForbiddenAfterSale(billPool, companyBillAfterSaleDto);
            log.info("订单：{},sku：{}，账单状态：{}，不满足售后条件", billPool.getOrderNumber(), billPool.getGoodsSku(), companyBillAfterSaleDto.getBillStatus());
            throw new ParameterException("商品sku:{}已存在开票数据，暂无法售后，请联系运营驳回发票或红冲！", billPool.getGoodsSku());
        }
    }

    /**
     * 开票前 供应商侧或者客户侧某一侧在账单，需要删除，另一侧在账单池中需要更新池数据
     * @param billAfterSaleDto
     * @param billPool
     * @param afterSaleNum
     */
    @Transactional
    public void adjustBillPoolOnPartialSettlement(BillAfterSaleDto billAfterSaleDto, SettleBillPool billPool, BigDecimal afterSaleNum) {
        deleteShopBillDetailForAllAfterSale(billAfterSaleDto.getBillId(),billAfterSaleDto.getDetailId(),billPool.getId());

        SettleBillPool updateBillPool = new SettleBillPool();
        updateBillPool.setId(billPool.getId());
        updateBillPool.setSupplierTotalPriceNaked(BigDecimal.ZERO);
        updateBillPool.setSupplierTotalPriceTax(BigDecimal.ZERO);
        updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
        updateBillPool.setCheckedNum(BigDecimal.ZERO);
        updateBillPool.setSupplierCheckedNum(BigDecimal.ZERO);
        updateBillPool.setGoodsTotalPriceTax(BigDecimal.ZERO);
        updateBillPool.setGoodsTotalPriceNaked(BigDecimal.ZERO);
        updateBillPool.setSupplierTotalPriceTax(BigDecimal.ZERO);
        updateBillPool.setSupplierTotalPriceNaked(BigDecimal.ZERO);
        updateBillPool.setUpdateTime(new Date());
        updateBillPool.setModifier("systemAfterSale");
        settleBillPoolService.updateById(updateBillPool);
        processUpdateOrderLife(billPool.getOrderDetailId(), afterSaleNum, BigDecimal.ZERO, BigDecimal.ZERO);
    }

    /**
     * 客户部分出&供应商未出 执行更新
     *
     * @param companyBillAfterSaleDto
     * @param afterSaleNum
     * @param billPool
     */
    public void companyPartialOutAndSupplierNotOutProcess(BillAfterSaleDto companyBillAfterSaleDto, BigDecimal afterSaleNum, SettleBillPool billPool, Boolean isCompanyInvoiceFlag) {
        log.info("企业部分开票，供应商未出账");
        BigDecimal newCheckNum = companyBillAfterSaleDto.getCheckedNum().subtract(afterSaleNum);

        RowPriceDtoForPriceMode rowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                billPool.getGoodsUnitPriceNaked(), billPool.getGoodsUnitPriceTax(), BillCustomerTypeEnum.COMPANY.getCode(), billPool.getPricingMode(), newCheckNum);

        companyPartialAfterSaleProcessUpdateForBill(companyBillAfterSaleDto, afterSaleNum, isCompanyInvoiceFlag,rowPriceDtoForPriceMode);

        companyPartialAfterSaleProcessUpdateForBillPool(billPool, newCheckNum, rowPriceDtoForPriceMode,afterSaleNum, isCompanyInvoiceFlag, companyBillAfterSaleDto, Boolean.TRUE);


        BigDecimal reconciliationNum = BigDecimal.ZERO;
        if (!ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus()) &&
                !ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus())) {
            reconciliationNum = newCheckNum;
        }
        processUpdateOrderLife(billPool.getOrderDetailId(), afterSaleNum, newCheckNum, reconciliationNum);

    }

    /**
     * 客户侧出账，账单池数据更新
     * 供应商侧未出账则更新，已结算则不更新
     *
     * @param billPool 售后账单池数据
     * @param newCheckNum 新的出账数量
     * @param companyRowPriceDtoForPriceMode 客户行小计
     * @param afterSaleNum 售后数量
     * @param isCompanyInvoiceFlag 客户开票标记
     * @param companyBillAfterSaleDto 客户售后对象
     * @param isSupplierUpdateFlag 供应商是否更新标记
     */
    @Transactional
    public void companyPartialAfterSaleProcessUpdateForBillPool(SettleBillPool billPool, BigDecimal newCheckNum,
                                                                RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode, BigDecimal afterSaleNum,
                                                                Boolean isCompanyInvoiceFlag, BillAfterSaleDto companyBillAfterSaleDto,
                                                                Boolean isSupplierUpdateFlag) {
        SettleBillPool updateBillPool = new SettleBillPool();
        updateBillPool.setId(billPool.getId());
        updateBillPool.setCheckedNum(newCheckNum);

        updateBillPool.setGoodsTotalPriceTax(companyRowPriceDtoForPriceMode.getTotalPriceTax());
        updateBillPool.setGoodsTotalPriceNaked(companyRowPriceDtoForPriceMode.getTotalPriceNaked());
        updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
        updateBillPool.setUpdateTime(new Date());
        updateBillPool.setModifier("systemAfterSale");

        QueryWrapper<SettleBillLifeCycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleBillLifeCycle::getSettleBillPoolId, billPool.getId());
        SettleBillLifeCycle one = settleBillLifeCycleService.getOne(queryWrapper);
        SettleBillLifeCycle updateBillLifeCycle = new SettleBillLifeCycle();
        updateBillLifeCycle.setId(one.getId());
        updateBillLifeCycle.setCustomerCheckoutNum(newCheckNum);
        updateBillLifeCycle.setCustomerReconciliationNum(one.getCustomerReconciliationNum().compareTo(BigDecimal.ZERO) > 0 ? newCheckNum : one.getCustomerReconciliationNum());

        if (isSupplierUpdateFlag) {
            //供应商账单未出情况下 需要更新
            BigDecimal newSupplierCheckNum = billPool.getSupplierCheckedNum().subtract(afterSaleNum);
            updateBillPool.setSupplierCheckedNum(newSupplierCheckNum);

            RowPriceDtoForPriceMode rowPriceDtoForPriceModeForSupplier = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                    billPool.getSupplierUnitPriceNaked(), billPool.getSupplierUnitPriceTax(), BillCustomerTypeEnum.SUPPLIER.getCode(), billPool.getPricingMode(), newSupplierCheckNum);
            updateBillPool.setSupplierTotalPriceTax(rowPriceDtoForPriceModeForSupplier.getTotalPriceTax());
            updateBillPool.setSupplierTotalPriceNaked(rowPriceDtoForPriceModeForSupplier.getTotalPriceNaked());

            updateBillLifeCycle.setSupplierCheckoutNum(one.getSupplierCheckoutNum().compareTo(BigDecimal.ZERO) > 0 ? newCheckNum : one.getSupplierCheckoutNum());

            updateBillLifeCycle.setSupplierReconciliationNum(one.getSupplierReconciliationNum().compareTo(BigDecimal.ZERO) > 0 ? newCheckNum : one.getSupplierReconciliationNum());

        }

        settleBillPoolService.updateById(updateBillPool);

        if (isCompanyInvoiceFlag) {
            updateBillLifeCycle.setCustomerReconciliationNum(newCheckNum);
            updateBillLifeCycle.setCustomerInvoicingStatus(companyBillAfterSaleDto.getInvoicableQuantity().compareTo(afterSaleNum) == 0 ?
                    ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode() : ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode());
            //企业开票了 对应的票有可能结算了

        }else {
            if(ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus())){
                updateBillLifeCycle.setCustomerReconciliationNum(newCheckNum);
            }
        }

        //更新账单生命周期
        settleBillLifeCycleService.updateById(updateBillLifeCycle);
    }


    /**
     * 开票前&部分开票
     * 供应商&客户账单需要更新
     * 企业账单数据更新
     *
     * @param billAfterSaleDto     售后账单对象
     * @param afterSaleNum         售后数量
     * @param isCompanyInvoiceFlag 企业开票标记
     */
    @Transactional
    public void companyPartialAfterSaleProcessUpdateForBill(BillAfterSaleDto billAfterSaleDto, BigDecimal afterSaleNum, Boolean isCompanyInvoiceFlag, RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode) {
        SettleShopBillDetail updateShopBillDetail = new SettleShopBillDetail();
        updateShopBillDetail.setDetailId(billAfterSaleDto.getDetailId());
        updateShopBillDetail.setCheckedNum(billAfterSaleDto.getCheckedNum().subtract(afterSaleNum));

        if (isCompanyInvoiceFlag) {
            //客户开票了 可开票数据有数值
            updateShopBillDetail.setReconciliationStatus(billAfterSaleDto.getInvoicableQuantity().compareTo(afterSaleNum) == 0 ?
                    ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode() : ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode());
        }

        //可开票数量有数值的
        if (billAfterSaleDto.getInvoicableQuantity().compareTo(BigDecimal.ZERO) > 0) {
            //账单状态是已确认以后的
            updateShopBillDetail.setInvoicableQuantity(billAfterSaleDto.getInvoicableQuantity().subtract(afterSaleNum));
        }

        updateShopBillDetail.setTotalPriceTax(companyRowPriceDtoForPriceMode.getTotalPriceTax());
        updateShopBillDetail.setTotalPriceNaked(companyRowPriceDtoForPriceMode.getTotalPriceNaked());
        settleShopBillDetailService.updateById(updateShopBillDetail);
        //更新账单数据
        SettleShopBill settleShopBill = settleShopBillService.getById(billAfterSaleDto.getBillId());
        settleShopBillService.updateShopBillMoney(settleShopBill);
    }

    /**
     * 企业&供应商都出帐了
     *
     * @param billPool
     * @param allFlag
     * @param afterSaleNum
     */
    @Transactional
    public void handleSupplierOutAccountCaseForCompanyHaveOut(SettleBillPool billPool, Boolean allFlag, BigDecimal afterSaleNum) {
        //客户是否已经开票了
        BillAfterSaleDto companyBillAfterSaleDto = settleShopBillService.getBaseMapper().getBillAfterDto(billPool.getId(), BillCustomerTypeEnum.COMPANY.getCode());

        companyForbiddenAfterSaleValid(companyBillAfterSaleDto, billPool);

        //企业部分开票情况
        if (isCompanyPartialInvoiceFlag(companyBillAfterSaleDto.getReconciliationStatus())) {
            log.info("企业&供应商都出帐，且企业处于部分开票状态");
            //企业部分开票 数据进行校验 是否允许售后
            companyPartialInvoiceValid(allFlag, billPool, companyBillAfterSaleDto, afterSaleNum);

            //可开票数量》=售后数量，接下来重点是供应商是否已结算，供应商已结算，需要生成一个新的负向账单明细
            companyPartialSettleAndSupplierHadOutProcess(companyBillAfterSaleDto, billPool, afterSaleNum, Boolean.TRUE);

        } else {
            //开票前的状态
            log.info("企业&供应商都出帐,且企业处于开票前状态");
            if (allFlag) {
                //客户删除，但是供应商需要判断是否已结算：未结算->同步删除;已结算->生成新的负向账单明细
                allAfterSaleBeforeInvoiceForCompanyHaveOutAndSupplierOut(companyBillAfterSaleDto, billPool, afterSaleNum);
            } else {
                // 部分售后
                //客户账单更新数据，供应商需要判断是否已结算：未结算->同步更新;已结算->生成新的负向账单明细
                companyPartialSettleAndSupplierHadOutProcess(companyBillAfterSaleDto,billPool,afterSaleNum,Boolean.FALSE);

            }

        }
    }


    /**
     * 客户&供应商 同步更新账单池数据
     *
     * @param companyBillAfterSaleDto  企业售后对象
     * @param afterSaleNum             售后数量
     * @param billPool                 账单池数据
     * @param supplierBillAfterSaleDto 供应商售后对象
     */
    @Transactional
    public void companyAndSupplierUpdateBillAndPoolForPartial(BillAfterSaleDto companyBillAfterSaleDto, BigDecimal afterSaleNum, SettleBillPool billPool, BillAfterSaleDto supplierBillAfterSaleDto) {
        BigDecimal newCheckNum = companyBillAfterSaleDto.getCheckedNum().subtract(afterSaleNum);

        RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                billPool.getGoodsUnitPriceNaked(), billPool.getGoodsUnitPriceTax(), BillCustomerTypeEnum.COMPANY.getCode(), billPool.getPricingMode(), newCheckNum);

        RowPriceDtoForPriceMode supplierRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                billPool.getSupplierUnitPriceNaked(), billPool.getSupplierUnitPriceTax(), BillCustomerTypeEnum.SUPPLIER.getCode(), billPool.getPricingMode(), newCheckNum);

        // 客户侧更新，供应商侧也需要更新
        companyPartialAfterSaleProcessUpdateForBill(companyBillAfterSaleDto, afterSaleNum, Boolean.FALSE, companyRowPriceDtoForPriceMode);
        // 供应商侧账单更新
        companyPartialAfterSaleProcessUpdateForBill(supplierBillAfterSaleDto, afterSaleNum, Boolean.FALSE, supplierRowPriceDtoForPriceMode);
        //更新账单池数据
        companyPartialAfterSaleProcessUpdateForBillPool(billPool, newCheckNum, companyRowPriceDtoForPriceMode, afterSaleNum, Boolean.FALSE, companyBillAfterSaleDto, Boolean.TRUE);

    }


    /**
     * 企业出账，开票前,供应商已出账(结算/未结算)
     * 整单售后
     *
     * @param companyBillAfterSaleDto
     * @param billPool
     * @param afterSaleNum
     */
    @Transactional
    public void allAfterSaleBeforeInvoiceForCompanyHaveOutAndSupplierOut(BillAfterSaleDto companyBillAfterSaleDto, SettleBillPool billPool, BigDecimal afterSaleNum) {
        log.info("包裹全部售后,开票前，企业&供应商都已经出账");
        //判断供应商是否已经结算
        BillAfterSaleDto supplierBillAfterSaleDto = settleShopBillService.getBaseMapper().getBillAfterDto(billPool.getId(), BillCustomerTypeEnum.SUPPLIER.getCode());

        //客户账单删除
        deleteShopBillDetailForAllAfterSale(companyBillAfterSaleDto.getBillId(), companyBillAfterSaleDto.getDetailId(), billPool.getId());

        SettleBillPool updateBillPool = new SettleBillPool();
        updateBillPool.setId(billPool.getId());
        updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
        updateBillPool.setCheckedNum(BigDecimal.ZERO);
        updateBillPool.setGoodsTotalPriceTax(BigDecimal.ZERO);
        updateBillPool.setGoodsTotalPriceNaked(BigDecimal.ZERO);
        if (isSupplierSettleFlag(supplierBillAfterSaleDto.getBillStatus())) {

            //供应商账单-结算后 生成新帐单
            handleSupplierSettleCase(billPool, afterSaleNum);

        } else {
            //供应商账单-结算前 同步删除
            deleteShopBillDetailForAllAfterSale(supplierBillAfterSaleDto.getBillId(), supplierBillAfterSaleDto.getDetailId(), billPool.getId());
            updateBillPool.setSupplierCheckedNum(BigDecimal.ZERO);
            updateBillPool.setSupplierTotalPriceTax(BigDecimal.ZERO);
            updateBillPool.setSupplierTotalPriceNaked(BigDecimal.ZERO);
        }

        //更新账单池数据
        settleBillPoolService.updateById(updateBillPool);

        //更新生命周期
        processUpdateOrderLife(billPool.getOrderDetailId(), afterSaleNum, BigDecimal.ZERO, BigDecimal.ZERO);

    }


    /**
     * 企业&供应商
     * 删除账单明细-整单售后
     *
     * @param billId 账单id
     * @param detailId 售后账单明细id
     * @param billPoolId 售后账单池id
     */
    @Transactional
    public void deleteShopBillDetailForAllAfterSale(Long billId, Long detailId, Long billPoolId) {
        SettleShopBill settleShopBill = settleShopBillService.getById(billId);
        SettleShopBillDetail settleShopBillDetail = settleShopBillDetailService.getById(detailId);
        log.info("供应商||企业 删除账单售后明细，账单编号：{},售后明细id:{}",settleShopBill.getBillSn(),settleShopBillDetail.getDetailId());
        settleShopBillService.deleteFromBill(settleShopBill, Collections.singletonList(settleShopBillDetail), Collections.singletonList(billPoolId), "订单售后-整单", BillOperateLogTypeEnum.DELETE_BILL.getCode());
    }

    /**
     * 客户侧是部分开票
     * 企业出账&供应商也出帐，部分售后
     * 供应商已结算了->生成新的负向账单
     * 供应商未结算->更新账单明细
     *
     * @param companyBillAfterSaleDto  客户售后对象
     * @param billPool  原池数据
     * @param afterSaleNum  售后数量
     * @param isCompanyInvoiceFlag  客户是否开票
     */
    @Transactional
    public void companyPartialSettleAndSupplierHadOutProcess(BillAfterSaleDto companyBillAfterSaleDto, SettleBillPool billPool, BigDecimal afterSaleNum, Boolean isCompanyInvoiceFlag) {
        log.info("部分售后");
        BigDecimal newCheckNum = companyBillAfterSaleDto.getCheckedNum().subtract(afterSaleNum);

        RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                billPool.getGoodsUnitPriceNaked(), billPool.getGoodsUnitPriceTax(), BillCustomerTypeEnum.COMPANY.getCode(), billPool.getPricingMode(), newCheckNum);

        //判断供应商是否已经结算
        BillAfterSaleDto supplierBillAfterSaleDto = settleShopBillService.getBaseMapper().getBillAfterDto(billPool.getId(), BillCustomerTypeEnum.SUPPLIER.getCode());

        if (isSupplierSettleFlag(supplierBillAfterSaleDto.getBillStatus())) {
            log.info("供应商已结算，生成新帐单");
            //供应商已结算 供应商侧出一个新的负帐单明细
            handleSupplierSettleCase(billPool, afterSaleNum);
            //更新客户账单
            companyPartialAfterSaleProcessUpdateForBill(companyBillAfterSaleDto, afterSaleNum, isCompanyInvoiceFlag, companyRowPriceDtoForPriceMode);
            //更新客户原账单池和生命周期数据，供应商侧不变
            companyPartialAfterSaleProcessUpdateForBillPool(billPool, newCheckNum, companyRowPriceDtoForPriceMode, afterSaleNum, isCompanyInvoiceFlag, companyBillAfterSaleDto, Boolean.FALSE);
        } else {
            //供应商未结算
            log.info("供应商未结算，进行售后数据更新");
            companyAndSupplierUpdateBillAndPoolForPartial(companyBillAfterSaleDto,afterSaleNum,billPool,supplierBillAfterSaleDto);
        }

        BigDecimal reconciliationNum = BigDecimal.ZERO;
        if (!ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus()) &&
                !ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode().equals(companyBillAfterSaleDto.getReconciliationStatus())) {
            reconciliationNum = newCheckNum;
        }
        //更新生命周期
        processUpdateOrderLife(billPool.getOrderDetailId(), afterSaleNum, newCheckNum, reconciliationNum);

    }

    /**
     * 企业未出账情况
     *
     * @param billPool
     * @param allAfterSaleFlag
     * @param afterSaleNum
     */
    @Transactional
    public void handleCompanyNotOutAccount(SettleBillPool billPool, Boolean allAfterSaleFlag, BigDecimal afterSaleNum) {
        if (isSupplierOutAccount(billPool)) {
            //企业未出&供应商出账了
            handleSupplierOutAccountCaseForCompanyNotOut(billPool, allAfterSaleFlag, afterSaleNum);
        } else {
            //企业&供应商 未出账
            handleSupplierNotOutAccountCaseForCompanyNotOut(billPool, allAfterSaleFlag, afterSaleNum);
        }
    }

    @Transactional
    public void processUpdateOrderLife(String orderDetailId,BigDecimal afterSaleNum,BigDecimal checkNum,BigDecimal reconciliationNum) {
        UpdateOrderDetailForBillDto updateOrderDetailForBillDto = new UpdateOrderDetailForBillDto();
        updateOrderDetailForBillDto.setOrderSalesChannel(OrderSalesChannelEnum.DFMALL.getCode());
        UpdateOrderDetailDto updateOrderDetailDto = new UpdateOrderDetailDto();
        updateOrderDetailDto.setOrderDetailId(orderDetailId);
        updateOrderDetailDto.setAfterSaleNum(afterSaleNum);
        updateOrderDetailDto.setCheckNum(checkNum);
        updateOrderDetailDto.setReconciliationNum(reconciliationNum);
        updateOrderDetailForBillDto.setUpdateOrderDetailDtoList(Collections.singletonList(updateOrderDetailDto));
        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
        orderLifeCycleStrategy.updatePurchaseOrderInfoForAfterSale(updateOrderDetailForBillDto);
    }

    /**
     * 企业未出&供应商未出
     *
     * @param billPool
     * @param allAfterSaleFlag
     * @param afterSaleNum
     */
    @Transactional
    public void handleSupplierNotOutAccountCaseForCompanyNotOut(SettleBillPool billPool, Boolean allAfterSaleFlag, BigDecimal afterSaleNum) {
        SettleBillPool updateBillPool = new SettleBillPool();
        updateBillPool.setId(billPool.getId());
        updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
        updateBillPool.setCheckedNum(billPool.getCheckedNum().subtract(afterSaleNum));
        updateBillPool.setSupplierCheckedNum(billPool.getSupplierCheckedNum().subtract(afterSaleNum));

        updateBillPool.setUpdateTime(new Date());
        updateBillPool.setModifier(LocalUserHolder.get() == null ? "systemAfterSale" : LocalUserHolder.get().getUsername());
        updateBillPool.setOrderDetailId(billPool.getOrderDetailId());

        if (allAfterSaleFlag) {
            //整单 更新池数据
            updateBillPool.setSupplierOutAccountState(BillPoolOutStatusEnum.NO_NEED_BILL.getCode());
            updateBillPool.setCompanyOutAccountState(BillPoolOutStatusEnum.NO_NEED_BILL.getCode());
            updateBillPool.setGoodsTotalPriceTax(BigDecimal.ZERO);
            updateBillPool.setGoodsTotalPriceNaked(BigDecimal.ZERO);
            updateBillPool.setSupplierTotalPriceNaked(BigDecimal.ZERO);
            updateBillPool.setSupplierTotalPriceTax(BigDecimal.ZERO);

        } else {
            RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                    billPool.getGoodsUnitPriceNaked(), billPool.getGoodsUnitPriceTax(), BillCustomerTypeEnum.COMPANY.getCode(),
                    billPool.getPricingMode(), updateBillPool.getCheckedNum());
            //修改金额 客户侧金额
            updateBillPool.setGoodsTotalPriceNaked(companyRowPriceDtoForPriceMode.getTotalPriceNaked());
            updateBillPool.setGoodsTotalPriceTax(companyRowPriceDtoForPriceMode.getTotalPriceNaked());

            RowPriceDtoForPriceMode supplierRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                    billPool.getSupplierUnitPriceNaked(), billPool.getSupplierUnitPriceTax(), BillCustomerTypeEnum.SUPPLIER.getCode(),
                    billPool.getPricingMode(), updateBillPool.getSupplierCheckedNum());
            // 供应商侧金额
            updateBillPool.setSupplierTotalPriceNaked(supplierRowPriceDtoForPriceMode.getTotalPriceNaked());
            updateBillPool.setSupplierTotalPriceTax(supplierRowPriceDtoForPriceMode.getTotalPriceTax());
        }

        settleBillPoolService.updateById(updateBillPool);
        //触发生命周期更新
        processUpdateOrderLife(billPool.getOrderDetailId(), afterSaleNum, BigDecimal.ZERO, BigDecimal.ZERO);

    }

    /**
     * 企业未出&供应商已出
     *
     * @param billPool
     * @param allAfterSaleFlag
     */
    @Transactional
    public void handleSupplierOutAccountCaseForCompanyNotOut(SettleBillPool billPool, Boolean allAfterSaleFlag,BigDecimal afterSaleNum) {
        BillAfterSaleDto billAfterSaleDto = settleShopBillService.getBaseMapper().getBillAfterDto(billPool.getId(), BillCustomerTypeEnum.SUPPLIER.getCode());
        if (isSupplierSettleFlag(billAfterSaleDto.getBillStatus())) {
            //供应商已结算处理
            handleSupplierSettleCase(billPool, afterSaleNum);

            SettleBillPool updateBillPool = new SettleBillPool();
            updateBillPool.setId(billPool.getId());
            updateBillPool.setModifier("systemAfterSale");
            updateBillPool.setUpdateTime(new Date());
            updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
            updateBillPool.setCheckedNum(billPool.getCheckedNum().subtract(afterSaleNum));

            if(allAfterSaleFlag){
                updateBillPool.setGoodsTotalPriceNaked(BigDecimal.ZERO);
                updateBillPool.setGoodsTotalPriceTax(BigDecimal.ZERO);
            }else {

                RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                        billPool.getGoodsUnitPriceNaked(), billPool.getGoodsUnitPriceTax(),
                        BillCustomerTypeEnum.COMPANY.getCode(), billPool.getPricingMode(), updateBillPool.getCheckedNum());

                //修改金额 客户侧金额
                updateBillPool.setGoodsTotalPriceNaked(companyRowPriceDtoForPriceMode.getTotalPriceNaked());
                updateBillPool.setGoodsTotalPriceTax(companyRowPriceDtoForPriceMode.getTotalPriceTax());
            }

            settleBillPoolService.updateById(updateBillPool);

            //更新生命周期
            processUpdateOrderLife(billPool.getOrderDetailId(),afterSaleNum,BigDecimal.ZERO,BigDecimal.ZERO);

        } else {
            //未结算处理逻辑
            handleSupplierNotSettleCase(billPool, allAfterSaleFlag, billAfterSaleDto,afterSaleNum);
        }
    }

    /**
     * 供应商侧已结算，生成负向账单池明细
     * 并指派到最新的账单里面
     * @param billPool
     * @param afterSaleNum
     */
    @Transactional
    public void handleSupplierSettleCase(SettleBillPool billPool, BigDecimal afterSaleNum) {
        log.info("供应商账单已结算，在新帐单中生成负向数据");
        // 供应商结算了 生成新的供应商账单，客户侧归零
        SettleBillPool supplierBillPool = DataAdapter.convert(billPool, SettleBillPool.class);
        //使用自增的
        supplierBillPool.setId(null);
        supplierBillPool.setDeliveryDetailId(0L);
        supplierBillPool.setCheckFormDetailId(0L);
        supplierBillPool.setSupplierCheckedNum(afterSaleNum.negate());
        supplierBillPool.setCheckedNum(BigDecimal.ZERO);
        supplierBillPool.setReturnNum(afterSaleNum);
        supplierBillPool.setCreator("systemAfterSale");
        supplierBillPool.setCreateTime(new Date());
        supplierBillPool.setCompanyOutAccountState(BillPoolOutStatusEnum.NO_NEED_BILL.getCode());
        supplierBillPool.setSupplierOutAccountState(BillPoolOutStatusEnum.UN_BILLED.getCode());
        supplierBillPool.setGoodsTotalPriceNaked(BigDecimal.ZERO);
        supplierBillPool.setGoodsTotalPriceTax(BigDecimal.ZERO);

        RowPriceDtoForPriceMode rowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(),
                billPool.getSupplierUnitPriceNaked(), billPool.getSupplierUnitPriceTax(), BillCustomerTypeEnum.SUPPLIER.getCode(), billPool.getPricingMode(), supplierBillPool.getSupplierCheckedNum());

        supplierBillPool.setSupplierTotalPriceNaked(rowPriceDtoForPriceMode.getTotalPriceNaked());
        supplierBillPool.setSupplierTotalPriceTax(rowPriceDtoForPriceMode.getTotalPriceTax());
        supplierBillPool.setSupplierPostage(BigDecimal.ZERO);
        supplierBillPool.setCompanyPostage(BigDecimal.ZERO);
        //保存池数据
        settleBillPoolService.save(supplierBillPool);
        //保存新的生命周期
        settleBillLifeCycleService.saveLifeCycleFromPools(Collections.singletonList(supplierBillPool));

        //出账
        String yearAndMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);
        settleShopBillService.realTimeCheckSupplierBill(Collections.singletonList(supplierBillPool), supplierBillPool.getTenantId(), yearAndMonth);
    }

    @Transactional
    public void handleSupplierNotSettleCase(SettleBillPool billPool, Boolean allAfterSaleFlag, BillAfterSaleDto billAfterSaleDto, BigDecimal afterSaleNum) {
        //对已有的账单明细数据进行处理 整单删除或者数据量更新
        if (allAfterSaleFlag) {
            adjustBillPoolOnPartialSettlement(billAfterSaleDto, billPool, afterSaleNum);
        } else {
            //部分的 就修改供应商账单明细
            SettleBillPool updateBillPool = new SettleBillPool();
            updateBillPool.setId(billPool.getId());
            updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
            updateBillPool.setCheckedNum(billPool.getCheckedNum().subtract(afterSaleNum));
            updateBillPool.setSupplierCheckedNum(billPool.getSupplierCheckedNum().subtract(afterSaleNum));

            RowPriceDtoForPriceMode companyRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(), billPool.getGoodsUnitPriceNaked(),
                    billPool.getGoodsUnitPriceTax(), BillCustomerTypeEnum.COMPANY.getCode(),
                    billPool.getPricingMode(), updateBillPool.getCheckedNum());
            //修改金额 客户侧金额
            updateBillPool.setGoodsTotalPriceNaked(companyRowPriceDtoForPriceMode.getTotalPriceNaked());
            updateBillPool.setGoodsTotalPriceTax(companyRowPriceDtoForPriceMode.getTotalPriceTax());

            RowPriceDtoForPriceMode supplierRowPriceDtoForPriceMode = OrderPoolPurchaseTypeUtils.calculateRowSubtotalForPriceMode(billPool.getTaxRate(), billPool.getSupplierUnitPriceNaked(),
                    billPool.getSupplierUnitPriceTax(), BillCustomerTypeEnum.SUPPLIER.getCode(),
                    billPool.getPricingMode(), updateBillPool.getSupplierCheckedNum());

            // 供应商侧金额
            updateBillPool.setSupplierTotalPriceNaked(supplierRowPriceDtoForPriceMode.getTotalPriceNaked());
            updateBillPool.setSupplierTotalPriceTax(supplierRowPriceDtoForPriceMode.getTotalPriceTax());
            updateBillPool.setUpdateTime(new Date());
            updateBillPool.setModifier(LocalUserHolder.get() == null ? "systemAfterSale" : LocalUserHolder.get().getUsername());
            settleBillPoolService.updateById(updateBillPool);
            orderDetailAfterSalePartial(billAfterSaleDto, updateBillPool, BillCustomerTypeEnum.SUPPLIER.getCode());
            processUpdateOrderLife(billPool.getOrderDetailId(), afterSaleNum, BigDecimal.ZERO, BigDecimal.ZERO);
        }

    }

    private boolean isSupplierOutAccount(SettleBillPool billPool) {
        return billPool.getSupplierOutAccountState()==20;
    }

    private boolean isCompanyNotAccount(SettleBillPool billPool) {
        return billPool.getCompanyOutAccountState() !=20;
    }

    private boolean isAllAfterSale(SettleBillPool billPool,BigDecimal aftersaleNum) {
        return billPool.getCheckedNum().compareTo(aftersaleNum)==0;
    }

    private boolean isCompanyPartialInvoiceFlag(Integer reconciliationStatus){
        return ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode().equals(reconciliationStatus);
    }

    private boolean isSupplierSettleFlag(Integer billStatus){
        return !BillStatusEnum.TO_BE_RECONCILED.getCode().equals(billStatus);
    }

    /**
     * 获取要更新的账单池对象
     *
     * @param billPool
     * @param afterSaleNum
     * @return
     */
    private SettleBillPool getAfterSaleUpdateBillPool(SettleBillPool billPool, BigDecimal afterSaleNum) {
        SettleBillPool updateBillPool = new SettleBillPool();
        updateBillPool.setId(billPool.getId());
        updateBillPool.setReturnNum(billPool.getReturnNum().add(afterSaleNum));
        updateBillPool.setCheckedNum(billPool.getCheckedNum().subtract(afterSaleNum));
        updateBillPool.setSupplierCheckedNum(billPool.getSupplierCheckedNum().subtract(afterSaleNum));
        updateBillPool.setUpdateTime(new Date());
        updateBillPool.setModifier(LocalUserHolder.get() == null ? "system_yfl" : LocalUserHolder.get().getUsername());
        updateBillPool.setOrderDetailId(billPool.getOrderDetailId());

        BigDecimal reallyTaxRate = new BigDecimal(billPool.getTaxRate()).multiply(new BigDecimal("0.01")).add(BigDecimal.ONE);
        //修改金额 客户侧金额
        updateBillPool.setGoodsTotalPriceNaked(billPool.getGoodsUnitPriceNaked().multiply(updateBillPool.getCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
        updateBillPool.setGoodsTotalPriceTax(billPool.getGoodsUnitPriceNaked().multiply(updateBillPool.getCheckedNum()).multiply(reallyTaxRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        // 供应商侧金额
        updateBillPool.setSupplierTotalPriceNaked(billPool.getSupplierUnitPriceNaked().multiply(updateBillPool.getSupplierCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
        updateBillPool.setSupplierTotalPriceTax(billPool.getSupplierUnitPriceTax().multiply(updateBillPool.getSupplierCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
        log.info("updateBillPool:{}", updateBillPool);
        return updateBillPool;
    }


    /**
     * 整单售后
     *
     * @param billAfterSaleDto
     * @param billPool
     */
    public void orderDetailAfterSale(BillAfterSaleDto billAfterSaleDto, SettleBillPool billPool) {
        SettleShopBill shopBill = settleShopBillService.getById(billAfterSaleDto.getBillId());
        SettleShopBillDetail settleShopBillDetail = this.getById(billAfterSaleDto.getDetailId());
        //整单 就直接删除
        settleShopBillService.deleteFromBill(shopBill, Collections.singletonList(settleShopBillDetail), Collections.singletonList(billPool.getId()), "订单售后-整单", BillOperateLogTypeEnum.DELETE_BILL.getCode());
        if (BillCustomerTypeEnum.COMPANY.getCode().equals(shopBill.getCustomerType())) {
            log.info("企业售后入参：{}", JSONUtil.toJsonStr(billAfterSaleDto));
            //因为上面删除账单已经更新了出账，对账数量，所以此处只需要赋值0就好了
            processUpdateOrderLife(billPool.getOrderDetailId(),billAfterSaleDto.getAfterSaleNum(),BigDecimal.ZERO,BigDecimal.ZERO);
        }
    }

    /**
     * 部分售后
     */
    @Transactional
    public void orderDetailAfterSalePartial(BillAfterSaleDto billAfterSaleDto, SettleBillPool updateBillPool, Integer customerType) {
        SettleShopBill shopBill = settleShopBillService.getById(billAfterSaleDto.getBillId());
        SettleShopBillDetail settleShopBillDetail = settleShopBillDetailService.getById(billAfterSaleDto.getDetailId());
        SettleShopBillDetail updateShopBillDetail = new SettleShopBillDetail();
        updateShopBillDetail.setDetailId(billAfterSaleDto.getDetailId());

        if(BillCustomerTypeEnum.SUPPLIER.getCode().equals(customerType)){
            updateShopBillDetail.setCheckedNum(updateBillPool.getSupplierCheckedNum());
            updateShopBillDetail.setTotalPriceNaked( updateBillPool.getSupplierTotalPriceNaked() );
            updateShopBillDetail.setTotalPriceTax(updateBillPool.getSupplierTotalPriceTax());
        }else {
            updateShopBillDetail.setCheckedNum(updateBillPool.getCheckedNum());
            updateShopBillDetail.setTotalPriceNaked(updateBillPool.getGoodsTotalPriceNaked());
            updateShopBillDetail.setTotalPriceTax(updateBillPool.getGoodsTotalPriceTax());
        }

        updateShopBillDetail.setUpdateTime(new Date());
        updateShopBillDetail.setModifier(LocalUserHolder.get() == null ? "system_yfl" : LocalUserHolder.get().getUsername());
        if (ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode().equals(billAfterSaleDto.getReconciliationStatus())) {
            updateShopBillDetail.setInvoicableQuantity(updateBillPool.getCheckedNum());
        }

        settleShopBillDetailService.updateById(updateShopBillDetail);

        // 修改账单
        settleShopBillService.updateShopBillMoney(shopBill);
        //修改生命周期
        QueryWrapper<SettleBillLifeCycle> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleBillLifeCycle::getSettleBillPoolId, updateBillPool.getId());
        SettleBillLifeCycle one = settleBillLifeCycleService.getOne(queryWrapper);

        if (BillCustomerTypeEnum.COMPANY.getCode().equals(customerType)) {
            one.setCustomerCheckoutNum(updateBillPool.getCheckedNum());
            one.setCustomerReconciliationNum(one.getCustomerReconciliationNum().compareTo(BigDecimal.ZERO) > 0 ? updateBillPool.getCheckedNum() : one.getCustomerReconciliationNum());

        } else {
            one.setSupplierCheckoutNum(updateBillPool.getSupplierCheckedNum());
            one.setSupplierReconciliationNum(one.getSupplierReconciliationNum().compareTo(BigDecimal.ZERO) > 0 ? updateBillPool.getSupplierCheckedNum() : one.getSupplierReconciliationNum());

        }

        settleBillLifeCycleService.updateById(one);

        if (BillCustomerTypeEnum.COMPANY.getCode().equals(shopBill.getCustomerType())) {

            processUpdateOrderLife(updateShopBillDetail.getOrderDetailId(),billAfterSaleDto.getAfterSaleNum(),settleShopBillDetail.getCheckedNum(),settleShopBillDetail.getInvoicableQuantity());

        }


    }

    private void supplierForbiddenAfterSale(SettleBillPool billPool) {
        log.info("电商账单已对账，不允许修改账单，发邮件通知管理员");
        List<SystemDictDataEntity> afterSaleEmails = systemDictDataMapper.selectListByDictType("after_sale_emails");
        List<String> emails = afterSaleEmails.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());
        mailService.sendEmail(MailService.SUPPLIER_FORBIDDEN_AFTER_SALE,
                mailService.supplierForbiddenAfterSaleTemplate(billPool.getOrderNumber(), billPool.getGoodsSku())
                , emails, null);
    }

    private void companyForbiddenAfterSale(SettleBillPool billPool, BillAfterSaleDto companyBillAfterSaleDto) {
        log.info("客户账单存在已开票，发邮件通知管理员人工处理");
        List<SystemDictDataEntity> afterSaleEmails = systemDictDataMapper.selectListByDictType("after_sale_emails");
        List<String> emails = afterSaleEmails.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());
        mailService.sendEmail(MailService.COMPANY_FORBIDDEN_AFTER_SALE,
                mailService.companyForbiddenAfterSaleTemplate(billPool.getOrderNumber(),
                        billPool.getGoodsSku(),
                        companyBillAfterSaleDto.getBillSn(),
                        ReconciliationStatusEnum.getReconciliationStatusName(companyBillAfterSaleDto.getReconciliationStatus()),
                        billPool.getCompanyName()
                ),
                emails,
                null);
    }

    /**
     * 根据验收明细获取准备售后的账单的状态
     */
    public List<BillStatusByAfterSaleDto> getBillStatusByAfterSale(List<Long> checkFormDetailIds) {
        log.info("查询准备售后的账单明细的入参：{}", checkFormDetailIds);
        if (CollectionUtils.isEmpty(checkFormDetailIds)) {
            return new ArrayList<>();
        }
        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleBillPool::getCheckFormDetailId, checkFormDetailIds);
        List<SettleBillPool> settleBillPools = settleBillPoolService.list(queryWrapper);
        if (CollectionUtils.isEmpty(settleBillPools)) {
            throw new ParameterException("未查询到相关账单数据信息！");
        }

        List<BillStatusByAfterSaleDto> billStatusByAfterSaleDtos = new ArrayList<>();
        Map<Long, SettleBillPool> billPoolMap = settleBillPools.stream().collect(Collectors.toMap(SettleBillPool::getCheckFormDetailId, Function.identity()));
        for (Long checkFormDetailId : checkFormDetailIds) {
            SettleBillPool billPool = billPoolMap.get(checkFormDetailId);
            if (billPool == null) {
                log.info("售后未查询到验收明细为：{}的池数据", checkFormDetailId);
                BillStatusByAfterSaleDto billStatusByAfterSaleDto = new BillStatusByAfterSaleDto();
                billStatusByAfterSaleDto.setCheckFormDetailId(checkFormDetailId);
                billStatusByAfterSaleDto.setReconciliationStatus(-2);
                billStatusByAfterSaleDtos.add(billStatusByAfterSaleDto);
                continue;
            }
            if (billPool.getCompanyOutAccountState() == 0) {
                //入池
                BillStatusByAfterSaleDto billStatusByAfterSaleDto = new BillStatusByAfterSaleDto();
                billStatusByAfterSaleDto.setCheckFormDetailId(checkFormDetailId);
                billStatusByAfterSaleDto.setReconciliationStatus(-1);
                billStatusByAfterSaleDto.setSettleBillPoolId(billPool.getId());
                billStatusByAfterSaleDtos.add(billStatusByAfterSaleDto);
            } else if (billPool.getCompanyOutAccountState() == 20) {
                //已出账
                SettleShopBillDetail one = settleShopBillDetailMapper.getSettleShopBillDetailByPoolId(billPool.getId(), BillCustomerTypeEnum.COMPANY.getCode());
                if (one == null) {
                    log.info("账单明细未查询到,池id:{},订单号：{}，商品SKU：{}", billPool.getId(), billPool.getOrderNumber(), billPool.getGoodsSku());
                    throw new ParameterException("账单明细数据异常！");
                }
                BillStatusByAfterSaleDto billStatusByAfterSaleDto = new BillStatusByAfterSaleDto();
                billStatusByAfterSaleDto.setBillDetailId(one.getDetailId());
                billStatusByAfterSaleDto.setSettleBillPoolId(billPool.getId());
                billStatusByAfterSaleDto.setCheckFormDetailId(checkFormDetailId);
                billStatusByAfterSaleDto.setReconciliationStatus(one.getReconciliationStatus());
                billStatusByAfterSaleDtos.add(billStatusByAfterSaleDto);
            } else {
                //无用数据 -99，66
                BillStatusByAfterSaleDto billStatusByAfterSaleDto = new BillStatusByAfterSaleDto();
                billStatusByAfterSaleDto.setSettleBillPoolId(billPool.getId());
                billStatusByAfterSaleDto.setCheckFormDetailId(checkFormDetailId);
                billStatusByAfterSaleDto.setReconciliationStatus(-2);
                billStatusByAfterSaleDtos.add(billStatusByAfterSaleDto);
            }
        }
        return billStatusByAfterSaleDtos;
    }

    /**
     * 友福利的售后-供应商
     *
     * @param billAfterSaleForYflDto 福利售后对象
     *                               <p>
     *                               1.先查看供应商账单是否有出账
     *                               2.有出账的 看是否已经结算 已结算不允许售后;没有出账的 进行供应商侧售后处理
     *                               3.看客户是否出账 有出账的 需要更新客户账单数据（积分和现金的比例划分） ；客户没出账的话，友福利的订单需要做对应的售后信息的变更，订单模块的提前处理
     *                               4.订单模块的数据处理调用我之前自行处理完成
     */
    @Transactional
    public void billAfterSaleForYflSupplier(BillAfterSaleForYflDto billAfterSaleForYflDto) {

        log.info("友福利的供应商售后明细入参：{}", billAfterSaleForYflDto);
        if (billAfterSaleForYflDto == null) {
            throw new ParameterException("售后处理账单入参不能为空！");
        }

        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleBillPool::getDeliveryDetailId, billAfterSaleForYflDto.getDeliveryDetailId())
                .eq(TenantBaseDO::getTenantId, yflYamlConfig.getTenantId());

        SettleBillPool settleBillPool = settleBillPoolService.getOne(queryWrapper);

        if (settleBillPool == null) {
            // 有可能是还没入池的
            return;
//            throw new ParameterException("未查询到帐单池数据！");
        }

        if (settleBillPool.getCheckedNum().compareTo(billAfterSaleForYflDto.getAfterSaleNum()) < 0) {
            throw new ParameterException("订单号：{}，商品编码：{}的售后数量大于出账数量，请检查数据！售后数量：{}，出账数量{}",
                    settleBillPool.getOrderNumber(), settleBillPool.getGoodsCode(), billAfterSaleForYflDto.getAfterSaleNum(), settleBillPool.getCheckedNum());
        }

        if (settleBillPool.getSupplierOutAccountState() == 0) {
            //未出账
            SettleBillPool updateSettleBillPool = new SettleBillPool();
            BigDecimal subtract = settleBillPool.getCheckedNum().subtract(billAfterSaleForYflDto.getAfterSaleNum());
            updateSettleBillPool.setId(settleBillPool.getId());
            if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                updateSettleBillPool.setSupplierOutAccountState(-99);
            }
            updateSettleBillPool.setReturnNum(settleBillPool.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));
            updateSettleBillPool.setCheckedNum(subtract);
            updateSettleBillPool.setSupplierCheckedNum(settleBillPool.getSupplierCheckedNum().subtract(billAfterSaleForYflDto.getAfterSaleNum()));
            //更新金额
            updateSettleBillPool.setSupplierTotalPriceTax(settleBillPool.getSupplierUnitPriceTax().multiply(updateSettleBillPool.getSupplierCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
            updateSettleBillPool.setSupplierTotalPriceNaked(settleBillPool.getSupplierUnitPriceNaked().multiply(updateSettleBillPool.getSupplierCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));

            Integer rate = settleBillPool.getTaxRate() == null ? 13 : settleBillPool.getTaxRate();
            BigDecimal reallyTaxRate = new BigDecimal(rate).multiply(new BigDecimal("0.01")).add(BigDecimal.ONE);
            updateSettleBillPool.setGoodsTotalPriceNaked(updateSettleBillPool.getCheckedNum().multiply(settleBillPool.getGoodsUnitPriceNaked()).setScale(2, BigDecimal.ROUND_HALF_UP));
            updateSettleBillPool.setGoodsTotalPriceTax(settleBillPool.getGoodsUnitPriceNaked().multiply(reallyTaxRate).multiply(updateSettleBillPool.getCheckedNum()).setScale(2, BigDecimal.ROUND_HALF_UP));
            log.info("供应商更新池数据：{}", updateSettleBillPool);
            settleBillPoolService.updateById(updateSettleBillPool);

        } else if (settleBillPool.getSupplierOutAccountState() == 20) {
            //已出账
            QueryWrapper<SettleShopBillDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(SettleShopBillDetail::getSettleBillPoolId, settleBillPool.getId())
                    .eq(SettleShopBillDetail::getPoolType, 1)
                    .eq(SettleShopBillDetail::getMallType, 2);
            SettleShopBillDetail settleShopBillDetail = settleShopBillDetailService.getOne(detailQueryWrapper);
            if (settleShopBillDetail == null) {
                throw new ParameterException("未查询到供应商账单明细数据！");
            }
            SettleShopBill settleShopBill = settleShopBillService.getById(settleShopBillDetail.getBillId());
            if (settleShopBill == null) {
                throw new ParameterException("账单供应商主数据信息有误！");
            }

            //判断电商账单是否结算，如果电商账单结算，不允许售后
            if (!BillStatusEnum.TO_BE_RECONCILED.getCode().equals(settleShopBill.getBillStatus())) {
                //电商账单已结算-最新月份生成一个负向账单
                handleSupplierSettleCase(settleBillPool, billAfterSaleForYflDto.getAfterSaleNum());
            } else {
                //未结算 处理账单明细数据
                BigDecimal checkedNum = settleShopBillDetail.getCheckedNum();
                BigDecimal afterSaleNum = billAfterSaleForYflDto.getAfterSaleNum();
                BigDecimal subtract = checkedNum.subtract(afterSaleNum);
                log.info("供应商 出账数量：{},售后数量，最终出账数量：{}", checkedNum, afterSaleNum, subtract);
                if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                    //整单售后
                    log.info("进入供应商整单售后");
                    settleShopBillService.deleteFromBill(settleShopBill, Collections.singletonList(settleShopBillDetail), Collections.singletonList(settleBillPool.getId()),
                            "友福利发起订单售后", BillOperateLogTypeEnum.DELETE_BILL.getCode());
                    //更新账单池数据
                    SettleBillPool updatePool = new SettleBillPool();
                    updatePool.setId(settleShopBillDetail.getSettleBillPoolId());
                    updatePool.setCheckedNum(subtract);
                    updatePool.setSupplierCheckedNum(subtract);
                    updatePool.setReturnNum(settleBillPool.getReturnNum().add(afterSaleNum));
                    settleBillPoolService.updateById(updatePool);
                } else if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    //部分售后
                    log.info("进入供应商部分售后");
                    SettleBillPool afterSaleUpdateBillPool = getAfterSaleUpdateBillPool(settleBillPool, billAfterSaleForYflDto.getAfterSaleNum());
                    BillAfterSaleDto billAfterSaleDto = new BillAfterSaleDto();
                    billAfterSaleDto.setBillId(settleShopBill.getBillId());
                    billAfterSaleDto.setBillSn(settleShopBill.getBillSn());
                    billAfterSaleDto.setDetailId(settleShopBillDetail.getDetailId());
                    billAfterSaleDto.setReconciliationStatus(settleShopBillDetail.getReconciliationStatus());
                    settleShopBillDetailService.orderDetailAfterSalePartial(billAfterSaleDto, afterSaleUpdateBillPool, settleShopBill.getCustomerType());
                    //修改账单池数据
                    settleBillPoolService.updateById(afterSaleUpdateBillPool);

                } else {
                    throw new ParameterException("订单号：{}，商品编码:{}的出账数量:{},售后数量：{}，售后数量大于出账数量，不允许售后！"
                            , settleShopBillDetail.getOrderNumber(), settleShopBillDetail.getGoodsCode(), settleShopBillDetail.getCheckedNum(), billAfterSaleForYflDto.getAfterSaleNum());
                }
            }
        }
    }

    /**
     * 友福利客户账单售后剔除
     *
     * @param billAfterSaleForYflDto
     */
    @Transactional
    public void billAfterSaleForYflCustomer(BillAfterSaleForYflDto billAfterSaleForYflDto) {
        log.info("友福利客户账单售后入参：{}", billAfterSaleForYflDto);

        //看是否已经入账出账，兼容一下历史的数据,历史的单子可能都没有入池
        QueryWrapper<SettleBillPoolYflCustomer> poolQueryWrapper = new QueryWrapper<>();
        poolQueryWrapper.lambda().eq(SettleBillPoolYflCustomer::getOrderNumber, billAfterSaleForYflDto.getOrderNumber())
                .eq(SettleBillPoolYflCustomer::getGoodsCode, billAfterSaleForYflDto.getGoodsCode());
        SettleBillPoolYflCustomer poolYflCustomer = settleBillPoolYflCustomerService.getOne(poolQueryWrapper);

        if (poolYflCustomer == null) {
            log.info("订单：{}，商品：{}售后未查询到账单池数据", billAfterSaleForYflDto.getOrderNumber(), billAfterSaleForYflDto.getGoodsCode());
            settleShopBillDetailService.updatePurchaseOrderInfoForYflAfterSale(billAfterSaleForYflDto, billAfterSaleForYflDto.getOrderDetailId(), null);
            return;
        }

        log.info("订单：{}，商品：{}，出账状态：{}", billAfterSaleForYflDto.getOrderNumber(), billAfterSaleForYflDto.getGoodsCode(), poolYflCustomer.getCompanyOutAccountState());
        QueryWrapper<ShopPurchaseSubOrderDetail> orderDetailQueryWrapper = new QueryWrapper<>();
        orderDetailQueryWrapper.lambda().eq(ShopPurchaseSubOrderDetail::getOrderNumber, billAfterSaleForYflDto.getOrderNumber());
        List<ShopPurchaseSubOrderDetail> subOrderDetailList = shopPurchaseSubOrderDetailService.list(orderDetailQueryWrapper);
        Map<String, ShopPurchaseSubOrderDetail> orderDetailMap = subOrderDetailList.stream().collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getOrderDetailId, Function.identity()));

        if (poolYflCustomer.getCompanyOutAccountState() == 20) {
            //已经出账
            SettleShopBill settleShopBill = settleShopBillDetailService.getBaseMapper().getbillByAfterForYfl(billAfterSaleForYflDto);
            QueryWrapper<SettleShopBillDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SettleShopBillDetail::getBillId, settleShopBill.getBillId())
                    .eq(SettleShopBillDetail::getOrderNumber, billAfterSaleForYflDto.getOrderNumber())
                    .eq(SettleShopBillDetail::getGoodsCode, billAfterSaleForYflDto.getGoodsCode());
            SettleShopBillDetail settleShopBillDetail = settleShopBillDetailService.getOne(queryWrapper);
            if (settleShopBillDetail == null) {
                throw new ParameterException("未查询到福利客户账单明细数据,请联系管理员！");
            }
            //开票了 先去红冲 再来售后
            if (settleShopBillDetail.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode()) ||
                    settleShopBillDetail.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode())) {
                throw new ParameterException("订单：{}，商品：{}的对账状态为：{}，请联系管理员处理", billAfterSaleForYflDto.getOrderNumber(),
                        billAfterSaleForYflDto.getGoodsCode(),
                        ReconciliationStatusEnum.getReconciliationStatusName(settleShopBillDetail.getReconciliationStatus()));
            }
            //部分开票的 要看部分售后的数据是否小于可开票数量
            if (settleShopBillDetail.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode())) {
                if (settleShopBillDetail.getInvoicableQuantity().compareTo(billAfterSaleForYflDto.getAfterSaleNum()) < 0) {
                    // 售后数量>可开票数量
                    log.info("订单：{}，商品：{}，可开票数量：{}，售后数量：{}", settleShopBillDetail.getOrderNumber(), settleShopBillDetail.getGoodsCode(),
                            settleShopBillDetail.getInvoicableQuantity(), billAfterSaleForYflDto.getAfterSaleNum());
                    throw new ParameterException("订单：{},商品{}处于部分开票状态，且剩余可开票数量小于售后数量,请检查售后数量！", settleShopBillDetail.getOrderNumber(),
                            settleShopBillDetail.getGoodsCode());
                }
                // 部分开票的售后
                settleShopBillDetailService.companyBillPartialInvoiced(billAfterSaleForYflDto, poolYflCustomer, settleShopBillDetail, orderDetailMap, settleShopBill);
                settleShopBillDetailService.updatePurchaseOrderInfoForYflAfterSale(billAfterSaleForYflDto, poolYflCustomer.getOrderDetailId(), settleShopBillDetail);

            } else {
                //1.先处理正在售后的这个数据 判断是否整单明细
                BigDecimal subtract = settleShopBillDetail.getCheckedNum().subtract(billAfterSaleForYflDto.getAfterSaleNum());
                log.info("客户售后 出账数量：{},售后数量：{}，最终出账数量：{}", settleShopBillDetail.getCheckedNum(), billAfterSaleForYflDto.getAfterSaleNum(), subtract);
                settleShopBillDetailService.companyBillHadOut(settleShopBill, poolYflCustomer, settleShopBillDetail, orderDetailMap, billAfterSaleForYflDto, subtract);
                //更新订单明细生命周期
                settleShopBillDetailService.updatePurchaseOrderInfoForYflAfterSale(billAfterSaleForYflDto, poolYflCustomer.getOrderDetailId(), settleShopBillDetail);
            }

        } else if (poolYflCustomer.getCompanyOutAccountState() == 0) {
            settleShopBillDetailService.companyBillHadNotOut(poolYflCustomer, billAfterSaleForYflDto, orderDetailMap);
            settleShopBillDetailService.updatePurchaseOrderInfoForYflAfterSale(billAfterSaleForYflDto, poolYflCustomer.getOrderDetailId(), null);
        } else {
            settleShopBillDetailService.updatePurchaseOrderInfoForYflAfterSale(billAfterSaleForYflDto, poolYflCustomer.getOrderDetailId(), null);
            return;
        }
    }

    /**
     * 福利客户侧部分开票情况下售后
     *
     * @param billAfterSaleForYflDto
     * @param poolYflCustomer
     * @param settleShopBillDetail
     * @param orderDetailMap
     */
    @Transactional
    public void companyBillPartialInvoiced(BillAfterSaleForYflDto billAfterSaleForYflDto,
                                           SettleBillPoolYflCustomer poolYflCustomer,
                                           SettleShopBillDetail settleShopBillDetail,
                                           Map<String, ShopPurchaseSubOrderDetail> orderDetailMap,
                                           SettleShopBill settleShopBill) {

        BigDecimal subtract = settleShopBillDetail.getInvoicedQuantity().subtract(billAfterSaleForYflDto.getAfterSaleNum());

        List<SettleBillPoolYflCustomer> updateSettleBillPoolYfl = new ArrayList<>();
        if (billAfterSaleForYflDto.getPayWay() == 1) {
            //混合支付
            List<BillAfterSaleUpdateForYflDto> billAfterSaleUpdateForYflDtos = getBrotherSaleOrderDetailList(billAfterSaleForYflDto, settleShopBillDetail);
            // 为0的 直接更新池数据 为20的需要更新明细和账单相关数据

            if (CollectionUtils.isNotEmpty(billAfterSaleUpdateForYflDtos)) {
                for (BillAfterSaleUpdateForYflDto billAfterSaleUpdateForYflDto : billAfterSaleUpdateForYflDtos) {
                    // 0, 20 的都要更新账单池数据
                    SettleBillPoolYflCustomer updateBillPoolYfl = new SettleBillPoolYflCustomer();
                    ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(billAfterSaleUpdateForYflDto.getOrderDetailId());
                    updateBillPoolYfl.setBillPoolYflId(billAfterSaleUpdateForYflDto.getBillPoolYflId());
                    updateBillPoolYfl.setGoodsPayMoney(shopPurchaseSubOrderDetail.getGoodsPayMoney());
                    updateBillPoolYfl.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
                    updateSettleBillPoolYfl.add(updateBillPoolYfl);
                }
            }
        }
        SettleBillPoolYflCustomer updateBillPoolYfl = new SettleBillPoolYflCustomer();
        SettleShopBillDetail updateSettleShopBillDetail = new SettleShopBillDetail();
        updateSettleShopBillDetail.setDetailId(settleShopBillDetail.getDetailId());
        updateSettleShopBillDetail.setCheckedNum(settleShopBillDetail.getCheckedNum().subtract(billAfterSaleForYflDto.getAfterSaleNum()));
        updateSettleShopBillDetail.setUnitPriceNaked(settleShopBillDetail.getUnitPriceNaked());
        updateSettleShopBillDetail.setUnitPriceTax(settleShopBillDetail.getUnitPriceTax());
        updateSettleShopBillDetail.setInvoicableQuantity(subtract);
        updateSettleShopBillDetail.setReconciliationStatus(subtract.compareTo(BigDecimal.ZERO) == 0 ?
                ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode() : ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode());
        calculateRowSubtotal(poolYflCustomer.getTaxRate(), updateSettleShopBillDetail);

        updateBillPoolYfl.setBillPoolYflId(settleShopBillDetail.getSettleBillPoolId());
        updateBillPoolYfl.setCheckedNum(settleShopBillDetail.getCheckedNum().subtract(billAfterSaleForYflDto.getAfterSaleNum()));
        updateBillPoolYfl.setReturnNum(poolYflCustomer.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));
        updateBillPoolYfl.setTotalPriceTax(updateSettleShopBillDetail.getTotalPriceTax());
        updateBillPoolYfl.setTotalPriceNaked(updateSettleShopBillDetail.getTotalPriceNaked());
        ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(poolYflCustomer.getOrderDetailId());
        updateBillPoolYfl.setGoodsPayMoney(shopPurchaseSubOrderDetail.getGoodsPayMoney());
        updateBillPoolYfl.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
        updateSettleBillPoolYfl.add(updateBillPoolYfl);
        //更新所有池明细
        settleBillPoolYflCustomerService.updateBatchById(updateSettleBillPoolYfl);
        //更新正在处理售后的账单明细
        updateById(updateSettleShopBillDetail);
        //更新用户积分
        updateActivityMemberForAftersaleForUser(settleShopBillDetail.getBillId(), poolYflCustomer);
        //更新账单金额
        settleShopBillService.updateShopBillMoney(settleShopBill);
    }

    @Transactional
    public void updatePurchaseOrderInfoForYflAfterSale(BillAfterSaleForYflDto billAfterSaleForYflDto, String orderDetailId, SettleShopBillDetail settleShopBillDetail) {
        UpdateOrderDetailForBillDto updateOrderDetailForBillDto = new UpdateOrderDetailForBillDto();
        updateOrderDetailForBillDto.setOrderSalesChannel(OrderSalesChannelEnum.YFLMALL.getCode());
        UpdateOrderDetailDto updateOrderDetailDto = new UpdateOrderDetailDto();
        updateOrderDetailDto.setOrderDetailId(orderDetailId);
        updateOrderDetailDto.setAfterSaleNum(billAfterSaleForYflDto.getAfterSaleNum());
        if (settleShopBillDetail != null) {
            updateOrderDetailDto.setCheckNum(settleShopBillDetail.getCheckedNum());
            if (ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode().equals(settleShopBillDetail.getReconciliationStatus())
                    || ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode().equals(settleShopBillDetail.getReconciliationStatus())) {
                updateOrderDetailDto.setReconciliationNum(BigDecimal.ZERO);
            } else {
                updateOrderDetailDto.setReconciliationNum(settleShopBillDetail.getCheckedNum());
            }
        } else {
            updateOrderDetailDto.setCheckNum(BigDecimal.ZERO);
            updateOrderDetailDto.setReconciliationNum(BigDecimal.ZERO);
        }

        updateOrderDetailForBillDto.setUpdateOrderDetailDtoList(Collections.singletonList(updateOrderDetailDto));
        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.YFLMALL.getCode());
        orderLifeCycleStrategy.updatePurchaseOrderInfoForAfterSale(updateOrderDetailForBillDto);
    }

    @Transactional
    public void companyBillHadNotOut(SettleBillPoolYflCustomer poolYflCustomer, BillAfterSaleForYflDto billAfterSaleForYflDto, Map<String, ShopPurchaseSubOrderDetail> orderDetailMap) {
        BigDecimal subtract = poolYflCustomer.getCheckedNum().subtract(billAfterSaleForYflDto.getAfterSaleNum());
        //未出账 判断售后的单子是混合支付还是纯积分支付
        if (billAfterSaleForYflDto.getPayWay() == 1) {
            //1.混合支付 对应的所有账单池明细都得更新
            List<BillAfterSaleUpdateForYflDto> billAfterSaleUpdateForYflDtos = getBrotherSaleOrderDetailList(billAfterSaleForYflDto, null);
            List<SettleBillPoolYflCustomer> updateSettleBillPoolYflList = new ArrayList<>();
            for (BillAfterSaleUpdateForYflDto billAfterSaleUpdateForYflDto : billAfterSaleUpdateForYflDtos) {
                ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(billAfterSaleUpdateForYflDto.getOrderDetailId());
                SettleBillPoolYflCustomer settleBillPoolYflCustomer = new SettleBillPoolYflCustomer();
                settleBillPoolYflCustomer.setBillPoolYflId(billAfterSaleUpdateForYflDto.getBillPoolYflId());
                settleBillPoolYflCustomer.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
                settleBillPoolYflCustomer.setGoodsPayMoney(shopPurchaseSubOrderDetail.getGoodsPayMoney());
                updateSettleBillPoolYflList.add(settleBillPoolYflCustomer);
            }
            //处理正在售后的
            if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                //整单售后
                SettleBillPoolYflCustomer settleBillPoolYflCustomer = new SettleBillPoolYflCustomer();
                settleBillPoolYflCustomer.setBillPoolYflId(poolYflCustomer.getBillPoolYflId());
                settleBillPoolYflCustomer.setCheckedNum(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setGoodsPayMoney(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setGoodsPayIntegral(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setTotalPriceNaked(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setTotalPriceTax(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setCompanyOutAccountState(-99);
                updateSettleBillPoolYflList.add(settleBillPoolYflCustomer);
                settleBillPoolYflCustomerService.updateBatchById(updateSettleBillPoolYflList);

            } else if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                //部分售后
                SettleBillPoolYflCustomer settleBillPoolYflCustomer = new SettleBillPoolYflCustomer();
                settleBillPoolYflCustomer.setCheckedNum(subtract);
                settleBillPoolYflCustomer.setBillPoolYflId(poolYflCustomer.getBillPoolYflId());
                settleBillPoolYflCustomer.setReturnNum(poolYflCustomer.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));

                SettleShopBillDetail settleShopBillDetail = new SettleShopBillDetail();
                settleShopBillDetail.setUnitPriceNaked(poolYflCustomer.getUnitPriceNaked());
                settleShopBillDetail.setUnitPriceTax(poolYflCustomer.getUnitPriceTax());
                settleShopBillDetail.setCheckedNum(subtract);
                calculateRowSubtotal(poolYflCustomer.getTaxRate(), settleShopBillDetail);
                settleBillPoolYflCustomer.setTotalPriceTax(settleShopBillDetail.getTotalPriceTax());
                settleBillPoolYflCustomer.setTotalPriceNaked(settleShopBillDetail.getTotalPriceNaked());
                updateSettleBillPoolYflList.add(settleBillPoolYflCustomer);
                settleBillPoolYflCustomerService.updateBatchById(updateSettleBillPoolYflList);

            } else {
                throw new ParameterException("售后数量大于订单数量，请检查数据！");
            }
            //看是否存在部分已经出账的
            List<BillAfterSaleUpdateForYflDto> saleUpdateForYflDtos = billAfterSaleUpdateForYflDtos.stream().filter(e -> e.getReconciliationStatus() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(saleUpdateForYflDtos)) {
                Long billId = saleUpdateForYflDtos.get(0).getBillId();
                SettleShopBill settleShopBill1 = settleShopBillService.getById(billId);
                if (settleShopBill1 == null) {
                    throw new ParameterException("未查询到该售后关联的已出账的订单明细账单主体数据，请联系管理员！");
                }
                //更新已出账的用户的积分数据
                updateActivityMemberForAftersaleForUser(settleShopBill1.getBillId(), poolYflCustomer);
                //更新已出账的账单数据
                settleShopBillService.updateShopBillMoney(settleShopBill1);
            }
        } else {
            //2.纯积分 只更新售后的账单池明细
            if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                //整单
                SettleBillPoolYflCustomer settleBillPoolYflCustomer = new SettleBillPoolYflCustomer();
                settleBillPoolYflCustomer.setBillPoolYflId(poolYflCustomer.getBillPoolYflId());
                settleBillPoolYflCustomer.setCheckedNum(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setGoodsPayMoney(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setGoodsPayIntegral(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setTotalPriceNaked(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setTotalPriceTax(BigDecimal.ZERO);
                settleBillPoolYflCustomer.setCompanyOutAccountState(-99);
                settleBillPoolYflCustomer.setReturnNum(poolYflCustomer.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));
                settleBillPoolYflCustomerService.updateById(settleBillPoolYflCustomer);
            } else if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                //部分
                SettleBillPoolYflCustomer settleBillPoolYflCustomer = new SettleBillPoolYflCustomer();
                settleBillPoolYflCustomer.setCheckedNum(subtract);
                settleBillPoolYflCustomer.setBillPoolYflId(poolYflCustomer.getBillPoolYflId());

                SettleShopBillDetail settleShopBillDetail = new SettleShopBillDetail();
                settleShopBillDetail.setUnitPriceNaked(poolYflCustomer.getUnitPriceNaked());
                settleShopBillDetail.setUnitPriceTax(poolYflCustomer.getUnitPriceTax());
                settleShopBillDetail.setCheckedNum(subtract);
                settleBillPoolYflCustomer.setReturnNum(poolYflCustomer.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));
                calculateRowSubtotal(poolYflCustomer.getTaxRate(), settleShopBillDetail);
                settleBillPoolYflCustomer.setTotalPriceTax(settleShopBillDetail.getTotalPriceTax());
                settleBillPoolYflCustomer.setTotalPriceNaked(settleShopBillDetail.getTotalPriceNaked());
                ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(billAfterSaleForYflDto.getOrderDetailId());
                settleBillPoolYflCustomer.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
                settleBillPoolYflCustomerService.updateById(settleBillPoolYflCustomer);

            } else {
                throw new ParameterException("售后数量大于订单数量，请检查数据！");
            }
        }
    }


    /**
     * 福利企业已经出账后的售后处理
     *
     * @param settleShopBill         已出账的账单
     * @param poolYflCustomer        正在售后的关联的账单池数据
     * @param settleShopBillDetail   正在售后的账单明细
     * @param orderDetailMap         售后的订单的所有的明细
     * @param billAfterSaleForYflDto 售后入参
     * @param subtract               售后数量
     */
    @Transactional
    public void companyBillHadOut(SettleShopBill settleShopBill,
                                  SettleBillPoolYflCustomer poolYflCustomer,
                                  SettleShopBillDetail settleShopBillDetail,
                                  Map<String, ShopPurchaseSubOrderDetail> orderDetailMap,
                                  BillAfterSaleForYflDto billAfterSaleForYflDto,
                                  BigDecimal subtract) {
        //如果是混合支付的,一个商品明细售后，需要更改其他明细的数据
        if (billAfterSaleForYflDto.getPayWay() == 1) {

            List<BillAfterSaleUpdateForYflDto> billAfterSaleUpdateForYflDtos = getBrotherSaleOrderDetailList(billAfterSaleForYflDto, settleShopBillDetail);

            //到这里 都是满足条件的明细 可以进行售后的
            // 为0的 直接更新池数据 为20的需要更新明细和账单相关数据
            List<SettleBillPoolYflCustomer> updateSettleBillPoolYfl = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(billAfterSaleUpdateForYflDtos)) {
                for (BillAfterSaleUpdateForYflDto billAfterSaleUpdateForYflDto : billAfterSaleUpdateForYflDtos) {
                    // 0, 20 的都要更新账单池数据
                    SettleBillPoolYflCustomer updateBillPoolYfl = new SettleBillPoolYflCustomer();
                    ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(billAfterSaleUpdateForYflDto.getOrderDetailId());
                    updateBillPoolYfl.setBillPoolYflId(billAfterSaleUpdateForYflDto.getBillPoolYflId());
                    updateBillPoolYfl.setGoodsPayMoney(shopPurchaseSubOrderDetail.getGoodsPayMoney());
                    updateBillPoolYfl.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
                    updateSettleBillPoolYfl.add(updateBillPoolYfl);
                }
            }

            if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                if (CollectionUtils.isNotEmpty(updateSettleBillPoolYfl)) {
                    settleBillPoolYflCustomerService.updateBatchById(updateSettleBillPoolYfl);
                }
                //账单售后了 删除明细 其中会更新用户月度使用的积分数据 所以先处理上面的订单的其他订单明细混合积分支付金额
                SettleShopBillDetailStrategy billDetailStrategy = settleBillFactory.getBillDetailStrategy(CustomerSourceTypeEnum.YFL_MALL.getCode());
                DetailRemoveFromBillDto detailRemoveFromBillDto = new DetailRemoveFromBillDto();
                detailRemoveFromBillDto.setDetailIds(Collections.singletonList(settleShopBillDetail.getDetailId()));
                detailRemoveFromBillDto.setBillId(settleShopBill.getBillId());
                detailRemoveFromBillDto.setIsSynchronousOther(Boolean.FALSE);
                detailRemoveFromBillDto.setReason("友福利售后");
                detailRemoveFromBillDto.setOperateType(1);
                billDetailStrategy.billDetailDelete(detailRemoveFromBillDto, settleShopBill);

            } else if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                //部分售后 更新包括正在售后在内的所有池明细 然后更新账单
                SettleBillPoolYflCustomer updateBillPoolYfl = new SettleBillPoolYflCustomer();
                SettleShopBillDetail updateSettleShopBillDetail = new SettleShopBillDetail();
                updateSettleShopBillDetail.setDetailId(settleShopBillDetail.getDetailId());
                updateSettleShopBillDetail.setCheckedNum(subtract);
                updateSettleShopBillDetail.setUnitPriceNaked(settleShopBillDetail.getUnitPriceNaked());
                updateSettleShopBillDetail.setUnitPriceTax(settleShopBillDetail.getUnitPriceTax());
                updateSettleShopBillDetail.setInvoicableQuantity(settleShopBillDetail.getInvoicableQuantity().compareTo(BigDecimal.ZERO) > 0 ? subtract : BigDecimal.ZERO);
                calculateRowSubtotal(poolYflCustomer.getTaxRate(), updateSettleShopBillDetail);

                updateBillPoolYfl.setBillPoolYflId(settleShopBillDetail.getSettleBillPoolId());
                updateBillPoolYfl.setCheckedNum(subtract);
                updateBillPoolYfl.setReturnNum(poolYflCustomer.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));
                updateBillPoolYfl.setTotalPriceTax(updateSettleShopBillDetail.getTotalPriceTax());
                updateBillPoolYfl.setTotalPriceNaked(updateSettleShopBillDetail.getTotalPriceNaked());
                ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(poolYflCustomer.getOrderDetailId());
                updateBillPoolYfl.setGoodsPayMoney(shopPurchaseSubOrderDetail.getGoodsPayMoney());
                updateBillPoolYfl.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
                updateSettleBillPoolYfl.add(updateBillPoolYfl);
                //更新所有池明细
                settleBillPoolYflCustomerService.updateBatchById(updateSettleBillPoolYfl);
                //更新正在处理售后的账单明细
                updateById(updateSettleShopBillDetail);
                //更新用户积分
                updateActivityMemberForAftersaleForUser(settleShopBillDetail.getBillId(), poolYflCustomer);
                //更新账单金额
                settleShopBillService.updateShopBillMoney(settleShopBill);
            } else {
                throw new ParameterException("售后数量大于订单数量，请检查数据！");
            }
        } else {//纯积分支付，且已经出账了并满足售后条件，只针对改售后的商品明细处理就好
            if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                //直接删
                SettleShopBillDetailStrategy billDetailStrategy = settleBillFactory.getBillDetailStrategy(CustomerSourceTypeEnum.YFL_MALL.getCode());
                DetailRemoveFromBillDto detailRemoveFromBillDto = new DetailRemoveFromBillDto();
                detailRemoveFromBillDto.setDetailIds(Collections.singletonList(settleShopBillDetail.getDetailId()));
                detailRemoveFromBillDto.setBillId(settleShopBill.getBillId());
                detailRemoveFromBillDto.setIsSynchronousOther(Boolean.FALSE);
                detailRemoveFromBillDto.setReason("友福利售后");
                detailRemoveFromBillDto.setOperateType(1);
                billDetailStrategy.billDetailDelete(detailRemoveFromBillDto, settleShopBill);
            } else if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                //更新池，账单，积分
                SettleBillPoolYflCustomer updateBillPoolYfl = new SettleBillPoolYflCustomer();
                SettleShopBillDetail updateSettleShopBillDetail = new SettleShopBillDetail();
                updateSettleShopBillDetail.setDetailId(settleShopBillDetail.getDetailId());
                updateSettleShopBillDetail.setCheckedNum(subtract);
                updateSettleShopBillDetail.setInvoicableQuantity(settleShopBillDetail.getInvoicableQuantity().compareTo(BigDecimal.ZERO) > 0 ? subtract : BigDecimal.ZERO);
                updateSettleShopBillDetail.setUnitPriceNaked(settleShopBillDetail.getUnitPriceNaked());
                updateSettleShopBillDetail.setUnitPriceTax(settleShopBillDetail.getUnitPriceTax());
                calculateRowSubtotal(poolYflCustomer.getTaxRate(), updateSettleShopBillDetail);

                updateBillPoolYfl.setBillPoolYflId(settleShopBillDetail.getSettleBillPoolId());
                updateBillPoolYfl.setCheckedNum(subtract);
                updateBillPoolYfl.setReturnNum(poolYflCustomer.getReturnNum().add(billAfterSaleForYflDto.getAfterSaleNum()));
                updateBillPoolYfl.setTotalPriceTax(updateSettleShopBillDetail.getTotalPriceTax());
                updateBillPoolYfl.setTotalPriceNaked(updateSettleShopBillDetail.getTotalPriceNaked());
                ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(poolYflCustomer.getOrderDetailId());
                updateBillPoolYfl.setGoodsPayMoney(shopPurchaseSubOrderDetail.getGoodsPayMoney());
                updateBillPoolYfl.setGoodsPayIntegral(shopPurchaseSubOrderDetail.getGoodsPayIntegral());
                settleBillPoolYflCustomerService.updateById(updateBillPoolYfl);
                updateById(updateSettleShopBillDetail);
                //更新某个人的积分
                updateActivityMemberForAftersaleForUser(settleShopBillDetail.getBillId(), poolYflCustomer);
                //更新账单金额
                settleShopBillService.updateShopBillMoney(settleShopBill);
            }
        }
    }

    /**
     * 获取混合支付售后相关的子订单
     *
     * @param billAfterSaleForYflDto
     * @param settleShopBillDetail
     * @return
     */
    public List<BillAfterSaleUpdateForYflDto> getBrotherSaleOrderDetailList(BillAfterSaleForYflDto billAfterSaleForYflDto, SettleShopBillDetail settleShopBillDetail) {
        //检查这个订单下有没有其他的账单明细处于开票状态
        QueryWrapper<SettleBillPoolYflCustomer> brotherPoolQuery = new QueryWrapper<>();
        brotherPoolQuery.lambda().eq(SettleBillPoolYflCustomer::getOrderNumber, billAfterSaleForYflDto.getOrderNumber())
                .ne(SettleBillPoolYflCustomer::getGoodsCode, billAfterSaleForYflDto.getGoodsCode())
                .ne(SettleBillPoolYflCustomer::getCompanyOutAccountState, -99);
        // 剩下的状态只有 0 , 20
        List<SettleBillPoolYflCustomer> brotherPools = settleBillPoolYflCustomerService.list(brotherPoolQuery);

        //混合支付 装该订单下的其他订单明细可以售后的明细
        List<BillAfterSaleUpdateForYflDto> billAfterSaleUpdateForYflDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(brotherPools)) {
            //订单有其他的商品,区分出账和未出账的，出账了的要看是否开票了
            Map<Integer, List<SettleBillPoolYflCustomer>> map = brotherPools.stream().collect(Collectors.groupingBy(SettleBillPoolYflCustomer::getCompanyOutAccountState));
            List<Long> billPoolYflIdExistDetail = new ArrayList<>();
            for (Integer state : map.keySet()) {
                // 0：未出账的  20：已出账的
                List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList = map.get(state);
                //已经出账的集合
                List<BillAfterSaleUpdateForYflDto> state20 = new ArrayList<>();
                for (SettleBillPoolYflCustomer settleBillPoolYflCustomer : settleBillPoolYflCustomerList) {
                    BillAfterSaleUpdateForYflDto billAfterSaleUpdateForYflDto = new BillAfterSaleUpdateForYflDto();
                    billAfterSaleUpdateForYflDto.setBillPoolYflId(settleBillPoolYflCustomer.getBillPoolYflId());
                    billAfterSaleUpdateForYflDto.setIsAfterSale(0);
                    billAfterSaleUpdateForYflDto.setCompanyOutAccountState(settleBillPoolYflCustomer.getCompanyOutAccountState());
                    billAfterSaleUpdateForYflDto.setOrderDetailId(settleBillPoolYflCustomer.getOrderDetailId());
                    if (state == 20) {
                        //出账了
                        billPoolYflIdExistDetail.add(settleBillPoolYflCustomer.getBillPoolYflId());
                        state20.add(billAfterSaleUpdateForYflDto);
                    } else {
                        billAfterSaleUpdateForYflDto.setReconciliationStatus(-1);
                        billAfterSaleUpdateForYflDtos.add(billAfterSaleUpdateForYflDto);
                    }
                }

                if (state == 20) {
                    //出了帐的明细 需要拿到对账状态
                    QueryWrapper<SettleShopBillDetail> brotherBillDetailQuery = new QueryWrapper<>();
                    brotherBillDetailQuery.lambda().in(SettleShopBillDetail::getSettleBillPoolId, billPoolYflIdExistDetail)
                            .eq(SettleShopBillDetail::getPoolType, 2);
                    List<SettleShopBillDetail> brotherBillDetail = settleShopBillDetailService.list(brotherBillDetailQuery);
                    List<SettleShopBillDetail> errorDetailList = brotherBillDetail.stream().filter(e -> e.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode()) ||
                            e.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode()) ||
                            e.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_PARTIAL_INVOICING.getCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(errorDetailList)) {
                        log.info("该售后订单其他明细存在开票状态，无法直接售后：{}", JSONUtil.toJsonStr(errorDetailList));
                        throw new ParameterException("订单下的其他明细存在开票状态，请联系管理员处理！");
                    }
                    //已出账的明细 都是满足售后的条件
                    Map<Long, SettleShopBillDetail> brotherDetailMap = brotherBillDetail.stream().collect(Collectors.toMap(SettleShopBillDetail::getSettleBillPoolId, Function.identity()));
                    state20.forEach(e -> {
                        e.setReconciliationStatus(1);
                        SettleShopBillDetail billDetail = brotherDetailMap.get(e.getBillPoolYflId());
                        e.setBillId(billDetail.getBillId());
                    });
                    //已出账的 可能存在跨越多个账单的情况 这种情况 程序处理太复杂（业务明确福利不会一个订单跨多个月的情况），如果发生，直接运维
                    List<Long> billIds = state20.stream().map(BillAfterSaleUpdateForYflDto::getBillId).distinct().collect(Collectors.toList());
                    if (billIds.size() > 1) {
                        throw new ParameterException("该售后的明细与其他订单明细存在不同的账单内，暂不支持线上售后，请联系管理员处理！");
                    }

                    if (settleShopBillDetail != null) {
                        if (!settleShopBillDetail.getBillId().equals(billIds.get(0))) {
                            throw new ParameterException("售后的订单明细与该订单下的其他明细不在一个账单，暂不支持线上售后，请联系管理员处理！");
                        }
                    }

                    billAfterSaleUpdateForYflDtos.addAll(state20);
                }
            }
        }
        return billAfterSaleUpdateForYflDtos;
    }

    /**
     * 售后 更新某个用户已出账的积分数据
     *
     * @param billId
     * @param poolYflCustomer
     */
    @Transactional
    public void updateActivityMemberForAftersaleForUser(Long billId, SettleBillPoolYflCustomer poolYflCustomer) {
        QueryWrapper<SettleShopBillActivityMember> activityMemberQueryWrapper = new QueryWrapper<>();
        activityMemberQueryWrapper.lambda().eq(SettleShopBillActivityMember::getBillId, billId)
                .eq(SettleShopBillActivityMember::getUserId, poolYflCustomer.getApplyUserId());
        SettleShopBillActivityMember activityMember = settleShopBillActivityMemberService.getOne(activityMemberQueryWrapper);

        QueryWrapper<SettleShopBillPostageDetail> postageDetailQueryWrapper = new QueryWrapper<>();
        postageDetailQueryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, billId)
                .eq(SettleShopBillPostageDetail::getApplyUserId, poolYflCustomer.getApplyUserId());
        List<SettleShopBillPostageDetail> postageDetailList = settleShopBillPostageDetailService.list(postageDetailQueryWrapper);

        List<ActivityUserInfoDto> activityUserInfoDtoList = systemIntegralMapper.getUserInfoByActivityId(poolYflCustomer.getActivityId(), Collections.singletonList(poolYflCustomer.getApplyUserId()));
        List<YflApplyUserUserMonthDto> YflApplyUserUserMonthDtoList = settleShopBillDetailService.getBaseMapper().getYflUserUsedMonthIntegral(billId, Collections.singletonList(poolYflCustomer.getApplyUserId()));
        BigDecimal usedForMonth = YflApplyUserUserMonthDtoList.get(0).getGoodsPayIntegral();
        if (CollectionUtils.isNotEmpty(postageDetailList)) {
            BigDecimal reduce = postageDetailList.stream().map(SettleShopBillPostageDetail::getPostage).reduce(BigDecimal.ZERO, BigDecimal::add);
            usedForMonth = usedForMonth.add(reduce);
        }

        UpdateWrapper<SettleShopBillActivityMember> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SettleShopBillActivityMember::getActivityMemberId, activityMember.getActivityMemberId())
                .set(SettleShopBillActivityMember::getUsedMonthAmount, usedForMonth)
                .set(SettleShopBillActivityMember::getIntegralAmount, activityUserInfoDtoList.get(0).getIntegralAmount())
                .set(SettleShopBillActivityMember::getIntegralUsableAmount, activityUserInfoDtoList.get(0).getIntegralUsableAmount())
                .set(SettleShopBillActivityMember::getUsedAmount, activityUserInfoDtoList.get(0).getIntegralAmount().subtract(activityUserInfoDtoList.get(0).getIntegralUsableAmount()));
        settleShopBillActivityMemberService.update(updateWrapper);
    }

    /**
     * 友服务售后处理邮费
     * 只要原来有邮费 就调用一次
     *
     * @param orderNumber  订单号
     * @param newPostage   新邮费
     * @param customerType 0：客户；1：供应商
     */
    @Transactional
    public void yflAfterSaleForPostage(String orderNumber, BigDecimal newPostage, Integer customerType) {
        log.info("友服务售后处理邮费：order_number:{},newPostage:{},customerType:{}", orderNumber, newPostage, customerType);

        SettleShopBillPostageDetail billPostageDetail = settleShopBillPostageDetailService.getBaseMapper().getPostageByCustomerType(customerType, orderNumber);
        SettleShopBill settleShopBill = new SettleShopBill();

        if (BillCustomerTypeEnum.SUPPLIER.getCode().equals(customerType)) {
            //供应商账单的邮费
            //更新账单池的邮费数据，保证后续调整没有异常，不影响已结算的账单的邮费
            QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SettleBillPool::getOrderNumber, orderNumber);
            List<SettleBillPool> list = settleBillPoolService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                List<SettleBillPool> settleBillPoolList = new ArrayList<>();
                list.forEach(e -> {
                    SettleBillPool updatePool = new SettleBillPool();
                    updatePool.setId(e.getId());
                    updatePool.setSupplierPostage(newPostage);
                    settleBillPoolList.add(updatePool);
                });
                settleBillPoolService.updateBatchById(settleBillPoolList);
            }

            if (billPostageDetail == null) {
                //可能供应商账单一个没有出
                return;
            }

            settleShopBill = settleShopBillService.getById(billPostageDetail.getBillId());

            if (!BillStatusEnum.TO_BE_RECONCILED.getCode().equals(settleShopBill.getBillStatus())) {
                //电商已结算 邮件通知 不更新
                log.info("电商账单已结算了，不更新邮费了,订单号：{},发邮件通知", orderNumber);
                List<SystemDictDataEntity> afterSaleEmails = systemDictDataMapper.selectListByDictType("after_sale_emails");
                List<String> emails = afterSaleEmails.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());
                mailService.sendEmail(MailService.SUPPLIER_FORBIDDEN_AFTER_SALE_POSTAGE, mailService.supplierForbiddenAfterSalePostageTemplate(orderNumber), emails, null);
                return;
            }

        } else if (BillCustomerTypeEnum.COMPANY.getCode().equals(customerType)) {
            //客户
            if (billPostageDetail == null) {
                //客户未出账
                return;
            }
            //判断是否开票了
            if (billPostageDetail.getInvoiceFlag().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_INVOICED.getCode()) ||
                    billPostageDetail.getInvoiceFlag().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode())) {
                log.info("postage had invoice:{}", billPostageDetail);
                throw new ParameterException("该订单的邮费已经开票，请联系管理员处理！");
            }

            settleShopBill = settleShopBillService.getById(billPostageDetail.getBillId());
        } else {
            throw new ParameterException("账单类型异常");
        }

        // 出账了 但没开票 可以删除
        if (newPostage.compareTo(BigDecimal.ZERO) == 0) {
            //邮费调为0
            settleShopBillPostageDetailService.removeById(billPostageDetail.getBillDetailPostageId());
        } else {
            //退部分邮费
            billPostageDetail.setPostage(newPostage);
            settleShopBillPostageDetailService.updateById(billPostageDetail);
        }
        //更新账单金额
        settleShopBillService.updateShopBillMoney(settleShopBill);
    }

    public void filterAfterSalePostage(List<SettleBillPool> settleBillPools) {
        List<SettleBillPool> collect = settleBillPools.stream().filter(e -> e.getSupplierPostage().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<String> orderNumberList = collect.stream().map(SettleBillPool::getOrderNumber).collect(Collectors.toList());
        List<ShopReturnVo> list = shopReturnService.queryReturnNumByOrder(orderNumberList);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<SettleBillPool>> listMap = collect.stream().collect(Collectors.groupingBy(SettleBillPool::getOrderNumber));
        List<SettleBillPool> updatePoolList = new ArrayList<>();
        list.forEach(e -> {
            List<SettleBillPool> settleBillPoolList = listMap.get(e.getOrderNumber());
            if (CollectionUtils.isEmpty(settleBillPoolList)) {
                return;
            }
            settleBillPoolList.forEach(c -> {
                c.setSupplierPostage(c.getSupplierPostage().subtract(e.getReturnFreight()));
                SettleBillPool updatePool = new SettleBillPool();
                updatePool.setId(c.getId());
                updatePool.setSupplierPostage(c.getSupplierPostage());
                updatePoolList.add(updatePool);
            });
        });
        if (CollectionUtils.isNotEmpty(updatePoolList)) {
            log.info("邮费更新：{}", settleBillPools);
            settleBillPoolService.getBaseMapper().updatePostageList(updatePoolList);
        }

    }

    public BillDetailReconciliationCheckDto billDetailReconciliationCheck(ReconciliationOperateDto reconciliationOperateDto, Integer poolType) {
        List<ReconciliationDetailDataDto> reconciliationDetailDataDtoList = new ArrayList<>();
        if (PoolTypeEnum.DFSHOP.getCode().equals(poolType)) {
            reconciliationDetailDataDtoList = settleShopBillDetailService.getBaseMapper().getDfshopReconciliationData(reconciliationOperateDto.getDetailIds());
        } else if (PoolTypeEnum.YFLCOMPANY.getCode().equals(poolType)) {
            reconciliationDetailDataDtoList = settleShopBillDetailService.getBaseMapper().getYflShopReconciliationData(reconciliationOperateDto.getDetailIds());
        } else {
            throw new ParameterException("查询池类型异常！");
        }


        if (CollectionUtils.isEmpty(reconciliationDetailDataDtoList)) {
            throw new ParameterException("未查询到相关账单明细数据！");
        }

        List<ReconciliationDetailDataDto> details = reconciliationDetailDataDtoList.stream().filter(e -> !e.getReconciliationStatus().equals(ReconciliationStatusEnum.RECONCILIATION_BILL_TO_BE_CONFIRMED.getCode())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(details)) {
            throw new ParameterException("存在非待确认的账单数据，请检查数据！");
        }

        List<Integer> customerType = reconciliationDetailDataDtoList.stream().map(ReconciliationDetailDataDto::getCustomerType).distinct().collect(Collectors.toList());
        if (customerType.size() > 1) {
            throw new ParameterException("客户类型异常");
        }

        List<String> customerCode = reconciliationDetailDataDtoList.stream().map(ReconciliationDetailDataDto::getCustomerCode).distinct().collect(Collectors.toList());
        if (customerCode.size() > 1) {
            log.info("客户编码异常 customerCode:{}", customerCode);
            throw new ParameterException("客户编码异常");
        }


        BillDetailReconciliationCheckDto billDetailReconciliationCheckDto = new BillDetailReconciliationCheckDto();
        billDetailReconciliationCheckDto.setReconciliationDetailDataDtoList(reconciliationDetailDataDtoList);
        billDetailReconciliationCheckDto.setCustomerType(customerType.get(0));
        billDetailReconciliationCheckDto.setCustomerCode(customerCode.get(0));
        return billDetailReconciliationCheckDto;
    }
}
