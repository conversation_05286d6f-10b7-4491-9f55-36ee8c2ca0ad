package com.ly.yph.api.goods.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class SupplierGoodsImportReqVO {
    @ExcelProperty("序号")
    private Integer index;

    @ExcelProperty("*商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    @ExcelProperty("*商品品牌")
    @NotBlank(message = "商品品牌不能为空")
    private String brandName;

    @ExcelProperty("*商品型号")
    private String materialsCode = "";

    @ExcelProperty("*包装规格")
    private String specGoodsWareQd;

    @ExcelProperty("*交期")
    @NotNull(message = "交期不能为空")
    private Integer deliveryDay;

    @ExcelProperty("*销售单位")
    @NotBlank(message = "销售单位不能为空")
    private String saleUnit;

    @ExcelProperty("*起订量")
    @NotNull(message = "起订量不能为空")
    private Integer goodsMoq;

    @ExcelProperty("*库存数量")
    @NotNull(message = "库存数量不能为空")
    private Integer stockAvailable;

    @ExcelProperty("*商品分类")
    @NotBlank(message = "商品分类不能为空")
    private String goodsClass;

    @ExcelProperty("*税率")
    @NotNull(message = "税率不能为空")
    private Integer taxRate;

    @ExcelProperty("商品SKU/物料编码")
    private String goodsSku;

    @ExcelProperty("税收分类编码")
    @NotBlank(message = "税收分类编码不能为空")
    private String taxCode;

    @ExcelProperty("*商品来源")
    @NotBlank(message = "商品来源不能为空")
    private String goodsSource;

    @ExcelProperty("询价单号")
    private String seekPriceNumbers;

    @ExcelProperty("*服务企业")
    @NotBlank(message = "服务企业不能为空")
    private String company;

    @ExcelProperty("*合同编号")
    @NotBlank(message = "合同编号不能为空")
    private String contractNumber;

    @ExcelProperty("*供货价(未税)")
    @NotBlank(message = "供货价不能为空")
    @Pattern(
            regexp = "^-?\\d+(\\.\\d{1,2})?$",
            message = "供货价格式错误：最多支持2位小数",
            groups = DefaultGroup.class
    )
    private String goodsPactPrice;

    @ExcelProperty("*平台销售价(未税)")
    @NotBlank(message = "平台销售价不能为空")
    @Pattern(
            regexp = "^-?\\d+(\\.\\d{1,2})?$",
            message = "平台销售价格式错误：最多支持2位小数",
            groups = DefaultGroup.class
    )
    private String goodsPlatformPrice;

    @ExcelProperty("*销售渠道")
    @NotBlank(message = "销售渠道不能为空")
    private String saleClient;

    public interface DefaultGroup {
    }

}
