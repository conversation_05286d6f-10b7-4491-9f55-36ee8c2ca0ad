package com.ly.yph.api.event;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.ly.yph.api.event.dto.UnprocessDto;
import com.ly.yph.api.event.entity.WaEventMessage;
import com.ly.yph.api.event.entity.WaEventMessageStatus;
import com.ly.yph.api.event.entity.WaEventSubscriptions;
import com.ly.yph.api.event.mapper.WaEventMessageMapper;
import com.ly.yph.api.event.mapper.WaEventMessageStatusMapper;
import com.ly.yph.api.event.mapper.WaEventSubscriptionsMapper;
import com.ly.yph.api.event.mapper.WaEventTypesMapper;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.tenant.core.aop.TenantIgnore;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.assertj.core.util.Arrays;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 事件管理
 *
 * <AUTHOR>
 * @date 2023/11/06
 */
@Service
@Slf4j
public class EventManager {
    public static final int BATCH = 1000;
    @Resource
    private WaEventMessageMapper messageMapper;
    @Resource
    private WaEventTypesMapper typesMapper;
    @Resource
    private WaEventMessageStatusMapper statusMapper;
    @Resource
    private WaEventSubscriptionsMapper subscriptionsMapper;

    public int publishMessage(EventTypes event) {
        return publishMessage(event, null);
    }

    public int publishMessage(EventTypes event, Map<String, Object> content) {
        return publishMessage(event, content, (byte) 0);
    }

    public int publishMessage(EventTypes event, Map<String, Object> content, byte priority) {
        // 类型不需要租户
        val waEventTypes = TenantUtils.executeIgnore(() -> typesMapper.selectOneByTypeCode(event.getName()));
        if (waEventTypes == null) {
            log.error("event type not found：{}", event.getName());
            return 0;
        }

        WaEventMessage message = _saveMessage(content, waEventTypes.getId(), priority);
        _saveMsgProcStatus(message);
        return message.getId();
    }

    private void _saveMsgProcStatus(WaEventMessage message) {
        List<WaEventSubscriptions> subscription = subscriptionsMapper.selectByEventTypeId(message.getEventTypeId());
        if (subscription.isEmpty()) {
            // log.error("event subscriptions not found：{}", message.getEventTypeId());
            return;
        }
        subscription.forEach(item -> {
            WaEventMessageStatus status = new WaEventMessageStatus();
            status.setMessageId(message.getId());
            status.setSubscriptionId(item.getId());
            status.setStatus((byte) 0);
            status.setTenantId(Math.toIntExact(TenantContextHolder.getTenantId()));
            status.setLastUpdateTime(new Date());
            statusMapper.insert(status);
        });
    }

    @NotNull
    private WaEventMessage _saveMessage(Map<String, Object> content, Integer eventTypeId, byte priority) {
        WaEventMessage message = new WaEventMessage();
        message.setTenantId(Math.toIntExact(TenantContextHolder.getTenantId()));
        message.setSendTime(new Date());
        message.setContent(JSON.toJSONString(content));
        message.setPriority(priority);
        message.setEventTypeId(eventTypeId);
        messageMapper.insert(message);
        return message;
    }

    @Scheduled(fixedDelay = 10, timeUnit = TimeUnit.SECONDS)
    public void procUnprocessedMessages() {
        processMessage();
    }

    @DistributedLock(value = "event_subscriptions_executor", leaseTime = 3600, waitLock = false, throwMessage = "已经存在事件订阅执行器")
    @TenantIgnore
    private void processMessage() {
        // log.info("开始执行时间订阅执行器");
        List<UnprocessDto> need_proc;
        try {
            need_proc = TenantUtils.executeIgnore(() -> statusMapper.selectUnprocessedMessages(BATCH));
        } catch (Exception ex) {
            log.error("event subscriptions exec failed：{}", ExceptionUtil.stacktraceToString(ex));
            return;
        }

        if (need_proc.isEmpty()) {
            return;
        }

        // 查询出订阅者
        need_proc.parallelStream().forEach(this::doExecutor);
    }

    private void doExecutor(UnprocessDto item) {
        String args = item.getArgs();
        String content = item.getContent();

        String serviceName = _getDynamicServiceName(item.getServiceName(), content);

        if (serviceName == null || serviceName.isEmpty()) {
            log.info("No service to execute for: {}", item.getServiceName());
            return;
        }

        IEventExecutor executor = SpringUtil.getBean(serviceName);
        if (executor == null) {
            log.error("event subscripts is null：{}", item.getServiceName());
            return;
        }

        Map<String, Object> argss = _mapperJson(args);
        Map<String, Object> contents = _mapperJson(content);

        try {
            // log.info("开始执行事件订阅：{}", item.getServiceName());
            TenantUtils.execute(Long.valueOf(item.getTenantId()), () -> {
                executor.exec(argss, contents);
                statusMapper.updateStatusByIdIn(CollectionUtil.toList(item.getStatusId()), (byte) 1);
            });

        } catch (Exception ex) {
            log.error("event subscriptions exec failed：{}", ExceptionUtil.stacktraceToString(ex));
            TenantUtils.execute(Long.valueOf(item.getTenantId()), () -> {
                statusMapper.updateStatusByIdIn(CollectionUtil.toList(item.getStatusId()), (byte) 2);
            });
        }
    }

    // 配置的格式 supplier|[DSZKH00]:priceChangeHitEventJustLogHandler/default:priceChangeHitShelvesEventHandler
    private String _getDynamicServiceName(String serviceName, String content) {
        // 如果 serviceName 不包含 '|', 它就不是动态的，我们可以直接返回它
        if (!serviceName.contains("|")) {
            return serviceName;
        }

        String[] parts = serviceName.split("\\|");
        Map<String, Object> contents = _mapperJson(content);

        // 从内容中提取条件键和其值
        String conditionKey = parts[0];
        Object conditionValue = contents.get(conditionKey);
        // 准备匹配服务和其条件的正则表达式模式
        Pattern pattern = Pattern.compile("\\[(.*?)\\]:(.*)");

        String defaultServiceName = null;

        // 遍历每个服务及其条件
        for (String part : parts[1].split("/")) {
            // 如果部分以 'default:' 开头，这是我们的默认服务
            if (part.startsWith("default:")) {
                defaultServiceName = part.split(":")[1];
                continue;
            }

            // 检查部分是否匹配我们的 '[condition]:service' 模式
            Matcher matcher = pattern.matcher(part);
            if (matcher.find()) {
                // 如果匹配，我们提取条件和服务名称
                String[] conditionValues = matcher.group(1).split(",");
                String sn = matcher.group(2);

                // 如果条件值在条件值中，则返回相应的服务

                if (conditionValue != null && Arrays.asList(conditionValues).contains(conditionValue.toString())) {
                    return sn;
                }
            }
        }

        // 没有服务匹配条件值，返回默认服务
        return defaultServiceName;
    }

    @NotNull
    private static Map<String, Object> _mapperJson(String args) {
        Map<String, Object> resultMap = new HashMap<>();

        JSONObject jobj = new JSONObject(args);
        for (String key : jobj.keySet()) {
            var jsonObject = jobj.get(key);
            resultMap.put(key, jsonObject);
        }
        return resultMap;
    }
}
