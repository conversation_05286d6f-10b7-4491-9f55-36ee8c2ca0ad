package com.ly.yph.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/4/14 18:47
 **/
@Data
@ApiModel("采购订单商品明细")
public class PurchaseSubOrderDetailVo implements Serializable {
    private static final long serialVersionUID = -6429837053085501066L;

    /**
     * 订单明细ID
     */
    @ApiModelProperty(value = "订单明细ID", dataType = "String")
    private String orderDetailId;
    /**
     * 采购子订单ID
     */
    @ApiModelProperty(value = "采购子订单ID", dataType = "String")
    private String orderId;
    /**
     * 采购子订单号
     */
    @ApiModelProperty(value = "采购子订单号", dataType = "String")
    private String orderNumber;
    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID", dataType = "Long")
    private Long goodsId;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码", dataType = "String")
    private String goodsCode;
    /**
     * 订货编码
     */
    @ApiModelProperty(value = "订货编码", dataType = "String")
    private String goodsSku;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", dataType = "String")
    private String goodsName;
    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述", dataType = "String")
    private String goodsDesc;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("供应商型号")
    private String materialsCode;

    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片", dataType = "String")
    private String goodsImage;
    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位", dataType = "String")
    private String saleUnit;
    /**
     * 订货数量
     */
    @ApiModelProperty(value = "订货数量", dataType = "Long")
    private Long confirmNum;
    /**
     * 订货数量(小数)
     */
    @ApiModelProperty(value = "订货数量(小数)", dataType = "decimal")
    private BigDecimal confirmNumDecimal;
    /**
     * 商品未税单价
     */
    @ApiModelProperty(value = "商品未税单价", dataType = "BigDecimal")
    private BigDecimal goodsUnitPriceNaked;
    /**
     * 未税销售价格小计
     */
    @ApiModelProperty(value = "未税销售价格小计", dataType = "BigDecimal")
    private BigDecimal goodsTotalPriceNaked;
    /**
     * 商品含税单价
     */
    @ApiModelProperty(value = "商品含税单价", dataType = "BigDecimal")
    private BigDecimal goodsUnitPriceTax;
    /**
     * 含税销售价格小计
     */
    @ApiModelProperty(value = "含税销售价格小计", dataType = "BigDecimal")
    private BigDecimal goodsTotalPriceTax;
    /**
     * 供应商含税小计
     */
    @ApiModelProperty(value = "供应商含税小计", dataType = "BigDecimal")
    private BigDecimal supplierTotalPriceTax;
    /**
     * 供应商商品含税单价
     */
    @ApiModelProperty(value = "供应商商品含税单价", dataType = "BigDecimal")
    private BigDecimal supplierUnitPriceTax;
    /**
     * 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式
     */
    @ApiModelProperty(value = "计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式", dataType = "Integer")
    private Integer pricingMode;
    /**
     * -1:失败 0:已取消 10:提交 20:待发货 30:待收货 40:已结算 50:已开票
     */
    @ApiModelProperty(value = "-1:失败 0:已取消 10:提交 20:待发货 30:待收货 40:已结算 50:已开票", dataType = "Integer")
    private Integer orderDetailState;
    private String orderDetailStateName;
    /**
     * 审批状态 0:不同意 1:同意
     */
    @ApiModelProperty(value = "审批状态 0:不同意 1:同意", dataType = "Integer")
    private Integer auditState;
    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见", dataType = "String")
    private String auditOpinion;
    /**
     * 商品末级分类ID
     */
    @ApiModelProperty(value = "商品末级分类ID", dataType = "String")
    private String thirdLevelGcid;
    /**
     * 商品末级分类名称
     */
    @ApiModelProperty(value = "商品末级分类名称", dataType = "String")
    private String thirdLevelGcName;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码", dataType = "String")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称", dataType = "String")
    private String supplierName;
    /**
     * 供应商类别 0:电商平台;1:独立供应商
     */
    @ApiModelProperty("供应商类别 0:电商平台;1:独立供应商")
    private Integer supplierType;
    /**
     * 供应商来源
     */
    @ApiModelProperty("供应商来源")
    private String supplierDataSource;
    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量", dataType = "String")
    private Integer deliveryNum;
    /**
     * 发货数量(小数)
     */
    @ApiModelProperty(value = "发货数量(小数)", dataType = "String")
    private BigDecimal deliveryNumDecimal;
    /**
     * 已退货数量
     */
    @ApiModelProperty(value = "已退货数量", dataType = "String")
    private BigDecimal returnNum = BigDecimal.ZERO;
    /**
     * 已退货积分/预算
     */
    @ApiModelProperty(value = "已退货积分/预算", dataType = "BigDecimal")
    private BigDecimal returnPrice = BigDecimal.ZERO;
    /**
     * 已退货金额
     */
    @ApiModelProperty(value = "已退货金额", dataType = "BigDecimal")
    private BigDecimal returnMoney = BigDecimal.ZERO;
    /**
     * 商品支付的积分(小计)
     */
    @ApiModelProperty(value = "商品支付的积分(小计)", dataType = "BigDecimal")
    private BigDecimal goodsPayIntegral;
    /**
     * 商品支付金额(含税小计,包括但不限于微信支付宝)
     */
    @ApiModelProperty(value = "商品支付金额(含税小计,包括但不限于微信支付宝)", dataType = "BigDecimal")
    private BigDecimal goodsPayMoney;
    /**
     * sap编码
     */
    @ApiModelProperty(value = "sap编码", dataType = "String")
    private String orderSapNumber;
    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号", dataType = "String")
    private String deliveryCode;
    /**
     * 物流名称
     */
    @ApiModelProperty(value = "物流名称", dataType = "String")
    private String deliveryName;
    /**
     * 发货单号
     */
    @ApiModelProperty(value = "发货单号", dataType = "String")
    private String packageId;
    /**
     * 退货状态(-1-退货失败 0-退货申请中 1-退货完成 2-部分退货)
     */
    @ApiModelProperty(value = "退货状态(-1-退货失败 0-退货申请中 1-退货完成 2-部分退货)", dataType = "String")
    private Integer detailReturnState;
    private String detailReturnStateName;

    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号", dataType = "String")
    private String purchaseNumber;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", dataType = "String")
    private String createTime;

    /**
     * 起订量
     */
    @ApiModelProperty(value = "起订量", dataType = "String")
    private String goodsMoq;


    @ApiModelProperty(value = "SAP物料编码", dataType = "String")
    private String maraMatnr;

    @ApiModelProperty("商品类型(1:实物商品 2:虚拟商品 3:服务) GoodsModelEnum枚举类")
    private Integer goodsModel;

    @ApiModelProperty("虚拟商品订单类型 1：直冲 2：卡密")
    private String virtualType;

    @ApiModelProperty("合同号")
    private String contractNumber;

    @ApiModelProperty("电商含税原价")
    private BigDecimal supplierUnitOriginalPriceTax;

    /**
     * 需求人
     */
    @ApiModelProperty(value = "需求人", dataType = "String")
    private String neederName;

    /**
     * 需求人部门
     */
    @ApiModelProperty(value = "需求人部门", dataType = "String")
    private String neederDepartmentName;

    @ApiModelProperty(value = "商品规格", dataType = "String")
    private String goodsSpec;
    @ApiModelProperty(value = "商品规格数组", dataType = "String")
    private String goodsSpecArray;
    @ApiModelProperty(value = "订单备注", dataType = "String")
    private String remark;


    @ApiModelProperty(value = "税率", dataType = "BigDecimal")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "货币代码", dataType = "java.lang.String")
    private String currencyCode;

    @ApiModelProperty(value = "货币名称", dataType = "java.lang.String")
    private String currencyName;

    @ApiModelProperty(value = "商品含税单价（人民币）", dataType = "java.math.BigDecimal")
    private BigDecimal goodsUnitTaxPriceCny;

    @ApiModelProperty(value = "商品未税单价（人民币）", dataType = "java.math.BigDecimal")
    private BigDecimal goodsUnitNakedPriceCny;

    @ApiModelProperty(value = "商品小计（人民币）", dataType = "java.math.BigDecimal")
    private BigDecimal goodsTotalPriceCny;

    @ApiModelProperty(value = "汇率", dataType = "java.math.BigDecimal")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "关税")
    private BigDecimal tariff;

    @ApiModelProperty("商品收货数量（整数）")
    private Integer receivingNum = 0;

    @ApiModelProperty("商品收货数量（小数）")
    private BigDecimal receivingNumDecimal = BigDecimal.ZERO;

    /**
     * 需求人名字
     */
    @ApiModelProperty(value = "需求人名字", dataType = "String")
    private String nickName;

    @ApiModelProperty(value = "参考发货天数")
    private Integer deliveryTime;

    @ApiModelProperty(value = "物品流向  0:入库；1：即入即出")
    private Integer flow;

    @ApiModelProperty(value = "用途", dataType = "String")
    private String useName;

    @ApiModelProperty(value = "结算方式 0非平台结算 1平台结算")
    private Integer settlementType;

    @ApiModelProperty(value = "预算编码")
    private String budgetCode;
    @ApiModelProperty(value = "预算名称")
    private String budgetName;
    @ApiModelProperty(value = "非最低价理由")
    private String notMinPriceReason;

    @ApiModelProperty(value = "商品关联专区id", dataType = "Long")
    private Long goodsZoneId;

    @ApiModelProperty(value = "商品关联专区小类", dataType = "String")
    private String cartZoneGoodsType;
}

