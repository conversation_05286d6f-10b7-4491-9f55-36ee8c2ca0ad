package com.ly.yph.api.settlement.supplier.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill;
import com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceBillVo;
import com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceQueryVo;
import com.ly.yph.core.base.database.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface SupplierInvoiceBillMapper extends BaseMapperX<SupplierInvoiceBill> {

    IPage<SupplierInvoiceBillVo> queryPage(Page<Object> adapterPageReq,
                                           @Param("query") SupplierInvoiceQueryVo supplierInvoiceQueryVo);

    List<SupplierInvoiceBill> getLastTimeForInvoiceUpload(Long billId);
}
