<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.settlement.supplier.mapper.SupplierInvoiceBillMapper">

    <select id="queryPage" resultType="com.ly.yph.api.settlement.supplier.vo.SupplierInvoiceBillVo">
        select
        sib.bill_sn,
        sib.id,
        sib.invoice_apply_number,
        sib.invoice_number,
        sib.amount_tax,
        sib.amount_naked,
        sib.amount_tax-sib.amount_naked as tax,
        sib.invoice_amount_tax,
        sib.state,
        sib.inconsistent_remark,
        sib.invoice_confirm_time,
        sib.create_time,
        sib.invoice_url,
        sib.supplier_name,
        sib.approve_reason,
        sib.bill_invoice_type,
        sib.inconsistent_url,
        sib.tolerance_amount
        from supplier_invoice_bill sib
        where sib.is_enable =1
        <if test="query.billSn !=null and query.billSn !=''">
            and sib.bill_sn =#{query.billSn}
        </if>
        <if test="query.invoiceApplyNumber !=null and query.invoiceApplyNumber !=''">
            and sib.invoice_apply_number =#{query.invoiceApplyNumber}
        </if>
        <if test="query.invoiceNumber !=null and query.invoiceNumber !=''">
            and sib.invoice_number =#{query.invoiceNumber}
        </if>
        <if test="query.supplierCode !=null and query.supplierCode !=''">
            and sib.supplier_code =#{query.supplierCode}
        </if>
        <if test="query.state !=null">
            and sib.state =#{query.state}
        </if>
        <if test="query.createTimeEnd != null and query.createTimeEnd !=''">
            and sib.create_time &gt;=#{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null and query.createTimeEnd !=''">
            and sib.create_time &lt;=#{query.createTimeEnd}
        </if>
        <if test="query.billInvoiceType !=null">
            and sib.bill_invoice_type =#{query.billInvoiceType}
        </if>
        order by sib.id desc
    </select>

    <select id="getLastTimeForInvoiceUpload" resultType="com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill">
        select *
        from supplier_invoice_bill where bill_id =#{billId} and is_enable =1 and state=2
    </select>

</mapper>