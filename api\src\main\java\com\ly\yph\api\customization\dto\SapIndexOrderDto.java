package com.ly.yph.api.customization.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static com.ly.yph.core.base.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 * @date 2025年07月29日
 */
@Data
public class SapIndexOrderDto implements Serializable {
    private static final long serialVersionUID = 8215853041308735308L;
    /**
     * 索引单号
     */
    @ExcelProperty(value = "索引单号")
    private String indexOrderSn;
    /**
     * 发票申请单号
     */
    @ExcelProperty(value = "发票申请单号")
    private String invoiceApplyNumber;
    /**
     * 发票号
     */
    @ExcelProperty(value = "发票号")
    private String invoiceNumber;

    /**
     * 索引单状态（0、初始，1、草稿，2、已提交索引单，3、索引单已结算，4、索引单被冲销）
     */
    @ExcelIgnore
    private Integer indexState;
    /**
     * 索引单状态（0、初始，1、草稿，2、已提交索引单，3、索引单已结算，4、索引单被冲销）
     */
    @ExcelProperty(value = "索引单状态")
    private String indexStateTxt;
    /**
     * 逻辑系统
     */
    @ExcelProperty(value = "逻辑系统")
    private String logicalSystem;
    /**
     * sap公司代码
     */
    @ExcelProperty(value = "sap公司代码")
    private String sapCompanyCode;
    @ExcelProperty(value = "工厂")
    private String werksCode;
    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    /**
     * 发票含税总金额
     */
    @ExcelProperty(value = "发票含税总金额")
    private BigDecimal invoiceTotalPrice;
    /**
     * 结算金额不含税
     */
    @ExcelProperty(value = "结算金额")
    private BigDecimal indexPrice;
    /**
     * 发票税额
     */
    @ExcelProperty(value = "发票税额")
    private BigDecimal invoiceAmount;
    /**
     * 交票人姓名
     */
    @ExcelProperty(value = "交票人姓名")
    private String invoicePeople;
    /**
     * 交票人手机号
     */
    @ExcelProperty(value = "交票人手机号")
    private String invoiceMobile;
    /**
     * 金额单位
     */
    @ExcelProperty(value = "金额单位")
    private String amountUnit;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = "GMT+8")
    private Date createTime;


}
