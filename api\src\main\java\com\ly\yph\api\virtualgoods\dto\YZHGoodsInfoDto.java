package com.ly.yph.api.virtualgoods.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年11月19日
 */
@Data
public class YZHGoodsInfoDto implements Serializable {
    private static final long serialVersionUID = 3211133306712835975L;
    /**
     * 虚拟商品 spu 编码
     */
    String goodsCode;
    /**
     * 虚拟商品 spu 名称
     */
    String goodsName;
    /**
     * 虚拟商品 Sku 编码
     */
    String goodsSkuCode;
    /**
     * 虚拟商品 Sku 名称
     */
    String goodsSkuName;
    /**
     * 商品主图
     */
    String imgUrl;
    /**
     * 商品图文详情描述信息 内容比较大，建议使用 text 字段类型来存储
     */
    String goodsDescribe;
    /**
     * 规格
     */
    String spec;
    /**
     * 面值
     */
    String face;
    /**
     * 二级分类编码
     */
    String secondCategoryCode;
    /**
     * 二级分类名称
     */
    String secondCategoryName;
    /**
     * 末级分类编码
     */
    String lastCategoryCode;
    /**
     * 末级分类名称
     */
    String lastCategoryName;
    /**
     * 商品销售价
     */
    Double sellPrice;
    /**
     * 商品市场价
     */
    Double marketPrice;
    /**
     * 商家名称
     */
    Double merchantName;
    /**
     * 商品上下架状态(1001:上架,1002:下架)
     */
    Integer shelvesStatus;

    Double taxRate;

    /**
     * 商品类型 1：直冲 2：卡密
     */
    Integer goodsType;

    /**
     * 充值类：2001
     * 卡密类：2002，2004
     */

    private String virtualGoodType;
}
