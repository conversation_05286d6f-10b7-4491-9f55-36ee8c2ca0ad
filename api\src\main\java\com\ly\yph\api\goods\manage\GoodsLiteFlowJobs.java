package com.ly.yph.api.goods.manage;

import com.ly.yph.api.goods.manage.processor.LiteFlowStandardProcessor;
import com.ly.yph.tenant.core.aop.TenantIgnore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/22 21:06
 */
@Service
@Slf4j
@RefreshScope
public class GoodsLiteFlowJobs {

    @Resource
    private LiteFlowStandardProcessor standardProcessor;

    @Scheduled(fixedDelay = 1000L * 60, initialDelay = 1000L * 60)
    @TenantIgnore
    public void schedulePeriodicRefreshForLog() {
        standardProcessor.schedulePeriodicRefresh();
    }


    @Scheduled(fixedDelay = 1000L * 60, initialDelay = 1000L * 60)
    @TenantIgnore
    public void liteFlowProcess() {
        standardProcessor.liteFlowProcess();
    }
}
