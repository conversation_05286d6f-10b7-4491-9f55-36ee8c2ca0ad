package com.ly.yph.api.order.vo;

import com.ly.yph.api.order.entity.ShopOrderAddress;
import com.ly.yph.api.supplier.vo.SupplierOrderDeliveryVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/12 19:02
 **/
@Data
@ApiModel("友福利商城 - 订单列表查询vo")
public class ShopYflOrderVo implements Serializable {

    private static final long serialVersionUID = 141630113137880085L;

    @ApiModelProperty("商城订单ID")
    private String orderId;

    @ApiModelProperty("商城订单号")
    private String orderNumber;

    @ApiModelProperty("供应商订单号")
    private String supplierOrderNumber;

    @ApiModelProperty("采购单ID")
    private String purchaseId;

    @ApiModelProperty("采购单号")
    private String purchaseNumber;

    @ApiModelProperty("活动ID")
    private Long activityId;

    @ApiModelProperty("下单时间")
    private Date createTime;

    @ApiModelProperty("电商名称")
    private String supplierName;

    @ApiModelProperty("运费")
    private BigDecimal orderFreightPrice;

    /**
     * 运费类型  0 积分 1 现金
     */
    @ApiModelProperty(value = "运费类型  0 积分 1 现金", dataType = "Integer")
    private Integer freightType;

    @ApiModelProperty("电商订单金额(含税)")
    private BigDecimal supplierOrderPriceTax;

    @ApiModelProperty("商城订单金额(含税)")
    private BigDecimal orderPriceTax;

    @ApiModelProperty("订单支付积分")
    private BigDecimal orderPayIntegral;

    @ApiModelProperty("订单支付金额(含税,包括但不限于微信支付宝)")
    private BigDecimal orderPayMoney;

    @ApiModelProperty("订单状态 -1:订单失败 0:订单取消 10:已提交 20:待发货 30:待收货 40:收货完成 ")
    private Integer orderState;

    @ApiModelProperty("订单售后状态 0:未售后 45:部分退货 50:全部退货")
    private Integer orderAfterSaleState;

    @ApiModelProperty("活动所属专区名称")
    private String transformersDistrictName;

    @ApiModelProperty("售前信息")
    private String preSalesPeople;

    @ApiModelProperty("售后信息")
    private String afterSalesPeople;

    @ApiModelProperty("订单商品明细")
    private List<ShopYflOrderDetailVo> shopYflOrderDetailVo;

    @ApiModelProperty("订单收货地址")
    private ShopOrderAddress shopOrderAddress;

    @ApiModelProperty("发货单信息")
    private List<SupplierOrderDeliveryVo> subOrderDeliveryVoList;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("订单类型(1:实物订单 2:虚拟订单 3:服务订单)")
    private Integer orderModel;

    @ApiModelProperty("虚拟商品订单类型 1：直冲 2：卡密")
    private String virtualType;

    @ApiModelProperty("充值账号")
    private String rechargeAccount;
}
