package com.ly.yph.core.http;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.net.HttpHeaders;
import com.ly.yph.core.base.exception.types.DataRequestException;
import com.ly.yph.core.http.annotation.HttpRecorder;
import com.yomahub.tlog.okhttp.TLogOkHttpInterceptor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.*;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 请求帮助类
 *
 * <AUTHOR>
 * @date 2022/01/18
 */
@SuppressWarnings("DuplicatedCode")
@Slf4j
@Data
public class RequestHelper {
    private static final OkHttpClient client;
    private static final TLogOkHttpInterceptor tlogInterceptor;

    static {
        tlogInterceptor = new TLogOkHttpInterceptor();
        client = new OkHttpClient.Builder().addInterceptor(tlogInterceptor).connectTimeout(10, TimeUnit.SECONDS).callTimeout(10, TimeUnit.SECONDS).writeTimeout(10, TimeUnit.SECONDS).followRedirects(true).build();
    }

    public Integer retryTime;
    public Integer retryInterval;
    public String resultFiledName;
    public String codeFieldName;
    public String successCode;
    public String resultSuccessName;
    public Integer retryIntervalFactor;
    public String errorMsgField;

    private void initConfig(Integer retryTime, Integer retryInterval, String resultFiledName, String codeFieldName, String successCode, String resultPrefix, String resultSuccessName) {
        this.retryTime = retryTime;
        this.retryInterval = retryInterval;
        this.resultFiledName = resultFiledName;
        this.codeFieldName = codeFieldName;
        this.successCode = successCode;
        this.resultSuccessName = resultSuccessName;
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "POST")
    public JSONArray doRequestArray(String apiUrl, JSONObject params, String messagePrefix) {
        return this.doRequestArray(apiUrl, params, messagePrefix, RequestMethod.POST.name(), "");
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "POST")
    public JSONObject doRequestObject(String apiUrl, JSONObject params, String messagePrefix) {
        return this.doRequestObject(apiUrl, params, messagePrefix, RequestMethod.POST.name(), "");
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "POST")
    public void doRequestNothing(String apiUrl, JSONObject params, String messagePrefix) {
        this.doRequestNothing(apiUrl, params, messagePrefix, RequestMethod.POST.name(), "");
    }

    /**
     * 用来解析jsonobjext
     *
     * @param result 结果
     * @return {@code JSONObject}
     */
    public JSONObject getJsonObject(String result) {
        JSONObject jo;
        try {
            jo = JSONUtil.parseObj(result);
        } catch (Exception ex) {
            log.error(StrUtil.format("请求失败，返回了非JSON内容：{}, ex stack:{}", result, ExceptionUtil.stacktraceToString(ex)));
            return null;
        }
        return jo;
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONArray doRequestArray(String apiUrl, JSONObject params, String messagePrefix, String method, String responsePrefix) {
        var fb = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        var request = new Request.Builder().url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : fb).build();
        return requestArray(apiUrl, params, messagePrefix, responsePrefix, request);
    }

    private void checkToken(String eResp) {
        if (eResp.contains("token")) {
            DataRequestException dExpt = new DataRequestException();
            dExpt.setResponse(eResp);
            throw dExpt;
        }
    }

    /**
     * 直接结果返回
     *
     * @param apiUrl        apiurl
     * @param params        参数个数
     * @param messagePrefix 消息前缀
     * @param method        方法
     * @return {@code JSONObject}
     */
    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doSimpleRequestObject(String apiUrl, JSONObject params, String messagePrefix, String method) {
        RequestBody formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        Request request = new Request.Builder().url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return simpleRequestObject(apiUrl, params, messagePrefix, request);
    }


    /**
     * @param params 参数个数
     * @return {@code JSONObject}
     */
    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doRequestObject(String apiUrl, JSONObject params, String messagePrefix, String method, String responsePrefix) {
        var formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        var request = new Request.Builder().url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return requestObject(apiUrl, params, messagePrefix, responsePrefix, request);
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doRequestObjectHutool(String apiUrl, JSONObject params, String messagePrefix, String method, String responsePrefix) {
        var formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        var request = new Request.Builder().url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return requestObjectHutool(apiUrl, params, messagePrefix, responsePrefix, request);
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doRequestObjectHutoolForm(String apiUrl, JSONObject params, String messagePrefix, String method) {
        var formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/x-www-form-urlencoded"));
        var request = new Request.Builder().url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return requestObjectHutoolForm(apiUrl, params, messagePrefix, request);
    }

    //给京东工业品用
    public JSONObject requestObjectHutoolForm(String apiUrl, JSONObject params, String messagePrefix, Request request) {
        var retryTimes = 0;
        var lastTime = System.currentTimeMillis();

        var eResp = "";
        var eRes = "";
        for (; ; ) {
            if (retryTimes > 0) {
                if (System.currentTimeMillis() - lastTime < Math.pow(this.getRetryIntervalFactor(), retryTimes) * this.getRetryInterval() * 1000 - RandomUtil.randomInt(1000)) {
                    Thread.yield();
                    continue;
                }
                checkToken(eResp);
                lastTime = System.currentTimeMillis();
                log.error(StrUtil.format("请求[{}]发生重试，参数：[{}],当前次数{}，总共可重试次数{}", apiUrl, JSON.toJSONString(params), retryTimes, this.getRetryTime()));
            }

            if (retryTimes++ >= this.getRetryTime()) {
                String error = StrUtil.format("{}：请求超过" + this.getRetryTime() + "次：url：[{}],参数：[{}]", messagePrefix, apiUrl, JSON.toJSONString(params));
                log.error(error);
                var dex = new DataRequestException(error);
                dex.setParams(JSON.toJSONString(params));
                dex.setUrl(apiUrl);
                dex.setResponse(eResp);
                dex.setResult(eRes);
                throw dex;
            }

            String result = "";
            try {
                result = eResp = HttpUtil.post(apiUrl, params);
                if (StrUtil.isBlank(result)) {
                    return null;
                }
                log.info("【京东iop预订单】返回值：" + result);
            } catch (Exception ex) {
                eResp = ex.getMessage();
                log.error(StrUtil.format("{}：http 请求产生异常:{}", messagePrefix, ExceptionUtil.stacktraceToString(ex)));
                continue;
            }

            if (result.startsWith("\"")  && result.endsWith("\"") ){
                result =  result.substring(1, result.length() - 1).replace("\\\"", "\"");
            }

            JSONObject resultObject = null;
            try {
                resultObject = JSONUtil.parseObj(result);
            } catch (Exception e) {
                resultObject = JSONUtil.parseObj(result.replace("\\\"", "\""));
            }


            if (resultObject.getBool("success")) {
                var rest = JSONUtil.getByPath(resultObject, StrUtil.strip(  "result", "."), null);
                if (rest == null) {
                    continue;
                } else {
                    if (rest instanceof String || rest instanceof Integer || rest instanceof Double || rest instanceof Float || rest instanceof Boolean) {
                        return new JSONObject();
                    }
                    return (JSONObject) rest;
                }
            } else {
                var dex = new DataRequestException();
                dex.setUrl(apiUrl);
                dex.setResponse(resultObject.toString());
                dex.setResult(resultObject.getStr("resultMessage"));
                throw dex;
//                log.error(StrUtil.format("{}：请求【获取消息】返回错误：{}，参数：{}，返回消息：{}", messagePrefix, apiUrl, JSON.toJSONString(params), resultObject));
//                eRes = ProcessError(resultObject);
//                if (eRes.equals("")) {
//                    eRes = JSON.toJSONString(resultObject);
//                }
//                continue;
            }
        }
    }


    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doSimpleRequestObjHutool(String apiUrl, JSONObject params, String messagePrefix, String method) {
        Map<String, String> paramsMap = new HashMap<>(19);
        params.entrySet().stream().forEach((item) -> {
            paramsMap.put(item.getKey(), item.getValue().toString());
        });
        RequestBody formBody = RequestBody.create(HttpUtil.toParams(paramsMap), MediaType.parse("application/x-www-form-urlencoded"));
        Request request = new Request.Builder().header(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded").url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return SimpleRequestObjHutool(apiUrl, params, messagePrefix);
    }

    public JSONObject SimpleRequestObjHutool(String apiUrl, JSONObject params, String messagePrefix) {
        DataRequestException dataRequestException = new DataRequestException("请求产生异常");
        dataRequestException.setParams(JSON.toJSONString(params));
        dataRequestException.setUrl(apiUrl);
        String result = "";
        try {
            result = HttpUtil.post(apiUrl, params);
            if (StrUtil.isBlank(result)) {
                return null;
            }
//            log.info("【京东iop】返回值：" + result);
        } catch (Exception ex) {
            log.error(StrUtil.format("{}：http 请求产生异常:{}", messagePrefix, ExceptionUtil.stacktraceToString(ex)));
        }
        if (result.startsWith("\"")  && result.endsWith("\"") ){
            result =  result.substring(1, result.length() - 1).replace("\\\"", "\"");
        }
        try {
            return JSONUtil.parseObj(result);
        } catch (Exception ex) {
//            log.error(StrUtil.format("请求失败，返回了非JSON内容：{}, ex stack:{}", result, ExceptionUtil.stacktraceToString(ex)));
            return JSONUtil.parseObj(result.replace("\\\"", "\""));
        }
    }


    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public void doRequestNothing(String apiUrl, JSONObject params, String messagePrefix, String method, String responsePrefix) {
        var formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        var request = new Request.Builder().url(apiUrl).method(method, method == RequestMethod.GET.name() ? null : formBody).build();
        requestNothing(apiUrl, params, messagePrefix, responsePrefix, request);
    }

    public JSONObject requestObject(String apiUrl, JSONObject params, String messagePrefix, String responsePrefix, Request request) {
        var retryTimes = 0;
        var lastTime = System.currentTimeMillis();

        var eResp = "";
        var eRes = "";
        for (; ; ) {
            if (retryTimes > 0) {
                if (System.currentTimeMillis() - lastTime < Math.pow(this.getRetryIntervalFactor(), retryTimes) * this.getRetryInterval() * 1000 - RandomUtil.randomInt(1000)) {
                    Thread.yield();
                    continue;
                }
                checkToken(eResp);
                lastTime = System.currentTimeMillis();
                log.error(StrUtil.format("请求[{}]发生重试，参数：[{}],当前次数{}，总共可重试次数{}", apiUrl, JSON.toJSONString(params), retryTimes, this.getRetryTime()));
            }

            if (retryTimes++ >= this.getRetryTime()) {
                String error = StrUtil.format("{}：请求超过" + this.getRetryTime() + "次：url：[{}],参数：[{}]", messagePrefix, apiUrl, JSON.toJSONString(params));
                log.error(error);
                var dex = new DataRequestException(error);
                dex.setParams(JSON.toJSONString(params));
                dex.setUrl(apiUrl);
                dex.setResponse(eResp);
                dex.setResult(eRes);
                throw dex;
            }

            String result = "";
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    eResp = JSON.toJSONString(response) + response.body().string();
                    log.error(StrUtil.format("{}：http 请求产生异常:{},请求返回内容:{}", messagePrefix, "返回码不是200", eResp));
                    continue;
                }

                result = eResp = response.body().string();
                if (StrUtil.isBlank(result)) {
                    return null;
                }
            } catch (Exception ex) {
                eResp = ex.getMessage();
                log.error(StrUtil.format("{}：http 请求产生异常:{}", messagePrefix, ExceptionUtil.stacktraceToString(ex)));
                continue;
            }

            JSONObject resultObject = getJsonObject(result);
            if (resultObject == null) {
                eRes = result;
                continue;
            }

            if (Arrays.stream(successCode.split(",")).anyMatch(x -> x.equals(JSONUtil.getByPath(resultObject, StrUtil.strip(responsePrefix + "." + getCodeFieldName(), "."), null)))) {
                var rest = JSONUtil.getByPath(resultObject, StrUtil.strip(responsePrefix + "." + getResultFiledName(), "."), null);
                if (rest == null) {
                    continue;
                } else {
                    if (rest instanceof String || rest instanceof Integer || rest instanceof Double || rest instanceof Float || rest instanceof Boolean) {
                        return new JSONObject();
                    }
                    return (JSONObject) rest;
                }
            } else {
                log.error(StrUtil.format("{}：请求【获取消息】返回错误：{}，参数：{}，返回消息：{}", messagePrefix, apiUrl, JSON.toJSONString(params), resultObject));
                eRes = ProcessError(resultObject);
                if (eRes.equals("")) {
                    eRes = JSON.toJSONString(resultObject);
                }
                continue;
            }
        }
    }

    public JSONObject requestObjectHutool(String apiUrl, JSONObject params, String messagePrefix, String responsePrefix, Request request) {
        var retryTimes = 0;
        var lastTime = System.currentTimeMillis();

        var eResp = "";
        var eRes = "";
        for (; ; ) {
            if (retryTimes > 0) {
                if (System.currentTimeMillis() - lastTime < Math.pow(this.getRetryIntervalFactor(), retryTimes) * this.getRetryInterval() * 1000 - RandomUtil.randomInt(1000)) {
                    Thread.yield();
                    continue;
                }
                checkToken(eResp);
                lastTime = System.currentTimeMillis();
                log.error(StrUtil.format("请求[{}]发生重试，参数：[{}],当前次数{}，总共可重试次数{}", apiUrl, JSON.toJSONString(params), retryTimes, this.getRetryTime()));
            }

            if (retryTimes++ >= this.getRetryTime()) {
                String error = StrUtil.format("{}：请求超过" + this.getRetryTime() + "次：url：[{}],参数：[{}]", messagePrefix, apiUrl, JSON.toJSONString(params));
                log.error(error);
                var dex = new DataRequestException(error);
                dex.setParams(JSON.toJSONString(params));
                dex.setUrl(apiUrl);
                dex.setResponse(eResp);
                dex.setResult(eRes);
                throw dex;
            }

            String result = "";
            try {
                result = eResp = HttpUtil.post(apiUrl, params);
                if (StrUtil.isBlank(result)) {
                    return null;
                }
                log.info("【京东预订单】返回值：" + result);
            } catch (Exception ex) {
                eResp = ex.getMessage();
                log.error(StrUtil.format("{}：http 请求产生异常:{}", messagePrefix, ExceptionUtil.stacktraceToString(ex)));
                continue;
            }

            JSONObject resultObject = getJsonObject(result);
            if (resultObject == null) {
                eRes = result;
                continue;
            }

            if (Arrays.stream(successCode.split(",")).anyMatch(x -> x.equals(JSONUtil.getByPath(resultObject, StrUtil.strip(responsePrefix + "." + getCodeFieldName(), "."), null)))) {
                var rest = JSONUtil.getByPath(resultObject, StrUtil.strip(responsePrefix + "." + getResultFiledName(), "."), null);
                if (rest == null) {
                    continue;
                } else {
                    if (rest instanceof String || rest instanceof Integer || rest instanceof Double || rest instanceof Float || rest instanceof Boolean) {
                        return new JSONObject();
                    }
                    return (JSONObject) rest;
                }
            } else {
                var dex = new DataRequestException();
                dex.setUrl(apiUrl);
                dex.setResponse(resultObject.toString());
                dex.setResult(resultObject.getStr("resultMessage"));
                throw dex;
//                log.error(StrUtil.format("{}：请求【获取消息】返回错误：{}，参数：{}，返回消息：{}", messagePrefix, apiUrl, JSON.toJSONString(params), resultObject));
//                eRes = ProcessError(resultObject);
//                if (eRes.equals("")) {
//                    eRes = JSON.toJSONString(resultObject);
//                }
//                continue;
            }
        }
    }

    public JSONArray requestArray(String apiUrl, JSONObject params, String messagePrefix, String responsePrefix, Request request) {
        int rt = 0;

        long lt = System.currentTimeMillis();
        String eResp = "";
        String eRes = "";
        for (; ; ) {
            if (rt > 0) {
                if (System.currentTimeMillis() - lt < Math.pow(this.getRetryIntervalFactor(), rt) * this.getRetryInterval() * 1000 - RandomUtil.randomInt(1000)) {
                    Thread.yield();
                    continue;
                }
                lt = System.currentTimeMillis();
                // 处理token问题
                checkToken(eResp);
                log.error(StrUtil.format("请求[{}]发生重试，参数：[{}],当前次数{}，总共可重试次数{}", apiUrl, JSON.toJSONString(params), rt, this.getRetryTime()));
            }
            if (rt++ >= this.getRetryTime()) {
                var err = StrUtil.format("{}：请求超过" + this.getRetryTime() + "次：url：[{}],参数：[{}]", messagePrefix, apiUrl, JSON.toJSONString(params));
                log.error(err);
                DataRequestException dExpt = new DataRequestException(err);
                dExpt.setParams(JSON.toJSONString(params));
                dExpt.setUrl(apiUrl);
                dExpt.setResponse(eResp);
                dExpt.setResult(eRes);
                throw dExpt;
            }

            var res = "";
            try (var resp = client.newCall(request).execute()) {
                if (!resp.isSuccessful()) {
                    eResp = JSON.toJSONString(resp) + resp.body().string();
                    log.error(StrUtil.format("{}：http 请求产生异常:{},请求返回内容:{}", messagePrefix, "返回码不是200", eResp));
                    continue;
                }

                res = eResp = resp.body().string();
                if (StrUtil.isBlank(res)) {
                    return null;
                }
            } catch (Exception ex) {
                //eResp = ex.toString();
                log.error(StrUtil.format("{}：http 请求产生异常:{}", messagePrefix, ExceptionUtil.stacktraceToString(ex)));
                continue;
            }

            // 如果没有外层的返回码，直接返回
            var resultObject = getJsonObject(res);
            if (resultObject == null) {
                eRes = res;
                continue;
            }

            if (Arrays.stream(successCode.split(",")).anyMatch(x -> x.equals(JSONUtil.getByPath(resultObject, StrUtil.strip(responsePrefix + "." + getCodeFieldName(), "."), null) + ""))) {
                var ra = JSONUtil.getByPath(resultObject, StrUtil.strip(responsePrefix + "." + getResultFiledName(), "."), new JSONArray());
                if (ra == null) {
                    eRes = res;
                    continue;
                }
                return ra;
            } else {
                log.error(StrUtil.format("{}：请求【获取消息】返回错误：{}，参数：{}，返回消息：{}", messagePrefix, apiUrl, JSON.toJSONString(params), resultObject));
                eRes = ProcessError(resultObject);
                if (eRes.equals("")) {
                    eRes = JSON.toJSONString(resultObject);
                }
                continue;
            }
        }
    }

    public void requestNothing(String apiUrl, JSONObject params, String messagePrefix, String responsePrefix, Request request) {
        var eResp = "";
        var eRes = "";
        var retryTimes = 0;
        var lastTime = System.currentTimeMillis();
        for (; ; ) {
            if (retryTimes > 0) {
                if (System.currentTimeMillis() - lastTime < this.getRetryInterval() * 1000) {
                    Thread.yield();
                    continue;
                }
                lastTime = System.currentTimeMillis();
                checkToken(eResp);
                log.error(StrUtil.format("请求[{}]发生重试，参数：[{}],当前次数{}，总共可重试次数{}", apiUrl, JSON.toJSONString(params), retryTimes, this.getRetryTime()));
            }
            if (retryTimes++ >= this.getRetryTime()) {
                String error = StrUtil.format("{}：请求超过" + this.getRetryTime() + "次：url：[{}],参数：[{}]", messagePrefix, apiUrl, JSON.toJSONString(params));
                log.error(error);
                var dex = new DataRequestException(error);
                dex.setParams(JSON.toJSONString(params));
                dex.setUrl(apiUrl);
                dex.setResponse(eResp);
                dex.setResult(eRes);
                throw dex;
            }
            var result = "";
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error(StrUtil.format("{}：http 请求产生异常:{},请求返回内容:{}", messagePrefix, "返回码不是200", response.body().string()));
                    eResp = JSON.toJSONString(response);
                    continue;
                }
                result = eResp = response.body().string();
                if (StrUtil.isBlank(result)) {
                    return;
                }
            } catch (Exception ex) {
                log.error(StrUtil.format("{}：http 请求产生异常:{}", messagePrefix, ExceptionUtil.stacktraceToString(ex)));
                eResp = ex.toString();
                continue;
            }
            var res = getJsonObject(result);
            if (res == null) {
                eRes = "";
                continue;
            }

            if (Arrays.stream(successCode.split(",")).anyMatch(x -> x.equals(JSONUtil.getByPath(res, StrUtil.strip(responsePrefix + "." + getCodeFieldName(), "."), null)))) {
                return;
            } else {
                log.error(StrUtil.format("{}：请求【获取消息】返回错误：{}，参数：{}，返回消息：{}", messagePrefix, apiUrl, JSON.toJSONString(params), res));
                eRes = ProcessError(res);
                if (eRes.equals("")) {
                    eRes = JSON.toJSONString(res);
                }
                continue;
            }
        }
    }

    /**
     * @param params 参数个数
     * @return {@code JSONObject}
     */
    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doSimpleRequestObject(String apiUrl, JSONObject params, String messagePrefix, String method, String responsePrefix, Map<String, String> headersParams) {
        Headers.Builder headersbuilder = new Headers.Builder();
        if (MapUtil.isNotEmpty(headersParams)) {
            Iterator<String> iterator = headersParams.keySet().iterator();
            String key = "";
            while (iterator.hasNext()) {
                key = iterator.next();
                headersbuilder.add(key, headersParams.get(key));
            }
        }
        var formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        var request = new Request.Builder().headers(headersbuilder.build()).url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return simpleRequestObject(apiUrl, params, messagePrefix, request);
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doLtSimpleRequestObject(String apiUrl, JSONObject params, String messagePrefix, String method, String responsePrefix, Map<String, String> headersParams) {
        Headers.Builder headersbuilder = new Headers.Builder();
        if (MapUtil.isNotEmpty(headersParams)) {
            Iterator<String> iterator = headersParams.keySet().iterator();
            String key = "";
            while (iterator.hasNext()) {
                key = iterator.next();
                headersbuilder.add(key, headersParams.get(key));
            }
        }
        var formBody = RequestBody.create(JSON.toJSONString(params), MediaType.parse("application/json"));
        var request = new Request.Builder().headers(headersbuilder.build()).url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        //
        String errorResponse = "";
        String errorResult = "";
        String result = "";
        DataRequestException dataRequestException = new DataRequestException("请求产生异常");
        dataRequestException.setParams(JSON.toJSONString(params));
        dataRequestException.setUrl(apiUrl);
        try (Response response = client.newCall(request).execute()) {
            result = response.body().string();
            if (StrUtil.isBlank(result)) {
                log.info(StrUtil.format("【simpleRequestObject】url:{},param:{},response:{} is result empty", apiUrl, params, result));
                dataRequestException.setResult(JSON.toJSONString(errorResult));
                dataRequestException.setResponse(result);
                throw dataRequestException;
            }
        } catch (Exception e) {
            log.info(StrUtil.format("Exception【simpleRequestObject】url:{},param:{},exection:{} is result empty", apiUrl, params, ExceptionUtil.stacktraceToString(e)));
            e.printStackTrace();
        }
        return getJsonObject(result);
    }


    public JSONObject simpleRequestObject(String apiUrl, JSONObject params, String messagePrefix, Request request) {
        String errorResponse = "";
        String errorResult = "";
        String result = "";
        DataRequestException dataRequestException = new DataRequestException("请求产生异常");
        dataRequestException.setParams(JSON.toJSONString(params));
        dataRequestException.setUrl(apiUrl);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error(StrUtil.format("{}：http 请求产生异常:{},请求返回内容:{}", messagePrefix, "返回码不是200", response.body().string()));
                errorResponse = JSON.toJSONString(response);
                dataRequestException.setResponse(errorResponse);
                throw dataRequestException;
            }

            result = response.body().string();
            if (StrUtil.isBlank(result)) {
                log.info(StrUtil.format("【simpleRequestObject】url:{},param:{},response:{} is result empty", apiUrl, params, result));
                dataRequestException.setResult(JSON.toJSONString(errorResult));
                dataRequestException.setResponse(result);
                throw dataRequestException;
            }
        } catch (Exception e) {
            log.info(StrUtil.format("Exception【simpleRequestObject】url:{},param:{},exection:{} is result empty", apiUrl, params, ExceptionUtil.stacktraceToString(e)));
            e.printStackTrace();
        } finally {

        }
        return getJsonObject(result);
    }

    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONObject doSimpleRequestObj(String apiUrl, JSONObject params, String messagePrefix, String method) {
        Map<String, String> paramsMap = new HashMap<>(19);
        params.entrySet().stream().forEach((item) -> {
            paramsMap.put(item.getKey(), item.getValue().toString());
        });
        RequestBody formBody = RequestBody.create(HttpUtil.toParams(paramsMap), MediaType.parse("application/x-www-form-urlencoded"));
        Request request = new Request.Builder().header(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded").url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return simpleRequestObject(apiUrl, params, messagePrefix, request);
    }

    public JSONArray simpleRequestArray(String apiUrl, JSONObject params, String messagePrefix, Request request) {
        String errorResponse = "";
        String errorResult = "";
        String result = "";
        DataRequestException dataRequestException = new DataRequestException("请求产生异常");
        dataRequestException.setParams(JSON.toJSONString(params));
        dataRequestException.setUrl(apiUrl);
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error(StrUtil.format("{}：http 请求产生异常:{},请求返回内容:{}", messagePrefix, "返回码不是200", response.body().string()));
                errorResponse = JSON.toJSONString(response);
                dataRequestException.setResponse(errorResponse);
                throw dataRequestException;
            }

            result = response.body().string();
            log.info("京东iop返回,{}",result);
            if (StrUtil.isBlank(result)) {
                log.info(StrUtil.format("【simpleRequestObject】url:{},param:{},response:{} is result empty", apiUrl, params, result));
                dataRequestException.setResult(JSON.toJSONString(errorResult));
                dataRequestException.setResponse(result);
                throw dataRequestException;
            }
        } catch (Exception e) {
            log.info(StrUtil.format("Exception【simpleRequestObject】url:{},param:{},exection:{} is result empty", apiUrl, params, ExceptionUtil.stacktraceToString(e)));
            e.printStackTrace();
        }
        var resultObject = getJsonObject(result);
        try {
            return JSONUtil.parseArray(resultObject.get("result"));
        } catch (Exception ex) {
            log.error(StrUtil.format("请求失败，返回了非JSON内容：{}, ex stack:{}", result, ExceptionUtil.stacktraceToString(ex)));
            return new JSONArray();
        }
    }


    @HttpRecorder(url = "#apiUrl", params = "#params", method = "#method")
    public JSONArray doSimpleRequestArray(String apiUrl, JSONObject params, String messagePrefix, String method) {
        Map<String, String> paramsMap = new HashMap<>(19);
        params.entrySet().stream().forEach((item) -> {
            paramsMap.put(item.getKey(), item.getValue().toString());
        });
        RequestBody formBody = RequestBody.create(HttpUtil.toParams(paramsMap), MediaType.parse("application/x-www-form-urlencoded"));
        Request request = new Request.Builder().header(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded").url(apiUrl).method(method, method.equals(RequestMethod.GET.name()) ? null : formBody).build();
        return simpleRequestArray(apiUrl, params, messagePrefix, request);
    }


    private String ProcessError(JSONObject result) {
        if (result == null) {
            return "不存在返回内容";
        }
        String jsonString = JSON.toJSONString(result);

        List<String> split = StrUtil.split(errorMsgField, ",");

        for (String s : split) {
//            String byPath = JSONUtil.getByPath(result, s, "");
//            if (!byPath.equals("")) {
//                return byPath;
//            }

            String regex = "\"" + s + "\"\\s*:\\s*\"(.*?)\"";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(jsonString);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        log.error("出现未配置的错误字段");
        return "";
    }
}
