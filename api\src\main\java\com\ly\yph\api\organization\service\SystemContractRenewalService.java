package com.ly.yph.api.organization.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.yph.api.electsign.service.ContractSignService;
import com.ly.yph.api.electsign.util.ConvertNumberToUppercase;
import com.ly.yph.api.electsign.vo.ContractOutOrgVo;
import com.ly.yph.api.electsign.vo.ContractOutUserVo;
import com.ly.yph.api.goods.workflow.WorkFlowManager;
import com.ly.yph.api.organization.controller.organization.vo.organization.SystemContractVO;
import com.ly.yph.api.organization.convert.organization.ContractRenewalConvert;
import com.ly.yph.api.organization.dto.StartRenewalDto;
import com.ly.yph.api.organization.dto.SystemContractRenewalUpdateDto;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.enums.OrganizationTypeEnum;
import com.ly.yph.api.organization.mapper.SystemContractRenewalMapper;
import com.ly.yph.api.organization.vo.ContractApprovalDetailVo;
import com.ly.yph.api.organization.vo.ContractApprovalRemindVo;
import com.ly.yph.api.organization.vo.ContractDetailVo;
import com.ly.yph.api.organization.vo.ContractRenewalApprovalVo;
import com.ly.yph.api.owrdandpdf.PdfContractProcessor;
import com.ly.yph.api.supplier.dto.ShopSupplierContractDto;
import com.ly.yph.api.supplier.dto.SystemContractQueryDto;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierDetail;
import com.ly.yph.api.supplier.service.ShopSupplierDetailService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.supplier.service.SupplierContractRealService;
import com.ly.yph.api.supplier.service.SystemContractService;
import com.ly.yph.api.supplier.vo.MySupplierVo;
import com.ly.yph.api.system.service.EmailSendLogService;
import com.ly.yph.api.system.service.FileService;
import com.ly.yph.api.workflow.entity.Workflow;
import com.ly.yph.api.workflow.entity.WorkflowAuditRecord;
import com.ly.yph.api.workflow.mapper.WorkflowMapper;
import com.ly.yph.api.workflow.service.WorkFlowService;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.core.util.PinYinUtil;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 合同续签表(SystemContractRenewal)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-13 18:38:36
 */
@Slf4j
@Service("systemContractRenewalService")
public class SystemContractRenewalService extends ServiceImpl<SystemContractRenewalMapper, SystemContractRenewalEntity> {

    @Resource
    private WorkFlowManager workFlowManager;

    @Resource
    private WorkFlowService workFlowService;

    @Resource
    private WorkflowMapper workFlowMapper;

    @Resource
    private SystemContractRenewalMapper contractRenewalMapper;

    @Resource
    private SupplierContractRealService contractRealService;

    @Resource
    private SystemContractService systemContractService;

    @Resource
    private SystemOrganizationService organizationService;

    @Resource
    private SystemUsersService usersService;

    @Resource
    private MailService mailService;
    @Resource
    private EmailSendLogService emailSendLogService;

    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private ShopSupplierDetailService shopSupplierDetailService;

    @Resource
    private ContractSignService contractSignService;

    @Value("${workflow.contract.approver.userName:07739}")
    private String contractUserName;

    @Resource
    private FileService fileService;


    @Transactional(rollbackFor = Exception.class)
    public void saveContractRenewal(StartRenewalDto startRenewalDto){
        SystemOrganization organization = organizationService.getById(startRenewalDto.getCompanyOrgId());
        if(organization.getOrganizationType().equals(OrganizationTypeEnum.SYSTEM.getCode())){
            SystemContractRenewalEntity contractRenewal = ContractRenewalConvert.INSTANCE.convert(startRenewalDto);
            contractRenewal.setTenantId(TenantContextHolder.getRequiredTenantId());
            contractRenewal.setSignType(Objects.isNull(startRenewalDto.getOldContractId()) ? 0 : 1);
            contractRenewal.setContractCode(this.createContractCode(startRenewalDto.getSupplierName()));
            contractRenewal.setContractName("平台入驻商家通则协议_"+startRenewalDto.getSupplierName());
            MySupplierVo shopSupplier = shopSupplierService.querySupplierInfo(startRenewalDto.getSupplierId());
            contractRenewal.setContacterBAddress(shopSupplier.getRegisterAddress());
            contractRenewal.setContacterBName(shopSupplier.getAdminNickname());
            contractRenewal.setContacterBMail(shopSupplier.getAdminEmail());
            contractRenewal.setContacterBPhone(shopSupplier.getAdminMobile());

            this.save(contractRenewal);
            workFlowManager.submitContractApproval(contractRenewal);
            //发送邮件给供应链
            sendToEmail(startRenewalDto);
            // 去生态聚合中心创建组织及代办人，
            createOrgAndUser(startRenewalDto.getSupplierId());
        }else {
            Assert.notEmpty(startRenewalDto.getContractCode(), () -> new ParameterException("请填写完整的合同信息"));
            Assert.notEmpty(startRenewalDto.getContractName(), () -> new ParameterException("请填写完整的合同信息"));
            Assert.notEmpty(startRenewalDto.getAttachmentUrl(), () -> new ParameterException("请填写完整的合同信息"));

            SystemContractRenewalEntity check = baseMapper.selectOne(new LambdaQueryWrapperX<SystemContractRenewalEntity>()
                    .eq(SystemContractRenewalEntity::getContractCode,startRenewalDto.getContractCode())
                    .eq(SystemContractRenewalEntity::getCompanyOrgId,startRenewalDto.getCompanyOrgId())
                    .eq(SystemContractRenewalEntity::getSupplierCode,startRenewalDto.getSupplierCode())
                    .eq(SystemContractRenewalEntity::getContractApprovalState,0));
            if(Objects.nonNull(check)){
                throw new ParameterException("该合同正在审核中，请等待审核结果");
            }

            ShopSupplierContractDto dto = new ShopSupplierContractDto();
            BeanUtil.copyProperties(startRenewalDto,dto);
            dto.setSignCompanyId(startRenewalDto.getCompanyOrgId());
            dto.setSignCompanyCode(startRenewalDto.getCompanyCode());
            dto.setSignCompany(startRenewalDto.getCompanyName());
            dto.setContractScannedUrl(startRenewalDto.getAttachmentUrl());
            dto.setContractStartDate(startRenewalDto.getValidityStart());
            dto.setContractEndDate(startRenewalDto.getValidityEnd());
            dto.setIsStart(0);
            systemContractService.saveContract(dto);
        }

    }

    /**
     * 生成合同编码   // LYZL-LCY-SC+ {年月日} +_+ {供应商全称拼音头字母}
     * @return
     */
    private String createContractCode(String supplierName) {
        StringBuffer contractCode = new StringBuffer();
        contractCode.append("LYZL-LCY-SC")
                .append(DateUtils.format(new Date(), DateUtils.DATE_PATTERN_NO_LINE)).append("_")
                .append(PinYinUtil.getFirstSpell(supplierName).toUpperCase());
        return contractCode.toString();
    }

    /**
     * 同步更新经办人信息到生态聚合中心
     * @param supplierId
     */
    private void createOrgAndUser(Long supplierId) {
        boolean isUpdate = false;
        ShopSupplier shopSupplier = shopSupplierService.getById(supplierId);
        if(StringUtils.isBlank(shopSupplier.getEAccountId())){
            if(StringUtils.isBlank(shopSupplier.getOperatorName())){
                throw new ParameterException("经办人信息未填写");
            }
            // 同步经办人信息
            ContractOutUserVo contractUserVo = getContractOutUserVo(shopSupplier);
            JSONObject innerUser = contractSignService.createOutUser(contractUserVo);
            if(innerUser!=null){
                isUpdate = true;
                shopSupplier.setEAccountId(innerUser.getStr("accountId"));
            }else{
                throw new ParameterException("同步经办人到生态聚合中心失败");
            }
        }
        if(StringUtils.isBlank(shopSupplier.getEOrganizeId())){
            // 同步供应商信息
            ContractOutOrgVo contractUserVo = getContractOutOrgVo(shopSupplier);
            JSONObject innerUser = contractSignService.createOutOrg(contractUserVo);
            if(innerUser!=null){
                isUpdate = true;
                shopSupplier.setEOrganizeId(innerUser.getStr("organizeId"));
            }else{
                throw new ParameterException("同步供应商数据到生态聚合中心失败");
            }
        }
        if(isUpdate){
            shopSupplierService.updateById(shopSupplier);
        }
    }

    private ContractOutOrgVo getContractOutOrgVo(ShopSupplier shopSupplier) {
        ContractOutOrgVo contractUserVo = new ContractOutOrgVo();
        contractUserVo.setAgentAccountId(shopSupplier.getEAccountId());
        contractUserVo.setOrganizeNo(shopSupplier.getCreditCode());
        contractUserVo.setOrganizeName(shopSupplier.getSupplierFullName());
        contractUserVo.setEmail(shopSupplier.getOperatorEmail());
        contractUserVo.setLicenseNumber(shopSupplier.getCreditCode());
        contractUserVo.setLicenseType("SOCNO");
        return contractUserVo;
    }

    private ContractOutUserVo getContractOutUserVo(ShopSupplier shopSupplier) {
        ContractOutUserVo contractUserVo = new ContractOutUserVo();
        contractUserVo.setContactsEmail(shopSupplier.getOperatorEmail());
        contractUserVo.setName(shopSupplier.getOperatorName());
        contractUserVo.setLicenseNumber(shopSupplier.getOperatorIdCard());
        contractUserVo.setLicenseType("IDCard");
        contractUserVo.setContactsMobile(shopSupplier.getOperatorPhone());
        contractUserVo.setLoginMobile(shopSupplier.getOperatorPhone());
        contractUserVo.setUniqueId(shopSupplier.getOperatorIdCard());
        return contractUserVo;
    }

    public PageResp<ContractRenewalApprovalVo> approvalPage(PageReq pageReq, SystemContractQueryDto reqVO){
        LoginUser loginUser = LocalUserHolder.get();
        if(loginUser!=null && loginUser.getOrganizationType().equals(OrganizationTypeEnum.SYSTEM.getCode())){
            reqVO.setUserIds(loginUser.getId().toString());
        }
        IPage<ContractRenewalApprovalVo> iPage = contractRenewalMapper.approvalPage(DataAdapter.adapterPageReq(pageReq),reqVO);
        return DataAdapter.adapterPage(iPage,ContractRenewalApprovalVo.class);
    }

    /**
     *
     * @return 返回结果
     */
    public List<ContractApprovalRemindVo> queryApprovalRemind() {
        LoginUser user = LocalUserHolder.get();
        if(user.getOrganizationType().equals(OrganizationTypeEnum.SYSTEM.getCode())){
            return contractRenewalMapper.queryApprovalRemind();
        }else {
            //供应商查看
            return contractRealService.queryApprovalRemind(user.getEntityOrganizationCode());
        }
    }

    public ContractApprovalDetailVo queryApprovalDetail(Long contractRenewalId){
        ContractApprovalDetailVo approvalDetailVo = new ContractApprovalDetailVo();
        SystemContractRenewalEntity renewalEntity = this.baseMapper.selectById(contractRenewalId);
        approvalDetailVo.setContractRenewalEntity(renewalEntity);
        if(Objects.nonNull(renewalEntity.getOldContractId())){
            SystemContractVO contractVO = systemContractService.getBaseMapper().queryInfoByContractId(renewalEntity.getOldContractId());
            approvalDetailVo.setOldContractDetailVo(contractVO);
        }
        return approvalDetailVo;
    }

    public ContractDetailVo queryApprovalInfo(Long contractRenewalId){
        SystemContractVO contractVO = this.getBaseMapper().queryApprovalInfo(contractRenewalId);
        ContractDetailVo contractDetailVo = new ContractDetailVo();
        contractDetailVo.setContractDetailVo(contractVO);

        Workflow workflow = workFlowMapper.selectByBusId(contractRenewalId);
        if(Objects.nonNull(workflow)){
            List<WorkflowAuditRecord> auditRecordList = workFlowService.selectAuditRecordByWorkId(workflow.getBusId());
            contractDetailVo.setAuditRecordList(auditRecordList);
        }
        return contractDetailVo;
    }

    public ContractDetailVo queryContractDetail(Long contractId){
        SystemContractVO contractVO = systemContractService.getBaseMapper().queryInfoByContractId(contractId);
        ContractDetailVo contractDetailVo = new ContractDetailVo();
        contractDetailVo.setContractDetailVo(contractVO);
        Workflow workflow = workFlowMapper.selectByBusId(contractVO.getContractId());
        if(workflow==null && contractVO.getContractRenewalId()!=null){
           SystemContractRenewalEntity contractRenewal = this.getById(contractVO.getContractRenewalId());
           if(contractRenewal!=null){
               workflow = workFlowMapper.selectByBusId(contractRenewal.getId());
           }
        }

        if(Objects.nonNull(workflow)){
            List<WorkflowAuditRecord> auditRecordList = workFlowService.selectAuditRecordByWorkId(workflow.getBusId());
            contractDetailVo.setAuditRecordList(auditRecordList);
        }
        return contractDetailVo;
    }

    private SystemContractRenewalEntity getByContractCode(String contractCode) {
        QueryWrapper<SystemContractRenewalEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SystemContractRenewalEntity::getContractCode, contractCode).last("order by create_time desc limit 1");
        return this.getOne(queryWrapper);
    }

    public void sendToEmail(StartRenewalDto startRenewalDto){
        SystemUsers users = usersService.getUserByUsername(contractUserName.split(",")[0]);
        String emailTemplate = mailService.getContractStartApprovalTemplate(startRenewalDto.getContractCode(),
                startRenewalDto.getContractName(),
                startRenewalDto.getCompanyName(),
                startRenewalDto.getSupplierName());

        mailService.sendEmail("东风商城合同审批提醒", emailTemplate,
                users.getEmail());
        emailSendLogService.createEmailSendLog("东风商城合同审批提醒", emailTemplate, users.getEmail(), true);
    }

    public SystemContractRenewalEntity queryByOldContractId(Long oldContractId){
        return this.getBaseMapper().selectOne(new LambdaQueryWrapperX<SystemContractRenewalEntity>()
                .eq(SystemContractRenewalEntity::getOldContractId,oldContractId)
                .eq(SystemContractRenewalEntity::getContractApprovalState,1)
                .eq(SystemContractRenewalEntity::getRenewalType,1));
    }

    public void updateContractRenewal(SystemContractRenewalUpdateDto updateRenewalDto) {
        SystemContractRenewalEntity contractRenewal = new SystemContractRenewalEntity();
        BeanUtil.copyProperties(updateRenewalDto, contractRenewal);
        this.updateById(contractRenewal);
    }

    public String createEContract(Long contractId) {

        SystemContractRenewalEntity contractRenewal = this.getById(contractId);

        ShopSupplier shopSupplier = shopSupplierService.getById(contractRenewal.getSupplierId());

        ShopSupplierDetail detail = shopSupplierDetailService.getBySupplierId(contractRenewal.getSupplierId());

        Map<String, String> vo = this.initDataMap(contractRenewal, shopSupplier, detail);

        try{
          byte[] pdfBytes = PdfContractProcessor.makePdfBytes("pdf_templates/supplier_contract_template.pdf", vo);
          String url = fileService.uploadFileByByte("supplierContractProtocol", "", "pdf", pdfBytes);
          log.info("文件数据：{}", url);

          contractRenewal.setAttachmentUrl(url);
          contractRenewal.setUpdateTime(new Date());
          this.updateById(contractRenewal);
          return url;

        } catch (Exception e) {
            log.error("生成合同预览失败 {}", e);
            throw new ParameterException(e.getMessage());
        }
    }

    private Map<String, String> initDataMap(SystemContractRenewalEntity contractRenewal, ShopSupplier shopSupplier,  ShopSupplierDetail detail) {
        if(StringUtils.isBlank(detail.getFirstPerformanceBond())){
            throw new ParameterException("保证金不能为空");
        }

        if( detail.getDealWay()==2 && StringUtils.isBlank(detail.getPriceDiscount())){
            throw new ParameterException("协议折扣不能为空");
        }

        if(shopSupplier.getSettlementPeriod()<=0){
            throw new ParameterException("结算账期不能为空");
        }

        if(StringUtils.isBlank(shopSupplier.getCreditCode()) ||
                StringUtils.isBlank(shopSupplier.getRegisterAddress()) ||
                StringUtils.isBlank(shopSupplier.getInvoiceBank()) ||
                StringUtils.isBlank(shopSupplier.getInvoiceBankAccount())){
            throw new ParameterException("供应商信息不能为空");
        }

        if(StringUtils.isBlank(contractRenewal.getContacterAMail()) ||
                StringUtils.isBlank(contractRenewal.getContacterAName()) ||
                StringUtils.isBlank(contractRenewal.getContacterAAddress()) ||
                StringUtils.isBlank(contractRenewal.getContacterAMail()) ||
                StringUtils.isBlank(contractRenewal.getContacterBMail()) ||
                StringUtils.isBlank(contractRenewal.getContacterBName()) ||
                StringUtils.isBlank(contractRenewal.getContacterBPhone()) ||
                StringUtils.isBlank(contractRenewal.getContacterBAddress())){
            throw new ParameterException("联系人信息不能为空");
        }

        String firstPerformance = detail.getFirstPerformanceBond();
        if(detail.getFirstPerformanceBond().contains("/")){
            firstPerformance = detail.getFirstPerformanceBond().split("/")[0];
        }

        String[] startArray = contractRenewal.getValidityStart().split(" ")[0].split("-");
        String[] endArray = contractRenewal.getValidityEnd().split(" ")[0].split("-");

        Map<String, String> vo = new HashMap<>();
        vo.put("aMail", contractRenewal.getContacterAMail());
        vo.put("aName", contractRenewal.getContacterAName());
        vo.put("aPhone", contractRenewal.getContacterAPhone());
        vo.put("bMail", contractRenewal.getContacterBMail());
        vo.put("bName", contractRenewal.getContacterBName());
        vo.put("bPhone", contractRenewal.getContacterBPhone());
        vo.put("bAddress", contractRenewal.getContacterBAddress());
        vo.put("btD", startArray[2]);
        vo.put("btM", startArray[1]);
        vo.put("btY", startArray[0]);
        vo.put("creditCode", shopSupplier.getCreditCode());
        vo.put("discount", detail.getDealWay()==1?"":((100-Double.valueOf(detail.getPriceDiscount())*100)+""));
        vo.put("etD", endArray[2]);
        vo.put("etM", endArray[1]);
        vo.put("etY", endArray[0]);
        vo.put("wayA", detail.getDealWay()==1?"\u25A0":"\u25A1");
        vo.put("wayB", detail.getDealWay()==1?"\u25A1":"\u25A0");
        vo.put("aAddress", contractRenewal.getContacterAAddress());
        vo.put("supplierName", shopSupplier.getSupplierFullName());
        vo.put("supplierName2", shopSupplier.getSupplierFullName());
        vo.put("supplierName3", shopSupplier.getSupplierFullName());
        vo.put("settlementPeriod", shopSupplier.getSettlementPeriod()+"");
        vo.put("supplierAddress", shopSupplier.getRegisterAddress());
        vo.put("invoiceBankAndAccount", shopSupplier.getInvoiceBank()+"  "+shopSupplier.getInvoiceBankAccount());
        vo.put("contractCode", contractRenewal.getContractCode());
        vo.put("firstPerformance", detail.getFirstPerformanceBond());
        vo.put("bondBig", ConvertNumberToUppercase.toChineseUpper(Long.valueOf(firstPerformance)));

        return vo;
    }

    public void sendSignEmail(SystemContractRenewalEntity entity) {
        MySupplierVo shopSupplier = shopSupplierService.querySupplierInfo(entity.getSupplierId());
        String emailTemplate = "";
        String subject = "东风商城电子合同签署结果通知";
        if(entity.getSignStatus() == 2){
            emailTemplate = mailService.getContractSignPassTemplate(entity.getContractCode(),
                    entity.getContractName(),
                    entity.getCompanyName(),
                    entity.getSupplierName(), entity.getValidityStart()+" 至 "+entity.getValidityEnd());
        }else{
            emailTemplate = mailService.getContractSignFailTemplate(entity.getContractCode(),
                    entity.getContractName(),
                    entity.getCompanyName(),
                    entity.getSupplierName(),
                    entity.getSignDesc());
        }

        mailService.sendEmail(subject, emailTemplate,
                shopSupplier.getAdminEmail());

        emailSendLogService.createEmailSendLog(subject, emailTemplate, shopSupplier.getAdminEmail() , true);
    }

    public List<SystemContractRenewalEntity> getSyncContract() {
        return this.baseMapper.getSyncContract();
    }
}

