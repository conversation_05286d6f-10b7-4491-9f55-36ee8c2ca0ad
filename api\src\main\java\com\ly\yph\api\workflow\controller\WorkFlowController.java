package com.ly.yph.api.workflow.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.workflow.WorkflowContext;
import com.ly.yph.api.workflow.controller.vo.ApproveImportExcelVO;
import com.ly.yph.api.workflow.controller.vo.ApproveImportRespVO;
import com.ly.yph.api.workflow.controller.vo.WorkFlowExcelVO;
import com.ly.yph.api.workflow.dto.WorkflowAuditRecordReq;
import com.ly.yph.api.workflow.dto.WorkflowReq;
import com.ly.yph.api.workflow.dto.WorkflowStepReq;
import com.ly.yph.api.workflow.entity.Workflow;
import com.ly.yph.api.workflow.entity.WorkflowAuditRecord;
import com.ly.yph.api.workflow.entity.WorkflowStep;
import com.ly.yph.api.workflow.service.WorkFlowService;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.excel.util.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 流程管理。
 *
 * <AUTHOR>
 * @date 2023/09/15
 */
@Api(tags = "流程管理 - 流程管控")
@RestController
@RequestMapping("workflow")
@SaCheckLogin
public class WorkFlowController {
    @Resource
    private WorkFlowService workFlowService;

    /**
     * 查询所有在审核中的流程
     *
     * @param pageReq
     * @param queryDto
     * @return {@link ServiceResult}<{@link PageResp}<{@link WorkflowStep}>>
     */
    @GetMapping("list-step")
    @SaCheckPermission("business:workflowx:query")
    public ServiceResult<PageResp<WorkflowStep>> listStep(PageReq pageReq, WorkflowStepReq queryDto) {
        return ServiceResult.succ(workFlowService.selectPageWorkFlowStep(queryDto, pageReq));
    }

    /**
     * 审核的详情数据查询，一般用于判断是否审核通过。
     *
     * @param workFlowId
     * @return {@link ServiceResult}<{@link WorkflowContext}>
     */
    @GetMapping("step-detail")
    @SaCheckPermission("business:workflowx:query")
    public ServiceResult<WorkflowContext> listStep(Long workFlowId) {
        WorkflowContext contextDetail = workFlowService.getContextDetail(workFlowId);
        return ServiceResult.succ(contextDetail);
    }

    /**
     * 查询主流程，主要用于查询。
     *
     * @param pageReq
     * @param queryDto
     * @return {@link ServiceResult}<{@link PageResp}<{@link Workflow}>>
     */
    @ApiOperation("查询主流程")
    @GetMapping("list-flow")
    @SaCheckPermission("business:workflowx:query")
    public ServiceResult<PageResp<Workflow>> listWorkFlow(PageReq pageReq, WorkflowReq queryDto) {
        return ServiceResult.succ(workFlowService.selectPageWorkFlow(queryDto, pageReq));
    }

    @ApiOperation("商品审核流程导出")
    @GetMapping("/list-export")
    @SaCheckPermission("business:workflowx:query")
    public void listExport(HttpServletResponse response,@Validated WorkflowReq queryDto)throws IOException {
        List<WorkFlowExcelVO> list = workFlowService.selectListWorkFlow(queryDto);
        // 导出 Excel
        ExcelUtils.write(response, "商品审核流程信息" + DateUtils.format(new Date(), "yyyy-MM-dd") + ".xls", "数据", WorkFlowExcelVO.class, list);
    }

    /**
     * 流程日志，流程审核完成后立刻删除，并存为日志信息。
     *
     * @param pageReq
     * @param queryDto
     * @return {@link ServiceResult}<{@link PageResp}<{@link WorkflowAuditRecord}>>
     */
    @ApiOperation("查询日志")
    @GetMapping("list-flow-log")
    @SaCheckPermission("business:workflowx:query")
    public ServiceResult<PageResp<WorkflowAuditRecord>> listLog(PageReq pageReq, WorkflowAuditRecordReq queryDto) {
        return ServiceResult.succ(workFlowService.selectPageWorkflowAuditRecord(queryDto, pageReq));
    }

    /**
     * 流程日志，
     *
     * @param stepId
     * @return {@link ServiceResult}<{@link PageResp}<{@link WorkflowAuditRecord}>>
     */
    @ApiOperation("查询日志")
    @GetMapping("get-next-step")
    @DataPermission(enable = false)
    @SaCheckPermission("business:workflowx:query")
    public ServiceResult<List<SystemUsers>> getNextStep(Integer stepId) {
        return ServiceResult.succ(workFlowService.getNextStep(stepId));
    }

    /**
     * 驳回
     *
     * @param stepId
     * @param reason
     * @return {@link ServiceResult}<{@link Boolean}>
     */
    @ApiOperation("驳回")
    @PostMapping("reject")
    @SaCheckPermission("business:workflowx:reject")
    public ServiceResult<Boolean> reject(Integer stepId, String reason) {
        workFlowService.tenantIdReject(stepId, reason);
        return ServiceResult.succ(true);
    }

    /**
     * 撤销商品审批
     *
     * @param goodsCode
     * @param reason
     * @return {@link ServiceResult}<{@link Boolean}>
     */
    @ApiOperation("撤销商品审批")
    @PostMapping("revoke")
    @SaCheckPermission("business:workflowx:reject")
    public ServiceResult<Boolean> revoke(String goodsCode, String reason) {
        workFlowService.revoke(goodsCode, reason);
        return ServiceResult.succ(true);
    }

    /**
     * 通过
     *
     * @param stepId
     * @param reason
     * @return {@link ServiceResult}<{@link Boolean}>
     */
    @ApiOperation("通过")
    @PostMapping("pass")
    @SaCheckPermission("business:workflowx:pass")
        public ServiceResult<Boolean> pass(Integer stepId, String reason) {
        workFlowService.tenantIdApprove(stepId, reason);
        return ServiceResult.succ(true);
    }

    @GetMapping("/get-import-template")
    @ApiOperation("获得批量审核模板")
    @SaCheckPermission("business:workflowx:import")
    public void importTemplate(final HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        final List<ApproveImportExcelVO> list = Arrays.asList(ApproveImportExcelVO.builder()
                        .sku("SK0022").status(1).content("通过")  .build(),
                ApproveImportExcelVO.builder()
                        .sku("SK0023").status(2).content("数据有问题，驳回")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "批量审核模板.xls", "批量审核列表", ApproveImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @ApiOperation("导入批量审核")
    @ApiImplicitParams({@ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)})
    @SaCheckPermission("business:workflowx:import")
    public ServiceResult<ApproveImportRespVO> importExcel(@RequestParam("file") final MultipartFile file) throws Exception {
        final List<ApproveImportExcelVO> list = ExcelUtils.read(file, ApproveImportExcelVO.class);
        return ServiceResult.succ(workFlowService.importApprove(list));
    }

    @PostMapping("/deepSeekAutoApprove")
    @ApiOperation("AI自动审核")
    @SaIgnore
    public ServiceResult<?> deepSeekAutoApprove() {
        workFlowService.deepSeekAutoApprove();
        return ServiceResult.succ();
    }


}