package com.ly.yph.api.goods.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolShelvesDownQueryDto;
import com.ly.yph.api.goods.service.DfmallGoodsPoolShelvesDownLogService;
import com.ly.yph.api.goods.service.DfmallGoodsPoolShelvesDownService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.page.PageReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品池下架记录(DfmallGoodsPoolShelvesDown)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-19 14:57:33
 */
@RestController
@RequestMapping("goods/mallGoodsPoolShelvesDown")
@Validated
@Log4j2
@Api(tags = "商品池下架记录管理")
@SaCheckLogin
public class DfmallGoodsPoolShelvesDownController {

    @Resource
    private DfmallGoodsPoolShelvesDownLogService dfmallGoodsPoolShelvesDownLogService;

    /**
     * 分页查询
     *
     * @param pageReq 分页数据
     * @param goodsPoolQueryDto 查询入参
     * @return 分页数据
     */
    @GetMapping("/queryPage")
    @ApiOperation("商品池分页查询")
    @SaCheckPermission("business:goodsPool:query")
    public ServiceResult<?> queryPage(PageReq pageReq, DfmallGoodsPoolShelvesDownQueryDto goodsPoolQueryDto) {
        return ServiceResult.succ(this.dfmallGoodsPoolShelvesDownLogService.queryPage(pageReq,goodsPoolQueryDto));
    }

}

