<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.yph.api.settlement.common.mapper.SettleShopBillPostageDetailMapper">


    <select id="getSumPostage" resultType="java.util.Map">
        select
            sum(postage) as postage
        from settle_shop_bill_postage_detail
        where bill_id =#{billId} and is_enable =1
    </select>

    <select id="querySettleShopBillPostagePage" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillPostageVo">
        select
        bill_detail_postage_id,
        bill_sn,
        order_number,
        supplier_order_number,
        apply_user_name,
        apply_dept_name,
        postage,
        tax_rate,
        apply_time,
        audit_time,
        store_name,
        create_time,
        invoice_flag
        from
            settle_shop_bill_postage_detail
        where
        1=1
        <if test="query.billId != null">
            and bill_id =#{query.billId}
        </if>
        <if test="query.orderNumber != null and query.orderNumber !=''">
            and order_number =#{query.orderNumber}
        </if>
        <if test="query.billSn != null and query.billSn !=''">
            and bill_sn =#{query.billSn}
        </if>
        <if test="query.postageStagingFlag !=null">
            and postage_staging_flag =#{query.postageStagingFlag}
        </if>
        and is_enable =1
    </select>

    <select id="getPostageExcel" resultType="com.ly.yph.api.settlement.common.vo.bill.SettleShopBillPostageExcelVo">
        select
        (@i:=@i+1) as indexNum,
        order_number,
        supplier_order_number,
        apply_user_name,
        apply_dept_name,
        postage,
        tax_rate,
        apply_time,
        audit_time,
        store_name,
        invoice_flag
        from
            settle_shop_bill_postage_detail,
        (select @i:=0) as itable
        where
        is_enable = 1 and bill_id =#{query.billId}
        <if test="query.orderNumber != null and query.orderNumber != ''">
            and order_number = #{query.orderNumber}
        </if>
        <if test="query.supplierOrderNumber != null and query.supplierOrderNumber != ''">
            and supplier_order_number = #{query.supplierOrderNumber}
        </if>
        <if test="query.billStagingFlag !=null">
            and postage_staging_flag =#{query.billStagingFlag}
        </if>

    </select>

    <select id="getPostageByCustomerType"
            resultType="com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail">
        select a.bill_detail_postage_id,
               a.bill_id,
               a.postage,
               a.invoice_flag
        from settle_shop_bill_postage_detail a
                 left join settle_shop_bill b on a.bill_id = b.bill_id
        where a.is_enable = 1
          and b.is_enable = 1
          and b.customer_type = #{customerType}
          and a.order_number =#{orderNumber}
    </select>
</mapper>