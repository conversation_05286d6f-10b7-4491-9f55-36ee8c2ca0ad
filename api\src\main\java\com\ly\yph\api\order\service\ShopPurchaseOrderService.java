package com.ly.yph.api.order.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ly.yph.api.companycredit.enums.AuthAmountType;
import com.ly.yph.api.companycredit.service.AuthAmountInfoService;
import com.ly.yph.api.companystore.dto.CompanyStoreOrderAddressSaveDto;
import com.ly.yph.api.companystore.service.CompanyStoreAddressUserService;
import com.ly.yph.api.companystore.service.CompanyStoreOrderAddressService;
import com.ly.yph.api.companystore.vo.StoreShopAddressVo;
import com.ly.yph.api.config.CustomizeDfsProperty;
import com.ly.yph.api.customization.common.CompanyConstant;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.customization.common.Constant;
import com.ly.yph.api.customization.common.PurchaseBudgetTypeEnum;
import com.ly.yph.api.customization.dto.ExtDeliveryNumDto;
import com.ly.yph.api.customization.dto.VoyahStoreInfoDto;
import com.ly.yph.api.customization.entity.SubOrderMultipleBudget;
import com.ly.yph.api.customization.factory.ExtApprove;
import com.ly.yph.api.customization.factory.ExtApproveFactory;
import com.ly.yph.api.customization.factory.ExtDelivery;
import com.ly.yph.api.customization.factory.ExtDeliveryFactory;
import com.ly.yph.api.customization.service.*;
import com.ly.yph.api.goods.common.SupplierConstants;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.enums.GoodsModelEnum;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.goods.vo.GoodsMasterDetailVo;
import com.ly.yph.api.goods.vo.GoodsPoolsVo;
import com.ly.yph.api.goods.vo.GoodsSaleLabelVo;
import com.ly.yph.api.goods.vo.ShopGoodsSupplierVo;
import com.ly.yph.api.honda.service.HondaNpmsBudgetService;
import com.ly.yph.api.honda.service.HondaNpmsMaterialService;
import com.ly.yph.api.miniapp.miniappnotify.MiniAppNotifyService;
import com.ly.yph.api.openapi.context.OrganizationCodeContextHolder;
import com.ly.yph.api.openapi.dal.dataobject.openPay.OpenPayDO;
import com.ly.yph.api.openapi.dal.mysql.openPay.OpenPayMapper;
import com.ly.yph.api.openapi.service.VoyahPurchaseService;
import com.ly.yph.api.openapi.v1.dto.SrmUsePriceDto;
import com.ly.yph.api.openapi.v1.vo.mesage.MessageTypeEnum;
import com.ly.yph.api.openapi.v1.vo.order.ConfirmDeliveryReq;
import com.ly.yph.api.order.common.CancelOrderTimerTask;
import com.ly.yph.api.order.common.GoodsReturnStateEnum;
import com.ly.yph.api.order.common.OrderNumberGenerator;
import com.ly.yph.api.order.common.PurchaseLabelHonda;
import com.ly.yph.api.order.config.DfsYamlConfig;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.convert.DeliveryConvert;
import com.ly.yph.api.order.convert.DeliveryDetailConvert;
import com.ly.yph.api.order.convert.PurchaseCustomLyConvert;
import com.ly.yph.api.order.dto.*;
import com.ly.yph.api.order.dto.srm.PurchaseCustomLySaveDto;
import com.ly.yph.api.order.dto.youServiceOrder.YouServiceOrderPageQueryDto;
import com.ly.yph.api.order.entity.*;
import com.ly.yph.api.order.enums.*;
import com.ly.yph.api.order.enums.orderconfirmcondition.PreOrderConfirmTypeEnum;
import com.ly.yph.api.order.exception.GoodsCheckException;
import com.ly.yph.api.order.exception.OrderException;
import com.ly.yph.api.order.mapper.ShopDeliveryDetailMapper;
import com.ly.yph.api.order.mapper.ShopDeliveryMapper;
import com.ly.yph.api.order.mapper.ShopOrderDetailFloatMapper;
import com.ly.yph.api.order.mapper.ShopPurchaseOrderMapper;
import com.ly.yph.api.order.vo.*;
import com.ly.yph.api.order.vo.youServiceOrder.YouServiceOrderPageVo;
import com.ly.yph.api.orderlifecycle.dto.UpdatePurchaseOrderInfoForConfirmDto;
import com.ly.yph.api.orderlifecycle.enums.OrderSalesChannelEnum;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleFactory;
import com.ly.yph.api.orderlifecycle.factory.OrderLifeCycleStrategy;
import com.ly.yph.api.organization.controller.organization.vo.organization.OrganizationSimpleRespVO;
import com.ly.yph.api.organization.controller.organization.vo.purchase.budget.OrderChangeBudgetAmountVO;
import com.ly.yph.api.organization.entity.*;
import com.ly.yph.api.organization.service.*;
import com.ly.yph.api.pay.dal.dataobject.order.PayOrderDO;
import com.ly.yph.api.pay.enums.order.PayOrderStatusEnum;
import com.ly.yph.api.pay.enums.order.WXPayOrderTradeStatusEnum;
import com.ly.yph.api.pay.service.order.PayOrderService;
import com.ly.yph.api.pay.service.order.dto.PayOrderCreateReqDTO;
import com.ly.yph.api.price.monitor.service.PriceMonitorService;
import com.ly.yph.api.product.ext.common.dto.request.*;
import com.ly.yph.api.product.ext.common.dto.response.*;
import com.ly.yph.api.product.ext.common.manage.BatchPriceGetter;
import com.ly.yph.api.product.ext.common.manage.RemoteInfoManage;
import com.ly.yph.api.product.ext.jd.config.JDConfig;
import com.ly.yph.api.product.ext.jd.dto.reponse.SupportedInfoOpenResp;
import com.ly.yph.api.product.ext.jd.dto.request.ReturnOrderReq;
import com.ly.yph.api.product.ext.jd.integral.config.JDIntegralConfig;
import com.ly.yph.api.settlement.common.entity.SettleCheckForm;
import com.ly.yph.api.settlement.common.service.SettleBillPoolService;
import com.ly.yph.api.settlement.common.service.SettleBillPoolYflCustomerService;
import com.ly.yph.api.settlement.common.service.SettleCheckService;
import com.ly.yph.api.southchina.dhec.config.DhecSmartWarehouseConfig;
import com.ly.yph.api.supplier.dto.SupplierSaveDeliveryDto;
import com.ly.yph.api.supplier.dto.SupplierSaveDeliveryGoodsDto;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.SupplierContractRealEntity;
import com.ly.yph.api.supplier.entity.SystemContractPermission;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.supplier.service.SupplierContractRealService;
import com.ly.yph.api.supplier.service.SupplierOrderService;
import com.ly.yph.api.supplier.service.SystemContractPermissionService;
import com.ly.yph.api.supplier.vo.SupplierOrderDeliveryGoodsVo;
import com.ly.yph.api.supplier.vo.SupplierOrderDeliveryVo;
import com.ly.yph.api.system.config.ShlianluConfig;
import com.ly.yph.api.system.dto.StartInstanceDto;
import com.ly.yph.api.system.entity.InvoiceSubject;
import com.ly.yph.api.system.entity.RequirementRecord;
import com.ly.yph.api.system.entity.ShopAddress;
import com.ly.yph.api.system.entity.ShopRequirement;
import com.ly.yph.api.system.feign.ActTaskFeign;
import com.ly.yph.api.system.service.*;
import com.ly.yph.api.utils.FormulaUtil;
import com.ly.yph.api.utils.OpenApiMessageUtil;
import com.ly.yph.api.utils.dto.FormulaPriceDto;
import com.ly.yph.api.utils.dto.PriceUtilDto;
import com.ly.yph.api.virtualgoods.entity.ShopVirtualPurchaseExtended;
import com.ly.yph.api.virtualgoods.service.ShopVirtualPurchaseExtendedService;
import com.ly.yph.api.zone.entity.GoodsZoneEntity;
import com.ly.yph.api.zone.service.GoodsZoneService;
import com.ly.yph.core.base.CommonCodeGeneral;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.exception.types.DataRequestException;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.invokelog.InvokeLogRecord;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.mapper.SystemDictDataMapper;
import com.ly.yph.core.dic.core.service.DictDataService;
import com.ly.yph.core.email.MailService;
import com.ly.yph.core.payment.core.client.dto.PayOrderInfoDTO;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.core.util.CollectionUtils;
import com.ly.yph.core.util.StringHelper;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.web.ServletUtils.getClientIP;
import static com.ly.yph.core.util.JsonUtils.toJsonString;

/**
 * (ShopPurchaseOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-13 15:44:01
 */
@Service
@Log4j2
@RefreshScope
public class ShopPurchaseOrderService extends ServiceImpl<ShopPurchaseOrderMapper, ShopPurchaseOrder> {
    private static final String ORDER_CONFIRM = "front-sms-order-confirm";
    @Value("${customize.dfs.custom-ly}")
    private String lyCompanyCodes;
    @Value("${customize.dfs.invoiceId-ly}")
    private Integer invoiceId;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private ShopPurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private ShopOrderAddressService shopOrderAddressService;
    @Resource
    private InvoiceSubjectService invoiceSubjectService;
    @Resource
    private ShopOrderInvoiceService shopOrderInvoiceService;
    @Resource
    private ShopPurchaseSubOrderService purchaseSubOrderService;
    @Resource
    private ShopPurchaseSubOrderDetailService purchaseSubOrderDetailService;
    @Resource
    private RemoteInfoManage remoteInfoManage;
    @Resource
    private ShopGoodsService shopGoodsService;
    @Resource
    private ShopCartService shopCartService;
    @Resource
    private ActTaskFeign actTaskFeign;
    @Resource
    private BatchPriceGetter priceGetter;
    @Resource
    private SystemBudgetService budgetService;
    @Value("${supplier.orderValidDay}")
    private Integer orderValidDay;
    @Resource
    private YflYamlConfig yflYamlConfig;
    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;
    @Resource
    private ShopReturnService returnSrv;
    @Resource
    private ShopReturnDetailService returnDetailSrv;
    @Resource
    private RequirementRecordService requirementRecordService;
    @Resource
    private ShopRequirementService shopRequirementService;
    @Resource
    private OrderNumberGenerator orderNumberGenerator;
    @Resource
    private ShopAddressService shopAddressService;
    @Resource
    private SystemUsersService systemUsersService;
    @Resource
    private ShopDeliveryMapper shopDeliveryMapper;
    @Resource
    private ShopDeliveryDetailMapper shopDeliveryDetailMapper;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private ShopGoodsStockService shopGoodsStockService;
    @Resource
    private ShopGoodsDetailService shopGoodsDetailService;
    @Resource
    private ExtApproveFactory extApproveFactory;
    @Resource
    private ExtInventoryService extInventoryService;
    @Resource
    private SystemOrganizationService systemOrganizationService;
    @Resource
    private ExternalInterfaceService externalInterfaceService;
    @Resource
    private ActNoticeService noticeSrv;
    @Resource
    private JDConfig jdconfig;
    @Resource
    private JDIntegralConfig jdIntegralConfig;
    @Resource
    private SystemSmsSendService smsSrv;
    @Resource
    public ShopBrandService shopBrandService;
    @Resource
    private SystemIntegralService systemIntegralService;
    @Resource
    private PayOrderService payOrderService;
    @Resource
    private SystemActivityService systemActivityService;
    @Resource
    private ShopMaterialRelationService materialRelationService;
    @Resource
    private MiniAppNotifyService miniAppNotifyService;
    @Resource
    private ShopDeliveryService deliverySrv;
    @Resource
    private ShopDeliveryDetailService deliveryDetailSrv;
    @Resource
    private ExtDeliveryFactory extDeliveryFactory;
    @Resource
    private DfgSapService dfgSapService;
    @Resource
    private ShopSrmContractService shopSrmContractService;
    @Resource
    private ShopOrderDetailFloatMapper shopOrderDetailFloatMapper;
    @Resource
    private SystemOrganizationPurchaseContractService companyService;
    @Resource
    private OpenApiMessageUtil openApiMessageUtil;
    @Resource
    private DfsYamlConfig dfsYamlConfig;
    @Resource
    private DfsDoaPayService dfsDoaPayService;
    @Resource
    private MailService mailService;
    @Resource
    private EmailSendLogService emailSendLogService;
    @Resource
    private ShopSrmContractDetailService shopSrmContractDetailService;
    @Resource
    private FormulaUtil formulaUtil;
    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;
    @Resource
    private CustomizeDfsProperty customizeDfsProperty;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    @Resource
    private DfgOaTodoService dfgOaTodoService;
    @Resource
    private DfgDeliveryService dfgDeliveryService;
    @Resource
    private DfmallGoodsPoolSubService dfmallGoodsPoolSubService;
    @Resource
    private AuthAmountInfoService authAmountInfoService;
    @Resource
    private DhecService dhecService;
    @Resource
    private DictDataService dictDataSrv;
    @Resource
    private SrmMaterialInfoService srmMaterialInfoSrv;
    @Resource
    private SettleCheckService settleCheckService;
    @Resource
    private PurchaseCustomLyMapper purchaseCustomLyMapper;
    @Resource
    private SettleBillPoolService settleBillPoolService;
    @Resource
    private SettleBillPoolYflCustomerService settleBillPoolYflCustomerService;

    @Resource
    private IlinkPushMsgService ilinkSrv;
    @Value("${order.splitLimit:#{150}}")
    private BigDecimal orderSplitLimit;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ShopPurchaseAttachmentService shopPurchaseAttachmentService;
    @Resource
    private ShopPurchaseHondaInvestService shopPurchaseHondaInvestService;
    @Resource
    private HondaNpmsBudgetService hondaNpmsBudgetService;

    @Resource
    private HondaNpmsMaterialService hondaNpmsMaterialService;

    @Resource
    private OrderLifeCycleFactory orderLifeCycleFactory;

    @Resource
    private ShopPurchaseSubOrderDetailCompareRecordService shopPurchaseSubOrderDetailCompareRecordService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CommonCodeGeneral commonCodeGeneral;
    @Resource
    private SrmMaterialInfoService srmMaterialInfoService;
    @Resource
    private CompanyStoreAddressUserService companyStoreAddressUserService;

    @Resource
    private ShopGoodsContractService shopGoodsContractService;
    @Resource
    private CompanyStoreOrderAddressService companyStoreOrderAddressService;
    @Resource
    private SystemDictDataMapper systemDictDataMapper;
    @Resource
    private PriceMonitorService priceMonitorSrv;

    @Resource
    private ShopVirtualPurchaseExtendedService shopVirtualPurchaseExtendedService;
    @Resource
    private SystemContractPermissionService contractPermissionService;
    @Resource
    private SupplierContractRealService supplierContractRealService;

    @Resource
    private VoyahPurchaseService voyahPurchaseService;
    @Resource
    private SubOrderMultipleBudgetService multipleBudgetService;

    @Resource
    private GoodsZoneService goodsZoneService;

    @Resource
    private AfterSalesRulesService afterSalesRulesService;
    @Resource
    private OpenPayMapper openPayMapper;
    @Resource
    private SystemSmsSendService smsSendService;
    @Resource
    private ShlianluConfig shlianluCfg;
    @Resource
    private DhecSmartWarehouseConfig dhecSmartWarehouseConfig;
    @Resource
    private SupplierOrderService supplierOrderService;

    /**
     * 计算订单明细金额
     *
     * @param pricingMode         计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式
     * @param orderDetail         订单明细
     * @param remotePriceInfoResp 商品实时价格
     * @return {@link ShopPurchaseSubOrderDetail}
     */
    public ShopPurchaseSubOrderDetail computeOrderDetail(Integer pricingMode, ShopPurchaseSubOrderDetail orderDetail, RemotePriceInfoResp remotePriceInfoResp) {
        // 电商未税单价
        BigDecimal supplierUnitPriceNaked = remotePriceInfoResp.getGoodsPactNakedPrice();
        // 电商含税单价
        BigDecimal supplierUnitPriceTax = remotePriceInfoResp.getGoodsPactPrice();
        // 商品未税单价
        BigDecimal goodsUnitPriceNaked = remotePriceInfoResp.getGoodsNakedSalePrice();
        // 商品含税单价
        BigDecimal goodsUnitPriceTax = remotePriceInfoResp.getGoodsSalePrice();
        // 税率
        Integer taxRate = orderDetail.getTaxRate();

        PriceUtilDto priceUtilDto = formulaUtil.pricingModeUtil(supplierUnitPriceNaked, supplierUnitPriceTax,
                goodsUnitPriceNaked, goodsUnitPriceTax, taxRate, orderDetail.getApplyNum().intValue(),
                orderDetail.getApplyNumDecimal(), pricingMode);

        FormulaPriceDto supplierPrice = priceUtilDto.getSupplierPrice();
        FormulaPriceDto goodsPrice = priceUtilDto.getGoodsPrice();

        // 协议价
        orderDetail.setSupplierUnitPriceNaked(supplierUnitPriceNaked);
        orderDetail.setSupplierUnitPriceTax(supplierUnitPriceTax);
        orderDetail.setSupplierTotalPriceNaked(supplierPrice.getTotalPriceNaked());
        orderDetail.setSupplierTotalPriceTax(supplierPrice.getTotalPriceTax());
        // 销售价
        orderDetail.setGoodsUnitPriceNaked(goodsPrice.getPriceNaked());
        orderDetail.setGoodsUnitPriceTax(goodsPrice.getPriceTax());
        orderDetail.setGoodsTotalPriceNaked(goodsPrice.getTotalPriceNaked());
        orderDetail.setGoodsTotalPriceTax(goodsPrice.getTotalPriceTax());
        orderDetail.setGoodsTotalTax(goodsPrice.getTotalTax());

        return orderDetail;
    }

    /**
     * @param getWay
     * @return
     */
    public String startPurchaseOrderWorkflow(final JSONObject getWay) {
        final LoginUser user = LocalUserHolder.get();
        final Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyCode", user.getEntityOrganizationCode());
        paramMap.put("procType", "A00501");
        final Long deployId = this.purchaseOrderMapper.queryDeployByCompanyCode(paramMap);
        String businessKey = "";
        if (null != deployId) {
            final StartInstanceDto startInstanceDto = new StartInstanceDto();
            startInstanceDto.setApplicant(user.getUsername());
            startInstanceDto.setApplicantName(user.getNickname());

            String purchaseNumber = getWay.getString("purchaseNumber");
            if (StringUtils.isBlank(purchaseNumber)) {
                purchaseNumber = this.orderNumberGenerator.make();
                if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                    purchaseNumber += "Y";
                } else {
                    purchaseNumber += "D";
                }
            }
            startInstanceDto.setBusinessKey(purchaseNumber);
            startInstanceDto.setCompanyCode(user.getEntityOrganizationCode());
            startInstanceDto.setProcType("A00501");
            startInstanceDto.setVariables(getWay);
            startInstanceDto.setTenantId(user.getTenantId());

            final ServiceResult<String> serviceResult = this.actTaskFeign.startInstance(startInstanceDto);
            if (serviceResult.getCode() != 0) {
                throw new OrderException("启动审批失败");
            }
            businessKey = serviceResult.getData();
        }
        return businessKey;
    }

    /**
     * 采购申请单保存
     *
     * @param param
     * @return
     */
    // 锁表太多，锁时太长，一致性通过其他方式保证。
    @Transactional(rollbackFor = Exception.class)
    public String savePurchaseOrder(final PurchaseOrderSaveDto param) {
        log.info("开始保存采购订单：" + param.getPurchaseNumber());

        var user = LocalUserHolder.get();
        var purchaseOrder = _initPurchaseOrder(param);
        _checkApplyUser(purchaseOrder);
        _checkBudgetCode(param);
        _checkCompanyStore(purchaseOrder,param);

        //判断是否走预算
        var budget = _getBudget(param);
        var orderGroupList = getOrderGroupList(param, purchaseOrder);
        var addressInfo = saveAddress(purchaseOrder, param);

        _checkDfpvBudgetCode(addressInfo,param);

        // 开票信息保存
        String invoiceSubject = saveInvoiceInfo(purchaseOrder);
        var regionDto = _initRegion(addressInfo);

        //企配仓存储用
        List<CompanyStoreOrderAddressSaveDto> companyStoreOrderAddressSaveList = new ArrayList<>();
        //子订单
        List<ShopPurchaseSubOrder> subOrderList = new ArrayList<>();
        Map<String, Object> cnt = param.getPurchaseOrderDetailList().stream().collect(Collectors.toMap(PurchaseOrderDetailSaveDto::getGoodsCode, PurchaseOrderDetailSaveDto::getApplyNum));
        var mrList = materialRelationService.getBaseMapper().selectSimpleList(cnt.keySet(), user.getEntityOrganizationCode());
        Map<String, List<ShopMaterialRelationEntity>> mrMap = mrList.stream().collect(Collectors.groupingBy(ShopMaterialRelationEntity::getGoodsCode));

        //商品合同校验
        var contractCheck =  this.goodsContractCheck(cnt.keySet());
        if (!contractCheck.isEmpty()) {
            throw new GoodsCheckException(JSON.toJSONString(contractCheck));
        }

        //校验商品
        Map<String, RemotePriceInfoResp> priceInfoRespMap = new HashMap<>();
        var checkResult = this.goodsCheck(cnt, regionDto, addressInfo.shopAddress.getAddress(), priceInfoRespMap::putAll);
        if (!checkResult.isEmpty()) {
            throw new GoodsCheckException(JSON.toJSONString(checkResult));
        }

        // 校验浮动价格
        Map<String, ShopOrderDetailFloat> floatPriceGoodsMap = checkFloatPriceOrderGoods(param.getFloatPriceGoodsList(), priceInfoRespMap);

        List<GoodsMasterDetailVo> goodsViewList = this.shopGoodsService.queryGoodsMasterDetailByCodes(cnt.keySet());
        Map<String, GoodsMasterDetailVo> goodsViewMap = goodsViewList.stream().collect(Collectors.toMap(GoodsMasterDetailVo::getGoodsCode, Function.identity()));
        List<ShopSupplier> supplierList = shopSupplierService.getBaseMapper().selectSimpleList(param.getPurchaseOrderDetailList().stream().map(PurchaseOrderDetailSaveDto::getSupplierCode).collect(Collectors.toList()));
        Map<String, ShopSupplier> supplierMap = supplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity()));

        Map<String, Integer> saleCntUpdate = new HashMap<>();

        Integer rowNumber = 1;
        var purchaseGoodsNumber = new BigDecimal(0);
        var purchaseGoodsPrice = new BigDecimal(0);
        var purchaseFreightPrice = new BigDecimal(0);
        var purchaseSubsidyFreight = new BigDecimal(0);
        var purchaseGoodsPriceNaked = new BigDecimal(0);
        for (List<PurchaseOrderDetailSaveDto> purchaseOrderDetailList : orderGroupList) {
            var subOrder = new ShopPurchaseSubOrder();
            subOrder.setOrderModel(purchaseOrderDetailList.get(0).getGoodsModel());
            String contractNumber = purchaseOrderDetailList.get(0).getContractNumber();
            subOrder.setContractNumber(contractNumber);
            Integer settlementType = purchaseOrderDetailList.get(0).getSettlementType();
            Integer isPlatformReconciliation = (settlementType == null || settlementType == 1) ? 1 : 2;
            subOrder.setIsPlatformReconciliation(isPlatformReconciliation);
            // 子订单合计字段 每供应商一个子订单
            var orderPriceTax = new BigDecimal(0);
            var orderPriceNaked = new BigDecimal(0);
            var supplierOrderPriceTax = new BigDecimal(0);
            var supplierOrderPriceNaked = new BigDecimal(0);
            //运费查询入参
            final List<GoodCodeInfoListParams> goodCodeInfoParamsList = new ArrayList<>();
            // 订单已按照商品类型拆单,所以商品类型就是订单类型
            var dataSource = supplierMap.get(purchaseOrderDetailList.get(0).getSupplierCode()).getDataSource();
            var srmContract = getSrmContract(subOrder, dataSource);

            //订单商品明细
            List<ShopPurchaseSubOrderDetail> orderDetailList = new ArrayList<>();
            for (var orderDetailSaveDto : purchaseOrderDetailList) {
                var subOrderDetail = initSubOrderDetail(orderDetailSaveDto, subOrder);

                GoodsMasterDetailVo goodsMasterDetailVo = goodsViewMap.get(orderDetailSaveDto.getGoodsCode());
                subOrderDetail = _orderDetailFill(orderDetailSaveDto, goodsMasterDetailVo, subOrderDetail, rowNumber, priceInfoRespMap, subOrder);
                // 插一脚，处理浮动价格
                handleFloatPriceOrderGoods(subOrder.getPricingMode(), subOrderDetail, floatPriceGoodsMap);
                supplierOrderPriceTax = supplierOrderPriceTax.add(subOrderDetail.getSupplierTotalPriceTax());
                supplierOrderPriceNaked = supplierOrderPriceNaked.add(subOrderDetail.getSupplierTotalPriceNaked());

                subOrder.setSupplierCode(goodsMasterDetailVo.getSupplierCode());
                subOrder.setSupplierName(goodsMasterDetailVo.getSupplierName());
                subOrder.setSupplierDataSource(supplierMap.get(goodsMasterDetailVo.getSupplierCode()).getDataSource());

                //外币处理
                _srmPriceProcess(srmContract, subOrderDetail);
                goodCodeInfoParamsList.add(new GoodCodeInfoListParams(subOrderDetail.getApplyNum().intValue(), subOrderDetail.getGoodsCode()));

                rowNumber++;
                purchaseGoodsNumber = purchaseGoodsNumber.add(new BigDecimal(subOrderDetail.getApplyNum())).add(subOrderDetail.getApplyNumDecimal());
                saleCntUpdate.put(subOrderDetail.getGoodsCode(), subOrderDetail.getApplyNum().intValue());
                orderPriceTax = orderPriceTax.add(subOrderDetail.getGoodsTotalPriceTax());
                orderPriceNaked = orderPriceNaked.add(subOrderDetail.getGoodsTotalPriceNaked());
                orderDetailList.add(subOrderDetail);
            }
            purchaseGoodsPrice = purchaseGoodsPrice.add(orderPriceTax);
            purchaseGoodsPriceNaked = purchaseGoodsPriceNaked.add(orderPriceNaked);
            if (addressInfo.orderAddress.getIsCompanyStore() == 1){
                CompanyStoreOrderAddressSaveDto companyStoreOrderAddressSaveDto = param.getCompanyStoreOrderAddressSaveDto();
                //initSubOrderDetail 这个方法里维护了订单号
                if (CollectionUtil.isEmpty(companyStoreOrderAddressSaveDto.getFilterSupplierList()) || !companyStoreOrderAddressSaveDto.getFilterSupplierList().contains(subOrder.getSupplierCode())) {
                    //企配仓插一脚 维护企配状态
                    subOrder.setIsCompanyStore(addressInfo.orderAddress.getIsCompanyStore());
                    // 创建一个新的 CompanyStoreOrderAddressSaveDto 的副本
                    CompanyStoreOrderAddressSaveDto saveDto = new CompanyStoreOrderAddressSaveDto();
                    BeanUtil.copyProperties(param.getCompanyStoreOrderAddressSaveDto(), saveDto); // 复制属性，避免直接引用原对象
                    saveDto.setOrderNumber(subOrder.getOrderNumber());
                    companyStoreOrderAddressSaveList.add(saveDto);
                }
            }
            this.zkhOrderCustomization(subOrder, purchaseOrder.getCompanyName());
            subOrder.setSubOrderDetailList(orderDetailList);
            _fillSubOrderData(param, purchaseOrderDetailList, subOrder, purchaseOrder, orderPriceTax, orderPriceNaked, supplierOrderPriceTax, supplierOrderPriceNaked);

            //获取运费
            DeliveryPriceResp deliveryPriceResp = _checkDelivery(goodCodeInfoParamsList, regionDto, addressInfo);

            ShopSupplier shopSupplier = supplierMap.get(subOrder.getSupplierCode());
            if (orderPriceTax.compareTo(shopSupplier.getFreeShippingCondition()) >= 0
                    && deliveryPriceResp.getTotalPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 用户下单金额满足包邮条件，并且邮费大于 0，需要平台补贴邮费
                subOrder.setOrderSubsidyFreight(deliveryPriceResp.getTotalPrice());
                purchaseSubsidyFreight = purchaseSubsidyFreight.add(subOrder.getOrderSubsidyFreight());
                log.info("补贴邮费：{},供应商：{}", subOrder.getOrderSubsidyFreight(), subOrder.getSupplierName());
            } else {
                //未满足电商订单金额包邮条件，需要用户支付邮费
                subOrder.setOrderFreightPrice(deliveryPriceResp.getTotalPrice());
                purchaseFreightPrice = purchaseFreightPrice.add(subOrder.getOrderFreightPrice());
            }
            //维护订单销售客户端
            keepOrderSaleClient(subOrder,user.getEntityOrganizationCode(),user.getTenantId(),invoiceSubject);

            subOrderList.add(subOrder);
        }
        //采购单信息
        log.info("邮费总金额：{}，平台补贴邮费总金额：{}", purchaseFreightPrice, purchaseSubsidyFreight);
        _fillPurchaseOrderInfo(purchaseOrder, purchaseGoodsPrice, purchaseFreightPrice, purchaseSubsidyFreight, purchaseGoodsNumber, purchaseGoodsPriceNaked);

        //友福利租户提交订单
        List<ShopPurchaseSubOrderDetail> orderDetailList = new ArrayList<>();
        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            //获取企业
            Assert.notNull(param.getActivityId(), () -> new ParameterException("未找到您参与的活动"));
            log.info("user:{}",user);
            Assert.isFalse(user.getAcceptDeliveryFee() == 0 && purchaseFreightPrice.compareTo(BigDecimal.ZERO) > 0, () -> new ParameterException("企业配置不允许产生邮费，请继续添加商品直至满足包邮金额"));
            SystemIntegral systemIntegral = systemIntegralService.getByActivityIdUserId(param.getActivityId(), user.getId());
            Assert.notNull(systemIntegral, () -> new ParameterException("未找到您参与的活动"));
            Assert.isFalse(param.getFreightType() == 0 && systemIntegral.getIntegralUsableAmount().compareTo(purchaseOrder.getPurchaseFreightPrice()) < 0, () -> new ParameterException("您的积分余额不足以支付邮费"));
            //是否是纯现金
            Boolean tag = false;
            List<String> goodsCodeList = param.getPurchaseOrderDetailList().stream().map(PurchaseOrderDetailSaveDto::getGoodsCode).collect(Collectors.toList());
            Map<String, BigDecimal> poolMap = new HashMap<>();
            if (systemIntegral.getIntegralUsableAmount().compareTo(BigDecimal.ZERO) == 0 ) {
                //纯现金支付
                //值列表配置 不能走纯现金 所有活动都能纯现金 去掉校验
//                List<SystemDictDataEntity> dataEntities = systemDictDataMapper.selectListByDictType("yfl_psup_out_org");
//                Assert.isFalse(CollectionUtil.isNotEmpty(dataEntities) && dataEntities.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList()).contains(user.getEntityOrganizationCode())
//                        , () -> new ParameterException("您的企业不能现金支付,请联系客服!"));

                for (String goodsCode : goodsCodeList) {
                    poolMap.put(goodsCode, BigDecimal.ZERO);
                }
                tag = true;
            }

            if (purchaseOrder.getPurchaseSubsidyFreight().compareTo(BigDecimal.ZERO) > 0) {
                systemIntegralService.updateIntegral(systemIntegral.getId(),
                        purchaseOrder.getPurchaseSubsidyFreight(),
                        0,
                        purchaseOrder.getPurchaseNumber(),
                        1,
                        "包邮反补");
            }

            if (tag || purchaseOrder.getPurchaseTotalPrice().compareTo(systemIntegral.getIntegralUsableAmount()) > 0) {
                SystemActivity systemActivity = systemActivityService.getById(systemIntegral.getIntegralActivityId());
                Assert.isFalse(systemActivity.getIsPay() == 0, () -> new ParameterException("此活动不能现金支付,请联系客服!"));
            }
            // 南菱定制化+ 联友等纯现金逻辑
            if (yflYamlConfig.getCustomizedCompany().contains(user.getEntityOrganizationId()) || tag) {
                //获取明细,计算积分, 现金额度
                if (!tag) {
                    List<GoodsPoolsVo> goodsPoolsVolist = dfmallGoodsPoolSubService.queryGoodsIntegralCeilingByCode(goodsCodeList);
                    poolMap = goodsPoolsVolist.stream().collect(Collectors.toMap(GoodsPoolsVo::getGoodsCode, GoodsPoolsVo::getIntegralCeiling));
                }
                yflCustomizationOrder(purchaseOrder, subOrderList, orderDetailList, param, poolMap, tag);
                Assert.isFalse(!tag && systemIntegral.getIntegralUsableAmount().compareTo(BigDecimal.ZERO) == 0, () -> new ParameterException("您的积分余额不足"));
            } else {
                // 原有纯积分,混合支付逻辑
                yflOrder(purchaseOrder, systemIntegral, subOrderList, orderDetailList, param.getFreightType());
            }
            // 扣除用户积分
            systemIntegralService.updateIntegral(systemIntegral.getId(),
                    purchaseOrder.getPurchaseSubsidyFreight()
                            .add(purchaseOrder.getPurchasePayIntegral())
                            .add(param.getFreightType() == 0 ? purchaseOrder.getPurchaseFreightPrice() : BigDecimal.ZERO),
                    2,
                    purchaseOrder.getPurchaseNumber(),
                    0,
                    "");
            //虚拟商品要保存一下虚拟采购单的扩展表
            shopPurchaseOrderService.saveVirtualPurchaseExt(subOrderList,param.getRechargeAccount(),orderDetailList);

        } else {
            for (ShopPurchaseSubOrder purchaseSubOrder : subOrderList) {
                //保存订单商品明细
                orderDetailList.addAll(purchaseSubOrder.getSubOrderDetailList());
            }
        }

        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            purchaseOrder.setOrderSalesChannel(2);
        }

        purchaseSubOrderDetailService.saveOrUpdateBatch(orderDetailList);
        //保存供应商子订单
        purchaseSubOrderService.saveOrUpdateBatch(subOrderList);
        //保存采购单
        this.saveOrUpdate(purchaseOrder);
        //保存企配仓订单信息
        if (CollectionUtil.isNotEmpty(companyStoreOrderAddressSaveList)){
            companyStoreOrderAddressService.saveBatch(companyStoreOrderAddressSaveList);
        }
        //保存浮动价格变化
        saveOrderDetailFloat(orderDetailList, floatPriceGoodsMap);
        //保存联友定制下单信息
        savePurchaseCustomLy(purchaseOrder, param.getCustomLySaveDto(), param.getSrmAutoCreateOrder());

        // 扣除预算
        if (ObjectUtil.isNotNull(param.getBudgetId())) {
            final OrderChangeBudgetAmountVO changeBudgetAmountVO = OrderChangeBudgetAmountVO.builder()
                    .changeAmount(purchaseOrder.getPurchaseTotalPrice())
                    .userId(Long.valueOf(purchaseOrder.getApplyUserId()))
                    .budgetId(param.getBudgetId())
                    .purchaseNumber(purchaseOrder.getPurchaseNumber())
                    .organizationId(user.getOrganizationId())
                    .username(purchaseOrder.getApplyUserName())
                    .applyDeptName(purchaseOrder.getApplyDeptName())
                    .build();
            try {
                this.budgetService.deductAmounts(changeBudgetAmountVO);
            } catch (final Exception ex) {
                log.error(ex.toString());
                throw new OrderException(ex.getMessage());
            }
        }

        //查询审批流程
        final Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyCode", user.getEntityOrganizationCode());
        paramMap.put("procType", "A00501");
        final Long deployId = this.purchaseOrderMapper.queryDeployByCompanyCode(paramMap);

        // 非友福利订单
        if (!TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            //校验并扣减授信余额，保证前面的预算，最后再扣减预算，它们有事物可以异常回滚，授信是调外部服务的
            authAmountInfoService.authAmountByPreOrder(purchaseOrder, subOrderList);
        }

        /**
         * 调用dfgsap接口创建物料
         * 1、走预算
         * 2、存货类
         * 3、岚图的DMS固定预算is_stock=0，is_invest=0,push_state=0,在此处不进入创建的查询，
         * 岚图DMS的创建物料是线下导入匹配，source_sys_id=dms,后期如果此处有改动，请注意
         */
        if (!Objects.isNull(budget) && budget.getIsStock() == 1) {
            log.info("submit order goods creata mara purchaseId：" + purchaseOrder.getPurchaseNumber());
            //不入库为费用类订单，不强制要求创建物料
            dfgSapService.createSapMara(orderDetailList, addressInfo.orderAddress);
        }

        // 向供应商下预订单
        this.submitSupplierPreOrder(purchaseOrder.getPurchaseNumber(), deployId, param);


        //绑定订单和需求归集关系
        if (!StringHelper.IsEmptyOrNull(param.getCartIds())) {
            this.saveRequirementRelation(param.getCartIds(), subOrderList);
        }
        //删除购物车商品
        if (!StringHelper.IsEmptyOrNull(param.getCartIds())) {
            final List<Long> cartIds = Arrays.asList(param.getCartIds().split(",")).stream().map(cid -> Long.parseLong(cid)).collect(Collectors.toList());
            this.shopCartService.delCartIds(cartIds);
        }

        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            CancelOrderTimerTask cancelOrderTimerTask = new CancelOrderTimerTask(purchaseOrder.getPurchaseNumber());
            Timer timer = new Timer();
            // x分钟后执行
            timer.schedule(cancelOrderTimerTask, 1000 * yflYamlConfig.getOrderTimeOut());
        }


        List<GoodsCountDto> goodsCountDto = new ArrayList<>();
        saleCntUpdate.forEach((k, v) -> {
            GoodsCountDto gtc = new GoodsCountDto();
            gtc.setGoodsCode(k);
            gtc.setGoodsCnt(v);
            goodsCountDto.add(gtc);
        });
        if (CollectionUtil.isNotEmpty(goodsCountDto)) {
            this.shopGoodsDetailService.getBaseMapper().updateGoodsSaleNums(goodsCountDto);
        }
        this.shopGoodsDetailService.getBaseMapper().updateGoodsSaleNums(goodsCountDto);
        return purchaseOrder.getPurchaseNumber();
    }


    private void _checkCompanyStore(ShopPurchaseOrder shopPurchaseOrder,PurchaseOrderSaveDto param) {
        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())){
            //友福利下单不走企配仓
            param.setIsCompanyStore(0);
            return;
        }
        StoreShopAddressVo storeAddressByAddressId = companyStoreAddressUserService.getStoreAddressByAddressId(shopPurchaseOrder.getAddressId());
        //没有走企配仓
        if (storeAddressByAddressId == null) {
            param.setIsCompanyStore(0);
            return;
        }
        //走了企配仓 先判断 1:
        List<PurchaseOrderDetailSaveDto> purchaseOrderDetailList = param.getPurchaseOrderDetailList();

        //1. 值列表配置,qpc_filtration_company 定义开启企配仓服务订单,若是 xx,xxx等供应商, 不走企配仓
        List<SystemDictDataEntity> filtrationCompany = systemDictDataMapper.selectListByDictType("qpc_filtration_company");

        // 过滤出 status 等于 1 的元素
        List<SystemDictDataEntity> filteredList = filtrationCompany.stream()
                .filter(entity -> entity.getStatus() == 1)
                .collect(Collectors.toList());

        List<String> filterSupplierList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(filteredList)){
            //下单供应商合计
            List<String> orderSupplier = purchaseOrderDetailList.stream().map(PurchaseOrderDetailSaveDto::getSupplierCode).distinct().collect(Collectors.toList());

            //配置的 需要过滤供应商合计
            List<String> supplierCodeList = filteredList.stream().map(SystemDictDataEntity::getValue).collect(Collectors.toList());

            if (new HashSet<>(supplierCodeList).containsAll(new HashSet<>(orderSupplier))) {
                //完全包含
                //下单供应商合计都是需要过滤的, 直接下单
                param.setIsCompanyStore(0);
                return;
            } else if (CollectionUtils.containsAny(orderSupplier, supplierCodeList)) {
                //部分包含
                // 使用流过滤并将结果收集到 filterSupplierList
                filterSupplierList = orderSupplier.stream()
                        .filter(supplierCodeList::contains) // 过滤出在 supplierCodeList 中的供应商代码
                        .collect(Collectors.toList()); // 收集结果到列表中
                //默认走企配仓
                param.setIsCompanyStore(1);
            }//不包含 往下走
        }

        //2: - 若商品标签包含【自行结算】，申请下单失效，点击提示：当前地址已启用企配仓，请将平台和自行结算商品分开提单！
        // 使用一个计数器来统计自行结算商品的数量
        long selfSettlementCount = purchaseOrderDetailList.stream()
                .filter(detail -> detail.getSettlementType() == 0) // 筛选出自行结算
                .count();
        if (selfSettlementCount == purchaseOrderDetailList.size()) {
            // 只有自行结算
            param.setIsCompanyStore(0);
            return;
        } else if (selfSettlementCount > 0) {
            throw new ParameterException("当前地址已启用企配仓，请将平台和自行结算商品分开提单！");
        }
        //最终判断走企配仓 , 那就维护下参数
        param.setIsCompanyStore(1);
        CompanyStoreOrderAddressSaveDto companyStoreOrderAddressSaveDto = DataAdapter.convert(storeAddressByAddressId, CompanyStoreOrderAddressSaveDto.class);
        companyStoreOrderAddressSaveDto.setPurchaseNumber(shopPurchaseOrder.getPurchaseNumber())
                .setUserId(LocalUserHolder.get().getId())
                .setOrganizationName(LocalUserHolder.get().getEntityOrganizationName())
                .setFilterSupplierList(filterSupplierList)
                .setOrganizationCode(LocalUserHolder.get().getEntityOrganizationCode());
        param.setCompanyStoreOrderAddressSaveDto(companyStoreOrderAddressSaveDto);
    }

    public PageResp<ShopDhecUseVo> dhecUseList(PageReq pageReq) {
        return dhecService.dhecUseList(pageReq);
    }

    public String dhecAllocationUrl(String key, String value) {
        return dhecService.dhecAllocationUrl(key, value);
    }

    /**
     * 保存联友用户下单定制化信息
     */
    private void savePurchaseCustomLy(ShopPurchaseOrder purchaseOrder, PurchaseCustomLySaveDto customLySaveDto, Integer srmAutoCreateOrder) {
        if (!lyCompanyCodes.equals(purchaseOrder.getCompanyCode()) || ObjectUtil.equal(srmAutoCreateOrder, 1)) {
            // 非联友订单或srm自动创建订单的，不处理
            return;
        }
        if (customLySaveDto == null) {
            throw new OrderException("联友定制化下单：定制化信息缺失");
        }
        PurchaseCustomLy customLy = PurchaseCustomLyConvert.INSTANCE.convert(customLySaveDto);
        customLy.setLyCustomId(UUID.randomUUID().toString().replaceAll("-", ""));
        customLy.setPurchaseNumber(purchaseOrder.getPurchaseNumber());
        purchaseCustomLyMapper.insert(customLy);
    }


    /**
     * 切换用户活动下所有单据的组织
     *
     * @param userId
     * @param oldActivityId
     * @param newActivityId
     * @param systemOrganization 切换后的组织
     * @param invoiceId          切换后的发票主体
     */
    public void fixPurchaseData(Long userId, Long oldActivityId, Long newActivityId, SystemOrganization systemOrganization, Integer invoiceId) {
        List<ShopPurchaseOrder> purchaseOrderList = this.list(new LambdaQueryWrapper<ShopPurchaseOrder>().eq(ShopPurchaseOrder::getActivityId, oldActivityId).eq(ShopPurchaseOrder::getApplyUserId, userId));
        if (CollectionUtil.isEmpty(purchaseOrderList)) {
            //用户没下单
            return;
        }
        purchaseOrderList.forEach(e -> {
            e.setCompanyCode(systemOrganization.getCode())
                    .setCompanyName(systemOrganization.getName())
                    .setApplyDeptName(systemOrganization.getName())
                    .setApplyDeptId(systemOrganization.getId().toString())
                    .setOrganizationId(systemOrganization.getId())
                    .setActivityId(newActivityId)
                    .setInvoiceId(invoiceId.toString());
        });
        this.updateBatchById(purchaseOrderList);

        List<ShopPurchaseSubOrder> purchaseSubOrderList = purchaseSubOrderService.list(new LambdaQueryWrapper<ShopPurchaseSubOrder>().in(ShopPurchaseSubOrder::getPurchaseNumber, purchaseOrderList.stream().map(ShopPurchaseOrder::getPurchaseNumber).collect(Collectors.toList())));
        purchaseSubOrderList.forEach(e -> {
            e.setOrganizationId(systemOrganization.getId());
        });
        purchaseSubOrderService.updateBatchById(purchaseSubOrderList);
        List<String> orderNumberList = purchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());
        List<ShopPurchaseSubOrderDetail> purchaseSubOrderDetailList = purchaseSubOrderDetailService.list(new LambdaQueryWrapper<ShopPurchaseSubOrderDetail>().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList));
        purchaseSubOrderDetailList.forEach(e -> {
            e.setOrganizationId(systemOrganization.getId());
        });
        purchaseSubOrderDetailService.updateBatchById(purchaseSubOrderDetailList);

        List<ShopOrderAddress> orderAddressList = shopOrderAddressService.list(new LambdaQueryWrapper<ShopOrderAddress>().in(ShopOrderAddress::getPurchaseId, purchaseOrderList.stream().map(ShopPurchaseOrder::getPurchaseId).collect(Collectors.toList())));
        orderAddressList.forEach(e -> {
            e.setOrganizationId(systemOrganization.getId());
        });
        shopOrderAddressService.updateBatchById(orderAddressList);

        //修复 售后相关
        //shop_return 修改organization_id,company_name
        //shop_return_detail 修改organization_id
        List<ShopReturn> shopReturnList = returnSrv.list(new LambdaQueryWrapper<ShopReturn>().in(ShopReturn::getOrderId, purchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderId).collect(Collectors.toList())));
        if (CollectionUtil.isNotEmpty(shopReturnList)) {
            shopReturnList.forEach(e -> {
                e.setOrganizationId(systemOrganization.getId()).setCompanyName(systemOrganization.getName());
            });
            returnSrv.updateBatchById(shopReturnList);

            List<ShopReturnDetail> shopReturnDetailList = returnDetailSrv.list(new LambdaQueryWrapper<ShopReturnDetail>().in(ShopReturnDetail::getReturnId, shopReturnList.stream().map(ShopReturn::getId).collect(Collectors.toList())));
            shopReturnDetailList.forEach(e -> {
                e.setOrganizationId(systemOrganization.getId());
            });
            returnDetailSrv.updateBatchById(shopReturnDetailList);
        }

        //修复包裹,包裹明细 验收
        //shop_delivery 修改organization_id
        //shop_delivery_detail 修改organization_id
        //修复 验收相关
        //settle_check_form 修改company_code,company_name
        List<ShopDelivery> shopDeliveryList = deliverySrv.list(new LambdaQueryWrapper<ShopDelivery>().in(ShopDelivery::getOrderNumber, orderNumberList));
        if (CollectionUtil.isEmpty(shopDeliveryList)) return;
        shopDeliveryList.forEach(e -> {
            e.setOrganizationId(systemOrganization.getId());
        });
        deliverySrv.updateBatchById(shopDeliveryList);

        List<ShopDeliveryDetail> deliveryDetailList = deliveryDetailSrv.list(new LambdaQueryWrapper<ShopDeliveryDetail>().in(ShopDeliveryDetail::getDeliveryId, shopDeliveryList.stream().map(ShopDelivery::getId).collect(Collectors.toList())));
        deliveryDetailList.forEach(e -> {
            e.setOrganizationId(systemOrganization.getId());
        });
        deliveryDetailSrv.updateBatchById(deliveryDetailList);

        List<SettleCheckForm> settleCheckFormList = settleCheckService.list(new LambdaQueryWrapper<SettleCheckForm>().in(SettleCheckForm::getOrderNumber, orderNumberList));
        if (CollectionUtil.isEmpty(settleCheckFormList)) return;
        settleCheckFormList.forEach(e -> {
            e.setCompanyCode(systemOrganization.getCode()).setCompanyName(systemOrganization.getName());
        });
        settleCheckService.updateBatchById(settleCheckFormList);
    }

    /**
     * 切换用户活动下所有购物车的组织
     *
     * @param userId
     * @param oldActivityCode
     * @param newActivityCode
     * @param organizationId  切换后的组织Id
     */
    public void fixCartData(Long userId, String oldActivityCode, String newActivityCode, Long organizationId) {
        List<ShopCart> shopCartList = shopCartService.list(new LambdaQueryWrapper<ShopCart>().eq(ShopCart::getUserId, userId).eq(ShopCart::getGoodsIntegralType, oldActivityCode));
        shopCartList.forEach(e -> {
            e.setOrganizationId(organizationId).setGoodsIntegralType(newActivityCode);
        });
        shopCartService.updateBatchById(shopCartList);
    }

    public List<InvoiceSubject> querySzlyInvoiceSubject() {
        final InvoiceSubject invoiceSubject = this.invoiceSubjectService.getById(invoiceId);
        return Collections.singletonList(invoiceSubject);
    }

    /**
     * 校验商品合同  来源是dfmall 并且 独立供应商 非对接接口平台供应商
     * @param goodsCodes
     * @return
     */
    public Map<String, String> goodsContractCheck(Collection<String> goodsCodes) {
        var goodsList = shopGoodsService.getBaseMapper().selectGoodsSkuByGoodsCodeIn(goodsCodes);
        List<ShopSupplier> suppliers = shopSupplierService.getSupplierInfoByCodes(goodsList.stream().map(ShopGoods::getSupplierCode).distinct().collect(Collectors.toList()));
        Map<String, ShopSupplier> suppliersDataSource = suppliers.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity(), (key1, key2)->key1));
        Map<String, String> errorGoodsMap = new HashMap<>();
        goodsList.forEach(goods -> {
            if (goods != null
                    && (Objects.equals(suppliersDataSource.get(goods.getSupplierCode()).getDataSource(), "dfmall")
                    || Objects.equals(suppliersDataSource.get(goods.getSupplierCode()).getDataSource(), CompanyEnum.VOYAH.getCompanyCode())
                    || Objects.equals(suppliersDataSource.get(goods.getSupplierCode()).getDataSource(), CompanyConstant.S4_DATA_SOURCE)
                    || Objects.equals(suppliersDataSource.get(goods.getSupplierCode()).getDataSource(), CompanyEnum.HONDA.getCompanyCode()))
                    && Objects.equals(suppliersDataSource.get(goods.getSupplierCode()).getSupplierType(), 1)
                    && !openApiConfig.getSuppliers().contains(goods.getSupplierCode())
                    && !Objects.equals(goods.getSupplierCode(), dhecSmartWarehouseConfig.getSupplierCode())
            ) {
                List<ShopGoodsContractEntity> goodsContractList = shopGoodsContractService.queryAllByGoodsId(goods.getGoodsId());
                if (CollectionUtil.isEmpty(goodsContractList)) {
                    errorGoodsMap.put(goods.getGoodsCode(), "商品[" + goods.getGoodsCode() + "]合同校验未通过");
                }
            }
        });

        LoginUser user = LocalUserHolder.get();
        if (user != null && CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            //岚图校验
            List<Long> goodIds = goodsList.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList());
            SystemOrganization organization = systemOrganizationService.getOrganizationByCode(CompanyEnum.VOYAH.getCompanyCode());
            QueryWrapper<ShopGoodsContractEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ShopGoodsContractEntity::getGoodsId,goodIds).eq(ShopGoodsContractEntity::getCompanyOrgId,organization.getId());
            List<ShopGoodsContractEntity> list = shopGoodsContractService.list(queryWrapper);
            if(CollUtil.isEmpty(list)){
                //电商商品 这里不会有数据 return 一下
                return errorGoodsMap;
            }
            List<Long> contractIds = list.stream().map(ShopGoodsContractEntity::getContractId).collect(Collectors.toList());

            //判断合同对应的供应商是否是岚图srm供应商，如果是的，才需要参与权限校验
            //一次下单只能买一个合同的商品
            QueryWrapper<SupplierContractRealEntity> realQw = new QueryWrapper<>();
            realQw.lambda().eq(SupplierContractRealEntity::getContractId,contractIds.get(0));
            List<SupplierContractRealEntity> realEntities = supplierContractRealService.list(realQw);
            SupplierContractRealEntity realEntity = realEntities.get(0);

            ShopSupplier supplier = shopSupplierService.querySupplierById(Convert.toStr(realEntity.getSupplierId()));
            //只处理岚图srm供应商的东西
            if(CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(supplier.getDataSource())){
                QueryWrapper<SystemContractPermission> permissionQueryWrapper = new QueryWrapper<>();
                permissionQueryWrapper.lambda().in(SystemContractPermission::getContractId,contractIds);
                List<SystemContractPermission> contractPermissions = contractPermissionService.list(permissionQueryWrapper);
                List<String> deptIds = contractPermissions.stream().map(SystemContractPermission::getDeptId).collect(Collectors.toList());
                if(CollUtil.isEmpty(deptIds)){
                    errorGoodsMap.put("", "当前用户无权限购买该合同商品，请联系合同对应采购员授予履约责任部门权限");
                    return errorGoodsMap;
                }
                //查询部门下的所有部门ID,如果更当前登陆人的ID一致，就表示可以买
                List<SystemOrganization> organizationAll = Lists.newArrayList();
                List<SystemOrganization> currentOrg = systemOrganizationService.listByIds(deptIds);
                organizationAll.addAll(currentOrg);
                for (String deptId : deptIds) {
                    List<SystemOrganization> organizations = systemOrganizationService.getOrganizationsByParentIdFromCache(Convert.toLong(deptId),true);
                    organizationAll.addAll(organizations);
                }
                List<Long> collect = organizationAll.stream().map(SystemOrganization::getId).collect(Collectors.toList());
                if(!collect.contains(user.getOrganizationId())){
                    errorGoodsMap.put("", "当前用户无权限购买该合同商品，请联系合同对应采购员授予履约责任部门权限");
                }
            }
        }

        return errorGoodsMap;
    }

    public List<ShopPurchaseOrder> listByPurchaseNumbers(Collection<String> purchaseNumbers) {
        return this.baseMapper.listByPurchaseNumbers(purchaseNumbers);
    }

    @Getter
    @Setter
    public static class GoodsCountDto {
        private String goodsCode;
        private Integer goodsCnt;
    }


    private static void _fillPurchaseOrderInfo(ShopPurchaseOrder purchaseOrder, BigDecimal purchaseGoodsPrice, BigDecimal purchaseFreightPrice, BigDecimal purchaseSubsidyFreight, BigDecimal purchaseGoodsNumber, BigDecimal purchaseGoodsPriceNaked) {
        purchaseOrder.setPurchaseState(10);
        purchaseOrder.setPurchaseGoodsPrice(purchaseGoodsPrice);
        purchaseOrder.setPurchaseGoodsPriceNaked(purchaseGoodsPriceNaked);
        purchaseOrder.setPurchaseFreightPrice(purchaseFreightPrice);
        purchaseOrder.setPurchaseSubsidyFreight(purchaseSubsidyFreight);
        purchaseOrder.setPurchaseGoodsNumber(purchaseGoodsNumber);
        purchaseOrder.setPurchaseTotalPrice(purchaseGoodsPrice.add(purchaseFreightPrice));
    }

    @NotNull
    private DeliveryPriceResp _checkDelivery(List<GoodCodeInfoListParams> goodCodeInfoParamsList, RegionDto regionDto, AddressInfo addressInfo) {
        DeliveryPriceResp deliveryPriceResp = remoteInfoManage.getDeliveryPrice(goodCodeInfoParamsList, regionDto, addressInfo.orderAddress.getAddress());
        if (deliveryPriceResp.getTotalPrice().compareTo(BigDecimal.ZERO) > 0
                && !yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
            throw new ParameterException("不能提交含有运费的订单,请联系客服!");
        }
        return deliveryPriceResp;
    }

    private void _fillSubOrderData(
            PurchaseOrderSaveDto param,
            List<PurchaseOrderDetailSaveDto> purchaseOrderDetailList,
            ShopPurchaseSubOrder subOrder,
            ShopPurchaseOrder purchaseOrder,
            BigDecimal orderPriceTax,
            BigDecimal orderPriceNaked,
            BigDecimal supplierOrderPriceTax,
            BigDecimal supplierOrderPriceNaked
    ) {
        LoginUser user = LocalUserHolder.get();
        subOrder.setContractNumber(purchaseOrderDetailList.get(0).getContractNumber());
        subOrder.setOrderState(10);
        // 订单需结算,从0开始验收完成后累加
        subOrder.setNeedSettleAmount(BigDecimal.ZERO);
        subOrder.setPurchaseNumber(purchaseOrder.getPurchaseNumber());
        subOrder.setOrderPriceTax(orderPriceTax);
        subOrder.setOrderPriceNaked(orderPriceNaked);
        subOrder.setSupplierOrderPriceTax(supplierOrderPriceTax);
        subOrder.setSupplierOrderPriceNaked(supplierOrderPriceNaked);
        subOrder.setOrganizationId(user.getOrganizationId());
        // 设置srm采购单的值
        String subOrderSrmApplyId = getSubOrderSrmApplyId(param.getSrmApplyId(), subOrder.getContractNumber());
        subOrder.setSrmApplyId(subOrderSrmApplyId);

        isPrePayOrder(purchaseOrder, subOrder);

        List<OrderRemarkDto> orderRemark = param.getOrderRemark();
        if (CollectionUtil.isNotEmpty(orderRemark)) {
            log.info("待处理数据{}",orderRemark);
            List<OrderRemarkDto> supplierRemark = orderRemark.stream().filter(item -> item.getSupplierCode().equals(subOrder.getSupplierCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(supplierRemark)) {
                log.info("处理备注数据{}",supplierRemark);
                subOrder.setRemark(supplierRemark.get(0).getRemark());
            }
        }
    }

    private void isPrePayOrder(ShopPurchaseOrder purchaseOrder, ShopPurchaseSubOrder subOrder) {
        //营销单位,非SRM供应商的订单  需要预付款
        if (dfsYamlConfig.getCompanycode().contains(purchaseOrder.getCompanyCode())
                && !"srm".equals(subOrder.getSupplierDataSource())) {
            subOrder.setIsPrePay(1);
            return;
        }
        //配置了预付款的标准企业 订单也标记是预付款
        SystemDictDataEntity prePayCompany = dictDataSrv.getDictData("pre_pay_company", purchaseOrder.getCompanyCode());
        if (prePayCompany != null && prePayCompany.getStatus() == 1) {
            subOrder.setIsPrePay(1);
            return;
        }
    }

    @NotNull
    private ShopPurchaseSubOrderDetail _orderDetailFill(PurchaseOrderDetailSaveDto orderDetailSaveDto, GoodsMasterDetailVo goodsMasterDetailVo, ShopPurchaseSubOrderDetail subOrderDetail, Integer rowNumber, Map<String, RemotePriceInfoResp> priceInfoRespMap, ShopPurchaseSubOrder subOrder) {
        var user = LocalUserHolder.get();
        BeanUtil.copyProperties(goodsMasterDetailVo, subOrderDetail);
        subOrderDetail.setThirdLevelGcName(goodsMasterDetailVo.getThirdClassName());
        BeanUtil.copyProperties(orderDetailSaveDto, subOrderDetail);
        subOrderDetail.setApplyNum(Long.valueOf(orderDetailSaveDto.getApplyNum()));
        subOrderDetail.setApplyNumDecimal(orderDetailSaveDto.getApplyNumDecimal());
        subOrderDetail.setConfirmNum(Long.valueOf(orderDetailSaveDto.getApplyNum()));
        subOrderDetail.setConfirmNumDecimal(orderDetailSaveDto.getApplyNumDecimal());
        subOrderDetail.setRowSerialNumber(rowNumber);
        subOrderDetail.setOrderDetailState(10);
        subOrderDetail.setOrganizationId(user.getOrganizationId());
        subOrderDetail.setPurchaseGoodsId(orderDetailSaveDto.getPurchaseGoodsId());
        RemotePriceInfoResp remotePriceInfoResp = priceInfoRespMap.get(orderDetailSaveDto.getGoodsCode());
        subOrderDetail = this.computeOrderDetail(subOrder.getPricingMode(), subOrderDetail, remotePriceInfoResp);
        subOrderDetail.setSupplierUnitOriginalPriceTax(remotePriceInfoResp.getGoodsOriginalPrice());
        subOrderDetail.setSupplierUnitOriginalPriceNaked(remotePriceInfoResp.getGoodsOriginalNakedPrice());
        return subOrderDetail;
    }

    private static void _srmPriceProcess(ShopSrmContractEntity srmContract, ShopPurchaseSubOrderDetail subOrderDetail) {
        if (!ObjectUtils.isEmpty(srmContract)) {
            // 人民币金额 =（1+关税）* 商品价格 * 汇率
            BigDecimal goodsPriceCny = srmContract.getTariff().add(BigDecimal.ONE).
                    multiply(subOrderDetail.getGoodsUnitPriceTax()).
                    multiply(srmContract.getExchangeRate());
            subOrderDetail.setGoodsUnitNakedPriceCny(goodsPriceCny.setScale(4, RoundingMode.HALF_UP));
            subOrderDetail.setGoodsUnitTaxPriceCny(goodsPriceCny.setScale(4, RoundingMode.HALF_UP));
            subOrderDetail.setCurrencyCode(srmContract.getCurrencyCode());
            subOrderDetail.setCurrencyName(srmContract.getCurrencyName());
        }
    }

    @NotNull
    private ShopPurchaseSubOrderDetail initSubOrderDetail(PurchaseOrderDetailSaveDto orderDetailSaveDto, ShopPurchaseSubOrder subOrder) {
        var subOrderDetail = new ShopPurchaseSubOrderDetail();
        subOrderDetail.setNeederName(orderDetailSaveDto.getNeederName());
        subOrderDetail.setNeederDepartmentName(orderDetailSaveDto.getNeederDepartmentName());
        if (StringHelper.IsEmptyOrNull(orderDetailSaveDto.getOrderDetailId())) {
            // orderDetailSaveDto.getOrderDetailId() 存在值的情况是什么?
            if (StringHelper.IsEmptyOrNull(subOrder.getOrderId())) {
                final String orderId = UUID.randomUUID().toString().replaceAll("-", "");
                String orderNumber = this.orderNumberGenerator.make();

                if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                    orderNumber += "Y";
                } else {
                    orderNumber += "D";
                }
//                if(CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(subOrder.getSupplierDataSource())){
//                    orderNumber = subOrder.getContractNumber() + orderNumber;
//                }
                subOrder.setOrderId(orderId);
                subOrder.setOrderNumber(orderNumber);
            }
            final String orderDetailId = UUID.randomUUID().toString().replaceAll("-", "");
            orderDetailSaveDto.setOrderDetailId(orderDetailId);
            orderDetailSaveDto.setOrderId(subOrder.getOrderId());
            orderDetailSaveDto.setOrderNumber(subOrder.getOrderNumber());
        } else {
            subOrder.setOrderId(orderDetailSaveDto.getOrderId());
            subOrder.setOrderNumber(orderDetailSaveDto.getOrderNumber());
        }
        return subOrderDetail;
    }

    @Nullable
    private ShopSrmContractEntity getSrmContract(ShopPurchaseSubOrder subOrder, String dataSource) {
        ShopSrmContractEntity srmContract = null;
        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(dataSource)
                || CompanyConstant.S4_DATA_SOURCE.equalsIgnoreCase(dataSource)
                || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(dataSource)) {
            // 走岚图计价模式 支持小数
            subOrder.setPricingMode(4);
        } else if (CompanyEnum.DHEC.getCompanyCode().equalsIgnoreCase(dataSource)) {
            // 走dhec计价逻辑
            subOrder.setPricingMode(3);
        } else if (StrUtil.isBlank(subOrder.getContractNumber())) {
            // 不走合同则用通用计价逻辑
            subOrder.setPricingMode(0);
        } else {
            // 走srm合同的单子按照合同要求计价
            srmContract = shopSrmContractService.getBySrmContractNumber(subOrder.getContractNumber());
            subOrder.setPricingMode(srmContract.getPricingMode());
            // 联友持续采购合同下单的独立供应商需要确认订单状态
            if (Integer.valueOf(1).equals(srmContract.getIsLanYou()) && srmContract.getPurchaseType() == 2) {
                subOrder.setSupplierConfirmState(0);
            }
        }
        return srmContract;
    }

    @NotNull
    private static RegionDto _initRegion(AddressInfo addressInfo) {
        final RegionDto regionDto = new RegionDto();
        regionDto.setProvince(addressInfo.shopAddress.getProvince());
        regionDto.setCity(addressInfo.shopAddress.getCity());
        regionDto.setCounty(addressInfo.shopAddress.getDistrict());
        return regionDto;
    }

    private String saveInvoiceInfo(ShopPurchaseOrder purchaseOrder) {
        LoginUser user = LocalUserHolder.get();
        final InvoiceSubject invoiceSubject = this.invoiceSubjectService.getById(purchaseOrder.getInvoiceId());
        if (null == invoiceSubject) {
            log.error("发票信息失效携带入参:" + purchaseOrder);
            throw new ParameterException("发票信息失效，请检查发票信息");
        }
        final ShopOrderInvoice orderInvoiceEntity = new ShopOrderInvoice();
        BeanUtil.copyProperties(invoiceSubject, orderInvoiceEntity);
        orderInvoiceEntity.setPurchaseId(purchaseOrder.getPurchaseId());
        orderInvoiceEntity.setOrganizationId(user.getOrganizationId());
        orderInvoiceEntity.setSubjectCode(invoiceSubject.getSubjectCode());
        this.shopOrderInvoiceService.save(orderInvoiceEntity);
        return invoiceSubject.getInvoiceSubject();
    }

    @NotNull
    private AddressInfo saveAddress(ShopPurchaseOrder purchaseOrder, PurchaseOrderSaveDto param) {
        var user = LocalUserHolder.get();
        //下单地址
        ShopAddress shopAddress = new ShopAddress();
        //企配仓地址
        ShopAddress address = new ShopAddress();
        String addressInfo = "";
        if (param.getDmsOrderFlag() == 1) {
            shopAddress = param.getDmsShopAddress();
            purchaseOrder.setIsExtReceipt(1);
            shopAddress.setFactoryCode(CompanyConstant.LTDMS_FACTORY_CODE);
            shopAddress.setFactoryName("DMS工厂");
            address = shopAddress;
        } else {
            shopAddress = this.shopAddressService.getById(purchaseOrder.getAddressId());
            if (null == shopAddress) {
                throw new ParameterException("地址信息失效，请在【个人中心】-【地址管理】中设置地址");
            }
            if (param.getIsCompanyStore() == 1) {
                //企配仓替换地址
                CompanyStoreOrderAddressSaveDto companyStoreOrderAddressSaveDto = param.getCompanyStoreOrderAddressSaveDto();
                BeanUtil.copyProperties(companyStoreOrderAddressSaveDto, address);
                //拼接个人地址 地址+实际收货人姓名和电话
                addressInfo = shopAddress.getProvince() + shopAddress.getCity() + shopAddress.getDistrict() + (StringUtils.isBlank(shopAddress.getStreet()) ? "" : shopAddress.getStreet()) + shopAddress.getAddress() + shopAddress.getAddressName() + shopAddress.getMobPhone();
                //维护企配仓订单下单人地址
                companyStoreOrderAddressSaveDto.setShopAddress(shopAddress);
                companyStoreOrderAddressSaveDto.setPurchaseNumber(purchaseOrder.getPurchaseNumber());
                param.setCompanyStoreOrderAddressSaveDto(companyStoreOrderAddressSaveDto);
            }else{
                address = shopAddress;
            }
        }
        // 收货地址保存
        final ShopOrderAddress orderAddress = new ShopOrderAddress();

        BeanUtil.copyProperties(shopAddress, orderAddress);
        //下单维护四级地址
        String street = shopAddress.getStreet();
        if (StrUtil.isNotBlank(street)) {
            orderAddress.setAddress(street + shopAddress.getAddress());
        }
        orderAddress.setPurchaseId(purchaseOrder.getPurchaseId());
        orderAddress.setOrganizationId(user.getOrganizationId());
        orderAddress.setCreateTime(new Date());
        orderAddress.setUpdateTime(new Date());
        orderAddress.setIsCompanyStore(param.getIsCompanyStore());
        orderAddress.setAddressInfo(addressInfo);
        this.shopOrderAddressService.save(orderAddress);
        return new AddressInfo(address, orderAddress);
    }

    private static class AddressInfo {
        public final ShopAddress shopAddress;
        public final ShopOrderAddress orderAddress;

        public AddressInfo(ShopAddress shopAddress, ShopOrderAddress orderAddress) {
            //企配仓/下单地址   走了企配仓, 这俩个地址就不一样了
            this.shopAddress = shopAddress;
            //下单地址
            this.orderAddress = orderAddress;
        }
    }

    @NotNull
    private List<List<PurchaseOrderDetailSaveDto>> getOrderGroupList(PurchaseOrderSaveDto param, ShopPurchaseOrder purchaseOrder) {
        final List<List<PurchaseOrderDetailSaveDto>> orderGroupList = new ArrayList<>();
        if (StringHelper.IsEmptyOrNull(purchaseOrder.getPurchaseId())) {
            purchaseOrder.setPurchaseId(UUID.randomUUID().toString().replaceAll("-", ""));

            // 新增时，已启动流程的订单，已生成采购单号。  这里判空，对没有进入流程的采购单生成采购单号
            if (StringHelper.IsEmptyOrNull(purchaseOrder.getPurchaseNumber())) {
                String purchaseNumber = this.orderNumberGenerator.make();
                if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                    purchaseNumber += "Y";
                } else {
                    purchaseNumber += "D";
                }

                purchaseOrder.setPurchaseNumber(purchaseNumber);
            }

            // 有福利租户默认全部平台内结算
            if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                param.getPurchaseOrderDetailList().forEach(e -> {
                    e.setSettlementType(1);
                });
            }

            // 新增的采购订单商品明细，根据商品的供应商编码、商品类型、合同号分组
            param.getPurchaseOrderDetailList().stream()
                    .collect(Collectors.groupingBy(ordDetail -> Arrays.asList(
                            ordDetail.getSupplierCode(),
                            ordDetail.getGoodsModel(),
                            ordDetail.getContractNumber(),
                            ordDetail.getSettlementType()
                    ), Collectors.toList()))
                    .forEach((supplierCode, orderDetailList) -> orderGroupList.add(orderDetailList));
        } else {
            // 修改的采购订单商品明细，根据子订单号进行分组
            param.getPurchaseOrderDetailList().stream().collect(Collectors.groupingBy(PurchaseOrderDetailSaveDto::getOrderNumber, Collectors.toList())).forEach((supplierCode, orderDetailList) -> {
                orderGroupList.add(orderDetailList);
            });
        }
        return orderGroupList;
    }

    @Nullable
    private SystemBudget _getBudget(PurchaseOrderSaveDto purchaseOrderSaveDto) {
        SystemBudget systemBudget = null;
        if (purchaseOrderSaveDto.getBudgetId() != null) {
            //走预算查询预算是否外部推送数据，如果是则需要调用外部接口进行验证预算是否可用
            systemBudget = budgetService.getById(purchaseOrderSaveDto.getBudgetId());
            /* 集团预算系统升级，不需要校验了
            if (!ObjectUtils.isEmpty(systemBudget)) {
                //预算为外部推送数据，验证预算
                if (systemBudget.getPushState() != null && systemBudget.getPushState() == 1 && systemBudget.getIsInvest() != 1) {
                    log.info("【_getBudget】校验集团预算 :[{}]", systemBudget.getApplyNumber());
                    pushBudgetInfoService.sendBudgetInfoState(systemBudget.getApplyNumber());
                }
            }
             */
        }
        return systemBudget;
    }

    @NotNull
    private static ShopPurchaseOrder _initPurchaseOrder(PurchaseOrderSaveDto purchaseOrderSaveDto) {
        var user = LocalUserHolder.get();
        final ShopPurchaseOrder shopPurchaseOrder = new ShopPurchaseOrder();
        BeanUtil.copyProperties(purchaseOrderSaveDto, shopPurchaseOrder);
        shopPurchaseOrder.setOrganizationId(user.getOrganizationId());
        shopPurchaseOrder.setApplyDeptId(String.valueOf(user.getOrganizationId()));
        shopPurchaseOrder.setApplyDeptName(user.getOrganizationName());
        shopPurchaseOrder.setCanConfirm(PreOrderConfirmTypeEnum.NOT_OK.getCode());
        shopPurchaseOrder.setExtBudget(0);
        if (CompanyEnum.DHEC.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            shopPurchaseOrder.setBudgetType(Constant.DHEC2.equals(purchaseOrderSaveDto.getBudgetType()) ? "2" : "1");
        } else if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            if (Constant.VOYAH1.equals(purchaseOrderSaveDto.getBudgetType()) || Constant.VOYAH4.equals(purchaseOrderSaveDto.getBudgetType())) {
                shopPurchaseOrder.setBudgetType(PurchaseBudgetTypeEnum.FY.getBudgetType());
                shopPurchaseOrder.setExtBudget(1);
            } else if (Constant.VOYAH2.equals(purchaseOrderSaveDto.getBudgetType())) {
                shopPurchaseOrder.setBudgetType(PurchaseBudgetTypeEnum.TZ.getBudgetType());
                shopPurchaseOrder.setExtBudget(1);
            } else if (Constant.VOYAH3.equals(purchaseOrderSaveDto.getBudgetType())) {
                shopPurchaseOrder.setBudgetType(PurchaseBudgetTypeEnum.CH.getBudgetType());
                shopPurchaseOrder.setExtBudget(0);
            }
            //预算无需启动类
            if (Constant.VOYAH4.equals(purchaseOrderSaveDto.getBudgetType())) {
                shopPurchaseOrder.setBudgetWithoutStart(1);
            }
        }
        shopPurchaseOrder.setOrderSalesChannel(1);
        return shopPurchaseOrder;
    }

    private static void _checkBudgetCode(PurchaseOrderSaveDto purchaseOrderSaveDto) {
        var user = LocalUserHolder.get();
        if (CompanyEnum.DFG01.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
        ) {
            //必须选择预算号
            if (StrUtil.isBlank(purchaseOrderSaveDto.getBudgetCode())) {
                throw new ParameterException("请选择预算号!");
            }
        }
    }

    private static void _checkDfpvBudgetCode(AddressInfo addressInfo, PurchaseOrderSaveDto purchaseOrderSaveDto) {
        var user = LocalUserHolder.get();
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
        ) {
            //岚图必须选择预算号
            if (addressInfo.shopAddress.getAddressType() == 2) {
                if (StrUtil.isBlank(purchaseOrderSaveDto.getBudgetCode())) {
                    throw new ParameterException("工厂地址必须选择预算！");
                }
            }
        }
    }

    private static void _checkApplyUser(ShopPurchaseOrder shopPurchaseOrder) {
        var user = LocalUserHolder.get();
        if (shopPurchaseOrder.getApplyUserId().equals(String.valueOf(shopPurchaseOrder.getAccepterId()))) {
            //岚图 智新 验收人可以是申请人
            if (!CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                    && !CompanyEnum.IPS.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
                throw new ParameterException("验收人不能是申请人!");
            }
        }
    }

    private void _checkVoyahBudget(LoginUser user, ShopPurchaseOrder purchaseOrder, List<ShopPurchaseSubOrder> subOrderList, PurchaseOrderSaveDto param) {
        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            if (purchaseOrder.getExtBudget() == 1 && PurchaseBudgetTypeEnum.TZ.getBudgetType().equalsIgnoreCase(purchaseOrder.getBudgetType())) {
                //只针对岚图投资类
                List<String> collect = subOrderList.stream().map(ShopPurchaseSubOrder::getContractNumber).distinct().collect(Collectors.toList());
                if (collect.size() > 1) {
                    throw new ParameterException("投资类预算不支持多合同同时下单，请分开提交!");
                }
            }
            if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(subOrderList.get(0).getSupplierDataSource()) & !PurchaseBudgetTypeEnum.CH.getBudgetType().equalsIgnoreCase(purchaseOrder.getBudgetType())) {
                //岚图合同订单校验预算
                if (CollUtil.isEmpty(param.getMultipleBudget())) {
                    throw new ParameterException("岚图SRM合同商品必须选择预算！");
                }
                Map<String, ShopPurchaseSubOrder> subCollect = subOrderList.stream().collect(Collectors.toMap(ShopPurchaseSubOrder::getOrderNumber, Function.identity()));
                Map<String, List<MultipleBudgetDto>> multipleBudget = param.getMultipleBudget().stream().collect(Collectors.groupingBy(MultipleBudgetDto::getOrderNumber));
                for (Map.Entry<String, ShopPurchaseSubOrder> stringShopPurchaseSubOrderEntry : subCollect.entrySet()) {
                    BigDecimal orderPriceNaked = stringShopPurchaseSubOrderEntry.getValue().getOrderPriceNaked();
                    BigDecimal reduce = multipleBudget.get(stringShopPurchaseSubOrderEntry.getKey()).stream().map(MultipleBudgetDto::getUseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (reduce.compareTo(orderPriceNaked) != 0) {
                        throw new ParameterException("您输入的预算使用金额（未税）与订单实际金额（未税）不一致，请检查更正。");
                    }
                }
            } else {
                if (StrUtil.isBlank(param.getBudgetCode())) {
                    throw new ParameterException("请选择预算号!");
                }
            }
        }
    }

    private void _generatedVoyahSrmSapOrder(ShopPurchaseOrder purchaseOrder, List<ShopPurchaseSubOrder> subOrderList) {
        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(purchaseOrder.getCompanyCode())) {
            for (ShopPurchaseSubOrder shopPurchaseSubOrder : subOrderList) {
                if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(shopPurchaseSubOrder.getSupplierDataSource())) {
                    String contractNumber = shopPurchaseSubOrder.getContractNumber();
                    shopPurchaseSubOrder.setOrderSapNumber(shopPurchaseSubOrder.getOrderNumber());
                }
            }
        }
    }

    private String getVoyahSrmOrderSn(String contractNumber) {
        String key = contractNumber + "PO";
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isBlank(value)) {
            value = shopPurchaseOrderService.queryVoyahSrmOrderSn(key);
            if (StrUtil.isBlank(value)) {
                value = "0";
            } else {
                String lastFourChars = StrUtil.sub(value, value.length() - 4, value.length());
                value = Convert.toStr(NumberUtil.parseInt(lastFourChars));
            }
        }
        value = Convert.toStr(Convert.toInt(value) + 1);
        stringRedisTemplate.opsForValue().set(key, value);
        return key + String.format("%04d", Convert.toInt(value));
    }
    private void yflCustomizationOrder(ShopPurchaseOrder shopPurchaseOrder, List<ShopPurchaseSubOrder> shopPurchaseSubOrderList, List<ShopPurchaseSubOrderDetail> orderDetailList, PurchaseOrderSaveDto purchaseOrderSaveDto, Map<String, BigDecimal> poolMap, Boolean tag) {
        if (purchaseOrderSaveDto.getFreightType() == null) {
            throw new ParameterException("未添加运费类型，请联系管理员");
        }
        if (CollectionUtil.isEmpty(poolMap)) {
            throw new ParameterException("未找的商品，请联系管理员");
        }

        // 待分配的金额
        BigDecimal waitMoney = BigDecimal.ZERO;
        // 待分配的积分
        BigDecimal waitIntegral = BigDecimal.ZERO;
        for (ShopPurchaseSubOrder shopPurchaseSubOrder : shopPurchaseSubOrderList) {
            List<ShopPurchaseSubOrderDetail> subOrderDetailList = shopPurchaseSubOrder.getSubOrderDetailList();
            shopPurchaseSubOrder.setFreightType(purchaseOrderSaveDto.getFreightType());
            //子订单合计支付积分
            BigDecimal orderPayIntegral = BigDecimal.ZERO;
            //子订单合计支付现金
            BigDecimal orderPayMoney = BigDecimal.ZERO;
            for (ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail : subOrderDetailList) {
                //获取
                String goodsCode = shopPurchaseSubOrderDetail.getGoodsCode();
                BigDecimal integralCeiling = poolMap.get(goodsCode);
                if (integralCeiling == null) {
                    throw new ParameterException("无法查询到此商品[" + goodsCode + "]的积分价格，请联系管理员");
                }
                BigDecimal payIntegral = yflCustomizationPrice(shopPurchaseSubOrderDetail.getTaxRate(), shopPurchaseSubOrderDetail.getGoodsUnitPriceNaked(), integralCeiling, BigDecimal.valueOf(shopPurchaseSubOrderDetail.getConfirmNum()).add(shopPurchaseSubOrderDetail.getConfirmNumDecimal()));
                waitIntegral = waitIntegral.add(payIntegral);
                shopPurchaseSubOrderDetail.setGoodsPayIntegral(payIntegral);
                BigDecimal payMoney = shopPurchaseSubOrderDetail.getGoodsTotalPriceTax().subtract(payIntegral);
                shopPurchaseSubOrderDetail.setGoodsPayMoney(payMoney);
                shopPurchaseSubOrderDetail.setIntegralCeiling(integralCeiling);

                orderPayIntegral = orderPayIntegral.add(payIntegral);
                orderPayMoney = orderPayMoney.add(payMoney);
                orderDetailList.add(shopPurchaseSubOrderDetail);
            }
            shopPurchaseSubOrder.setOrderPayIntegral(orderPayIntegral);
            shopPurchaseSubOrder.setOrderPayMoney(orderPayMoney);
        }

        waitMoney = shopPurchaseOrder.getPurchaseTotalPrice().subtract(waitIntegral);

        if (!tag) {
            //联友等 取消现金支付限额
            BigDecimal limitMoney = yflYamlConfig.getPaymentLimit().get(shopPurchaseOrder.getCompanyCode()) == null
                    ? null : new BigDecimal(yflYamlConfig.getPaymentLimit().get(shopPurchaseOrder.getCompanyCode()).toString());
            if (null == limitMoney) {
                throw new ParameterException("无法查询到现金支付限额");
            }
            if (waitMoney.compareTo(limitMoney) > 0) {
                throw new ParameterException("需要支付的现金超过限额[" + limitMoney + "]");
            }
        }

        shopPurchaseOrder.setPurchasePayIntegral(waitIntegral);
        shopPurchaseOrder.setPurchasePayMoney(waitMoney);
    }

    /**
     * 获取南菱比例拆分 积分总计
     *
     * @param taxrate
     * @param goodsUnitPriceNaked
     * @param integralCeiling
     * @param confirmNum
     * @return
     */
    public BigDecimal yflCustomizationPrice(Integer taxrate, BigDecimal goodsUnitPriceNaked, BigDecimal integralCeiling, BigDecimal confirmNum) {
        BigDecimal taxRate = BigDecimal.valueOf(taxrate)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .add(BigDecimal.ONE);
        return goodsUnitPriceNaked.multiply(taxRate)
                .multiply(integralCeiling).setScale(4, RoundingMode.DOWN).multiply(confirmNum).setScale(2, RoundingMode.HALF_UP);
    }

    private void yflOrder(ShopPurchaseOrder shopPurchaseOrder, SystemIntegral systemIntegral, List<ShopPurchaseSubOrder> shopPurchaseSubOrderList, List<ShopPurchaseSubOrderDetail> orderDetailList, Integer freightType) {
        if (shopPurchaseOrder.getPurchaseTotalPrice().compareTo(systemIntegral.getIntegralUsableAmount()) > 0) {
            // 采购单总金额大于剩余可用积分
            // 优先使用积分去抵扣运费
            BigDecimal purchasePayIntegral = systemIntegral.getIntegralUsableAmount().subtract(shopPurchaseOrder.getPurchaseFreightPrice());
            shopPurchaseOrder.setPurchasePayIntegral(purchasePayIntegral);

            BigDecimal purchasePayMoney = shopPurchaseOrder.getPurchaseTotalPrice().subtract(systemIntegral.getIntegralUsableAmount());
            //联友等 取消现金支付限额
            shopPurchaseOrder.setPurchasePayMoney(purchasePayMoney);
        } else {
            shopPurchaseOrder.setPurchasePayIntegral(shopPurchaseOrder.getPurchaseTotalPrice().subtract(shopPurchaseOrder.getPurchaseFreightPrice()));
        }
        // 待分配的金额
        BigDecimal waitMoney = shopPurchaseOrder.getPurchasePayMoney();
        // 待分配的积分
        BigDecimal waitIntegral = shopPurchaseOrder.getPurchasePayIntegral();
        // 采购单拆分比例-- 采购单积分占整个采购单商品总金额的比例(运费不计入总金额)
        BigDecimal orderIntegralPercentage = waitIntegral.divide(shopPurchaseOrder.getPurchaseGoodsPrice(), 4, BigDecimal.ROUND_HALF_UP);
        for (int j = 1; j <= shopPurchaseSubOrderList.size(); j++) {
            ShopPurchaseSubOrder shopPurchaseSubOrder = shopPurchaseSubOrderList.get(j - 1);
            shopPurchaseSubOrder.setFreightType(freightType);
            //订单金额(不包含运费)
            BigDecimal orderPriceTax = shopPurchaseSubOrder.getOrderPriceTax();
            //订单分配积分额度
            BigDecimal orderIntergral = orderPriceTax.multiply(orderIntegralPercentage).setScale(2, RoundingMode.UP);
            List<ShopPurchaseSubOrderDetail> subOrderDetailList = shopPurchaseSubOrder.getSubOrderDetailList();
            if (j == shopPurchaseSubOrderList.size()) {
                //最后一次计算，直接将剩余待分配积分赋给订单应付积分
                orderIntergral = waitIntegral;
            } else {
                //每次都要计算待分配积分是否 >= 上面计算的商品应付积分，若是，则按照计算结果显示；若否，则该种商品支付积分=待分配积分；
                if (orderIntergral.compareTo(orderPriceTax) > 0 || waitMoney.compareTo(BigDecimal.ZERO) == 0) {
                    orderIntergral = orderPriceTax;
                }
                waitIntegral = waitIntegral.subtract(orderIntergral);
            }
            //子订单合计支付积分
            BigDecimal orderPayIntegral = new BigDecimal(0);
            //子订单合计支付现金
            BigDecimal orderPayMoney = new BigDecimal(0);
            // 订单明细维度-- 需要分摊的积分占整个采购单商品总金额的比例(运费不计入总金额)
            BigDecimal detailIntegralPercentage = orderIntergral.divide(orderPriceTax, 4, BigDecimal.ROUND_HALF_UP);
            for (int i = 1; i <= subOrderDetailList.size(); i++) {
                ShopPurchaseSubOrderDetail subOrderDetail = subOrderDetailList.get(i - 1);
                // 商品应付积分（根据比例计算），每次计算四舍五入时，直接向上进位（保证最后一次分配时，剩余待分配积分不会大于商品总金额）
                BigDecimal goodsPayIntegral = subOrderDetail.getGoodsTotalPriceTax().multiply(detailIntegralPercentage)
                        .setScale(2, RoundingMode.UP);

                if (i == subOrderDetailList.size()) {
                    //最后一次计算，直接将剩余待分配积分赋给商品应付积分
                    goodsPayIntegral = orderIntergral;
                } else {
                    //每次都要计算待分配积分是否 >= 上面计算的商品应付积分，若是，则按照计算结果显示；若否，则该种商品支付积分=待分配积分；
                    if (orderIntergral.compareTo(goodsPayIntegral) < 0) {
                        goodsPayIntegral = orderIntergral;
                    }
                    orderIntergral = orderIntergral.subtract(goodsPayIntegral);
                }
                // 商品应付现金 = 商品行小计 - 商品支付积分
                BigDecimal goodsPayMoney = subOrderDetail.getGoodsTotalPriceTax().subtract(goodsPayIntegral);

                subOrderDetail.setGoodsPayIntegral(goodsPayIntegral);
                subOrderDetail.setGoodsPayMoney(goodsPayMoney);

                orderPayIntegral = orderPayIntegral.add(goodsPayIntegral);
                orderPayMoney = orderPayMoney.add(goodsPayMoney);
                orderDetailList.add(subOrderDetail);
            }
            shopPurchaseSubOrder.setOrderPayIntegral(orderPayIntegral);
            shopPurchaseSubOrder.setOrderPayMoney(orderPayMoney);
        }
    }

    private void saveOrderDetailFloat(List<ShopPurchaseSubOrderDetail> orderDetailList, Map<String, ShopOrderDetailFloat> floatPriceGoodsMap) {
        Map<String, ShopPurchaseSubOrderDetail> orderDetailMap = CollStreamUtil.toIdentityMap(orderDetailList, ShopPurchaseSubOrderDetail::getGoodsCode);
        Collection<ShopOrderDetailFloat> OrderDetailFloatList = floatPriceGoodsMap.values();
        OrderDetailFloatList.forEach(i -> i.setOrderDetailId(orderDetailMap.get(i.getGoodsCode()).getOrderDetailId()));
        shopOrderDetailFloatMapper.insertBatch(OrderDetailFloatList);
    }

    /**
     * 校验浮动价格订单商品
     *
     * @param floatPriceGoodsDtoList 浮动价格商品集合
     * @param priceInfoRespMap       实时价
     * @return Map
     */
    private Map<String, ShopOrderDetailFloat> checkFloatPriceOrderGoods(List<FloatPriceGoodsDto> floatPriceGoodsDtoList,
                                                                        Map<String, RemotePriceInfoResp> priceInfoRespMap) {

        if (CollUtil.isEmpty(floatPriceGoodsDtoList)) {
            return new HashMap<>();
        }
        Map<String, ShopOrderDetailFloat> resultMap = Maps.newHashMapWithExpectedSize(floatPriceGoodsDtoList.size());

        Map<String, FloatPriceGoodsDto> floatOrderGoodsMap = CollStreamUtil.toIdentityMap(floatPriceGoodsDtoList, FloatPriceGoodsDto::getGoodsCode);
        Map<String, ShopSrmContractDetailEntity> floatContractGoodsMap = shopSrmContractDetailService.getFloatContractDetailMap(floatOrderGoodsMap.keySet());
        String template = "浮动价格商品【{}】没有确认含税价格";
        String template1 = "浮动价格商品【{}】没有确认未税价格";
        String template2 = "请联系管理员检查合同编号为【{}】，商品编码为【{}】的合同明细最【{}】价格浮动百分比是否为空";
        String template3 = "商品【{}】的价格最低可调至{}，最高可调至{}";
        BigDecimal hundred = new BigDecimal(100);
        floatContractGoodsMap.forEach((k, v) -> {
            String contractNumber = v.getContractNumber();
            // 校验是否有浮动比例异常的合同明细
            BigDecimal minPercent = Opt.ofNullable(v.getMinPercent()).orElseThrow(() -> new ParameterException(StrUtil.format(template2, contractNumber, k, "小")));
            minPercent = minPercent.divide(hundred, 4, RoundingMode.HALF_UP);

            BigDecimal maxPercent = Opt.ofNullable(v.getMaxPercent()).orElseThrow(() -> new ParameterException(StrUtil.format(template2, contractNumber, k, "大")));
            maxPercent = maxPercent.divide(hundred, 4, RoundingMode.HALF_UP);

            BigDecimal minRate = BigDecimal.ONE.subtract(minPercent);
            BigDecimal maxRate = BigDecimal.ONE.add(maxPercent);

            // 原价格
            RemotePriceInfoResp remotePriceInfoResp = priceInfoRespMap.get(k);
            BigDecimal goodsSalePrice = remotePriceInfoResp.getGoodsSalePrice();
            BigDecimal goodsNakedSalePrice = remotePriceInfoResp.getGoodsNakedSalePrice();

            // 用户调整后的价格
            BigDecimal confirmUnitPriceTax = Optional.ofNullable(floatOrderGoodsMap.get(k).getConfirmUnitPriceTax())
                    .orElseThrow(() -> new ParameterException(StrUtil.format(template, k)));

            BigDecimal confirmUnitPriceNaked = Optional.ofNullable(floatOrderGoodsMap.get(k).getConfirmUnitPriceNaked())
                    .orElseThrow(() -> new ParameterException(StrUtil.format(template1, k)));

            BigDecimal minTaxPrice = goodsSalePrice.multiply(minRate).setScale(4, RoundingMode.HALF_UP);
            BigDecimal maxTaxPrice = goodsSalePrice.multiply(maxRate).setScale(4, RoundingMode.HALF_UP);

            BigDecimal minNakedPrice = goodsNakedSalePrice.multiply(minRate).setScale(4, RoundingMode.HALF_UP);
            BigDecimal maxNakedPrice = goodsNakedSalePrice.multiply(maxRate).setScale(4, RoundingMode.HALF_UP);

            if (CompareUtil.compare(confirmUnitPriceTax, minTaxPrice) < 0 || CompareUtil.compare(confirmUnitPriceTax, maxTaxPrice) > 0) {
                throw new ParameterException(StrUtil.format(template3, k, minTaxPrice, maxTaxPrice));
            }

            ShopOrderDetailFloat detailFloat = new ShopOrderDetailFloat()
                    .setGoodsCode(k)
                    .setGoodsUnitPriceTax(goodsSalePrice)
                    .setGoodsUnitPriceNaked(goodsNakedSalePrice)
                    .setMinUnitPriceTax(minTaxPrice)
                    .setMaxUnitPriceTax(maxTaxPrice)
                    .setMinUnitPriceNaked(minNakedPrice)
                    .setMaxUnitPriceNaked(maxNakedPrice)
                    .setConfirmUnitPriceTax(confirmUnitPriceTax.setScale(4, RoundingMode.HALF_UP))
                    .setConfirmUnitPriceNaked(confirmUnitPriceNaked.setScale(4, RoundingMode.HALF_UP));

            resultMap.put(k, detailFloat);
        });

        return resultMap;
    }

    private void handleFloatPriceOrderGoods(Integer pricingMode, ShopPurchaseSubOrderDetail purchaseSubOrderDetail, Map<String, ShopOrderDetailFloat> floatPriceGoodsMap) {
        ShopOrderDetailFloat confirmPrice = floatPriceGoodsMap.get(purchaseSubOrderDetail.getGoodsCode());
        if (ObjectUtil.isNull(confirmPrice)) {
            return;
        }

        // 用户确定的未税单价
        BigDecimal nakedPrice = confirmPrice.getConfirmUnitPriceNaked();
        // 用户确定的含税单价
        BigDecimal taxPrice = confirmPrice.getConfirmUnitPriceTax();
        // 税率
        Integer taxRate = purchaseSubOrderDetail.getTaxRate();

        PriceUtilDto priceUtilDto = formulaUtil.pricingModeUtil(nakedPrice, taxPrice, nakedPrice, taxPrice, taxRate,
                purchaseSubOrderDetail.getApplyNum().intValue(), purchaseSubOrderDetail.getApplyNumDecimal(), pricingMode);
        FormulaPriceDto supplierPrice = priceUtilDto.getSupplierPrice();
        FormulaPriceDto goodsPrice = priceUtilDto.getGoodsPrice();

        // 协议价
        purchaseSubOrderDetail.setSupplierUnitPriceNaked(nakedPrice);
        purchaseSubOrderDetail.setSupplierUnitPriceTax(taxPrice);
        purchaseSubOrderDetail.setSupplierTotalPriceNaked(supplierPrice.getTotalPriceNaked());
        purchaseSubOrderDetail.setSupplierTotalPriceTax(supplierPrice.getTotalPriceTax());
        // 销售价
        purchaseSubOrderDetail.setGoodsUnitPriceNaked(nakedPrice);
        purchaseSubOrderDetail.setGoodsUnitPriceTax(taxPrice);
        purchaseSubOrderDetail.setGoodsTotalPriceNaked(goodsPrice.getTotalPriceNaked());
        purchaseSubOrderDetail.setGoodsTotalPriceTax(goodsPrice.getTotalPriceTax());
        purchaseSubOrderDetail.setGoodsTotalTax(goodsPrice.getTotalTax());

        // 电商原价
        purchaseSubOrderDetail.setSupplierUnitOriginalPriceTax(taxPrice);
        purchaseSubOrderDetail.setSupplierUnitOriginalPriceNaked(nakedPrice);
    }

    /**
     * 获取srm申请单号字段
     *
     * @param selectedSrmApplyId 被选中的srm采购单号
     * @param contractNumber     合同编号
     */
    private String getSubOrderSrmApplyId(String selectedSrmApplyId, String contractNumber) {
        if (StrUtil.isNotBlank(selectedSrmApplyId)) {
            // 优先选择被选中的srm采购单号
            return selectedSrmApplyId;
        } else if (StrUtil.isNotBlank(contractNumber)) {
            // 根据合同查询srm采购单号
            ShopSrmContractEntity srmContract = shopSrmContractService.getBaseMapper().getBySrmContractNumber(contractNumber);
            if (srmContract != null) {
                return srmContract.getSrmApplyId();
            }
        }
        return null;
    }

    /**
     * 商品校验
     * 此处返回的商品异常 DMS下单有使用 如果返回的格式变化了 请一定要一起改动一下 这个DMS侧需要换goodsCode->备件编号，校验商品的异常也要返给DMS
     *
     * @param regionDto 区域信息
     * @param address   详细地址
     * @return
     */
    public Map<String, String> goodsCheck(final Map<String, Object> goodsMap, final RegionDto regionDto, final String address, Consumer<Map<String, RemotePriceInfoResp>> priceCallBack) {
        var error_list = new HashMap<String, String>();
        String goodsCodes = CollectionUtil.join(goodsMap.keySet(), ",");
        var goods = shopGoodsService.getBaseMapper().selectGoodsSkuByGoodsCodeIn(goodsMap.keySet());
        // 将goods转换成sku到goodscode 的map
        Map<String, String> skuToGoodsCodeMap = goods.stream()
                .collect(Collectors.toMap(
                        shopGoods -> shopGoods.getSupplierCode() + shopGoods.getGoodsSku(), // 将supplierCode和sku拼接成key
                        ShopGoods::getGoodsCode // 作为value的值
                ));
        Map<String, String> goodsCodeToskuMap = goods.stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, ShopGoods::getGoodsSku));
        Map<Long, ShopGoods> goodsIdsMap = goods.stream().collect(Collectors.toMap(ShopGoods::getGoodsId, Function.identity()));
        // 对商品可售性多线程同时校验
        Long tId = TenantContextHolder.getRequiredTenantId();
        LoginUser loginUser = LocalUserHolder.get();
        final CountDownLatch signal = new CountDownLatch(6);
        commonIoExecutors.execute(() -> TenantUtils.execute(tId, loginUser, () -> {
            try {
                // 有福利租户校验商品上下架
                if (tId.equals(yflYamlConfig.getTenantId())) {
                    List<ShopGoods> downShopGoodsList = dfmallGoodsPoolShelvesDownService.getOffShelfProducts(
                            LocalUserHolder.get().getId(),
                            LocalUserHolder.get().getEntityOrganizationId(), Arrays.asList(goodsCodes.split(","))
                    );
                    downShopGoodsList.forEach(item -> error_list.putIfAbsent(item.getGoodsCode(), "商品[" + item.getGoodsSku() + "]已下架"));
                } else {
                    // srm的商品不校验可售性
                    List<Long> needCheckGoodsIds = filterNoCheckSaleGoods(goods);

                    if (CollectionUtil.isNotEmpty(needCheckGoodsIds)) {
                        List<GoodsSaleLabelVo> goodsSaleLabelVos = shopGoodsService.getGoodsSaleLabel(needCheckGoodsIds, loginUser.getEntityOrganizationCode());
                        goodsSaleLabelVos.forEach(item -> {
                            if (item.getIsSale() == 0) {
                                error_list.putIfAbsent(goodsIdsMap.get(item.getGoodsId()).getGoodsCode(), "商品[" + goodsIdsMap.get(item.getGoodsId()).getGoodsSku() + "]已下架");
                            }
                        });
                    }
                }
            } finally {
                signal.countDown();
            }
        }));

        commonIoExecutors.execute(() -> TenantUtils.execute(tId, loginUser, () -> {
            try {
                remoteInfoManage.getBatchGoodsStatus(goodsCodes).forEach(item -> {
                    if (!item.getIsShelves()) {
                        error_list.putIfAbsent(skuToGoodsCodeMap.get(item.getSupplierCode() + item.getSku()), "供应商商品[" + item.getSku() + "]已下架");
                    }
                });
            } finally {
                signal.countDown();
            }
        }));

        commonIoExecutors.execute(() -> TenantUtils.execute(tId, loginUser, () -> {
            try {
                remoteInfoManage.batchCheckCanSale(goodsCodes, regionDto, address).forEach(item -> {
                    if (!item.getIsCanSale()) {
                        error_list.putIfAbsent(skuToGoodsCodeMap.get(item.getSupplierCode() + item.getSku()), "商品[" + item.getSku() + "]不可购买");
                    }
                });
            } finally {
                signal.countDown();
            }
        }));

        commonIoExecutors.execute(() -> TenantUtils.execute(tId, loginUser, () -> {
            try {
                final List<RemoteAreaSaleLimitResp> areaSaleLimitRespList = remoteInfoManage.batchCheckAreaLimit(goodsCodes, regionDto, address);
                areaSaleLimitRespList.forEach(item -> {
                    if (item.getIsLimit()) {
                        error_list.putIfAbsent(skuToGoodsCodeMap.get(item.getSupplierCode() + item.getSku()), "商品[" + item.getSku() + "]当前收货区域不可购买");
                    }
                });
            } finally {
                signal.countDown();
            }
        }));

        commonIoExecutors.execute(() -> TenantUtils.execute(tId, loginUser, () -> {
            try {
                final List<RemoteStockInfoResp> stockInfoRespList = this.remoteInfoManage.getBatchGoodsStock(goodsMap, regionDto, address);
                stockInfoRespList.forEach(item -> {
                    if (item.getStockStatus().equals(RemoteStockInfoResp.StockStatusEnum.unSale)) {
                        error_list.putIfAbsent(skuToGoodsCodeMap.get(item.getSupplierCode() + item.getSkuId()), "商品[" + item.getSkuId() + "]已下架");
                    } else if (item.getStockStatus().equals(RemoteStockInfoResp.StockStatusEnum.noGoods)) {
                        error_list.putIfAbsent(skuToGoodsCodeMap.get(item.getSupplierCode() + item.getSkuId()), "商品[" + item.getSkuId() + "]已售罄");
                    }
                });
            } finally {
                signal.countDown();
            }
        }));

        final Map<String, RemotePriceInfoResp> remotePriceInfoMapPER = new HashMap<>();
        commonIoExecutors.execute(() -> TenantUtils.execute(tId, loginUser, () -> {
            try {
                final Map<String, RemotePriceInfoResp> remotePriceInfoMap = this.priceGetter.batchGetSalePrice(goodsMap.keySet());
                goodsMap.keySet().forEach(item -> {
                    val remote_info = remotePriceInfoMap.get(item);
                    if (remote_info == null || remote_info.getGoodsSalePrice() == null) {
                        error_list.putIfAbsent(item, "商品[" + goodsCodeToskuMap.get(item) + "]无售卖价格");
                        return;
                    }
                    if (remote_info.getGoodsSalePrice().compareTo(BigDecimal.ZERO) == 0) {
                        error_list.putIfAbsent(item, "商品[" + goodsCodeToskuMap.get(item) + "]价格为0");
                        return;
                    }
                    if (remote_info.getDiscountRange() == 1) {
                        error_list.putIfAbsent(item, "商品[" + goodsCodeToskuMap.get(item) + "]价格超出设置折扣范围");
                        return;
                    }
                    Integer priceMonitorResult = priceMonitorSrv.doPriceMonitor(item, loginUser.getEntityOrganizationCode(), remote_info.getGoodsPactPrice());
                    if (Arrays.asList(3, 4, 5, 6).contains(priceMonitorResult)) {
                        error_list.putIfAbsent(item, "商品[" + goodsCodeToskuMap.get(item) + "]因【价格异常波动】限售");
                    } else if (priceMonitorResult == 2) {
                        error_list.putIfAbsent(item, "商品[" + goodsCodeToskuMap.get(item) + "]因【超出询比定标价格协议】限售");
                    } else if (priceMonitorResult == 1) {
                        error_list.putIfAbsent(item, "商品[" + goodsCodeToskuMap.get(item) + "]因【超出企业重点监控商品价格协议】限售");
                    }
                });
                remotePriceInfoMapPER.putAll(remotePriceInfoMap);
            } finally {
                signal.countDown();
            }
        }));
        try {
            signal.await();
            // 执行回调逻辑
            if (priceCallBack != null) {
                priceCallBack.accept(remotePriceInfoMapPER);
            }
            // 等待所有校验完成
        } catch (Exception e) {
            log.error("供应商可售性异步校验失败", e);
            throw new RuntimeException("供应商可售性异步校验失败");
        }
        return error_list;
    }

    /**
     * 过滤以下类型商品
     * srm商品
     * 东本智能柜商品
     * @return 商品id
     */
    private List<Long> filterNoCheckSaleGoods(List<ShopGoods> goods) {
        List<String> supplierCodes = goods.stream().map(ShopGoods::getSupplierCode).distinct().collect(Collectors.toList());
        List<ShopSupplier> suppliers = shopSupplierService.getSupplierInfoByCodes(supplierCodes);
        Map<String, String> suppliersDataSource = suppliers.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, ShopSupplier::getDataSource));
        // 东风商城租户校验商品可授性
        List<Long> needCheckGoodsIds = new ArrayList<>();
        goods.forEach(item -> {
            if (item == null) {
                return;
            }
            boolean isNotSrm = !Objects.equals(suppliersDataSource.get(item.getSupplierCode()), "srm");
            boolean isNotDhecSmartGoods = !StrUtil.equals(item.getSupplierCode(), dhecSmartWarehouseConfig.getSupplierCode());
            if (isNotSrm && isNotDhecSmartGoods) {
                needCheckGoodsIds.add(item.getGoodsId());
            }
        });
        return needCheckGoodsIds;
    }

    /**
     * 提交供应商预订单
     */
    public void submitSupplierPreOrder(final String purchaseNumber, final Long deployId, final PurchaseOrderSaveDto param) {
        Integer srmAutoCreateOrder = param.getSrmAutoCreateOrder();
        Integer tradeUnionFlag = param.getTradeUnionFlag();
        PurchaseCustomLySaveDto customLySaveDto = param.getCustomLySaveDto();
        List<PreOrderInfo> preOrderInfoList = this.purchaseOrderMapper.querySupplierPreOrder(purchaseNumber);

        if(GoodsModelEnum.VIRTUAL.getModelCode().equals(preOrderInfoList.get(0).getOrderModel())){
            //虚拟商品没有预订单的环节，啥也不干，等钱付了，在干
            return;
        }

        //企配仓
        if (preOrderInfoList.get(0).getIsCompanyStore() == 1) {
            //
            preOrderInfoList = this.purchaseOrderMapper.queryCompanyStoreSupplierPreOrder(purchaseNumber);
        }
        for (final PreOrderInfo preOrderInfo : preOrderInfoList) {
            final List<OrderSkuList> orderSkuList = this.purchaseSubOrderDetailService.querySupplierOrderDetail(preOrderInfo.getYphOrderNo());

            // 处理采购单中的 具体商品条目
            preOrderInfo.setSkuList(orderSkuList);
        }
        try {
            // 远程预定单，走消息通道《鑫方盛》
            List<PreOrderInfo> openApiSuppler = preOrderInfoList.stream().filter(item -> openApiConfig.getSuppliers().contains(item.getSupplierCode())).collect(Collectors.toList());

            // 走普通调用流程的订单
            List<PreOrderInfo> commonSuppler = CollectionUtil.subtractToList(preOrderInfoList, openApiSuppler);

            // 需要发送消息到消息表，消息类型的订价格以商品保存时刻的价格为准
            // openApiSuppler 中的额订单，需要处理价格，后续具体供应商订单号通过API接口进行数据补全？

            // 走网关的供应商
            this.doApiSupplierConfirm(openApiSuppler);

            // 不走网关的供应商使用供应商接口来处理预订单
            this.doCommonSupplierConfirm(commonSuppler);

            QueryWrapper<ShopPurchaseOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
            ShopPurchaseOrder shopPurchaseOrder = this.getOne(queryWrapper);
            String companyCode = shopPurchaseOrder.getCompanyCode();
            //保存预订单信息至生命周期
            OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
            orderLifeCycleStrategy.savePurchaseOrderInfoForPreOrder(purchaseNumber);

            // 联友定制化: 根据【办公用品/工会】的选项判断审批过程是在srm系统还是商城，三种选项：否、是、其他
            // 选择【是】或者【其他】：走商城审批
            // 选择【否】且【有】srm申请单：商城自动审批通过,直接确认订单
            // 选择【否】且【无】srm申请单：走srm审批
            if (lyCompanyCodes.equals(companyCode) && ObjectUtil.notEqual(srmAutoCreateOrder, 1)) {
                if (ObjectUtil.equal(tradeUnionFlag, 0) && ObjectUtil.equal(customLySaveDto.getHadSrmPurchase(), 0)) {
                    //ApplyType 1、2、3、4 项目、投资、费用、框架类  联友定制化  费用，投资类不关联成本预估，需要发送预定单至srm
                    if (ObjectUtil.equal(customLySaveDto.getApplyType(), "2") || ObjectUtil.equal(customLySaveDto.getApplyType(), "3")) {
                        srmMaterialInfoService.sendPreOrderSrm(purchaseNumber, param);
                        log.info("联友定制化下单,采购单信息发送到srm【" + purchaseNumber + "】");
                    } else {
                        openApiMessageUtil.sendMessage(companyCode, MessageTypeEnum.CUSTOMER_PURCHASE_SUBMIT, MapUtil.of("purchaseNumber", purchaseNumber));
                        log.info("联友定制化下单,采购单发送到srm审批【" + purchaseNumber + "】");
                    }
                    return;
                }
                if (ObjectUtil.equal(tradeUnionFlag, 0) && ObjectUtil.equal(customLySaveDto.getHadSrmPurchase(), 1)) {
                    log.info("联友定制化下单，直接确认采购单【" + purchaseNumber + "】");
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            ShopPurchaseOrderService.this.shopPurchaseOrderService.confirmPurchaseOrder(purchaseNumber, "");
                        }
                    });
                    return;
                }
                log.info("联友定制化下单,采购单在商城审批【" + purchaseNumber + "】");
            }

            if (null == deployId) {
                //发动机预订单需特殊处理 目前发动机都是走外部审批
                if (param.getCompanyCode().equalsIgnoreCase(CompanyEnum.DHEC.getCompanyCode())) {
                    boolean isSmart = ObjectUtil.equal(param.getDhecSmartOrderFlag(), 1);
                    dhecService.sendPreOrderDhec(purchaseNumber, param, isSmart);
                    if (isSmart) {
                        // 东本智能柜订单，直接确认
                        autoHandleDhecSmartOrderAfterCommit(purchaseNumber);
                        return;
                    }
                }
                //查询采购单属于哪个公司
                Boolean isExt = sendExtPurchaseOrderInfo(companyCode, purchaseNumber, param.getDmsOrderFlag());
                if (isExt) {
                    log.info("外部系统审批,不直接确认采购单【" + purchaseNumber + "】");
                    return;
                }
            }

            // 商城判断是否直接确认订单
            boolean shopFlag = null == deployId && !TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId());
            // SRM自动创建订单直接确认订单
            boolean srmFlag = ObjectUtil.equal(srmAutoCreateOrder, 1);
            // DMS不用审批直接确认订单
            boolean dmsFlag = ObjectUtil.equal(param.getDmsOrderFlag(), 1);
            // 联友定制
            if (shopFlag || srmFlag || dmsFlag) {
                // 没有审批流程直接确认订单(排除友福利) SRM合同自动创建的订单无需审批流 岚图dms下单
                log.info("无审批流程图,直接确认采购单【" + purchaseNumber + "】");
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        ShopPurchaseOrderService.this.shopPurchaseOrderService.confirmPurchaseOrder(purchaseNumber, "");
                    }
                });
            }
        } catch (final DataRequestException data) {
            log.error("供应商创建订单失败：", data);
            // 提交供应商订单失败，回退授信余额
            authAmountInfoService.authAmountByReturn(purchaseNumber);
            String exceptionMessage = StrUtil.isBlank(data.getResult()) ?
                    (StrUtil.isBlank(data.getResponse()) ? data.getMessage() : data.getResponse()) : data.getResult();
            this.oldCancelOrder(purchaseNumber, exceptionMessage);
            throw new OrderException(exceptionMessage);
        } catch (final Exception e) {
            log.error("供应商创建订单失败Exception:", e);
            // 提交供应商订单失败，回退授信余额
            authAmountInfoService.authAmountByReturn(purchaseNumber);
            this.oldCancelOrder(purchaseNumber, "提交供应商订单失败");
            throw new OrderException("提交供应商订单失败");
        }
    }

    private void  oldCancelOrder(String purchaseNumber ,String exceptionMessage) {
        log.info("oldCancelOrder 取消订单 ------{}，{}，{}", purchaseNumber, exceptionMessage);
        final List<ShopPurchaseSubOrder> purchaseSubOrderList = this.purchaseSubOrderService.queryConfirmSubOrder(purchaseNumber);
        for (final ShopPurchaseSubOrder purchaseSubOrder : purchaseSubOrderList) {
            try {
                this.remoteInfoManage.cancelOrder(purchaseSubOrder.getOrderNumber(), purchaseSubOrder.getSupplierOrderNumber(), purchaseSubOrder.getSupplierCode());
            } catch (Exception ex) {
                log.error(purchaseSubOrder.getSupplierName() + "订单" + purchaseSubOrder.getSupplierOrderNumber() + "取消失败", ex);
            }
        }
    }

    private void autoHandleDhecSmartOrderAfterCommit(String purchaseNumber) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                log.info("东本智能柜订单，直接确认：【" + purchaseNumber + "】");
                // 自动确认订单
                shopPurchaseOrderService.confirmPurchaseOrder(purchaseNumber, "");

                ShopPurchaseOrder purchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(purchaseNumber);
                List<ShopPurchaseSubOrder> subOrders = purchaseSubOrderService.queryInfoByPurchaseNumber(purchaseNumber);
                ShopPurchaseSubOrder subOrder = subOrders.get(0);
                String orderId = subOrder.getOrderId();
                List<ShopPurchaseSubOrderDetail> orderDetails = purchaseSubOrderDetailService.getOrderDetailByOrderId(orderId);
                ShopPurchaseSubOrderDetail orderDetail = orderDetails.get(0);

                // 自动发货
                SupplierSaveDeliveryDto supplierSaveDeliveryDto = new SupplierSaveDeliveryDto();
                supplierSaveDeliveryDto.setOrderId(orderId);
                supplierSaveDeliveryDto.setDelivery(0);
                supplierSaveDeliveryDto.setPostman(purchaseOrder.getApplyUserName());
                supplierSaveDeliveryDto.setPostmanPhone(purchaseOrder.getApplyUserPhone());
                supplierSaveDeliveryDto.setDeliveryCode("");
                supplierSaveDeliveryDto.setDeliveryName("");
                supplierSaveDeliveryDto.setDeliveryTime("");
                List<SupplierSaveDeliveryGoodsDto> saveDeliveryGoodsDtoList = new ArrayList<>();
                SupplierSaveDeliveryGoodsDto supplierSaveDeliveryGoodsDto = new SupplierSaveDeliveryGoodsDto();
                supplierSaveDeliveryGoodsDto.setOrderDetailId(orderDetail.getOrderDetailId());
                supplierSaveDeliveryGoodsDto.setGoodsCode(orderDetail.getGoodsCode());
                supplierSaveDeliveryGoodsDto.setDeliveryNum(Math.toIntExact(orderDetail.getApplyNum()));
                supplierSaveDeliveryGoodsDto.setDeliveryNumDecimal(BigDecimal.ZERO);
                saveDeliveryGoodsDtoList.add(supplierSaveDeliveryGoodsDto);
                supplierSaveDeliveryDto.setSaveDeliveryGoodsDtoList(saveDeliveryGoodsDtoList);
                supplierOrderService.supplierSaveDelivery(supplierSaveDeliveryDto);

                String oldOrgCode = OrganizationCodeContextHolder.getOrganizationCode();
                try {
                    OrganizationCodeContextHolder.setOrganizationCode(dhecSmartWarehouseConfig.getSupplierCode());
                    List<ShopDelivery> deliveries = deliverySrv.queryByOrderNumber(subOrder.getOrderNumber());
                    ShopDelivery delivery = deliveries.get(0);
                    ConfirmDeliveryReq deliveryReq = new ConfirmDeliveryReq();
                    deliveryReq.setOrderNumber(subOrder.getOrderNumber());
                    deliveryReq.setPackageId(delivery.getPackageId());
                    deliveryReq.setState(1);
                    deliveryReq.setOrderFinishTime(new Date());
                    deliverySrv.confirmDelivery(deliveryReq);
                } finally {
                    if (oldOrgCode != null) {
                        OrganizationCodeContextHolder.setOrganizationCode(oldOrgCode);
                    } else {
                        OrganizationCodeContextHolder.clear();
                    }
                }
//
//                // 自动收货
//                List<ShopDelivery> deliveries = deliverySrv.queryByOrderNumber(subOrder.getOrderNumber());
//                deliverySrv.cusReceiptPackage(deliveries.get(0).getId(),null);
            }
        });
    }

    private void returnAuthAmountByPurchaseNumber(String purchaseNumber) {
        ShopPurchaseOrder shopPurchaseOrder = purchaseOrderMapper.selectOne(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
        if (shopPurchaseOrder != null) {
            externalInterfaceService.remoteDealAmount(shopPurchaseOrder, Collections.emptyList(), ExternalInterfaceService.AuthAmountType.CANCEL);
        }
    }

    private void doApiSupplierConfirm(List<PreOrderInfo> preOrderInfo) {
        if (preOrderInfo == null || preOrderInfo.size() == 0) {
            return;
        }
        preOrderInfo.forEach(item -> {
            // 岚图汽车 先调用他们接口认证能不能下单
            if (CollectionUtil.isNotEmpty(openApiConfig.getCustomizedSuppliersByOrder()) && openApiConfig.getCustomizedSuppliersByOrder().contains(item.getSupplierCode())) {
                this.remoteInfoManage.openApiPreOrder(item);
            }

            final ShopPurchaseSubOrder purchaseSubOrder = new ShopPurchaseSubOrder();
            // ---------------------------- 调用供应商接口更新订单数据 0--------------------------
            purchaseSubOrder.setSupplierOrderNumber("");

            for (final OrderSkuList orderSku : item.getSkuList()) {
                final ShopPurchaseSubOrderDetail purchaseSubOrderDetail = new ShopPurchaseSubOrderDetail();
                purchaseSubOrderDetail.setSupplierOrderNumber("");

                //_------------------------------------更新详情价格信息 0-------------------------
                purchaseSubOrderDetail.setSupplierUnitPriceTax(orderSku.getUnitPrice());
                purchaseSubOrderDetail.setSupplierUnitPriceNaked(orderSku.getUnitNakePrice());
                purchaseSubOrderDetail.setSupplierTotalPriceTax(orderSku.getUnitPrice().multiply(BigDecimal.valueOf(orderSku.getSkuCount())));
                purchaseSubOrderDetail.setSupplierTotalPriceNaked(orderSku.getUnitNakePrice().multiply(BigDecimal.valueOf(orderSku.getSkuCount())));
                purchaseSubOrderDetail.setConfirmNum(orderSku.getSkuCount().longValue());

                //-------------------------------------更新详情价格信息 1-------------------------
                final UpdateWrapper<ShopPurchaseSubOrderDetail> orderDetailUpdateWrapper = new UpdateWrapper<>();
                orderDetailUpdateWrapper.lambda().eq(ShopPurchaseSubOrderDetail::getOrderNumber, item.getYphOrderNo()).eq(ShopPurchaseSubOrderDetail::getGoodsSku, orderSku.getSkuId());
                this.purchaseSubOrderDetailService.update(purchaseSubOrderDetail, orderDetailUpdateWrapper);
            }
            purchaseSubOrder.setSupplierOrderPriceTax(item.getSupplierOrderPriceTax());
            purchaseSubOrder.setSupplierOrderPriceNaked(item.getSupplierOrderPriceNaked());
            purchaseSubOrder.setOrderState(10);
            //_---------------------------- 调用供应商接口更新订单数据 1--------------------------
            final UpdateWrapper<ShopPurchaseSubOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ShopPurchaseSubOrder::getOrderNumber, item.getYphOrderNo());
            this.purchaseSubOrderService.update(purchaseSubOrder, updateWrapper);

            // 先发个消息，通知兄弟们审核预订单
            this.openApiMessageUtil.sendMessage(item.getSupplierCode(), MessageTypeEnum.PRE_ORDER, MapUtil.of("orderNumber", item.getYphOrderNo()));
        });
    }


    /**
     * 不走网关的供应商
     * 使用之前的逻辑，调用供应商接口
     *
     * @param commonSuppler 通用供应商处理
     */
    private void doCommonSupplierConfirm(List<PreOrderInfo> commonSuppler) {
        if (commonSuppler == null || commonSuppler.size() == 0) {
            return;
        }
        final List<PreOrderResp> preOrderRespList = this.remoteInfoManage.preOrder(commonSuppler);
        preOrderRespList.forEach(preOrderResp -> {
            String supplierCode = preOrderResp.getSupplierCode();
            ShopSupplier shopSupplier = shopSupplierService.selectByCode(supplierCode);
            // 回写供应商订单与订单明细金额
            final ShopPurchaseSubOrder purchaseSubOrder = new ShopPurchaseSubOrder();
            //_---------------------------- 调用供应商接口更新订单数据 0--------------------------
            purchaseSubOrder.setSupplierOrderNumber(preOrderResp.getOrderNo());
            purchaseSubOrder.setSupplierOrderPriceTax(preOrderResp.getTotalPrice());
            purchaseSubOrder.setSupplierOrderPriceNaked(preOrderResp.getTotalNakePrice());
            purchaseSubOrder.setOrderState(10);
            //_---------------------------- 调用供应商接口更新订单数据 1--------------------------
            final UpdateWrapper<ShopPurchaseSubOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ShopPurchaseSubOrder::getOrderNumber, preOrderResp.getYphOrderNo());
            this.purchaseSubOrderService.update(purchaseSubOrder, updateWrapper);

            final List<PreOrderResp.PreOrderSkuList> preOrderSkuList = preOrderResp.getPreOrderSkuListList();
            if (CollectionUtil.isEmpty(preOrderSkuList)) {
                final UpdateWrapper<ShopPurchaseSubOrderDetail> orderDetailUpdateWrapper = new UpdateWrapper<>();
                orderDetailUpdateWrapper.lambda()
                        .eq(ShopPurchaseSubOrderDetail::getOrderNumber, preOrderResp.getYphOrderNo())
                        .set(ShopPurchaseSubOrderDetail::getSupplierOrderNumber, preOrderResp.getOrderNo());
                this.purchaseSubOrderDetailService.update(null, orderDetailUpdateWrapper);
            } else {
                for (final PreOrderResp.PreOrderSkuList preOrderSkuResp : preOrderSkuList) {
                    final ShopPurchaseSubOrderDetail purchaseSubOrderDetail = new ShopPurchaseSubOrderDetail();
                    purchaseSubOrderDetail.setSupplierOrderNumber(preOrderResp.getOrderNo());

                    //_------------------------------------更新详情价格信息 0-------------------------
                    purchaseSubOrderDetail.setSupplierUnitPriceTax(preOrderSkuResp.getPrice());
                    purchaseSubOrderDetail.setSupplierUnitPriceNaked(preOrderSkuResp.getNakePrice());

                    BigDecimal skuCount = preOrderSkuResp.getSkuCount() == 0 ? preOrderSkuResp.getSkuCountDecimal() : new BigDecimal(preOrderSkuResp.getSkuCount());

                    final BigDecimal supplierTotalPrice = preOrderSkuResp.getPrice()
                            .multiply(skuCount);
                    final BigDecimal supplierTotalNakedPrice = preOrderSkuResp.getNakePrice()
                            .multiply(skuCount);
                    purchaseSubOrderDetail.setSupplierTotalPriceTax(supplierTotalPrice);
                    purchaseSubOrderDetail.setSupplierTotalPriceNaked(supplierTotalNakedPrice);

                    purchaseSubOrderDetail.setConfirmNum(null == preOrderSkuResp.getSkuCount() ? 0 : preOrderSkuResp.getSkuCount().longValue());
                    purchaseSubOrderDetail.setConfirmNumDecimal(null == preOrderSkuResp.getSkuCountDecimal() ? new BigDecimal(0) : preOrderSkuResp.getSkuCountDecimal());
                    //_------------------------------------更新详情价格信息 1-------------------------

                    final UpdateWrapper<ShopPurchaseSubOrderDetail> orderDetailUpdateWrapper = new UpdateWrapper<>();
                    orderDetailUpdateWrapper.lambda().eq(ShopPurchaseSubOrderDetail::getOrderNumber, preOrderResp.getYphOrderNo())
                            .eq(ShopPurchaseSubOrderDetail::getGoodsSku, preOrderSkuResp.getSkuId());
                    this.purchaseSubOrderDetailService.update(purchaseSubOrderDetail, orderDetailUpdateWrapper);
                }
            }

        });
    }

    public Long getPurchasePayPrice(String orderNumber, Integer orderType, Long activityId, String appType) {
        SystemActivity systemActivity = systemActivityService.getById(activityId);
        PayOrderCreateReqDTO payOrderCreateReqDTO = new PayOrderCreateReqDTO();
        payOrderCreateReqDTO.setSubject("东风商城·友福利");
        payOrderCreateReqDTO.setBody(systemActivity.getActivityName() + "-混合支付");

        if(StrUtil.isNotBlank(appType) && "yfl_app".equals(appType)){
            payOrderCreateReqDTO.setAppId(5L);
        }else if(StrUtil.isNotBlank(appType) && "dfn_h5".equals(appType)){
            payOrderCreateReqDTO.setAppId(9L);
        }else {
            payOrderCreateReqDTO.setAppId(1L);
        }

        payOrderCreateReqDTO.setUserIp(getClientIP());
        Date nowDate = new Date();
        nowDate.setTime(nowDate.getTime() + 15 * 60 * 1000);
        payOrderCreateReqDTO.setExpireTime(nowDate);
        switch (orderType) {
            case 0:
                QueryWrapper<ShopPurchaseOrder> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().select(ShopPurchaseOrder::getPurchasePayMoney, ShopPurchaseOrder::getPurchaseNumber)
                        .eq(ShopPurchaseOrder::getPurchaseNumber, orderNumber);
                ShopPurchaseOrder shopPurchaseOrder = purchaseOrderMapper.selectOne(queryWrapper);
                payOrderCreateReqDTO.setMerchantOrderId(shopPurchaseOrder.getPurchaseNumber());
                BigDecimal purchasePayMoney = shopPurchaseOrder.getPurchasePayMoney();
                if (purchasePayMoney.compareTo(BigDecimal.ZERO) == 0) {
                    List<ShopPurchaseSubOrder> shopPurchaseSubOrders = purchaseSubOrderService.queryConfirmSubOrder(shopPurchaseOrder.getPurchaseNumber());
                    purchasePayMoney = shopPurchaseSubOrders.stream().map(ShopPurchaseSubOrder::getOrderPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (shopPurchaseSubOrders.get(0).getFreightType() == 1) {
                        BigDecimal orderFreightPriceTotal = shopPurchaseSubOrders.stream().map(ShopPurchaseSubOrder::getOrderFreightPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                        purchasePayMoney = purchasePayMoney.add(orderFreightPriceTotal);
                    }
                }
                BigDecimal purchaseAmount = purchasePayMoney.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                payOrderCreateReqDTO.setAmount(purchaseAmount.intValue());
                break;
            case 1:
                QueryWrapper<ShopPurchaseSubOrder> querySubOrder = new QueryWrapper<>();
                querySubOrder.lambda().select(ShopPurchaseSubOrder::getOrderNumber, ShopPurchaseSubOrder::getOrderPayMoney, ShopPurchaseSubOrder::getFreightType, ShopPurchaseSubOrder::getOrderFreightPrice)
                        .eq(ShopPurchaseSubOrder::getOrderNumber, orderNumber);
                ShopPurchaseSubOrder purchaseSubOrder = purchaseSubOrderService.getOne(querySubOrder);
                payOrderCreateReqDTO.setMerchantOrderId(purchaseSubOrder.getOrderNumber());
                BigDecimal orderAmount = (purchaseSubOrder.getOrderPayMoney().add(purchaseSubOrder.getFreightType() == 1 ? purchaseSubOrder.getOrderFreightPrice() : BigDecimal.ZERO)).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                payOrderCreateReqDTO.setAmount(orderAmount.intValue());
                break;
            case 3:
                OpenPayDO openPayDO = openPayMapper.selectByOpenPayNumber(orderNumber);
                // 开发支付时，支付截至时间以外部支付订单的支付截至时间为准
                payOrderCreateReqDTO.setExpireTime(openPayDO.getPaymentDeadline());
                payOrderCreateReqDTO.setMerchantOrderId(orderNumber);
                payOrderCreateReqDTO.setAmount(openPayDO.getPayCash());
                break;
        }
        //创建支付订单
        Long payId = null;
        if (payOrderCreateReqDTO.getAmount() > 0) {
            payId = payOrderService.createPayOrder(payOrderCreateReqDTO);
        }
        return payId;
    }

    /**
     * 获取各供应商运费
     *
     * @param purchaseDeliveryPriceDto
     * @return
     */
    public PurchaseDeliveryPriceVo getPurchaseDeliveryPrice(final PurchaseDeliveryPriceDto purchaseDeliveryPriceDto) {
        final PurchaseDeliveryPriceVo purchaseDeliveryPriceVo = new PurchaseDeliveryPriceVo();
        // todo 商店地址
        final ShopAddress shopAddress = this.shopAddressService.getById(purchaseDeliveryPriceDto.getAddressId());
        // 采购订单总运费
        BigDecimal purchaseFreightPrice = new BigDecimal(0);
        final Map<String, List<PurchaseDeliveryPriceDto.GoodsInfo>> goodsInfoMap = purchaseDeliveryPriceDto.getSkuInfoList().stream().collect(Collectors.groupingBy(PurchaseDeliveryPriceDto.GoodsInfo::getSupplierCode, Collectors.toList()));

        final List<PurchaseDeliveryPriceVo.SupplierFreightPrice> freightPriceList = new ArrayList<>();
        for (final Map.Entry<String, List<PurchaseDeliveryPriceDto.GoodsInfo>> goodsInfo : goodsInfoMap.entrySet()) {
            final List<GoodCodeInfoListParams> goodCodeInfoList = new ArrayList<>();
            final PurchaseDeliveryPriceVo.SupplierFreightPrice supplierFreightPrice = new PurchaseDeliveryPriceVo.SupplierFreightPrice();
            goodsInfo.getValue().forEach(goods -> {
                final GoodCodeInfoListParams goodCodeInfoListParams = new GoodCodeInfoListParams(goods.getCount(), goods.getGoodsCode());
                supplierFreightPrice.setSupplierName(goods.getSupplierName());
                goodCodeInfoList.add(goodCodeInfoListParams);
            });
            final RegionDto regionDto = new RegionDto();
            BeanUtil.copyProperties(shopAddress, regionDto);
            regionDto.setCounty(shopAddress.getDistrict());
            //获取每个供应商的运费
            final DeliveryPriceResp deliveryPriceResp = this.remoteInfoManage.getDeliveryPrice(goodCodeInfoList, regionDto, shopAddress.getAddress());

            supplierFreightPrice.setSupplierCode(goodsInfo.getKey());
            supplierFreightPrice.setFreightPrice(deliveryPriceResp.getTotalPrice());
            freightPriceList.add(supplierFreightPrice);

            purchaseFreightPrice = purchaseFreightPrice.add(deliveryPriceResp.getTotalPrice());
        }
        purchaseDeliveryPriceVo.setFreightPriceList(freightPriceList);
        purchaseDeliveryPriceVo.setPurchaseFreightPrice(purchaseFreightPrice);
        return purchaseDeliveryPriceVo;
    }

    /**
     * 订单支付完成回调
     *
     * @param orderNumber 订单号或者采购单号
     */
    @InvokeLogRecord
    public void orderPayCallBack(String orderNumber) {
        log.info("[orderPayCallBack][支付订单({}) 调用确认订单回调接口]", orderNumber);
        ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(orderNumber);
        if (null != shopPurchaseOrder) {
            this.confirmPurchaseOrder(orderNumber, "");
        } else {
            this.confirmSubOrder(orderNumber);
        }
    }

    /**
     * 是否需要验收
     * 1.企业档案开启验收功能
     *
     * @param supplierTypeArray 供应商类别集合,用逗号隔开
     * @return boolean
     */
    public boolean isNeedCheck(String supplierTypeArray, Integer isInvest) {
        LoginUser loginUser = LocalUserHolder.get();
        // 1.企业档案开启验收功能
        SystemOrganizationPurchaseContractEntity company = companyService.getPurchaseByOrgId(loginUser.getEntityOrganizationId());
        Set<Integer> supplierTypeSet = Arrays.stream(supplierTypeArray.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toSet());
        for (Integer supplierType : company.getCheckScope()) {
            if (supplierTypeSet.contains(supplierType)) {
                return true;
            }
        }

        //岚图投资类，根据预算类型判断是会否开启验收
        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(loginUser.getEntityOrganizationCode()) && isInvest == 1) {
            return true;
        }

        return false;
    }

    /**
     * 确认订单
     *
     * @param purchaseNumber  采购单号
     * @param approvalOpinion 审批意见
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult confirmPurchaseOrder(final String purchaseNumber, String approvalOpinion) {
        log.info("confirmPurchaseOrder begin:{}", purchaseNumber);
        PayOrderDO payOrder = payOrderService.getOrder(purchaseNumber);
        if (null != payOrder && !PayOrderStatusEnum.SUCCESS.getStatus().equals(payOrder.getStatus())) {
            throw new OrderException("订单未支付");
        }

        final ShopPurchaseOrderVo purchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseNumber);
        if(purchaseOrderVo.getPurchaseState()==20){
            return ServiceResult.error("采购单已被取消");
        }

        final List<ShopPurchaseSubOrder> purchaseSubOrderList = this.purchaseSubOrderService.queryConfirmSubOrder(purchaseNumber);
        if(yflYamlConfig.getTenantId().equals(purchaseOrderVo.getTenantId())){
           List<ShopPurchaseSubOrder> confirmddSubOrderList = purchaseSubOrderList.stream().filter(item->item.getOrderState()==30).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(confirmddSubOrderList)){
                return ServiceResult.succ();
            }
        }
        final ShopPurchaseOrder shopPurchaseOrder = new ShopPurchaseOrder();
        shopPurchaseOrder.setPurchaseState(30);
        shopPurchaseOrder.setPurchaseNumber(purchaseNumber);
        shopPurchaseOrder.setAuditTime(new Date());
        shopPurchaseOrder.setApprovalOpinion(approvalOpinion);
        BigDecimal failAmount = new BigDecimal(0);

        int isDfsDoaPay = 0;

        for (final ShopPurchaseSubOrder purchaseSubOrder : purchaseSubOrderList) {
            if (purchaseSubOrder.getOrderState() != null && purchaseSubOrder.getOrderState() >= 20) {
                // Confirmed orders cannot be confirmed again
                continue;
            }

            //查询包含除SRM以外供应商的采购单
            String dsPurchaseNumber = this.purchaseSubOrderService.queryDsPurchaseNumber(purchaseNumber, dfsYamlConfig.getSrmDataSource());
            if (dfsYamlConfig.getCompanycode().contains(purchaseOrderVo.getCompanyCode())
                    && StrUtil.isNotBlank(dsPurchaseNumber)) {
                //查询电商订单DFS预付款成功状态
                DfsDoaPayEntity dfsDoaPayEntity = dfsDoaPayService.queryPayByPurchaseNumber(dsPurchaseNumber);
                if (null == dfsDoaPayEntity) {
                    //订单未支付，无法确认
                    break;
                }
                // 已经支付的，无需同步DOA
                isDfsDoaPay = 1;
            }

            // send message to supplier, notice them to deliver goods
            if (openApiConfig.getSuppliers().contains(purchaseSubOrder.getSupplierCode())) {
                openApiMessageUtil.sendMessage(purchaseSubOrder.getSupplierCode(),
                        MessageTypeEnum.ORDER,
                        MapUtil.of("orderNumber", purchaseSubOrder.getOrderNumber())
                );
            }

            try {
                boolean confirm = this.remoteInfoManage.confirmOrder(purchaseSubOrder.getOrderNumber(),
                        purchaseSubOrder.getSupplierOrderNumber(),
                        purchaseSubOrder.getSupplierCode());
                if (confirm) {
                    Integer orderModel = purchaseSubOrder.getOrderModel();
                    ShopPurchaseOrder purchase = this.queryPurOrderByPurNumber(purchaseSubOrder.getPurchaseNumber());
                    if (lyCompanyCodes.equals(purchase.getCompanyCode())) {
                        // [联友定制]不与下方南方定制合并判断,这里后续可删
                        if (orderModel.equals(GoodsModelEnum.VIRTUAL.getModelCode()) || orderModel.equals(GoodsModelEnum.SERVICE.getModelCode())) {
                            purchaseSubOrder.setOrderState(30);
                            // 同步数据到包裹待收货状态
                            this.syncToReceive(purchaseSubOrder);
                        } else {
                            // 其他:都要发货收货
                            purchaseSubOrder.setOrderState(20);
                            ShopSupplier supplier = shopSupplierService.selectByCode(purchaseSubOrder.getSupplierCode());
                            //联友发邮件有单独的逻辑，这里排除联友
                            if (supplier.getCanReceiveEmail() == 1 && !lyCompanyCodes.equals(purchase.getCompanyCode())) {
                                // 供应商发送订单邮件提醒，针对有发货数据的商品。
                                Long tenantId = TenantContextHolder.getRequiredTenantId();
                                commonIoExecutors.execute(() -> TenantUtils.execute(tenantId, () -> {
                                    try {
                                        this.emailSupplier(supplier, purchaseSubOrder, purchaseOrderVo.getCompanyName());
                                    } catch (Exception e) {
                                        log.error("订单邮件提醒异常", e);
                                    }
                                }));
                            }
                        }
                    } else if ("srm".equals(purchaseSubOrder.getSupplierDataSource())) {
                        // [南方定制化]
                        ShopSrmContractEntity contract = shopSrmContractService.getBySrmContractNumber(purchaseSubOrder.getContractNumber());
                        if (orderModel.equals(GoodsModelEnum.VIRTUAL.getModelCode()) || orderModel.equals(GoodsModelEnum.SERVICE.getModelCode())) {
                            // 1.虚拟不发货收货
                            purchaseSubOrder.setOrderState(40);
                            this.syncToCheck(purchaseSubOrder);
                        } else if (contract != null && contract.getPurchaseType() == 1 && customizeDfsProperty.getMarketCompanyCode().contains(purchase.getCompanyCode())) {
                            // 2.单次采购且营销单位,无需发/收货
                            purchaseSubOrder.setOrderState(40);
                            // 同步数据到包裹验收状态
                            this.syncToCheck(purchaseSubOrder);
                        } else if (purchase.getDfsNeedDelivery() == 0) {
                            // 3.用户选择无需发/收货
                            purchaseSubOrder.setOrderState(40);
                            // 同步数据到包裹验收状态
                            this.syncToCheck(purchaseSubOrder);
                        } else {
                            // 其他:都要发货收货
                            purchaseSubOrder.setOrderState(20);
                            ShopSupplier supplier = shopSupplierService.selectByCode(purchaseSubOrder.getSupplierCode());
                            //联友发邮件有单独的逻辑，这里排除联友
                            if (supplier.getCanReceiveEmail() == 1 && !lyCompanyCodes.equals(purchase.getCompanyCode())) {
                                // 供应商发送订单邮件提醒，针对有发货数据的商品。
                                Long tenantId = TenantContextHolder.getRequiredTenantId();
                                commonIoExecutors.execute(() -> TenantUtils.execute(tenantId, () -> {
                                    try {
                                        this.emailSupplier(supplier, purchaseSubOrder, purchaseOrderVo.getCompanyName());
                                    } catch (Exception e) {
                                        log.error("订单邮件提醒异常", e);
                                    }
                                }));
                            }
                        }
                    } else {
                        // 其他:都要发货收货
                        purchaseSubOrder.setOrderState(20);
                        ShopSupplier supplier = shopSupplierService.selectByCode(purchaseSubOrder.getSupplierCode());
                        //联友发邮件有单独的逻辑，这里排除联友
                        if (supplier.getCanReceiveEmail() == 1 && !lyCompanyCodes.equals(purchase.getCompanyCode())) {
                            // 供应商发送订单邮件提醒，针对有发货数据的商品。
                            Long tenantId = TenantContextHolder.getRequiredTenantId();
                            commonIoExecutors.execute(() -> TenantUtils.execute(tenantId, () -> {
                                try {
                                    this.emailSupplier(supplier, purchaseSubOrder, purchaseOrderVo.getCompanyName());
                                } catch (Exception e) {
                                    log.error("订单邮件提醒异常", e);
                                }
                            }));
                        }
                    }

                    //联友发邮件的逻辑
                    if (lyCompanyCodes.equals(purchase.getCompanyCode()) && !StringHelper.IsEmptyOrNull(purchaseSubOrder.getContractNumber())) {
                        ShopSupplier supplier = shopSupplierService.selectByCode(purchaseSubOrder.getSupplierCode());
                        ShopSrmContractEntity srmContract = shopSrmContractService.getBySrmContractNumber(purchaseSubOrder.getContractNumber());
                        //联友持续采购的合同都要发邮件，单次采购不发邮件
                        if (supplier.getCanReceiveEmail() == 1
                                && srmContract.getPurchaseType() == 2) {
                            Long tenantId = TenantContextHolder.getRequiredTenantId();
                            commonIoExecutors.execute(() -> TenantUtils.execute(tenantId, () -> {
                                try {
                                    this.emailSupplier(supplier, purchaseSubOrder, purchaseOrderVo.getCompanyName());
                                } catch (Exception e) {
                                    log.error("订单邮件提醒异常", e);
                                }
                            }));
                        }

                    }

                    purchaseSubOrder.setFailReason("确认订单成功");
                } else {
                    failAmount = failAmount.add(purchaseSubOrder.getOrderPriceTax());
                    // 向供应商确认订单失败
                    purchaseSubOrder.setOrderState(0);
                    purchaseSubOrder.setFailReason("供应商确认订单未通过");
                    // 回退授信余额
                    authAmountInfoService.authAmountByConfirmOrder(purchaseSubOrder, AuthAmountType.CANCEL.getType());
                }
            } catch (final DataRequestException data) {
                failAmount = failAmount.add(purchaseSubOrder.getOrderPriceTax());

                // 回退授信余额
                authAmountInfoService.authAmountByConfirmOrder(purchaseSubOrder, AuthAmountType.CANCEL.getType());
                purchaseSubOrder.setOrderState(-1);

                if (!StringHelper.IsEmptyOrNull(data.getResult())) {
                    purchaseSubOrder.setFailReason(data.getResult());
                    log.error(data.getResult());
                } else {
                    purchaseSubOrder.setFailReason("供应商确认订单失败");
                    log.error(purchaseSubOrder.getSupplierName() + "订单【" + purchaseSubOrder.getSupplierOrderNumber() + "】确认失败");
                    if (!StringHelper.IsEmptyOrNull(data.getResponse())) {
                        log.error(data.getResponse());
                    }
                }
            } catch (Exception ex) {
                log.error("【confirmPurchaseOrder】确定订单Exception异常", ex);
                failAmount = failAmount.add(purchaseSubOrder.getOrderPriceTax());
                // 回退授信余额
                authAmountInfoService.authAmountByConfirmOrder(purchaseSubOrder, AuthAmountType.CANCEL.getType());
                purchaseSubOrder.setOrderState(-1);

                purchaseSubOrder.setFailReason("供应商确认订单失败");
                log.error("商城订单号：" + purchaseSubOrder.getOrderNumber() + "电商：" + purchaseSubOrder.getSupplierName() + "订单【" + purchaseSubOrder.getSupplierOrderNumber() + "】确认失败");
            }
            this.updateSubOrderState(purchaseSubOrder);

            if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                //福利确认订单入账单池
                settleBillPoolYflCustomerService.yflOrderToPool(purchaseSubOrder.getOrderNumber());
                //发送通知短信
                this.sendNoteMsg(purchaseSubOrder, purchaseOrderVo.getApplyUserPhone(), Long.valueOf(purchaseOrderVo.getApplyUserId()));
                //发送订阅信息
                try {
                    if (purchaseSubOrder.getOrderState() == 20) {
                        String applyUserId = purchaseOrderVo.getApplyUserId();
                        SystemUsers user = systemUsersService.getUser(Long.valueOf(applyUserId));
                        if (user != null) {
                            SystemIntegral systemIntegral = systemIntegralService.getByActivityIdUserId(purchaseOrderVo.getActivityId(), Long.valueOf(applyUserId));
                            SystemActivity systemActivity = systemActivityService.getById(purchaseOrderVo.getActivityId());
                            if(!miniAppNotifyService.getNotNoticeActivityCode().contains(systemActivity.getActivityCode())){
                                miniAppNotifyService.sendIntegralRedemptionNotice(applyUserId, user.getNotifyInfo(), purchaseSubOrder.getOrderNumber(), purchaseOrderVo.getApplyUserName(), purchaseOrderVo.getPurchasePayIntegral().setScale(2, BigDecimal.ROUND_HALF_UP), purchaseOrderVo.getCreateTime(), systemIntegral.getIntegralUsableAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                            }else{
                                log.info("下发微信通知【{}】--【{}】过滤不发消息----sendIntegralRedemptionNotice", purchaseNumber, systemActivity.getActivityCode());

                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("发送订阅消息异常", e);
                }
            }

            // 发送短信
            this.sendMsg(purchaseSubOrder, purchaseNumber);

        }

        //设置不出账公司 排除友福利
        if (!TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            List<SystemDictDataEntity> dictDataList = dictDataSrv.getDictDatasByDictType("no_bill_company");
            log.info("confirmPurchaseOrderDebugList====> purchaseOrderVo:{} ", purchaseOrderVo);
            dictDataList.removeIf(item -> item.getStatus() == 0);
            if (CollectionUtil.isNotEmpty(dictDataList)) {
                List<SystemDictDataEntity> filterCompany = dictDataList.stream().filter(item -> item.getValue().equals(purchaseOrderVo.getCompanyCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(filterCompany)) {
                    shopPurchaseOrder.setSapOrderType(Constant.SAP_NO_BILL);
                }
            }
            //定向服务商的采购单 在此处直接出供应商账单 现阶段只在东风商城存在这样的供应商
            settleBillPoolService.savePlatformOutReconciliation(purchaseNumber);
        }


        final UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
        this.update(shopPurchaseOrder, updateWrapper);


        if (failAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 失败的订单金额，预算释放
            this.budgetUpdate(purchaseOrderVo, failAmount, 2);
        }
        //兼容外部
        log.info("purchaseOrderVoContent:" + purchaseOrderVo);
        extInventoryService.sendConfirmOrderToExt(purchaseOrderVo.getCompanyCode(), purchaseNumber);

        // 确认订单-更新生命周期[上一步发送外部 更新了sap_order_type]
        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
        UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto = new UpdatePurchaseOrderInfoForConfirmDto();
        updatePurchaseOrderInfoForConfirmDto.setPurchaseNumber(purchaseNumber);
        orderLifeCycleStrategy.updatePurchaseOrderInfoForApprove(updatePurchaseOrderInfoForConfirmDto);

        //非友福利，保存采购单同步doa系统消息
        if (!TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId()) && isDfsDoaPay == 0) {
            openApiMessageUtil.sendMessage(purchaseOrderVo.getCompanyCode(), MessageTypeEnum.CUSTOMER_PURCHASE_CONFIRM, MapUtil.of("purchaseNumber", purchaseNumber));
        }

        PurchaseCustomLy purchaseCustomLy = purchaseCustomLyMapper.getOneByPurchaseNumber(purchaseOrderVo.getPurchaseNumber());
        try {
            if (lyCompanyCodes.equals(purchaseOrderVo.getCompanyCode()) && Objects.equals(purchaseCustomLy.getHadSrmPurchase(), 1)) {
                // srm确认扣除金额
                srmMaterialInfoSrv.srmUsePrice(SrmUsePriceDto.builder().dfsMallPurchaseApplyNo(shopPurchaseOrder.getPurchaseNumber()).type("2").build());
                // srm回传电商订单金额
                srmMaterialInfoSrv.srmEmallOrder(shopPurchaseOrder.getPurchaseNumber());
            }
        } catch (Exception ex) {
            log.error("srmUsePrice error:{};type:{}", shopPurchaseOrder.getPurchaseNumber(), "2");
            log.error(ex);
        }

        return ServiceResult.succ();
    }

    /**
     * 供应商发送订单邮件提醒
     *
     * @param shopSupplier     供应商信息
     * @param purchaseSubOrder 采购单信息
     */
    private void emailSupplier(ShopSupplier shopSupplier, ShopPurchaseSubOrder purchaseSubOrder, String companyName) {
        List<SystemUsers> userList = systemUsersService.getUsersByOrganizationIds(Collections.singleton(shopSupplier.getOrganizationId()));
        if (CollectionUtil.isEmpty(userList)) {
            log.error("供应商联系人为空");
            return;
        }
        String[] emailList = userList.stream().filter(x -> StringUtils.isNotBlank(x.getEmail())).
                sorted(Comparator.comparing(SystemUsers::getCreateTime, Comparator.naturalOrder())).
                map(SystemUsers::getEmail).distinct().toArray(String[]::new);
        if (emailList.length == 0) {
            log.error("供应商联系人未配置邮箱");
            return;
        }
        String createTime = new SimpleDateFormat(DatePattern.NORM_DATE_PATTERN).format(purchaseSubOrder.getCreateTime());
        String template = mailService.supplierOrderTemplate(purchaseSubOrder.getSupplierOrderNumber(),
                purchaseSubOrder.getSupplierOrderPriceTax(),
                purchaseSubOrder.getSupplierOrderPriceNaked(),
                companyName,
                createTime);
        if ("srm".equals(purchaseSubOrder.getSupplierDataSource())) {
            template = mailService.supplierOrderTemplateBySrm(purchaseSubOrder.getSupplierOrderNumber(),
                    purchaseSubOrder.getSupplierOrderPriceTax(),
                    purchaseSubOrder.getSupplierOrderPriceNaked(),
                    companyName,
                    createTime);
        }
        //没有人员就不发，人员多个取创建时间第一个。
        mailService.sendEmail("东风商城订单提醒", template, emailList[0]);
        emailSendLogService.createEmailSendLog("东风商城订单提醒", template, emailList[0], true);
    }

    /**
     * 无需发货和收货的订单,需要验收,直接到待验收状态
     *
     * @param order 订单
     */
    private void syncToReceive(ShopPurchaseSubOrder order) {
        // 保存验收单
        Date now = new Date();
        ShopDelivery deliveryEty = DeliveryConvert.INSTANCE.convert(order);
        deliveryEty.setDeliveryTime(now);
        // -99-无需发货
        deliveryEty.setSupDeliveryState(DeliveryStateEnum.DELIVERY_IGNORE.getCode());
        deliveryEty.setDeliveryTime(now);
        // 0-待收货
        deliveryEty.setCusReceivingState(ReceiveStateEnum.RECEIVE_WAIT.getCode());
        // 0-待验收
        deliveryEty.setCheckState(CheckStateEnum.CHECK_WAIT.getCode());
        ShopPurchaseOrder purchase = this.queryPurOrderByPurNumber(order.getPurchaseNumber());
        deliveryEty.setAccepterId(purchase.getAccepterId());
        deliveryEty.setAccepterName(purchase.getAccepterName());
        deliverySrv.save(deliveryEty);

        // 保存验收单详情
        List<ShopPurchaseSubOrderDetail> orderDetails = purchaseSubOrderDetailService.getOrderDetailByOrderId(order.getOrderId());
        List<ShopDeliveryDetail> saveDeliveryDetailList = new ArrayList<>(orderDetails.size());
        orderDetails.forEach(detail -> {
            ShopDeliveryDetail deliveryDetail = DeliveryDetailConvert.INSTANCE.convert(detail);
            deliveryDetail.setDeliveryId(deliveryEty.getId());
            deliveryDetail.setGoodsModel(order.getOrderModel());
            Integer confirmNum = detail.getConfirmNum().intValue();
            deliveryDetail.setDeliveryNum(confirmNum);
            deliveryDetail.setDeliveryNumDecimal(detail.getConfirmNumDecimal());
            // 0-待验收
            deliveryDetail.setCheckDetailState(CheckDetailStateEnum.CHECK_DETAIL_WAIT.getCode());
            saveDeliveryDetailList.add(deliveryDetail);
        });
        deliveryDetailSrv.saveBatch(saveDeliveryDetailList);
        deliverySrv.buildDeliveryBusMes(Collections.singleton(deliveryEty.getId()));
        try {
            //ilink添加待收货提醒
            ilinkSrv.sendDeliveryMsg(Collections.singletonList(deliveryEty));
        } catch (Exception e) {
            log.error("【ilink】发送待收货消息失败 deliverId {},purchaseNumber {}", deliveryEty.getId(), purchase.getPurchaseNumber());
        }
    }

    /**
     * 无需发货和收货的订单,需要验收,直接到待验收状态
     *
     * @param order 订单
     */
    private void syncToCheck(ShopPurchaseSubOrder order) {
        // 保存验收单
        Date now = new Date();
        ShopDelivery deliveryEty = DeliveryConvert.INSTANCE.convert(order);
        deliveryEty.setDeliveryTime(now);
        // -99-无需发货
        deliveryEty.setSupDeliveryState(DeliveryStateEnum.DELIVERY_IGNORE.getCode());
        deliveryEty.setDeliveryTime(now);
        // -99-无需收货
        deliveryEty.setCusReceivingState(ReceiveStateEnum.RECEIVE_IGNORE.getCode());
        deliveryEty.setCusReceivingTime(now);
        // 0-待验收
        deliveryEty.setCheckState(CheckStateEnum.CHECK_WAIT.getCode());
        ShopPurchaseOrder purchase = this.queryPurOrderByPurNumber(order.getPurchaseNumber());
        deliveryEty.setAccepterId(purchase.getAccepterId());
        deliveryEty.setAccepterName(purchase.getAccepterName());
        deliverySrv.save(deliveryEty);

        // 保存验收单详情
        List<ShopPurchaseSubOrderDetail> orderDetails = purchaseSubOrderDetailService.getOrderDetailByOrderId(order.getOrderId());
        List<ShopDeliveryDetail> saveDeliveryDetailList = new ArrayList<>(orderDetails.size());
        orderDetails.forEach(detail -> {
            ShopDeliveryDetail deliveryDetail = DeliveryDetailConvert.INSTANCE.convert(detail);
            deliveryDetail.setDeliveryId(deliveryEty.getId());
            deliveryDetail.setGoodsModel(order.getOrderModel());
            Integer confirmNum = detail.getConfirmNum().intValue();
            deliveryDetail.setDeliveryNum(confirmNum);
            deliveryDetail.setReceivingNum(confirmNum);
            deliveryDetail.setDeliveryNumDecimal(detail.getConfirmNumDecimal());
            deliveryDetail.setReceivingNumDecimal(detail.getConfirmNumDecimal());
            // 0-待验收
            deliveryDetail.setCheckDetailState(CheckDetailStateEnum.CHECK_DETAIL_WAIT.getCode());
            saveDeliveryDetailList.add(deliveryDetail);
        });
        deliveryDetailSrv.saveBatch(saveDeliveryDetailList);
        try {
            //ilink添加待验收提醒
            ilinkSrv.sendAcceptanceMsg(deliveryEty, purchase, false);
        } catch (Exception e) {
            log.error("【ilink】发送待验收消息失败 deliverId {},purchaseNumber {}", deliveryEty.getId(), purchase.getPurchaseNumber());
        }
    }

    /**
     * 收货人与下单人不一致发送短信且只发一次
     *
     * @param purchaseSubOrder
     * @param mobile
     * @param userId
     */
    private void sendNoteMsg(ShopPurchaseSubOrder purchaseSubOrder, String mobile, Long userId) {
        // 短信切换,当前这类短信是关闭状态。代码是坑,先删除,后续有需求重写
    }

    /**
     * 确认供应商订单
     *
     * @param orderNumber 子订单号
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult confirmSubOrder(String orderNumber) {
        log.info(" 供应商确认订单 confirmSubOrder begin:{}", orderNumber);
        PayOrderDO payOrder = payOrderService.getOrder(orderNumber);
        if (null != payOrder && !PayOrderStatusEnum.SUCCESS.getStatus().equals(payOrder.getStatus())) {
            throw new OrderException("订单未支付");
        }


        BigDecimal failAmount = new BigDecimal(0);
        ShopPurchaseSubOrder purchaseSubOrder = this.purchaseSubOrderService.queryConfirmSubOrderByOrderNumber(orderNumber);
        if(yflYamlConfig.getTenantId().equals(purchaseSubOrder.getTenantId())){
            if(purchaseSubOrder.getOrderState()>=20){
                return ServiceResult.succ();
            }else if(purchaseSubOrder.getOrderState()<10){
                return ServiceResult.error(purchaseSubOrder.getFailReason());
            }
        }

        try {
            final boolean confirm = this.remoteInfoManage.confirmOrder(purchaseSubOrder.getOrderNumber(),
                    purchaseSubOrder.getSupplierOrderNumber(),
                    purchaseSubOrder.getSupplierCode());
            if (confirm) {
                purchaseSubOrder.setOrderState(20);
                purchaseSubOrder.setFailReason("确认订单成功");
            } else {
                failAmount = failAmount.add(purchaseSubOrder.getOrderPriceTax());
                // 向供应商确认订单失败
                purchaseSubOrder.setOrderState(0);
                purchaseSubOrder.setFailReason("供应商确认订单未通过");
                // 回退授信余额
                authAmountInfoService.authAmountByConfirmOrder(purchaseSubOrder, AuthAmountType.CANCEL.getType());
            }
        } catch (final DataRequestException data) {
            failAmount = failAmount.add(purchaseSubOrder.getOrderPriceTax());
            // 回退授信余额
            authAmountInfoService.authAmountByConfirmOrder(purchaseSubOrder, AuthAmountType.CANCEL.getType());
            purchaseSubOrder.setOrderState(-1);

            if (!StringHelper.IsEmptyOrNull(data.getResult())) {
                purchaseSubOrder.setFailReason(data.getResult());
                log.error(data.getResult());
            } else {
                purchaseSubOrder.setFailReason("供应商确认订单失败");
                log.error(purchaseSubOrder.getSupplierName() + "订单【" + purchaseSubOrder.getSupplierOrderNumber() + "】确认失败");
                if (!StringHelper.IsEmptyOrNull(data.getResponse())) {
                    log.error(data.getResponse());
                }
            }
        }
        this.updateSubOrderState(purchaseSubOrder);

        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            //福利确认订单入账单池
            settleBillPoolYflCustomerService.yflOrderToPool(purchaseSubOrder.getOrderNumber());

            // 获取手机号
            final ShopPurchaseOrderVo purchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseSubOrder.getPurchaseNumber());

            // 福利下单,发送短信
            this.sendNoteMsg(purchaseSubOrder, purchaseOrderVo.getApplyUserPhone(), Long.valueOf(purchaseOrderVo.getApplyUserId()));
            // 发送订阅信息
            try {
                if (purchaseSubOrder.getOrderState() == 20) {
                    String applyUserId = purchaseOrderVo.getApplyUserId();
                    SystemIntegral systemIntegral = systemIntegralService.getByActivityIdUserId(purchaseOrderVo.getActivityId(), Long.valueOf(applyUserId));
                    SystemUsers user = systemUsersService.getUser(Long.valueOf(applyUserId));
                    if (user != null) {
                        SystemActivity systemActivity = systemActivityService.getById(purchaseOrderVo.getActivityId());
                        if(!miniAppNotifyService.getNotNoticeActivityCode().contains(systemActivity.getActivityCode())) {
                            miniAppNotifyService.sendIntegralRedemptionNotice(applyUserId, user.getNotifyInfo(), purchaseSubOrder.getOrderNumber(), purchaseOrderVo.getApplyUserName(), purchaseOrderVo.getPurchasePayIntegral(), purchaseOrderVo.getCreateTime(), systemIntegral.getIntegralUsableAmount());
                        }else{
                            log.info("下发微信通知--【{}】过滤不发消息----sendintegralSendNotice", systemActivity.getActivityCode());
                        }

                    }
                }
            } catch (Exception e) {
                log.error("发送订阅消息异常", e);
            }
        }
        final ShopPurchaseOrder shopPurchaseOrder = new ShopPurchaseOrder();
        shopPurchaseOrder.setPurchaseState(30);
        shopPurchaseOrder.setPurchaseNumber(purchaseSubOrder.getPurchaseNumber());

        QueryWrapper<ShopPurchaseSubOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ne(ShopPurchaseSubOrder::getOrderState, 0)
                .lt(ShopPurchaseSubOrder::getOrderState, 20)
                .eq(ShopPurchaseSubOrder::getPurchaseNumber, purchaseSubOrder.getPurchaseNumber());
        long count = purchaseSubOrderService.count(queryWrapper);

        UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, shopPurchaseOrder.getPurchaseNumber());
        if (count == 0) {
            purchaseOrderMapper.update(shopPurchaseOrder, updateWrapper);
        }

        ShopPurchaseOrderVo purchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseSubOrder.getPurchaseNumber());

        // send message to supplier, notice them to deliver goods
        if (openApiConfig.getSuppliers().contains(purchaseSubOrder.getSupplierCode())) {
            openApiMessageUtil.sendMessage(purchaseSubOrder.getSupplierCode(),
                    MessageTypeEnum.ORDER,
                    MapUtil.of("orderNumber", purchaseSubOrder.getOrderNumber())
            );
        }

        if (failAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 失败的订单金额，预算释放
            this.budgetUpdate(purchaseOrderVo, failAmount, 2);
        }
        //更新订单生命周期信息
        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(
                TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId()) ? OrderSalesChannelEnum.YFLMALL.getCode() : OrderSalesChannelEnum.DFMALL.getCode());
        UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto = new UpdatePurchaseOrderInfoForConfirmDto();
        updatePurchaseOrderInfoForConfirmDto.setPurchaseNumber(purchaseSubOrder.getPurchaseNumber());
        orderLifeCycleStrategy.updatePurchaseOrderInfoForApprove(updatePurchaseOrderInfoForConfirmDto);

        return ServiceResult.succ();
    }

    @Transactional(rollbackFor = Exception.class)
    public ServiceResult cancelOrder(final String purchaseNumber, String approvalOpinion) {
        return cancelOrder(purchaseNumber, approvalOpinion, "订单审批驳回");
    }

    /**
     * 审批不通过
     *
     * @param purchaseNumber
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult cancelOrder(final String purchaseNumber, String approvalOpinion, String failReason) {
        log.info("取消订单1------{}，{}，{}", purchaseNumber, approvalOpinion, failReason);
        final List<ShopPurchaseSubOrder> purchaseSubOrderList = this.purchaseSubOrderService.queryConfirmSubOrder(purchaseNumber);
        final ShopPurchaseOrder shopPurchaseOrder = new ShopPurchaseOrder();
        shopPurchaseOrder.setPurchaseState(20);
        if (StrUtil.isNotBlank(approvalOpinion)) {
            shopPurchaseOrder.setApprovalOpinion(approvalOpinion);
        }

        List<String> orderNumberList = Lists.newArrayList();

        //汇总退积分额度
        BigDecimal returnIntegral = BigDecimal.ZERO;

        for (final ShopPurchaseSubOrder purchaseSubOrder : purchaseSubOrderList) {
            purchaseSubOrder.setOrderState(0);
            purchaseSubOrder.setFailReason(failReason);
            this.updateSubOrderState(purchaseSubOrder);

            try {
                this.remoteInfoManage.cancelOrder(purchaseSubOrder.getOrderNumber(), purchaseSubOrder.getSupplierOrderNumber(), purchaseSubOrder.getSupplierCode());
            } catch (final DataRequestException data) {
                // 即使是取消订单失败或异常，也不需要回滚，供应商在订单超期后会自动取消订单。
                if (!StringHelper.IsEmptyOrNull(data.getResult())) {
                    log.error(data.getResult());
                } else {
                    log.error(purchaseSubOrder.getSupplierName() + "订单" + purchaseSubOrder.getSupplierOrderNumber() + "取消失败");
                }
            }
            shopPurchaseOrder.setPurchaseNumber(purchaseSubOrder.getPurchaseNumber());

            //恢复库存
            this.recoverGoodsStock(purchaseSubOrder.getOrderNumber(), purchaseSubOrder.getSupplierCode());
            // 回退授信余额
            authAmountInfoService.authAmountByConfirmOrder(purchaseSubOrder, AuthAmountType.CANCEL.getType());

            orderNumberList.add(purchaseSubOrder.getOrderNumber());
            log.info("要退款积分明细，{}，{}", purchaseSubOrder.getOrderPayIntegral(), purchaseSubOrder.getOrderNumber());
            returnIntegral = returnIntegral.add(purchaseSubOrder.getOrderPayIntegral()).add((purchaseSubOrder.getFreightType() == 0 ? purchaseSubOrder.getOrderFreightPrice() : BigDecimal.ZERO));
        }
        final UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
        this.update(shopPurchaseOrder, updateWrapper);

        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
        UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto = new UpdatePurchaseOrderInfoForConfirmDto();
        updatePurchaseOrderInfoForConfirmDto.setPurchaseNumber(purchaseNumber);
        orderLifeCycleStrategy.updatePurchaseOrderInfoForApprove(updatePurchaseOrderInfoForConfirmDto);

        final ShopPurchaseOrderVo purchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseNumber);
        // 预算释放
        try {
            this.budgetUpdate(purchaseOrderVo, purchaseOrderVo.getPurchaseTotalPrice(), 2);

            budgetService.extReturnAmount(purchaseOrderVo.getPurchaseNumber(),purchaseOrderVo.getPurchaseGoodsPriceNaked(),purchaseOrderVo.getCompanyCode(),orderNumberList);


        } catch (final Exception e) {
            log.error("预算释放失败：", e);
        }
        //取消同步订单状态到外部系统需要根据外部系统返回来做业务，不能放到try里面去
        voyahPurchaseService.extSyncOrderState(purchaseOrderVo.getPurchaseNumber(),purchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList()),purchaseOrderVo.getCompanyCode());

        if (null != purchaseOrderVo.getActivityId()) {
            //友福利相关
            //取消支付订单
            List<String> merchantOrderIdList = purchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());
            merchantOrderIdList.add(purchaseNumber);
            payOrderService.closeWxPayOrder(merchantOrderIdList);
            SystemIntegral systemIntegral =
                    systemIntegralService.getByActivityIdUserId(purchaseOrderVo.getActivityId(), Long.valueOf(purchaseOrderVo.getApplyUserId()));
            // 退还积分
            systemIntegralService.updateIntegral(systemIntegral.getId(), returnIntegral, 3, purchaseOrderVo.getPurchaseNumber(), 0, "订单超时取消");
            // 获取积分活动
            SystemActivity systemActivity = systemActivityService.getById(purchaseOrderVo.getActivityId());

            purchaseSubOrderList.forEach(item -> {
                try {
                    String applyUserId = shopPurchaseOrder.getApplyUserId();
                    SystemUsers user = systemUsersService.getUser(Convert.toLong(applyUserId));
                    if (user != null) {
                        // 发送微信通知
                        TenantUtils.execute(user.getTenantId(), () -> {
                            if(!miniAppNotifyService.getNotNoticeActivityCode().contains(systemActivity.getActivityCode())){
                                miniAppNotifyService.sendOrderCancelNotice(applyUserId, user.getNotifyInfo(), item);
                            }else{
                                log.info("下发微信通知--【{}】过滤不发消息----sendintegralSendNotice", systemActivity.getActivityCode());
                            }
                        });
                    }

                } catch (Exception e) {
                    log.error("发送订阅消息异常", e);
                }
            });
        }
        // 发送订单取消消息
        purchaseSubOrderList.forEach(item -> {
            if (openApiConfig.getSuppliers().contains(item.getSupplierCode())) {
                openApiMessageUtil.sendMessage(item.getSupplierCode(), MessageTypeEnum.CANCEL_ORDER, MapUtil.of("orderNumber", item.getOrderNumber()));
            }
        });

        ShopPurchaseOrder p = baseMapper.queryPurOrderByPurNumber(purchaseNumber);
        try {
            if (lyCompanyCodes.equals(p.getCompanyCode())) {
                // srm释放金额
                srmMaterialInfoSrv.srmUsePrice(SrmUsePriceDto.builder().dfsMallPurchaseApplyNo(purchaseNumber).type("3").build());
            }
        } catch (Exception ex) {
            log.error("srmUsePrice error:{};type:{}", purchaseNumber, "3");
            log.error(ex);
        }

        return ServiceResult.succ();
    }

    /**
     * 审批结束后修改订单状态
     *
     * @param purchaseSubOrder
     */
    public void updateSubOrderState(final ShopPurchaseSubOrder purchaseSubOrder) {
        final UpdateWrapper<ShopPurchaseSubOrder> subOrderUpdateWrapper = new UpdateWrapper<>();
        subOrderUpdateWrapper.lambda().set(ShopPurchaseSubOrder::getOrderState, purchaseSubOrder.getOrderState())
                .set(ShopPurchaseSubOrder::getFailReason, purchaseSubOrder.getFailReason())
                .set(ShopPurchaseSubOrder::getFinishedTime, new Date())
                .eq(ShopPurchaseSubOrder::getOrderNumber, purchaseSubOrder.getOrderNumber());
        this.purchaseSubOrderService.update(subOrderUpdateWrapper);

        final UpdateWrapper<ShopPurchaseSubOrderDetail> subOrderDetailUpdateWrapper = new UpdateWrapper<>();
        subOrderDetailUpdateWrapper.lambda().set(ShopPurchaseSubOrderDetail::getOrderDetailState, purchaseSubOrder.getOrderState())

                .eq(ShopPurchaseSubOrderDetail::getOrderNumber, purchaseSubOrder.getOrderNumber());
        this.purchaseSubOrderDetailService.update(subOrderDetailUpdateWrapper);
    }

    /**
     * @param purchaseOrderVo 采购单信息
     * @param budgetAmount    预算金额
     * @param operateType     预算操作类型 1预算扣除 2预算返还
     */
    public void budgetUpdate(final ShopPurchaseOrderVo purchaseOrderVo, final BigDecimal budgetAmount, final Integer operateType) {
        if (!StringHelper.IsEmptyOrNull(purchaseOrderVo.getBudgetCode())) {
            final OrderChangeBudgetAmountVO changeBudgetAmountVO = OrderChangeBudgetAmountVO.builder().changeAmount(budgetAmount).userId(Long.valueOf(purchaseOrderVo.getApplyUserId())).budgetId(purchaseOrderVo.getBudgetId()).purchaseNumber(purchaseOrderVo.getPurchaseNumber()).organizationId(Long.valueOf(purchaseOrderVo.getApplyDeptId())).username(purchaseOrderVo.getApplyUserName()).applyDeptName(purchaseOrderVo.getApplyDeptName()).build();
            switch (operateType) {
                case 1:
                    // 1预算扣除
                    this.budgetService.deductAmounts(changeBudgetAmountVO);
                    break;
                case 2:
                    // 预算返还
                    this.budgetService.returnAmounts(changeBudgetAmountVO);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 查询采购单详情
     *
     * @return
     */
    public ShopPurchaseOrderVo queryPurchaseOrderVoByNumber(final PurchaseOrderQueryDto purchaseOrderQueryDto) {
        //查询采购单详情
        String purchaseNumber = purchaseOrderQueryDto.getPurchaseNumber();
        final ShopPurchaseOrderVo shopPurchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseNumber);
        final QueryWrapper<ShopPurchaseSubOrder> queryOrderWrapper = new QueryWrapper<>();
        queryOrderWrapper.lambda().select(ShopPurchaseSubOrder::getOrderNumber).eq(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumber);
        final List<Object> objectList = this.purchaseSubOrderService.listObjs(queryOrderWrapper);
        shopPurchaseOrderVo.setOrderNumberList(objectList);
        // 查询发货单信息
        final List<SupplierOrderDeliveryVo> subOrderDeliveryVoList = this.shopDeliveryMapper.queryDeliveryByPurchase(purchaseNumber);
        subOrderDeliveryVoList.forEach(delivery -> {
            final List<SupplierOrderDeliveryGoodsVo> deliveryGoodsVoList = this.shopDeliveryDetailMapper.queryDeliveryGoodsByPackageId(delivery.getId());
            delivery.setDeliveryGoodsVoList(deliveryGoodsVoList);
        });
        shopPurchaseOrderVo.setSubOrderDeliveryVoList(subOrderDeliveryVoList);

        //计算有效时间
        final Double createTime = (double) shopPurchaseOrderVo.getCreateTime().getTime();
        final Double l = (System.currentTimeMillis() - createTime) / (24 * 3600 * 1000);
        final double ceil = Math.ceil(l);
        final Long validPeriod = new Double(this.orderValidDay - ceil).longValue();
        shopPurchaseOrderVo.setValidPeriod(validPeriod < 0 ? -1 : validPeriod);

        List<FloatPriceGoodsDto> floatPriceGoodsDtoList = shopOrderDetailFloatMapper.selectFloatListByPurchaseNumber(purchaseNumber);
        shopPurchaseOrderVo.setFloatPriceGoodsList(floatPriceGoodsDtoList);
        List<PurchaseSubOrderDetailVo> orderDetailList = shopPurchaseOrderVo.getOrderDetailList();
        BigDecimal purchaseTotalPriceCny = BigDecimal.ZERO;
        for (PurchaseSubOrderDetailVo vo : orderDetailList) {
            BigDecimal confirmNum = vo.getConfirmNumDecimal();
            BigDecimal goodsUnitTaxPriceCny = vo.getGoodsUnitTaxPriceCny();
            BigDecimal mu = goodsUnitTaxPriceCny.multiply(confirmNum);
            vo.setGoodsTotalPriceCny(mu.setScale(2, RoundingMode.HALF_UP));
            purchaseTotalPriceCny = purchaseTotalPriceCny.add(mu);
            //兼容处理之前需求人为空情况 取申请人
            vo.setNeederName(StringUtils.isEmpty(vo.getNeederName()) ? shopPurchaseOrderVo.getApplyUserName() : vo.getNeederName());
            vo.setNeederDepartmentName(StringUtils.isEmpty(vo.getNeederDepartmentName()) ? shopPurchaseOrderVo.getApplyDeptName() : vo.getNeederName());
        }
        shopPurchaseOrderVo.setPurchaseTotalPriceCny(purchaseTotalPriceCny.setScale(2, RoundingMode.HALF_UP));
        shopPurchaseOrderVo.setCustomLy(purchaseCustomLyMapper.getOneByPurchaseNumber(purchaseNumber));

        //营销单位的采购单 查询电商订单DFS预付款成功状态 订单未支付
        if (dfsYamlConfig.getCompanycode().contains(shopPurchaseOrderVo.getCompanyCode()) && Objects.equals(shopPurchaseOrderVo.getPurchaseState(), PurchaseOrderEnum.PURCHASE_APPROVAL_PASS.getCode())) {
            shopPurchaseOrderVo.setIsDfsPay(1);
            shopPurchaseOrderVo.setDoaPayStatus(0);
            //查询电商订单DFS预付款成功状态
            DfsDoaPayEntity doaPay = dfsDoaPayService.queryDfsPayByPurchaseNumber(shopPurchaseOrderVo.getPurchaseNumber());
            if (doaPay != null) {
                shopPurchaseOrderVo.setDoaPayStatus(doaPay.getPayState());
                shopPurchaseOrderVo.setDoaPayAmount(doaPay.getPayAmount());
                shopPurchaseOrderVo.setDoaPayTime(doaPay.getPayDate());
                shopPurchaseOrderVo.setPayAccountName(doaPay.getPayAccountName());
            }
        }

        if(CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(shopPurchaseOrderVo.getCompanyCode())){
            ShopPurchaseHondaInvestVo hondaInvestVo = shopPurchaseHondaInvestService.getByPurchaseOrderId(shopPurchaseOrderVo.getPurchaseId());
            shopPurchaseOrderVo.setShopPurchaseHondaInvestVo(hondaInvestVo);

            List<ShopPurchaseAttachmentVo> attachmentVos = shopPurchaseAttachmentService.getByPurchaseOrderId(shopPurchaseOrderVo.getPurchaseId());

            shopPurchaseOrderVo.setShopPurchaseAttachmentVoList(attachmentVos);
        }

        if(CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(shopPurchaseOrderVo.getCompanyCode())){
            List<SubOrderMultipleBudget> listByPurchaseNumber = multipleBudgetService.getListByPurchaseNumber(shopPurchaseOrderVo.getPurchaseNumber());
            if(CollUtil.isNotEmpty(listByPurchaseNumber)){
                shopPurchaseOrderVo.setMultipleBudgetVoList(cn.hutool.core.bean.BeanUtil.copyToList(listByPurchaseNumber, SubOrderMultipleBudgetVo.class));
            }
        }
        return shopPurchaseOrderVo;
    }

    /**
     * 分页查询采购申请单列表
     *
     * @param pageReq  分页对象
     * @param queryDto 查询dto
     * @return {@link PageResp}<{@link ShopPurchaseOrderPageVo}>
     */
    public PageResp<ShopPurchaseOrderPageVo> queryPurchaseOrderPage(final PageReq pageReq, final PurchaseOrderQueryPageDto queryDto) {
        final IPage<ShopPurchaseOrderPageVo> pageVo = this.baseMapper.queryPurchaseOrderPageVo(DataAdapter.adapterPageReq(pageReq), queryDto);
        List<ShopPurchaseOrderPageVo> records = pageVo.getRecords();
        //币种数据
        extractedCurrency(records);
        //列表倒计时提醒
        orderCountdown(records);
        //预付款单位 支付状态
        orderDfsDoaPay(records);
        //填充 供应商来源
        fillDataSource(records);
        return DataAdapter.adapterPage(pageVo, ShopPurchaseOrderPageVo.class);
    }

    private void fillDataSource(List<ShopPurchaseOrderPageVo> records) {
        if (CollUtil.isNotEmpty(records)) {
            if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(records.get(0).getCompanyCode())) {
                List<String> purchaseNumberList = records.stream().map(ShopPurchaseOrderPageVo::getPurchaseNumber).collect(Collectors.toList());
                //岚图需要填充供应商来源
                List<ShopPurchaseSubOrder> subOrders = purchaseSubOrderService.getBaseMapper()
                        .selectList(new LambdaQueryWrapperX<ShopPurchaseSubOrder>()
                                .in(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumberList)
                                .select(ShopPurchaseSubOrder::getPurchaseNumber, ShopPurchaseSubOrder::getSupplierDataSource));
                Map<String, String> collect = subOrders.stream().collect(Collectors.toMap(ShopPurchaseSubOrder::getPurchaseNumber, ShopPurchaseSubOrder::getSupplierDataSource,(e1, e2) -> e1));
                for (ShopPurchaseOrderPageVo record : records) {
                    record.setDataSource(collect.get(record.getPurchaseNumber()));
                }
            }
        }
    }


    private void orderDfsDoaPay(List<ShopPurchaseOrderPageVo> records) {
        for (ShopPurchaseOrderPageVo x : records) {
            if (!dfsYamlConfig.getCompanycode().contains(x.getCompanyCode()) ||
                    !Objects.equals(x.getPurchaseState(), PurchaseOrderEnum.PURCHASE_APPROVAL_PASS.getCode())) {
                continue;
            }
            x.setIsDfsPay(1);
            x.setDoaPayStatus(0);
            //查询电商订单DFS预付款成功状态
            DfsDoaPayEntity doaPay = dfsDoaPayService.queryDfsPayByPurchaseNumber(x.getPurchaseNumber());
            if (doaPay != null) {
                x.setDoaPayStatus(doaPay.getPayState());
                x.setDoaPayAmount(doaPay.getPayAmount());
                x.setDoaPayTime(doaPay.getPayDate());
                x.setPayAccountName(doaPay.getPayAccountName());
            }
        }
    }

    /**
     * 采购订单 倒计时提醒
     * 南方营销单位 审批完成后并且未发货需要倒计时
     * 其他单位，采购单在审批中，需要倒计时
     */
    private void orderCountdown(List<ShopPurchaseOrderPageVo> records) {
        Set<String> marketCompanyCode = customizeDfsProperty.getMarketCompanyCode();
        for (ShopPurchaseOrderPageVo x : records) {//采购单在审批中，需要倒计时
            if (Objects.equals(x.getPurchaseState(), PurchaseOrderEnum.PURCHASE_APPROVAL_ON.getCode())) {
                x.setCountdown(PurchaseOrderEnum.COUNTDOWN_ON.getCode());
            }
            //南方营销单位 审批完成后还需要查看是否发货 代发货状态也需要提醒
            if (marketCompanyCode.stream().anyMatch(item -> item.equals(x.getCompanyCode()))
                    && Objects.equals(x.getPurchaseState(), PurchaseOrderEnum.PURCHASE_APPROVAL_PASS.getCode())) {
                List<ShopPurchaseSubOrder> purchaseSubOrderList = purchaseSubOrderService.getBaseMapper()
                        .selectList(new LambdaQueryWrapperX<ShopPurchaseSubOrder>()
                                .eq(ShopPurchaseSubOrder::getPurchaseNumber, x.getPurchaseNumber())
                                .select(ShopPurchaseSubOrder::getOrderState));
                if (purchaseSubOrderList.stream().anyMatch(y -> y.getOrderState() == 10)) {
                    x.setCountdown(PurchaseOrderEnum.COUNTDOWN_ON.getCode());
                }
            }
        }
    }

    public PageResp<ShopPurchaseOrderPageVo> queryPurchaseOrderPageYfw(final PageReq pageReq, final PurchaseOrderQueryPageDto queryDto) {
        List<SystemDictDataEntity> dictDataList = dictDataSrv.getDictDatasByDictType("proxy_receipt_package");
        Map<String, String> map = dictDataList.stream().collect(Collectors.toMap(SystemDictDataEntity::getValue, SystemDictDataEntity::getLabel));
        LoginUser loginUser = LocalUserHolder.get();
        String username = loginUser.getUsername();
        String scope = map.get(username);
        if (StrUtil.isNotBlank(scope)) {
            String companyCodes = scope.split("#")[1];
            if (!"all".equals(companyCodes)) {
                queryDto.setCompanyCodes(StrUtil.splitTrim(companyCodes, ","));
            }
        } else {
            queryDto.setApplyEmpCode(username);
        }
        final IPage<ShopPurchaseOrderPageVo> pageVo = this.baseMapper.queryPurchaseOrderPageVo(DataAdapter.adapterPageReq(pageReq), queryDto);
        List<ShopPurchaseOrderPageVo> records = pageVo.getRecords();
        //币种数据
        extractedCurrency(records);
        return DataAdapter.adapterPage(pageVo, ShopPurchaseOrderPageVo.class);
    }


    /**
     * 填充币种数据
     *
     * @param records 商城采购申请单分页Vo对象
     */
    public void extractedCurrency(List<ShopPurchaseOrderPageVo> records) {
        List<String> purchaseNumberList = records.stream().map(ShopPurchaseOrderPageVo::getPurchaseNumber).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(purchaseNumberList)) {
            return;
        }
        Map<String, OrderContractVo> currencyVoMap = shopSrmContractService.getByPurchaseNumberList(purchaseNumberList).stream().collect(Collectors.toMap(OrderContractVo::getPurchaseNumber, t -> t));
        if (!CollectionUtil.isEmpty(currencyVoMap)) {
            records.forEach(x -> {
                OrderContractVo orderContractVo = currencyVoMap.get(x.getPurchaseNumber());
                if (!ObjectUtils.isEmpty(orderContractVo)) {
                    x.setCurrencyCode(orderContractVo.getCurrencyCode());
                    x.setCurrencyName(orderContractVo.getCurrencyName());
                }
            });
        }
    }

    public PageResp<MyOrderFrontVo> queryMyOrderPage(PageReq pageReq, MyOrderQueryDto queryDto) {
        final LoginUser user = LocalUserHolder.get();
        //切分相同简称的多个供应商编码
        queryDto.setSupplierCodes(StrUtil.splitTrim(queryDto.getSupplierCode(), ','));
        IPage<MyOrderFrontVo> pageVo = baseMapper.queryMyOrderPage(DataAdapter.adapterPageReq(pageReq), queryDto);
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            pageVo.getRecords().stream().forEach(myOrderFrontVo -> {
                for (MyOrderDetailFrontVo myOrderDetailFrontVo : myOrderFrontVo.getOrderDetailList()) {
                    //填充SAP参考编码
                    QueryWrapper<ShopMaterialRelationEntity> maraQueryWrapper = new QueryWrapper<>();
                    maraQueryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, user.getEntityOrganizationCode())
                            .eq(ShopMaterialRelationEntity::getGoodsCode, myOrderDetailFrontVo.getGoodsCode())
                            .eq(ShopMaterialRelationEntity::getMaraWerks,myOrderFrontVo.getFactoryCode())
                            .eq(ShopMaterialRelationEntity::getIsDel, 0);
                    List<ShopMaterialRelationEntity> shopMaterialRelationEntities = materialRelationService.list(maraQueryWrapper);
                    Map<String, String> goodsCodeMap = shopMaterialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr, (key1, key2) -> key1));
                    myOrderDetailFrontVo.setMaraMatnr(goodsCodeMap.get(myOrderDetailFrontVo.getGoodsCode()));
                }
            });
        }

        return DataAdapter.adapterPage(pageVo, MyOrderFrontVo.class);
    }

    public List<MyOrderFrontExcelVo> exportMyOrder(MyOrderQueryDto queryDto) {
        queryDto.setSupplierCodes(StrUtil.splitTrim(queryDto.getSupplierCode(), ','));
        List<MyOrderFrontExcelVo> myOrderFrontExcelVos = baseMapper.exportMyOrder(queryDto);
        if (CollectionUtil.isEmpty(myOrderFrontExcelVos)) {
            return myOrderFrontExcelVos;
        }
        fillCustomisationField(myOrderFrontExcelVos);
        return myOrderFrontExcelVos;
    }

    private void fillCustomisationField(List<MyOrderFrontExcelVo> myOrderFrontExcelVos) {
        final LoginUser user = LocalUserHolder.get();
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            List<String> purchaseNumberList = myOrderFrontExcelVos.stream().map(MyOrderFrontExcelVo::getPurchaseNumber).collect(Collectors.toList());
            List<ExtDeliveryNumDto> extDeliveryNumDtos = dfgDeliveryService.queryExtDeliveryNumByPurchaseNumber(purchaseNumberList);
            myOrderFrontExcelVos.forEach(order -> {
                //填充SAP参考编码
                QueryWrapper<ShopMaterialRelationEntity> maraQueryWrapper = new QueryWrapper<>();
                maraQueryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, user.getEntityOrganizationCode())
                        .eq(ShopMaterialRelationEntity::getGoodsCode, order.getGoodsCode())
                        .eq(ShopMaterialRelationEntity::getIsDel, 0);
                List<ShopMaterialRelationEntity> shopMaterialRelationEntities = materialRelationService.list(maraQueryWrapper);
                Map<String, String> goodsCodeMap = shopMaterialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr, (key1, key2) -> key1));
                if (!CollectionUtil.isEmpty(goodsCodeMap)) {
                    order.setMaraMatnr(goodsCodeMap.get(order.getGoodsCode()));
                }
                //填充外部收货数量
                if (CollectionUtil.isNotEmpty(extDeliveryNumDtos)) {
                    List<ExtDeliveryNumDto> extDeliveryNumDtos1 = extDeliveryNumDtos.stream().filter(item -> item.getPurchaseNumber().equals(order.getPurchaseNumber()))
                            .filter(item1 -> item1.getItemNo().equals(order.getRowSerialNumber())).collect(Collectors.toList());
                    Optional<Integer> reduce = extDeliveryNumDtos1.stream().map(ExtDeliveryNumDto::getNum).reduce((a, b) -> a + b);
                    if (reduce.isPresent()) {
                        order.setExtDeliveryNum(reduce.get());
                    }
                }
            });
        }
    }

    public List<ShopPurchaseOrderPageVo> queryPurchaseOrderList(final PurchaseOrderQueryPageDto queryDto) {
        final PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        final IPage<ShopPurchaseOrderPageVo> pageVo = this.baseMapper.queryPurchaseOrderPageVo(DataAdapter.adapterPageReq(pageReq), queryDto);
        return pageVo.getRecords();
    }

    /**
     * 查询采购订单
     *
     * @param purchaseOrderQueryDto 采购订单查询dto
     * @return {@link List}<{@link ShopPurchaseOrdersVo}>
     */
    public List<ShopPurchaseOrdersVo> queryPurchaseOrder(final PurchaseOrderQueryDto purchaseOrderQueryDto) {
        final List<ShopPurchaseOrdersVo> shopPurchaseOrdersVos = this.purchaseOrderMapper.queryPurchaseOrder(purchaseOrderQueryDto);
        return shopPurchaseOrdersVos;
    }

    /**
     * 查询采购订单
     *
     * @param list 列表
     * @return {@link ServiceResult}
     */
    public ServiceResult queryLogisticsTrack(final ArrayList<ShopPurchaseWlDto> list) {
        final List<DeliveryDetailResp> list1 = new ArrayList<>();
        for (final ShopPurchaseWlDto shopPurchaseWlDto : list) {
            DeliveryDetailResp deliveryDetailResp = new DeliveryDetailResp();
            try {
                deliveryDetailResp = this.remoteInfoManage.getDeliveryDetail(shopPurchaseWlDto.getOrderNo(), shopPurchaseWlDto.getPackageId(), shopPurchaseWlDto.getSupplier());
            } catch (final Exception e) {
                log.error("logisticsTrack_error" + e);
                break;
            }
            if (null == deliveryDetailResp) {
                break;
            }
            list1.add(deliveryDetailResp);
        }
        final List<DeliveryDetailResp> myList = list1.stream().distinct().collect(Collectors.toList());
        return ServiceResult.succ(myList);
    }

    public ServiceResult queryLogisticsTrackByDeliveryId(List<Long> deliveryIds) {
        final List<DeliveryDetailResp> list1 = new ArrayList<>();
        for (Long deliveryId : deliveryIds) {
            ShopDelivery shopDelivery = deliverySrv.getById(deliveryId);
            if (ObjectUtil.isNull(shopDelivery)) {
                continue;
            }
            DeliveryDetailResp deliveryDetailResp = new DeliveryDetailResp();
            try {
                deliveryDetailResp = this.remoteInfoManage.getDeliveryDetailExcluded100(shopDelivery.getSupplierOrderId(), shopDelivery.getPackageId(), shopDelivery.getSupplierCode());
            } catch (final Exception e) {
                log.error("logisticsTrack_error" + e);
                break;
            }
            if (null == deliveryDetailResp) {
                break;
            }
            list1.add(deliveryDetailResp);
        }
        final List<DeliveryDetailResp> myList = list1.stream().distinct().collect(Collectors.toList());
        return ServiceResult.succ(myList);
    }

    /**
     * vos查询采购订单页面
     *
     * @param pageReq               页面请求
     * @param purchaseOrderQueryDto 采购订单查询dto
     * @return {@link PageResp}<{@link PurchaseSubOrderDetailVo}>
     */
    public PageResp<PurchaseSubOrderDetailVo> queryPurchaseOrderDetailPage(final PageReq pageReq, final PurchaseOrderQueryDto purchaseOrderQueryDto) {
        final IPage<PurchaseSubOrderDetailVo> iPage = this.purchaseOrderMapper.queryPurchaseOrderPageVos(DataAdapter.adapterPageReq(pageReq), purchaseOrderQueryDto);
        return DataAdapter.adapterPage(iPage, PurchaseSubOrderDetailVo.class);
    }

    /**
     * 分页查询订单列表
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@link PageResp}<{@link ShopOrderPageVo}>
     */
    public PageResp<ShopOrderPageVo> querySubOrderPage(final PageReq pageReq, final ShopOrderQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getOrderState())) {
            queryDto.setOrderStates(Arrays.asList(queryDto.getOrderState().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final IPage<ShopOrderPageVo> orderPage = this.baseMapper.queryOrderPage(DataAdapter.adapterPageReq(pageReq), queryDto);

        //填充全称
        List<ShopOrderPageVo> shopOrderPageVos = orderPage.getRecords();
        List<String> supplierCodes = shopOrderPageVos.stream().map(ShopOrderPageVo::getSupplierCode).collect(Collectors.toList());
        Map<String, String> supplierMap = querySupplierFullName(supplierCodes);
        for (ShopOrderPageVo shopOrderPageVo : shopOrderPageVos) {
            shopOrderPageVo.setSupplierFullName(supplierMap.get(shopOrderPageVo.getSupplierCode()));
        }
        orderPage.setRecords(shopOrderPageVos);
        return DataAdapter.adapterPage(orderPage, ShopOrderPageVo.class);
    }

    @DataPermission(enable = false)
    public Map<String, String> querySupplierFullName(List<String> supplierCodes) {
        if (CollectionUtil.isEmpty(supplierCodes)) {
            return Maps.newHashMap();
        }
        supplierCodes = supplierCodes.stream().distinct().collect(Collectors.toList());
        List<ShopSupplier> shopSuppliers = shopSupplierService.getSupplierInfoByCodes(supplierCodes);
        return shopSuppliers.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, ShopSupplier::getSupplierFullName));
    }

    /**
     * 友服务订单查询
     *
     * @param pageReq
     * @param youServiceOrderPageQueryDto
     * @return
     */
    public PageResp<YouServiceOrderPageVo> queryYouServiceSubOrderPage(PageReq pageReq, YouServiceOrderPageQueryDto youServiceOrderPageQueryDto) {
        List<SystemDictDataEntity> dictDataList = dictDataSrv.getDictDatasByDictType("proxy_receipt_package");
        Map<String, String> map = dictDataList.stream().collect(Collectors.toMap(SystemDictDataEntity::getValue, SystemDictDataEntity::getLabel));
        LoginUser loginUser = LocalUserHolder.get();
        String username = loginUser.getUsername();
        String scope = map.get(username);
        if (StrUtil.isNotBlank(scope)) {
            String companyCodes = scope.split("#")[1];
            if (!"all".equals(companyCodes)) {
                youServiceOrderPageQueryDto.setCompanyCodes(StrUtil.splitTrim(companyCodes, ","));
            }
        } else {
            youServiceOrderPageQueryDto.setApplyUserId(loginUser.getId());
        }
        IPage<YouServiceOrderPageVo> youServiceOrderPageVoIPage = this.baseMapper.queryYouServiceSubOrderPage(DataAdapter.adapterPageReq(pageReq), youServiceOrderPageQueryDto);
        return DataAdapter.adapterPage(youServiceOrderPageVoIPage, YouServiceOrderPageVo.class);
    }

    /**
     * 分页查询订单列表 【东财】
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@link PageResp}<{@link ShopOrderPageVo}>
     */
    public PageResp<ShopOrderPageVo> querySubOrderPageForDC(final PageReq pageReq, final ShopOrderQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getOrderState())) {
            queryDto.setOrderStates(Arrays.asList(queryDto.getOrderState().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final IPage<ShopOrderPageVo> orderPage = this.baseMapper.queryOrderPageForDC(DataAdapter.adapterPageReq(pageReq), queryDto);
        //应辉哥要求，此处需要暴露
//        orderPage.getRecords().forEach(item->{
//            //如果 电商含税官网价 小于 商城售价 隐藏，不能给客户看到我们比电商卖得贵
//            if (item.getSupplierOriginalPriceTax().compareTo(item.getOrderPriceTax()) < 1) {
//                item.setSupplierOriginalPriceTax(null);
//            }
//        });
        //填充全称
        List<ShopOrderPageVo> shopOrderPageVos = orderPage.getRecords();
        List<String> supplierCodes = shopOrderPageVos.stream().map(ShopOrderPageVo::getSupplierCode).collect(Collectors.toList());
        Map<String, String> supplierMap = querySupplierFullName(supplierCodes);
        for (ShopOrderPageVo shopOrderPageVo : shopOrderPageVos) {
            shopOrderPageVo.setSupplierFullName(supplierMap.get(shopOrderPageVo.getSupplierCode()));
        }
        return DataAdapter.adapterPage(orderPage, ShopOrderPageVo.class);
    }

    public ShopPurchaseSubOrderVo querySubOrderInfo(final String orderNumber) {
        final ShopPurchaseSubOrderVo purchaseSubOrderVo = this.purchaseSubOrderService.querySubOrderVo(orderNumber);
        if (purchaseSubOrderVo == null) {
            throw new HttpException("订单不存在");
        }
        final List<PurchaseSubOrderDetailVo> orderGoodsVos = this.purchaseSubOrderDetailService.queryListBySubOrderNumber(purchaseSubOrderVo.getOrderNumber());
        //计算人民币总价
        BigDecimal orderPriceCny = BigDecimal.ZERO;
        for (PurchaseSubOrderDetailVo vo : orderGoodsVos) {
            BigDecimal confirmNum = vo.getConfirmNumDecimal();
            BigDecimal goodsUnitTaxPriceCny = vo.getGoodsUnitTaxPriceCny();
            BigDecimal mu = goodsUnitTaxPriceCny.multiply(confirmNum);
            vo.setGoodsTotalPriceCny(mu.setScale(2, RoundingMode.HALF_UP));
            orderPriceCny = orderPriceCny.add(mu);
        }
        purchaseSubOrderVo.setOrderDetailList(orderGoodsVos);
        purchaseSubOrderVo.setOrderPriceCny(orderPriceCny.setScale(2, RoundingMode.HALF_UP));
        //订单收据设置 srm来源供应商统一设置
        purchaseSubOrderVo.setSupplierName("srm".equals(purchaseSubOrderVo.getSupplierDataSource()) ? purchaseSubOrderVo.getSupplierName() : "联友智连科技有限公司");
        Map<String, PurchaseSubOrderDetailVo> goodsCodeMap = orderGoodsVos.stream().collect(Collectors.toMap(PurchaseSubOrderDetailVo::getGoodsCode, Function.identity()));
        // 查询发货单信息
        final List<SupplierOrderDeliveryVo> subOrderDeliveryVoList = this.shopDeliveryMapper.queryDeliveryInfoByOrder(purchaseSubOrderVo.getOrderId());
        subOrderDeliveryVoList.forEach(delivery -> {
            final List<SupplierOrderDeliveryGoodsVo> deliveryGoodsVoList = this.shopDeliveryDetailMapper.queryDeliveryGoodsByPackageId(delivery.getId());
            delivery.setDeliveryGoodsVoList(deliveryGoodsVoList);
            deliveryGoodsVoList.forEach(e -> {
                if (goodsCodeMap.get(e.getGoodsCode()) != null) {
                    PurchaseSubOrderDetailVo purchaseSubOrderDetailVo = goodsCodeMap.get(e.getGoodsCode());
                    purchaseSubOrderDetailVo.setReceivingNum(purchaseSubOrderDetailVo.getReceivingNum() + e.getReceivingNum());
                    purchaseSubOrderDetailVo.setReceivingNumDecimal(purchaseSubOrderDetailVo.getReceivingNumDecimal().add(e.getReceivingNumDecimal()));
                }
            });
        });
        purchaseSubOrderVo.setSubOrderDeliveryVoList(subOrderDeliveryVoList);

        //查询退货单信息
        List<ShopReturnDetailVo> shopReturnDetailVos = returnSrv.returnDetailInfoByOrderNumber(purchaseSubOrderVo.getOrderNumber());
        if (CollectionUtil.isNotEmpty(shopReturnDetailVos)) {
            Map<String, ShopReturnDetailVo> map = shopReturnDetailVos.stream().collect(Collectors.toMap(ShopReturnDetailVo::getGoodsCode, ShopReturnDetailVo -> ShopReturnDetailVo));
            purchaseSubOrderVo.getOrderDetailList().forEach(data -> {
                ShopReturnDetailVo shopReturnDetailVo = map.get(data.getGoodsCode());
                if (shopReturnDetailVo != null) {
                    data.setReturnNum(shopReturnDetailVo.getReturnNum());
                    data.setReturnMoney(shopReturnDetailVo.getReturnTotalMoneyTax());
                    data.setReturnPrice(shopReturnDetailVo.getReturnTotalPriceTax());
                }
            });
        }

        return purchaseSubOrderVo;
    }

    public ShopPurchaseSubOrderVo querySubOrderInfoForDC(final String orderNumber) {
        final ShopPurchaseSubOrderVo purchaseSubOrderVo = this.purchaseSubOrderService.querySubOrderVo(orderNumber);
        if (purchaseSubOrderVo == null) {
            throw new HttpException("订单不存在");
        }
        final List<PurchaseSubOrderDetailVo> orderGoodsVos = this.purchaseSubOrderDetailService.queryListBySubOrderNumber(purchaseSubOrderVo.getOrderNumber());
        //应辉哥要求，此处需要暴露
//        orderGoodsVos.forEach(item -> {
//            if (item.getSupplierUnitOriginalPriceTax().compareTo(item.getGoodsUnitPriceTax()) < 1) {
//                item.setSupplierUnitOriginalPriceTax(null);
//            }
//        });
        orderGoodsVos.stream().forEach(item -> item.setSupplierUnitPriceTax(null));
        purchaseSubOrderVo.setOrderDetailList(orderGoodsVos);

        // 查询发货单信息
        final List<SupplierOrderDeliveryVo> subOrderDeliveryVoList = this.shopDeliveryMapper.queryDeliveryInfoByOrder(purchaseSubOrderVo.getOrderId());
        subOrderDeliveryVoList.forEach(delivery -> {
            final List<SupplierOrderDeliveryGoodsVo> deliveryGoodsVoList = this.shopDeliveryDetailMapper.queryDeliveryGoodsByPackageId(delivery.getId());
            delivery.setDeliveryGoodsVoList(deliveryGoodsVoList);
        });
        purchaseSubOrderVo.setSubOrderDeliveryVoList(subOrderDeliveryVoList);

        //查询退货单信息
        List<ShopReturnDetailVo> shopReturnDetailVos = returnSrv.returnDetailInfoByOrderNumber(purchaseSubOrderVo.getOrderNumber());
        if (CollectionUtil.isNotEmpty(shopReturnDetailVos)) {
            Map<String, ShopReturnDetailVo> map = shopReturnDetailVos.stream().collect(Collectors.toMap(ShopReturnDetailVo::getGoodsCode, ShopReturnDetailVo -> ShopReturnDetailVo));
            purchaseSubOrderVo.getOrderDetailList().forEach(data -> {
                ShopReturnDetailVo shopReturnDetailVo = map.get(data.getGoodsCode());
                if (shopReturnDetailVo != null) {
                    data.setReturnNum(shopReturnDetailVo.getReturnNum());
                    data.setReturnMoney(shopReturnDetailVo.getReturnTotalMoneyTax());
                    data.setReturnPrice(shopReturnDetailVo.getReturnTotalPriceTax());
                }
            });
        }

        return purchaseSubOrderVo;
    }

    /**
     * 友福利商城，订单列表查询
     *
     * @param pageReq
     * @param yflOrderQueryDto
     * @return
     */
    public PageResp<ShopYflOrderVo> queryYflSubOrderPage(PageReq pageReq, ShopYflOrderQueryDto yflOrderQueryDto) {
        LoginUser localUser = LocalUserHolder.get();
        yflOrderQueryDto.setUsername(localUser.getUsername());
        IPage<ShopYflOrderVo> subOrderPage = purchaseSubOrderService.queryYflSubOrderPage(pageReq, yflOrderQueryDto);
        List<ShopYflOrderVo> records = subOrderPage.getRecords();
        for (ShopYflOrderVo record : records) {
            if (Objects.equals(GoodsModelEnum.VIRTUAL.getModelCode(), record.getOrderModel())) {
                //填充是卡密还是直冲
                ShopVirtualPurchaseExtended one = shopVirtualPurchaseExtendedService.getOne(new LambdaQueryWrapperX<ShopVirtualPurchaseExtended>().eq(ShopVirtualPurchaseExtended::getOrderNumber, record.getOrderNumber()));
                record.setVirtualType(one.getVirtualType());
            }
        }
        return DataAdapter.adapterPage(subOrderPage, ShopYflOrderVo.class);
    }

    /**
     * 友福利商城，订单信息查询
     *
     * @param orderNumber
     * @return
     */
    public ShopYflOrderVo queryYflSubOrderInfo(String orderNumber) {
        ShopYflOrderVo shopYflOrderVo = purchaseSubOrderService.queryYflSubOrder(orderNumber);
        ShopOrderAddress shopOrderAddress = shopOrderAddressService.queryByPurchaseId(shopYflOrderVo.getPurchaseId());
        shopYflOrderVo.setShopOrderAddress(shopOrderAddress);
        List<ShopYflOrderDetailVo> shopYflOrderDetailVo = shopYflOrderVo.getShopYflOrderDetailVo();
        Map<String, ShopYflOrderDetailVo> goodsMap = CollectionUtils.convertMap(shopYflOrderDetailVo, ShopYflOrderDetailVo::getGoodsSku);
        // 查询发货单信息
        final List<SupplierOrderDeliveryVo> subOrderDeliveryVoList = this.shopDeliveryMapper.queryDeliveryInfoByOrder(shopYflOrderVo.getOrderId());
        //发货售后权益
        subOrderDeliveryVoList.forEach(delivery -> {
            final List<SupplierOrderDeliveryGoodsVo> deliveryGoodsVoList = this.shopDeliveryDetailMapper.queryDeliveryGoodsByPackageId(delivery.getId());
            delivery.setDeliveryGoodsVoList(deliveryGoodsVoList);
            //查询售后信息
            List<AfterSalesRules> afterSalesRules = afterSalesRulesService.queryBySupplierCode(delivery.getSupplierCode());
            if ("DSJD000".equalsIgnoreCase(delivery.getSupplierCode()) || "DSJD001".equalsIgnoreCase(delivery.getSupplierCode())) {
                List<Long> skuList = CollectionUtils.convertList(deliveryGoodsVoList, e -> Long.valueOf(e.getGoodsSku()));
                List<SupportedInfoOpenResp> goodsAttributes = this.remoteInfoManage.getGoodsAttributes(delivery.getPackageId(), delivery.getOrderNumber(), skuList, delivery.getSupplierCode());
                if (CollectionUtil.isNotEmpty(goodsAttributes)) {
                    goodsAttributes.forEach(e -> {
                        ShopYflOrderDetailVo vo = goodsMap.get(e.getWareId());
                        if (vo != null) {
                            e.setPackageId(delivery.getPackageId());
                            List<SupportedInfoOpenResp> supportedInfoOpenRespList = vo.getSupportedInfoOpenRespList();
                            if (CollectionUtil.isEmpty(supportedInfoOpenRespList)) {
                                ArrayList<SupportedInfoOpenResp> list = new ArrayList<>();
                                list.add(e);
                                vo.setSupportedInfoOpenRespList(list);
                            } else {
                                supportedInfoOpenRespList.add(e);
                                vo.setSupportedInfoOpenRespList(supportedInfoOpenRespList);
                            }
                        }
                    });
                }
            } else if (CollectionUtil.isNotEmpty(afterSalesRules)) {
                //线下可售后供应商
                returnSrv.processOfflineAfterSales(goodsMap, delivery , afterSalesRules);
            }
        });
        shopYflOrderVo.setSubOrderDeliveryVoList(subOrderDeliveryVoList);

        if (Objects.equals(GoodsModelEnum.VIRTUAL.getModelCode(), shopYflOrderVo.getOrderModel())) {
            //填充是卡密还是直冲
            ShopVirtualPurchaseExtended one = shopVirtualPurchaseExtendedService.getOne(new LambdaQueryWrapperX<ShopVirtualPurchaseExtended>().eq(ShopVirtualPurchaseExtended::getOrderNumber, shopYflOrderVo.getOrderNumber()));
            shopYflOrderVo.setVirtualType(one.getVirtualType());
            shopYflOrderVo.setRechargeAccount(one.getRechargeAccount());
        }
        if (ObjectUtil.isEmpty(shopYflOrderVo.getSupplierOrderNumber())) {
            shopYflOrderVo.setSupplierOrderNumber("");
        }
        return shopYflOrderVo;
    }

    public VirtualOrderKMInfoVo queryYflVirtualSubOrderCardPwd(String orderNumber) {
        ShopVirtualPurchaseExtended one = shopVirtualPurchaseExtendedService.getOne(new LambdaQueryWrapperX<ShopVirtualPurchaseExtended>().eq(ShopVirtualPurchaseExtended::getOrderNumber, orderNumber));
        if(Objects.isNull(one)){
            return new VirtualOrderKMInfoVo();
        }
        VirtualOrderKMInfoVo result = new VirtualOrderKMInfoVo();
        result.setVirtualAccount(one.getVirtualAccount());
        result.setVirtualPassword(one.getVirtualPassword());
        result.setVirtualOverdue(one.getVirtualOverdue());
        return result;
    }

    public List<Map<String, Object>> queryYflSubOrderStateCount(String userName) {
        return purchaseSubOrderService.queryYflSubOrderStateCount(userName);
    }

    /**
     * 查询商城订单金额统计信息
     *
     * @param queryDto 查询dto
     * @return {@link ShopOrderTotalPriceVo}
     */
    public ShopOrderTotalPriceVo subOrderPriceSumInfo(final ShopOrderQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getOrderState())) {
            queryDto.setOrderStates(Arrays.asList(queryDto.getOrderState().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final List<ShopTaxVo> orderPriceList = this.purchaseOrderMapper.queryOrderPrice(queryDto);
        final ShopOrderTotalPriceVo totalPrice = new ShopOrderTotalPriceVo();
        orderPriceList.forEach(orderPrice -> {
            totalPrice.setOrderFreightPrice(totalPrice.getOrderFreightPrice().add(orderPrice.getOrderFreightPrice()));
            totalPrice.setOrderPriceTax(totalPrice.getOrderPriceTax().add(orderPrice.getOrderPriceTax()));
            totalPrice.setOrderPriceNaked(totalPrice.getOrderPriceNaked().add(orderPrice.getOrderPriceNaked()));
            totalPrice.setSupplierOrderPriceTax(totalPrice.getSupplierOrderPriceTax().add(orderPrice.getSupplierOrderPriceTax()));
            totalPrice.setSupplierOrderPriceNaked(totalPrice.getSupplierOrderPriceNaked().add(orderPrice.getSupplierOrderPriceNaked()));
        });
        totalPrice.setGrossProfit(totalPrice.getOrderPriceTax().subtract(totalPrice.getSupplierOrderPriceTax()));
        if (totalPrice.getGrossProfit().compareTo(BigDecimal.ZERO) == 0) {
            totalPrice.setGrossProfitMargin(BigDecimal.ZERO);
        } else {
            totalPrice.setGrossProfitMargin(totalPrice.getGrossProfit().divide(totalPrice.getOrderPriceTax(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
        }
        return totalPrice;
    }

    /**
     * 查询商城订单金额统计信息
     *
     * @param queryDto 查询dto
     * @return {@link ShopOrderTotalPriceVo}
     */
    public ShopOrderTotalPriceVo subOrderPriceSumInfoForDC(final ShopOrderQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getOrderState())) {
            queryDto.setOrderStates(Arrays.asList(queryDto.getOrderState().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final List<ShopTaxVo> orderPriceList = this.purchaseOrderMapper.queryOrderPrice(queryDto);
        final ShopOrderTotalPriceVo totalPrice = new ShopOrderTotalPriceVo();
        orderPriceList.forEach(orderPrice -> {
            totalPrice.setOrderFreightPrice(totalPrice.getOrderFreightPrice().add(orderPrice.getOrderFreightPrice()));
            totalPrice.setOrderPriceTax(totalPrice.getOrderPriceTax().add(orderPrice.getOrderPriceTax()));
            totalPrice.setOrderPriceNaked(totalPrice.getOrderPriceNaked().add(orderPrice.getOrderPriceNaked()));
        });
        return totalPrice;
    }

    /**
     * 订单失败原因
     *
     * @param orderNumber 订单号
     * @return {@link ServiceResult}
     */
    public ServiceResult queryOrderFailReason(final String orderNumber) {
        final ShopTaxVo shopTaxVo = this.purchaseOrderMapper.getFailReason(orderNumber);
        return ServiceResult.succ(shopTaxVo);
    }

    /**
     * 订单失败
     *
     * @param orderNumber 订单号
     * @param failReason  失败原因
     * @return {@link ServiceResult}
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderCancel(final String orderNumber, final String failReason) {
        log.info("取消订单2------{}，{}", orderNumber, failReason);
        final ShopPurchaseSubOrderVo shopPurchaseSubOrderVo = this.purchaseSubOrderService.querySubOrderVo(orderNumber);
        LoginUser user = LocalUserHolder.get();
        if (user.getTenantId().equals(yflYamlConfig.getTenantId()) && shopPurchaseSubOrderVo.getOrderState() != 10) {
            //友福利订单状态只有未被确认的订单可被取消
            return;
        }
        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            String purchaseNumber = shopPurchaseSubOrderVo.getPurchaseNumber();
            ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(purchaseNumber);
            if (shopPurchaseOrder != null && shopPurchaseOrder.getPurchasePayIntegral().compareTo(shopPurchaseOrder.getPurchaseTotalPrice()) < 0) {
                //混合支付 校验支付中的订单不能取消
                //采购单维度 支付成功,支付中不允许取消,
                PayOrderInfoDTO purchasePay = payOrderService.realPayOrderInfo(purchaseNumber);
                List<String> stateList = new ArrayList<>();
                stateList.add(WXPayOrderTradeStatusEnum.SUCCESS.getCode());
                stateList.add(WXPayOrderTradeStatusEnum.USERPAYING.getCode());
                if (stateList.contains(purchasePay.getTradeState())) {
                    log.info("[orderCancel][订单号({}) 采购单维度支付订单信息({})]", orderNumber, toJsonString(purchasePay));
                    throw new ParameterException("此订单不允许取消");
                }
                //订单维度支付,采购单未支付成功,且支付成功,支付中不允许取消
                PayOrderInfoDTO orderPay = payOrderService.realPayOrderInfo(orderNumber);
                if (stateList.contains(orderPay.getTradeState())) {
                    log.info("[orderCancel][订单号({}) 订单维度支付订单信息({})]", orderNumber, toJsonString(orderPay));
                    throw new ParameterException("此订单不允许取消");
                }
            }
        }
        if (null == shopPurchaseSubOrderVo) {
            throw new ParameterException("找不到该订单");
        }
        //修改供应商订单状态
        final UpdateWrapper<ShopPurchaseSubOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("order_state", 0);
        updateWrapper.set("fail_reason", failReason);
        updateWrapper.eq("order_number", orderNumber);
        this.purchaseSubOrderService.update(updateWrapper);
        //修改订单明细状态
        final UpdateWrapper<ShopPurchaseSubOrderDetail> updateWrappers = new UpdateWrapper<>();
        updateWrappers.set("order_detail_state", 0);
        updateWrappers.eq("order_number", orderNumber);
        this.purchaseSubOrderDetailService.update(updateWrappers);

        //如果所有的子订单都取消了,采购单也取消
        QueryWrapper<ShopPurchaseSubOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopPurchaseSubOrder::getPurchaseNumber, shopPurchaseSubOrderVo.getPurchaseNumber())
                .gt(ShopPurchaseSubOrder::getOrderState, 0);
        Long orderCount = purchaseSubOrderService.count(queryWrapper);
        if (orderCount <= 0) {
            UpdateWrapper<ShopPurchaseOrder> purchaseUpdateWrapper = new UpdateWrapper<>();
            purchaseUpdateWrapper.set("purchase_state", 20);
            purchaseUpdateWrapper.eq("purchase_number", shopPurchaseSubOrderVo.getPurchaseNumber());
            shopPurchaseOrderService.update(purchaseUpdateWrapper);
        }

        OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
        UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto = new UpdatePurchaseOrderInfoForConfirmDto();
        updatePurchaseOrderInfoForConfirmDto.setPurchaseNumber(shopPurchaseSubOrderVo.getPurchaseNumber());
        orderLifeCycleStrategy.updatePurchaseOrderInfoForApprove(updatePurchaseOrderInfoForConfirmDto);

        final BigDecimal cancelPrice = shopPurchaseSubOrderVo.getOrderPriceTax().add(shopPurchaseSubOrderVo.getOrderFreightPrice());
        final BigDecimal cancelPriceNaked = shopPurchaseSubOrderVo.getOrderPriceNaked();
        //授信-查询订单拿到含税总价格
        //确认订单减少授信额度
        //必须订单之前去确认授信额度是否足够，如果不足，则不让确认在确认订单
        authAmountInfoService.returnAuthAmountByOrder(cancelPrice, shopPurchaseSubOrderVo.getCompanyCode(), shopPurchaseSubOrderVo.getApplyUserName(), shopPurchaseSubOrderVo.getApplyUserId(), orderNumber, AuthAmountType.CANCEL.getType());
        if (null != shopPurchaseSubOrderVo.getBudgetId()) {
            final OrderChangeBudgetAmountVO changeBudgetAmountVO = OrderChangeBudgetAmountVO.builder()
                    .changeAmount(cancelPrice)
                    .userId(Long.valueOf(shopPurchaseSubOrderVo.getApplyUserId()))
                    .budgetId(shopPurchaseSubOrderVo.getBudgetId())
                    .purchaseNumber(shopPurchaseSubOrderVo.getPurchaseNumber())
                    .organizationId(Long.valueOf(shopPurchaseSubOrderVo.getApplyDeptId()))
                    .username(shopPurchaseSubOrderVo.getApplyUserName())
                    .applyDeptName(shopPurchaseSubOrderVo.getApplyDeptName())
                    .build();

            this.budgetService.returnAmounts(changeBudgetAmountVO);
        }

        budgetService.extReturnAmount(shopPurchaseSubOrderVo.getPurchaseNumber(),cancelPriceNaked,shopPurchaseSubOrderVo.getCompanyCode(),Collections.singletonList(orderNumber));
        voyahPurchaseService.extSyncOrderState(shopPurchaseSubOrderVo.getPurchaseNumber(),Collections.singletonList(shopPurchaseSubOrderVo.getOrderNumber()),shopPurchaseSubOrderVo.getCompanyCode());


        if (null != shopPurchaseSubOrderVo.getActivityId()) {
            SystemIntegral systemIntegral =
                    systemIntegralService.getByActivityIdUserId(shopPurchaseSubOrderVo.getActivityId(), Long.valueOf(shopPurchaseSubOrderVo.getApplyUserId()));
            //退还积分
            systemIntegralService.updateIntegral(systemIntegral.getId(),
                    shopPurchaseSubOrderVo.getOrderPayIntegral().add(shopPurchaseSubOrderVo.getFreightType() == 0 ? shopPurchaseSubOrderVo.getOrderFreightPrice() : BigDecimal.ZERO),
                    3,
                    shopPurchaseSubOrderVo.getOrderNumber(),
                    0,
                    "订单取消");
            // 获取积分活动
            SystemActivity systemActivity = systemActivityService.getById(shopPurchaseSubOrderVo.getActivityId());

            try {

                String applyUserId = shopPurchaseSubOrderVo.getApplyUserId();
                SystemUsers systemUsers = systemUsersService.getUser(Long.valueOf(applyUserId));
                if (systemUsers != null) {
                    //发送微信通知
                    TenantUtils.execute(user.getTenantId(), () -> {
                        ShopPurchaseSubOrder shopPurchaseSubOrder = new ShopPurchaseSubOrder();
                        BeanUtil.copyProperties(shopPurchaseSubOrderVo, shopPurchaseSubOrder);
                        if(!miniAppNotifyService.getNotNoticeActivityCode().contains(systemActivity.getActivityCode())) {
                            miniAppNotifyService.sendOrderCancelNotice(applyUserId, systemUsers.getNotifyInfo(), shopPurchaseSubOrder);
                        }
                    });
                }

            } catch (Exception e) {
                log.error("发送订阅消息异常", e);
            }
        }

        //恢复库存
        this.recoverGoodsStock(shopPurchaseSubOrderVo.getOrderNumber(), shopPurchaseSubOrderVo.getSupplierCode());
        // 取消发货单
        List<ShopDelivery> deliveryList = deliverySrv.queryByOrderNumber(shopPurchaseSubOrderVo.getOrderNumber());
        deliveryList.forEach(delivery -> deliverySrv.supCancel(delivery.getId()));
        try {
            this.remoteInfoManage.cancelOrder(shopPurchaseSubOrderVo.getOrderNumber(), shopPurchaseSubOrderVo.getSupplierOrderNumber(), shopPurchaseSubOrderVo.getSupplierCode());
        } catch (DataRequestException data) {
            log.error("取消供应商订单失败[orderCancel error]" + data.getResult());
        }
    }

    public void recoverGoodsStock(final String orderNumber, final String supplierCode) {
        final ShopSupplier shopSupplier = this.shopSupplierService.selectByCode(supplierCode);
        final List<ShopGoodsStock> goodsStockList = new ArrayList<>();
        final List<PurchaseSubOrderDetailVo> subOrderDetailVoList = this.purchaseSubOrderDetailService.queryListBySubOrderNumber(orderNumber);
        subOrderDetailVoList.stream().forEach(subOrderDetailVo -> {
            this.shopGoodsDetailService.updateGoodsSaleNum(subOrderDetailVo.getGoodsCode(), -subOrderDetailVo.getConfirmNum().intValue());
            if (shopSupplier.getSupplierType() == 1) {
                final ShopGoodsStock shopGoodsStock = new ShopGoodsStock();
                shopGoodsStock.setGoodsCode(subOrderDetailVo.getGoodsCode());
                shopGoodsStock.setStockAvailable(subOrderDetailVo.getConfirmNum().intValue());
                goodsStockList.add(shopGoodsStock);
            }
        });
        if (CollectionUtil.isNotEmpty(goodsStockList)) {
            this.shopGoodsStockService.recoverGoodsStock(goodsStockList);
        }
    }

    /**
     * 分页查询订单详情列表
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@link PageResp}<{@link ShopOrderDetailPageVo}>
     */
    public PageResp<ShopOrderDetailPageVo> querySubOrderDetailPage(final PageReq pageReq, final ShopOrderDetailQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        IPage<ShopOrderDetailPageVo> orderDetailPage = this.baseMapper.queryOrderDetailPage(DataAdapter.adapterPageReq(pageReq), queryDto);

        List<ShopOrderDetailPageVo> shopOrderPageVos = orderDetailPage.getRecords();
        List<String> supplierCodes = shopOrderPageVos.stream().map(ShopOrderDetailPageVo::getSupplierCode).collect(Collectors.toList());
        Map<String, String> supplierMap = querySupplierFullName(supplierCodes);
        for (ShopOrderDetailPageVo shopOrderPageVo : shopOrderPageVos) {
            shopOrderPageVo.setSupplierFullName(supplierMap.get(shopOrderPageVo.getSupplierCode()));
        }
        orderDetailPage.setRecords(shopOrderPageVos);
        return DataAdapter.adapterPage(orderDetailPage, ShopOrderDetailPageVo.class);
    }

    public ShopOrderDetailTotalPriceVo subOrderDetailPriceSumInfo(final ShopOrderDetailQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final List<ShopPurchaseSubOrderDetail> detailList = this.baseMapper.queryOrderDetailPrice(queryDto);
        final ShopOrderDetailTotalPriceVo totalPrice = new ShopOrderDetailTotalPriceVo();
        detailList.forEach(detailPrice -> {
            totalPrice.setGoodsTotalPriceTax(totalPrice.getGoodsTotalPriceTax().add(detailPrice.getGoodsTotalPriceTax()));
            totalPrice.setGoodsTotalPriceNaked(totalPrice.getGoodsTotalPriceNaked().add(detailPrice.getGoodsTotalPriceNaked()));
            totalPrice.setSupplierTotalPriceTax(totalPrice.getSupplierTotalPriceTax().add(detailPrice.getSupplierTotalPriceTax()));
            totalPrice.setSupplierTotalPriceNaked(totalPrice.getSupplierTotalPriceNaked().add(detailPrice.getSupplierTotalPriceNaked()));
        });
        totalPrice.setGrossProfit(totalPrice.getGoodsTotalPriceTax().subtract(totalPrice.getSupplierTotalPriceTax()));
        if (totalPrice.getGrossProfit().compareTo(BigDecimal.ZERO) == 0) {
            totalPrice.setGrossProfitMargin(BigDecimal.ZERO);
        } else {
            totalPrice.setGrossProfitMargin(totalPrice.getGrossProfit().divide(totalPrice.getGoodsTotalPriceTax(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
        }
        return totalPrice;
    }

    /**
     * 出口货物由excel
     *
     * @param shopPurchaseWlDto 商店购买王dto
     * @param response          响应
     * @throws IOException ioexception
     */
    public void exportGoodsByExcel(final ShopPurchaseWlDto shopPurchaseWlDto, final HttpServletResponse response) throws IOException {
        try (final ExcelWriter writer = ExcelUtil.getWriter(); final ServletOutputStream out = response.getOutputStream()) {
            final ShopPurchaseOrderVo shopPurchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderByNumber(shopPurchaseWlDto.getPurchaseNumber());
            final List<PurchaseSubOrderDetailVo> purchaseSubOrderDetailVos = this.purchaseOrderMapper.queryBySkus(shopPurchaseWlDto);
            final List<List<String>> rows = new ArrayList<>();

            final String fileName = "采购详情" + shopPurchaseWlDto.getPurchaseNumber() + ".xls";
            writer.writeHeadRow(CollUtil.newArrayList("采购名称", "申请部门名称", "申请人名称", "需求人名称", "是否常购", "预算申请号", "预算号",
                    "预算类型", "商品编码", "商品描述", "计量单位", "未税单价", "含税单价", "数量", "状态", "商品sku", "供应商"));
            purchaseSubOrderDetailVos.forEach(item -> {
                if (shopPurchaseOrderVo.getIsUrgent() == 1) {
                    shopPurchaseOrderVo.setIsUrgentName("紧急采购");
                } else {
                    shopPurchaseOrderVo.setIsUrgentName("常规采购");
                }
                switch (item.getOrderDetailState()) {
                    case -1:
                        shopPurchaseOrderVo.setPurchaseStateName("订单失败");
                        break;
                    case 0:
                        shopPurchaseOrderVo.setPurchaseStateName("已取消");
                        break;
                    case 10:
                        shopPurchaseOrderVo.setPurchaseStateName("已提交");
                        break;
                    case 20:
                        shopPurchaseOrderVo.setPurchaseStateName("待发货");
                        break;
                    case 30:
                        shopPurchaseOrderVo.setPurchaseStateName("待收货");
                        break;
                    case 40:
                        shopPurchaseOrderVo.setPurchaseStateName("收货完成");
                        break;
                    case 45:
                        shopPurchaseOrderVo.setPurchaseStateName("部分退货");
                        break;
                    case 50:
                        shopPurchaseOrderVo.setPurchaseStateName("全部退货");
                        break;
                    default:
                        break;
                }

                final String purchaseName = shopPurchaseOrderVo.getPurchaseName();
                final String applyUserName = shopPurchaseOrderVo.getApplyUserName();
                final String applyDeptName = shopPurchaseOrderVo.getApplyDeptName();
                final String budgetApplyCode = shopPurchaseOrderVo.getBudgetApplyCode();
                final String budgetCode = shopPurchaseOrderVo.getBudgetCode();
                final String budgetType = shopPurchaseOrderVo.getBudgetType();
                final String goodsCode = item.getGoodsCode();
                final String goodsDesc = item.getGoodsDesc();
                final String saleUnit = item.getSaleUnit();
                final String isUrgents = shopPurchaseOrderVo.getIsUrgentName();
                final String goodsUnitPriceNaked = item.getGoodsUnitPriceNaked().toString();
                final String goodsUnitPriceTax = item.getGoodsUnitPriceTax().toString();
                final String confirmNum = item.getConfirmNumDecimal().add(new BigDecimal(item.getConfirmNum())).toString();
                final String purchaseStateName = shopPurchaseOrderVo.getPurchaseStateName();
                final String goodsSku = item.getGoodsSku();
                final String supplierName = item.getSupplierName();
                final String nickName = StringUtils.isNotBlank(item.getNeederName()) ? item.getNeederName() : shopPurchaseOrderVo.getApplyUserName();
                rows.add(ListUtil.toList(purchaseName, applyDeptName, applyUserName, nickName, isUrgents, budgetApplyCode, budgetCode, budgetType,
                        goodsCode, goodsDesc, saleUnit, goodsUnitPriceNaked, goodsUnitPriceTax, confirmNum, purchaseStateName, goodsSku, supplierName));
            });

            writer.write(rows);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        } catch (final IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出excel细节
     *
     * @param purchaseOrderQueryDto 采购订单查询dto
     * @param response              响应
     */
    public void exportDetailExcel(final PurchaseOrderQueryDto purchaseOrderQueryDto, final HttpServletResponse response) throws IOException {
        final ExcelWriter writer = ExcelUtil.getWriter();
        final PageReq pageReq = new PageReq();
        pageReq.setPageSize(999);
        final IPage<PurchaseSubOrderDetailVo> iPage = this.purchaseOrderMapper.queryPurchaseOrderPageVos(DataAdapter.adapterPageReq(pageReq), purchaseOrderQueryDto);
        val employees = iPage.getRecords();
        final List<Map<String, Object>> rows = employees.stream().map(item -> {
            switch (item.getOrderDetailState()) {
                case -1:
                    item.setOrderDetailStateName("订单失败");
                    break;
                case 0:
                    item.setOrderDetailStateName("已取消");
                    break;
                case 10:
                    item.setOrderDetailStateName("已提交");
                    break;
                case 20:
                    item.setOrderDetailStateName("待发货");
                    break;
                case 30:
                    item.setOrderDetailStateName("待收货");
                    break;
                case 40:
                    item.setOrderDetailStateName("收货完成");
                    break;
                case 45:
                    item.setOrderDetailStateName("部分退货");
                    break;
                case 50:
                    item.setOrderDetailStateName("全部退货");
                    break;
            }

            final Map<String, Object> maps = new HashMap<>();
            maps.put("goodsCode", item.getGoodsCode());
            maps.put("orderDetailStateName", item.getOrderDetailStateName());
            maps.put("goodsDesc", item.getGoodsDesc());
            maps.put("goodsUnitPriceTax", item.getGoodsUnitPriceTax());
            maps.put("confirmNum", item.getConfirmNum());
            maps.put("supplierName", item.getSupplierName());

            return maps;
        }).collect(Collectors.toList());
        // Header
        writer.addHeaderAlias("goodsCode", "商品编码");
        writer.addHeaderAlias("orderDetailStateName", "状态");
        writer.addHeaderAlias("goodsDesc", "商品描述");
        writer.addHeaderAlias("goodsUnitPriceTax", "价格");
        writer.addHeaderAlias("confirmNum", "数量");
        writer.addHeaderAlias("supplierName", "供应商");
        final String fileName = "采购申请单信息.xls";
        // Body
        writer.setColumnWidth(0, 30);
        writer.setColumnWidth(1, 30);
        writer.setColumnWidth(2, 30);
        writer.setColumnWidth(3, 30);
        writer.setColumnWidth(4, 30);
        writer.setColumnWidth(5, 30);
        writer.write(rows, true);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        response.setCharacterEncoding("UTF-8");
        final ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }

    public void exportOrderManagementExcel(ShopOrderQueryPageDto queryDto, HttpServletResponse response) throws IOException {
        try (ExcelWriter writer = ExcelUtil.getWriter(); ServletOutputStream out = response.getOutputStream()) {
            if (StringUtils.isNotBlank(queryDto.getOrderState())) {
                queryDto.setOrderStates(Arrays.asList(queryDto.getOrderState().split(",")));
            }
            if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
                queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
            }
            if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
                queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
            }
            queryDto.setQueryType(TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId()) ? 1 : 0);
            List<ShopOrderPageVo> shopOrderPageVoList = baseMapper.queryOrderList(queryDto);
            if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                writer.writeHeadRow(CollUtil.newArrayList("商城订单号", "供应商订单号", "采购单号", "商户订单号", "企业名称", "开票主体", "开票类型", "申请人", "收货人", "申请时间", "电商名称", "活动名称", "运费", "补贴邮费", "电商订单金额(含税)", "商城订单金额(总价)", "积分支付金额", "微信支付金额", "毛利润", "毛利率(%)", "订单状态", "退款时间", "退款状态", "个人退款金额", "积分退款金额","订单标记"));
            } else {
                writer.writeHeadRow(CollUtil.newArrayList("商城订单号", "供应商订单号", "采购单号", "企业名称", "开票主体", "申请人", "申请时间", "电商名称", "运费", "补贴邮费", "电商订单金额(含税)", "商城订单金额(总价)", "毛利润", "毛利率(%)", "订单状态", "结算方式","订单标记"));
            }

            List<List<String>> rows = new ArrayList<>();
            shopOrderPageVoList.forEach(data -> {
                String orderStateName = "";
                switch (data.getOrderState()) {
                    case -1:
                        orderStateName = "订单失败";
                        break;
                    case 0:
                        orderStateName = "已取消";
                        break;
                    case 10:
                        orderStateName = "已提交";
                        break;
                    case 20:
                        orderStateName = "待发货";
                        break;
                    case 30:
                        orderStateName = "待收货";
                        break;
                    case 40:
                        orderStateName = "收货完成";
                        break;
                    case 45:
                        orderStateName = "部分退货";
                        break;
                    case 50:
                        orderStateName = "全部退货";
                        break;
                    default:
                        orderStateName = "";
                        break;
                }

                String returnStateName = "";
                switch (data.getReturnState() == null ? 99 : data.getReturnState()) {
                    case -2:
                        returnStateName = GoodsReturnStateEnum.REPLACEMENT_FAILURE.getValue();
                        break;
                    case -1:
                        returnStateName = GoodsReturnStateEnum.RETURN_OF_FAILURE.getValue();
                        break;
                    case 0:
                        returnStateName = GoodsReturnStateEnum.APPLICATION_FOR_RETURN.getValue();
                        break;
                    case 1:
                        returnStateName = GoodsReturnStateEnum.RETURN_COMPLETE.getValue();
                        break;
                    case 2:
                        returnStateName = GoodsReturnStateEnum.REPLACEMENT_PENDING.getValue();
                        break;
                    case 4:
                        returnStateName = GoodsReturnStateEnum.REPLACEMENT_COMPLETE.getValue();
                        break;
                    case 5:
                        returnStateName = GoodsReturnStateEnum.CANCEL_RETURN.getValue();
                        break;
                    case 10:
                        returnStateName = GoodsReturnStateEnum.INITIATE.getValue();
                        break;
                    case 20:
                        returnStateName = GoodsReturnStateEnum.AUDIT.getValue();
                        break;
                    case 30:
                        returnStateName = GoodsReturnStateEnum.RECEIVING.getValue();
                        break;
                    case 40:
                        returnStateName = GoodsReturnStateEnum.PROCESSING.getValue();
                        break;
                    case 50:
                        returnStateName = GoodsReturnStateEnum.CONFIRMED.getValue();
                        break;
                    default:
                        returnStateName = "";
                        break;
                }
                //
                String saleClientName = "";
                switch (data.getSaleClient() == null ? 99 : data.getSaleClient()) {
                    case 1:
                        saleClientName = "B端";
                        break;
                    case 2:
                        saleClientName = "C端";
                        break;
                    default:
                        saleClientName = "未知";
                        break;
                }

                // 毛利润
                BigDecimal incomePrice = data.getOrderPriceTax().subtract(data.getSupplierOrderPriceTax());
                // 毛利率(%)
                BigDecimal incomePriceTax = incomePrice.divide(data.getOrderPriceTax(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                String purchaseNumber = data.getPurchaseNumber();
                String orderNumber = data.getOrderNumber();
                String supplierOrderNumber = data.getSupplierOrderNumber();
                String payOrderNumber = data.getPayOrderNumber() == null ? "-" : data.getPayOrderNumber();
                String companyName = data.getCompanyName();
                String applyUserName = data.getApplyUserName();
                String addressName = data.getAddressName();
                String invoiceSubject = data.getInvoiceSubject();
                String invoiceTypeName = data.getInvoiceTypeName();
                String activityName = data.getActivityName();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String createTime = sdf.format(data.getCreateTime());
                String supplierName = data.getSupplierName();
                String orderFreightPrice = data.getOrderFreightPrice().toString();
                String orderSubsidyFreight = data.getOrderSubsidyFreight().toString();
                String supplierOrderPriceTax = data.getSupplierOrderPriceTax().toString();
                String orderPriceTax = data.getOrderPriceTax().toString();
                String orderPayIntegral = data.getOrderPayIntegral().toString();
                String orderPayMoney = data.getOrderPayMoney().toString();
                String platformReconciliationWay = data.getIsPlatformReconciliation()==1?"平台内结算":"平台外结算";

                if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                    String returnAllPriceTax = data.getReturnAllPriceTax().toString();
                    String returnAllMoneyTax = data.getReturnAllMoneyTax().toString();
                    String returnAmountTime = data.getReturnAmountTime();
                    rows.add(ListUtil.toList(orderNumber, supplierOrderNumber, purchaseNumber, payOrderNumber, companyName, invoiceSubject, invoiceTypeName, applyUserName, addressName, createTime, supplierName, activityName, orderFreightPrice, orderSubsidyFreight, supplierOrderPriceTax, orderPriceTax, orderPayIntegral, orderPayMoney, incomePrice.toString(), incomePriceTax.toString(), orderStateName, returnAmountTime, returnStateName, returnAllMoneyTax, returnAllPriceTax,saleClientName));
                } else {
                    rows.add(ListUtil.toList(orderNumber, supplierOrderNumber, purchaseNumber, companyName, invoiceSubject, applyUserName, createTime, supplierName, orderFreightPrice, orderSubsidyFreight, supplierOrderPriceTax, orderPriceTax, incomePrice.toString(), incomePriceTax.toString(), orderStateName,platformReconciliationWay,saleClientName));
                }
            });

            mergeCells(writer, rows);

            writer.write(rows);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("订单信息.xls", "utf-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        }
    }

    public void exportOrderManagementExcelForDC(ShopOrderQueryPageDto queryDto, HttpServletResponse response) throws IOException {
        try (ExcelWriter writer = ExcelUtil.getWriter(); ServletOutputStream out = response.getOutputStream()) {
            if (StringUtils.isNotBlank(queryDto.getOrderState())) {
                queryDto.setOrderStates(Arrays.asList(queryDto.getOrderState().split(",")));
            }
            if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
                queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
            }
            if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
                queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
            }
            queryDto.setQueryType(TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId()) ? 1 : 0);
            List<ShopOrderPageVo> shopOrderPageVoList = baseMapper.queryOrderList(queryDto);
            if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                writer.writeHeadRow(CollUtil.newArrayList("商城订单号", "供应商订单号", "采购单号", "商户订单号", "企业名称", "开票主体", "开票类型", "申请人", "收货人", "申请时间", "电商名称", "活动名称", "运费", "商城订单金额(总价)", "积分支付金额", "微信支付金额", "订单状态", "退款时间", "退款状态", "个人退款金额", "积分退款金额","订单标记"));
            } else {
                writer.writeHeadRow(CollUtil.newArrayList("商城订单号", "供应商订单号", "采购单号", "企业名称", "开票主体", "申请人", "申请时间", "电商名称", "运费", "商城订单金额(总价)", "订单状态", "结算方式","订单标记"));
            }

            List<List<String>> rows = new ArrayList<>();
            shopOrderPageVoList.forEach(data -> {
                String orderStateName = "";
                switch (data.getOrderState()) {
                    case -1:
                        orderStateName = "订单失败";
                        break;
                    case 0:
                        orderStateName = "已取消";
                        break;
                    case 10:
                        orderStateName = "已提交";
                        break;
                    case 20:
                        orderStateName = "待发货";
                        break;
                    case 30:
                        orderStateName = "待收货";
                        break;
                    case 40:
                        orderStateName = "收货完成";
                        break;
                    case 45:
                        orderStateName = "部分退货";
                        break;
                    case 50:
                        orderStateName = "全部退货";
                        break;
                    default:
                        orderStateName = "";
                        break;
                }

                String returnStateName = "";
                switch (data.getReturnState() == null ? 99 : data.getReturnState()) {
                    case -2:
                        returnStateName = GoodsReturnStateEnum.REPLACEMENT_FAILURE.getValue();
                        break;
                    case -1:
                        returnStateName = GoodsReturnStateEnum.RETURN_OF_FAILURE.getValue();
                        break;
                    case 0:
                        returnStateName = GoodsReturnStateEnum.APPLICATION_FOR_RETURN.getValue();
                        break;
                    case 1:
                        returnStateName = GoodsReturnStateEnum.RETURN_COMPLETE.getValue();
                        break;
                    case 2:
                        returnStateName = GoodsReturnStateEnum.REPLACEMENT_PENDING.getValue();
                        break;
                    case 4:
                        returnStateName = GoodsReturnStateEnum.REPLACEMENT_COMPLETE.getValue();
                        break;
                    case 5:
                        returnStateName = GoodsReturnStateEnum.CANCEL_RETURN.getValue();
                        break;
                    case 10:
                        returnStateName = GoodsReturnStateEnum.INITIATE.getValue();
                        break;
                    case 20:
                        returnStateName = GoodsReturnStateEnum.AUDIT.getValue();
                        break;
                    case 30:
                        returnStateName = GoodsReturnStateEnum.RECEIVING.getValue();
                        break;
                    case 40:
                        returnStateName = GoodsReturnStateEnum.PROCESSING.getValue();
                        break;
                    case 50:
                        returnStateName = GoodsReturnStateEnum.CONFIRMED.getValue();
                        break;
                    default:
                        returnStateName = "";
                        break;
                }

                String saleClientName = "";
                switch (data.getSaleClient() == null ? 99 : data.getSaleClient()) {
                    case 1:
                        saleClientName = "B端";
                        break;
                    case 2:
                        saleClientName = "C端";
                        break;
                    default:
                        saleClientName = "未知";
                        break;
                }

                // 毛利润
                BigDecimal incomePrice = data.getOrderPriceTax().subtract(data.getSupplierOrderPriceTax());
                // 毛利率(%)
                BigDecimal incomePriceTax = incomePrice.divide(data.getOrderPriceTax(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                String purchaseNumber = data.getPurchaseNumber();
                String orderNumber = data.getOrderNumber();
                String supplierOrderNumber = data.getSupplierOrderNumber();
                String payOrderNumber = data.getPayOrderNumber() == null ? "-" : data.getPayOrderNumber();
                String companyName = data.getCompanyName();
                String applyUserName = data.getApplyUserName();
                String addressName = data.getAddressName();
                String invoiceSubject = data.getInvoiceSubject();
                String invoiceTypeName = data.getInvoiceTypeName();
                String activityName = data.getActivityName();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String createTime = sdf.format(data.getCreateTime());
                String supplierName = data.getSupplierName();
                String orderFreightPrice = data.getOrderFreightPrice().toString();
                String orderSubsidyFreight = data.getOrderSubsidyFreight().toString();
                String supplierOrderPriceTax = data.getSupplierOrderPriceTax().toString();
                String orderPriceTax = data.getOrderPriceTax().toString();
                String orderPayIntegral = data.getOrderPayIntegral().toString();
                String orderPayMoney = data.getOrderPayMoney().toString();
                String isPlatformReconciliation = (data.getIsPlatformReconciliation()!=null&& data.getIsPlatformReconciliation()==2)?"平台外结算":"平台内结算";

                if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
                    String returnAllPriceTax = data.getReturnAllPriceTax().toString();
                    String returnAllMoneyTax = data.getReturnAllMoneyTax().toString();
                    String returnAmountTime = data.getReturnAmountTime();
                    rows.add(ListUtil.toList(orderNumber, supplierOrderNumber, purchaseNumber, payOrderNumber, companyName, invoiceSubject, invoiceTypeName, applyUserName, addressName, createTime, supplierName, activityName, orderFreightPrice, orderPriceTax, orderPayIntegral, orderPayMoney, orderStateName, returnAmountTime, returnStateName, returnAllMoneyTax, returnAllPriceTax,saleClientName));
                } else {
                    rows.add(ListUtil.toList(orderNumber, supplierOrderNumber, purchaseNumber, companyName, invoiceSubject, applyUserName, createTime, supplierName, orderFreightPrice, orderPriceTax, orderStateName,isPlatformReconciliation,saleClientName));
                }
            });

            mergeCells(writer, rows);

            writer.write(rows);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("订单信息.xls", "utf-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        }
    }


    /**
     * 合并单元格
     *
     * @param writer
     * @param rows
     */
    private void mergeCells(ExcelWriter writer, List<List<String>> rows) {
        if (!TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            return;
        }
        LinkedHashMap<String, List<List<String>>> map = rows.stream().collect(Collectors.groupingBy(e -> e.get(0), LinkedHashMap::new, Collectors.toList()));
        int index = 1;
        for (String key : map.keySet()) {
            List<List<String>> lists = map.get(key);
            if (lists.size() == 1) {
                index = index + lists.size();
            } else {
                for (int i = 0; i < lists.get(0).size() - 4; i++) {
                    writer.merge(index, index + lists.size() - 1, i, i, null, true);
                }
                index = index + lists.size();
            }
        }
    }

    public List<ShopOrderDetailPageVo> exportOrderDetailsExcel(final ShopOrderDetailQueryPageDto queryDto) {
        final PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final IPage<ShopOrderDetailPageVo> orderDetailPage = this.baseMapper.queryOrderDetailPage(DataAdapter.adapterPageReq(pageReq), queryDto);
        final List<ShopOrderDetailPageVo> exportList = new ArrayList<>();
        orderDetailPage.getRecords().forEach(detailVo -> {
            final ShopOrderDetailPageVo convert = DataAdapter.convert(detailVo, ShopOrderDetailPageVo.class);
            switch (detailVo.getOrderDetailState()) {
                case -1:
                    convert.setOrderDetailStateName("失败");
                    break;
                case 0:
                    convert.setOrderDetailStateName("已取消");
                    break;
                case 10:
                    convert.setOrderDetailStateName("已提交");
                    break;
                case 20:
                    convert.setOrderDetailStateName("待发货");
                    break;
                case 30:
                    convert.setOrderDetailStateName("待收货");
                    break;
                case 40:
                    convert.setOrderDetailStateName("收货完成");
                    break;
                case 45:
                    convert.setOrderDetailStateName("部分退货");
                    break;
                case 50:
                    convert.setOrderDetailStateName("全部退货");
                    break;
                default:
                    convert.setOrderDetailStateName("");
                    break;
            }
            convert.setGrossProfit(detailVo.getGoodsTotalPriceTax().subtract(detailVo.getSupplierTotalPriceTax()));
            convert.setGrossProfitMargin(convert.getGrossProfit()
                    .divide(detailVo.getGoodsTotalPriceTax(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100")));
            exportList.add(convert);
        });
        return exportList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void returnByGoods(final ReturnGoodsDto returnGoodsDto) {
        final Integer returnNum = returnGoodsDto.getReturnNum();
        final Integer type = returnGoodsDto.getServiceType();
        // 订单详情表
        final ShopPurchaseSubOrderDetail purSubOrderDetailEty = this.purchaseSubOrderDetailService.getByOrderIdAndGoodsCode(returnGoodsDto.getOrderId(), returnGoodsDto.getGoodsCode());
        Integer state = checkReturn(purSubOrderDetailEty, type, returnNum);

        // 订单表
        final ShopPurchaseSubOrder purSubOrderEty = this.purchaseSubOrderService.getById(purSubOrderDetailEty.getOrderId());
        final String supplierCode = purSubOrderEty.getSupplierCode();
        if (!supplierCode.equals(this.jdconfig.getCode()) && !supplierCode.equals(this.jdIntegralConfig.getCode())) {
            //目前只有京东商品支持售后功能
            throw new ParameterException("该商品暂不支持售后");
        }

        // 调用供应商退货接口，不通过会抛异常，通过则开始写入退货数据
        final ReturnOrderDto returnOrderDto = new ReturnOrderDto();
        final ReturnOrderDto.GoodsInfo goodsInfo = new ReturnOrderDto.GoodsInfo();
        goodsInfo.setSku(Long.valueOf(purSubOrderDetailEty.getGoodsSku()));
        goodsInfo.setNum(returnNum);
        goodsInfo.setGoodsName(purSubOrderDetailEty.getGoodsName());
        BeanUtil.copyProperties(returnGoodsDto, returnOrderDto);
        returnOrderDto.setReason(returnGoodsDto.getReturnReason());
        returnOrderDto.setYphOrderId(purSubOrderEty.getOrderId());
        returnOrderDto.setGoodsInfo(goodsInfo);
        final String returnCode = this.remoteInfoManage.applyForAfterSale(returnOrderDto, supplierCode);
        // 采购单表
        final QueryWrapper<ShopPurchaseOrder> purQueryWrapper = new QueryWrapper<>();
        purQueryWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purSubOrderEty.getPurchaseNumber());
        final ShopPurchaseOrder purOrderEty = this.shopPurchaseOrderService.getOne(purQueryWrapper);

        // 退货明细表
        final ShopReturnDetail returnDetailEty = new ShopReturnDetail();
        returnDetailEty.setGoodsCode(purSubOrderDetailEty.getGoodsCode());
        returnDetailEty.setGoodsSku(purSubOrderDetailEty.getGoodsSku());
        returnDetailEty.setGoodsDesc(purSubOrderDetailEty.getGoodsDesc());
        returnDetailEty.setGoodsImage(purSubOrderDetailEty.getGoodsImage());
        returnDetailEty.setConfirmNum(new BigDecimal(purSubOrderDetailEty.getConfirmNum()));
        returnDetailEty.setReturnNum(new BigDecimal(returnNum));
        returnDetailEty.setCustomerMobilePhone(returnGoodsDto.getCustomerMobilePhone());
        returnDetailEty.setCustomerContactName(returnGoodsDto.getCustomerContactName());
        returnDetailEty.setServiceType(returnGoodsDto.getServiceType());
        final BigDecimal goodsUnitPriceTax = purSubOrderDetailEty.getGoodsUnitPriceTax();
        final BigDecimal goodsUnitPriceNaked = purSubOrderDetailEty.getGoodsUnitPriceNaked();
        final BigDecimal returnTotalPriceTax = goodsUnitPriceTax.multiply(new BigDecimal(returnNum));
        final BigDecimal returnTotalPriceNaked = goodsUnitPriceNaked.multiply(new BigDecimal(returnNum));
        returnDetailEty.setGoodsUnitPriceTax(goodsUnitPriceTax);
        returnDetailEty.setGoodsUnitPriceNaked(goodsUnitPriceNaked);
        returnDetailEty.setReturnTotalPriceTax(returnTotalPriceTax);
        returnDetailEty.setReturnTotalPriceNaked(returnTotalPriceNaked);
        // 退货表
        final ShopReturn returnEty = new ShopReturn();
        returnEty.setReturnCode(returnCode);
        returnEty.setSupplierCode(supplierCode);
        returnEty.setSupplierName(purSubOrderEty.getSupplierName());
        returnEty.setPurchaseId(purOrderEty.getPurchaseId());
        returnEty.setPurchaseNumber(purOrderEty.getPurchaseNumber());
        if (purOrderEty.getBudgetId() != null) {
            returnEty.setBudgetCode(purOrderEty.getBudgetCode());
            final QueryWrapper<SystemBudget> qw = new QueryWrapper<>();
            qw.lambda().select(SystemBudget::getTypeCode).eq(SystemBudget::getId, purOrderEty.getBudgetId());
            returnEty.setBudgetType(this.budgetService.getOne(qw).getTypeCode());
        }
        returnEty.setApplyUserId(purOrderEty.getApplyUserId());
        returnEty.setApplyUserName(purOrderEty.getApplyUserName());
        returnEty.setOrganizationId(purOrderEty.getOrganizationId());
        returnEty.setCompanyName(purOrderEty.getCompanyName());
        returnEty.setPurchaseCreateTime(purOrderEty.getCreateTime());
        returnEty.setSupplierOrderId(purSubOrderEty.getSupplierOrderNumber());
        returnEty.setOrderId(purSubOrderEty.getOrderId());
        returnEty.setReturnAllNum(new BigDecimal(returnNum));
        returnEty.setReturnAllPriceTax(returnTotalPriceTax);
        returnEty.setReturnAllPriceNaked(returnTotalPriceNaked);
        returnEty.setReturnReason(returnGoodsDto.getReturnReason());
        returnEty.setReturnState(state);

        // 插入数据
        purSubOrderDetailEty.setDetailReturnState(state);
        purSubOrderDetailEty.setModifier(null);
        purSubOrderDetailEty.setUpdateTime(null);
        this.purchaseSubOrderDetailService.updateById(purSubOrderDetailEty);
        this.returnSrv.save(returnEty);
        returnDetailEty.setReturnId(returnEty.getId());
        returnDetailEty.setOrganizationId(purOrderEty.getOrganizationId());
        this.returnDetailSrv.save(returnDetailEty);
    }

    public void exportPurchaseDetailsExcel(final PurchaseOrderQueryDto purchaseOrderQueryDto, final HttpServletResponse response) throws IOException {
        final ExcelWriter writer = ExcelUtil.getWriter();
        final PageReq pageReq = new PageReq();
        pageReq.setPageSize(999);
        final IPage<PurchaseSubOrderDetailVo> iPage = this.purchaseOrderMapper.queryPurchaseOrderPageVos(DataAdapter.adapterPageReq(pageReq), purchaseOrderQueryDto);
        val employees = iPage.getRecords();
        final List<Map<String, Object>> rows = employees.stream().map(item -> {
            switch (item.getOrderDetailState()) {
                case -1:
                    item.setOrderDetailStateName("订单失败");
                    break;
                case 0:
                    item.setOrderDetailStateName("已取消");
                    break;
                case 10:
                    item.setOrderDetailStateName("已提交");
                    break;
                case 20:
                    item.setOrderDetailStateName("待发货");
                    break;
                case 30:
                    item.setOrderDetailStateName("待收货");
                    break;
                case 40:
                    item.setOrderDetailStateName("收货完成");
                    break;
                case 45:
                    item.setOrderDetailStateName("部分退货");
                    break;
                case 50:
                    item.setOrderDetailStateName("全部退货");
                    break;
            }
            if (item.getDetailReturnState() != null) {
                switch (item.getDetailReturnState()) {
                    case -1:
                        item.setDetailReturnStateName("退货失败");
                        break;
                    case 0:
                        item.setDetailReturnStateName("退货申请中");
                        break;
                    case 1:
                        item.setDetailReturnStateName("退货完成");
                        break;
                    case 2:
                        item.setDetailReturnStateName("部分退货");
                        break;
                }
            }
            final Map<String, Object> maps = new HashMap<>();
            maps.put("goodsCode", item.getGoodsCode());
            maps.put("orderDetailStateName", item.getOrderDetailStateName());
            maps.put("goodsDesc", item.getGoodsDesc());
            maps.put("detailReturnStateName", item.getDetailReturnStateName());
            maps.put("goodsUnitPriceTax", item.getGoodsUnitPriceTax());
            maps.put("confirmNum", item.getConfirmNum());
            maps.put("supplierName", item.getSupplierName());
            maps.put("purchaseNumber", item.getPurchaseNumber());
            maps.put("createTime", item.getCreateTime());
            return maps;
        }).collect(Collectors.toList());
        // Header
        writer.addHeaderAlias("goodsCode", "商城编码");
        writer.addHeaderAlias("orderDetailStateName", "状态");
        writer.addHeaderAlias("goodsDesc", "商品描述");
        writer.addHeaderAlias("detailReturnStateName", "售后状态");
        writer.addHeaderAlias("goodsUnitPriceTax", "含税价格");
        writer.addHeaderAlias("confirmNum", "采购数量");
        writer.addHeaderAlias("supplierName", "供应商名称");
        writer.addHeaderAlias("purchaseNumber", "采购申请单号");
        writer.addHeaderAlias("createTime", "申请时间");
        final String fileName = "我的采购明细.xls";
        // Body
        writer.setColumnWidth(0, 30);
        writer.setColumnWidth(1, 30);
        writer.setColumnWidth(2, 30);
        writer.setColumnWidth(3, 30);
        writer.setColumnWidth(4, 30);
        writer.setColumnWidth(5, 30);
        writer.setColumnWidth(6, 30);
        writer.setColumnWidth(7, 30);
        writer.setColumnWidth(8, 30);
        writer.write(rows, true);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        response.setCharacterEncoding("UTF-8");
        final ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }

    /**
     * 保存需求提报，购物车，订单关系
     *
     * @param
     * @param
     */
    private void saveRequirementRelation(final String cartIds, final List<ShopPurchaseSubOrder> shopPurchaseSubOrderList) {
        final List<String> cartIdList = Arrays.asList(cartIds.split(","));
        final QueryWrapper<RequirementRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(RequirementRecord::getCartId, cartIdList);
        final List<RequirementRecord> requirementRecordEntities = this.requirementRecordService.list(queryWrapper);
        if (CollectionUtil.isEmpty(requirementRecordEntities)) {
            return;
        }
        //修改需求提报记录表
        final List<RequirementRecord> updateList = new ArrayList<>();
        final List<ShopRequirement> requirementList = new ArrayList<>();
        shopPurchaseSubOrderList.forEach(itm -> {
            //获取订单详情
            final List<ShopPurchaseSubOrderDetail> orderDetail = this.purchaseSubOrderDetailService.getOrderDetailByOrderId(itm.getOrderId());
            final Map<String, ShopPurchaseSubOrderDetail> orderDetailEntity = orderDetail.stream().collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getGoodsCode, ShopPurchaseSubOrderDetailEntity -> ShopPurchaseSubOrderDetailEntity));

            requirementRecordEntities.forEach(requirementRecord -> {
                //获取这条需求
                final ShopRequirement requirementEntity = this.shopRequirementService.getById(requirementRecord.getRequirementId());
                final ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailEntity.get(requirementEntity.getGoodsCode());
                if (shopPurchaseSubOrderDetail == null) {
                    return;
                }
                requirementRecord.setUpdateTime(new Date());
                requirementRecord.setOrderId(itm.getOrderId());
                requirementRecord.setOrderNumber(itm.getOrderNumber());
                requirementRecord.setPurchaseNumber(itm.getPurchaseNumber());
                final LoginUser user = LocalUserHolder.get();
                requirementRecord.setUpdateUser(user.getId());
                requirementEntity.setOrderId(itm.getOrderId());
                requirementEntity.setOrderNumber(itm.getOrderNumber());
                requirementEntity.setPurchaseNumber(itm.getPurchaseNumber());
                requirementEntity.setHandleTime(new Date());
                requirementEntity.setUpdateTime(new Date());
                requirementEntity.setStatus(2);
                updateList.add(requirementRecord);
                requirementList.add(requirementEntity);
            });
            this.requirementRecordService.updateBatchById(updateList);
            //修改需求提报订单ID
            this.shopRequirementService.updateBatchById(requirementList);
        });
    }

    public void cancelPurchaseOrderJob(Integer timeOutDay) {
        log.info("超时审批流程自动取消任务");
        List<ShopPurchaseOrder> purchaseOrderArray;
        // 获取自定义超期时间的企业
        List<SystemDictDataEntity> companyOrderOverTime = dictDataSrv.getDictDatasByDictType("company_order_over_time");
        if (CollUtil.isNotEmpty(companyOrderOverTime)) {
            List<String> filterCompanyCodes = new ArrayList<>();
            // 按企业配置超期时间处理
            for (SystemDictDataEntity dictDataEntity : companyOrderOverTime) {
                Date overTime = getOverTime(dictDataEntity.getValue());
                // 同一时间企业编码逗号分隔
                String companyCodes = dictDataEntity.getLabel();
                // 遍历相同企业处理
                for(String companyCode : companyCodes.split(",")){
                    filterCompanyCodes.add(companyCode);
                    purchaseOrderArray = purchaseOrderMapper.queryTimeOutOrder(null, companyCode, overTime);
                    toCancelPurchaseOrder(purchaseOrderArray, dictDataEntity.getValue());
                }
            }
            // 对未配置的企业使用默认超期时间处理
            Date overTime = getOverTime(timeOutDay.toString());
            purchaseOrderArray = purchaseOrderMapper.queryTimeOutOrder(filterCompanyCodes, null, overTime);
            toCancelPurchaseOrder(purchaseOrderArray, timeOutDay.toString());
        }else{
            Date overTime = getOverTime(timeOutDay.toString());
            purchaseOrderArray = purchaseOrderMapper.queryTimeOutOrder(null, null, overTime);
            toCancelPurchaseOrder(purchaseOrderArray, timeOutDay.toString());
        }
    }

    private void toCancelPurchaseOrder(List<ShopPurchaseOrder> purchaseOrderArray, String timeOutDay) {
        if (CollUtil.isEmpty(purchaseOrderArray)) {
            return;
        }
        List<ShopPurchaseOrder> updateList = new ArrayList<>();
        for (ShopPurchaseOrder shopPurchaseOrder : purchaseOrderArray) {
            try{
                log.info("超时自动取消订单---{}", shopPurchaseOrder.getPurchaseNumber());
                SystemOrganization systemOrganization = systemOrganizationService.getBaseMapper().selectById(shopPurchaseOrder.getOrganizationId());
                if (openApiConfig.getSuppliers().contains(systemOrganization.getCode())) {
                    continue;
                }

                String purchaseNumber = shopPurchaseOrder.getPurchaseNumber();
                String companyCode = shopPurchaseOrder.getCompanyCode();

                // 废弃外部审批记录
                Boolean isExt = cancelExtApprove(companyCode, purchaseNumber);
                // 不是外部审批走系统取消订单流程
                if (!isExt) {
                    try {
                        this.actTaskFeign.sysCancelTask(purchaseNumber, "采购申请单超过有效期【" + timeOutDay + "天】未审批完成，系统自动取消", shopPurchaseOrder.getTenantId());
                    } catch (Exception e) {
                        log.error("purchaseOrderTimeOutCancelFail:{},失败原因:{}", purchaseNumber, ExceptionUtil.stacktraceToString(e));
                        continue;
                    }
                }
                try {
                    if (shopPurchaseOrder.getSendOrNot() == null) {
                        //增加邮件通知
                        noticeSrv.emailApprovalOver(purchaseNumber, "超时，订单取消");
                        shopPurchaseOrder.setSendOrNot(1);
                        updateList.add(shopPurchaseOrder);
                    }
                } catch (Exception e) {
                    log.error("订单超时未审批短信通知申请人失败,采购单号：{} 申请人id：{}  失败原因：{}", shopPurchaseOrder.getPurchaseNumber(),
                            shopPurchaseOrder.getApplyUserId(), ExceptionUtil.stacktraceToString(e));
                }
                if (CompanyEnum.DFG01.getCompanyCode().equalsIgnoreCase(companyCode)) {
                    dfgOaTodoService.dfg01CancelOrderSetTodoDone(purchaseNumber);
                }
                OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
                UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto = new UpdatePurchaseOrderInfoForConfirmDto();
                updatePurchaseOrderInfoForConfirmDto.setPurchaseNumber(purchaseNumber);
                orderLifeCycleStrategy.updatePurchaseOrderInfoForApprove(updatePurchaseOrderInfoForConfirmDto);
            }catch (Exception e){
                log.error("定时取消采购单报错：{} {}", shopPurchaseOrder.getPurchaseNumber(), e);
            }
        }
        this.updateBatchById(updateList);



    }

    private Date getOverTime(String overDay) {
        long oneDayMillisecond = 24 * 60 * 60 * 1000L;
        BigDecimal limitDay = new BigDecimal(overDay);
        BigDecimal limitTime = limitDay.multiply(new BigDecimal(String.valueOf(oneDayMillisecond)));
        long overTime = System.currentTimeMillis() - limitTime.longValue();
        return new Date(overTime);
    }


    public Boolean cancelExtApprove(String companyCode, String purchaseNumber) {
        // 判断接口平台接入客户
        List<String> customers = openApiConfig.getCustomers();
        if ((CollectionUtil.isNotEmpty(customers) && customers.contains(companyCode))) {

            // 为客户生成废弃采购单消息
            openApiMessageUtil.sendMessage(companyCode, MessageTypeEnum.CUSTOMER_PURCHASE_CANCEL, MapUtil.of("purchaseNumber", purchaseNumber));

            log.info("通知接口平台接入的客户,废弃采购单【" + purchaseNumber + "】");
            //改订单状态
            this.cancelOrder(purchaseNumber, "", "超过供应商约定期限自动驳回");
            //发短信
            noticeSrv.smsApprovalOver(purchaseNumber, "超时，订单取消");
            //增加邮件通知
            noticeSrv.emailApprovalOver(purchaseNumber, "超时，订单取消");
            return true;
        }

        ExtApprove extApprove = extApproveFactory.getExtApprove(companyCode);
        if (!Objects.isNull(extApprove)) {
            //超期废弃外部审批流程
            log.info("开始废弃外部审批记录：purchaseNumber:{},companyCode:{}", purchaseNumber, companyCode);
            extApprove.cancelExtApprove(purchaseNumber);
            //改订单状态
            this.cancelOrder(purchaseNumber, "", "超过供应商约定期限自动驳回");
            //发短信
            noticeSrv.smsApprovalOver(purchaseNumber, "超时，订单取消");
            //增加邮件通知
            noticeSrv.emailApprovalOver(purchaseNumber, "超时，订单取消");
            return true;
        } else {
            return false;
        }
    }

    public Boolean sendExtPurchaseOrderInfo(String companyCode, String purchaseNumber, Integer... dmsFlag) {
        // 判断接口平台接入客户
        List<String> customers = openApiConfig.getCustomers();
        if ((CollectionUtil.isNotEmpty(customers) && customers.contains(companyCode))) {
            // 为客户生成提交采购单消息
            openApiMessageUtil.sendMessage(companyCode, MessageTypeEnum.CUSTOMER_PURCHASE_SUBMIT, MapUtil.of("purchaseNumber", purchaseNumber));

            log.info("接口平台接入的客户,不直接确认采购单【" + purchaseNumber + "】");
            return true;
        }
        // 接入发送订单的外部系统处理
        ExtApprove extApprove = extApproveFactory.getExtApprove(companyCode);
        if (!Objects.isNull(extApprove)) {
            if (dmsFlag.length > 0) {
                if (dmsFlag[0] == 1) {
                    return false;
                }
            }
            //超期废弃外部审批流程
            log.info("开始发送采购单到外部系统审核：purchaseNumber:{},companyCode:{}", purchaseNumber, companyCode);

            extApprove.sendPurchaseOrderInfo(purchaseNumber);
            return true;
        } else {
            return false;
        }
    }

    public Boolean sendDeliveryInfoToSap(List<ReceiptGoodsDto> receiptGoodsDtoList) {
        //获取companyCode,去区分公司，单次收货只可能是一个公司，所以只用查询第一条
        if (CollectionUtil.isEmpty(receiptGoodsDtoList)) {
            return false;
        }
        ShopDeliveryDetail shopDeliveryDetail = deliveryDetailSrv.getById(receiptGoodsDtoList.get(0).getId());
        SystemOrganization systemOrganization = systemOrganizationService.findCompanyByOrganizationId(shopDeliveryDetail.getOrganizationId());
        String companyCode = systemOrganization.getCode();
        ExtDelivery extDelivery = extDeliveryFactory.getExtDelivery(companyCode);
        if (!Objects.isNull(extDelivery)) {
            //将收货信息发送至外部SAP
            log.info("发送收货信息到外部SAP系统：receiptGoodsDto:{},companyCode:{}", receiptGoodsDtoList.toString(), companyCode);
            extDelivery.sendDeliveryInfoToSap(receiptGoodsDtoList);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 特供神龙使用
     *
     * @param deliveryCode
     * @return
     */
    public Boolean sendDeliveryInfoToDPCASap(String deliveryCode, String supplierCode) {
        log.info("【人工运维处理神龙发货单】包裹单号：" + deliveryCode + "；供应商编码：" + supplierCode);
        //获取companyCode,去区分公司，单次收货只可能是一个公司，所以只用查询第一条
        if (StringUtils.isEmpty(deliveryCode)) {
            throw new ParameterException("参数异常！");
        }
        List<ShopDelivery> deliveries = deliverySrv.list(new LambdaQueryWrapperX<ShopDelivery>().eq(ShopDelivery::getSupplierCode, supplierCode).eq(ShopDelivery::getDeliveryCode, deliveryCode));
        if (CollUtil.isEmpty(deliveries)) {
            throw new ParameterException("根据参数未查询到包裹信息！");
        }
        if (CollUtil.size(deliveries) > 1) {
            throw new ParameterException("根据参数查询到多个包裹信息，请联系管理员！");
        }
        List<ShopDeliveryDetail> detailByDeliveryId = deliveryDetailSrv.getDetailByDeliveryId(deliveries.get(0).getId());
        if (CollectionUtil.isEmpty(detailByDeliveryId)) {
            throw new ParameterException("包裹明细异常，请联系管理员！");
        }
        List<ReceiptGoodsDto> receiptGoodsDtoList = Lists.newArrayList();
        for (ShopDeliveryDetail shopDeliveryDetail : detailByDeliveryId) {
            ReceiptGoodsDto receiptGoodsDto = new ReceiptGoodsDto();
            receiptGoodsDto.setId(shopDeliveryDetail.getId());
            receiptGoodsDto.setReceiptNum(shopDeliveryDetail.getReceivingNum());
            receiptGoodsDtoList.add(receiptGoodsDto);
        }
        SystemOrganization systemOrganization = systemOrganizationService.findCompanyByOrganizationId(detailByDeliveryId.get(0).getOrganizationId());
        String companyCode = systemOrganization.getCode();
        if (!CompanyEnum.DPCA.getCompanyCode().equalsIgnoreCase(companyCode)) {
            throw new ParameterException("只允许补发神龙公司发货单");
        }
        ExtDelivery extDelivery = extDeliveryFactory.getExtDelivery(companyCode);
        if (!Objects.isNull(extDelivery)) {
            //将收货信息发送至外部SAP
            log.info("发送收货信息到外部SAP系统：receiptGoodsDto:{},companyCode:{}", receiptGoodsDtoList.toString(), companyCode);
            extDelivery.sendDeliveryInfoToSap(receiptGoodsDtoList);
            return true;
        } else {
            return false;
        }
    }

    public void myOrderDetailGoodsExport(final String orderNumber, final HttpServletResponse response) throws IOException {
        try (final ExcelWriter writer = ExcelUtil.getWriter(); final ServletOutputStream out = response.getOutputStream()) {
            final List<PurchaseSubOrderDetailVo> orderGoodsVos = this.purchaseSubOrderDetailService.queryListBySubOrderNumber(orderNumber);
            writer.writeHeadRow(CollUtil.newArrayList("商品描述", "商品编码", "订货编码", "商品单价", "购买数量", "金额行小计", "商品SKU", "供应商"));
            final List<List<String>> rows = new ArrayList<>();
            orderGoodsVos.forEach(data -> {
                // 商品描述
                final String goodsDesc = data.getGoodsDesc();
                // 商品编码
                final String goodsCode = data.getGoodsCode();
                // 订货编码
                final String goodsSku = data.getGoodsSku();
                // 商品单价
                final String goodsUnitPrice = String.valueOf(data.getGoodsUnitPriceTax());
                // 购买数量
                final String confirmNum = data.getConfirmNumDecimal().add(new BigDecimal(data.getConfirmNum())).toString();
                // 金额行小计
                final String goodsTotalPrice = String.valueOf(data.getGoodsTotalPriceTax());

                final String sku = String.valueOf(data.getGoodsSku());

                final String supplierName = String.valueOf(data.getSupplierName());

                rows.add(ListUtil.toList(goodsDesc, goodsCode, goodsSku, goodsUnitPrice, confirmNum, goodsTotalPrice, sku, supplierName));
            });
            writer.write(rows);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("订单商品导出.xls", "utf-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        }
    }

    public ShopOrderAddress selectOrderAddress(final String purchaseNumber) {
        //获取订单中地址id
        final ShopPurchaseOrderVo shopPurchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseNumber);
        if (shopPurchaseOrderVo == null) {
            throw new ParameterException("采购单不存在");
        }
        return this.shopOrderAddressService.queryByPurchaseId(shopPurchaseOrderVo.getPurchaseId());
    }

    public PageResp<T> getOftenBuyList(final PageReq pageReq, final OftenBuyListDto oftenBuyListDto) {
        final List<OftenBuyListVo> oftenBuyVos = this.getOftenBuy(oftenBuyListDto);
        if (oftenBuyVos.size() <= 0) {
            return new PageResp<T>((long) oftenBuyVos.size());
        }
        int end = pageReq.getCurPage() * pageReq.getPageSize();
        if (end > oftenBuyVos.size()) {
            end = oftenBuyVos.size();
        }
        final List<OftenBuyListVo> oftenBuyListVos = oftenBuyVos.subList(pageReq.getCurPage() - 1, end);
        final PageResp<T> pageResp = new PageResp<>();
        pageResp.setCurPage(pageReq.getCurPage());
        pageResp.setPages(new Double(Math.ceil((double) oftenBuyVos.size() / pageReq.getPageSize())).longValue());
        pageResp.setPageSize(pageReq.getPageSize());
        pageResp.setCount(oftenBuyVos.size());
        final Object vo = (Object) oftenBuyListVos;
        final List<T> results = (List<T>) vo;
        pageResp.setData(results);
        return pageResp;
    }

    public List<OftenBuyListVo> getOftenBuy(final OftenBuyListDto oftenBuyListDto) {
        final List<OftenBuyListVo> oftenBuyListVoList = new ArrayList<>();
        oftenBuyListDto.setSupplierCodes(StrUtil.splitTrim(oftenBuyListDto.getSupplierCode(), ','));
        final List<OftenBuyListVo> voList = this.purchaseOrderMapper.getOftenBuyList(oftenBuyListDto);
        if (voList.size() <= 0) {
            return oftenBuyListVoList;
        }
        final Map<String, List<OftenBuyListVo>> listMap = voList.stream().filter(item -> StringUtils.isNotBlank(item.getGoodsCode())).collect(Collectors.groupingBy(OftenBuyListVo::getGoodsCode));
        listMap.keySet().forEach(key -> {
            final OftenBuyListVo oftenBuyListVo = new OftenBuyListVo();
            BeanUtil.copyProperties(listMap.get(key).get(0), oftenBuyListVo);
            //同一商品采买次数
            oftenBuyListVo.setBuyTheCount(listMap.get(key).size());
            //购买数量总和
            BigDecimal buyTotal = new BigDecimal(0);
            //含税销售价格合计
            BigDecimal totalPrice = new BigDecimal(0);
            for (final OftenBuyListVo oftenBuyVo : listMap.get(key)) {
                buyTotal = buyTotal.add(oftenBuyVo.getBuyNum());
                totalPrice = totalPrice.add(oftenBuyVo.getGoodsTotalPriceTax());
            }

            //采买数量
            oftenBuyListVo.setBuyTheNumber(buyTotal);
            oftenBuyListVo.setAveragePrice(totalPrice.divide(buyTotal, 2, BigDecimal.ROUND_HALF_UP));

            ShopSupplier shopSupplier = shopSupplierService.selectByCode(oftenBuyListVo.getSupplierCode());
            if ("srm".equals(shopSupplier.getDataSource())) {
                oftenBuyListVo.setAveragePrice(totalPrice.divide(buyTotal, 4, BigDecimal.ROUND_HALF_UP));
            }

            oftenBuyListVoList.add(oftenBuyListVo);
        });
        //按采买次数降序
        oftenBuyListVoList.sort(Comparator.comparing(OftenBuyListVo::getBuyTheCount).reversed());
        return oftenBuyListVoList;
    }

    public void oftenBuyListExport(final HttpServletResponse response, final OftenBuyListDto oftenBuyListDto) throws IOException {
        try (final ExcelWriter writer = ExcelUtil.getWriter(); final ServletOutputStream out = response.getOutputStream()) {
            final List<OftenBuyListVo> oftenBuyList = this.getOftenBuy(oftenBuyListDto);
            writer.writeHeadRow(CollUtil.newArrayList("电商SKU", "商品编码", "商品描述", "历史均价", "采买次数", "采买数量", "供应商"));
            final List<List<String>> rows = new ArrayList<>();
            oftenBuyList.forEach(data -> {
                rows.add(ListUtil.toList(data.getGoodsSku(), data.getGoodsCode(), data.getGoodsDesc(), String.valueOf(data.getAveragePrice()), String.valueOf(data.getBuyTheCount()), String.valueOf(data.getBuyTheNumber()), data.getSupplierName()));
            });
            writer.write(rows);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("常购清单.xls", "utf-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        }
    }

    public ShopPurchaseOrder queryPurOrderByPurNumber(final String purchaseNumber) {
        return this.baseMapper.queryPurOrderByPurNumber(purchaseNumber);
    }

    public ShopPurchaseOrder queryPurOrderByOtherRelationPurNumber(final String otherRelationNumber) {
        return this.baseMapper.queryPurOrderByOtherRelationPurNumber(otherRelationNumber);
    }

    public List<ShopPurchaseOrder> getPurOrderByBudgetId(Long budgetId) {
        return this.baseMapper.getPurOrderByBudgetId(budgetId);
    }

    //发送短信
    private void sendMsg(ShopPurchaseSubOrder purchaseSubOrder, String purchaseNumber) {
        if (purchaseSubOrder.getOrderState() == 20) {
            try {
                ShopSupplier shopSupplier = shopSupplierService.selectByCode(purchaseSubOrder.getSupplierCode());
                JsonArray jsonArray = new JsonParser().parse(shopSupplier.getOrderPeople()).getAsJsonArray();
                if (jsonArray != null && jsonArray.size() > 0) {
                    JsonObject jsonObject = jsonArray.get(0).getAsJsonObject();
                    String phone = jsonObject.get("phone").toString().substring(1, jsonObject.get("phone").toString().length() - 1);
                    if (StringUtils.isNotBlank(phone)) {
                        log.info("确认订单成功，开始发送短信,订单号：{}", purchaseSubOrder.getOrderNumber());
                        Map<String, Object> templateParams = new HashMap<>(2);
                        ShopPurchaseOrderVo purchaseOrderVo = this.purchaseOrderMapper.queryPurchaseOrderVoByNumber(purchaseNumber);
                        templateParams.put("companyName", purchaseOrderVo.getCompanyName());
                        templateParams.put("orderNo", purchaseSubOrder.getOrderNumber());
                        // 短信功能已切换联麓,这些模板当前禁用状态,若需启用需重新开发
//                        smsSrv.sendSingleSmsToAdmin(phone, null, ORDER_CONFIRM, templateParams);
                    }
                }
            } catch (Exception e) {
                log.error("确认订单短信发送失败，商城订单号：{}", purchaseSubOrder.getOrderNumber());
                e.printStackTrace();
            }
        }
    }

    public PageResp<MallOrderQueryVo> queryMallOrderPage(PageReq pageReq, MallOrderQueryDto queryDto) {
        Set<Long> organizations = systemUsersService.getOrganizationCondition(queryDto.getEntityOrganizationId());
        queryDto.setSupplierCodes(StrUtil.splitTrim(queryDto.getSupplierCode(), ','));
        IPage<MallOrderQueryVo> pageVo = this.baseMapper.queryMallOrderPage(DataAdapter.adapterPageReq(pageReq), queryDto, organizations);
        pageVo.getRecords().forEach(itm -> {
            JSONArray afterSalesPeople = JSONArray.parseArray(itm.getAfterSalesPeople());
            if (CollectionUtil.isNotEmpty(afterSalesPeople)) {
                JSONObject jsonObject = JSONObject.parseObject(afterSalesPeople.get(0).toString());
                itm.setAfterSalesPerson(jsonObject.getString("name"));
                itm.setAfterSalesTel(jsonObject.getString("phone"));
                itm.setAfterSalesPeople(null);
            }
        });
        return DataAdapter.adapterPage(pageVo, MallOrderQueryVo.class);
    }

    public List<MyOrderFrontExcelVo> exportMallOrder(MallOrderQueryDto queryDto) {
        Set<Long> organizations = systemUsersService.getOrganizationCondition(queryDto.getEntityOrganizationId());
        queryDto.setSupplierCodes(StrUtil.splitTrim(queryDto.getSupplierCode(), ','));
        return baseMapper.exportMallOrder(queryDto, organizations);
    }


    /**
     * 查询采购单相关信息汇总
     *
     * @param purchaseNumber
     * @return
     */
    public PurchaseOrderInfoCollectDto getPurchaseOrderInfoCollect(String purchaseNumber) {
        PurchaseOrderInfoCollectDto purchaseOrderInfoCollectDto = new PurchaseOrderInfoCollectDto();
        QueryWrapper<ShopPurchaseOrder> shopPurchaseOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseOrderQueryWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
        ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.getOne(shopPurchaseOrderQueryWrapper);

        QueryWrapper<ShopPurchaseSubOrder> shopPurchaseSubOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderQueryWrapper.lambda()
                .eq(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumber)
                .gt(ShopPurchaseSubOrder::getOrderState, 0);
        List<ShopPurchaseSubOrder> shopPurchaseSubOrderList = purchaseSubOrderService.list(shopPurchaseSubOrderQueryWrapper);
        if (CollectionUtil.isEmpty(shopPurchaseSubOrderList)) {
            log.info("采购单【" + purchaseNumber + "】：全部订单已取消");
            purchaseOrderInfoCollectDto.setShopPurchaseOrder(shopPurchaseOrder);
            return purchaseOrderInfoCollectDto;
        }
        List<String> orderNumberList = shopPurchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());

        QueryWrapper<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderDetailQueryWrapper.lambda().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList)
                .gt(ShopPurchaseSubOrderDetail::getOrderDetailState, 0);
        List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList = purchaseSubOrderDetailService.list(shopPurchaseSubOrderDetailQueryWrapper);
        Map<String/*orderNumber*/, List<ShopPurchaseSubOrderDetail>> shopPurchaseSubOrderDetailMap = shopPurchaseSubOrderDetailList.stream().collect(Collectors.groupingBy(ShopPurchaseSubOrderDetail::getOrderNumber));
        List<String> brandIds = shopPurchaseSubOrderDetailList.stream().map(ShopPurchaseSubOrderDetail::getBrandId).collect(Collectors.toList());
        QueryWrapper<ShopBrand> shopBrandQueryWrapper = new QueryWrapper<>();
        shopBrandQueryWrapper.lambda().in(ShopBrand::getBrandId, brandIds);
        List<ShopBrand> shopBrandList = shopBrandService.list(shopBrandQueryWrapper);
        if (CollectionUtil.isNotEmpty(shopBrandList)) {
            Map<Long, ShopBrand> shopBrandMap = shopBrandList.stream().collect(Collectors.toMap(ShopBrand::getBrandId, Function.identity()));
            purchaseOrderInfoCollectDto.setShopBrandMap(shopBrandMap);
        }

        ShopOrderAddress shopOrderAddress = shopOrderAddressService.queryByPurchaseId(shopPurchaseOrder.getPurchaseId());
        SystemBudget systemBudget = budgetService.getById(shopPurchaseOrder.getBudgetId());
        purchaseOrderInfoCollectDto.setSystemBudget(systemBudget);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderDetailMap(shopPurchaseSubOrderDetailMap);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderDetailList(shopPurchaseSubOrderDetailList);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderList(shopPurchaseSubOrderList);
        purchaseOrderInfoCollectDto.setShopPurchaseOrder(shopPurchaseOrder);
        purchaseOrderInfoCollectDto.setShopOrderAddress(shopOrderAddress);
        return purchaseOrderInfoCollectDto;
    }

    public PurchaseOrderInfoCollectDto getPurchaseOrderInfoCollectHasCancel(String purchaseNumber) {
        PurchaseOrderInfoCollectDto purchaseOrderInfoCollectDto = new PurchaseOrderInfoCollectDto();
        QueryWrapper<ShopPurchaseOrder> shopPurchaseOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseOrderQueryWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
        ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.getOne(shopPurchaseOrderQueryWrapper);

        QueryWrapper<ShopPurchaseSubOrder> shopPurchaseSubOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderQueryWrapper.lambda()
                .eq(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumber);
        List<ShopPurchaseSubOrder> shopPurchaseSubOrderList = purchaseSubOrderService.list(shopPurchaseSubOrderQueryWrapper);
        List<String> orderNumberList = shopPurchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());

        QueryWrapper<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderDetailQueryWrapper.lambda().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList);
        List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList = purchaseSubOrderDetailService.list(shopPurchaseSubOrderDetailQueryWrapper);
        Map<String/*orderNumber*/, List<ShopPurchaseSubOrderDetail>> shopPurchaseSubOrderDetailMap = shopPurchaseSubOrderDetailList.stream().collect(Collectors.groupingBy(ShopPurchaseSubOrderDetail::getOrderNumber));
        List<String> brandIds = shopPurchaseSubOrderDetailList.stream().map(ShopPurchaseSubOrderDetail::getBrandId).collect(Collectors.toList());
        QueryWrapper<ShopBrand> shopBrandQueryWrapper = new QueryWrapper<>();
        shopBrandQueryWrapper.lambda().in(ShopBrand::getBrandId, brandIds);
        List<ShopBrand> shopBrandList = shopBrandService.list(shopBrandQueryWrapper);
        if (CollectionUtil.isNotEmpty(shopBrandList)) {
            Map<Long, ShopBrand> shopBrandMap = shopBrandList.stream().collect(Collectors.toMap(ShopBrand::getBrandId, Function.identity()));
            purchaseOrderInfoCollectDto.setShopBrandMap(shopBrandMap);
        }
        ShopOrderAddress shopOrderAddress = shopOrderAddressService.queryByPurchaseId(shopPurchaseOrder.getPurchaseId());
        SystemBudget systemBudget = budgetService.getById(shopPurchaseOrder.getBudgetId());
        purchaseOrderInfoCollectDto.setSystemBudget(systemBudget);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderDetailMap(shopPurchaseSubOrderDetailMap);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderDetailList(shopPurchaseSubOrderDetailList);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderList(shopPurchaseSubOrderList);
        purchaseOrderInfoCollectDto.setShopPurchaseOrder(shopPurchaseOrder);
        purchaseOrderInfoCollectDto.setShopOrderAddress(shopOrderAddress);
        return purchaseOrderInfoCollectDto;
    }

    public PurchaseOrderInfoCollectDto getPurchaseOrderInfoCollectByDhec(String purchaseNumber) {
        PurchaseOrderInfoCollectDto purchaseOrderInfoCollectDto = new PurchaseOrderInfoCollectDto();
        QueryWrapper<ShopPurchaseOrder> shopPurchaseOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseOrderQueryWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
        ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.getOne(shopPurchaseOrderQueryWrapper);

        QueryWrapper<ShopPurchaseSubOrder> shopPurchaseSubOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderQueryWrapper.lambda()
                .eq(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumber)
                .gt(ShopPurchaseSubOrder::getOrderState, 0);
        List<ShopPurchaseSubOrder> shopPurchaseSubOrderList = purchaseSubOrderService.list(shopPurchaseSubOrderQueryWrapper);
        ShopOrderAddress shopOrderAddress = shopOrderAddressService.queryByPurchaseId(shopPurchaseOrder.getPurchaseId());
        SystemBudget systemBudget = budgetService.getById(shopPurchaseOrder.getBudgetId());
        if (CollectionUtil.isEmpty(shopPurchaseSubOrderList)) {
            log.info("采购单【" + purchaseNumber + "】：全部订单已取消");
            purchaseOrderInfoCollectDto.setShopPurchaseOrder(shopPurchaseOrder);
            purchaseOrderInfoCollectDto.setSystemBudget(systemBudget);
            purchaseOrderInfoCollectDto.setShopOrderAddress(shopOrderAddress);
            return purchaseOrderInfoCollectDto;
        }
        List<String> orderNumberList = shopPurchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());

        QueryWrapper<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderDetailQueryWrapper.lambda().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList)
                .gt(ShopPurchaseSubOrderDetail::getOrderDetailState, 0);
        List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList = purchaseSubOrderDetailService.list(shopPurchaseSubOrderDetailQueryWrapper);
        Map<String/*orderNumber*/, List<ShopPurchaseSubOrderDetail>> shopPurchaseSubOrderDetailMap = shopPurchaseSubOrderDetailList.stream().collect(Collectors.groupingBy(ShopPurchaseSubOrderDetail::getOrderNumber));
        List<String> brandIds = shopPurchaseSubOrderDetailList.stream().map(ShopPurchaseSubOrderDetail::getBrandId).collect(Collectors.toList());
        QueryWrapper<ShopBrand> shopBrandQueryWrapper = new QueryWrapper<>();
        shopBrandQueryWrapper.lambda().in(ShopBrand::getBrandId, brandIds);
        List<ShopBrand> shopBrandList = shopBrandService.list(shopBrandQueryWrapper);
        if (CollectionUtil.isNotEmpty(shopBrandList)) {
            Map<Long, ShopBrand> shopBrandMap = shopBrandList.stream().collect(Collectors.toMap(ShopBrand::getBrandId, Function.identity()));
            purchaseOrderInfoCollectDto.setShopBrandMap(shopBrandMap);
        }

        purchaseOrderInfoCollectDto.setSystemBudget(systemBudget);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderDetailMap(shopPurchaseSubOrderDetailMap);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderDetailList(shopPurchaseSubOrderDetailList);
        purchaseOrderInfoCollectDto.setShopPurchaseSubOrderList(shopPurchaseSubOrderList);
        purchaseOrderInfoCollectDto.setShopPurchaseOrder(shopPurchaseOrder);
        purchaseOrderInfoCollectDto.setShopOrderAddress(shopOrderAddress);
        return purchaseOrderInfoCollectDto;
    }


    public List<String> getOtherRelationNumberRank(String relationNumber) {
        return purchaseOrderMapper.getOtherRelationNumberRank(relationNumber);
    }

    /**
     * 查询履约信息
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@code PageResp<MallOrderQueryVo>}
     */
    public PageResp<AgreementQueryVo> queryAgreement(PageReq pageReq, AgreementQueryDto queryDto) {
        Set<Long> organizations = null;
        if (queryDto.getEntityOrganizationId() != null) {
            organizations = systemUsersService.getOrganizationCondition(queryDto.getEntityOrganizationId());
        }
        // Retrieve all performance data from database by organization condition.
        IPage<AgreementQueryVo> pageVo = null;
        if (queryDto.getState().equals(30)) {
            pageVo = this.baseMapper.queryAgreement(DataAdapter.adapterPageReq(pageReq), queryDto, organizations);
        } else if (queryDto.getState().equals(20)) {
            pageVo = this.baseMapper.queryAgreementDelivered(DataAdapter.adapterPageReq(pageReq), queryDto, organizations);
        }

        //填充全称
        List<AgreementQueryVo> shopOrderPageVos = pageVo.getRecords();
        List<String> supplierCodes = shopOrderPageVos.stream().map(AgreementQueryVo::getSupplierCode).collect(Collectors.toList());
        Map<String, String> supplierMap = querySupplierFullName(supplierCodes);
        for (AgreementQueryVo shopOrderPageVo : shopOrderPageVos) {
            shopOrderPageVo.setSupplierFullName(supplierMap.get(shopOrderPageVo.getSupplierCode()));
        }
        pageVo.setRecords(shopOrderPageVos);

        if (pageVo.getRecords().size() > 0) {
            // Get all order numbers
            List<String> orderList = pageVo.getRecords().stream().map(AgreementQueryVo::getOrderNumber).collect(Collectors.toList());
            // Get order confirm datetime form approval record.
            QueryApprovalTimeDto queryApprovalTimeDto = new QueryApprovalTimeDto();
            queryApprovalTimeDto.setDeployKey("A00501").setOrderList(orderList).setTenantId(TenantContextHolder.getTenantId());
            // You can refer to the detailed code:
            // https://devcloud.szlanyou.com/gitlab/lyshopcloud/lylcyyph/-/blob/9b7a02da52abe7505cce2da98c9aed078c0d0b93/workflow/src/main/java/com/ly/yph/workflow/service/ActivitiTaskService.java#L339-L341
            ServiceResult<List<QueryApprovalTimeVo>> approvalTimeVo = actTaskFeign.queryApprovalTime(queryApprovalTimeDto);

//            List<QueryApprovalTimeVo> approvalData = approvalTimeVo.getData();
//            Map<String, Integer> approvalStatus = approvalData.stream().collect(Collectors.toMap(QueryApprovalTimeVo::getOrderId, QueryApprovalTimeVo::getInstanceState));
//            Map<String, Date> approvalTime = approvalData.stream().collect(Collectors.toMap(QueryApprovalTimeVo::getOrderId, QueryApprovalTimeVo::getUpdateTime));
            // Set the order time.
            pageVo.getRecords().forEach(itm -> {
                // Historical data, it may contain order confirm date.
                if (itm.getAuditTime() == null) {
                    // Determine whether the approval data can be retrieved. if there is no approval data, the order time will be confirmed order time.
//                    if (approvalStatus.get(itm.getOrderNumber()) == null) {
//                        itm.setAuditTime(itm.getSubmissionTime());
//                    } else {
//                        // Approved.
//                        if (approvalStatus.get(itm.getOrderNumber()) == 0) {
//                            itm.setAuditTime(approvalTime.get(itm.getOrderNumber()));
//                        }
//                    }
                    itm.setAuditTime(itm.getSubmissionTime());
                }
                // Set the delivery date.
                if (itm.getDeliveryDate() == null || itm.getDeliveryDate() == 0) {
                    itm.setDeliveryDate(7);
                }
                // Set requested delivery date.
                if (itm.getAuditTime() != null) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(itm.getAuditTime());
                    cal.add(Calendar.DATE, itm.getDeliveryDate());
                    itm.setRequiredDeliveryTime(cal.getTime());
                }
                // Set the number of overdue days.
                if (itm.getSupReceivingTime() == null && itm.getRequiredDeliveryTime() != null) {
                    long s = itm.getRequiredDeliveryTime().getTime() - System.currentTimeMillis();
                    TimeUnit time = TimeUnit.DAYS;
                    long overdue = time.convert(s, TimeUnit.MILLISECONDS);
                    itm.setSurplusDays(overdue >= 0 ? String.valueOf(overdue) : "已超期");
                    if ("已超期".equals(itm.getSurplusDays())) {
                        itm.setExceedDays(Math.abs(overdue));
                    }
                    //有妥投时间
                } else if (itm.getSupReceivingTime() != null && itm.getRequiredDeliveryTime() != null) {
                    long s = itm.getRequiredDeliveryTime().getTime() - itm.getSupReceivingTime().getTime();
                    TimeUnit time = TimeUnit.DAYS;
                    long overdue = time.convert(s, TimeUnit.MILLISECONDS);
                    if (overdue < 0) {
                        itm.setExceedDays((Math.abs(overdue)));
                        itm.setSurplusDays("已超期");
                    }
                }
                // Supplier package delivered ,but the user did not receive it.
                // 1. Subtract the delivery time from the current time.
                // 2. Set not receive day with the result.
                if (itm.getSupReceivingTime() != null) {
                    long s = System.currentTimeMillis() - itm.getSupReceivingTime().getTime();
                    TimeUnit time = TimeUnit.DAYS;
                    long days = time.convert(s, TimeUnit.MILLISECONDS);
                    itm.setNotReceiveDay(days >= 0 ? days : 0);
                }
            });
        }
        return DataAdapter.adapterPage(pageVo, AgreementQueryVo.class);
    }

    public PageResp<AgreementQueryVo> goodsToBeShipped(PageReq pageReq, AgreementQueryDto queryDto) {
        return this.queryAgreement(pageReq, queryDto);
    }

    /**
     * 查询履约管理-发货提醒列表
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@code PageResp<MallOrderQueryVo>}
     */
    public PageResp<AgreementQueryVo> queryAgreementDelivery(PageReq pageReq, AgreementQueryDto queryDto) {
        Set<Long> organizations = null;
        if (queryDto.getEntityOrganizationId() != null) {
            organizations = systemUsersService.getOrganizationCondition(queryDto.getEntityOrganizationId());
        }
        IPage<AgreementQueryVo> pageVo = null;
        //OrganizationTypeEnum  1 供应商 2 采购商 3 运营
        Integer organizationType = LocalUserHolder.get().getOrganizationType();
        if (1 == organizationType) {
            String organizationCode = LocalUserHolder.get().getOrganizationCode();
            queryDto.setSupplierCode(organizationCode);
            pageVo = this.baseMapper.queryAgreementDelivery(DataAdapter.adapterPageReq(pageReq), queryDto, null);
        } else {
            String username = LocalUserHolder.get().getUsername();
            SystemDictDataEntity dd = dictDataSrv.getDictData("proxy_receipt_package", username);
            if (dd != null) {
                String companyArray = dd.getLabel().split("#")[1];
                List<String> companyCodeList = null;
                if (!"all".equals(companyArray)) {
                    companyCodeList = StrUtil.splitTrim(companyArray, ",");
                }
                if (StringUtils.isEmpty(queryDto.getCompanyCode())) {
                    queryDto.setCompanyCodeList(companyCodeList);
                }
            }
            pageVo = this.baseMapper.queryAgreementDelivery(DataAdapter.adapterPageReq(pageReq), queryDto, null);
        }
        //填充全称
        List<AgreementQueryVo> shopOrderPageVos = pageVo.getRecords();
        List<String> supplierCodes = shopOrderPageVos.stream().map(AgreementQueryVo::getSupplierCode).collect(Collectors.toList());
        Map<String, String> supplierMap = querySupplierFullName(supplierCodes);
        for (AgreementQueryVo shopOrderPageVo : shopOrderPageVos) {
            shopOrderPageVo.setSupplierFullName(supplierMap.get(shopOrderPageVo.getSupplierCode()));
        }
        pageVo.setRecords(shopOrderPageVos);
        if (pageVo.getRecords().size() > 0) {
            // Get all order numbers
            List<String> orderList = pageVo.getRecords().stream().map(AgreementQueryVo::getOrderNumber).collect(Collectors.toList());
            // Get order confirm datetime form approval record.
            QueryApprovalTimeDto queryApprovalTimeDto = new QueryApprovalTimeDto();
            queryApprovalTimeDto.setDeployKey("A00501").setOrderList(orderList).setTenantId(TenantContextHolder.getTenantId());
            pageVo.getRecords().forEach(itm -> {
                // Historical data, it may contain order confirm date.
                if (itm.getAuditTime() == null) {
                    itm.setAuditTime(itm.getSubmissionTime());
                }
                // Set the delivery date.
                if (itm.getDeliveryDate() == null || itm.getDeliveryDate() == 0) {
                    itm.setDeliveryDate(7);
                }
                // Set requested delivery date.
                if (itm.getAuditTime() != null) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(itm.getAuditTime());
                    cal.add(Calendar.DATE, itm.getDeliveryDate());
                    itm.setRequiredDeliveryTime(cal.getTime());
                }
                // Set the number of overdue days.
                if (itm.getSupReceivingTime() == null && itm.getRequiredDeliveryTime() != null) {
                    long s = itm.getRequiredDeliveryTime().getTime() - System.currentTimeMillis();
                    TimeUnit time = TimeUnit.DAYS;
                    long overdue = time.convert(s, TimeUnit.MILLISECONDS);
                    itm.setSurplusDays(overdue >= 0 ? String.valueOf(overdue) : "已超期");
                    if ("已超期".equals(itm.getSurplusDays())) {
                        itm.setExceedDays(Math.abs(overdue));
                    }
                    //有妥投时间
                } else if (itm.getSupReceivingTime() != null && itm.getRequiredDeliveryTime() != null) {
                    long s = itm.getRequiredDeliveryTime().getTime() - itm.getSupReceivingTime().getTime();
                    TimeUnit time = TimeUnit.DAYS;
                    long overdue = time.convert(s, TimeUnit.MILLISECONDS);
                    if (overdue < 0) {
                        itm.setExceedDays((Math.abs(overdue)));
                        itm.setSurplusDays("已超期");
                    }
                }
                if (itm.getSupReceivingTime() != null) {
                    long s = System.currentTimeMillis() - itm.getSupReceivingTime().getTime();
                    TimeUnit time = TimeUnit.DAYS;
                    long days = time.convert(s, TimeUnit.MILLISECONDS);
                    itm.setNotReceiveDay(days >= 0 ? days : 0);
                }
            });
        }
        return DataAdapter.adapterPage(pageVo, AgreementQueryVo.class);
    }

    /**
     * 查询履约管理-妥投提醒列表
     *
     * @param pageReq  页面请求
     * @param queryDto 查询dto
     * @return {@code PageResp<MallOrderQueryVo>}
     */
    public PageResp<AgreementQueryVo> queryAgreementReceiving(PageReq pageReq, AgreementQueryDto queryDto) {
        Set<Long> organizations = null;
        if (queryDto.getEntityOrganizationId() != null) {
            organizations = systemUsersService.getOrganizationCondition(queryDto.getEntityOrganizationId());
        }
        // Retrieve all performance data from database by organization condition.
        IPage<AgreementQueryVo> pageVo = null;
        //OrganizationTypeEnum  1 供应商 2 采购商 3 运营
        Integer organizationType = LocalUserHolder.get().getOrganizationType();
        if (1 == organizationType) {
            String organizationCode = LocalUserHolder.get().getOrganizationCode();
            queryDto.setSupplierCode(organizationCode);
            pageVo = this.baseMapper.queryAgreementReceiving(DataAdapter.adapterPageReq(pageReq), queryDto, null);
        } else {
            String username = LocalUserHolder.get().getUsername();
            SystemDictDataEntity dd = dictDataSrv.getDictData("proxy_receipt_package", username);
            if (dd != null) {
                String companyArray = dd.getLabel().split("#")[1];
                List<String> companyCodeList = null;
                if (!"all".equals(companyArray)) {
                    companyCodeList = StrUtil.splitTrim(companyArray, ",");
                }
                if (StringUtils.isEmpty(queryDto.getCompanyCode())) {
                    queryDto.setCompanyCodeList(companyCodeList);
                }
            }
            pageVo = this.baseMapper.queryAgreementReceiving(DataAdapter.adapterPageReq(pageReq), queryDto, null);
        }
        //填充全称
        List<AgreementQueryVo> shopOrderPageVos = pageVo.getRecords();
        List<String> supplierCodes = shopOrderPageVos.stream().map(AgreementQueryVo::getSupplierCode).collect(Collectors.toList());
        Map<String, String> supplierMap = querySupplierFullName(supplierCodes);
        for (AgreementQueryVo shopOrderPageVo : shopOrderPageVos) {
            shopOrderPageVo.setSupplierFullName(supplierMap.get(shopOrderPageVo.getSupplierCode()));
        }
        pageVo.setRecords(shopOrderPageVos);

        if (pageVo.getRecords().size() > 0) {
            // Get all order numbers
            List<String> orderList = pageVo.getRecords().stream().map(AgreementQueryVo::getOrderNumber).collect(Collectors.toList());
            // Get order confirm datetime form approval record.
            QueryApprovalTimeDto queryApprovalTimeDto = new QueryApprovalTimeDto();
            queryApprovalTimeDto.setDeployKey("A00501").setOrderList(orderList).setTenantId(TenantContextHolder.getTenantId());
            // You can refer to the detailed code:
            pageVo.getRecords().forEach(itm -> {
                List<PurchaseSubOrderDetailVo> purchaseSubOrderDetailVos = this.baseMapper.queryGoodsListBySubOrderNumber(itm.getOrderNumber(), itm.getDeliveryCode());
                itm.setPurchaseSubOrderDetailList(purchaseSubOrderDetailVos);
                // Historical data, it may contain order confirm date.
                if (itm.getAuditTime() == null) {
                    itm.setAuditTime(itm.getSubmissionTime());
                }
                // Set the delivery date.
                if (itm.getDeliveryDate() == null || itm.getDeliveryDate() == 0) {
                    itm.setDeliveryDate(7);
                }
                // Set requested delivery date.
                if (itm.getAuditTime() != null) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(itm.getAuditTime());
                    cal.add(Calendar.DATE, itm.getDeliveryDate());
                    itm.setRequiredDeliveryTime(cal.getTime());
                }
                // Set the number of overdue days.
                if (itm.getSupReceivingTime() == null && itm.getRequiredDeliveryTime() != null) {
                    long s = itm.getRequiredDeliveryTime().getTime() - System.currentTimeMillis();
                    TimeUnit time = TimeUnit.DAYS;
                    long overdue = time.convert(s, TimeUnit.MILLISECONDS);
                    itm.setSurplusDays(overdue >= 0 ? String.valueOf(overdue) : "已超期");
                    if ("已超期".equals(itm.getSurplusDays())) {
                        itm.setExceedDays(Math.abs(overdue));
                    }
                    //有妥投时间
                } else if (itm.getSupReceivingTime() != null && itm.getRequiredDeliveryTime() != null) {
                    long s = itm.getRequiredDeliveryTime().getTime() - itm.getSupReceivingTime().getTime();
                    TimeUnit time = TimeUnit.DAYS;
                    long overdue = time.convert(s, TimeUnit.MILLISECONDS);
                    if (overdue < 0) {
                        itm.setExceedDays((Math.abs(overdue)));
                        itm.setSurplusDays("已超期");
                    }
                }
                // Supplier package delivered ,but the user did not receive it.
                // 1. Subtract the delivery time from the current time.
                // 2. Set not receive day with the result.
                if (itm.getSupReceivingTime() != null) {
                    long s = System.currentTimeMillis() - itm.getSupReceivingTime().getTime();
                    TimeUnit time = TimeUnit.DAYS;
                    long days = time.convert(s, TimeUnit.MILLISECONDS);
                    itm.setNotReceiveDay(days >= 0 ? days : 0);
                }
            });
        }
        return DataAdapter.adapterPage(pageVo, AgreementQueryVo.class);
    }

    public Map<String, Long> queryUnshippedGoods(AgreementQueryDto queryDto) {
        PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        PageResp<AgreementQueryVo> resultVo = this.goodsToBeShipped(pageReq, queryDto);
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("undeliveredNumber", resultVo.getCount());
        Long count = 0L;
        for (AgreementQueryVo datum : resultVo.getData()) {
            if (datum.getExceedDays() != null) {
                count++;
            }
        }
        resultMap.put("timeoutNumber", count);
        return resultMap;
    }

    public Map<String, Long> queryUnreceivedGoods(AgreementQueryDto queryDto) {
        PageReq pageReq = new PageReq();
        pageReq.setPageSize(99999);
        PageResp<AgreementQueryVo> resultVo = this.goodsToBeShipped(pageReq, queryDto);
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("notReceivedByCustomers", resultVo.getCount());
        Long count = 0L;
        for (AgreementQueryVo datum : resultVo.getData()) {
            if (datum.getNotReceiveDay() != null && datum.getNotReceiveDay() > 7) {
                count++;
            }
        }
        resultMap.put("timeoutNumber", count);
        return resultMap;
    }

    //切换jd账号,调用这个方法注意,会清除标识
    public void flConfirmReceive(final Long tenantId, final String supplierOrderId, final String orderNumber, final String supplierCode) {
        if (this.yflYamlConfig.getTenantId().equals(tenantId)) {
            //电商确认收货
            this.remoteInfoManage.confirmReceive(supplierOrderId, orderNumber, supplierCode);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void returnBatchByGoods(final ReturnBatchGoodsDto returnBatchGoodsDto) {
        final String orderId = returnBatchGoodsDto.getOrderId();
        final Long deliveryId = returnBatchGoodsDto.getDeliveryId();
        final List<ReturnBatchGoodsDto.ReturnGoodsInfo> returnGoodsInfoList = returnBatchGoodsDto.getReturnGoodsInfoList();
        final List<String> goodsCodeList = returnGoodsInfoList.stream().map(ReturnBatchGoodsDto.ReturnGoodsInfo::getGoodsCode).collect(Collectors.toList());
        final int returnAllNum = returnGoodsInfoList.stream().mapToInt(ReturnBatchGoodsDto.ReturnGoodsInfo::getWareNum).sum();
        final List<ShopPurchaseSubOrderDetail> listByOrderIdAndGoodsCodes = purchaseSubOrderDetailService.getListByOrderIdAndGoodsCodes(orderId, goodsCodeList);
        if (CollectionUtil.isEmpty(listByOrderIdAndGoodsCodes)) {
            throw new ParameterException("未找到售后商品");
        }
        ShopDelivery shopDelivery = deliverySrv.getById(deliveryId);
        if (shopDelivery == null) {
            throw new HttpException("包裹不存在或未找到");
        }
        final Map<String, ReturnBatchGoodsDto.ReturnGoodsInfo> map = returnGoodsInfoList.stream().collect(Collectors.toMap(ReturnBatchGoodsDto.ReturnGoodsInfo::getGoodsCode, ReturnGoodsInfo -> ReturnGoodsInfo));

        final List<ReturnOrderReq.ApplyInfoItemOpenReq> list = new ArrayList<>();
        final List<ShopReturnDetailSaveDto> shopReturnDetailSaveDtoList = new ArrayList<>();
        listByOrderIdAndGoodsCodes.forEach(e -> {
            final String goodsCode = e.getGoodsCode();
            final ReturnBatchGoodsDto.ReturnGoodsInfo returnGoodsInfo = map.get(goodsCode);
            if (returnGoodsInfo == null) {
                throw new ParameterException("未找到" + goodsCode + "售后商品");
            }
            final Integer wareNum = returnGoodsInfo.getWareNum();
            final Integer state = this.checkReturn(e, wareNum, returnGoodsInfo.getWareType());
            final ReturnOrderReq.ApplyInfoItemOpenReq applyInfoItemOpenReq = new ReturnOrderReq.ApplyInfoItemOpenReq();

            final ReturnOrderReq.WareDescInfoOpenReq wareDescInfoOpenReq = DataAdapter.convert(returnGoodsInfo, ReturnOrderReq.WareDescInfoOpenReq.class);

            final ReturnOrderReq.WareDetailInfoOpenReq wareDetailInfoOpenReq = DataAdapter.convert(returnGoodsInfo, ReturnOrderReq.WareDetailInfoOpenReq.class);
            wareDetailInfoOpenReq.setMainWareId(Long.valueOf(e.getGoodsSku()))
                    .setWareId(Long.valueOf(e.getGoodsSku()))
                    .setWareName(returnGoodsInfo.getGoodsName());
            applyInfoItemOpenReq.setCustomerExpect(returnGoodsInfo.getCustomerExpect())
                    .setWareDescInfoOpenReq(wareDescInfoOpenReq)
                    .setWareDetailInfoOpenReq(wareDetailInfoOpenReq);
            list.add(applyInfoItemOpenReq);

            final ShopReturnDetailSaveDto shopReturnDetailSaveDto = new ShopReturnDetailSaveDto();
            shopReturnDetailSaveDto.setGoodsCode(goodsCode)
                    .setGoodsSku(e.getGoodsSku())
                    .setGoodsDesc(e.getGoodsDesc())
                    .setGoodsImage(e.getGoodsImage())
                    .setConfirmNum(new BigDecimal(e.getConfirmNum()))
                    .setReturnNum(new BigDecimal(wareNum))
                    .setGoodsUnitPriceTax(e.getGoodsUnitPriceTax())
                    .setGoodsUnitPriceNaked(e.getGoodsUnitPriceNaked())
                    .setReturnTotalPriceTax(e.getGoodsTotalPriceTax().multiply(new BigDecimal(wareNum)))
                    .setReturnTotalPriceNaked(e.getGoodsUnitPriceNaked().multiply(new BigDecimal(wareNum)))
                    .setOrganizationId(e.getOrganizationId())
                    .setCustomerContactName(returnBatchGoodsDto.getCustomerName())
                    .setCustomerMobilePhone(returnBatchGoodsDto.getPhone())
                    .setServiceType(returnGoodsInfo.getCustomerExpect())
                    .setRemark(returnGoodsInfo.getQuestionDesc());
            shopReturnDetailSaveDtoList.add(shopReturnDetailSaveDto);

            e.setDetailReturnState(state).setUpdateTime(null).setModifier(null);
            this.purchaseSubOrderDetailService.updateById(e);
        });

        final ShopPurchaseSubOrder purSubOrderEty = this.purchaseSubOrderService.getById(orderId);
        final String supplierCode = purSubOrderEty.getSupplierCode();
        if (!supplierCode.equals(this.jdconfig.getCode()) && !supplierCode.equals(this.jdIntegralConfig.getCode())) {
            throw new ParameterException("该商品暂不支持售后");
        }
        final ShopPurchaseOrder shopPurchaseOrder = shopPurchaseOrderService.queryPurOrderByPurNumber(purSubOrderEty.getPurchaseNumber());

        String returnCode = this.orderNumberGenerator.make();
        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            returnCode += "Y";
        } else {
            returnCode += "D";
        }

        final ReturnOrderReq returnOrderReq = this.conversionParameter(returnBatchGoodsDto, supplierCode, list, returnCode, shopDelivery.getPackageId());
        final ShopReturnSaveDto shopReturnSaveDto = new ShopReturnSaveDto();
        shopReturnSaveDto.setReturnCode(returnCode)
                .setDeliveryId(deliveryId)
                .setIsShow(1)
                .setSupplierCode(supplierCode)
                .setSupplierName(purSubOrderEty.getSupplierName())
                .setSupplierOrderId(purSubOrderEty.getSupplierOrderNumber())
                .setCompanyName(shopPurchaseOrder.getCompanyName())
                .setPurchaseCreateTime(purSubOrderEty.getCreateTime())
                .setReturnType(yflYamlConfig.getTenantId().equals(purSubOrderEty.getTenantId()) ? 1 : 0)
                .setReturnFreightState(0)
                .setReturnReason(returnGoodsInfoList.get(0).getQuestionDesc())
                .setApplyUserId(shopPurchaseOrder.getApplyUserId())
                .setApplyUserName(shopPurchaseOrder.getApplyUserName())
                .setOrderId(purSubOrderEty.getOrderId())
                .setPurchaseNumber(purSubOrderEty.getPurchaseNumber())
                .setReturnAllNum(new BigDecimal(returnAllNum))
                .setPurchaseOrderDetailList(shopReturnDetailSaveDtoList);
        ArrayList<ShopReturnSaveDto> saveList = new ArrayList<>();
        saveList.add(shopReturnSaveDto);
        returnSrv.returnSave(saveList);
        this.remoteInfoManage.applyForAfterSaleByGoodsList(returnOrderReq, supplierCode);
    }

    private ReturnOrderReq conversionParameter(final ReturnBatchGoodsDto returnBatchGoodsDto, final String supplierCode, final List<ReturnOrderReq.ApplyInfoItemOpenReq> list, final String returnCode, final String packageId) {
        //转换地址信息
        final Long pickAddressId = returnBatchGoodsDto.getPickAddressId();
        final Long returnAddressId = returnBatchGoodsDto.getReturnAddressId();
        final ShopAddress pickShopAddress = this.shopAddressService.getById(pickAddressId);
        final ShopAddress returnShopAddress = this.shopAddressService.getById(returnAddressId);
        final String pickShopAddressInfo = pickShopAddress.getProvince() + pickShopAddress.getCity() + pickShopAddress.getDistrict() + (StrUtil.isBlank(pickShopAddress.getStreet()) ? "" : pickShopAddress.getStreet()) + pickShopAddress.getAddress();
        final String returnShopAddressInfo = returnShopAddress.getProvince() + returnShopAddress.getCity() + returnShopAddress.getDistrict() + (StrUtil.isBlank(returnShopAddress.getStreet()) ? "" : returnShopAddress.getStreet()) + returnShopAddress.getAddress();
        final SupplierAreaResp pickSupplierAreaResp = this.remoteInfoManage.queryAreaInfo(pickShopAddressInfo, supplierCode);
        final SupplierAreaResp returnSupplierAreaResp = this.remoteInfoManage.queryAreaInfo(returnShopAddressInfo, supplierCode);


        final ReturnOrderReq returnOrderReq = new ReturnOrderReq();
        final ReturnOrderReq.CustomerInfoOpenReq customerInfoOpenReq = DataAdapter.convert(returnBatchGoodsDto, ReturnOrderReq.CustomerInfoOpenReq.class);
        customerInfoOpenReq.setCustomerMobilePhone(returnBatchGoodsDto.getPhone()).setCustomerContactName(returnBatchGoodsDto.getCustomerName());
        final ReturnOrderReq.PickupWareInfoOpenReq pickupWareInfoOpenReq = DataAdapter.convert(returnBatchGoodsDto, ReturnOrderReq.PickupWareInfoOpenReq.class);
        pickupWareInfoOpenReq.setPickWareProvince(pickSupplierAreaResp.getProvinceId())
                .setPickWareCounty(pickSupplierAreaResp.getCountyId())
                .setPickWareCity(pickSupplierAreaResp.getCityId())
                .setPickWareAddress(pickShopAddressInfo);
        Optional.ofNullable(pickSupplierAreaResp.getTownId()).ifPresent(s -> {
            pickupWareInfoOpenReq.setPickWareVillage(pickSupplierAreaResp.getTownId());
        });
        final ReturnOrderReq.ReturnWareInfoOpenReq returnWareInfoOpenReq = DataAdapter.convert(returnBatchGoodsDto, ReturnOrderReq.ReturnWareInfoOpenReq.class);
        returnWareInfoOpenReq.setReturnWareProvince(returnSupplierAreaResp.getProvinceId())
                .setReturnWareCountry(returnSupplierAreaResp.getCountyId())
                .setReturnWareCity(returnSupplierAreaResp.getCityId())
                .setReturnWareAddress(returnShopAddressInfo);
        Optional.ofNullable(returnSupplierAreaResp.getTownId()).ifPresent(s -> {
            returnWareInfoOpenReq.setReturnWareVillage(returnSupplierAreaResp.getTownId());
        });
        final ReturnOrderReq.ApplyAfterSaleOpenReq applyAfterSaleOpenReq = new ReturnOrderReq.ApplyAfterSaleOpenReq();

        applyAfterSaleOpenReq.setThirdApplyId(returnCode)
                .setIsHasInvoice(returnBatchGoodsDto.getIsHasInvoice())
                .setOrderId(Long.valueOf(packageId))
                .setCustomerInfoVo(customerInfoOpenReq)
                .setPickupWareInfoOpenReq(pickupWareInfoOpenReq)
                .setReturnWareInfoOpenReq(returnWareInfoOpenReq)
                .setApplyInfoItemOpenReqList(list);
        returnOrderReq.setApplyAfterSaleOpenReq(applyAfterSaleOpenReq);
        return returnOrderReq;
    }

    private Integer checkReturn(final ShopPurchaseSubOrderDetail purSubOrderDetailEty, final Integer returnNum, final Integer type) {
        if (purSubOrderDetailEty == null) {
            throw new ParameterException("订单详情不存在");
        }

        Integer state = 0;
        /** 退货中或退货完成状态，不能进行退货操作   同种商品可多次售后,取消校验
         if (type == 10) {
         if (purSubOrderDetailEty.getDetailReturnState() != null && (purSubOrderDetailEty.getDetailReturnState() == 0 || purSubOrderDetailEty.getDetailReturnState() == 1 || purSubOrderDetailEty.getDetailReturnState() == 3)) {
         throw new ParameterException("当前状态不允许退货");
         }
         }
         */
        // 换货中中或换货完成状态，不能进行换货操作
        if (type == 20) {
            state = 3;
            if (purSubOrderDetailEty.getDetailReturnState() != null && (purSubOrderDetailEty.getDetailReturnState() == 0 || purSubOrderDetailEty.getDetailReturnState() == 1 || purSubOrderDetailEty.getDetailReturnState() == 3 || purSubOrderDetailEty.getDetailReturnState() == 4)) {
                throw new ParameterException("当前状态不允许换货");
            }
        }

        // 退货数量不能大于（订货数量-已退货数量）
        int oldReturnNum = 0;
        if (purSubOrderDetailEty.getDetailReturnState() != null && purSubOrderDetailEty.getDetailReturnState() == 2) {
            oldReturnNum = oldReturnNum + this.returnDetailSrv.queryOldReturnNum(purSubOrderDetailEty.getOrderId(), purSubOrderDetailEty.getGoodsCode());
        }
        if (returnNum > purSubOrderDetailEty.getConfirmNum() - oldReturnNum) {
            throw new ParameterException(StrUtil.format("退货数量不允许大于订货数量（{}）-已退货数量（{}）", purSubOrderDetailEty.getConfirmNum(), oldReturnNum));
        }
        return state;
    }

    public ShopReturnDeliveryInfoVo queryShopReturnDeliveryInfo(String orderNumber) {
        final ShopPurchaseSubOrderVo purchaseSubOrderVo = this.purchaseSubOrderService.querySubOrderVo(orderNumber);
        if (purchaseSubOrderVo == null) {
            throw new HttpException("订单不存在");
        }
        ShopReturnDeliveryInfoVo shopReturnDeliveryInfoVo = DataAdapter.convert(purchaseSubOrderVo, ShopReturnDeliveryInfoVo.class);
        final List<PurchaseSubOrderDetailVo> orderGoodsVos = this.purchaseSubOrderDetailService.queryListBySubOrderNumber(orderNumber);
        Map<String, PurchaseSubOrderDetailVo> goodsMap = CollectionUtils.convertMap(orderGoodsVos, PurchaseSubOrderDetailVo::getGoodsCode);

        BigDecimal confirmNumDecimal = orderGoodsVos.stream().map(PurchaseSubOrderDetailVo::getConfirmNumDecimal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal confirmNum = new BigDecimal(orderGoodsVos.stream().mapToLong(PurchaseSubOrderDetailVo::getConfirmNum).sum())
                .add(confirmNumDecimal);
        BigDecimal deliveryNumDecimal = orderGoodsVos.stream().map(PurchaseSubOrderDetailVo::getDeliveryNumDecimal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal deliverNum = new BigDecimal(orderGoodsVos.stream().mapToLong(PurchaseSubOrderDetailVo::getDeliveryNum).sum())
                .add(deliveryNumDecimal);
        // 查询发货单信息
        final List<SupplierOrderDeliveryVo> subOrderDeliveryVoList = this.shopDeliveryMapper.queryAllDeliveryInfoByOrder(purchaseSubOrderVo.getOrderId());
        List<ShopReturnDeliveryVo> shopReturnDeliveryVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(subOrderDeliveryVoList)) {
            shopReturnDeliveryVos = DataAdapter.convertList(subOrderDeliveryVoList, ShopReturnDeliveryVo.class);
            for (ShopReturnDeliveryVo delivery : shopReturnDeliveryVos) {
                List<SupplierOrderDeliveryGoodsVo> deliveryGoodsVoList = shopDeliveryDetailMapper.queryDeliveryGoodsByPackageId(delivery.getId());
                //查询退货单信息
                List<ShopReturnDetailVo> shopReturnDetailVos = returnSrv.returnDetailInfoByDeliveryId(Long.valueOf(delivery.getId()));
                Map<String, ShopReturnDetailVo> map = CollectionUtils.convertMap(shopReturnDetailVos, ShopReturnDetailVo::getGoodsCode);
                List<ShopReturnDeliveryGoodsVo> shopReturnDeliveryGoodsVos = new ArrayList<>();
                deliveryGoodsVoList.forEach(data -> {
                    PurchaseSubOrderDetailVo purchaseSubOrderDetailVo = goodsMap.get(data.getGoodsCode());
                    if (purchaseSubOrderDetailVo == null) {
                        return;
                    }
                    shopReturnDeliveryGoodsVos.add(convertDetail(purchaseSubOrderDetailVo, map));
                });

                delivery.setShopReturnDeliveryGoodsVos(shopReturnDeliveryGoodsVos);
            }
        }

        //订单存在还未发货的明细
        if (deliverNum.compareTo(confirmNum) < 0) {
            ShopReturnDeliveryVo shopReturnDeliveryVo = new ShopReturnDeliveryVo();
            List<ShopReturnDeliveryGoodsVo> shopReturnDeliveryGoodsVos = new ArrayList<>();
            List<ShopReturnDetailVo> shopReturnDetailVos = returnSrv.returnDetailInfoByOrderNumber(orderNumber);
            Map<String, ShopReturnDetailVo> map = CollectionUtils.convertMap(shopReturnDetailVos, ShopReturnDetailVo::getGoodsCode);
            //获取未发货的商品明细
            for (PurchaseSubOrderDetailVo orderGoodsVo : orderGoodsVos) {
                if (orderGoodsVo.getConfirmNum() > orderGoodsVo.getDeliveryNum()
                        || orderGoodsVo.getConfirmNumDecimal().compareTo(orderGoodsVo.getDeliveryNumDecimal()) > 0) {
                    //未发货完全
                    shopReturnDeliveryGoodsVos.add(convertDetail(orderGoodsVo, map));
                }
            }
            shopReturnDeliveryVo.setShopReturnDeliveryGoodsVos(shopReturnDeliveryGoodsVos);
            shopReturnDeliveryVos.add(shopReturnDeliveryVo);
        }
        shopReturnDeliveryInfoVo.setShopReturnDeliveryVos(shopReturnDeliveryVos);
        return shopReturnDeliveryInfoVo;
    }

    public ShopReturnDeliveryGoodsVo convertDetail(PurchaseSubOrderDetailVo purchaseSubOrderDetailVo, Map<String, ShopReturnDetailVo> map) {
        ShopReturnDeliveryGoodsVo shopReturnDeliveryGoodsVo = DataAdapter.convert(purchaseSubOrderDetailVo, ShopReturnDeliveryGoodsVo.class);
        ShopReturnDetailVo shopReturnDetailVo = map.get(shopReturnDeliveryGoodsVo.getGoodsCode());
        if (shopReturnDetailVo != null) {
            shopReturnDeliveryGoodsVo.setReturnNum(shopReturnDetailVo.getReturnNum());
            shopReturnDeliveryGoodsVo.setReturnMoney(shopReturnDetailVo.getReturnTotalMoneyTax());
            shopReturnDeliveryGoodsVo.setReturnPrice(shopReturnDetailVo.getReturnTotalPriceTax());
        }
        return shopReturnDeliveryGoodsVo;
    }

    public void approachingExpirationJob(Integer timeOutDay) {
        if (openApiConfig.getSuppliers().contains(OrganizationCodeContextHolder.getOrganizationCode())) {
            return;
        }

        log.info("临近超期订单通知任务");
        final List<ShopPurchaseOrder> purchaseOrderArray = purchaseOrderMapper.getApproachingExpirationOrder(timeOutDay);
        if (purchaseOrderArray.size() == 0) {
            return;
        }

        purchaseOrderArray.forEach(shopPurchaseOrder -> {
            try {
                //增加邮件通知
                noticeSrv.emailApproachingExpiration(shopPurchaseOrder.getPurchaseNumber());
            } catch (Exception e) {
                log.error("订单邻近超时未审批邮件通知需求人,审批人失败,采购单号：{} 申请人id：{}  失败原因：{}", shopPurchaseOrder.getPurchaseNumber(),
                        shopPurchaseOrder.getApplyUserId(), ExceptionUtil.stacktraceToString(e));
            }
        });
    }

    /**
     * 查询东风南方需要预付款的采购单
     */
    public List<String> queryDfsPayPurchaseNumber(String purchaseNumber) {
        return purchaseOrderMapper.queryDfsPayPurchaseNumber(purchaseNumber);
    }
    /**
     * 查询东风南方电商需要预付款的采购单
     */
    public List<String> queryDsDfsPayPurchaseNumber(String srmDataSource) {
        return purchaseOrderMapper.queryDsDfsPayPurchaseNumber(srmDataSource);
    }

    public List<ShopPurchaseSubOrderVo> queryOrderInfo(final String purchaseNumber) {
        final List<ShopPurchaseSubOrderVo> list = baseMapper.queryOrderVo(purchaseNumber);
        if (CollectionUtil.isEmpty(list)) {
            throw new HttpException("订单不存在");
        }

        for (ShopPurchaseSubOrderVo purchaseSubOrderVo : list) {
            final List<PurchaseSubOrderDetailVo> orderGoodsVos = this.purchaseSubOrderDetailService.queryListBySubOrderNumber(purchaseSubOrderVo.getOrderNumber());
            purchaseSubOrderVo.setOrderDetailList(orderGoodsVos);

            // 查询发货单信息
            final List<SupplierOrderDeliveryVo> subOrderDeliveryVoList = this.shopDeliveryMapper.queryDeliveryInfoByOrder(purchaseSubOrderVo.getOrderId());
            subOrderDeliveryVoList.forEach(delivery -> {
                final List<SupplierOrderDeliveryGoodsVo> deliveryGoodsVoList = this.shopDeliveryDetailMapper.queryDeliveryGoodsByPackageId(delivery.getId());
                delivery.setDeliveryGoodsVoList(deliveryGoodsVoList);
            });
            purchaseSubOrderVo.setSubOrderDeliveryVoList(subOrderDeliveryVoList);

            //查询退货单信息
            List<ShopReturnDetailVo> shopReturnDetailVos = returnSrv.returnDetailInfoByOrderNumber(purchaseSubOrderVo.getOrderNumber());
            if (CollectionUtil.isNotEmpty(shopReturnDetailVos)) {
                Map<String, ShopReturnDetailVo> map = shopReturnDetailVos.stream().collect(Collectors.toMap(ShopReturnDetailVo::getGoodsCode, ShopReturnDetailVo -> ShopReturnDetailVo));
                purchaseSubOrderVo.getOrderDetailList().forEach(data -> {
                    ShopReturnDetailVo shopReturnDetailVo = map.get(data.getGoodsCode());
                    if (shopReturnDetailVo != null) {
                        data.setReturnNum(shopReturnDetailVo.getReturnNum());
                        data.setReturnMoney(shopReturnDetailVo.getReturnTotalMoneyTax());
                        data.setReturnPrice(shopReturnDetailVo.getReturnTotalPriceTax());
                    }
                });
            }
        }
        return list;
    }

    public PageResp<ShopOrderDetailPageVo> querySubOrderDetailPageForDC(PageReq pageReq, ShopOrderDetailQueryPageDto queryDto) {
        if (StringUtils.isNotBlank(queryDto.getSupplierCode())) {
            queryDto.setSupplierCodes(Arrays.asList(queryDto.getSupplierCode().split(",")));
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyCode())) {
            queryDto.setCompanyCodes(Arrays.asList(queryDto.getCompanyCode().split(",")));
        }
        final IPage<ShopOrderDetailPageVo> orderDetailPage = this.baseMapper.queryOrderDetailPage(DataAdapter.adapterPageReq(pageReq), queryDto);
        return DataAdapter.adapterPage(orderDetailPage, ShopOrderDetailPageVo.class);
    }

    /**
     * 根据采购单查询所有商品明细
     *
     * @param purchaseNumberList
     * @return
     */
    public Map<String, List<ShopPurchaseSubOrderDetail>> queryOrderDetailByPurchaseNumber(List<String> purchaseNumberList) {
        if (CollectionUtil.isEmpty(purchaseNumberList)) {
            return Maps.newHashMap();
        }
        QueryWrapper<ShopPurchaseSubOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumberList).gt(ShopPurchaseSubOrder::getOrderState, 0);
        List<ShopPurchaseSubOrder> purchaseOrderList = purchaseSubOrderService.list(queryWrapper);
        if (CollectionUtil.isEmpty(purchaseOrderList)) {
            return Maps.newHashMap();
        }
        List<String> orderNumberList = purchaseOrderList.stream().map(item -> item.getOrderNumber()).collect(Collectors.toList());
        QueryWrapper<ShopPurchaseSubOrderDetail> detailQuery = new QueryWrapper<>();
        detailQuery.lambda().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList).gt(ShopPurchaseSubOrderDetail::getOrderDetailState, 0);
        List<ShopPurchaseSubOrderDetail> detailList = purchaseSubOrderDetailService.list(detailQuery);
        return detailToPurchaseMap(purchaseOrderList, detailList);
    }

    /**
     * 根据采购单查询所有商品明细
     *
     * @param orderNumberList
     * @return
     */
    public Map<String, List<ShopPurchaseSubOrderDetail>> queryOrderDetailByOrderNumber(List<String> orderNumberList) {
        if (CollectionUtil.isEmpty(orderNumberList)) {
            return Maps.newHashMap();
        }
        QueryWrapper<ShopPurchaseSubOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ShopPurchaseSubOrder::getOrderNumber, orderNumberList).gt(ShopPurchaseSubOrder::getOrderState, 0);
        List<ShopPurchaseSubOrder> purchaseOrderList = purchaseSubOrderService.list(queryWrapper);
        if (CollectionUtil.isEmpty(purchaseOrderList)) {
            return Maps.newHashMap();
        }
        QueryWrapper<ShopPurchaseSubOrderDetail> detailQuery = new QueryWrapper<>();
        detailQuery.lambda().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList).gt(ShopPurchaseSubOrderDetail::getOrderDetailState, 0);
        List<ShopPurchaseSubOrderDetail> detailList = purchaseSubOrderDetailService.list(detailQuery);
        return detailToPurchaseMap(purchaseOrderList, detailList);
    }

    private Map<String, List<ShopPurchaseSubOrderDetail>> detailToPurchaseMap(List<ShopPurchaseSubOrder> purchaseOrderList, List<ShopPurchaseSubOrderDetail> detailList) {
        Map<String, List<ShopPurchaseSubOrder>> subOrderMap = purchaseOrderList.stream().collect(Collectors.groupingBy(ShopPurchaseSubOrder::getPurchaseNumber));
        Map<String, List<ShopPurchaseSubOrderDetail>> detailMap = Maps.newHashMap();
        for (Map.Entry<String, List<ShopPurchaseSubOrder>> stringListEntry : subOrderMap.entrySet()) {
            List<String> orderNumber = stringListEntry.getValue().stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());
            detailMap.put(stringListEntry.getKey(), detailList.stream().filter(item -> orderNumber.contains(item.getOrderNumber())).collect(Collectors.toList()));
        }
        return detailMap;
    }

    public void updatePurchaseOrderTypeBySap(String purchaseNumber) {
        UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber)
                .set(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber)
                .set(ShopPurchaseOrder::getSapOrderType,Constant.SAP_ORDER_TYPE_THREE)
                .set(ShopPurchaseOrder::getIsExtReceipt,Constant.EXT_RECEIPT);
        this.update(updateWrapper);
    }

    public void sendExtPurchaseOrderInfoToDhec(String params) {
        dhecService.sendExtPurchaseOrderInfoToDhec(params);
    }


    public Set<OrderSettlementRemindersDto> orderSettlementReminders(List<String> goodsCodes) {
        List<ShopGoodsSupplierVo> goodsSupplierVos = shopGoodsService.queryShopGoodsSupplierByGoodCodes(goodsCodes);
        if (CollectionUtil.isEmpty(goodsSupplierVos)) {
            throw new HttpException("商品不存在");
        }
        String organizationCode = LocalUserHolder.get().getEntityOrganizationCode();
        //获取独立供应商与电商信息
        Map<String, List<ShopGoodsSupplierVo>> supplierTypeMap = goodsSupplierVos.stream().collect(Collectors.groupingBy(ShopGoodsSupplierVo::getDataSource));
        Set<OrderSettlementRemindersDto> dt = new TreeSet<>();
        supplierTypeMap.keySet().forEach(x -> {
            //电商处理 全部付款单位为同一单位  联友智连科技有限公司 但需要区分是否营销单位
            if (x.equals(SupplierDataSourceEnum.DFMALL.getName())) {
                //营销单位匹配 营销单位默认结算方式为：预付 非营销单位默认结算方式为：月结
                String settleMentMethod = customizeDfsProperty.getMarketCompanyCode().stream()
                        .anyMatch(m -> m.equals(organizationCode)) ? "预付" : "月结";

                OrderSettlementRemindersDto dto = OrderSettlementRemindersDto.builder()
                        .goodsCodes(supplierTypeMap.get(x).stream()
                                .map(ShopGoodsSupplierVo::getGoodsCode)
                                .collect(Collectors.joining(",")))
                        .settleMentMethod(settleMentMethod)
                        .settleSupplierName("联友智连科技有限公司")
                        .dataSource(x)
                        .supplierCode(organizationCode)
                        .build();
                dt.add(dto);
            }

            //独立供应商处理 每个供应商结算方式不一致 需要查看srm同步信息
            if (x.equals(SupplierDataSourceEnum.SRM.getName())) {
                Map<String, List<ShopGoodsSupplierVo>> settleMentMethodMap = goodsSupplierVos.stream()
                        .filter(y -> StringUtils.isNotBlank(y.getSettleMentMethod()))
                        .collect(Collectors.groupingBy(o -> o.getSettleMentMethod() + o.getSupplierShortName()));

                settleMentMethodMap.keySet().forEach(y -> settleMentMethodMap.get(y).forEach(s -> {
                    //srm结算信息 合同结算方式默认月结
                    OrderSettlementRemindersDto dto = OrderSettlementRemindersDto.builder()
                            .goodsCodes(goodsSupplierVos.stream()
                                    .filter(g -> g.getSupplierCode()
                                            .equals(s.getSupplierCode()) && StringUtils.isNotEmpty(g.getSettleMentMethod()))
                                    .map(ShopGoodsSupplierVo::getGoodsCode).collect(Collectors.joining(",")))
                            .settleMentMethod(PurchaseOrderEnum.SETTLE_MENT_METHOD_FIXED.getCode().toString()
                                    .equals(s.getSettleMentMethod()) ? s.getSettleMentCycle()
                                    : PurchaseOrderEnum.SETTLE_MENT_METHOD_FIXED_NO.getName())
                            .settleSupplierName(s.getSupplierShortName())
                            .dataSource(x)
                            .supplierCode(s.getSupplierCode()).build();
                    dt.add(dto);
                }));
            }
        });
        return dt;
    }

    @Transactional(rollbackFor = Exception.class)
    public String savePreOrder(PurchaseOrderSaveDto param) {
        log.info("begin savePreOrder {}", param.getPurchaseNumber());
        ShopPurchaseOrder shopPurchaseOrder = this.getOne(new QueryWrapper<ShopPurchaseOrder>().lambda().eq(ShopPurchaseOrder::getPurchaseNumber, param.getPurchaseNumber()));
        if (shopPurchaseOrder == null) {
            throw new OrderException("采购单【" + param.getPurchaseNumber() + "】 不存在");
        } else if (shopPurchaseOrder.getCanConfirm() == PreOrderConfirmTypeEnum.NOT_OK.getCode()) {
            return "ok";
        } else if (shopPurchaseOrder.getCanConfirm() != PreOrderConfirmTypeEnum.UN_CONFIRM.getCode()) {
            throw new OrderException("采购单【" + param.getPurchaseNumber() + "】 状态" + shopPurchaseOrder.getCanConfirm() + "不对，不可下单");
        }

        var user = LocalUserHolder.get();

        var purchaseOrder = _initPurchaseOrder(param);

        var prePurchaseOrder = _checkPurchaseOrder(param);

        _checkApplyUser(purchaseOrder);

        _checkBudgetCode(param);

        //判断是否走预算
        var budget = _getBudget(param);

        // 开票信息保存
        String invoiceSubject = saveInvoiceInfo(purchaseOrder);

        // 数据库获取订单商品数据
        Map<String, ShopPurchaseSubOrderDetail> subOrderDetailsMap = getSubOrderDetails(param.getPurchaseNumber());

        Map<String, List<PurchaseOrderDetailSaveDto>> subOrderDetailMap = param.getPurchaseOrderDetailList().stream().collect(Collectors.groupingBy(PurchaseOrderDetailSaveDto::getOrderId));

        List<ShopPurchaseSubOrder> subOrderList = new ArrayList<>();
        List<ShopPurchaseSubOrderDetail> orderDetailList = new ArrayList<>();
        for (String subOrderId : subOrderDetailMap.keySet()) {
            ShopPurchaseSubOrder subOrder = new ShopPurchaseSubOrder();
            subOrder.setOrderId(subOrderId);
            subOrder.setSupplierCode(subOrderDetailMap.get(subOrderId).get(0).getSupplierCode());
            for (PurchaseOrderDetailSaveDto detailSaveDto : subOrderDetailMap.get(subOrderId)) {
                subOrder.setContractNumber(detailSaveDto.getContractNumber());
                ShopPurchaseSubOrderDetail detail = new ShopPurchaseSubOrderDetail();
                ShopPurchaseSubOrderDetail detailOld = subOrderDetailsMap.get(detailSaveDto.getOrderDetailId());
                BeanUtil.copyProperties(detailOld, detail);
                detail.setBudgetId(detailSaveDto.getBudgetId());
                detail.setBudgetCode(detailSaveDto.getBudgetCode());
                detail.setBudgetName(detailSaveDto.getBudgetName());
                detail.setOrderDetailId(detailSaveDto.getOrderDetailId());
                detail.setGoodsCode(detailSaveDto.getGoodsCode());
                orderDetailList.add(detail);
            }
//            // 设置srm采购单的值
            String subOrderSrmApplyId = getSubOrderSrmApplyId(param.getSrmApplyId(), subOrder.getContractNumber());
            subOrder.setSrmApplyId(subOrderSrmApplyId);
            List<OrderRemarkDto> orderRemark = param.getOrderRemark();
            if (CollectionUtil.isNotEmpty(orderRemark)) {
                log.info("待处理数据{}",orderRemark);
                List<OrderRemarkDto> supplierRemark = orderRemark.stream().filter(item -> item.getSupplierCode().equals(subOrder.getSupplierCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(supplierRemark)) {
                    log.info("处理备注数据{}",supplierRemark);
                    subOrder.setRemark(supplierRemark.get(0).getRemark());
                }
            }
            //维护订单销售客户端
            keepOrderSaleClient(subOrder,user.getEntityOrganizationCode(),user.getTenantId(),invoiceSubject);
            subOrderList.add(subOrder);
        }



        purchaseOrder.setPurchaseGoodsNumber(shopPurchaseOrder.getPurchaseGoodsNumber());
        purchaseOrder.setGoodsZoneId(shopPurchaseOrder.getGoodsZoneId());

        // 保存东本定制下单信息
        saveHondaConInfo(param, purchaseOrder);

        purchaseSubOrderDetailService.saveOrUpdateBatch(orderDetailList);
        //保存供应商子订单
        purchaseSubOrderService.saveOrUpdateBatch(subOrderList);
        //保存采购单
        this.saveOrUpdate(purchaseOrder);

        //保存联友定制下单信息
        savePurchaseCustomLy(purchaseOrder, param.getCustomLySaveDto(), param.getSrmAutoCreateOrder());

        // 扣除预算
        if (ObjectUtil.isNotNull(param.getBudgetId())) {
            final OrderChangeBudgetAmountVO changeBudgetAmountVO = OrderChangeBudgetAmountVO.builder()
                    .changeAmount(prePurchaseOrder.getPurchaseTotalPrice())
                    .userId(Long.valueOf(purchaseOrder.getApplyUserId()))
                    .budgetId(param.getBudgetId())
                    .purchaseNumber(prePurchaseOrder.getPurchaseNumber())
                    .organizationId(user.getOrganizationId())
                    .username(purchaseOrder.getApplyUserName())
                    .applyDeptName(purchaseOrder.getApplyDeptName())
                    .build();
            try {

                this.budgetService.deductAmounts(changeBudgetAmountVO);
            } catch (final Exception ex) {
                log.error(ex.toString());
                throw new OrderException(ex.getMessage());
            }
        }

        List<ShopPurchaseSubOrder> subOrders = purchaseSubOrderService.queryConfirmSubOrder(purchaseOrder.getPurchaseNumber());

        //保存多预算
        multipleBudgetService.saveMultipleBudget(user,param,purchaseOrder,subOrders);

        _checkVoyahBudget(user,purchaseOrder,subOrders,param);

        // 冻结东本积分预算
        this.deductHondaBudget(purchaseOrder);

        //占用外部预算
        this.budgetService.extDeductAmount(purchaseOrder.getPurchaseNumber());

//        //查询审批流程
        final Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyCode", user.getEntityOrganizationCode());
        paramMap.put("procType", "A00501");
        final Long deployId = this.purchaseOrderMapper.queryDeployByCompanyCode(paramMap);

//        // 非友福利订单
        if (!TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            //校验并扣减授信余额，保证前面的预算，最后再扣减预算，它们有事物可以异常回滚，授信是调外部服务的
            authAmountInfoService.authAmountByPreOrder(purchaseOrder, subOrders);
        }

//        /**
//         * 调用dfgsap接口创建物料
//         * 1、走预算
//         * 2、存货类
//         */
        if (!Objects.isNull(budget) && budget.getIsStock() == 1) {
            log.info("【savePreOrder】调用集团SAP创建物料：" + purchaseOrder.getPurchaseNumber());
            ShopOrderAddress address = this.shopOrderAddressService.queryByPurchaseId(purchaseOrder.getPurchaseId());
            String supplierDataSource = subOrders.get(0).getSupplierDataSource();
            if(CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(supplierDataSource)){
                //兼容历史1227没创建的物料
                voyahPurchaseService.createVoyahSrmMara(orderDetailList, address,subOrders);
            }else{
                log.info("【savePreOrder】开始创建物料");
                //不入库为费用类订单，不强制要求创建物料
                dfgSapService.createSapMara(orderDetailList, address);
            }
        }
        // 预下单成功后处理逻辑
        this.supplierPreOrderToDeal(purchaseOrder.getPurchaseNumber(), deployId, param);

        //绑定订单和需求归集关系
        if (!StringHelper.IsEmptyOrNull(param.getCartIds())) {
            this.saveRequirementRelation(param.getCartIds(), subOrders);
        }
        // 删除购物车商品
        if (!StringHelper.IsEmptyOrNull(param.getCartIds())) {
            final List<Long> cartIds = Arrays.asList(param.getCartIds().split(",")).stream().map(cid -> Long.parseLong(cid)).collect(Collectors.toList());
            this.shopCartService.delCartIds(cartIds);
        }
        log.info("end savePreOrder {}", param.getPurchaseNumber());
        return "ok";
    }

    public void keepOrderSaleClient(ShopPurchaseSubOrder subOrder, String organizationCode, Long tenantId, String invoiceSubject) {
        if (getIsToSaveSupplierBillMessage(organizationCode, invoiceSubject, tenantId)) {
            subOrder.setSaleClient(2);
        }else{
            subOrder.setSaleClient(1);
        }
    }

    /**
     * 是否2c  返回true 为c端订单 false 为b端订单
     * 2C：友福利（积分租户全部订单）、东本车主商城、神龙车主商城、东本营销积分
     * 2B：东风商城、3SM商城 （无需打标，暂不参与校验）
     * @param organizationCode
     * @param invoiceSubject
     * @param tenantId
     * @return
     */
    public Boolean getIsToSaveSupplierBillMessage( String organizationCode ,String invoiceSubject ,Long tenantId) {
        //友福利
        if (yflYamlConfig.getTenantId().equals(tenantId)) {
            return true;
        }
        // 东风商城的
        if (CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(organizationCode) &&
                "东风本田汽车销售有限公司".equals(invoiceSubject)) {
            // 存入消息
            return true;
        }
        //东风本田信赖商城 , 神龙汽车车主商城
        if (CompanyEnum.DSDB000.getCompanyCode().equalsIgnoreCase(organizationCode) ||
                "DPCAJF".equalsIgnoreCase(organizationCode)) {
            // 存入消息
            return true;
        }
        //3SM商城
        if (CompanyEnum.DS3SM000.getCompanyCode().equalsIgnoreCase(organizationCode) ) {
            return false;
        }

        return false;
    }

    private Map<String, ShopPurchaseSubOrderDetail> getSubOrderDetails(String purchaseNumber) {
        List<ShopPurchaseSubOrderDetail> orderDetail = purchaseSubOrderDetailService.getOrderDetailByPurchaseNumber(purchaseNumber);
        if (orderDetail == null) {
            throw new OrderException("订单保存失败");
        }
        return orderDetail.stream().collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getOrderDetailId, Function.identity()));
    }

    private void deductHondaBudget(ShopPurchaseOrder purchaseOrder) {
        var user = LocalUserHolder.get();
        if (!CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            return;
        }
        Map<String, String> budgetErrorMap = hondaNpmsBudgetService.npmsBudgetDeal(purchaseOrder.getPurchaseId(), "YS01");
        if (budgetErrorMap.get(purchaseOrder.getOtherRelationNumber()) != null) {
            // 积分不足时，抛出异常，让用户可以换积分或预算重新下单，订单超时会自动取消
            //this.cancelOrder(purchaseOrder.getPurchaseNumber(), budgetErrorMap.get(purchaseOrder.getOtherRelationNumber()));
            throw new OrderException(budgetErrorMap.get(purchaseOrder.getOtherRelationNumber()));
        }
    }

    private void saveHondaConInfo(PurchaseOrderSaveDto param, ShopPurchaseOrder purchaseOrder) {
        var user = LocalUserHolder.get();
        if (!CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            return;
        }

        // 校验内部积分假勤商品购买次数  每个用户每年只能购买一次 每次买一个
        if(purchaseOrder.getOrderLabel()==PurchaseLabelHonda.ORDER_LABEL_POINT_JQ){
            if(purchaseOrder.getPurchaseGoodsNumber().compareTo(BigDecimal.ONE)>0){
                throw new OrderException("假勤商品每次只能兑换一天！");
            }
            // 每个用户每年只能购买一次
            Boolean isNotBuy = this.queryHondaJiaQinGoods(purchaseOrder.getApplyUserId());
            if(isNotBuy){
                throw new OrderException("假勤商品每个年度只能兑换一次！");
            }
        }

        //销售售后订单库位收货人下单时指定
        if(purchaseOrder.getOrderLabel()== PurchaseLabelHonda.ORDER_LABEL_POINT_SERVER ||
                purchaseOrder.getOrderLabel()== PurchaseLabelHonda.ORDER_LABEL_POINT_SALE){
            List<SystemDictDataEntity> dictList = dictDataSrv.getDictDatasByDictType("honda_jf_warehouser");
            if(CollectionUtil.isEmpty(dictList)){
                throw new ParameterException("积分仓管员未配置，请联系商城客服");
            }
            Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(SystemDictDataEntity::getValue, SystemDictDataEntity::getLabel));
            String label = dictMap.get(purchaseOrder.getOrderLabel().toString());
            UpdateWrapper<ShopOrderAddress> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(ShopOrderAddress::getWarehouserCode, label.split("_")[0])
                            .set(ShopOrderAddress::getWarehouserPhone, label.split("_")[1]).
                    eq(ShopOrderAddress::getPurchaseId, param.getPurchaseId());
            shopOrderAddressService.update(updateWrapper);
        }

        // 积分类订单直接审批通过
        if(purchaseOrder.getOrderLabel()== PurchaseLabelHonda.ORDER_LABEL_POINT_SERVER ||
                purchaseOrder.getOrderLabel()== PurchaseLabelHonda.ORDER_LABEL_POINT_SALE||
                purchaseOrder.getOrderLabel()== PurchaseLabelHonda.ORDER_LABEL_POINT_IN||
                purchaseOrder.getOrderLabel()== PurchaseLabelHonda.ORDER_LABEL_POINT_JQ){
            purchaseOrder.setPurchaseState(30);
        }

        OrganizationSimpleRespVO organizationVO = systemOrganizationService.getSectionOrg(user.getOrganizationId());
        purchaseOrder.setApplySectionCode(organizationVO.getCode());
        purchaseOrder.setApplySectionName(organizationVO.getName());
        purchaseOrder.setSapOrderType(1);
        // 生成东本单号
        purchaseOrder.setOtherRelationNumber(this.getOrderShortSn());
        // 保存附件数据
        if (param.getAttachmentList() != null) {
            List<ShopPurchaseAttachment> attachmentsList = new ArrayList<>();
            List<ShopPurchaseAttachmentDto> attachmentsDtoList = param.getAttachmentList();
            attachmentsDtoList.forEach(item -> {
                ShopPurchaseAttachment shopPurchaseAttachment = new ShopPurchaseAttachment();
                shopPurchaseAttachment.setPurchaseOrderId(param.getPurchaseId());
                shopPurchaseAttachment.setIsDel(0);
                shopPurchaseAttachment.setUpdateTime(new Date());
                shopPurchaseAttachment.setCreateTime(new Date());
                BeanUtil.copyProperties(item, shopPurchaseAttachment);
                attachmentsList.add(shopPurchaseAttachment);
            });
            shopPurchaseAttachmentService.saveBatch(attachmentsList);
        }
        // 东本投资类数据
        if (param.getHondaInvestDto() != null) {
            ShopPurchaseHondaInvestEntity entity = new ShopPurchaseHondaInvestEntity();
            BeanUtil.copyProperties(param.getHondaInvestDto(), entity);
            entity.setFundSrc(param.getHondaInvestDto().getFundSrc());
            entity.setAccountTitle(param.getHondaInvestDto().getAccountTitle());
            entity.setMainPeriodType(param.getHondaInvestDto().getMainPeriodType());
            entity.setMainPeriod(param.getHondaInvestDto().getMainPeriod());
            entity.setMainPeriodEnd(param.getHondaInvestDto().getMainPeriodEnd());
            entity.setMainPeriodStart(param.getHondaInvestDto().getMainPeriodStart());
            entity.setPurchaseOrderId(param.getPurchaseId());
            shopPurchaseHondaInvestService.save(entity);
        }
    }

    /**
     * 东本假勤商品校验
     * @param applyUserId
     * @return
     */
    private Boolean queryHondaJiaQinGoods(String applyUserId) {
        Date beginYear = DateUtils.getBeginZeroOfYear();
        ShopPurchaseOrder purchaseOrder = this.baseMapper.queryHondaJiaQinGoods(PurchaseLabelHonda.ORDER_LABEL_POINT_JQ, applyUserId, beginYear);
        return purchaseOrder!=null;
    }

    private ShopPurchaseOrder _checkPurchaseOrder(PurchaseOrderSaveDto param) {
        ShopPurchaseOrder unPurchaseOrder = this.baseMapper.queryById(param.getPurchaseId());
        if (unPurchaseOrder == null || !PreOrderConfirmTypeEnum.UN_CONFIRM.getCode().equals(unPurchaseOrder.getCanConfirm())) {
            throw new ParameterException("订单不存在或已被取消");
        }
        return unPurchaseOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cartToPreOrder(ShopPurchaseOrderVo purchaseOrderVo) {
        var user = LocalUserHolder.get();

        final ShopPurchaseOrder purchaseOrder = this.initShopPurchaseOrder(user, purchaseOrderVo);

        List<PurchaseOrderDetailSaveDto> orderDetailSaveDtoList = this.getOrderDetailSaveDtoList(purchaseOrderVo);

        var orderGroupList = this.getOrderGroupListNew(orderDetailSaveDtoList);

        PurchaseOrderSaveDto param = new PurchaseOrderSaveDto();
        param.setPurchaseOrderDetailList(orderDetailSaveDtoList);
        _checkCompanyStore(purchaseOrder,param);

        var addressInfo = this.saveAddress(purchaseOrder, param);

        var regionDto = _initRegion(addressInfo);

        var priceInfoRespMap = purchaseOrderVo.getPriceInfoRespMap();
        //企配仓存储用
        List<CompanyStoreOrderAddressSaveDto> companyStoreOrderAddressSaveList = new ArrayList<>();
        //子订单
        List<ShopPurchaseSubOrder> subOrderList = new ArrayList<>();
        Map<String, Object> cnt = orderDetailSaveDtoList.stream().collect(Collectors.toMap(PurchaseOrderDetailSaveDto::getGoodsCode, PurchaseOrderDetailSaveDto::getApplyNum));
        // 校验浮动价格
        Map<String, ShopOrderDetailFloat> floatPriceGoodsMap = checkFloatPriceOrderGoods(purchaseOrderVo.getFloatPriceGoodsList(), priceInfoRespMap);
        List<GoodsMasterDetailVo> goodsViewList = this.shopGoodsService.queryGoodsMasterDetailByCodes(cnt.keySet());
        Map<String, GoodsMasterDetailVo> goodsViewMap = goodsViewList.stream().collect(Collectors.toMap(GoodsMasterDetailVo::getGoodsCode, Function.identity()));
        List<ShopSupplier> supplierList = shopSupplierService.getBaseMapper().selectSimpleList(orderDetailSaveDtoList.stream().map(PurchaseOrderDetailSaveDto::getSupplierCode).collect(Collectors.toList()));
        Map<String, ShopSupplier> supplierMap = supplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity()));
        List<String> goodsCodeList = new ArrayList<>();
        Map<String, Integer> saleCntUpdate = new HashMap<>();
        List<PurchaseSubOrderDetailVo> subOrderDetailVos = new ArrayList<>();
        Integer rowNumber = 1;
        var purchaseGoodsNumber = new BigDecimal(0);
        var purchaseGoodsPrice = new BigDecimal(0);
        var purchaseFreightPrice = new BigDecimal(0);
        var purchaseSubsidyFreight = new BigDecimal(0);
        var purchaseGoodsPriceNaked = new BigDecimal(0);
        for (List<PurchaseOrderDetailSaveDto> purchaseOrderDetailList : orderGroupList) {
            var subOrder = new ShopPurchaseSubOrder();
            subOrder.setOrderModel(purchaseOrderDetailList.get(0).getGoodsModel());
            String contractNumber = purchaseOrderDetailList.get(0).getContractNumber();
            subOrder.setContractNumber(contractNumber);
            Integer settlementType = purchaseOrderDetailList.get(0).getSettlementType();
            Integer isPlatformReconciliation = (settlementType == null || settlementType == 1) ? 1 : 2;
            subOrder.setIsPlatformReconciliation(isPlatformReconciliation);
            // 子订单合计字段 每供应商一个子订单
            var orderPriceTax = new BigDecimal(0);
            var orderPriceNaked = new BigDecimal(0);
            var supplierOrderPriceTax = new BigDecimal(0);
            var supplierOrderPriceNaked = new BigDecimal(0);
            //运费查询入参
            final List<GoodCodeInfoListParams> goodCodeInfoParamsList = new ArrayList<>();
            // 订单已按照商品类型拆单,所以商品类型就是订单类型
            var dataSource = supplierMap.get(purchaseOrderDetailList.get(0).getSupplierCode()).getDataSource();
            var srmContract = getSrmContract(subOrder, dataSource);

            //订单商品明细
            List<ShopPurchaseSubOrderDetail> orderDetailList = new ArrayList<>();
            for (var orderDetailSaveDto : purchaseOrderDetailList) {
                GoodsMasterDetailVo goodsMasterDetailVo = goodsViewMap.get(orderDetailSaveDto.getGoodsCode());
                subOrder.setSupplierDataSource(supplierMap.get(goodsMasterDetailVo.getSupplierCode()).getDataSource());
                var subOrderDetail = initSubOrderDetail(orderDetailSaveDto, subOrder);
                subOrderDetail = _orderDetailFill(orderDetailSaveDto, goodsMasterDetailVo, subOrderDetail, rowNumber, priceInfoRespMap, subOrder);
                // 插一脚，处理浮动价格
                handleFloatPriceOrderGoods(subOrder.getPricingMode(), subOrderDetail, floatPriceGoodsMap);
                supplierOrderPriceTax = supplierOrderPriceTax.add(subOrderDetail.getSupplierTotalPriceTax());
                supplierOrderPriceNaked = supplierOrderPriceNaked.add(subOrderDetail.getSupplierTotalPriceNaked());

                subOrder.setSupplierCode(goodsMasterDetailVo.getSupplierCode());
                subOrder.setSupplierName(goodsMasterDetailVo.getSupplierName());


                //外币处理
                _srmPriceProcess(srmContract, subOrderDetail);
                goodCodeInfoParamsList.add(new GoodCodeInfoListParams(subOrderDetail.getApplyNum().intValue(), subOrderDetail.getGoodsCode()));

                rowNumber++;
                purchaseGoodsNumber = purchaseGoodsNumber.add(new BigDecimal(subOrderDetail.getApplyNum())).add(subOrderDetail.getApplyNumDecimal());
                saleCntUpdate.put(subOrderDetail.getGoodsCode(), subOrderDetail.getApplyNum().intValue());
                orderPriceTax = orderPriceTax.add(subOrderDetail.getGoodsTotalPriceTax());
                orderPriceNaked = orderPriceNaked.add(subOrderDetail.getGoodsTotalPriceNaked());
                orderDetailList.add(subOrderDetail);
                goodsCodeList.add(subOrderDetail.getGoodsCode());

                PurchaseSubOrderDetailVo vo = new PurchaseSubOrderDetailVo();
                BeanUtil.copyProperties(subOrderDetail, vo);
                vo.setSupplierCode(subOrder.getSupplierCode());
                vo.setSupplierName(subOrder.getSupplierName());
                vo.setSupplierType(orderDetailSaveDto.getSupplierType());
                vo.setSupplierDataSource(subOrder.getSupplierDataSource());
                vo.setDeliveryTime(goodsMasterDetailVo.getDeliveryTime());
                vo.setSettlementType(orderDetailSaveDto.getSettlementType());
                vo.setMaraMatnr(orderDetailSaveDto.getMaraMatnr());
                vo.setCartZoneGoodsType(orderDetailSaveDto.getCartZoneGoodsType());
                vo.setGoodsZoneId(orderDetailSaveDto.getGoodsZoneId());
                vo.setTaxRate(new BigDecimal(subOrderDetail.getTaxRate()));
                subOrderDetailVos.add(vo);
            }
            purchaseGoodsPrice = purchaseGoodsPrice.add(orderPriceTax);
            purchaseGoodsPriceNaked = purchaseGoodsPriceNaked.add(orderPriceNaked);

            subOrder.setSubOrderDetailList(orderDetailList);

            subOrder.setContractNumber(purchaseOrderDetailList.get(0).getContractNumber());
            subOrder.setOrderState(9);
            // 订单需结算,从0开始验收完成后累加
            subOrder.setNeedSettleAmount(BigDecimal.ZERO);
            subOrder.setPurchaseNumber(purchaseOrder.getPurchaseNumber());
            subOrder.setOrderPriceTax(orderPriceTax);
            subOrder.setOrderPriceNaked(orderPriceNaked);
            subOrder.setSupplierOrderPriceTax(supplierOrderPriceTax);
            subOrder.setSupplierOrderPriceNaked(supplierOrderPriceNaked);
            subOrder.setOrganizationId(user.getOrganizationId());

            if(CollectionUtil.isNotEmpty(purchaseOrderVo.getRemarkMaps())
                    && StringUtils.isNotBlank(purchaseOrderVo.getRemarkMaps().get(subOrder.getSupplierCode()))){
                subOrder.setRemark(purchaseOrderVo.getRemarkMaps().get(subOrder.getSupplierCode()));
            }

            isPrePayOrder(purchaseOrder, subOrder);

            //获取运费
            DeliveryPriceResp deliveryPriceResp = _checkDelivery(goodCodeInfoParamsList, regionDto, addressInfo);
            if (addressInfo.orderAddress.getIsCompanyStore() == 1){
                CompanyStoreOrderAddressSaveDto companyStoreOrderAddressSaveDto = param.getCompanyStoreOrderAddressSaveDto();
                //initSubOrderDetail 这个方法里维护了订单号
                if (CollectionUtil.isEmpty(companyStoreOrderAddressSaveDto.getFilterSupplierList()) || !companyStoreOrderAddressSaveDto.getFilterSupplierList().contains(subOrder.getSupplierCode())) {
                    //企配仓插一脚 维护企配状态
                    subOrder.setIsCompanyStore(addressInfo.orderAddress.getIsCompanyStore());
                    // 创建一个新的 CompanyStoreOrderAddressSaveDto 的副本
                    CompanyStoreOrderAddressSaveDto saveDto = new CompanyStoreOrderAddressSaveDto();
                    BeanUtil.copyProperties(param.getCompanyStoreOrderAddressSaveDto(), saveDto); // 复制属性，避免直接引用原对象
                    saveDto.setOrderNumber(subOrder.getOrderNumber());
                    companyStoreOrderAddressSaveList.add(saveDto);
                    //给备注字段加东西
                    subOrder.setRemark(addressInfo.orderAddress.getAddressInfo()+(StringUtils.isBlank(subOrder.getRemark())?"":";"+subOrder.getRemark()));
                }
            }
            this.zkhOrderCustomization(subOrder, purchaseOrder.getCompanyName());


            ShopSupplier shopSupplier = supplierMap.get(subOrder.getSupplierCode());
            if (orderPriceTax.compareTo(shopSupplier.getFreeShippingCondition()) >= 0
                    && deliveryPriceResp.getTotalPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 用户下单金额满足包邮条件，并且邮费大于 0，需要平台补贴邮费
                subOrder.setOrderSubsidyFreight(deliveryPriceResp.getTotalPrice());
                purchaseSubsidyFreight = purchaseSubsidyFreight.add(subOrder.getOrderSubsidyFreight());
                log.info("补贴邮费：{},供应商：{}", subOrder.getOrderSubsidyFreight(), subOrder.getSupplierName());
            } else {
                //未满足电商订单金额包邮条件，需要用户支付邮费
                subOrder.setOrderFreightPrice(deliveryPriceResp.getTotalPrice());
                purchaseFreightPrice = purchaseFreightPrice.add(subOrder.getOrderFreightPrice());
            }

            //岚图填充一个是否延期字段
            subOrder.setDelayOrder(voyahPurchaseService.fillDelayOrder(subOrder,shopSupplier.getSupplierId()));

            //维护订单销售客户端
            keepOrderSaleClient(subOrder,user.getEntityOrganizationCode(),user.getTenantId(),"");

            subOrderList.add(subOrder);
        }
        //采购单信息
        log.info("邮费总金额：{}，平台补贴邮费总金额：{}", purchaseFreightPrice, purchaseSubsidyFreight);
        purchaseOrder.setPurchaseState(10);
        purchaseOrder.setPurchaseGoodsPrice(purchaseGoodsPrice);
        purchaseOrder.setPurchaseGoodsPriceNaked(purchaseGoodsPriceNaked);
        purchaseOrder.setPurchaseFreightPrice(purchaseFreightPrice);
        purchaseOrder.setPurchaseSubsidyFreight(purchaseSubsidyFreight);
        purchaseOrder.setPurchaseGoodsNumber(purchaseGoodsNumber);
        purchaseOrder.setPurchaseTotalPrice(purchaseGoodsPrice.add(purchaseFreightPrice));
        purchaseOrderVo.setOrderDetailList(subOrderDetailVos);

        fullOrderLabel(purchaseOrder, purchaseOrderVo);

        List<ShopPurchaseSubOrderDetail> orderDetailList = new ArrayList<>();
        for (ShopPurchaseSubOrder purchaseSubOrder : subOrderList) {
            //保存订单商品明细
            orderDetailList.addAll(purchaseSubOrder.getSubOrderDetailList());
        }
        //异步生成东本物料编码
        this.makeHondaMaterials(goodsCodeList);

        //保存比价记录
        saveCompareRecord(purchaseOrderVo.getCompareRecordList(), orderDetailList, subOrderDetailVos);

        _generatedVoyahSrmSapOrder(purchaseOrder,subOrderList);

        //校验岚图预订单
        voyahPurchaseService.srmCheckOrder(purchaseOrder,subOrderList,orderDetailList);

        purchaseSubOrderDetailService.saveOrUpdateBatch(orderDetailList);
        //保存供应商子订单
        purchaseSubOrderService.saveOrUpdateBatch(subOrderList);
        //保存采购单
        this.saveOrUpdate(purchaseOrder);
        //保存浮动价格变化
         saveOrderDetailFloat(orderDetailList, floatPriceGoodsMap);
        //保存企配仓订单信息
        if (CollectionUtil.isNotEmpty(companyStoreOrderAddressSaveList)){
            companyStoreOrderAddressService.saveBatch(companyStoreOrderAddressSaveList);
        }
        // 向供应商下预订单
        //替换企配仓地址
        this.submitSupplierPreOrderNew(purchaseOrder.getPurchaseNumber());

        purchaseOrder.setCanConfirm(PreOrderConfirmTypeEnum.UN_CONFIRM.getCode());

        List<ShopOrderAddress> orderAddresses = new ArrayList<>();
        orderAddresses.add(addressInfo.orderAddress);

        purchaseOrderVo.setOrderAddressList(orderAddresses);
        //保存采购单
        this.saveOrUpdate(purchaseOrder);
    }

    /**
     * 提前填充东本内部积分订单类型
     * @param purchaseOrder
     * @param purchaseOrderVo
     */
    private void fullOrderLabel(ShopPurchaseOrder purchaseOrder, ShopPurchaseOrderVo purchaseOrderVo) {
        if(purchaseOrder.getCompanyCode().equalsIgnoreCase(CompanyEnum.HONDA.getCompanyCode())
        && purchaseOrderVo.getOrderDetailList().get(0).getGoodsZoneId()!=null){
            GoodsZoneEntity goodsZoneEntity = goodsZoneService.getById(purchaseOrderVo.getOrderDetailList().get(0).getGoodsZoneId());
            if(null!=goodsZoneEntity && "nbjf".equalsIgnoreCase(goodsZoneEntity.getZoneCode())){
                purchaseOrder.setOrderLabel(9);
            }
        }
    }

    /**
     * zkh定制化 为区分客户业绩 下单备注字段最后拼接客户名称
     * @param subOrder
     * @param companyName
     */
    private void zkhOrderCustomization(ShopPurchaseSubOrder subOrder, String companyName) {
        if (subOrder.getSupplierCode().equals(SupplierConstants.ZKH_CODE)){
            if (StringUtils.isNotBlank(subOrder.getRemark())) {
                subOrder.setRemark(subOrder.getRemark() + "," + companyName);
            } else {
                subOrder.setRemark(companyName);
            }
        }
    }

    /**
     * 保存订单商品同款比价记录
     * @param compareRecordList
     * @param orderDetailList
     */
    private void saveCompareRecord(List<ShopPurchaseSubOrderDetailCompareRecordDto> compareRecordList,
                                   List<ShopPurchaseSubOrderDetail> orderDetailList,
                                   List<PurchaseSubOrderDetailVo> orderDetailVoList) {
        if(CollectionUtil.isNotEmpty(compareRecordList)){
            LoginUser loginUser = LocalUserHolder.get();
            List<ShopPurchaseSubOrderDetailCompareRecordEntity> recordEntityList = new ArrayList<>();
            Map<Integer, List<ShopPurchaseSubOrderDetailCompareRecordDto>> compareRecordMap = compareRecordList.stream().collect(Collectors.groupingBy(ShopPurchaseSubOrderDetailCompareRecordDto::getCompareGoodsId));
            Map<Long, ShopPurchaseSubOrderDetail> orderDetailMap = orderDetailList.stream().collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getGoodsId, Function.identity(), (key1,key2)->key1));
            Map<Long, PurchaseSubOrderDetailVo> orderDetailVoMap = orderDetailVoList.stream().collect(Collectors.toMap(PurchaseSubOrderDetailVo::getGoodsId, Function.identity(), (key1,key2)->key1));
            for(Integer goodsId : compareRecordMap.keySet()){
                List<ShopPurchaseSubOrderDetailCompareRecordDto> itemList = compareRecordMap.get(goodsId);
                ShopPurchaseSubOrderDetail orderDetail = orderDetailMap.get(Long.valueOf(goodsId));
                PurchaseSubOrderDetailVo orderDetailVo = orderDetailVoMap.get(Long.valueOf(goodsId));

                for(ShopPurchaseSubOrderDetailCompareRecordDto recordQueryDto : itemList){
                    if(recordQueryDto.getNotMinPriceReason()!=null){
                        // 保存到数据库
                        orderDetail.setNotMinPriceReason(recordQueryDto.getNotMinPriceReason());
                        // 回显到页面
                        orderDetailVo.setNotMinPriceReason(recordQueryDto.getNotMinPriceReason());
                    }
                    ShopPurchaseSubOrderDetailCompareRecordEntity entity = new ShopPurchaseSubOrderDetailCompareRecordEntity();
                    BeanUtil.copyProperties(recordQueryDto, entity);
                    entity.setId(commonCodeGeneral.makeMysqlId());
                    entity.setOrderDetailId(orderDetail.getOrderDetailId());
                    entity.setOrderId(orderDetail.getOrderId());
                    entity.setCreateTime(new Date());
                    entity.setCreateName(loginUser.getNickname());
                    entity.setCreateCode(loginUser.getUsername());
                    recordEntityList.add(entity);
                }
           }
           shopPurchaseSubOrderDetailCompareRecordService.saveBatch(recordEntityList);
        }
    }

    /**
     * 组装订单详情数据
     *
     * @param purchaseOrderVo
     * @return
     */
    private List<PurchaseOrderDetailSaveDto> getOrderDetailSaveDtoList(ShopPurchaseOrderVo purchaseOrderVo) {
        List<PurchaseOrderDetailSaveDto> orderDetailSaveDtoList = new ArrayList<>();
        for (PurchaseSubOrderDetailVo purchaseSubOrderDetailVo : purchaseOrderVo.getOrderDetailList()) {
            PurchaseOrderDetailSaveDto saveDto = new PurchaseOrderDetailSaveDto();
            BeanUtil.copyProperties(purchaseSubOrderDetailVo, saveDto);
            saveDto.setApplyNum(purchaseSubOrderDetailVo.getConfirmNum().intValue());
            saveDto.setApplyNumDecimal(purchaseSubOrderDetailVo.getConfirmNumDecimal());
            orderDetailSaveDtoList.add(saveDto);
        }
        return orderDetailSaveDtoList;
    }

    /**
     * 初始化采购单
     *
     * @param user
     * @param purchaseOrderVo
     * @return
     */
    private ShopPurchaseOrder initShopPurchaseOrder(LoginUser user, ShopPurchaseOrderVo purchaseOrderVo) {
        SystemUsers users = systemUsersService.getUser(user.getId());
        final ShopPurchaseOrder purchaseOrder = new ShopPurchaseOrder();
        purchaseOrder.setOrganizationId(users.getOrganizationId());
        purchaseOrder.setApplyDeptName(users.getOrganizationName());
        purchaseOrder.setApplyEmpCode(users.getUsername());
        purchaseOrder.setApplyUserId(users.getId() + "");
        purchaseOrder.setApplyUserName(users.getNickname());
        purchaseOrder.setApplyUserPhone(users.getMobile());
        purchaseOrder.setApplyBuyerEmail(users.getEmail());
        purchaseOrder.setCompanyCode(user.getEntityOrganizationCode());
        purchaseOrder.setCompanyName(user.getEntityOrganizationName());
        purchaseOrder.setApplyDeptId(String.valueOf(users.getOrganizationId()));
        purchaseOrder.setCanConfirm(PreOrderConfirmTypeEnum.NOT_OK.getCode());
        purchaseOrder.setGoodsZoneId(purchaseOrderVo.getOrderDetailList().get(0).getGoodsZoneId());
        final String purchaseNumber = this.orderNumberGenerator.make();
        purchaseOrder.setPurchaseNumber(purchaseNumber);
        purchaseOrder.setPurchaseId(UUID.randomUUID().toString().replaceAll("-", ""));
        purchaseOrder.setAddressId(purchaseOrderVo.getAddressId());
        purchaseOrderVo.setPurchaseId(purchaseOrder.getPurchaseId());
        purchaseOrderVo.setPurchaseNumber(purchaseOrder.getPurchaseNumber());
        purchaseOrder.setOrderSalesChannel(1);
        if (TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId())) {
            purchaseOrder.setOrderSalesChannel(2);
        }
        return purchaseOrder;
    }

    /**
     * 生成东本物料编码
     *
     * @param goodsCodeList
     */
    private void makeHondaMaterials(List<String> goodsCodeList) {
        var user = LocalUserHolder.get();
        if (user.getEntityOrganizationCode().equals(CompanyEnum.HONDA.getCompanyCode())) {
            hondaNpmsMaterialService.asynGoodsMaterial(goodsCodeList);
        }
    }

    /**
     * 预下单后处理
     */
    public void supplierPreOrderToDeal(final String purchaseNumber, final Long deployId, final PurchaseOrderSaveDto param) {
        Integer srmAutoCreateOrder = param.getSrmAutoCreateOrder();
        Integer tradeUnionFlag = param.getTradeUnionFlag();
        PurchaseCustomLySaveDto customLySaveDto = param.getCustomLySaveDto();
        try {
            QueryWrapper<ShopPurchaseOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseNumber);
            ShopPurchaseOrder shopPurchaseOrder = this.getOne(queryWrapper);
            String companyCode = shopPurchaseOrder.getCompanyCode();
            //保存预订单信息至生命周期
            OrderLifeCycleStrategy orderLifeCycleStrategy = orderLifeCycleFactory.getOrderLifeCycleStrategy(OrderSalesChannelEnum.DFMALL.getCode());
            orderLifeCycleStrategy.savePurchaseOrderInfoForPreOrder(purchaseNumber);

            // 联友定制化: 根据【办公用品/工会】的选项判断审批过程是在srm系统还是商城，三种选项：否、是、其他
            // 选择【是】或者【其他】：走商城审批
            // 选择【否】且【无】srm申请单：走srm审批
            // 选择【否】且【有】srm申请单：商城自动审批通过,直接确认订单
            if (lyCompanyCodes.equals(companyCode) && ObjectUtil.notEqual(srmAutoCreateOrder, 1)) {
                if (ObjectUtil.equal(tradeUnionFlag, 0) && ObjectUtil.equal(customLySaveDto.getHadSrmPurchase(), 0)) {
                    openApiMessageUtil.sendMessage(companyCode, MessageTypeEnum.CUSTOMER_PURCHASE_SUBMIT, MapUtil.of("purchaseNumber", purchaseNumber));
                    log.info("联友定制化下单,采购单发送到srm审批【" + purchaseNumber + "】");
                    return;
                }
                if (ObjectUtil.equal(tradeUnionFlag, 0) && ObjectUtil.equal(customLySaveDto.getHadSrmPurchase(), 1)) {
                    log.info("联友定制化下单，直接确认采购单【" + purchaseNumber + "】");
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            ShopPurchaseOrderService.this.shopPurchaseOrderService.confirmPurchaseOrder(purchaseNumber, "");
                        }
                    });
                    return;
                }
                log.info("联友定制化下单,采购单在商城审批【" + purchaseNumber + "】");
            }

            if (null == deployId) {
                //查询采购单属于哪个公司
                Boolean isExt = sendExtPurchaseOrderInfo(companyCode, purchaseNumber);
                if (isExt) {
                    log.info("外部系统审批,不直接确认采购单【" + purchaseNumber + "】");
                    return;
                }
            }

            // 商城判断是否直接确认订单
            boolean shopFlag = null == deployId && !TenantContextHolder.getRequiredTenantId().equals(yflYamlConfig.getTenantId());
            // SRM自动创建订单直接确认订单
            boolean srmFlag = ObjectUtil.equal(srmAutoCreateOrder, 1);
            // 联友定制
            if (shopFlag || srmFlag) {
                // 没有审批流程直接确认订单(排除友福利) SRM合同自动创建的订单无需审批流
                log.info("无审批流程图,直接确认采购单【" + purchaseNumber + "】");
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        ShopPurchaseOrderService.this.shopPurchaseOrderService.confirmPurchaseOrder(purchaseNumber, "");
                    }
                });
            }
        } catch (final DataRequestException data) {
            log.error("供应商创建订单失败["+purchaseNumber+"] ：", data);
            // 提交供应商订单失败，回退授信余额
            authAmountInfoService.authAmountByReturn(purchaseNumber);
            String exceptionMessage = StrUtil.isBlank(data.getResult()) ?
                    (StrUtil.isBlank(data.getResponse()) ? data.getMessage() : data.getResponse()) : data.getResult();
            throw new OrderException(exceptionMessage);
        } catch (final Exception e) {
            log.error("供应商创建订单失败Exception:", e);
            // 提交供应商订单失败，回退授信余额
            authAmountInfoService.authAmountByReturn(purchaseNumber);
            throw new OrderException("提交供应商订单失败");
        }
    }

    /**
     * 提交供应商预订单
     */
    public void submitSupplierPreOrderNew(final String purchaseNumber) {
        List<PreOrderInfo> preOrderInfoList = this.purchaseOrderMapper.querySupplierPreOrder(purchaseNumber);
        //企配仓
        if (preOrderInfoList.stream().anyMatch(preOrderInfo -> preOrderInfo.getIsCompanyStore() == 1)) {
            preOrderInfoList = this.purchaseOrderMapper.queryCompanyStoreSupplierPreOrder(purchaseNumber);
        }
        for (final PreOrderInfo preOrderInfo : preOrderInfoList) {
            final List<OrderSkuList> orderSkuList = this.purchaseSubOrderDetailService.querySupplierOrderDetail(preOrderInfo.getYphOrderNo());

            // 处理采购单中的 具体商品条目
            preOrderInfo.setSkuList(orderSkuList);
        }
        try {
            // 远程预定单，走消息通道《鑫方盛》
            List<PreOrderInfo> openApiSuppler = preOrderInfoList.stream().filter(item -> openApiConfig.getSuppliers().contains(item.getSupplierCode())).collect(Collectors.toList());

            //定制化走网关订单
            List<PreOrderInfo> toMoveSuppler = openApiSuppler.stream()
                    .filter(item -> openApiConfig.getCustomizedSuppliersByOrder().contains(item.getSupplierCode()))
                    .collect(Collectors.toList());
            // 走普通调用流程的订单
            List<PreOrderInfo> commonSuppler = CollectionUtil.subtractToList(preOrderInfoList, openApiSuppler);
            commonSuppler.addAll(toMoveSuppler);

            // 需要发送消息到消息表，消息类型的订价格以商品保存时刻的价格为准
            // openApiSuppler 中的额订单，需要处理价格，后续具体供应商订单号通过API接口进行数据补全？

            openApiSuppler.removeAll(toMoveSuppler);
            // 走网关的供应商
            this.doApiSupplierConfirm(openApiSuppler);

            // 不走网关的供应商使用供应商接口来处理预订单
            this.doCommonSupplierConfirm(commonSuppler);

        } catch (final DataRequestException data) {
            log.error("供应商创建订单失败：", data);
            String exceptionMessage = StrUtil.isBlank(data.getResult()) ?
                    (StrUtil.isBlank(data.getResponse()) ? data.getMessage() : data.getResponse()) : data.getResult();
            ShopPurchaseOrder purchaseOrder = new ShopPurchaseOrder();
            purchaseOrder.setPurchaseNumber(purchaseNumber);
            this.toCancelNotOkPurchaseOrder(purchaseOrder, exceptionMessage);
            throw new OrderException(exceptionMessage);
        } catch (final Exception e) {
            log.error("供应商创建订单失败Exception:", e);
            ShopPurchaseOrder purchaseOrder = new ShopPurchaseOrder();
            purchaseOrder.setPurchaseNumber(purchaseNumber);
            this.toCancelNotOkPurchaseOrder(purchaseOrder, "提交供应商订单失败");
            throw new OrderException("提交供应商订单失败");
        }
    }


    public ServiceResult checkPreOrder(String purchaseNumber) {
        List<ShopPurchaseSubOrder> shopPurchaseSubOrderList = purchaseSubOrderService.getAllSubOrder(purchaseNumber);
        List<ShopPurchaseSubOrder> unFinishedOrder = shopPurchaseSubOrderList.stream().filter(e -> e.getOrderState() == 9).collect(Collectors.toList());
        if (unFinishedOrder.size() > 0) {
            return ServiceResult.succ("wait", "订单正在处理中...");
        }
        List<ShopPurchaseSubOrder> okOrder = shopPurchaseSubOrderList.stream().filter(e -> e.getOrderState() == 10).collect(Collectors.toList());
        if (okOrder.size() == shopPurchaseSubOrderList.size()) {
            return ServiceResult.succ("ok", "预下单成功");
        }
        return ServiceResult.succ("error", shopPurchaseSubOrderList.get(0).getFailReason());
    }

    @NotNull
    private List<List<PurchaseOrderDetailSaveDto>> getOrderGroupListNew(List<PurchaseOrderDetailSaveDto> purchaseOrderDetailSaveDtoList) {
        final List<List<PurchaseOrderDetailSaveDto>> orderGroupList = new ArrayList<>();

        // 新增的采购订单商品明细，根据商品的供应商编码、商品类型、合同号分组
        purchaseOrderDetailSaveDtoList.stream()
                .collect(Collectors.groupingBy(ordDetail -> Arrays.asList(
                        ordDetail.getSupplierCode(),
                        ordDetail.getGoodsModel(),
                        ordDetail.getContractNumber(),
                        ordDetail.getSettlementType()
                ), Collectors.toList()))
                .forEach((supplierCode, orderDetailList) -> orderGroupList.add(orderDetailList));

        List<List<PurchaseOrderDetailSaveDto>> splitOrderList = new ArrayList<>();
        Iterator<List<PurchaseOrderDetailSaveDto>> orderListIterator = orderGroupList.iterator();
        while (orderListIterator.hasNext()) {
            List<PurchaseOrderDetailSaveDto> orderList = orderListIterator.next();
            if (orderList.size() > orderSplitLimit.intValue()) {
                List<List<PurchaseOrderDetailSaveDto>> splitItemOrderList = splitIntoSubsets(orderList);
                splitOrderList.addAll(splitItemOrderList);
                orderListIterator.remove();
            }
        }
        orderGroupList.addAll(splitOrderList);
        return orderGroupList;
    }

    /**
     * 对订单进行拆分
     *
     * @param orderList
     * @return
     */
    private List<List<PurchaseOrderDetailSaveDto>> splitIntoSubsets(List<PurchaseOrderDetailSaveDto> orderList) {
        List<List<PurchaseOrderDetailSaveDto>> subOrderList = new ArrayList<>();
        BigDecimal postage = getSupplierPostage(orderList.get(0).getSupplierCode());
        if (postage != null) {
            Collections.sort(orderList, Comparator.comparing(PurchaseOrderDetailSaveDto::getSupplierTotalPriceTax)); // 先对数据按供应商含税小计进行排序，便于后续处理
            toBacktrackOrder(orderList, 0, subOrderList, new ArrayList<>(), postage);
        } else {
            toSplitOrderList(orderList, subOrderList);
        }
        return subOrderList;
    }

    /**
     * 获取供应商邮费
     *
     * @param supplierCode
     * @return
     */
    private BigDecimal getSupplierPostage(String supplierCode) {
        List<SystemDictDataEntity> list = dictDataSrv.getDictDatasByDictType("C0001");
        if (CollUtil.isNotEmpty(list)) {
            list = list.stream().filter(item -> supplierCode.equalsIgnoreCase(item.getValue())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                return new BigDecimal(list.get(0).getLabel());
            }
        }
        return null;
    }


    /**
     * 将订单拆分为指定大小的子订单
     *
     * @param orderList
     * @param subOrderList
     */
    private void toSplitOrderList(List<PurchaseOrderDetailSaveDto> orderList, List<List<PurchaseOrderDetailSaveDto>> subOrderList) {
        int currentIndex = 0;
        int listSize = orderList.size();
        while (currentIndex < listSize) {
            int endIndex = Math.min(currentIndex + orderSplitLimit.intValue(), listSize);
            List<PurchaseOrderDetailSaveDto> subList = orderList.subList(currentIndex, endIndex);
            subOrderList.add(new ArrayList<>(subList));
            currentIndex = endIndex;
        }
    }

    /**
     * 使用回溯算法拆分订单
     *
     * @param orderList
     * @param start
     * @param subOrderList
     * @param currentSubset
     * @param limitPostage
     */
    private void toBacktrackOrder(List<PurchaseOrderDetailSaveDto> orderList, int start, List<List<PurchaseOrderDetailSaveDto>> subOrderList, List<PurchaseOrderDetailSaveDto> currentSubset, BigDecimal limitPostage) {
        // 如果当前子集的和已经大于或等于59，或者已经处理完所有元素，则将当前子集添加到结果集中
        if (currentSubset.stream().map(PurchaseOrderDetailSaveDto::getSupplierTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(limitPostage) >= 0 || start == orderList.size()) {
            if (currentSubset.stream().map(PurchaseOrderDetailSaveDto::getSupplierTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(limitPostage) >= 0) {
                subOrderList.add(new ArrayList<>(currentSubset));
            }
            return;
        }

        // 尝试将当前元素添加到当前子集中
        for (int i = start; i < Math.min(start + orderSplitLimit.intValue(), orderList.size()); i++) {
            currentSubset.add(orderList.get(i));

            // 递归处理下一个元素
            toBacktrackOrder(orderList, i + 1, subOrderList, currentSubset, limitPostage);

            // 回溯，撤销当前选择
            currentSubset.remove(currentSubset.size() - 1);

            // 剪枝：如果当前子集已经很大，或者下一个元素加入后和会远大于59，则停止继续尝试
            BigDecimal sum = currentSubset.stream().map(PurchaseOrderDetailSaveDto::getSupplierTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal nextElement = orderList.get(i).getSupplierTotalPriceTax();
            if (currentSubset.size() > orderSplitLimit.intValue() - (orderList.size() - i - 1)
                    || sum.add(nextElement).compareTo(limitPostage.multiply(new BigDecimal(2))) > 0) {
                break;
            }
        }
    }

    /**
     * 获取东本定制单号
     *
     * @param
     * @return
     */
    private String getOrderShortSn() {
        String orderStart = "6";
        Long orderSn = null;
        //获取当天日期
        String nowDate = DateUtils.format(new Date(), "yyMM");
        // 以供应商编码与当天日期为key
        String key = orderStart + nowDate;
        RBucket<Long> bucket = redissonClient.getBucket(key, new LongCodec());
        try {
            orderSn = bucket.get();
        } catch (Exception e) {
            // redis 出现异常时执行
            log.error("生成订单短号时redis异常...", e);
            Long number = 0L;
            String oldMixShortOrderSn = this.baseMapper.queryHondaMixShortOrderSn(orderStart + nowDate);
            if (org.apache.commons.lang.StringUtils.isNotBlank(oldMixShortOrderSn)) {
                number = Long.valueOf(oldMixShortOrderSn.substring(oldMixShortOrderSn.length() - 4, oldMixShortOrderSn.length()));
            }
            bucket.set(number + 1L, 31, TimeUnit.DAYS);
            return key + (number + 1);
        }
        System.out.println(orderSn);
        if (orderSn == null || 0L == orderSn) {
            String oldMixShortOrderSn = this.baseMapper.queryHondaMixShortOrderSn(orderStart + nowDate);
            //缓存三十一天
            long invalidTime = 40 * 24 * 60 * 60;
            if (org.apache.commons.lang.StringUtils.isBlank(oldMixShortOrderSn)) {
                Long num = 0L;
                bucket.set(num, 31, TimeUnit.DAYS);
            } else {
                int number = Integer.valueOf(oldMixShortOrderSn.substring(oldMixShortOrderSn.length() - 4, oldMixShortOrderSn.length()));
                //stringRedisTemplate.opsForValue().set(key,number+"",invalidTime);
                bucket.set(Long.valueOf(number), 31, TimeUnit.DAYS);
            }

        }
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key);
        long incr = rAtomicLong.incrementAndGet();
//        int num = Integer.valueOf(key);
//        long incr = stringRedisTemplate.opsForValue().increment(key, 1L);
        String incrStr = String.format("%06d", incr);
        return key + incrStr;
    }


    public void cancelNotMakePurchaseOrderJob(Integer limitMinute, Integer isPlatformReconciliation) {
        log.info("超时未制单自动取消任务");
        // 默认十五分钟
        limitMinute = limitMinute == null ? 15 : limitMinute;
        // 默认平台内
        isPlatformReconciliation = isPlatformReconciliation==null?1:isPlatformReconciliation;
        long oneDayMillisecond = limitMinute * 60 * 1000L;
        long overTime1 = System.currentTimeMillis() - oneDayMillisecond;
        Date overTime = new Date(overTime1);
        String failReason = "超时未制单自动取消并删除";
        List<ShopPurchaseOrder> purchaseOrderArray = purchaseOrderMapper.queryTimeOutNotMakeOrder(overTime, isPlatformReconciliation);
        for (ShopPurchaseOrder purchaseOrderItem : purchaseOrderArray) {
            this.toCancelNotOkPurchaseOrder(purchaseOrderItem, failReason);
            this.actTaskFeign.sysCancelTask(purchaseOrderItem.getPurchaseNumber(), failReason, purchaseOrderItem.getTenantId());
        }
    }

    private void toCancelNotOkPurchaseOrder(ShopPurchaseOrder purchaseOrderItem, String failReason) {
        log.info("取消订单3 ------{}，{}，{}", purchaseOrderItem.getPurchaseNumber(), failReason);
        final List<ShopPurchaseSubOrder> purchaseSubOrderList = this.purchaseSubOrderService.queryConfirmSubOrder(purchaseOrderItem.getPurchaseNumber());
        final ShopPurchaseOrder shopPurchaseOrder = new ShopPurchaseOrder();
        for (final ShopPurchaseSubOrder purchaseSubOrder : purchaseSubOrderList) {
            try {
                this.remoteInfoManage.cancelOrder(purchaseSubOrder.getOrderNumber(), purchaseSubOrder.getSupplierOrderNumber(), purchaseSubOrder.getSupplierCode());
            } catch (Exception data) {
                log.error(purchaseSubOrder.getSupplierName() + "订单" + purchaseSubOrder.getSupplierOrderNumber() + "取消失败", data);
            }
            shopPurchaseOrder.setPurchaseNumber(purchaseSubOrder.getPurchaseNumber());
            //恢复库存
            this.recoverGoodsStock(purchaseSubOrder.getOrderNumber(), purchaseSubOrder.getSupplierCode());

            final UpdateWrapper<ShopPurchaseSubOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ShopPurchaseSubOrder::getOrderId, purchaseSubOrder.getOrderId())
                    .set(ShopPurchaseSubOrder::getOrderState, 0)
                    .set(ShopPurchaseSubOrder::getFailReason, failReason)
                    .set(ShopPurchaseSubOrder::getIsEnable, 0);
            purchaseSubOrderService.update(updateWrapper);

            UpdateWrapper<ShopPurchaseSubOrderDetail> uw = new UpdateWrapper<>();
            uw.lambda().eq(ShopPurchaseSubOrderDetail::getOrderId, purchaseSubOrder.getOrderId())
                    .set(ShopPurchaseSubOrderDetail::getOrderDetailState, 0)
                    .set(ShopPurchaseSubOrderDetail::getIsEnable, 0);
            purchaseSubOrderDetailService.update(uw);
        }
        final UpdateWrapper<ShopPurchaseOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ShopPurchaseOrder::getPurchaseNumber, purchaseOrderItem.getPurchaseNumber())
                .set(ShopPurchaseOrder::getPurchaseState, 20)
                .set(ShopPurchaseOrder::getCanConfirm, PreOrderConfirmTypeEnum.NOT_OK.getCode())
                .set(ShopPurchaseOrder::getIsEnable, 0);
        this.update(updateWrapper);
    }

    /**
     * 获取岚图门店信息
     * @return
     */
    public List<VoyahStoreInfoDto> queryVoyahStoreInfos() {
        return dfgSapService.queryVoyahStoreInfos();
    }

    public String queryVoyahSrmOrderSn(String s) {
        return this.baseMapper.queryVoyahSrmOrderSn(s);
    }

    public ServiceResult<?> checkGoodsIsBuyed(String activityId, Long goodsId) {
        if (StrUtil.isBlank(activityId) || goodsId==null) {
            return ServiceResult.error("商品id活动Id不能为空");
        }
        final LoginUser user = LocalUserHolder.get();
        List<ShopPurchaseOrder> list = this.baseMapper.checkGoodsIsBuyed(activityId, goodsId, user.getId());
        if(CollectionUtil.isEmpty(list)){
            return ServiceResult.succ(false);
        }
        return ServiceResult.succ(true);
    }

    public void saveVirtualPurchaseExt(List<ShopPurchaseSubOrder> subOrderList, String account, List<ShopPurchaseSubOrderDetail> orderDetailList) {
        if (!GoodsModelEnum.VIRTUAL.getModelCode().equals(subOrderList.get(0).getOrderModel())) {
            return;
        }
        ShopVirtualPurchaseExtended virtualPurchaseExtended = new ShopVirtualPurchaseExtended();
        virtualPurchaseExtended.setVirtualPurchaseId(UUID.randomUUID().toString().replaceAll("-", ""));
        virtualPurchaseExtended.setPurchaseNumber(subOrderList.get(0).getPurchaseNumber());
        virtualPurchaseExtended.setOrderNumber(subOrderList.get(0).getOrderNumber());
        virtualPurchaseExtended.setRechargeAccount(account);
        if ("直充类".equalsIgnoreCase(orderDetailList.get(0).getThirdLevelGcName())) {
            virtualPurchaseExtended.setVirtualType("1");
        } else if ("卡密类".equalsIgnoreCase(orderDetailList.get(0).getThirdLevelGcName())) {
            virtualPurchaseExtended.setVirtualType("2");
        }
        shopVirtualPurchaseExtendedService.save(virtualPurchaseExtended);
    }
}
