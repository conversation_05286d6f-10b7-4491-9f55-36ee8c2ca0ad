package com.ly.yph.api.virtualgoods.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.manage.GoodsUpDownManage;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.goods.workflow.WorkFlowManager;
import com.ly.yph.api.order.entity.ShopDelivery;
import com.ly.yph.api.order.entity.ShopDeliveryDetail;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.enums.DeliveryStateEnum;
import com.ly.yph.api.order.enums.ReceiveStateEnum;
import com.ly.yph.api.order.service.ShopDeliveryDetailService;
import com.ly.yph.api.order.service.ShopDeliveryService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.settlement.common.service.DeliveryForBillProcessService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.virtualgoods.config.YzhVirtualConfig;
import com.ly.yph.api.virtualgoods.dto.*;
import com.ly.yph.api.virtualgoods.entity.ShopVirtualPurchaseExtended;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.ly.yph.api.goods.service.ShopGoodsDetailService.replaceAmp;

/**
 * <AUTHOR>
 * @date 2025年07月11日
 */
@Service
@Slf4j
public class ShopVirtualGoodsService {

    @Resource
    private ShopVirtualGoodsService self;
    @Resource
    private ShopGoodsService goodsService;
    @Resource
    private CodeGeneral codeGeneral;
    @Resource
    private ShopGoodsDetailService goodsDetailService;
    @Resource
    private YphStandardClassService yphStandardClassService;
    @Resource
    private ShopSupplierService supplierService;
    @Resource
    private ShopBrandService shopBrandService;
    @Resource
    private ShopGoodsPriceService priceService;
    @Resource
    private SystemOrganizationService organizationService;
    @Resource
    private ShopGoodsStockService shopGoodsStockService;
    @Resource
    private WorkFlowManager workFlowManager;
    @Resource
    private GoodsUpDownManage goodsUpDownManage;
    @Resource
    private YZHCenterService yzhCenterService;

    @Resource
    private YzhVirtualConfig yzhVirtualConfig;
    @Resource
    private ShopPurchaseSubOrderService subOrderService;

    @Resource
    private ShopDeliveryService shopDeliveryService;

    @Resource
    private ShopDeliveryDetailService shopDeliveryDetailService;
    @Resource
    private ShopPurchaseSubOrderDetailService orderDetailService;
    @Resource
    private DeliveryForBillProcessService deliveryForBillProcessService;

    @Resource
    private ShopVirtualPurchaseExtendedService shopVirtualPurchaseExtendedService;


    @Transactional(rollbackFor = Exception.class)
    public ServiceResult saveVirtualGoods(SaveVirtualGoodsDto param) {
        if (param.getSupplierType().equalsIgnoreCase("1")) {
            param.setSupplierCode("GYSYZHVIRTUAL");
        } else {
            return ServiceResult.error("参数异常：未匹配到供应商编码");
        }

        ShopGoods exist = goodsService.selectBySkuAndSupplier(param.getGoodsSku(), param.getSupplierCode());

        // 如果已经存在了
        if (exist != null) {
            //要修改了
            ShopGoods newShopGoods = new ShopGoods();
            newShopGoods.setPrice(param.getGoodsPactPrice());
            newShopGoods.setNakePrice(param.getGoodsPactNakedPrice());
            newShopGoods.setUpdateTime(new Date());
            newShopGoods.setGoodsId(exist.getGoodsId());
            self.fillGoodsClass(newShopGoods, param.getGoodsClass());
            goodsService.updateById(newShopGoods);

            var shopGoodsPrices = priceService.list(new LambdaQueryWrapperX<ShopGoodsPrice>().eq(ShopGoodsPrice::getGoodsCode,exist.getGoodsCode()));
            if (!shopGoodsPrices.isEmpty()) {
                var shopGoodsPrice = shopGoodsPrices.get(0);
                BigDecimal taxRate = new BigDecimal(param.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        .add(new BigDecimal(1));

                shopGoodsPrice.setGoodsPactPrice(param.getGoodsPactPrice())
                        .setGoodsPactNakedPrice(param.getGoodsPactPrice().divide(taxRate, 4, RoundingMode.HALF_UP))
                        .setGoodsOriginalPrice(param.getGoodsOriginalPrice())
                        .setGoodsOriginalNakedPrice(param.getGoodsOriginalPrice().divide(taxRate, 4, RoundingMode.HALF_UP))
                        .setUpdateTime(new Date());
                priceService.updateById(shopGoodsPrice);
            }
            //修改商城订单表的供应商订单号
            UpdateWrapper<ShopGoodsDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ShopGoodsDetail::getGoodsCode, exist.getGoodsCode())
                    .set(ShopGoodsDetail::getGoodsImage, replaceAmp(StrUtil.subBefore(param.getImageArray(), ";", false)))
                    .set(ShopGoodsDetail::getGoodsImageMore, replaceAmp(StrUtil.subAfter(param.getImageArray(), ";", false)))
                    .set(ShopGoodsDetail::getOrganizationId, exist.getOrganizationId());
            goodsDetailService.update(updateWrapper);
            return ServiceResult.succ();
        }

        //保存shop_goods shop_brand
        ShopGoods shopGoods = self.createVirtualGoodsInfo(param);

        //保存shop_goods_price
        self.fillShopGoodsPrice(param,shopGoods);

        //shop_goods_detail
        self.fillShopGoodsDetail(param,shopGoods);

        //shop_goods_stock
        self.fillShopGoodsStock(param,shopGoods);

        workFlowManager.goodsContractOperation(CollectionUtil.newArrayList(shopGoods.getGoodsId()), CollectionUtil.newArrayList(shopGoods), false, new HashMap<>());

        goodsUpDownManage.goodsIdUpToLiteFlow(CollectionUtil.newArrayList(shopGoods.getGoodsId()));

        return ServiceResult.succ();
    }

    public ShopGoods createVirtualGoodsInfo(SaveVirtualGoodsDto params) {
        ShopGoods shopGoods = BeanUtil.copyProperties(params, ShopGoods.class);
        shopGoods.setGoodsCode(this.codeGeneral.getProductCode(params.getSupplierCode()));
        shopGoods.setGoodsSku(params.getGoodsSku());
        shopGoods.setGoodsModel(2);
        self.fillBrand(shopGoods, params.getBrandName());
        self.fillGoodsClass(shopGoods, params.getGoodsClass());
        self.fillSupplierInfo(shopGoods, params.getSupplierCode());
        shopGoods.setAuditState(1);
        //保存过来的默认虚拟商品，审核通过
        goodsService.save(shopGoods);
        return shopGoods;
    }

    public void fillBrand(ShopGoods shopGoods, String brandName) {
        ShopBrand brand = new ShopBrand();
        brand.setBrandName(brandName);
        shopBrandService.saveShopBrand(brand);
        shopGoods.setBrandId(String.valueOf(brand.getBrandId()));
        shopGoods.setBrandName(brandName);
    }

    public void fillGoodsClass(ShopGoods shopGoods, String classCode) {
        YphStandardClassEntity goodsClass = this.yphStandardClassService.selectByCode(classCode);
        String[] codes = goodsClass.getClassPath().split(",");
        List<YphStandardClassEntity> scList = yphStandardClassService.list(new LambdaQueryWrapperX<YphStandardClassEntity>().in(YphStandardClassEntity::getClassCode, codes));
        for (YphStandardClassEntity item : scList) {
            switch (item.getClassLevel()) {
                case 1:
                    shopGoods.setFirstClass(item.getClassCode());
                    shopGoods.setFirstLevelGcid(String.valueOf(item.getStandardClassId()));
                    shopGoods.setFirstClassName(item.getClassName());
                    break;
                case 2:
                    shopGoods.setSecondClass(item.getClassCode());
                    shopGoods.setSecondLevelGcid(String.valueOf(item.getStandardClassId()));
                    shopGoods.setSecondClassName(item.getClassName());
                    break;
                case 3:
                    shopGoods.setThirdClass(item.getClassCode());
                    shopGoods.setThirdLevelGcid(String.valueOf(item.getStandardClassId()));
                    shopGoods.setThirdClassName(item.getClassName());
                    break;
                default:
                    break;
            }
        }
        shopGoods.setSupplierClass(goodsClass.getClassCode());
        shopGoods.setSupplierClassId(null);
        shopGoods.setSupplierClassName(goodsClass.getClassName());
    }
    public void fillSupplierInfo(ShopGoods se, String supplierCode) {
        SystemOrganization organization = this.organizationService.getOrganizationByCode(supplierCode);
        se.setOrganizationId(organization.getId());
        se.setSupplierCode(organization.getCode());
        ShopSupplier supplier = supplierService.selectByCode(organization.getCode());
        se.setSupplierName(supplier.getSupplierShortName());
        se.setSupplierType(supplier.getSupplierType());
    }
    public ShopGoodsPrice fillShopGoodsPrice(SaveVirtualGoodsDto param, ShopGoods shopGoods) {
        ShopGoodsPrice price = new ShopGoodsPrice();
        BeanUtil.copyProperties(param, price);
        BigDecimal taxRate = new BigDecimal(param.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));

        price.setGoodsCode(shopGoods.getGoodsCode())
                .setGoodsPactPrice(param.getGoodsPactPrice().setScale(2, RoundingMode.HALF_UP))
                .setGoodsOriginalPrice(param.getGoodsOriginalPrice().setScale(2, RoundingMode.HALF_UP))
                .setGoodsPactNakedPrice(param.getGoodsPactPrice().divide(taxRate, 2, RoundingMode.HALF_UP))
                .setGoodsOriginalNakedPrice(param.getGoodsOriginalPrice().divide(taxRate, 2, RoundingMode.HALF_UP))
                .setGoodsSku(shopGoods.getGoodsSku())
                .setOrganizationId(shopGoods.getOrganizationId());
        this.priceService.saveItem(price);
        return price;
    }

    public ShopGoodsDetail fillShopGoodsDetail(SaveVirtualGoodsDto param, ShopGoods shopGoods) {
        ShopGoodsDetail detail = new ShopGoodsDetail();
        com.ly.yph.core.util.BeanUtil.copyProperties(param, detail);
        detail.setGoodsSku(shopGoods.getGoodsSku()).setGoodsCode(shopGoods.getGoodsCode());
        com.ly.yph.core.util.BeanUtil.copyProperties(param, detail);
        detail.setGoodsImage(replaceAmp(StrUtil.subBefore(param.getImageArray(), ";", false)))
                .setGoodsImageMore(replaceAmp(StrUtil.subAfter(param.getImageArray(), ";", false)))
                .setOrganizationId(shopGoods.getOrganizationId());
        this.goodsDetailService.save(detail);
        return detail;
    }

    public ShopGoodsStock fillShopGoodsStock(SaveVirtualGoodsDto param, ShopGoods shopGoods) {
        ShopGoodsStock stock = new ShopGoodsStock()
                .setOrganizationId(shopGoods.getOrganizationId())
                .setGoodsSku(shopGoods.getGoodsSku())
                .setStockAlert(param.getGoodsMoq())
                .setGoodsCode(shopGoods.getGoodsCode())
                .setStockAvailable(param.getStockAvailable());
        this.shopGoodsStockService.save(stock);
        return stock;
    }

    public List<QueryVirtualGoodsInfoDto> queryGoodsInfo(YZHGoodInfoParamDto goodsInfoParam) {
        List<YZHGoodsInfoDto> yzhGoodsInfoDtos = yzhCenterService.queryGoodsInfo(goodsInfoParam);
        if (CollectionUtil.isEmpty(yzhGoodsInfoDtos)) {
            return Lists.newArrayList();
        }
        List<QueryVirtualGoodsInfoDto> queryVirtualGoodsInfoDtos = BeanUtil.copyToList(yzhGoodsInfoDtos, QueryVirtualGoodsInfoDto.class);
        for (QueryVirtualGoodsInfoDto yzhGoodsInfoDto : queryVirtualGoodsInfoDtos) {
            String goodsSku = yzhGoodsInfoDto.getGoodsSkuCode();
            if (goodsInfoParam.getType() == 1) {
                ShopGoods shopGoods = goodsService.selectOneBySkuAndSupplier(goodsSku, "GYSYZHVIRTUAL");
                if (Objects.isNull(shopGoods)) {
                    continue;
                }
                yzhGoodsInfoDto.setTaxCode(shopGoods.getTaxCode());
                yzhGoodsInfoDto.setTaxRate(shopGoods.getTaxRate());
            }else{
                if(!Objects.isNull(yzhGoodsInfoDto.getTaxRate())){
                    yzhGoodsInfoDto.setTaxRate(yzhGoodsInfoDto.getTaxRate() * 100);
                }
            }
        }
        return queryVirtualGoodsInfoDtos;
    }


    @Transactional(rollbackFor = Exception.class)
    public ServiceResult processVirtualOrderState() {
        List<ShopPurchaseSubOrder> processList = subOrderService.list(new LambdaQueryWrapperX<ShopPurchaseSubOrder>()
                .eq(ShopPurchaseSubOrder::getOrderModel, 2)
                .eq(ShopPurchaseSubOrder::getOrderState, 20)
                .in(ShopPurchaseSubOrder::getSupplierCode, Lists.newArrayList(yzhVirtualConfig.getCode())));

        List<ShopPurchaseSubOrder> yzhV = processList.stream().filter(item -> yzhVirtualConfig.getCode().equalsIgnoreCase(item.getSupplierCode())).collect(Collectors.toList());
        self._processYzhVirtualOrder(yzhV);
        return ServiceResult.succ();
    }

    /**
     * 1、充值中，2、充值完成 3、充值失败
     * @param yzhV
     */
    public void _processYzhVirtualOrder(List<ShopPurchaseSubOrder> yzhV) {
        if (CollectionUtil.isEmpty(yzhV)) {
            return;
        }
        List<String> removeOrderNumber = Lists.newArrayList();
        for (ShopPurchaseSubOrder subOrder : yzhV) {
            ShopVirtualPurchaseExtended one = shopVirtualPurchaseExtendedService.getOne(new LambdaQueryWrapperX<ShopVirtualPurchaseExtended>().eq(ShopVirtualPurchaseExtended::getOrderNumber, subOrder.getOrderNumber()));
            if ("2".equalsIgnoreCase(one.getVirtualType())) {
                //需要查一下卡密
                List<YZHCardOrderDetailDto> yzhCardOrderDetailDtos = yzhCenterService.queryCardOrderInfo(subOrder.getSupplierOrderNumber());
                if (CollectionUtil.isEmpty(yzhCardOrderDetailDtos)){
                    continue;
                }
                YZHCardOrderDetailDto yzhCardOrderDetailDto = yzhCardOrderDetailDtos.get(0);
                // 存卡号卡密、优惠券及优惠券跳转连接
                shopVirtualPurchaseExtendedService.update(new UpdateWrapper<ShopVirtualPurchaseExtended>().lambda()
                        .eq(ShopVirtualPurchaseExtended::getOrderNumber, subOrder.getOrderNumber())
                        .set(ShopVirtualPurchaseExtended::getVirtualLink, yzhCardOrderDetailDto.getUrl())
                        .set(ShopVirtualPurchaseExtended::getVirtualAccount, yzhCardOrderDetailDto.getCardCode())
                        .set(ShopVirtualPurchaseExtended::getVirtualPassword, yzhCardOrderDetailDto.getCardPwd())
                        .set(ShopVirtualPurchaseExtended::getVirtualOverdue, yzhCardOrderDetailDto.getUseEndTime())
                );
            }
        }
        //1、充值中，2、充值完成 3、充值失败
        Map<String, Integer> stringIntegerMap = yzhCenterService.queryOrderInfoBatch(yzhV.stream().map(ShopPurchaseSubOrder::getSupplierOrderNumber).collect(Collectors.toList()));

        for (ShopPurchaseSubOrder subOrder : yzhV) {
            log.info("【_processYzhVirtualOrder】处理订单状态：" + subOrder.getOrderNumber());
            if (stringIntegerMap.get(subOrder.getSupplierOrderNumber()) == 1) {
                //不管，充值中
                log.info("【_processYzhVirtualOrder】订单充值中：" + subOrder.getOrderNumber());
            }else if(stringIntegerMap.get(subOrder.getSupplierOrderNumber())==2){
                log.info("【_processYzhVirtualOrder】订单充值完成：" + subOrder.getOrderNumber());
                //充值完成
                //模拟插入已妥投包裹  剩下的交给友福利 T+0自动收货自动出账
                ShopDelivery shopDelivery = virtualOrderDelivery(subOrder);
                //还是要改订单状态成30
                virtualOrderStateUpdate(subOrder,30);
                //更新订单明细生命周期
                deliveryForBillProcessService.deliveryInfoProcess(Collections.singletonList(shopDelivery.getId()));
                deliveryForBillProcessService.deliveryOrderArriveProcess(shopDelivery.getId());
            }else if(stringIntegerMap.get(subOrder.getSupplierOrderNumber())==3){
                log.info("【_processYzhVirtualOrder】订单充值失败：" + subOrder.getOrderNumber());
                //充值失败
                //改状态
                virtualOrderStateUpdate(subOrder,-1);
            }
        }
    }

    public ShopDelivery virtualOrderDelivery(ShopPurchaseSubOrder order) {
        val newEnt = new ShopDelivery();
        newEnt.setPackageId(order.getOrderNumber());
        newEnt.setSupplierOrderId(order.getSupplierOrderNumber());
        newEnt.setSupplierCode(order.getSupplierCode());
        newEnt.setSupplierShortName(order.getSupplierName());
        newEnt.setOrderId(order.getOrderId());
        newEnt.setOrderNumber(order.getOrderNumber());
        newEnt.setSupDeliveryState(DeliveryStateEnum.DELIVERY_OK.getCode());
        newEnt.setSupReceivingTime(new Date());
        newEnt.setCusReceivingState(ReceiveStateEnum.RECEIVE_WAIT.getCode());
        newEnt.setDeliveryCode("虚拟商品订单完成模拟发货");
        newEnt.setDeliveryName("");
        newEnt.setOrganizationId(order.getOrganizationId());
        newEnt.setTenantId(order.getTenantId());
        newEnt.setDeliveryTime(new Date() );
        shopDeliveryService.save(newEnt);

        val orderDetailList = orderDetailService.getOrderDetailByOrderId(order.getOrderId());
        List<ShopDeliveryDetail> skus = Lists.newArrayList();
        for (ShopPurchaseSubOrderDetail detail : orderDetailList) {
            val dd = new ShopDeliveryDetail();
            BeanUtil.copyProperties(detail, dd);
            dd.setDeliveryId(newEnt.getId()).setGoodsModel(order.getOrderModel()).setGoodsSku(detail.getGoodsSku()).setDeliveryNum(Convert.toInt(detail.getConfirmNum()));

            dd.setSupplierTotalPriceTax(detail.getSupplierUnitPriceTax().multiply(new BigDecimal(dd.getDeliveryNum())).setScale(2, RoundingMode.HALF_UP));
            dd.setSupplierTotalPriceNaked(detail.getSupplierUnitPriceNaked().multiply(new BigDecimal(dd.getDeliveryNum())).setScale(2, RoundingMode.HALF_UP));
            //未税单价 * 数量
            dd.setGoodsTotalPriceNaked(detail.getGoodsUnitPriceNaked().multiply(new BigDecimal(dd.getDeliveryNum())).setScale(2, RoundingMode.HALF_UP));
            //税率
            BigDecimal taxRate = new BigDecimal(detail.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(new BigDecimal(1));
            //未税单价 * 税率 * 数量
            dd.setGoodsTotalPriceTax(detail.getGoodsUnitPriceNaked().multiply(taxRate).multiply(new BigDecimal(dd.getDeliveryNum())).setScale(2, RoundingMode.HALF_UP));
            skus.add(dd);
        }
        if(CollUtil.isNotEmpty(skus)){
            shopDeliveryDetailService.saveBatch(skus);

        }
        return newEnt;
    }


    public void virtualOrderStateUpdate(ShopPurchaseSubOrder order,Integer state){
        subOrderService.updateOrderState(order.getOrderId(),state);
        UpdateWrapper<ShopPurchaseSubOrderDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ShopPurchaseSubOrderDetail::getOrderId,order.getOrderId()).set(ShopPurchaseSubOrderDetail::getOrderDetailState,state);
        orderDetailService.update(updateWrapper);
    }

}
