package com.ly.yph.api.settlement.common.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.settlement.common.entity.*;
import com.ly.yph.api.settlement.common.enums.BillCustomerTypeEnum;
import com.ly.yph.api.settlement.common.enums.ReconciliationStatusEnum;
import com.ly.yph.api.settlement.common.enums.SupplierBillDetailStagingFlagEnum;
import com.ly.yph.api.settlement.common.mapper.SettleShopBillPostageDetailMapper;
import com.ly.yph.api.settlement.common.vo.bill.*;
import com.ly.yph.api.settlement.supplier.dto.RemoveToNextMonthDto;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillService;
import com.ly.yph.api.system.dto.OrderExportExcelVO;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettleShopBillPostageDetailService extends ServiceImpl<SettleShopBillPostageDetailMapper, SettleShopBillPostageDetail> {

    /**
     * 东风商城保存邮费
     *
     * @param settleShopBill   账单
     * @param postageDetailMap 邮费列表
     */
    public void saveShopBillPostageDetail(SettleShopBill settleShopBill, Map<String, SettleBillPool> postageDetailMap) {
        if (postageDetailMap.isEmpty()) {
            return;
        }

        List<SettleBillPool> billPoolsPostage = postageDetailMap.entrySet().stream().map(item -> item.getValue()).collect(Collectors.toList());

        List<String> orderNumbers = billPoolsPostage.stream().map(SettleBillPool::getOrderNumber).distinct().collect(Collectors.toList());
        QueryWrapper<SettleShopBillPostageDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBillPostageDetail::getCustomerCode, settleShopBill.getCustomerCode())
                .in(SettleShopBillPostageDetail::getOrderNumber, orderNumbers);
        //查询历史订单是否存在邮费
        List<SettleShopBillPostageDetail> billPostageDetails = list(queryWrapper);


        List<SettleShopBillPostageDetail> validData = new ArrayList<>();
        if (CollectionUtils.isEmpty(billPostageDetails)) {
            //直接新增邮费明细
            validData = getBillPostageDetailList(billPoolsPostage, settleShopBill);
        } else {
            //过滤掉集合中的，留下来的新增明细
            List<SettleBillPool> collect1 = billPoolsPostage.stream().filter(item -> {
                List<SettleShopBillPostageDetail> collect = billPostageDetails.stream().filter(e -> e.getOrderNumber().equals(item.getOrderNumber())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    //不存在历史邮费
                    return true;
                } else {
                    //存在历史邮费
                    return false;
                }
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect1)) {
                //全部是历史的
                return;
            }
            validData = getBillPostageDetailList(collect1, settleShopBill);
        }
        saveBatch(validData);
    }


    /**
     * 邮费统计保存
     *
     * @param settleBillPools 有效的邮费相关的账单池数据
     * @param settleShopBill  对应的客户账单
     * @return
     */
    public List<SettleShopBillPostageDetail> getBillPostageDetailList(List<SettleBillPool> settleBillPools, SettleShopBill settleShopBill) {

        List<SettleShopBillPostageDetail> shopBillPostageDetails = new ArrayList<>();

        for (SettleBillPool billPool : settleBillPools) {
            SettleShopBillPostageDetail postageDetail = new SettleShopBillPostageDetail();
            postageDetail.setOrderNumber(billPool.getOrderNumber());
            postageDetail.setSupplierOrderNumber(billPool.getSupplierOrderNumber());
            postageDetail.setApplyUserId(billPool.getApplyUserId());
            postageDetail.setApplyUserName(billPool.getApplyUserName());
            postageDetail.setApplyDeptId(billPool.getApplyDeptId());
            postageDetail.setApplyDeptName(billPool.getApplyDeptName());
            postageDetail.setApplyTime(billPool.getApplyTime());
            postageDetail.setAuditTime(billPool.getAuditTime());
            postageDetail.setPostage(settleShopBill.getCustomerType().equals(BillCustomerTypeEnum.COMPANY.getCode()) ? billPool.getCompanyPostage() : billPool.getSupplierPostage());
            postageDetail.setBillId(settleShopBill.getBillId());
            postageDetail.setBillSn(settleShopBill.getBillSn());
            postageDetail.setCustomerCode(settleShopBill.getCustomerCode());
            postageDetail.setCustomerName(settleShopBill.getCustomerName());
            postageDetail.setStoreCode(BillCustomerTypeEnum.COMPANY.getCode().equals(settleShopBill.getCustomerType()) ? billPool.getSupplierCode() : billPool.getCompanyCode());
            postageDetail.setStoreName(BillCustomerTypeEnum.COMPANY.getCode().equals(settleShopBill.getCustomerType()) ? billPool.getSupplierName() : billPool.getCompanyName());
            postageDetail.setTaxRate(6);
            postageDetail.setInvoiceFlag(ReconciliationStatusEnum.RECONCILIATION_BILL_CHECKED.getCode());
            //计算含税总金额 不计算了 统一再后面计算 更新账单总金额
//            settleShopBill.setTotalTaxAmount(settleShopBill.getTotalTaxAmount().add(postageDetail.getPostage()));
            shopBillPostageDetails.add(postageDetail);
        }

        return shopBillPostageDetails;
    }

    public List<SettleShopBillPostageDetail> yflCheckedPostage(SettleShopBill settleShopBill, List<SettleBillPoolYflCustomer> newSettleBillPoolYflCustomers,List<OrderExportExcelVO> orderExportExcelVOS) {

        QueryWrapper<SettleShopBillPostageDetail> postageDetailQueryWrapper = new QueryWrapper<>();
        postageDetailQueryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, settleShopBill.getBillId());
        List<SettleShopBillPostageDetail> shopBillPostageDetails = list(postageDetailQueryWrapper);
        List<String> orderNumbers = shopBillPostageDetails.stream().map(SettleShopBillPostageDetail::getOrderNumber).collect(Collectors.toList());

        List<OrderExportExcelVO> newPostageOrder = orderExportExcelVOS.stream().filter(e -> {
            List<String> collect = orderNumbers.stream().filter(c -> c.equals(e.getOrderNumber())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                //历史没出过邮费
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());

        List<SettleShopBillPostageDetail> newSettleShopBillPostageDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newPostageOrder)) {
            //保存邮费
//            newSettleShopBillPostageDetailList = fillYflPostageDetail(newSettleBillPoolYflCustomers, newPostageOrder, settleShopBill);
        }

        return newSettleShopBillPostageDetailList;
    }

    /**
     * 福利邮费
     *
     * @param settleBillPoolYflCustomerList 出账池数据
     * @param settleShopBill                账单
     * @return
     */
    public List<SettleShopBillPostageDetail> fillYflPostageDetail(List<SettleBillPoolYflCustomer> settleBillPoolYflCustomerList, SettleShopBill settleShopBill) {
        List<SettleBillPoolYflCustomer> postageList = settleBillPoolYflCustomerList.stream().filter(e -> e.getOrderFreightPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(postageList)) {
            log.info("本次出账无邮费！");
            return new ArrayList<>();
        }


        Map<String, List<SettleBillPoolYflCustomer>> orderNumberMap = postageList.stream().collect(Collectors.groupingBy(SettleBillPoolYflCustomer::getOrderNumber));

        QueryWrapper<SettleShopBillPostageDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleShopBillPostageDetail::getOrderNumber, orderNumberMap.keySet())
                        .eq(SettleShopBillPostageDetail::getCustomerCode,settleShopBill.getCustomerCode());
        List<SettleShopBillPostageDetail> existPostageList = list(queryWrapper);

        Map<String, SettleShopBillPostageDetail> settleShopBillPostageDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existPostageList)) {
            settleShopBillPostageDetailMap = existPostageList.stream().collect(Collectors.toMap(SettleShopBillPostageDetail::getOrderNumber, Function.identity()));
        }

        List<SettleShopBillPostageDetail> shopBillPostageDetailList = new ArrayList<>();
        for (String orderNumber : orderNumberMap.keySet()) {
            List<SettleBillPoolYflCustomer> billPoolYflCustomerList = orderNumberMap.get(orderNumber);
            SettleShopBillPostageDetail postageDetail = new SettleShopBillPostageDetail();
            if (settleShopBillPostageDetailMap.isEmpty()) {
                postageDetail = DataAdapter.convert(billPoolYflCustomerList.get(0), SettleShopBillPostageDetail.class);
            } else {
                if (settleShopBillPostageDetailMap.get(orderNumber) != null) {
                    continue;
                } else {
                    postageDetail = DataAdapter.convert(billPoolYflCustomerList.get(0), SettleShopBillPostageDetail.class);
                }
            }
            postageDetail.setTaxRate(6);
            postageDetail.setPostage(billPoolYflCustomerList.get(0).getOrderFreightPrice());
            postageDetail.setBillSn(settleShopBill.getBillSn());
            postageDetail.setBillId(settleShopBill.getBillId());
            postageDetail.setFreightType(billPoolYflCustomerList.get(0).getFreightType());
            postageDetail.setInvoiceFlag(postageDetail.getFreightType()==0? ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode() : -1);
            postageDetail.setCustomerCode(billPoolYflCustomerList.get(0).getCompanyCode());
            postageDetail.setCustomerName(billPoolYflCustomerList.get(0).getCompanyName());
            shopBillPostageDetailList.add(postageDetail);
        }


        return shopBillPostageDetailList;
    }

    public PageResp<SettleShopBillPostageVo> querySettleShopBillPostagePage(PageReq pageReq, SettleShopBillPostagePageReqVo reqVo) {
        IPage<SettleShopBillPostageVo> postagePageReqVoIPage = this.getBaseMapper().querySettleShopBillPostagePage(DataAdapter.adapterPageReq(pageReq), reqVo);
        return DataAdapter.adapterPage(postagePageReqVoIPage, SettleShopBillPostageVo.class);
    }

    public List<SettleShopBillPostageExcelVo> getPostageExcel(SettleShopBillDetailPageReqVo vo) {
        List<SettleShopBillPostageExcelVo> postageExcelVoList = this.getBaseMapper().getPostageExcel(vo);
        return postageExcelVoList;
    }
}
