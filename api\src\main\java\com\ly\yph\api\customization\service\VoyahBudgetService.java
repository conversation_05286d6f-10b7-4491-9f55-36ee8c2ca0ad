package com.ly.yph.api.customization.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.customization.common.Constant;
import com.ly.yph.api.customization.common.PurchaseBudgetTypeEnum;
import com.ly.yph.api.customization.config.VoyahPurchaseConfig;
import com.ly.yph.api.customization.dto.*;
import com.ly.yph.api.customization.entity.SubOrderMultipleBudget;
import com.ly.yph.api.order.entity.ShopPurchaseOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.exception.OrderException;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.organization.controller.organization.vo.purchase.budget.BudgetSimpleRespVO;
import com.ly.yph.api.utils.XapiSignUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年08月23日
 */
@Service("voyahBudgetService")
@Slf4j
@RefreshScope
public class VoyahBudgetService {
    @Resource
    VoyahPurchaseConfig voyahPurchaseConfig;
    @Resource
    private SubOrderMultipleBudgetService multipleBudgetService;
    @Resource
    VoyahBudgetService self;

    @Resource
    private ShopPurchaseSubOrderService subOrderService;


    /**
     * 查询岚图预算系统预算
     * @param budgetType
     * @param budgetDate
     * @return
     */
    public List<BudgetSimpleRespVO> getVoyahBudgetInfo(String budgetType, String budgetDate,String userCode,String applyNumber,String budgetNumber,String costCenterNm) {
        VoyahBudgetQueryDto voyahBudgetQueryDto = new VoyahBudgetQueryDto();
        voyahBudgetQueryDto.setBudgetYear(budgetDate);
        voyahBudgetQueryDto.setUserCode(userCode);
        voyahBudgetQueryDto.setCostType(Constant.VOYAH1.equals(budgetType) ? "2" : "3");
        if (Constant.VOYAH4.equals(budgetType)) {
            voyahBudgetQueryDto.setCostType("1");
        }
        voyahBudgetQueryDto.setBudgetNo(budgetNumber);
        voyahBudgetQueryDto.setApplyNo(applyNumber);
        voyahBudgetQueryDto.setPageSize("50");
        voyahBudgetQueryDto.setCostCenterNm(costCenterNm);
        String queryBudgetJson = JSONUtil.toJsonStr(voyahBudgetQueryDto);
        log.info("【getVoyahBudgetInfo】获取岚图预算 参数：" + queryBudgetJson);
        String result = restCallCommon("COMMONINTER01",queryBudgetJson , null);
        log.info("【getVoyahBudgetInfo】获取岚图预算 返回：" + result);
        VoyahBudgetResult budgetResult  = JSONUtil.toBean(result, VoyahBudgetResult.class);
        List<VoyahBudgetResponseDto> voyahBudgetResponseDto;
        if ("1".equals(budgetResult.getResult())) {
            voyahBudgetResponseDto = JSONUtil.toList(budgetResult.getRows(), VoyahBudgetResponseDto.class);
        }else{
            throw new ParameterException("获取岚图采购系统预算信息异常，请联系系统管理员！");
        }
        List<BudgetSimpleRespVO> resultList = Lists.newArrayList();
        for (VoyahBudgetResponseDto item : voyahBudgetResponseDto) {
            BudgetSimpleRespVO budgetSimpleRespVo = new BudgetSimpleRespVO();
            budgetSimpleRespVo.setApplyNumber(item.getApplyNo());
            budgetSimpleRespVo.setBudgetYear(item.getBudgetYear());
            budgetSimpleRespVo.setBudgetNumber(item.getBudgetNo());
            budgetSimpleRespVo.setBudgetName(item.getBudgetDesc());
            if("GRBGYP".equalsIgnoreCase(item.getCostType())){
                budgetSimpleRespVo.setBudgetType("办公用品预算");
            }else{
                budgetSimpleRespVo.setBudgetType(Constant.VOYAH1.equals(budgetType) ? "费用类预算" : "投资类预算");
                if (Constant.VOYAH4.equals(budgetType)) {
                    budgetSimpleRespVo.setBudgetType("无需启动类");
                }
            }
            budgetSimpleRespVo.setUsableAmount(item.getUsableAmt());
            budgetSimpleRespVo.setBudgetProvideNm(item.getDeptNm());
            budgetSimpleRespVo.setTotalAmount(item.getBudgetAmt());
            budgetSimpleRespVo.setBusinessScope(item.getCompanyNo());
            budgetSimpleRespVo.setDeptNm(item.getDeptNm());
            budgetSimpleRespVo.setCostCenterCode(item.getCostCenterCode());
            resultList.add(budgetSimpleRespVo);
        }
        return resultList;
    }

    public Map<String, String> buildHeadersVhome() {
        String token = voyahPurchaseConfig.getVoyahVhomeUsername() + ":" + voyahPurchaseConfig.getVoyahVhomePassword();
        String sign = Base64.encode(token.getBytes(StandardCharsets.UTF_8));
        //请求Header参数定义
        Map<String, String> reqHeaders = new HashMap<>();
        //客户端ID 【必填】
        reqHeaders.put("Authorization", "Basic " + sign);
        log.info("【buildHeaders】生成的Basic " + JSONUtil.toJsonStr(reqHeaders));
        return reqHeaders;
    }

    public void sendVoyahDeductAmount(ShopPurchaseOrder purchaseOrder){
        String purchaseNumber = purchaseOrder.getPurchaseNumber();
        List<SubOrderMultipleBudget> budgetList = multipleBudgetService.getListByPurchaseNumber(purchaseNumber);
        List<VoyahBudgetOperationDto> operList = Lists.newArrayList();
        //如果这个表数据为空，表示商城的订单外部预算
        if (CollUtil.isEmpty(budgetList)) {
            VoyahBudgetOperationDto voyahBudgetOperationDto = new VoyahBudgetOperationDto();
            voyahBudgetOperationDto.setBillDesc(purchaseOrder.getRemark());
            voyahBudgetOperationDto.setBillDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
            voyahBudgetOperationDto.setApplyNo(purchaseOrder.getBudgetApplyCode());
            voyahBudgetOperationDto.setCheckInfo(PurchaseBudgetTypeEnum.FY.getBudgetType().equalsIgnoreCase(purchaseOrder.getBudgetType()) ? "B" : "C");
            if (purchaseOrder.getBudgetWithoutStart() == 1) {
                voyahBudgetOperationDto.setCheckInfo("A");
            }
            voyahBudgetOperationDto.setUserCode(purchaseOrder.getApplyEmpCode());
            voyahBudgetOperationDto.setUseAmt(Convert.toStr(purchaseOrder.getPurchaseGoodsPriceNaked()));
            voyahBudgetOperationDto.setBillNo(purchaseOrder.getPurchaseNumber());
            operList.add(voyahBudgetOperationDto);
        }else{
            //这里就表示SRM商品的预算
            for (SubOrderMultipleBudget subOrderMultipleBudget : budgetList) {
                VoyahBudgetOperationDto voyahBudgetOperationDto = new VoyahBudgetOperationDto();
                voyahBudgetOperationDto.setBillDesc(purchaseOrder.getRemark());
                voyahBudgetOperationDto.setBillDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
                voyahBudgetOperationDto.setApplyNo(subOrderMultipleBudget.getBudgetApplyCode());
                voyahBudgetOperationDto.setCheckInfo(PurchaseBudgetTypeEnum.FY.getBudgetType().equalsIgnoreCase(purchaseOrder.getBudgetType()) ? "B" : "C");
                if (purchaseOrder.getBudgetWithoutStart() == 1) {
                    voyahBudgetOperationDto.setCheckInfo("A");
                }
                voyahBudgetOperationDto.setUserCode(purchaseOrder.getApplyEmpCode());
                voyahBudgetOperationDto.setUseAmt(Convert.toStr(subOrderMultipleBudget.getUseAmount()));
                voyahBudgetOperationDto.setBillNo(subOrderMultipleBudget.getOrderNumber());
                operList.add(voyahBudgetOperationDto);
            }
        }
        self.operationVoyahBudget(1, operList);
    }


    public void sendVoyahReturnAmount(ShopPurchaseOrder purchaseOrder, BigDecimal price,List<String> orderNumberList) {

        List<SubOrderMultipleBudget> budgetList = multipleBudgetService.getListByPurchaseNumber(purchaseOrder.getPurchaseNumber());
        List<VoyahBudgetOperationDto> operList = Lists.newArrayList();
        if(CollUtil.isEmpty(budgetList)){
            VoyahBudgetOperationDto voyahBudgetOperationDto = new VoyahBudgetOperationDto();
            voyahBudgetOperationDto.setBillDesc(purchaseOrder.getRemark());
            voyahBudgetOperationDto.setBillDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
            voyahBudgetOperationDto.setApplyNo(purchaseOrder.getBudgetApplyCode());
            voyahBudgetOperationDto.setCheckInfo(PurchaseBudgetTypeEnum.FY.getBudgetType().equalsIgnoreCase(purchaseOrder.getBudgetType()) ? "B" : "C");
            if (purchaseOrder.getBudgetWithoutStart() == 1) {
                voyahBudgetOperationDto.setCheckInfo("A");
            }
            voyahBudgetOperationDto.setUserCode(purchaseOrder.getApplyEmpCode());
            voyahBudgetOperationDto.setUseAmt(Convert.toStr(price));
            voyahBudgetOperationDto.setBillNo(purchaseOrder.getPurchaseNumber());
            operList.add(voyahBudgetOperationDto);
        }else{
            budgetList.removeIf(item -> !orderNumberList.contains(item.getOrderNumber()));
            for (SubOrderMultipleBudget subOrderMultipleBudget : budgetList) {
                VoyahBudgetOperationDto voyahBudgetOperationDto = new VoyahBudgetOperationDto();
                voyahBudgetOperationDto.setBillDesc(purchaseOrder.getRemark());
                voyahBudgetOperationDto.setBillDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
                voyahBudgetOperationDto.setApplyNo(subOrderMultipleBudget.getBudgetApplyCode());
                voyahBudgetOperationDto.setCheckInfo(PurchaseBudgetTypeEnum.FY.getBudgetType().equalsIgnoreCase(purchaseOrder.getBudgetType()) ? "B" : "C");
                if (purchaseOrder.getBudgetWithoutStart() == 1) {
                    voyahBudgetOperationDto.setCheckInfo("A");
                }
                voyahBudgetOperationDto.setUserCode(purchaseOrder.getApplyEmpCode());
                voyahBudgetOperationDto.setUseAmt(Convert.toStr(subOrderMultipleBudget.getUseAmount()));
                voyahBudgetOperationDto.setBillNo(subOrderMultipleBudget.getOrderNumber());
                operList.add(voyahBudgetOperationDto);
            }
        }
        self.operationVoyahBudget(2, operList);
    }

    /**
     * 释放预算 & 占用预算
     * @param type 1:占用 2：释放
     * @param voyahBudgetOperationDto
     */
    public void operationVoyahBudget(Integer type, List<VoyahBudgetOperationDto> voyahBudgetOperationDto) {
        Map<String, List<VoyahBudgetOperationDto>> budgetMap = Maps.newHashMap();
        if (type == 1) {
            //占用
            budgetMap.put("occupyList", voyahBudgetOperationDto);
        } else if (type == 2) {
            //释放
            budgetMap.put("releaseList", voyahBudgetOperationDto);
        }
        String operationBudgetJson = JSONUtil.toJsonStr(budgetMap);

        log.info("【checkVoyahBudget】操作岚图预算 参数：" + operationBudgetJson);
        String result = restCallCommon("COMMONINTER02", operationBudgetJson, null);
        log.info("【checkVoyahBudget】操作岚图预算 返回：" + result);
        Map<String, Object> bean = JSONUtil.toBean(result, Map.class);
        if (!Convert.toBool(bean.get("resultSatus"))) {
            throw new OrderException(Convert.toStr(bean.get("resultDesc")));
        }
    }

    /**
     * 岚图预算系统发送预算实绩
     * @param type 订单状态 0取消 1提交  2完结
     * @param purchaseOrder
     */
    public void voyahBudgetPerformance(String type,ShopPurchaseOrder purchaseOrder) {
        //岚图预算系统天天报错，不调用了，停了去球
        List<SubOrderMultipleBudget> budgetList = multipleBudgetService.getListByPurchaseNumber(purchaseOrder.getPurchaseNumber());
        List<VoyahBudgetPerformanceDto> performanceList = Lists.newArrayList();
        if(CollUtil.isEmpty(budgetList)){
            VoyahBudgetPerformanceDto voyahBudgetPerformanceDto = new VoyahBudgetPerformanceDto();
            voyahBudgetPerformanceDto.setAmt(Convert.toStr(purchaseOrder.getPurchaseGoodsPrice()));
            voyahBudgetPerformanceDto.setAmtNoTax(Convert.toStr(purchaseOrder.getPurchaseGoodsPriceNaked()));
            voyahBudgetPerformanceDto.setApplyTime(DateUtil.format(purchaseOrder.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            voyahBudgetPerformanceDto.setOrderStatus(type);
            voyahBudgetPerformanceDto.setPurchaseNo(purchaseOrder.getPurchaseNumber());
            voyahBudgetPerformanceDto.setConsigneeEmpNo(purchaseOrder.getApplyEmpCode());
            voyahBudgetPerformanceDto.setBudgetNo(purchaseOrder.getBudgetCode());
            voyahBudgetPerformanceDto.setSuppCode(CompanyEnum.LYZL.getCompanyCode());
            voyahBudgetPerformanceDto.setSuppName(CompanyEnum.LYZL.getCompanyName());
            voyahBudgetPerformanceDto.setPoNo("");
            performanceList.add(voyahBudgetPerformanceDto);
        }else{
            for (SubOrderMultipleBudget subOrderMultipleBudget : budgetList) {
                VoyahBudgetPerformanceDto voyahBudgetPerformanceDto = new VoyahBudgetPerformanceDto();
                voyahBudgetPerformanceDto.setAmt("");
                voyahBudgetPerformanceDto.setAmtNoTax(Convert.toStr(subOrderMultipleBudget.getUseAmount()));
                voyahBudgetPerformanceDto.setApplyTime(DateUtil.format(purchaseOrder.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
                voyahBudgetPerformanceDto.setOrderStatus(type);
                voyahBudgetPerformanceDto.setPurchaseNo(purchaseOrder.getPurchaseNumber());
                voyahBudgetPerformanceDto.setConsigneeEmpNo(purchaseOrder.getApplyEmpCode());
                voyahBudgetPerformanceDto.setBudgetNo(subOrderMultipleBudget.getBudgetCode());
                ShopPurchaseSubOrder byOrderNumber = subOrderService.getByOrderNumber(subOrderMultipleBudget.getOrderNumber());
                voyahBudgetPerformanceDto.setSuppCode(byOrderNumber.getSupplierCode().replace(CompanyEnum.VOYAH.getCompanyCode() + "SUP_", ""));
                voyahBudgetPerformanceDto.setSuppName(byOrderNumber.getSupplierName());
                voyahBudgetPerformanceDto.setPoNo(subOrderMultipleBudget.getOrderNumber());
                performanceList.add(voyahBudgetPerformanceDto);
            }
        }
        String voyahBudgetOperationDtoJson = JSONUtil.toJsonStr(performanceList);
        log.info("【voyahBudgetPerformance】发送岚图实绩数据 参数：" + voyahBudgetOperationDtoJson);
        String result = restCallCommon("DFMALL03", voyahBudgetOperationDtoJson, null);
        log.info("【voyahBudgetPerformance】发送岚图实绩数据 返回：" + result);
    }

    public void voyahBudgetPerformance(ShopPurchaseOrder purchaseOrder,BigDecimal priceNaked) {
        List<SubOrderMultipleBudget> budgetList = multipleBudgetService.getListByPurchaseNumber(purchaseOrder.getPurchaseNumber());
        List<VoyahBudgetPerformanceDto> performanceList = Lists.newArrayList();
        if(CollUtil.isEmpty(budgetList)){
            VoyahBudgetPerformanceDto voyahBudgetPerformanceDto = new VoyahBudgetPerformanceDto();
            voyahBudgetPerformanceDto.setAmt(Convert.toStr(purchaseOrder.getPurchaseGoodsPrice()));
            voyahBudgetPerformanceDto.setAmtNoTax(Convert.toStr(priceNaked));
            voyahBudgetPerformanceDto.setApplyTime(DateUtil.format(purchaseOrder.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            voyahBudgetPerformanceDto.setOrderStatus("0");
            voyahBudgetPerformanceDto.setPurchaseNo(purchaseOrder.getPurchaseNumber());
            voyahBudgetPerformanceDto.setConsigneeEmpNo(purchaseOrder.getApplyEmpCode());
            voyahBudgetPerformanceDto.setBudgetNo(purchaseOrder.getBudgetCode());
            voyahBudgetPerformanceDto.setSuppCode(CompanyEnum.LYZL.getCompanyCode());
            voyahBudgetPerformanceDto.setSuppName(CompanyEnum.LYZL.getCompanyName());
            voyahBudgetPerformanceDto.setPoNo("");
            performanceList.add(voyahBudgetPerformanceDto);
        }else{
            for (SubOrderMultipleBudget subOrderMultipleBudget : budgetList) {
                VoyahBudgetPerformanceDto voyahBudgetPerformanceDto = new VoyahBudgetPerformanceDto();
                voyahBudgetPerformanceDto.setAmt("");
                voyahBudgetPerformanceDto.setAmtNoTax(Convert.toStr(subOrderMultipleBudget.getUseAmount()));
                voyahBudgetPerformanceDto.setApplyTime(DateUtil.format(purchaseOrder.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
                voyahBudgetPerformanceDto.setOrderStatus("0");
                voyahBudgetPerformanceDto.setPurchaseNo(purchaseOrder.getPurchaseNumber());
                voyahBudgetPerformanceDto.setConsigneeEmpNo(purchaseOrder.getApplyEmpCode());
                voyahBudgetPerformanceDto.setBudgetNo(subOrderMultipleBudget.getBudgetCode());
                ShopPurchaseSubOrder byOrderNumber = subOrderService.getByOrderNumber(subOrderMultipleBudget.getOrderNumber());
                voyahBudgetPerformanceDto.setSuppCode(byOrderNumber.getSupplierCode().replace(CompanyEnum.VOYAH.getCompanyCode() + "SUP_", ""));
                voyahBudgetPerformanceDto.setSuppName(byOrderNumber.getSupplierName());
                voyahBudgetPerformanceDto.setPoNo(subOrderMultipleBudget.getOrderNumber());
                performanceList.add(voyahBudgetPerformanceDto);
            }
        }
        String voyahBudgetOperationDtoJson = JSONUtil.toJsonStr(performanceList);
        log.info("【voyahBudgetPerformance】发送岚图实绩数据 参数：" + voyahBudgetOperationDtoJson);
        String result = restCallCommon("DFMALL03", voyahBudgetOperationDtoJson, null);
        log.info("【voyahBudgetPerformance】发送岚图实绩数据 返回：" + result);
    }

    public String restCallCommon(String apiLabel, String data, Map<String, String> heads) {
        //获取配置信息
        String systemCode = "DFMALL";
        StringBuffer url = new StringBuffer(voyahPurchaseConfig.getVoyahBudgetUrl());
        String sign = XapiSignUtils.getParametersAfterSign(voyahPurchaseConfig.getVoyahBudgetUsername(), voyahPurchaseConfig.getVoyahBudgetPassword(), data);
        url.append(systemCode).append("/").append(apiLabel).append("?");
        url.append(sign);
        log.info("【restCallCommon】请求岚图预算系统URL：" + url);
        HttpRequest body = HttpUtil.createPost(url.toString()).body(data);
        if (!Objects.isNull(heads)) {
            body.addHeaders(heads);
        }
    try (final HttpResponse executed = body.execute()) {
      String result = executed.body();
      return result;
    }
    }
    public VoyahBudgetResponseDto getVoyahBudgetByBudgetApplyNo(String userCode,String budgetDate,String applyNo,String budgetType){
        return getVoyahBudgetByBudgetApplyNo(userCode,budgetDate,applyNo,budgetType,0);
    }
    public VoyahBudgetResponseDto getVoyahBudgetByBudgetApplyNo(String userCode,String budgetDate,String applyNo,String budgetType,Integer budgetWithoutStart){
        VoyahBudgetQueryDto voyahBudgetQueryDto = new VoyahBudgetQueryDto();
        voyahBudgetQueryDto.setBudgetYear(budgetDate);
        voyahBudgetQueryDto.setUserCode(userCode);
        voyahBudgetQueryDto.setCostType(Constant.VOYAH1.equals(budgetType) ? "2" : "3");
        if(budgetWithoutStart == 1){
            voyahBudgetQueryDto.setCostType("1");
        }
        voyahBudgetQueryDto.setBudgetNo("");
        voyahBudgetQueryDto.setUseState("all");
        voyahBudgetQueryDto.setApplyNo(applyNo);
        String queryBudgetJson = JSONUtil.toJsonStr(voyahBudgetQueryDto);
        log.info("【getVoyahBudgetByBudgetApplyNo】获取岚图预算 参数：" + queryBudgetJson);
        String result = restCallCommon("COMMONINTER01",queryBudgetJson , null);
        log.info("【getVoyahBudgetByBudgetApplyNo】获取岚图预算 返回：" + result);
        VoyahBudgetResult budgetResult  = JSONUtil.toBean(result, VoyahBudgetResult.class);
        List<VoyahBudgetResponseDto> voyahBudgetResponseDto = Lists.newArrayList();
        if ("1".equals(budgetResult.getResult())) {
            voyahBudgetResponseDto = JSONUtil.toList(budgetResult.getRows(), VoyahBudgetResponseDto.class);
        }else{
            throw new ParameterException("获取岚图采购系统预算信息异常，请联系系统管理员！");
        }
        if(CollUtil.isEmpty(voyahBudgetResponseDto)){
            return null;
        }else{
            return voyahBudgetResponseDto.get(0);
        }
    }
}
