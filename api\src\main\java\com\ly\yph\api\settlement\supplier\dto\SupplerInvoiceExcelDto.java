package com.ly.yph.api.settlement.supplier.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SupplerInvoiceExcelDto {

    @ExcelProperty("对账年度")
    private Integer checkYear;

    @ExcelProperty("对账周期")
    private Integer checkMonth;

    @ExcelProperty("下单人")
    private String applyUserName;

    @ExcelProperty("采购单号")
    private String purchaseNumber;

    @ExcelProperty("订单号")
    private String orderNumber;

    @ExcelProperty("供应商订单号")
    private String supplierOrderNumber;

    @ExcelProperty("sku")
    private String goodsSku;

    @ExcelProperty("下单数量")
    private BigDecimal confirmNum;

    @ExcelProperty("开票数量")
    private BigDecimal invoiceNum;

    @ExcelProperty("未税单价")
    private BigDecimal goodsUnitPriceNaked;

    @ExcelProperty("含税单价")
    private BigDecimal goodsUnitPriceTax;

    @ExcelProperty("未税总价")
    private BigDecimal goodsTotalPriceNaked;

    @ExcelProperty("含税总价")
    private BigDecimal goodsTotalPriceTax;

    @ExcelProperty("税率")
    private Integer taxRate;

    @ExcelProperty("税收分类编码")
    private String taxCode;

    @ExcelProperty("规格")
    private String materialsCode;

    @ExcelProperty("型号")
    private String saleUnit;

}
