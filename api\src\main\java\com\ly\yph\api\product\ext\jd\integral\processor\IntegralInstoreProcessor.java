package com.ly.yph.api.product.ext.jd.integral.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.product.ext.common.BaseDataProcessor;
import com.ly.yph.api.product.ext.jd.integral.config.JDIntegralConfig;
import com.ly.yph.api.product.ext.jd.dto.reponse.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.ly.yph.api.product.ext.jd.integral.entity.BackupJdIntegralGoodsEntity;
import com.ly.yph.api.product.ext.jd.integral.service.BackupJdIntegralGoodsService;
import com.ly.yph.api.product.ext.zkh.entity.QueueMsgSupplierProductProcessInfo;
import com.ly.yph.api.product.ext.zkh.service.QueueMsgSupplierProductProcessInfoService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * step4 商品详情入库
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Service("IntegralInstoreProcessor")
@Slf4j
public class IntegralInstoreProcessor extends BaseDataProcessor<Long> {
    @Getter
    protected String processName = "JDIntegral入备份库";
    @Resource
    private JDIntegralConfig config;
    @Resource
    private QueueMsgSupplierProductProcessInfoService msgSrv;
    @Resource
    private BackupJdIntegralGoodsService backupSrv;
    @Resource
    private CodeGeneral cg;
    @Resource
    private SystemTenantService tenantSrv;


    @Override
    public List<Long> supplier(final int c) {
        String sup = config.getCode();
        return msgSrv.listBySup(c, sup).stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
    }

    @Override
    @DistributedLock(value = "jd_integral_in_process_locker", key = "#id", waitLock = false)
    public void processItem(final Long id) {
        // 获取所有租户 id
        QueueMsgSupplierProductProcessInfo msg = msgSrv.getById(id);
        if (msg == null || msg.getInStoreStatus() == 1) {
            return;
        }
        //商品只能维护进配置里的租户
        TenantUtils.execute(config.getTenantIds(), () -> {
            try {
                //记录商品进度
                var backup = backupSrv.selectBySku(Long.valueOf(msg.getSkuId()));
                BackupJdIntegralGoodsEntity backupJdIntegralGoodsEntity = doExec(msg, backup);
                if (backupJdIntegralGoodsEntity.getId() == null) {
                    backupSrv.save(backupJdIntegralGoodsEntity);
                } else {
                    backupSrv.getBaseMapper().updateByPrimaryKeySelective(backupJdIntegralGoodsEntity);
                }
                msgSrv.updateInStoreSuccess(id);
            } catch (Exception ex) {
                log.error("JD integral to backup error, skuId:{}", msg.getSkuId(), ex);
                msgSrv.updateInStoreError(id, ex.getMessage());
            }
        });
    }

    @Override
    @DistributedLock(value = "jd_integral_in_process_locker_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Long> ids) {
        val msg = msgSrv.getBaseMapper().selectSimple(ids);
        val failArray = new ArrayList<String>();
        val succArray = new ArrayList<String>();
        TenantUtils.execute(config.getTenantIds(), () -> {
            val entities = new ArrayList<BackupJdIntegralGoodsEntity>();
            var backups = backupSrv.getBaseMapper().selectBySkuIdIn(msg.stream().map(QueueMsgSupplierProductProcessInfo::getSkuId)                    .map(Long::valueOf).collect(Collectors.toList()));
            // skuId和实体的映射，生成map
            var backupMap = backups.stream().collect(Collectors.toMap(BackupJdIntegralGoodsEntity::getSkuId, item -> item));
            msg.forEach(item -> {
                try {
                    //记录商品进度
                    entities.add(doExec(item, backupMap.get(Long.valueOf(item.getSkuId()))));
                    if (!failArray.contains(item.getSkuId())){
                        succArray.add(item.getSkuId());
                    }
                } catch (Exception ex) {
                    log.error("JD Integral to backup error, skuId:{}", item.getSkuId(), ex);
                    failArray.add(item.getSkuId());
                    succArray.remove(item.getSkuId());
                }
            });

            var entities_list = entities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BackupJdIntegralGoodsEntity::getSkuId))), ArrayList::new));
            if (!entities_list.isEmpty())
                backupSrv.saveBatch(entities_list.stream().filter(item -> item.getId() == null).collect(Collectors.toList()));
            if (!entities_list.isEmpty())
                backupSrv.getBaseMapper().updateBatchByIdx(entities_list.stream().filter(item -> item.getId() != null).collect(Collectors.toList()));
        });
        //queue_msg_supplier_product_process_info只存租户1的
        if (!succArray.isEmpty())
            msgSrv.getBaseMapper().updateInstoreInfo(1, new ArrayList<>(succArray),config.getCode());
        if (!failArray.isEmpty())
            msgSrv.getBaseMapper().updateInstoreInfo(2, new ArrayList<>(failArray),config.getCode());
    }

    private BackupJdIntegralGoodsEntity doExec(QueueMsgSupplierProductProcessInfo item, BackupJdIntegralGoodsEntity backup) {
        var res = JSON.parseObject(item.getInfo(), GetSkuPoolInfoGoodsResp.class);
        var entity = backup == null ? new BackupJdIntegralGoodsEntity() : BeanUtil.copyProperties(backup, BackupJdIntegralGoodsEntity.class);

        BeanUtil.copyProperties(res, entity);
        entity.setUpdateTime(System.currentTimeMillis());
        if (backup == null) {
            // 生成good code
            entity.setGoodCode(cg.getProductCode(config.getCode()));
            entity.setSkuId(Long.valueOf(item.getSkuId()));
            entity.setCreateTime(System.currentTimeMillis());
        }

        // 分类信息
        if (CollectionUtil.isNotEmpty(res.getSkuPoolExtInfoList())) {
            entity.setContractSkuPoolExt(res.getSkuPoolExtInfoList().get(0).getPoolExtValue());
        }

        // 如果品类扩展字段不为空，这拼接上扩展的品类字段
        fillField(res, entity);
        return entity;
    }

    private void fillField(GetSkuPoolInfoGoodsResp r, BackupJdIntegralGoodsEntity e) {
        if (!StrUtil.isBlank(e.getContractSkuPoolExt())) {
            e.setCategory(e.getCategory() + ";" + e.getContractSkuPoolExt());
        }
        e.setImagePath(config.getImagePrefix() + "/" + e.getImagePath());
        e.setCategoryAttrListStr(JSONObject.toJSONString(r.getCategoryAttrList()));
        e.setParamGroupAttrlist(JSONObject.toJSONString(r.getParamGroupAttrList()));
        e.setBookExtInfo(JSONObject.toJSONString(r.getBookExtInfo()));
        e.setSoundExtInfo(JSONObject.toJSONString(r.getSoundExtInfo()));
        
        // 更新和添加统一设置成未同步状态，标志位全部重设
        e.setSynchronize((byte) 0);
        e.setValidateFlag((byte) 0);
        // 这里不要
        e.setPriceProcFlag((byte) 0);
        e.setShiftProcFlag((byte) 0);
        e.setStockProcFlag((byte) 0);
        e.setImageProcFlag((byte) 0);
        e.setCanSaleProcFlag((byte) 0);
        e.setCategoryProcFlag((byte) 0);
        e.setIsEnable(1);

        String introduct = e.getIntroduce();

        if (StrUtil.isBlank(introduct)) {
            if (StrUtil.isNotBlank(e.getBookExtInfo())) {
                try {
                    JSONObject bookExt = JSON.parseObject(e.getBookExtInfo());
                    String productFeatures = bookExt.getString("productFeatures");
                    introduct += productFeatures;
                } catch (Exception ex) {
                    log.error("jd_integral_json_ext_info_parse_error:{}", ex.getMessage());
                }
            }
        }

        e.setIntroduce(introduct);
        if (StrUtil.isNotBlank(e.getCategory())) {
            final var ca = e.getCategory().split(";");
            e.setCategory(ca[ca.length - 1]);
        }
    }
}
