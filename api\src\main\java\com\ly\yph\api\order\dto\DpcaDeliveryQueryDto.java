package com.ly.yph.api.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月19日
 */
@Data
public class DpcaDeliveryQueryDto implements Serializable {
    @ApiModelProperty(value = "物流单号", dataType = "String")
    String logisticsOrderSn;
    @ApiModelProperty(value = "确认收货时间", dataType = "String")
    Date acceptanceTime;
    @ApiModelProperty(value = "采购单号", dataType = "String")
    String purchaseNumber;
    @ApiModelProperty(value = "采购单行号", dataType = "Integer")
    Integer itemNo;
    @ApiModelProperty(value = "商品编码", dataType = "String")
    String goodsCode;
    @ApiModelProperty(value = "单位", dataType = "String")
    String unit;
    @ApiModelProperty(value = "采购数量", dataType = "Integer")
    Long goodsNum;
    @ApiModelProperty(value = "收货数量", dataType = "Integer")
    Integer deliveryNum;
    @ApiModelProperty(value = "货币单位", dataType = "String")
    String currencyUnit = "CNY";
    @ApiModelProperty(value = "未税单价", dataType = "String")
    BigDecimal price;
    @ApiModelProperty(value = "未税收货价格", dataType = "String")
    BigDecimal rowPrice;
    @ApiModelProperty(value = "含税单价", dataType = "String")
    BigDecimal priceTax;
    @ApiModelProperty(value = "含税收货价格", dataType = "String")
    BigDecimal rowPriceTax;
    @ApiModelProperty(value = "税率", dataType = "String")
    Integer taxRate;
    @ApiModelProperty(value = "购买人", dataType = "String")
    String buyerName;
    @ApiModelProperty(value = "是否收货完成", dataType = "String")
    String completed;
    String deliveryCode;
    String packageId;
}
