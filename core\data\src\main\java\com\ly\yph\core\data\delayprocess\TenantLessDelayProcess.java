package com.ly.yph.core.data.delayprocess;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.ly.yph.core.base.exception.ErrorCodeConstants;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.tenant.core.util.TenantUtils;
import com.yomahub.tlog.core.thread.TLogInheritableTask;

import lombok.var;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 延迟过程
 *
 * <AUTHOR>
 * @date 2022/12/12
 */
@Component
@Slf4j
@Data
public class TenantLessDelayProcess {
    private ReentrantLock lock = new ReentrantLock();
    private static final long SCHEDULER_PERIOD = 5;
    @Resource
    private ThreadPoolExecutor computeThread;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    @Resource
    private RedissonClient redissonClient;
    private final static Map<String, DelayProcessItem> _holder = new HashMap<>(16);

    public void addProcess(DelayProcessItem item) {
        _holder.put(item.getName(), item);
    }

    public void pushProcessInfo(String name, String infoJsonStr) {
        DelayProcessItem delayProcessItem = _holder.get(name);
        if (delayProcessItem == null) {
            throw HttpException.exception(ErrorCodeConstants.NOT_CONTAINER, name);
        }
        final RSet<String> oriList = this.redissonClient.getSet(delayProcessItem.getRedisName());
        var res = oriList.tryAdd(infoJsonStr);
        log.info("update" + name + ":" + infoJsonStr);
    }

    @Scheduled(fixedDelay = SCHEDULER_PERIOD, timeUnit = TimeUnit.SECONDS)
    public void schedulePeriodicRefresh() {
        commonIoExecutors.execute(new TLogInheritableTask() {
            @Override
            public void runTask() {
                _holder.forEach((hk, hv) -> _doproc(hk, hv));
            }
        });
    }

    @DistributedLock(value = "tenantless_delay_exec_lock", key = "#hk", leaseTime = 600, waitLock = false)
    private void _doproc(String hk, DelayProcessItem hv) {
        final Set<String> od = getList(hk);
        if (od.isEmpty()) {
            return;
        }
        // bean 需要继承自 IDelayProcessor
        IDelayProcessor processor = SpringUtil.getBean(hv.getBeanName());

        if (hv.getBatchSize() <= 0) {
            TenantUtils.executeIgnore(() -> {
                        try {
                            processor.processData(od);
                        } catch (Exception e) {
                            log.error("delay process data error:{}", ExceptionUtil.stacktraceToString(e));
                        }
                    }
            );
        } else {
            List<List<String>> split = CollectionUtil.split(od, hv.getBatchSize());
            TenantUtils.executeIgnore(() -> {
                for (List<String> strings : split) {
                    try {
                        processor.processData(strings);
                    } catch (Exception e) {
                        log.error("delay process data error:{}", ExceptionUtil.stacktraceToString(e));
                    }
                }
            });
        }
    }

    private Set<String> getList(String name) {
        DelayProcessItem delayProcessItem = _holder.get(name);
        if (delayProcessItem == null) {
            throw HttpException.exception(ErrorCodeConstants.NOT_CONTAINER, name);
        }

        final RSet<String> l = this.redissonClient.getSet(delayProcessItem.getRedisName());
        if (l == null || l.isEmpty()) {
            return new HashSet<>();
        }
        final Set<String> d = l.removeRandom(delayProcessItem.getDefaultCountOneTime());
        log.info("处理" + name + "数据，共:{}条", d.size());

        if (d.isEmpty()) {
            return new HashSet<>();
        }
        return d;
    }
}
