package com.ly.yph.api.pay.job;

import com.ly.yph.api.pay.service.order.PayOrderService;
import com.ly.yph.tenant.core.aop.TenantIgnore;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 轮询获取支付状态
 * '
 *
 * <AUTHOR>
 * @date 2022/09/22
 */
@Service
@Slf4j
public class GetPayStatusScheduled {
    private static final long SCHEDULER_PERIOD = 10 * 1000L;
    @Resource private PayOrderService payOrderService;

    @Scheduled(fixedDelay = SCHEDULER_PERIOD, initialDelay = SCHEDULER_PERIOD)
    @TenantIgnore
    public void loopStatus() {
        try {
            this.payOrderService.loopRemotePayStatus();
        } catch (final Exception ex) {
            final String moduleName = "payment-notify";
            final String functionName = "notify";
            final String errorMessage = ExceptionUtil.stacktraceToString(ex);
            log.error(StrUtil.format("[{}][{}]:errorMessage", moduleName, functionName, errorMessage));
        }
    }
}
