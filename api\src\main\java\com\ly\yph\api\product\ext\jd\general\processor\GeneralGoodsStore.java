package com.ly.yph.api.product.ext.jd.general.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopGoodsDetail;
import com.ly.yph.api.goods.entity.ShopGoodsPrice;
import com.ly.yph.api.goods.entity.ShopGoodsStock;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.product.ext.common.BaseDataStore;
import com.ly.yph.api.product.ext.jd.general.config.JDGeneralConfig;
import com.ly.yph.api.product.ext.jd.general.entity.BackupJdGeneralGoodsEntity;
import com.ly.yph.api.product.ext.jd.general.service.BackupJdGeneralGoodsService;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.ShopSupplierClass;
import com.ly.yph.api.supplier.service.ShopSupplierClassService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.oss.OSSAutoConfig;
import com.ly.yph.core.oss.OSSClient;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.collection.CollUtil.join;

/**
 * step5 写入 shop_goods 数据库
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Service("GeneralGoodsStore")
@Slf4j
public class GeneralGoodsStore extends BaseDataStore<Integer, BackupJdGeneralGoodsEntity> {
    @Getter
    protected String processName = "JDGeneral入标准库";
    @Resource
    private OSSAutoConfig ossConfig;
    @Resource
    private JDGeneralConfig config;
    @Resource
    private BackupJdGeneralGoodsService backSrv;
    @Resource
    private ShopGoodsService goodSrv;
    @Resource
    private YphSupplierBrandRelationService brandSrv;
    @Resource
    private ShopBrandService shopBrandSrv;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private ShopGoodsStockService stockSrv;
    @Resource
    private ShopGoodsPriceService priceSrv;
    @Resource
    private ShopGoodsDetailService detailSrv;
    @Resource
    private YphStandardClassService stClassSrv;
    @Resource
    private ShopSupplierClassService supplierClassSrv;
    @Resource
    private DfmallGoodsPoolShelvesDownService dfmallGoodsPoolShelvesDownService;
    @Resource
    private ThreadPoolExecutor commonIoExecutors;

    @Override
    public List<Integer> supplier(int count) {
        return TenantUtils.executeIgnore(() -> backSrv.selectAllForStoreProcess(count));
    }

    @Override
    @DistributedLock(value = "jd_general_store_process", key = "#id", waitLock = false)
    public void doStoreItem(Integer id) {
        var backup = TenantUtils.executeIgnore(() -> backSrv.getById(id));
        if (backup == null || backup.getSynchronize() != 0) {
            return;
        }

        TenantUtils.execute(backup.getTenantId(), () -> {
            // 清理单条场景下的重复数据，和 batch 逻辑保持一致：仅删除 is_enable 为 0 的冗余记录
            List<ShopGoods> sameGoods = goodSrv.getBaseMapper().queryGoodsByGoodsCodeIn(Collections.singleton(backup.getGoodCode()));
            if (CollectionUtil.isNotEmpty(sameGoods) && sameGoods.size() > 1) {
                List<Long> dupIds = sameGoods.stream()
                        .filter(g -> "0".equals(g.getIsEnable()))
                        .map(ShopGoods::getGoodsId)
                        .collect(Collectors.toList());
                if (!dupIds.isEmpty()) {
                    goodSrv.getBaseMapper().deleteByGoodsIdIn(dupIds);
                }
            }
            
            var eEnt = goodSrv.getGoodsForStore(backup.getGoodCode());
            doProcessItem(eEnt, backup);
        });

        TenantUtils.executeIgnore(() -> {
            this.saveDetailInfo(CollectionUtil.toList(backup));
            this.saveStockInfo(CollectionUtil.toList(backup));
            this.savePriceInfo(CollectionUtil.toList(backup));

            if (!needCreate.isEmpty()) {
                this.goodSrv.saveBatch(this.needCreate.values());
            }
            if (!needUpdate.isEmpty()) {
                this.goodSrv.getBaseMapper().updateGoodsBatch(this.needUpdate.values());
            }
            if (!needApprove.isEmpty()) {
                this.goodSrv.supplierSubmitGoods(this.needApprove);
            }

            if (!needUpdateBack.isEmpty()) this.backSrv.getBaseMapper().updateSyncInfo(this.needUpdateBack.values());
            if (!needDeleteShelves.isEmpty())
                this.dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolIdBatch(null, needDeleteShelves);
            if (!needSetShelvesInfo.isEmpty()) this.setShelvesInfo(this.needSetShelvesInfo);
        });
    }

    @Override
    @DistributedLock(value = "jd_general_store_process_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Integer> ids) {
        List<BackupJdGeneralGoodsEntity> uEnts = TenantUtils.executeIgnore(() -> this.backSrv.getBaseMapper().selectAllByIdIn(ids));
        // 生成一个goodscode到对象的map
        Map<String, BackupJdGeneralGoodsEntity> goodsMap = uEnts.stream().collect(Collectors.toMap(BackupJdGeneralGoodsEntity::getGoodCode, Function.identity()));

        List<ShopGoods> shopGoods = TenantUtils.executeIgnore(() -> this.goodSrv.getBaseMapper().queryGoodsByGoodsCodeIn(goodsMap.keySet()));

        // 从shopGoods列表中筛选重复的getGoodsCode，且is_enable为0的项
        Map<String, List<ShopGoods>> groupedByGoodsCode = shopGoods.stream()
                .collect(Collectors.groupingBy(ShopGoods::getGoodsCode));

        List<ShopGoods> duplicatesWithIsEnableZero = groupedByGoodsCode.values().stream()
                .filter(list -> list.size() > 1)
                .flatMap(List::stream)
                .filter(shopGood -> shopGood.getIsEnable().equals("0"))
                .collect(Collectors.toList());
        shopGoods.removeAll(duplicatesWithIsEnableZero);

        if(!CollectionUtil.isEmpty(duplicatesWithIsEnableZero)){
            goodSrv.getBaseMapper().deleteByGoodsIdIn(duplicatesWithIsEnableZero.stream().map(ShopGoods::getGoodsId).collect(Collectors.toList()));
        }

        // 生成一个goodscode到对象的map
        Map<String, ShopGoods> shopGoodsMap = shopGoods.stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, Function.identity()));

        CountDownLatch latch = new CountDownLatch(uEnts.size());
        uEnts.parallelStream().forEach(item -> {
            try {
                TenantUtils.execute(item.getTenantId(), () -> {
                    ShopGoods eEnt = shopGoodsMap.get(item.getGoodCode());
                    doProcessItem(eEnt, item);
                });
            } catch (Exception ex) {
                log.error("JdGeneralGoodsSaveError:{}", ExceptionUtil.stacktraceToString(ex, 5000));
            } finally {
                latch.countDown();
            }
        });
        try {
            latch.await(); // 等待所有任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // 处理中断异常
        }
        TenantUtils.executeIgnore(() -> {
            if (!uEnts.isEmpty()) this.saveDetailInfo(uEnts);
            if (!uEnts.isEmpty()) this.saveStockInfo(uEnts);
            if (!uEnts.isEmpty()) this.savePriceInfo(uEnts);
            if (!needCreate.isEmpty()) this.goodSrv.saveBatch(this.needCreate.values());
            if (!needUpdate.isEmpty()) this.goodSrv.getBaseMapper().updateGoodsBatch(this.needUpdate.values());
            if (!needApprove.isEmpty()) this.goodSrv.supplierSubmitGoods(this.needApprove);
            if (!needUpdateBack.isEmpty()) this.backSrv.getBaseMapper().updateSyncInfo(this.needUpdateBack.values());
        });

        commonIoExecutors.execute(() -> {
            TenantUtils.executeIgnore(() -> {
                if (!needSetShelvesInfo.isEmpty()) this.setShelvesInfo(this.needSetShelvesInfo);
                if (!needDeleteShelves.isEmpty())
                    this.dfmallGoodsPoolShelvesDownService.deleteByGoodsIdOrGoodsPoolIdBatch(null, needDeleteShelves);
            });
        });
    }

    private void doProcessItem(ShopGoods eEnt, BackupJdGeneralGoodsEntity backup) {
        try {
            var entity = new ShopGoods();
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, entity);
            }

            setIntroduce(backup, entity, eEnt);
            fillFiled(backup, entity);
            entity.setTenantId(backup.getTenantId());
            // 存几个表的数据
            if (eEnt != null) {
                this.needUpdate.put(entity.getGoodsCode(), entity);
                this.needDeleteShelves.add(entity.getGoodsId());
            } else {
                this.needCreate.put(entity.getGoodsCode(), entity);
            }
            // 判断是否需要审核
            if (entity.getAuditState() == 1) {
                this.needSetShelvesInfo.add(backup);
            } else {
                this.needApprove.add(entity);
            }
            backup.setSynchronize((byte) 1);
        } catch (Exception ex) {
            log.error("General_process_save_store_error：tenant-id:{},id:{} | stack:{}", TenantContextHolder.getTenantId(), backup.getId(), ExceptionUtil.stacktraceToString(ex));
            backup.setSynchronize((byte) 2);
        }
        needUpdateBack.put(backup.getGoodCode(), backup);
    }


    private void setShelvesInfo(List<BackupJdGeneralGoodsEntity> uEnts) {
        if (!uEnts.isEmpty())
            goodSrv.shopGoodsCodeUp(uEnts.stream().filter(item -> item.getSkuState() == 1).map(BackupJdGeneralGoodsEntity::getGoodCode).collect(Collectors.toList()), null);
        if (!uEnts.isEmpty())
            goodSrv.shopGoodsDown(uEnts.stream().filter(item -> item.getSkuState() == 0).map(BackupJdGeneralGoodsEntity::getGoodCode).collect(Collectors.toList()), "供应商推送下架");
    }

    private void savePriceInfo(Collection<BackupJdGeneralGoodsEntity> uEnts) {
        List<ShopGoodsPrice> needUpdate = new ArrayList<>(16);
        List<ShopGoodsPrice> needCreate = new ArrayList<>(16);
        val eEnts = priceSrv.getPriceForTrans(uEnts.stream().map(BackupJdGeneralGoodsEntity::getGoodCode).collect(Collectors.toList()));

        uEnts.forEach(item -> {
            var eEnt = eEnts.get(item.getGoodCode());
            var sEnt = new ShopGoodsPrice();
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }

            sEnt.setIsEnable("1");
            sEnt.setGoodsCode(item.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(item.getSkuId()));
            sEnt.setGoodsOriginalPrice(item.getJdPrice());
            sEnt.setGoodsOriginalNakedPrice(item.getJdPriceNaked());
            sEnt.setGoodsPactPrice(item.getGivenPrice());
            sEnt.setGoodsPactNakedPrice(item.getGivenPriceNaked());
            sEnt.setUpdateTime(new Date());
            sEnt.setTenantId(item.getTenantId());

            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.priceSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.priceSrv.getBaseMapper().updateGoodsPriceBatch(needUpdate);
    }

    private void saveStockInfo(Collection<BackupJdGeneralGoodsEntity> uEnts) {
        List<ShopGoodsStock> needUpdate = new ArrayList<>(16);
        List<ShopGoodsStock> needCreate = new ArrayList<>(16);
        val eEnts = stockSrv.getStockForTrans(uEnts.stream().map(BackupJdGeneralGoodsEntity::getGoodCode).collect(Collectors.toList()));
        uEnts.forEach(uEnt -> {
            var sEnt = new ShopGoodsStock();
            var eEnt = eEnts.get(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSkuId()));
            sEnt.setStockAvailable(0);
            sEnt.setStockAlert(0);
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setIsEnable("1");
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.stockSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.stockSrv.getBaseMapper().updateStockInfoBatch(needUpdate);
    }

    private void saveDetailInfo(Collection<BackupJdGeneralGoodsEntity> uEnts) {
        List<ShopGoodsDetail> needUpdate = new ArrayList<>(16);
        List<ShopGoodsDetail> needCreate = new ArrayList<>(16);
        val eEnts = detailSrv.getDetailForTrans(uEnts.stream().map(BackupJdGeneralGoodsEntity::getGoodCode).collect(Collectors.toList()));
        uEnts.forEach(uEnt -> {
            var sEnt = new ShopGoodsDetail();
            var eEnt = eEnts.get(uEnt.getGoodCode());
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            }
            sEnt.setGoodsCode(uEnt.getGoodCode());
            sEnt.setGoodsSku(String.valueOf(uEnt.getSkuId()));
            sEnt.setIsEnable("1");
            sEnt.setGoodsMoq(0);
            sEnt.setGoodsClick(0);
            sEnt.setTenantId(uEnt.getTenantId());
            sEnt.setCommentNum(0);
            sEnt.setGoodsCollect(0);
            sEnt.setGoodsMoq(uEnt.getLowestBuy());
            // 处理规格
            String spec = "";
            var specJson = new JSONObject();
            var pa = new JSONArray();
            if (!StrUtil.isBlank(uEnt.getParamGroupAttrlist())) {
                pa = JSONObject.parseArray(uEnt.getParamGroupAttrlist());
            }

            if (pa != null) {
                for (int i = 0; i < pa.size(); i++) {
                    var g = new Gson().fromJson(pa.getString(i), JSONObject.class);
                    var l = g.getJSONArray("paramAttributeList");
                    for (int k = 0; k < l.size(); k++) {
                        var json = l.getJSONObject(k);
                        var s = JSONArray.parseArray(json.getJSONArray("paramAttrValList").toString(), String.class);
                        var a = join(s, ",");
                        spec = spec + json.getString("paramAttrName") + " " + a + " ";
                        specJson.put(json.getString("paramAttrName"), a);
                    }
                }
                sEnt.setGoodsSpec(spec);
                sEnt.setGoodsSpecArray(JSON.toJSONString(specJson));
            }
            sEnt.setGoodsImage(uEnt.getImagePath());
            sEnt.setGoodsImageMore(uEnt.getImagePathMore());
            sEnt.setUpdateTime(new Date());
            if (eEnt != null) {
                needUpdate.add(sEnt);
            } else {
                sEnt.setCreateTime(new Date());
                needCreate.add(sEnt);
            }
        });
        if (!needCreate.isEmpty()) this.detailSrv.saveBatch(needCreate);
        if (!needUpdate.isEmpty()) this.detailSrv.getBaseMapper().updateByPrimaryKeySelectiveBatch(needUpdate);
    }

    private void fillFiled(BackupJdGeneralGoodsEntity b, ShopGoods e) {
        String supplierName = "";
        if (CollectionUtil.isNotEmpty(config.getConfigList())) {
            Map<String, JDGeneralConfig.GeneralConfig> configMap = config.getConfigList().stream().collect(Collectors.toMap(JDGeneralConfig.GeneralConfig::getCode, Function.identity()));
            JDGeneralConfig.GeneralConfig generalConfig = configMap.get(b.getSupplierCode());
            if (generalConfig != null) {
                supplierName = generalConfig.getName();
            }
        }
        e.setGoodsName(b.getSkuName());
        e.setGoodsCode(b.getGoodCode());
        e.setGoodsSku(String.valueOf(b.getSkuId()));
        e.setBackupGoodId(b.getId());
        e.setSupplierCode(b.getSupplierCode());
        e.setSupplierName(supplierName);
        ShopSupplier supplierEnt = shopSupplierService.selectByCode(b.getSupplierCode());
        e.setOrganizationId(supplierEnt.getOrganizationId());
        e.setSupplierType(supplierEnt.getSupplierType());
        e.setGoodsDesc(b.getSkuName() + " 销售单位：" + b.getSaleUnit());
        e.setGoodsSubtitle(b.getSkuName() + " 销售单位：" + b.getSaleUnit());
        e.setUpdateTime(new Date());
        setCategory(b, e);
        setBrand(b, e);
        e.setSaleUnit(b.getSaleUnit());
        e.setProductionPlace(b.getProductArea());
        e.setSpecGoodsWareQd(b.getWareInfo());
        e.setTaxCode(b.getTaxCode());
        e.setTaxRate(b.getTaxRatePercentage().intValue());
        // 这里不知道多少天
        e.setDeliveryTime(7);
        e.setMaterialsCode(b.getSeoModel());
        e.setManufacturerMaterialNo(b.getSeoModel());
        e.setGoodsKeywords("");
        e.setIsEnable("1");
        // 供应商档案配置不审批或者曾经已经审批通过的,无需审批,其他都需要审批
        e.setAuditState(supplierEnt.getIsAudited() == 0 || (e.getAuditState() != null && e.getAuditState() == 1) ? 1 : 0);
        e.setGoodsExplain("");
        e.setGoodsFeatures("");
        e.setHeedEvent("");
        e.setIsSpecial(0);
        // 先让商品不可见
        e.setSpecialEvent("");
        e.setGoodsMobileBoydUrl("");
    }

    private void setBrand(BackupJdGeneralGoodsEntity uEnt, ShopGoods sEnt) {
        // 能走到这里一定是brand 已经ok 的
        val t = brandSrv.selectOneBySupplierAndBrand(uEnt.getSupplierCode(), uEnt.getBrandName());
        if (t != null) {
            if (t.getStandardBrandId() == null) {
                sEnt.setBrandName(uEnt.getBrandName());
                sEnt.setBrandId("0");
            } else {
                sEnt.setBrandId(String.valueOf(t.getStandardBrandId()));
                val k = shopBrandSrv.getById(t.getStandardBrandId());
                sEnt.setBrandName(k.getBrandName());
            }
        } else {
            Long supplierId = shopSupplierService.selectByCode(uEnt.getSupplierCode()).getSupplierId();
            //保存到品牌关系映射表
            brandSrv.saveNewBrand(uEnt.getBrandName(), supplierId);
            sEnt.setBrandName(uEnt.getBrandName());
            sEnt.setBrandId("0");
        }
    }

    private void setCategory(BackupJdGeneralGoodsEntity uEnt, ShopGoods sEnt) {
        sEnt.setSupplierClass(uEnt.getCategory());
        sEnt.setSupplierClassName(uEnt.getCategoryName());

        val sss = stClassSrv.selectBySupplierClassCode(uEnt.getCategory(),uEnt.getSupplierCode());
        val c3 = stClassSrv.queryThreeTierClassByCode(sss.getClassCode());
        QueryWrapper<ShopSupplierClass> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopSupplierClass::getClassCode, uEnt.getCategory()).eq(ShopSupplierClass::getSupplierCode, uEnt.getSupplierCode());
        ShopSupplierClass one = supplierClassSrv.getOne(queryWrapper);
        sEnt.setSupplierClassId(one.getSupplierClassId().toString());
        sEnt.setFirstLevelGcid(String.valueOf(c3.getClassIdLv1()));
        sEnt.setFirstClassName(String.valueOf(c3.getClassNameLv1()));
        sEnt.setFirstClass(String.valueOf(c3.getClassCodeLv1()));
        sEnt.setSecondLevelGcid(String.valueOf(c3.getClassIdLv2()));
        sEnt.setSecondClassName(String.valueOf(c3.getClassNameLv2()));
        sEnt.setSecondClass(String.valueOf(c3.getClassCodeLv2()));
        sEnt.setThirdLevelGcid(String.valueOf(c3.getClassIdLv3()));
        sEnt.setThirdClassName(String.valueOf(c3.getClassNameLv3()));
        sEnt.setThirdClass(String.valueOf(c3.getClassCodeLv3()));
        val s = stClassSrv.selectNameByCode(sss.getClassCode());
        sEnt.setStandCategoryName(s == null ? "未找到标准分类" : s);
    }

    private void setIntroduce(BackupJdGeneralGoodsEntity uEnt, ShopGoods sEnt, ShopGoods eEnt) {
        if (CollectionUtil.isEmpty(config.getConfigList())) {
            log.info("未配置京东通用信息.[{}]", config);
            return;
        }
        Map<String, JDGeneralConfig.GeneralConfig> configMap = config.getConfigList().stream().collect(Collectors.toMap(JDGeneralConfig.GeneralConfig::getCode, Function.identity()));
        JDGeneralConfig.GeneralConfig generalConfig = configMap.get(uEnt.getSupplierCode());
        if (generalConfig == null) {
            log.info("未配置全京东通用信息.[{}]", uEnt.getSupplierCode());
            return;
        }


        if (eEnt != null) {
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                // 先删除原来的数据
                ossClient.delete(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", generalConfig.getOssPrefix(), sEnt.getGoodsCode()));

                // 再次添加数据
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", generalConfig.getOssPrefix(), sEnt.getGoodsCode()), uEnt.getIntroduce());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(generalConfig.getDefaultShopGoodBodyUrl());
            }
        } else {
            // 内容设置到oss,商品下架注意清除
            try (OSSClient ossClient = new OSSClient(ossConfig.getAccessKey(), ossConfig.getSecretKey(), ossConfig.getEndpoint())) {
                String resultUrl = ossClient.putString(ossConfig.getTextBuketName(), StrUtil.format("{}_product_{}", generalConfig.getOssPrefix(), uEnt.getGoodCode()), uEnt.getIntroduce());
                sEnt.setGoodsBoydUrl(resultUrl);
            } catch (IOException e) {
                sEnt.setGoodsBoydUrl(generalConfig.getDefaultShopGoodBodyUrl());
            }
        }
    }

}
