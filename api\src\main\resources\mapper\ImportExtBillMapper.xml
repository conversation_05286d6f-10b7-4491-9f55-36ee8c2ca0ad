<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.settlement.common.mapper.ImportExtBillMapper">
    <resultMap type="com.ly.yph.api.settlement.common.entity.ImportExtBill" id="ImportExtBillMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="purchaseNumber" column="purchase_number" jdbcType="VARCHAR"/>
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <result property="goodsCode" column="goods_code" jdbcType="VARCHAR"/>
        <result property="itemNo" column="item_no" jdbcType="INTEGER"/>
        <result property="goodsSku" column="goods_sku" jdbcType="VARCHAR"/>
        <result property="confirmNum" column="confirm_num" jdbcType="INTEGER"/>
        <result property="deliveryNum" column="delivery_num" jdbcType="INTEGER"/>
        <result property="goodsUnitPriceTax" column="goods_unit_price_tax" jdbcType="NUMERIC"/>
        <result property="goodsTotalPriceTax" column="goods_total_price_tax" jdbcType="NUMERIC"/>
        <result property="goodsUnitPriceNaked" column="goods_unit_price_naked" jdbcType="NUMERIC"/>
        <result property="goodsTotalPriceNaked" column="goods_total_price_naked" jdbcType="NUMERIC"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
    </resultMap>
    <!--查询单个-->
    <select id="queryById" resultMap="ImportExtBillMap">
        select id,
               file_name,
               purchase_number,
               order_number,
               goods_code,
               item_no,
               goods_sku,
               confirm_num,
               delivery_num,
               goods_unit_price_tax,
               goods_total_price_tax,
               goods_unit_price_naked,
               goods_total_price_naked,
               remark,
               create_time,
               update_time,
               creator
        from import_ext_bill
        where id = #{id}
    </select>
    <select id="queryDfpvExtBill" resultType="com.ly.yph.api.settlement.common.dto.bill.ExternalBillDto">
        select a.sap_delivery_id,
               a.purchase_number,
               a.item_no,
               a.mara_matnr,
               a.rceipt_num                             delivery_num,
               a.receipt as  check_date,
               a.delivery_time as check_time,
               (select aa.goods_sku
                from shop_purchase_sub_order_detail aa
                where aa.order_number in
                      (select order_number
                       from shop_purchase_sub_order b
                       where b.purchase_number = a.purchase_number)
                  and aa.row_serial_number = a.item_no) goods_sku,
               (select aa.goods_code
                from shop_purchase_sub_order_detail aa
                where aa.order_number in
                      (select order_number
                       from shop_purchase_sub_order b
                       where b.purchase_number = a.purchase_number)
                  and aa.row_serial_number = a.item_no) goods_code,
               (select aa.confirm_num
                from shop_purchase_sub_order_detail aa
                where aa.order_number in
                      (select order_number
                       from shop_purchase_sub_order b
                       where b.purchase_number = a.purchase_number)
                  and aa.row_serial_number = a.item_no) confirm_num,
               (select aa.order_number
                from shop_purchase_sub_order_detail aa
                where aa.order_number in
                      (select order_number
                       from shop_purchase_sub_order b
                       where b.purchase_number = a.purchase_number)
                  and aa.row_serial_number = a.item_no) order_number
        from dfpv_sap_delivery a
        where
            check_bill = 0
          and length(purchase_number) &lt; 20
          and a.company_code = #{companyCode}
        union all
        select sss.sap_delivery_id,
               www.purchase_number,
               sss.item_no,
               sss.mara_matnr,
               sss.rceipt_num   delivery_num,
               asw.goods_sku    goods_sku,
               asw.goods_code   goods_code,
               asw.confirm_num  confirm_num,
               asw.order_number order_number,
               sss.receipt as  checkDate,
               sss.delivery_time as checkTime
        from dfpv_sap_delivery sss
                 left join shop_purchase_sub_order www on sss.purchase_number = www.order_id
                 left join shop_purchase_sub_order_detail asw
                           on sss.item_no = asw.row_serial_number and asw.order_number = www.order_number
        where
            check_bill = 0
          and length(sss.purchase_number) &gt; 20 and sss.company_code = #{companyCode};
    </select>
    <select id="queryDhecExtBill" resultType="com.ly.yph.api.settlement.common.dto.bill.ExternalBillDto">
        select id,purchase_number,order_number,goods_code,goods_sku,check_num as delivery_num,check_time,ext_order_sn as other_relation_number from external_check where check_bill = 0 and company_code =  #{companyCode};
    </select>
    <select id="getImportExtBillVoList" resultType="com.ly.yph.api.settlement.common.vo.bill.ImportExtBillVo">
        select
        a.item_no,
        a.id,
        a.remark,
        a.file_name,
        a.purchase_number,
        a.order_number,
        a.goods_desc,
        a.goods_code,
        a.goods_sku,
        a.confirm_num,
        a.delivery_num,
        a.goods_unit_price_tax,
        a.goods_total_price_tax,
        a.goods_unit_price_naked,
        a.goods_total_price_naked,
        a.check_time,
        a.tax_rate,
        a.company_code,
        a.company_name,
        a.invoice_apply_number,
        a.goods_check_status,
        a.bill_check_status,
        a.invoice_status,
        a.create_time,
        a.other_relation_number,b.apply_user_name
        from import_ext_bill a left join shop_purchase_order b on a.purchase_number = b.purchase_number
        where a.is_enable = 1
        <if test="query.companyCode != null and query.companyCode !=''">
            and a.company_code =#{query.companyCode}
        </if>
        <if test="query.purchaseNumber != null and query.purchaseNumber !=''">
            and a.purchase_number =#{query.purchaseNumber}
        </if>
        <if test="query.orderNumber !=null and query.orderNumber !=''">
            and a.order_number =#{query.orderNumber}
        </if>
        <if test="query.goodsCode != null and query.goodsCode !=''">
            and a.goods_code =#{query.goodsCode}
        </if>
        <if test="query.goodsSku != null and query.goodsSku !=''">
            and a.goods_sku =#{query.goodsSku}
        </if>
        <if test="query.taxRate !=null">
            and a.tax_rate =#{query.taxRate}
        </if>
        <if test="query.goodsCheckStatus !=null">
            and a.goods_check_status =#{query.goodsCheckStatus}
        </if>
        <if test="query.billCheckStatus !=null">
            and a.bill_check_status =#{query.billCheckStatus}
        </if>
        <if test="query.invoiceStatus !=null">
            and a.invoice_status =#{query.invoiceStatus}
        </if>
        <if test="query.invoiceApplyNumber !=null and query.invoiceApplyNumber !=''">
            and a.invoice_apply_number =#{query.invoiceApplyNumber}
        </if>
        <if test="query.beginDate != null and  query.beginDate != '' ">
            and a.check_time &gt;= #{query.beginDate}
        </if>
        <if test="query.endDate != null and query.endDate != '' ">
            and a.check_time &lt;= #{query.endDate}
        </if>
        <if test="query.applyUserName != null and query.applyUserName != '' ">
            and b.apply_user_name =#{query.applyUserName}
        </if>
    </select>

    <select id="getOutCheckDfcvSapDelivery" resultType="com.ly.yph.api.order.vo.check.OutCheckSapDetailVo">
        select
        purchase_number,
        item_no,
        delivery_num as menge
        from
            import_ext_bill
        where is_enable = 1 and purchase_number in
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getOrderDetailByPurchaseNumberList"
            resultType="com.ly.yph.api.settlement.common.entity.ImportExtBill">
        select spo.purchase_number,
               spo.other_relation_number,
               spso.order_number,
               spsod.goods_sku,
               spsod.goods_code,
               spsod.row_serial_number as item_no,
               spsod.confirm_num,
               spsod.goods_unit_price_naked,
               spsod.goods_unit_price_tax,
               spsod.tax_rate,
               spsod.goods_desc
        from shop_purchase_sub_order_detail spsod
                 left join shop_purchase_sub_order spso on spso.order_number = spsod.order_number
                 left join shop_purchase_order spo on spo.purchase_number = spso.purchase_number
        where spsod.is_enable = 1
          and spo.company_code = #{companyCode}
          and spo.purchase_number in
        <foreach collection="list" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>

