package com.ly.yph.api.settlement.supplier.factory;

import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailPageReqVo;
import com.ly.yph.api.settlement.supplier.dto.RemoveToNextMonthDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceByDetailIdDto;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface SupplierInvoiceStrategy {
    /**
     * 获取excel开票模板
     *
     * @param response
     */
    void exportInvoiceTemplate(HttpServletResponse response) throws IOException;

    /**
     * excel 开票
     * @param file
     * @param settleShopBill
     * @return
     * @throws IOException
     */
    String excelInvoice(MultipartFile file, SettleShopBill settleShopBill) throws IOException;

    /**
     * 整单开票
     * @param settleShopBill
     * @return
     */
    String allDetailInvoice(SettleShopBill settleShopBill);

    /**
     * 按勾选明细开票
     * @param supplierInvoiceByDetailIdDto
     * @param settleShopBill
     * @return
     */
    String supplierInvoiceByDetailIds(SupplierInvoiceByDetailIdDto supplierInvoiceByDetailIdDto, SettleShopBill settleShopBill);

    /**
     * 发票审批通过
     * @param supplierInvoiceBill
     */
    void supplierInvoicePass(SupplierInvoiceBill supplierInvoiceBill);

    /**
     * 发票导出
     * @param response
     * @param id
     * @throws IOException
     */
    void supplierInvoiceExport(HttpServletResponse response, Long id) throws IOException;

    /**
     * 发票删除
     * @param supplierInvoiceBill
     */
    void invoiceDel(SupplierInvoiceBill supplierInvoiceBill);

    /**
     * 发票驳回
     * @param supplierInvoiceBill
     * @param rejectReason
     */
    void supplierInvoiceReject(SupplierInvoiceBill supplierInvoiceBill, String rejectReason);

    /**
     * 明细（账单明细，邮费明细）移月
     * @param removeToNextMonthDto
     * @return
     */
    String removeToNextMonth(RemoveToNextMonthDto removeToNextMonthDto);

    /**
     * 导出明细（账单明细，邮费明细）
     * @param response
     * @param reqVo
     * @throws IOException
     */
    void exportBillDetail(HttpServletResponse response, SettleShopBillDetailPageReqVo reqVo) throws IOException;
}
