package com.ly.yph.api.orderlifecycle.factory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.orderlifecycle.dto.*;
import com.ly.yph.api.orderlifecycle.entity.OrderDetailPool;
import com.ly.yph.api.orderlifecycle.entity.PurchaseOrderInfoPool;
import com.ly.yph.api.orderlifecycle.enums.LifeCycleNodeStatusEnum;
import com.ly.yph.api.orderlifecycle.service.CommentService;
import com.ly.yph.api.orderlifecycle.service.OrderDetailPoolService;
import com.ly.yph.api.orderlifecycle.utils.EnhancedIPHashSnowflakeGenerator;
import com.ly.yph.api.orderlifecycle.utils.OrderPoolPurchaseTypeUtils;
import com.ly.yph.api.orderlifecycle.service.PurchaseOrderInfoPoolService;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoiceDelDto;
import com.ly.yph.core.base.CommonCodeGeneral;
import com.ly.yph.core.base.page.DataAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DfshopOrderLifeCycleStrategy implements OrderLifeCycleStrategy {

    @Resource
    private PurchaseOrderInfoPoolService purchaseOrderInfoPoolService;

    @Resource
    private OrderDetailPoolService orderDetailPoolService;

    @Resource
    private ShopPurchaseSubOrderDetailService shopPurchaseSubOrderDetailService;

    @Resource
    private CommonCodeGeneral commonCodeGeneral;

    @Resource
    private CommentService commentService;

    /**
     * 确认订单后保存采购单基础信息
     */
    @Transactional
    @Override
    public void savePurchaseOrderInfoForPreOrder(String purchaseNumber) {
        log.info("进入生命周期【预订单环节】，采购单号：{}", purchaseNumber);
        PurchaseOrderInfoDto purchaseOrderInfoDto = purchaseOrderInfoPoolService.getBaseMapper().getPurchaseOrderInfoDto(purchaseNumber);
        if(purchaseOrderInfoDto==null){
            log.info("未查询到采购单号的信息：{}",purchaseNumber);
            return;
        }
        PurchaseOrderInfoPool purchaseOrderInfoPool = DataAdapter.convert(purchaseOrderInfoDto, PurchaseOrderInfoPool.class);

        //获取id
        Long purchaseOrderInfoId = EnhancedIPHashSnowflakeGenerator.nextId();
        purchaseOrderInfoPool.setId(purchaseOrderInfoId);
        purchaseOrderInfoPool.setIsFix(0);

        List<OrderDetailPoolPreOrderDto> orderDetailPoolPreOrderList = purchaseOrderInfoPoolService.getBaseMapper().getOrderDetailPoolPreOrderList(purchaseNumber);
        if (CollectionUtil.isEmpty(orderDetailPoolPreOrderList)) {
            log.info("采购单：{}对应的明细全部是平台外结算的数据，暂不进入生命周期管控", purchaseNumber);
            return;
        }
        List<OrderDetailPool> orderDetailPoolList = DataAdapter.convertList(orderDetailPoolPreOrderList, OrderDetailPool.class);
        orderDetailPoolList.forEach(e -> {
            e.setPurchaseInfoId(purchaseOrderInfoId);
            e.setId(EnhancedIPHashSnowflakeGenerator.nextId());
            e.setOrderSalesChannel(purchaseOrderInfoPool.getOrderSalesChannel());
            OrderPoolPurchaseTypeUtils.fillPurchaseTypeInfo(e, purchaseOrderInfoPool, purchaseOrderInfoDto.getAddressType());
        });

        purchaseOrderInfoPoolService.save(purchaseOrderInfoPool);
        orderDetailPoolService.saveBatch(orderDetailPoolList, 500);
        log.info("生命周期更新完成-【预订单】,采购单号：{}",purchaseNumber);
    }


    /**
     * 审批更新采购单
     *
     * @param updatePurchaseOrderInfoForConfirmDto
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForApprove(UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto) {
        log.info("环节【确认订单】采购单:{}", updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber());
        fillInfo(updatePurchaseOrderInfoForConfirmDto);
        log.info("销售渠道：{}", updatePurchaseOrderInfoForConfirmDto.getOrderSalesChannel());
        if (CollectionUtil.isEmpty(updatePurchaseOrderInfoForConfirmDto.getUpdateOrderDetailDtoList())) {
            return;
        }

        QueryWrapper<PurchaseOrderInfoPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(PurchaseOrderInfoPool::getId,
                PurchaseOrderInfoPool::getPurchaseNumber,
                PurchaseOrderInfoPool::getOrderSalesChannel)
                .eq(PurchaseOrderInfoPool::getPurchaseNumber, updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber())
                .eq(PurchaseOrderInfoPool::getOrderSalesChannel, updatePurchaseOrderInfoForConfirmDto.getOrderSalesChannel());
        PurchaseOrderInfoPool purchaseOrderInfoPool = purchaseOrderInfoPoolService.getOne(queryWrapper);
        if (purchaseOrderInfoPool == null) {
            log.info("未查询到【采购单信息】,确认订单环节-采购单号：{}，渠道：{}", updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber(),
                    updatePurchaseOrderInfoForConfirmDto.getOrderSalesChannel());
        }

        List<UpdateOrderDetailDto> updateOrderDetailDtoList = updatePurchaseOrderInfoForConfirmDto.getUpdateOrderDetailDtoList();
        if (CollectionUtil.isEmpty(updateOrderDetailDtoList)) {
            log.info("未查询到【采购单明细】信息,确认订单环节-采购单号：{}，渠道：{}", updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber(),
                    updatePurchaseOrderInfoForConfirmDto.getOrderSalesChannel());
            return;
        }

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().select(OrderDetailPool::getId,
                OrderDetailPool::getOrderDetailId,
                OrderDetailPool::getOrderSalesChannel)
                .eq(OrderDetailPool::getOrderSalesChannel, updatePurchaseOrderInfoForConfirmDto.getOrderSalesChannel())
                .in(OrderDetailPool::getOrderDetailId, updateOrderDetailDtoList.stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList()));
        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(detailPoolQueryWrapper);
        if (CollectionUtil.isEmpty(orderDetailPoolList)) {
            log.info("系统内未查询到【采购单明细】信息,确认订单环节-采购单号：{}，渠道：{}，商城订单明细id:{}", updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber(),
                    updatePurchaseOrderInfoForConfirmDto.getOrderSalesChannel(),
                    updateOrderDetailDtoList.stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList()));
            return;
        }

        //更新采购单信息
        UpdateWrapper<PurchaseOrderInfoPool> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(PurchaseOrderInfoPool::getApprovalOpinion, updatePurchaseOrderInfoForConfirmDto.getApprovalOpinion())
                .set(PurchaseOrderInfoPool::getPurchaseState, updatePurchaseOrderInfoForConfirmDto.getPurchaseState())
                .set(PurchaseOrderInfoPool::getOtherRelationNumber, StringUtils.isBlank(updatePurchaseOrderInfoForConfirmDto.getOtherRelationNumber()) ? "" : updatePurchaseOrderInfoForConfirmDto.getOtherRelationNumber())
                .eq(PurchaseOrderInfoPool::getId, purchaseOrderInfoPool.getId());
        if (updatePurchaseOrderInfoForConfirmDto.getSapOrderType() == 0 || updatePurchaseOrderInfoForConfirmDto.getSapOrderType() == 4) {
            updateWrapper.lambda().set(PurchaseOrderInfoPool::getIsOutSettle, 0);
        } else {
            updateWrapper.lambda().set(PurchaseOrderInfoPool::getIsOutSettle, 1);
        }


        //更新商品明细
        List<OrderDetailPool> updateOrderDetailList = new ArrayList<>();
        Map<String, UpdateOrderDetailDto> orderDetailMap = updateOrderDetailDtoList.stream().collect(Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId, Function.identity()));
        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {
            UpdateOrderDetailDto updateOrderDetailDto = orderDetailMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateDetail = new OrderDetailPool();
            updateDetail.setId(orderDetailPool.getId());
            updateDetail.setOrderDetailState(updateOrderDetailDto.getOrderDetailState());
            updateDetail.setConfirmTime(updateOrderDetailDto.getConfirmTime());
            updateOrderDetailList.add(updateDetail);
        }

        //更新采购单信息
        purchaseOrderInfoPoolService.update(updateWrapper);
        //更新商品明细信息
        orderDetailPoolService.updateBatchById(updateOrderDetailList);
        log.info("环节【确认订单】生命周期更新完成，采购单:{}",updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber());
    }

    private void fillInfo(UpdatePurchaseOrderInfoForConfirmDto updatePurchaseOrderInfoForConfirmDto) {
        List<UpdateOrderDetailDto> updateOrderDetailDtoList = shopPurchaseSubOrderDetailService.getBaseMapper().getUpdateOrderDetailDtoByPurchaseNumber(updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber());
        if (CollectionUtil.isEmpty(updateOrderDetailDtoList)) {
            log.info("采购单：{}在确认订单环节，未查询到明细数据", updatePurchaseOrderInfoForConfirmDto.getPurchaseNumber());
            return;
        }
        updatePurchaseOrderInfoForConfirmDto.setUpdateOrderDetailDtoList(updateOrderDetailDtoList);
        updatePurchaseOrderInfoForConfirmDto.setPurchaseState(updateOrderDetailDtoList.get(0).getPurchaseState());
        updatePurchaseOrderInfoForConfirmDto.setApprovalOpinion(updateOrderDetailDtoList.get(0).getApprovalOpinion());
        updatePurchaseOrderInfoForConfirmDto.setSapOrderType(updateOrderDetailDtoList.get(0).getSapOrderType());
        updatePurchaseOrderInfoForConfirmDto.setOtherRelationNumber(updateOrderDetailDtoList.get(0).getOtherRelationNumber());
        updatePurchaseOrderInfoForConfirmDto.setOrderSalesChannel(updateOrderDetailDtoList.get(0).getOrderSalesChannel());

    }

    /**
     * 发货
     *
     * @param updateOrderDetailForDeliveryDto
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForDelivery(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        log.info("进入【发货】环节，订单号：{},渠道：{}", updateOrderDetailForDeliveryDto.getOrderNumber(),
                updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
        if (CollectionUtil.isEmpty(updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【发货】订单号：{},渠道：{}商品明细为空", updateOrderDetailForDeliveryDto.getOrderNumber(),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForDeliveryDto.getGetOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getDeliveryStatus,
                        OrderDetailPool::getDeliveryNum,
                        OrderDetailPool::getConfirmNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【发货】订单号：{},渠道：{}商品明细查询出来为空，商品明细id:{}", updateOrderDetailForDeliveryDto.getOrderNumber(),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        Map<String, UpdateOrderDetailDto> deliveryMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId, Function.identity()));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            UpdateOrderDetailDto updateOrderDetailDto = deliveryMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setDeliveryNum(orderDetailPool.getDeliveryNum().add(updateOrderDetailDto.getDeliveryNum()));
            updateOrderDetailPool.setOrderDetailState(updateOrderDetailDto.getOrderDetailState());
            updateOrderDetailPool.setSupplierOrderNumber(updateOrderDetailDto.getSupplierOrderNumber());
            //售后不影响发货数量 只影响收货数量
            BigDecimal subtract = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getDeliveryNum());

            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
            } else if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
            } else {
                updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    /**
     * 妥投
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForProperDelivery(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        log.info("进入妥投环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【妥投】订单号：{},渠道：{}商品明细为空", updateOrderDetailForDeliveryDto.getOrderNumber(),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForDeliveryDto.getGetOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getProperDeliveryNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【妥投】订单号：{},渠道：{}商品明细查询出来为空，商品明细id:{}", updateOrderDetailForDeliveryDto.getOrderNumber(),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel(),
                    orderDetailIds);
            return;
        }
        Map<String, UpdateOrderDetailDto> deliveryMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId, Function.identity()));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            UpdateOrderDetailDto updateOrderDetailDto = deliveryMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            //因为东风商城 包裹妥投 所以取得是发货数量进行累加
            updateOrderDetailPool.setProperDeliveryNum(orderDetailPool.getProperDeliveryNum().add(updateOrderDetailDto.getDeliveryNum()));
            updateOrderDetailPool.setOrderDetailState(updateOrderDetailDto.getOrderDetailState());
            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    /**
     * 收货
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForReceive(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        log.info("进入收货环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【收货】订单号：{},渠道：{}商品明细为空", updateOrderDetailForDeliveryDto.getOrderNumber(),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
            return;
        }
        log.info("收货明细：{}", JSONUtil.toJsonStr(updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList()));

        List<String> orderDetailIds = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForDeliveryDto.getGetOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getCustomerReceiptNum,
                        OrderDetailPool::getConfirmNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);
        log.info("orderDetailPools:{}",JSONUtil.toJsonStr(orderDetailPools));
        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【收货】订单号：{},渠道：{}商品明细查询出来为空，商品明细id:{}", updateOrderDetailForDeliveryDto.getOrderNumber(),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        Map<String, UpdateOrderDetailDto> deliveryMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId, Function.identity()));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            UpdateOrderDetailDto updateOrderDetailDto = deliveryMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setCustomerReceiptNum(orderDetailPool.getCustomerReceiptNum().add(updateOrderDetailDto.getReceiveNum()));
            updateOrderDetailPool.setOrderDetailState(updateOrderDetailDto.getOrderDetailState());
            //收货 也只看收货数量 无需关注售后数量
            BigDecimal realReceiveNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getCustomerReceiptNum());

            if (realReceiveNum.compareTo(BigDecimal.ZERO) == 0) {
                updateOrderDetailPool.setCustomerReceiptStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
            } else if (realReceiveNum.compareTo(BigDecimal.ZERO) > 0) {
                updateOrderDetailPool.setCustomerReceiptStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
            } else {
               updateOrderDetailPool.setCustomerReceiptStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    /**
     * 验收
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForCheck(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        log.info("进入验收环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【验收】,渠道：{}商品明细为空", updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda()
                .in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForDeliveryDto.getGetOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getCustomerAcceptanceNum,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum);
        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【验收】订单号：{},渠道：{}商品明细查询出来为空，商品明细id:{}", updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderNumber).collect(Collectors.toList()),
                    updateOrderDetailForDeliveryDto.getGetOrderSalesChannel(),
                    orderDetailIds);
            return;
        }
        //防止同一条明细 有相同sku验收数量未累加
        Map<String, BigDecimal> checkMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(
                Collectors.toMap(UpdateOrderDetailDto::getOrderDetailId,
                        UpdateOrderDetailDto::getCustomerAcceptanceNum,
                        BigDecimal::add));

        List<OrderDetailPool> updateList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            BigDecimal bigDecimal = checkMap.get(orderDetailPool.getOrderDetailId());
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());
            updateOrderDetailPool.setCustomerAcceptanceNum(orderDetailPool.getCustomerAcceptanceNum().add(bigDecimal));

            BigDecimal checkNum = orderDetailPool.getConfirmNum().subtract(updateOrderDetailPool.getCustomerAcceptanceNum()).subtract(orderDetailPool.getAfterSaleNum());

            if (checkNum.compareTo(BigDecimal.ZERO) == 0) {
                updateOrderDetailPool.setCustomerAcceptanceStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
            } else if (checkNum.compareTo(BigDecimal.ZERO) > 0) {
                updateOrderDetailPool.setCustomerAcceptanceStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
            } else {
                updateOrderDetailPool.setCustomerAcceptanceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateList.add(updateOrderDetailPool);
        }
        orderDetailPoolService.updateBatchById(updateList);
    }

    /**
     * 出账 东风商城账单没有记录订单明细id,福利的有订单明细id
     */
    @Transactional
    @Override
    public void updatePurchaseOrderInfoForBillOut(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入出账环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【出账】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds,1000).forEach(batchList->{
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batchList)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getCustomerCheckoutNum,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【出账】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForBillOut(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());
    }


    /**
     * 对账
     */
    @Transactional
    @Override
    public void updatePurchaseOrderInfoForBillReconciliation(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入账单对账环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【对账】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds, 1000).forEach(batchOrderIds -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batchOrderIds)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getCustomerReconciliationNum,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum);
            List<OrderDetailPool> batchList = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(batchList);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【对账】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForBillReconciliation(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());

    }

    /**
     * 删除账单明细
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForDeleteBillDetail(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入账单明细删除环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【删除账单明细】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds, 1000).forEach(batchOrderIds -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batchOrderIds)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getCustomerCheckoutNum,
                            OrderDetailPool::getCustomerReconciliationNum,
                            OrderDetailPool::getAfterSaleNum,
                            OrderDetailPool::getCustomerInvoicedNum);
            List<OrderDetailPool> batchList = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(batchList);
        });


        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【删除账单明细】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForDeleteBillDetail(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());



    }

    /**
     * 申请开票
     *
     * @param updateOrderDetailForBillDto
     */
    @Override
    @Transactional
    public void updatePurchaseOrderInfoForApplyInvoice(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入发票申请环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【开票】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds, 1000).forEach(batchOrderDetailIds -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batchOrderDetailIds)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getCustomerInvoicingNum);
            List<OrderDetailPool> batchList = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(batchList);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【开票申请】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForApplyInvoice(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());

    }

    /**
     * 开票[确认，驳回] 针对真实开票数量
     */
    @Transactional
    @Override
    public void updatePurchaseOrderInfoForInvoiceApprove(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入发票审批环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【开票审批】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds,1000).forEach(batch->{
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batch)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getCustomerInvoicedNum,
                            OrderDetailPool::getCustomerInvoicedMoney,
                            OrderDetailPool::getCustomerInvoicingNum,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【开票审批】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForInvoiceApprove(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList(),
                updateOrderDetailForBillDto.getInvoiceApproveType());


    }

    @Override
    @Transactional
    public void updatePurchaseOrderInfoForInvoiceRepayment(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入发票结算环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【发票结算】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();
        Lists.partition(orderDetailIds, 1000).forEach(batch -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batch)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum,
                            OrderDetailPool::getCustomerSettlementNum,
                            OrderDetailPool::getCustomerSettlementMoney);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【发票结算】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForInvoiceRepayment(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList());


    }

    @Override
    @Transactional
    public void updatePurchaseOrderInfoForInvoiceOffset(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入发票红冲环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【发票红冲】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }

        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailPool> orderDetailPools = new ArrayList<>();

        Lists.partition(orderDetailIds, 1000).forEach(batch -> {
            QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
            detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, batch)
                    .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                    .select(OrderDetailPool::getId,
                            OrderDetailPool::getOrderDetailId,
                            OrderDetailPool::getConfirmNum,
                            OrderDetailPool::getAfterSaleNum,
                            OrderDetailPool::getCustomerInvoicedNum,
                            OrderDetailPool::getCustomerInvoicingNum,
                            OrderDetailPool::getCustomerInvoicedMoney,
                            OrderDetailPool::getCustomerInvoiceStatus,
                            OrderDetailPool::getCustomerSettlementStatus,
                            OrderDetailPool::getCustomerSettlementMoney,
                            OrderDetailPool::getCustomerSettlementNum);
            List<OrderDetailPool> list = orderDetailPoolService.list(detailPoolQueryWrapper);
            orderDetailPools.addAll(list);
        });

        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【发票红冲】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        commentService.updateForInvoiceOffset(orderDetailPools,updateOrderDetailForBillDto.getUpdateOrderDetailDtoList(),
                updateOrderDetailForBillDto.getInvoiceSettleType());


    }

    @Override
    @Transactional
    public void updatePurchaseOrderInfoForAfterSale(UpdateOrderDetailForBillDto updateOrderDetailForBillDto) {
        log.info("进入售后环节");
        if (CollectionUtil.isEmpty(updateOrderDetailForBillDto.getUpdateOrderDetailDtoList())) {
            log.info("环节【售后】,渠道：{}商品明细为空", updateOrderDetailForBillDto.getOrderSalesChannel());
            return;
        }
        log.info("updateOrderDetailForBillDto:{}",JSONUtil.toJsonStr(updateOrderDetailForBillDto));
        List<String> orderDetailIds = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());

        QueryWrapper<OrderDetailPool> detailPoolQueryWrapper = new QueryWrapper<>();
        detailPoolQueryWrapper.lambda().in(OrderDetailPool::getOrderDetailId, orderDetailIds)
                .eq(OrderDetailPool::getOrderSalesChannel, updateOrderDetailForBillDto.getOrderSalesChannel())
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getAfterSaleNum,
                        OrderDetailPool::getCustomerCheckoutNum,
                        OrderDetailPool::getCustomerReconciliationNum,
                        OrderDetailPool::getCustomerInvoicedNum,
                        OrderDetailPool::getCustomerSettlementNum);

        List<OrderDetailPool> orderDetailPools = orderDetailPoolService.list(detailPoolQueryWrapper);

        log.info("查询要售后的明细生命周期：{}",JSONUtil.toJsonStr(orderDetailPools));
        if (CollectionUtil.isEmpty(orderDetailPools)) {
            log.info("环节【售后】,渠道：{}，商品明细查询为空，id为：{}", updateOrderDetailForBillDto.getOrderSalesChannel(),
                    orderDetailIds);
            return;
        }

        List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList =  purchaseOrderInfoPoolService.getBaseMapper().getOrderDetailStateByIds(orderDetailIds);
        if(CollectionUtil.isEmpty(shopPurchaseSubOrderDetailList)){
            log.info("售后环节，未查询到订单明细，查询明细id:{}",orderDetailIds);
            return;
        }

        Map<String, ShopPurchaseSubOrderDetail> orderDetailMap = shopPurchaseSubOrderDetailList.stream().collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getOrderDetailId, Function.identity()));


        Map<String, BigDecimal> afterSaleMap = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getAfterSaleNum,
                BigDecimal::add
        ));

        Map<String, BigDecimal> checkoutMap = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getCheckNum,
                BigDecimal::add
        ));

        Map<String, BigDecimal> reconciliationMap = updateOrderDetailForBillDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getReconciliationNum,
                BigDecimal::add
        ));

        List<OrderDetailPool> updateOrderDetailList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPools) {
            OrderDetailPool updateOrderDetail = new OrderDetailPool();
            updateOrderDetail.setId(orderDetailPool.getId());
            BigDecimal afterSaleNum = afterSaleMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal newAfterSaleNum = orderDetailPool.getAfterSaleNum().add(afterSaleNum);
            ShopPurchaseSubOrderDetail subOrderDetail = orderDetailMap.get(orderDetailPool.getOrderDetailId());
            updateOrderDetail.setAfterSaleNum(newAfterSaleNum);
            updateOrderDetail.setOrderDetailState(subOrderDetail.getOrderDetailState());

            BigDecimal subtractAfterSaleNum = orderDetailPool.getConfirmNum().subtract(newAfterSaleNum);
            if (subtractAfterSaleNum.compareTo(BigDecimal.ZERO) == 0) {
                updateOrderDetail.setAfterSaleStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
            } else if (subtractAfterSaleNum.compareTo(BigDecimal.ZERO) > 0) {
                updateOrderDetail.setAfterSaleStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
            } else {
                updateOrderDetail.setAfterSaleStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            BigDecimal checkOutNum = checkoutMap.get(orderDetailPool.getOrderDetailId());
            if (checkOutNum.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal newCheckOutNum = orderDetailPool.getCustomerCheckoutNum().subtract(afterSaleNum);
                updateOrderDetail.setCustomerCheckoutNum(newCheckOutNum);
            } else {
                updateOrderDetail.setCustomerCheckoutNum(orderDetailPool.getCustomerCheckoutNum());
            }

            BigDecimal checkOutFlag = orderDetailPool.getConfirmNum().subtract(updateOrderDetail.getAfterSaleNum()).subtract(updateOrderDetail.getCustomerCheckoutNum());

            if (checkOutFlag.compareTo(BigDecimal.ZERO) > 0) {
                if (updateOrderDetail.getCustomerCheckoutNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else {
                    updateOrderDetail.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                }

            } else if (checkOutFlag.compareTo(BigDecimal.ZERO) == 0) {
                if (updateOrderDetail.getCustomerCheckoutNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else if (updateOrderDetail.getCustomerCheckoutNum().compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetail.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetail.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            } else {
                updateOrderDetail.setCustomerCheckoutStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }


            BigDecimal reconciliationNum = reconciliationMap.get(orderDetailPool.getOrderDetailId());
            if(reconciliationNum.compareTo(BigDecimal.ZERO)>0){
                BigDecimal newReconciliationNum = orderDetailPool.getCustomerReconciliationNum().subtract(afterSaleNum);
                updateOrderDetail.setCustomerReconciliationNum(newReconciliationNum);
            }else {
                updateOrderDetail.setCustomerReconciliationNum(orderDetailPool.getCustomerReconciliationNum());
            }

            BigDecimal reconciliationFlag = orderDetailPool.getConfirmNum().subtract(updateOrderDetail.getAfterSaleNum()).subtract(updateOrderDetail.getCustomerReconciliationNum());


            if (reconciliationFlag.compareTo(BigDecimal.ZERO) > 0) {
                if (updateOrderDetail.getCustomerReconciliationNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else {
                    updateOrderDetail.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                }
            } else if (reconciliationFlag.compareTo(BigDecimal.ZERO) == 0) {
                if (updateOrderDetail.getCustomerReconciliationNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else if (updateOrderDetail.getCustomerReconciliationNum().compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetail.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetail.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }
            } else {
                updateOrderDetail.setCustomerReconciliationStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            //发票
            BigDecimal invoiceFlag = orderDetailPool.getConfirmNum().subtract(orderDetailPool.getCustomerInvoicedNum()).subtract(updateOrderDetail.getAfterSaleNum());
            if (invoiceFlag.compareTo(BigDecimal.ZERO) > 0) {
                if (orderDetailPool.getCustomerInvoicedNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else {
                    updateOrderDetail.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                }
            } else if (invoiceFlag.compareTo(BigDecimal.ZERO) == 0) {
                if (orderDetailPool.getCustomerInvoicedNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else if (orderDetailPool.getCustomerInvoicedNum().compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetail.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetail.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }

            } else {
                updateOrderDetail.setCustomerInvoiceStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            //结算
            BigDecimal settleFlag = orderDetailPool.getConfirmNum().subtract(orderDetailPool.getCustomerSettlementNum()).subtract(updateOrderDetail.getAfterSaleNum());

            if (settleFlag.compareTo(BigDecimal.ZERO) > 0) {
                if (orderDetailPool.getCustomerSettlementNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else {
                    updateOrderDetail.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                }

            } else if (settleFlag.compareTo(BigDecimal.ZERO) == 0) {
                if (orderDetailPool.getCustomerSettlementNum().compareTo(BigDecimal.ZERO) == 0) {
                    updateOrderDetail.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                } else if (orderDetailPool.getCustomerSettlementNum().compareTo(BigDecimal.ZERO) > 0) {
                    updateOrderDetail.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
                } else {
                    updateOrderDetail.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
                }

            } else {
                updateOrderDetail.setCustomerSettlementStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }

            updateOrderDetailList.add(updateOrderDetail);
        }

        orderDetailPoolService.updateBatchById(updateOrderDetailList);
    }


    @Override
    public void updatePurchaseOrderInfoForDeliveryDelete(UpdateOrderDetailForDeliveryDto updateOrderDetailForDeliveryDto) {
        List<String> orderDetailIdList = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().map(UpdateOrderDetailDto::getOrderDetailId).collect(Collectors.toList());
        QueryWrapper<OrderDetailPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderDetailPool::getOrderSalesChannel,updateOrderDetailForDeliveryDto.getGetOrderSalesChannel())
                .in(OrderDetailPool::getOrderDetailId,orderDetailIdList)
                .select(OrderDetailPool::getId,
                        OrderDetailPool::getOrderDetailId,
                        OrderDetailPool::getConfirmNum,
                        OrderDetailPool::getDeliveryNum,
                        OrderDetailPool::getProperDeliveryNum);

        List<OrderDetailPool> orderDetailPoolList = orderDetailPoolService.list(queryWrapper);
        if(CollectionUtil.isEmpty(orderDetailIdList)){
            log.info("删除的包裹未在生命周期中找到数据，订单明细id:{},渠道：{}",orderDetailIdList,updateOrderDetailForDeliveryDto.getGetOrderSalesChannel());
            return;
        }

        Map<String, BigDecimal> deliveryNumMap = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getDeliveryNum,
                BigDecimal::add
        ));

        Map<String, BigDecimal> properNumMap  = updateOrderDetailForDeliveryDto.getUpdateOrderDetailDtoList().stream().collect(Collectors.toMap(
                UpdateOrderDetailDto::getOrderDetailId,
                UpdateOrderDetailDto::getProperNum,
                BigDecimal::add));

        List<OrderDetailPool> updateOrderDetailList = new ArrayList<>();
        for (OrderDetailPool orderDetailPool : orderDetailPoolList) {
            OrderDetailPool updateOrderDetailPool = new OrderDetailPool();
            updateOrderDetailPool.setId(orderDetailPool.getId());

            BigDecimal deliveryNum = deliveryNumMap.get(orderDetailPool.getOrderDetailId());
            BigDecimal properNum = properNumMap.get(orderDetailPool.getOrderDetailId());

            BigDecimal newDeliveryNum = orderDetailPool.getDeliveryNum().subtract(deliveryNum);
            updateOrderDetailPool.setDeliveryNum(newDeliveryNum);
            updateOrderDetailPool.setProperDeliveryNum(orderDetailPool.getProperDeliveryNum().subtract(properNum));

            BigDecimal deliveryNumFlag = orderDetailPool.getConfirmNum().subtract(newDeliveryNum);

            if(deliveryNumFlag.compareTo(BigDecimal.ZERO)==0){
                updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.FULL_COMPLETED.getCode());
            }else if(deliveryNumFlag.compareTo(BigDecimal.ZERO)>0){
                if(newDeliveryNum.compareTo(BigDecimal.ZERO)==0){
                    updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.NOT_START.getCode());
                }else {
                    updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.PORTION.getCode());
                }
            }else {
                updateOrderDetailPool.setDeliveryStatus(LifeCycleNodeStatusEnum.ABNORMAL.getCode());
            }
            updateOrderDetailList.add(updateOrderDetailPool);
        }

        orderDetailPoolService.updateBatchById(updateOrderDetailList);
    }

    @Override
    public void updatePurchaseOrderInfoForSupplierInvoice(List<SupplierInvoiceDelDto> supplierInvoiceDelDtos) {
        commentService.updatePurchaseOrderInfoForSupplierInvoice(supplierInvoiceDelDtos);
    }
}
