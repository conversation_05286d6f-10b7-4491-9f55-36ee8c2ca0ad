package com.ly.yph.api.product.ext.qx.processor;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.product.ext.common.BaseDataProcessor;
import com.ly.yph.api.product.ext.common.enums.MessageStatusEnum;
import com.ly.yph.api.product.ext.qx.config.QxConfig;
import com.ly.yph.api.product.ext.qx.dto.QxGetGoodsSellPriceResp;
import com.ly.yph.api.product.ext.qx.entity.BackupQxGoodsEntity;
import com.ly.yph.api.product.ext.qx.service.BackupQxGoodsService;
import com.ly.yph.api.product.ext.qx.service.QxRemoteProxy;
import com.ly.yph.api.product.ext.zkh.service.QueueMsgSupplierProductProcessInfoService;
import com.ly.yph.core.lock.RedissonLock;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 填充backup_qx_goods表
 *
 * <AUTHOR>
 * @date 2022/03/15
 */
@Service("QxInstoreProcessor")
@Slf4j
public class QxInstoreProcessor extends BaseDataProcessor<Long> {
    @Getter
    protected String processName = "QX数据获取";
    @Value("${application.data.qx.process-per-time}")
    public int processPerTime;
    @Value("${application.data.qx.detail-per-time}")
    public int detailPerTime;
    @Resource
    private QxConfig config;
    @Resource
    private QueueMsgSupplierProductProcessInfoService msgInfoSrv;
    @Resource
    private BackupQxGoodsService backupSrv;
    @Resource
    private CodeGeneral codeGeneral;
    @Resource
    private SystemTenantService systemTenantService;
    @Resource
    private QxRemoteProxy qxRemoteProxy;
    @Resource
    private RedissonLock redissonLock;

    @Override
    public List<Long> supplier(int count) {
        return msgInfoSrv.selectIdBySupplierAndStatus(count, MessageStatusEnum.INIT, config.getCode());
    }

    @Override
    @DistributedLock(value = "qx_instore_process_locker", key = "#id", leaseTime = 60, waitLock = false)
    public void processItem(Long id) {
        Assert.notNull(id);
        val uEnt = msgInfoSrv.getById(id);
        if (uEnt == null || uEnt.getInStoreStatus() == 1) {
            return;
        }

        // 再加个sku的锁,防止电商疯狂推重复消息
        String lockName = uEnt.getSupplier() + '_' + uEnt.getSkuId();
        if (redissonLock.isLock(lockName)) {
            return;
        }

        TenantUtils.execute(systemTenantService.getTenantIds(), () -> {
            var eEnt = backupSrv.selectOneBySkuId(Long.valueOf(uEnt.getSkuId()));
            JSONObject res = JSONUtil.parseObj(uEnt.getInfo());

            var sEnt = new BackupQxGoodsEntity();
            if (eEnt != null) {
                BeanUtil.copyProperties(eEnt, sEnt);
            } else {
                // 生成good code
                sEnt.setGoodCode(codeGeneral.getProductCode(config.getCode()));
                sEnt.setSku(uEnt.getSkuId());
            }
            sEnt.setUrl(res.getStr("url"));
            sEnt.setModel(res.getStr("model"));
            sEnt.setWeight(res.getStr("weight"));
            sEnt.setImagePath(res.getStr("image_path"));
            sEnt.setState(res.getInt("state") == null ? 0 : res.getInt("state"));
            sEnt.setBrandName(res.getStr("brand_name"));
            sEnt.setName(res.getStr("name"));
            sEnt.setProductArea(res.getStr("product_area"));
            sEnt.setUpc(res.getStr("upc"));
            sEnt.setUnit(res.getStr("unit"));
            sEnt.setMinNum(res.getInt("min_num"));
            sEnt.setCategory(res.getStr("category"));
            sEnt.setService(res.getStr("service"));
            sEnt.setIntroduction(res.getStr("introduction"));
            sEnt.setParam(res.getStr("goods_spec"));
            sEnt.setWare(res.getStr("ware"));
            sEnt.setSaleActives(res.getInt("sale_actives"));
            sEnt.setCreateTime(new Date());
            sEnt.setUpdateTime(new Date());
            sEnt.setTaxCode(res.getStr("tax_code"));
            //查询价格
            Map<String, QxGetGoodsSellPriceResp> salePrice = qxRemoteProxy.getSalePrice(uEnt.getSkuId());
            if (salePrice.get(uEnt.getSkuId()) == null) {
                msgInfoSrv.updateInStoreError(id, "价格查询失败,请联系电商处理");
                return;
            }
            sEnt.setMallPrice(salePrice.get(uEnt.getSkuId()).getMallPrice());
            sEnt.setPrice(salePrice.get(uEnt.getSkuId()).getPrice());
            sEnt.setNakedPrice(salePrice.get(uEnt.getSkuId()).getNakedPrice());
            BigDecimal taxRate = salePrice.get(uEnt.getSkuId()).getTaxRate();
            sEnt.setTaxRate(taxRate.movePointRight(2).intValue());
            // 更新和添加统一设置成未同步状态，标志位全部重设
            sEnt.setSynchronize((byte) 0);
            sEnt.setValidateFlag((byte) 0);
            sEnt.setIsEnable("1");
            // 这里不要
            sEnt.setPriceProcFlag((byte) 0);
            sEnt.setShiftProcFlag((byte) 0);
            sEnt.setImageProcFlag((byte) 0);
            sEnt.setCategoryProcFlag((byte) 0);
            sEnt.setTenantId(TenantContextHolder.getTenantId());
            backupSrv.saveOrUpdate(sEnt);
        });
        msgInfoSrv.updateInStoreSuccess(id);

        // 释放锁
        redissonLock.unlock(lockName);
    }
}
