package com.ly.yph.api.bill.service.invoiceplan;

import com.ly.yph.api.bill.controller.plan.vo.*;
import com.ly.yph.api.bill.dal.dataobject.invoiceplan.MizdInvoicePlanDO;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;

import java.util.*;
import javax.validation.*;


/**
 * 账单计划 Service 接口
 *
 * <AUTHOR>
 */
public interface MizdInvoicePlanService {

    /**
     * 创建账单计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInvoicePlan(@Valid MizdInvoicePlanSaveReqVO createReqVO);

    /**
     * 供应商账单自动生成支付计划
     * @param billId
     */
    void createInvoicePlanByBill(Long billId);

    /**
     * 更新账单计划
     *
     * @param updateReqVO 更新信息
     */
    void updateInvoicePlan(@Valid MizdInvoicePlanSaveReqVO updateReqVO);

    /**
     * 删除账单计划
     *
     * @param id 编号
     */
    void deleteInvoicePlan(Long id);

    /**
     * 获得账单计划
     *
     * @param id 编号
     * @return 账单计划
     */
    MizdInvoicePlanDO getInvoicePlan(Long id);

    // MizdInvoicePlanDO getInvoicePlanExt(String payNo);

    /**
     * 获得账单计划分页
     *
     * @param pageReqVO 分页查询
     * @return 账单计划分页
     */
    PageResp<MizdInvoicePlanDO> getInvoicePlanPage(PageReq pager, MizdInvoicePlanPageReqVO pageReqVO);
    PageResp<MizdInvoicePlanDO> getInvoicePlanPageExt(PageReq pager, MizdInvoicePlanPageReqExtVO pageReqVO);

    /**
     * 更新发票计划状态
     *
     * @param updateReqVO 包含更新信息的请求对象
     */
    void updateInvoiceStatus(@Valid MizdInvoicePlanStatusUpdateReqVO updateReqVO);

    /**
     * 批量导入发票计划
     *
     * @param list 包含发票计划导入信息的列表
     */
    void importInvoicePlan(List<MizdInvoicePlanImportVO> list);

}