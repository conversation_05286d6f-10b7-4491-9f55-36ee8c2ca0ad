<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.organization.mapper.SystemContractRenewalMapper">

    <resultMap type="com.ly.yph.api.organization.entity.SystemContractRenewalEntity" id="SystemContractRenewalMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="contractCode" column="contract_code" jdbcType="VARCHAR"/>
        <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
        <result property="attachmentUrl" column="attachment_url" jdbcType="VARCHAR"/>
        <result property="validityStart" column="validity_start" jdbcType="TIMESTAMP"/>
        <result property="validityEnd" column="validity_end" jdbcType="TIMESTAMP"/>
        <result property="renewalType" column="renewal_type" jdbcType="INTEGER"/>
        <result property="discountChange" column="discount_change" jdbcType="INTEGER"/>
        <result property="companyOrgId" column="company_org_id" jdbcType="INTEGER"/>
        <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="oldContractId" column="old_contract_id" jdbcType="INTEGER"/>
        <result property="contractApprovalState" column="contract_approval_state" jdbcType="INTEGER"/>
        <result property="isEnable" column="is_enable" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="SystemContractRenewalMap">
        select
        *
        from system_contract_renewal
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierCode != null and supplierCode != ''">
                and supplier_code = #{supplierCode}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
            <if test="contractCode != null and contractCode != ''">
                and contract_code = #{contractCode}
            </if>
            <if test="contractName != null and contractName != ''">
                and contract_name = #{contractName}
            </if>
            <if test="attachmentUrl != null and attachmentUrl != ''">
                and attachment_url = #{attachmentUrl}
            </if>
            <if test="validityStart != null">
                and validity_start = #{validityStart}
            </if>
            <if test="validityEnd != null">
                and validity_end = #{validityEnd}
            </if>
            <if test="renewalType != null">
                and renewal_type = #{renewalType}
            </if>
            <if test="discountChange != null">
                and discount_change = #{discountChange}
            </if>
            <if test="companyOrgId != null">
                and company_org_id = #{companyOrgId}
            </if>
            <if test="companyCode != null and companyCode != ''">
                and company_code = #{companyCode}
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name = #{companyName}
            </if>
            <if test="oldContractId != null">
                and old_contract_id = #{oldContractId}
            </if>
            <if test="contractApprovalState != null">
                and contract_approval_state = #{contractApprovalState}
            </if>
            <if test="isEnable != null">
                and is_enable = #{isEnable}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="modifier != null and modifier != ''">
                and modifier = #{modifier}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <select id="approvalPage" resultType="com.ly.yph.api.organization.vo.ContractRenewalApprovalVo">
        SELECT
            w.workflow_id as workFlowId,
            w.id as stepId,
            r.id as contractRenewalId,
            r.supplier_name,
            r.company_code,
            r.company_name,
            r.contract_code,
            r.contract_name,
            concat(r.validity_start,'-',r.validity_end) as validityDate,
            ifnull(cr.is_start,0),
            w.status,
            w.step_order,
            w.user_ids,
            r.sign_type
        FROM workflow_step W
             INNER JOIN system_contract_renewal r ON r.id = w.bus_id
             LEFT JOIN system_contract c on c.id = r.old_contract_id
             LEFT JOIN supplier_contract_real cr on cr.contract_id = c.id
        <where>
            <if test="queryDto.signCompanyId != null">
                AND r.company_org_id = #{queryDto.signCompanyId}
            </if>
            <if test="queryDto.supplierCode != null and queryDto.supplierCode != ''">
                and r.supplier_code = #{queryDto.supplierCode}
            </if>
            <if test="queryDto.contractCode != null and queryDto.contractCode != ''">
                and r.contract_code = #{queryDto.contractCode}
            </if>
            <if test="queryDto.contractName != null and queryDto.contractName != ''">
                and r.contract_name = #{queryDto.contractName}
            </if>
            <if test="queryDto.contractStartDate != null and queryDto.contractStartDate != '' and queryDto.contractEndDate and queryDto.contractEndDate != '' ">
                AND cr.contract_end_date BETWEEN #{queryDto.contractStartDate}
                AND #{queryDto.contractEndDate}
            </if>
            <if test="queryDto.signType != null">
                AND r.sign_type = #{queryDto.signType}
            </if>
            <if test="queryDto.userIds != null">
                AND w.user_ids = #{queryDto.userIds}
            </if>
            <if test="queryDto.approveStatus != null">
                AND w.status = #{queryDto.approveStatus}
            </if>

            and w.handler in ('contractWorkFlowStepHandlerStep1', 'contractWorkFlowStepHandlerStep2') and w.status!=-1
            order by  w.id desc
        </where>
    </select>

    <select id="queryApprovalRemind" resultType="com.ly.yph.api.organization.vo.ContractApprovalRemindVo">
        SELECT
            c.contract_code,
            cr.contract_end_date,
            DATEDIFF(cr.contract_end_date, CURDATE()) AS countdownDay,
            r.sign_type
        FROM system_contract_renewal r
        INNER JOIN supplier_contract_real cr on cr.contract_id = r.old_contract_id
        INNER JOIN system_contract c on c.id = r.old_contract_id
        WHERE r.sign_type = 1
          and cr.contract_end_date &lt; DATE_ADD(NOW(), INTERVAL 45 DAY)
          and r.contract_approval_state = 0
        UNION
        select r.contract_code, r.validity_end, -1, r.sign_type
        from system_contract_renewal r
        where r.sign_type = 0
          and r.contract_approval_state = 0

    </select>

    <select id="queryApprovalInfo" resultType="com.ly.yph.api.organization.controller.organization.vo.organization.SystemContractVO">
        select
            r.contract_code,
            r.contract_name,
            r.company_name as signCompany,
            r.validity_start as contractStartDate,
            r.validity_end as contractEndDate,
            r.attachment_url as contractScannedUrl,
            r.renewal_type,
            r.discount_change,
            r.discount
        from system_contract_renewal r
        where r.id = #{id}
    </select>

    <select id="getSyncContract" resultMap="SystemContractRenewalMap">
        select
        id,
        supplier_id,
        supplier_code,
        supplier_name,
        contract_code,
        contract_name,
        attachment_url,
        validity_start,
        validity_end,
        renewal_type,
        discount_change,
        discount,
        company_org_id,
        company_code,
        company_name,
        old_contract_id,
        contract_approval_state,
        sign_type,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time,
        tenant_id,
        doc_file_key,
        sign_flow_id,
        sign_platform_url,
        sign_supplier_url,
        finish_file_key,
        sign_status,
        sign_time,
        sign_desc,
        is_sync,
        authentication_source,
        contacter_a_name,
        contacter_a_phone,
        contacter_a_mail,
        contacter_a_address,
        contacter_b_name,
        contacter_b_phone,
        contacter_b_mail,
        contacter_b_address
        from system_contract_renewal where is_sync=1 order by create_time desc limit 2
    </select>
</mapper>
