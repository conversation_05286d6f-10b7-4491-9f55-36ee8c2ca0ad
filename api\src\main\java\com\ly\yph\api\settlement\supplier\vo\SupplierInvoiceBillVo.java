package com.ly.yph.api.settlement.supplier.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SupplierInvoiceBillVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("账单编号")
    private String billSn;

    @ApiModelProperty("发票申请单号")
    private String invoiceApplyNumber;

    @ApiModelProperty("发票号")
    private String invoiceNumber;

    @ApiModelProperty("发票路径")
    private String invoiceUrl;

    @ApiModelProperty("申请含税金额")
    private BigDecimal amountTax;

    @ApiModelProperty("申请未税金额")
    private BigDecimal amountNaked;

    @ApiModelProperty("税额")
    private BigDecimal tax;

    @ApiModelProperty("发票含税金额")
    private BigDecimal invoiceAmountTax;

    @ApiModelProperty("发票状态")
    private Integer state;

    @ApiModelProperty("不一致说明")
    private String inconsistentRemark;

    @ApiModelProperty("不一致附件")
    private String inconsistentUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("发票确认时间")
    private Date invoiceConfirmTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("发票申请时间")
    private Date createTime;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("审批理由")
    private String approveReason;

    @ApiModelProperty("发票明细类型 默认：0 账单明细类型 ;1:邮费明细类型")
    private Integer billInvoiceType;

    @ApiModelProperty("发票容差金额")
    private BigDecimal toleranceAmount;
}
