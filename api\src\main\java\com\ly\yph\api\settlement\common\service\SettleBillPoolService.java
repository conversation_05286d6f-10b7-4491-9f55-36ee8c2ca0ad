package com.ly.yph.api.settlement.common.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.entity.ShopDelivery;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.mapper.ShopDeliveryDetailMapper;
import com.ly.yph.api.order.mapper.ShopReturnDetailMapper;
import com.ly.yph.api.order.service.ShopDeliveryService;
import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto;
import com.ly.yph.api.settlement.common.dto.bill.PoolAndOutDataRelationDto;
import com.ly.yph.api.settlement.common.dto.settleBillPool.BillPoolMatchOutBillDto;
import com.ly.yph.api.settlement.common.dto.settleBillPool.OutBillCheckedDataDto;
import com.ly.yph.api.order.service.ShopPurchaseOrderService;
import com.ly.yph.api.settlement.common.entity.SettleBillPool;
import com.ly.yph.api.settlement.common.entity.SettleBillPoolYflCustomer;
import com.ly.yph.api.settlement.common.entity.SettleCompanyBillMessage;
import com.ly.yph.api.settlement.common.enums.*;
import com.ly.yph.api.settlement.common.mapper.SettleBillPoolMapper;
import com.ly.yph.api.settlement.common.vo.bill.SettleBillPoolPageReqVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleBillPoolPageVo;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.email.MailService;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.db.TenantBaseDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 类注释
 *
 * <AUTHOR>
 * @date 2023/6/2 15:16
 */
@Slf4j
@Service
public class SettleBillPoolService extends ServiceImpl<SettleBillPoolMapper, SettleBillPool> {
    @Resource
    private SettleBillLifeCycleService billLifeCycleSrv;
    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;
    @Resource
    private SettleShopBillService settleShopBillService;
    @Resource
    private BillDetailOutCheckedRelationService billDetailOutCheckedRelationService;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private ShopReturnDetailMapper shopReturnDetailMapper;
    @Resource
    private SettleBillLifeCycleService settleBillLifeCycleService;
    @Resource
    private SettleBillPoolYflCustomerService settleBillPoolYflCustomerService;
    @Resource
    private YflYamlConfig yflYamlConfig;
    @Resource
    private MailService mailService;
    @Resource
    private SettleCompanyBillMessageService settleCompanyBillMessageService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ShopDeliveryService shopDeliveryService;
    @Resource
    private ShopDeliveryDetailMapper shopDeliveryDetailMapper;

    public void updateBillPoolStatus(List<Long> settleBillPoolIds, Integer customerType, String customerCode) {
        if (CollectionUtils.isEmpty(settleBillPoolIds)) {
            return;
        }
        Lists.partition(settleBillPoolIds, 800).forEach(subclassList ->
                this.getBaseMapper().updateBillPoolStatusByIds(subclassList, customerType, customerCode, new Date()));
    }

    @Transactional
    public void sapDeliveryToBillPool(Collection<Long> deliveryIds) {
        List<SettleBillPool> billPoolSaves = this.getBaseMapper().getToPoolBySapDeliveryData(deliveryIds);
        // 同步数据到账单池
        if (CollUtil.isNotEmpty(billPoolSaves)) {
            // 分布式锁拦截处理供应商妥投消息重复且并发的情况
            List<SettleBillPool> validData = billPoolSaves.stream().filter(e -> {
                log.info("开始处理包裹明细：{}", e.getDeliveryDetailId());
                String key = "deliveryDetailId_" + e.getDeliveryDetailId();
                Boolean aBoolean = stringRedisTemplate.opsForValue().setIfAbsent(key, e.getDeliveryDetailId().toString(), 15, TimeUnit.SECONDS);
                if (aBoolean) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(validData)) {
                log.info("并发过滤后，无需重复入帐单池！,源包裹id:{}", JSONUtil.toJsonStr(deliveryIds));
                return;
            }

            // 保存账单池
            this.saveBatch(validData, 16);
            // 保存账单池生命周期
            billLifeCycleSrv.saveLifeCycleFromPool(CollStreamUtil.toList(validData, SettleBillPool::getId));
        }
    }

    public PageResp<SettleBillPoolPageVo> querySettleBillPoolPage(PageReq pageReq, SettleBillPoolPageReqVo reqVo) {
        IPage<SettleBillPoolPageVo> settleBillPoolPage = this.getBaseMapper().querySettleBillPoolPage(DataAdapter.adapterPageReq(pageReq), reqVo);

        //填充全称
        List<SettleBillPoolPageVo> lateCheckVos = settleBillPoolPage.getRecords();
        if (CollectionUtil.isNotEmpty(lateCheckVos)) {
            List<String> supplierCodes = lateCheckVos.stream().map(SettleBillPoolPageVo::getSupplierCode).collect(Collectors.toList());
            Map<String, String> supplierMap = shopPurchaseOrderService.querySupplierFullName(supplierCodes);
            for (SettleBillPoolPageVo lateCheckVo : lateCheckVos) {
                lateCheckVo.setSupplierFullName(supplierMap.get(lateCheckVo.getSupplierCode()));
            }
        }
        settleBillPoolPage.setRecords(lateCheckVos);
        return DataAdapter.adapterPage(settleBillPoolPage, SettleBillPoolPageVo.class);
    }


    public List<SettleBillPoolPageVo> exportPoolList(SettleBillPoolPageReqVo reqVo) {
       List<SettleBillPoolPageVo> settleBillPoolPageVoList =  this.getBaseMapper().queryPoolList(reqVo);
       if(settleBillPoolPageVoList.size()>50000){
           throw new ParameterException("数据量大于5万条，请选择验收区间导出");
       }
       return settleBillPoolPageVoList;
    }

    /**
     * 单个企业 验收数据匹配出账
     *
     * @param outBillCheckedDataDtoList
     */
    @Transactional
    public void outBillChecked(List<OutBillCheckedDataDto> outBillCheckedDataDtoList,String yearAndMonth) {
        log.info("outBillCheckedDataDtoList:{}", JSONUtil.toJsonStr(outBillCheckedDataDtoList));
        if (CollectionUtils.isEmpty(outBillCheckedDataDtoList)) {
            throw new ParameterException("外部验收数据不能为空！");
        }

        List<String> orderNumbers = outBillCheckedDataDtoList.stream().map(OutBillCheckedDataDto::getOrderNumber).distinct().collect(Collectors.toList());
        List<BillPoolMatchOutBillDto> billPoolMatchOutBillDtos = this.getBaseMapper().getOutBillPoolsByOrderNumbers(orderNumbers);

        if (CollectionUtils.isEmpty(billPoolMatchOutBillDtos)) {
            outBillCheckedDataDtoList.forEach(e -> e.setBillMatchStatus(2));
            return;
        }


        Map<String, List<BillPoolMatchOutBillDto>> orderNumberMapLists = billPoolMatchOutBillDtos.stream().collect(Collectors.groupingBy(BillPoolMatchOutBillDto::getOrderNumber));
        Map<String, List<OutBillCheckedDataDto>> outDataOrderNumberMap = outBillCheckedDataDtoList.stream().collect(Collectors.groupingBy(OutBillCheckedDataDto::getOrderNumber));

        List<PoolAndOutDataRelationDto> poolAndOutDataRelationDtos = new ArrayList<>();

        for (String orderNumber : outDataOrderNumberMap.keySet()) {
            List<BillPoolMatchOutBillDto> billPools = orderNumberMapLists.get(orderNumber);
            List<OutBillCheckedDataDto> outBillCheckedDataDtos = outDataOrderNumberMap.get(orderNumber);
            //账单池没有对应订单的数据
            if (CollectionUtils.isEmpty(billPools)) {
                outBillCheckedDataDtos.forEach(e -> e.setBillMatchStatus(2));
                continue;
            }

            Map<String, List<BillPoolMatchOutBillDto>> goodsCodePoolsMap = billPools.stream().collect(Collectors.groupingBy(BillPoolMatchOutBillDto::getGoodsCode));
            Map<String, List<OutBillCheckedDataDto>> goodsCodeOutDataMap = outBillCheckedDataDtos.stream().collect(Collectors.groupingBy(OutBillCheckedDataDto::getGoodsCode));

            for (String goodsCode : goodsCodeOutDataMap.keySet()) {
                List<BillPoolMatchOutBillDto> billPoolList = goodsCodePoolsMap.get(goodsCode);
                List<OutBillCheckedDataDto> dataDtoList = goodsCodeOutDataMap.get(goodsCode);

                //同个订单某个商品没出账
                if (CollectionUtils.isEmpty(billPoolList)) {
                    dataDtoList.forEach(e -> e.setBillMatchStatus(2));
                    continue;
                }

                //同个订单-商品 有出账
                BigDecimal goodsCodeCheckedTotalNum = billPoolList.stream().map(BillPoolMatchOutBillDto::getCheckedNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal goodsCodeOutDataTotalNum = dataDtoList.stream().map(OutBillCheckedDataDto::getGoodsNum).reduce(BigDecimal.ZERO, BigDecimal::add);

                if (goodsCodeCheckedTotalNum.compareTo(goodsCodeOutDataTotalNum) == 0) {
                    //匹配成功
                    dataDtoList.forEach(e -> e.setBillMatchStatus(1));
                    //建立与账单池的关系 用于出账,开票
                    //1.优先包裹出账数量和验收数量一致的匹配
                    for (BillPoolMatchOutBillDto billPoolMatchOutBillDto : billPoolList) {
                        EqualPriorityMatch(billPoolMatchOutBillDto, dataDtoList, poolAndOutDataRelationDtos);
                    }

                    List<BillPoolMatchOutBillDto> haveNotEqualMatchList = billPoolList.stream()
                            .filter(e -> e.getCheckedNum().compareTo(e.getPoolHaveMatchedNum()) != 0)
                            .sorted(Comparator.comparing(BillPoolMatchOutBillDto::getCheckedNum))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(haveNotEqualMatchList)) {
                        //2.剩下需要组合的再重新匹配
                        List<OutBillCheckedDataDto> OutDataHaveNotEqualList = dataDtoList.stream().filter(e -> e.getGoodsNum().compareTo(e.getHaveMatchedNum()) != 0)
                                .sorted(Comparator.comparing(OutBillCheckedDataDto::getGoodsNum))
                                .collect(Collectors.toList());
                        for (BillPoolMatchOutBillDto billPoolMatchOutBillDto : haveNotEqualMatchList) {
                            billPoolMatchOutCheckedData(billPoolMatchOutBillDto, OutDataHaveNotEqualList, poolAndOutDataRelationDtos);
                        }
                    }
                } else {
                    //数量匹配失败 需要手动去匹配包裹
                    log.info("订单：{},商品编码：{}总数量不一致",orderNumber,goodsCode);
                    dataDtoList.forEach(e -> e.setBillMatchStatus(2));
                    continue;
                }
            }
        }
        if (CollectionUtils.isEmpty(poolAndOutDataRelationDtos)) {
            return;
        }
        //对应的账单池自动出账和确认，等待被开票
        settleShopBillService.checkedBillOfOutData(poolAndOutDataRelationDtos, yearAndMonth);
        //保存关系账单明细与外部验收数据的关系
        billDetailOutCheckedRelationService.saveRelations(poolAndOutDataRelationDtos);
    }

    /**
     * 账单出账数据与客户验收数量一致的优先匹配
     *
     * @param billPoolMatchOutBillDto    账单池数据
     * @param dataDtoList                外部验收数据
     * @param poolAndOutDataRelationDtos 关系集合
     */
    public void EqualPriorityMatch(BillPoolMatchOutBillDto billPoolMatchOutBillDto, List<OutBillCheckedDataDto> dataDtoList, List<PoolAndOutDataRelationDto> poolAndOutDataRelationDtos) {
        for (OutBillCheckedDataDto outBillCheckedDataDto : dataDtoList) {
            if (outBillCheckedDataDto.getGoodsNum().compareTo(outBillCheckedDataDto.getHaveMatchedNum()) == 0) {
                continue;
            }
            if (billPoolMatchOutBillDto.getCheckedNum().compareTo(outBillCheckedDataDto.getGoodsNum()) == 0) {

                PoolAndOutDataRelationDto relationDto = new PoolAndOutDataRelationDto();
                relationDto.setBillPoolId(billPoolMatchOutBillDto.getBillPoolId());
                relationDto.setBillCheckedNum(billPoolMatchOutBillDto.getCheckedNum());
                relationDto.setOutDataId(outBillCheckedDataDto.getImportId());
                relationDto.setOutCheckedNum(outBillCheckedDataDto.getGoodsNum());
                relationDto.setMatchNum(billPoolMatchOutBillDto.getCheckedNum());
                relationDto.setOrderNumber(billPoolMatchOutBillDto.getOrderNumber());
                relationDto.setGoodsCode(billPoolMatchOutBillDto.getGoodsCode());
                poolAndOutDataRelationDtos.add(relationDto);

                billPoolMatchOutBillDto.setPoolHaveMatchedNum(billPoolMatchOutBillDto.getCheckedNum());
                outBillCheckedDataDto.setHaveMatchedNum(outBillCheckedDataDto.getGoodsNum());
                return;
            }
        }

    }

    /**
     * 账单池数据与外部数据匹配 验收数量不等于出账数量
     *
     * @param billPoolMatchOutBillDto
     * @param dataDtoList
     * @param poolAndOutDataRelationDtos
     */
    public void billPoolMatchOutCheckedData(BillPoolMatchOutBillDto billPoolMatchOutBillDto, List<OutBillCheckedDataDto> dataDtoList, List<PoolAndOutDataRelationDto> poolAndOutDataRelationDtos) {
        BigDecimal poolHaveMatchedNum = billPoolMatchOutBillDto.getPoolHaveMatchedNum();
        BigDecimal checkedNum = billPoolMatchOutBillDto.getCheckedNum();
        if (checkedNum.compareTo(poolHaveMatchedNum) == 0) {
            return;
        } else if (checkedNum.compareTo(poolHaveMatchedNum) == -1) {
            log.info("异常数据：{},checkedNum:{}，poolHaveMatchedNum：{}", billPoolMatchOutBillDto, poolHaveMatchedNum);
            throw new ParameterException("账单池数量出账数量不能小于已匹配数量，请检查数据！");
        }

        // 账单池还需要去匹配的数据
        BigDecimal toBillOutNum = checkedNum.subtract(poolHaveMatchedNum);


        for (OutBillCheckedDataDto outBillCheckedDataDto : dataDtoList) {
            BigDecimal goodsNum = outBillCheckedDataDto.getGoodsNum();
            BigDecimal haveMatchedNum = outBillCheckedDataDto.getHaveMatchedNum();
            if (goodsNum.compareTo(haveMatchedNum) == 0) {
                //这个外部验收数据已经被匹配完了，换下一个匹配
                continue;
            }
            //外部验收待匹配的数据
            BigDecimal outToMatchNum = goodsNum.subtract(haveMatchedNum);

            PoolAndOutDataRelationDto relationDto = new PoolAndOutDataRelationDto();
            relationDto.setBillPoolId(billPoolMatchOutBillDto.getBillPoolId());
            relationDto.setBillCheckedNum(billPoolMatchOutBillDto.getCheckedNum());
            relationDto.setOutDataId(outBillCheckedDataDto.getImportId());
            relationDto.setOutCheckedNum(outBillCheckedDataDto.getGoodsNum());
            relationDto.setGoodsCode(outBillCheckedDataDto.getGoodsCode());
            relationDto.setOrderNumber(outBillCheckedDataDto.getOrderNumber());

            if (toBillOutNum.compareTo(outToMatchNum) == 0) {
                //刚好匹配上,建立关系
                relationDto.setMatchNum(outToMatchNum);
                poolAndOutDataRelationDtos.add(relationDto);

                //更新外部数据已匹配数量,账单池已匹配数量
                outBillCheckedDataDto.setHaveMatchedNum(outBillCheckedDataDto.getHaveMatchedNum().add(outToMatchNum));
                billPoolMatchOutBillDto.setPoolHaveMatchedNum(billPoolMatchOutBillDto.getPoolHaveMatchedNum().add(toBillOutNum));
                return;
            } else if (toBillOutNum.compareTo(outToMatchNum) == -1) {
                //账单池数量小于外部数据，也算匹配完成了 更新外部待匹配的数据,再循环下一个包裹来匹配验收明细
                relationDto.setMatchNum(toBillOutNum);
                poolAndOutDataRelationDtos.add(relationDto);

                outBillCheckedDataDto.setHaveMatchedNum(outBillCheckedDataDto.getHaveMatchedNum().add(toBillOutNum));
                billPoolMatchOutBillDto.setPoolHaveMatchedNum(billPoolMatchOutBillDto.getPoolHaveMatchedNum().add(toBillOutNum));
                return;
            } else {
                //账单池数量大于外部数，先补包裹匹配数据,匹配数量为验收待匹配的数量，
                relationDto.setMatchNum(outToMatchNum);
                poolAndOutDataRelationDtos.add(relationDto);

                outBillCheckedDataDto.setHaveMatchedNum(outBillCheckedDataDto.getHaveMatchedNum().add(outToMatchNum));
                billPoolMatchOutBillDto.setPoolHaveMatchedNum(billPoolMatchOutBillDto.getPoolHaveMatchedNum().add(outToMatchNum));
                //继续调用匹配 递归直到这个包裹匹配完成

                billPoolMatchOutCheckedData(billPoolMatchOutBillDto, dataDtoList, poolAndOutDataRelationDtos);
                return;
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePlatformOutReconciliation(String purchaseNumber) {
        //SRM,DHEC 两家自己的供应商不用出账，下游收货验收也不出账了
        List<SettleBillPool> settleBillPoolList = getBaseMapper().getPlatformOutReconciliationListByPurchaseNumber(purchaseNumber);
        if (CollectionUtils.isEmpty(settleBillPoolList)) {
            log.info("采购单：{}查询外部出账数据为空，无需出外部账单", purchaseNumber);
            return;
        }
        //DFMALL的平台外对账的订单实时入池，出账；后续的收货验收都需要排除出账
        saveBatch(settleBillPoolList);
        // 保存账单池生命周期
        billLifeCycleSrv.saveLifeCycleFromPools(settleBillPoolList);
        //实时生成供应商账单，不生成客户账单

        List<String> supplierCodes = settleBillPoolList.stream().map(SettleBillPool::getSupplierCode).distinct().collect(Collectors.toList());
        List<ShopSupplier> supplierList = shopSupplierService.getBaseMapper().queryBillSupplierInfo(supplierCodes, TenantContextHolder.getRequiredTenantId());
        if (CollectionUtil.isEmpty(supplierList)) {
            log.info("实时生成账单，查询供应商信息为空，入参codes:{},租户tanantId:{}", supplierCodes, TenantContextHolder.getRequiredTenantId());
            return;
        }
        Map<String, ShopSupplier> supplierMap = supplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity(), (key1, key2) -> key1));

        Map<String, List<SettleBillPool>> supplierPoolsMap = settleBillPoolList.stream().collect(Collectors.groupingBy(SettleBillPool::getSupplierCode));
        String yearAndMonth = DateUtils.format(new Date(), DateUtils.YEAR_MONTH);
        for (String supplierCode : supplierPoolsMap.keySet()) {
            ShopSupplier supplier = supplierMap.get(supplierCode);
            if (supplier == null) {
                log.info("实时生成电商账单，未发现电商信息，电商编码为：{}", supplierCode);
                continue;
            }
            List<SettleBillPool> billPoolList = supplierPoolsMap.get(supplierCode);

            settleShopBillService.processCheckOutBaseOnCompanyFlagAndPlatformFlag(supplier.getSupplierCode(),
                    supplier.getSupplierFullName(),
                    yearAndMonth,
                    Boolean.FALSE,
                    BillCustomerTypeEnum.SUPPLIER.getCode(),
                    CustomerSourceTypeEnum.STANDARD_MALL.getCode(),
                    BillAreaTypeEnum.CENTRAL_CHINA.getCode(),
                    PlatformReconciliationEnum.OUTPLATFORMRECONCILIATION.getCode(),
                    billPoolList);

        }
    }

    /**
     * 包裹入池
     *
     * @param deliveryIdList 包裹id集合
     */
    @Transactional
    public void deliveryToPool(List<Long> deliveryIdList) {

        List<ShopDelivery> deliveryList = shopDeliveryService.listByIds(deliveryIdList);

        Map<Integer, List<ShopDelivery>> historyMap = deliveryList.stream().collect(Collectors.groupingBy(ShopDelivery::getIsHistory));

        for (Integer integer : historyMap.keySet()) {
            List<ShopDelivery> shopDeliveries = historyMap.get(integer);
            if (integer == 1) {
                deliveryToPoolForNew(shopDeliveries);
            } else {
                deliveryToPoolForHistoryYfl(shopDeliveries);
            }
        }

    }

    @Transactional
    public void deliveryToPoolForHistoryYfl(List<ShopDelivery> shopDeliveries) {
        List<ShopDelivery> yflDelivery = shopDeliveries.stream().filter(e -> yflYamlConfig.getTenantId().equals(e.getTenantId())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(yflDelivery)){
            return;
        }
        //yfl的更新账单池明细状态
        List<ShopPurchaseSubOrderDetail> orderDetailIdList = shopDeliveryDetailMapper.getOrderDetailIdByDeliveryId(yflDelivery.stream().map(ShopDelivery::getId).collect(Collectors.toList()));
        if(CollectionUtil.isEmpty(orderDetailIdList)){
            return;
        }

        QueryWrapper<SettleBillPoolYflCustomer> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleBillPoolYflCustomer::getOrderDetailId,
                orderDetailIdList.stream().map(ShopPurchaseSubOrderDetail::getOrderDetailId).collect(Collectors.toList()));
        List<SettleBillPoolYflCustomer> settleBillPoolYflCustomers = settleBillPoolYflCustomerService.list(queryWrapper);
        if (CollectionUtil.isEmpty(settleBillPoolYflCustomers)) {
            log.info("福利账单池未查询到数据,包裹id:{}", JSONUtil.toJsonStr(yflDelivery.stream().map(ShopDelivery::getId).collect(Collectors.toList())));
            return;
        }

        Map<String, ShopPurchaseSubOrderDetail> orderDetailMap = orderDetailIdList.stream()
                .collect(Collectors.toMap(ShopPurchaseSubOrderDetail::getOrderDetailId, Function.identity(), (key1, key2) -> key1));

        List<SettleBillPoolYflCustomer> updateForYflPool = new ArrayList<>();
        settleBillPoolYflCustomers.forEach(e -> {
            ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = orderDetailMap.get(e.getOrderDetailId());
            SettleBillPoolYflCustomer yflCustomer = new SettleBillPoolYflCustomer();
            yflCustomer.setBillPoolYflId(e.getBillPoolYflId());
            yflCustomer.setOrderDetailState(shopPurchaseSubOrderDetail.getOrderDetailState());
            updateForYflPool.add(yflCustomer);
        });
        settleBillPoolYflCustomerService.updateBatchById(updateForYflPool);

    }

    @Transactional
    public void deliveryToPoolForNew(List<ShopDelivery> shopDeliveries) {
        //3sm的包裹排除
        List<SettleBillPool> settleBillPoolList = getBaseMapper().getToPoolByDeliveryData(shopDeliveries.stream().map(ShopDelivery::getId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(settleBillPoolList)) {
            log.info("包裹id:{}无需入账", shopDeliveries.stream().map(ShopDelivery::getId).collect(Collectors.toList()));
            return;
        }
        // 发货前的售后剔除
        List<SettleCompanyBillMessage> settleCompanyBillMessages = new ArrayList<>();
        settleBillPoolList.forEach(e->{
            SettleCompanyBillMessage settleCompanyBillMessage =new SettleCompanyBillMessage();
            settleCompanyBillMessage.setDeliveryDetailId(e.getDeliveryDetailId());
            settleCompanyBillMessage.setTenantId(e.getTenantId());
            //福利的 默认客户已验收
            if(yflYamlConfig.getTenantId().equals(settleCompanyBillMessage.getTenantId())){
                settleCompanyBillMessage.setCompanyCheckState(1);
            }
            settleCompanyBillMessages.add(settleCompanyBillMessage);});

        filterPreAfterSale(settleBillPoolList,settleCompanyBillMessages);
        saveBatch(settleBillPoolList,200);
        settleBillLifeCycleService.saveLifeCycleFromPools(settleBillPoolList);
        settleCompanyBillMessageService.saveBatch(settleCompanyBillMessages,200);
    }

    private void filterPreAfterSale(List<SettleBillPool> settleBillPools,List<SettleCompanyBillMessage> settleCompanyBillMessages) {

        Map<String, List<SettleBillPool>> orderDetailIdMap = settleBillPools.stream().collect(Collectors.groupingBy(SettleBillPool::getOrderDetailId));
        //查询售后明细
        List<String> orderDetailIdListForPreReturn = shopReturnDetailMapper.getPreReturnOrderDetailIdList(orderDetailIdMap.keySet());

        if (CollectionUtil.isEmpty(orderDetailIdListForPreReturn)) {
            return;
        }
        Map<Long, SettleCompanyBillMessage> messageMap = settleCompanyBillMessages.stream().collect(Collectors.toMap(SettleCompanyBillMessage::getDeliveryDetailId, Function.identity()));

        for (String orderDetailId : orderDetailIdMap.keySet()) {

            if (!orderDetailIdListForPreReturn.contains(orderDetailId)) {
                continue;
            }
            //存在先售后的 得过滤数据
            orderDetailIdMap.get(orderDetailId)
                    .forEach(e -> {
                        e.setReturnNum(e.getDeliveryNum());
                        e.setCheckedNum(BigDecimal.ZERO);
                        e.setSupplierCheckedNum(BigDecimal.ZERO);
                        //无需出账
                        e.setCompanyOutAccountState(BillPoolOutStatusEnum.NO_NEED_BILL.getCode());
                        e.setSupplierOutAccountState(BillPoolOutStatusEnum.NO_NEED_BILL.getCode());

                        SettleCompanyBillMessage message = messageMap.get(e.getDeliveryDetailId());
                        message.setCompanyProcessState(3);
                        message.setSupplierProcessState(3);
                        message.setCompanyRemark("发货前售后");
                    });
        }
    }

    /**
     * 1.区分东风商城 | 友福利商城
     * 2 - 东风商城
     * - 新包裹 全部更新账单池数据（因为发货了就入帐单池了）
     * - 历史包裹,
     * - 因外部系统是妥投入池的，订单明细的状态不会被更新，所以这里需要去查询赋值
     * - 标准商城 验收入池，此时已经拿到最新的订单明细状态
     * - 福利商城
     * - 客户侧 更新友福利池的订单明细状态
     * - 供应商侧
     * - 新包裹 直接更新池数据
     * - 旧包裹 验收入池 已经是最新的状态
     */
    @Transactional
    public void deliveryReceiptUpdateOrderStateForBillPool(List<Long> deliveryIdList) {

        List<ShopDelivery> deliveryList = shopDeliveryService.listByIds(deliveryIdList);

        if (CollectionUtil.isEmpty(deliveryList)) {
            return;
        }

        List<SettleBillPool> settleBillPools = getBaseMapper().getBillPoolByDeliveryIds(deliveryList.stream().map(ShopDelivery::getId).collect(Collectors.toList()));
        if (CollectionUtil.isNotEmpty(settleBillPools)) {
            // 更新账单池中 订单明细的状态
            List<SettleBillPool> updateBillPoolList = new ArrayList<>();
            settleBillPools.forEach(e -> {
                SettleBillPool update = new SettleBillPool();
                update.setId(e.getId());
                update.setOrderDetailState(40);
                update.setCusReceivingState(e.getCusReceivingState());
                update.setCusReceivingTime(e.getCusReceivingTime());
                updateBillPoolList.add(update);
            });
            updateBatchById(updateBillPoolList);
        }

        // 更新友福利
        deliveryToPoolForHistoryYfl(deliveryList);

    }

    @Transactional
    public void UpdatePoolForDeliveryDelete(List<Long> deliveryDetailIdList) {
        QueryWrapper<SettleBillPool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SettleBillPool::getDeliveryDetailId,deliveryDetailIdList);
        List<SettleBillPool> settleBillPools = list(queryWrapper);
        if(CollectionUtil.isEmpty(settleBillPools)){
            log.info("删除包裹 未查询到账单明细，明细id:{}",deliveryDetailIdList);
            return;
        }

        // 前面删除 已经控制了已签收和是否已上传签收单的校验
        // 删除之前 只可能供应商出账了 客户一定是没出帐
        // 保险处理 只要有出账的 发邮件提醒 人工介入
        List<SettleBillPool> okList = new ArrayList<>();
        List<SettleBillPool> errorList = new ArrayList<>();
        for (SettleBillPool settleBillPool : settleBillPools) {

            if (BillPoolOutStatusEnum.BILLED.getCode() != settleBillPool.getCompanyOutAccountState() &&
                    BillPoolOutStatusEnum.BILLED.getCode() != settleBillPool.getSupplierOutAccountState()) {
                okList.add(settleBillPool);
            } else {
                errorList.add(settleBillPool);
            }
        }

        if (CollectionUtil.isNotEmpty(okList)) {
            UpdateWrapper<SettleBillPool> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(SettleBillPool::getId, okList.stream().map(SettleBillPool::getId).collect(Collectors.toList()))
                    .set(SettleBillPool::getSupplierOutAccountState, BillPoolOutStatusEnum.NO_NEED_BILL.getCode())
                    .set(SettleBillPool::getCompanyOutAccountState, BillPoolOutStatusEnum.NO_NEED_BILL.getCode())
                    .set(BaseEntity::getIsEnable,0);
            update(updateWrapper);
        }

        if(CollectionUtil.isNotEmpty(errorList)){
            //发邮件 人工运维
            mailService.sendEmail("独立供应商包裹删除但是账单已出账通知！",
                    "账单主键："+JSONUtil.toJsonStr(errorList),"<EMAIL>");
        }

    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void companyBillMessageProcess(SettleCompanyBillMessage message) {
        //校验消息有无正在处理，防止与供应商消息同时处理同一条消息
        Boolean aBoolean = stringRedisTemplate.opsForValue().setIfAbsent("company_bill_message_" + message.getId(), message.getId().toString(), 5, TimeUnit.SECONDS);
        if (!aBoolean) {
            return;
        }
        try {
            settleShopBillService.companyBillOutForMessage(message);
            settleCompanyBillMessageService.processMessageForCompanySuccess(message);
        }catch (Exception e){
            settleCompanyBillMessageService.processMessageForCompanyFailure(message,e.getMessage());
            throw new RuntimeException("客户出账消息id:[" + message.getId() + "]处理失败:", e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void supplierBillMessageProcess(SettleCompanyBillMessage message) {
        Boolean aBoolean = stringRedisTemplate.opsForValue().setIfAbsent("supplier_bill_message_" + message.getId(), message.getId().toString(), 5, TimeUnit.SECONDS);
        if (!aBoolean) {
            return;
        }

        try {
            settleShopBillService.supplierBillOutForMessage(message);
            settleCompanyBillMessageService.processMessageForSupplierSuccess(message);
        } catch (Exception e) {
            settleCompanyBillMessageService.processMessageForSupplierFailure(message,e.getMessage());
            throw new ParameterException("供应商出账消息id:[" + message.getId() + "]处理失败：", e);
        }
    }
}
