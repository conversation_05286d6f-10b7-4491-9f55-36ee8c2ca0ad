package com.ly.yph.api.openapi.v1.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.ly.yph.api.openapi.v1.vo.delivery.OpenDeliveryVo;
import com.ly.yph.api.openapi.v1.vo.mesage.MessageTypeEnum;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.order.convert.DeliveryDetailConvert;
import com.ly.yph.api.order.dto.DpcaDeliveryQueryDto;
import com.ly.yph.api.order.dto.DpcaExportDeliveryQueryDto;
import com.ly.yph.api.order.dto.PurchaseOrderInfoCollectDto;
import com.ly.yph.api.order.dto.ReceiptGoodsDto;
import com.ly.yph.api.order.entity.*;
import com.ly.yph.api.order.mapper.ShopDeliveryDetailMapper;
import com.ly.yph.api.order.mapper.ShopDeliveryMapper;
import com.ly.yph.api.order.service.*;
import com.ly.yph.api.utils.OpenApiMessageUtil;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类注释
 *
 * <AUTHOR>
 * @date 2024/11/22 10:52
 */
@Slf4j
@Service
public class OpenDeliveryService {
    @Resource
    private OpenApiConfig openApiCfg;
    @Resource
    private OpenApiMessageUtil openApiMessageUtil;
    @Resource
    private ShopDeliveryMapper deliveryMapper;
    @Resource
    private ShopDeliveryDetailMapper deliveryDetailMapper;
    @Resource
    private ShopDeliveryService deliverySrv;
    @Resource
    private ShopDeliveryDetailService shopDeliveryDetailService;
    @Resource
    private ShopPurchaseSubOrderService shopPurchaseSubOrderService;

    @Resource
    private ShopPurchaseSubOrderDetailService subOrderDetailService;
    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;

    public void pushDelivery(Long deliveryId) {
        List<String> companyCodeList = openApiCfg.getPushDeliveryCustomers();
        String code = deliveryMapper.getCompanyCodeByDeliveryId(deliveryId);
        if (companyCodeList.contains(code)) {
            openApiMessageUtil.sendMessage(code, MessageTypeEnum.CUSTOMER_DELIVERY, MapUtil.of("deliveryId", deliveryId));
        }
    }

    public OpenDeliveryVo getDeliveryById(Long deliveryId, String companyCode) {
        OpenDeliveryVo vo = deliveryMapper.getOpenDeliveryById(deliveryId, companyCode);
        if(vo == null) {
            throw new ParameterException("未查询到该发货单信息,{}:{}",companyCode,deliveryId);
        }
        List<ShopDeliveryDetail> deliveryDetail = deliveryDetailMapper.getDetailByDeliveryId(vo.getId());
        vo.setDetailList(DeliveryDetailConvert.INSTANCE.convertOpenDeliveryDetailVo(deliveryDetail));
        return vo;
    }

    public void receiptPackageByPurchaseNumber(String purchaseNumber, String companyCode) {
        List<Long> deliveryIdList = deliveryMapper.getDeliveryIdListByPurchaseNumberAndCompanyCode(purchaseNumber, companyCode);
        Assert.notNull(deliveryIdList, () -> new ParameterException("待收货数据为空!"));
        deliveryIdList.forEach(deliveryId -> deliverySrv.cusReceiptPackage(deliveryId, null));
    }

    /**
     * 同步妥投时间
     * @param deliveryId
     */
    public void pushDeliveryRealReceivingTime(Long deliveryId, Date realReceivingTime) {
        List<String> companyCodeList = openApiCfg.getPushDeliveryRealReceivingTimeCustomers();
        String code = deliveryMapper.getCompanyCodeByDeliveryId(deliveryId);
        if (companyCodeList.contains(code)) {
            Map<String, Object> params = new HashMap<>();
            params.put("deliveryId", deliveryId);
            params.put("realReceivingTime", DateUtils.format(realReceivingTime, DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
            openApiMessageUtil.sendMessage(code, MessageTypeEnum.CONFIRM_DIV, params);
        }
    }

    public List<DpcaDeliveryQueryDto> dpcaGetDelivery(String purchaseNumber, String begin, String end) {
        List<Long> deliveryIds = deliveryMapper.queryDpcaDelivery(purchaseNumber, begin, end);
        if(CollUtil.isEmpty(deliveryIds)){
            return Lists.newArrayList();
        }
        List<ShopDelivery> shopDeliveryList = deliverySrv.listByIds(deliveryIds);

        List<ShopDeliveryDetail> deliveryDetail = shopDeliveryDetailService.list(new LambdaQueryWrapperX<ShopDeliveryDetail>().in(ShopDeliveryDetail::getDeliveryId, deliveryIds));

        List<ReceiptGoodsDto> receiptGoodsDtoList = Lists.newArrayList();

        deliveryDetail.forEach(item -> {
            //过滤掉之前按单个商品收货的数据，避免发送重复收货数据
            int receiptNum = Convert.toInt(new BigDecimal(item.getDeliveryNum()).add(item.getDeliveryNumDecimal()).subtract(new BigDecimal(item.getReturnNum())));
            if (receiptNum != 0) {
                ReceiptGoodsDto receiptGoodsDto = new ReceiptGoodsDto();
                receiptGoodsDto.setId(item.getId());
                receiptGoodsDto.setReceiptNum(receiptNum);
                receiptGoodsDtoList.add(receiptGoodsDto);
            }
        });

        org.springframework.util.Assert.noNullElements(shopDeliveryList, "未查询到收货主表数据");

        List<String> deliveryDetailIds = shopDeliveryList.stream().map(ShopDelivery::getOrderId).collect(Collectors.toList());

        //查询采购单，一次收货只会存在一个采购单
        List<ShopPurchaseSubOrder> subOrders = shopPurchaseSubOrderService.listByIds(deliveryDetailIds);
        Map<String, ShopPurchaseSubOrder> subOrderMap = subOrders.stream().collect(Collectors.toMap(ShopPurchaseSubOrder::getOrderNumber, Function.identity()));
        List<String> orderNumberList = subOrders.stream().map(ShopPurchaseSubOrder::getOrderNumber).distinct().collect(Collectors.toList());
        //填充数据
        Map<String, List<ShopPurchaseSubOrderDetail>> purchaseDetailMapByOrderNumberList = subOrderDetailService.getPurchaseDetailMapByOrderNumberList(orderNumberList);
        List<String> purchaseNumberList = subOrders.stream().map(ShopPurchaseSubOrder::getPurchaseNumber).distinct().collect(Collectors.toList());
        List<ShopPurchaseOrder> purchaseList = shopPurchaseOrderService.list(new LambdaQueryWrapperX<ShopPurchaseOrder>().in(ShopPurchaseOrder::getPurchaseNumber, purchaseNumberList));
        Map<String, ShopPurchaseOrder> purchaseMap = purchaseList.stream().collect(Collectors.toMap(ShopPurchaseOrder::getPurchaseNumber, Function.identity()));

        List<DpcaDeliveryQueryDto> dpcaDeliveryInfoDtoList = Lists.newArrayList();

        Map<Long, ShopDeliveryDetail> shopDeliveryDetailMap = deliveryDetail.stream().collect(Collectors.toMap(ShopDeliveryDetail::getId, Function.identity()));
        Map<Long, ShopDelivery> shopDeliveryMap = shopDeliveryList.stream().collect(Collectors.toMap(ShopDelivery::getId, Function.identity()));
        for (ReceiptGoodsDto receiptGoodsDto : receiptGoodsDtoList) {
            ShopDeliveryDetail shopDeliveryDetail = shopDeliveryDetailMap.get(receiptGoodsDto.getId());
            DpcaDeliveryQueryDto dpcaDeliveryInfoDto = new DpcaDeliveryQueryDto();
            ShopDelivery shopDelivery = shopDeliveryMap.get(shopDeliveryDetail.getDeliveryId());
            List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList = purchaseDetailMapByOrderNumberList.get(shopDelivery.getOrderNumber());
            ShopPurchaseSubOrder subOrder = subOrderMap.get(shopDelivery.getOrderNumber());
            dpcaDeliveryInfoDto.setLogisticsOrderSn(StrUtil.sub(shopDelivery.getPackageId() + shopDelivery.getDeliveryCode(), 0, 32));
            dpcaDeliveryInfoDto.setDeliveryCode(shopDelivery.getDeliveryCode());
            dpcaDeliveryInfoDto.setPackageId(shopDelivery.getPackageId());
            dpcaDeliveryInfoDto.setAcceptanceTime(shopDelivery.getCusReceivingTime());
            dpcaDeliveryInfoDto.setPurchaseNumber(subOrder.getPurchaseNumber());
            ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = shopPurchaseSubOrderDetailList.stream().filter(item -> item.getGoodsCode().equals(shopDeliveryDetail.getGoodsCode())).findFirst().get();
            dpcaDeliveryInfoDto.setItemNo(shopPurchaseSubOrderDetail.getRowSerialNumber());
            dpcaDeliveryInfoDto.setGoodsCode(shopDeliveryDetail.getGoodsCode());
            dpcaDeliveryInfoDto.setUnit(shopDeliveryDetail.getSaleUnit());
            dpcaDeliveryInfoDto.setGoodsNum(shopPurchaseSubOrderDetail.getConfirmNum());
            dpcaDeliveryInfoDto.setDeliveryNum(receiptGoodsDto.getReceiptNum());
            dpcaDeliveryInfoDto.setPrice(shopPurchaseSubOrderDetail.getGoodsUnitPriceNaked());
            dpcaDeliveryInfoDto.setRowPrice(shopPurchaseSubOrderDetail.getGoodsUnitPriceNaked().multiply(new BigDecimal(receiptGoodsDto.getReceiptNum())));
            dpcaDeliveryInfoDto.setPriceTax(shopPurchaseSubOrderDetail.getGoodsUnitPriceTax());
            dpcaDeliveryInfoDto.setRowPriceTax(shopPurchaseSubOrderDetail.getGoodsUnitPriceTax().multiply(new BigDecimal(receiptGoodsDto.getReceiptNum())));
            dpcaDeliveryInfoDto.setTaxRate(shopPurchaseSubOrderDetail.getTaxRate());
            ShopPurchaseOrder purchaseOrder = purchaseMap.get(subOrder.getPurchaseNumber());
            dpcaDeliveryInfoDto.setBuyerName(purchaseOrder.getApplyUserName());
            dpcaDeliveryInfoDto.setCompleted(shopDelivery.getCusReceivingState() == 2 ? "X" : "");
            dpcaDeliveryInfoDtoList.add(dpcaDeliveryInfoDto);
        }
        return dpcaDeliveryInfoDtoList;
    }

    public List<DpcaExportDeliveryQueryDto> exportDpcaGetDelivery(String purchaseNumber, String begin, String end) {
        List<Long> deliveryIds = deliveryMapper.queryDpcaDelivery(purchaseNumber, begin, end);
        if(CollUtil.isEmpty(deliveryIds)){
            return Lists.newArrayList();
        }
        List<ShopDelivery> shopDeliveryList = deliverySrv.listByIds(deliveryIds);

        List<ShopDeliveryDetail> deliveryDetail = shopDeliveryDetailService.list(new LambdaQueryWrapperX<ShopDeliveryDetail>().in(ShopDeliveryDetail::getDeliveryId, deliveryIds));

        List<ReceiptGoodsDto> receiptGoodsDtoList = Lists.newArrayList();

        deliveryDetail.forEach(item -> {
            //过滤掉之前按单个商品收货的数据，避免发送重复收货数据
            int receiptNum = Convert.toInt(new BigDecimal(item.getDeliveryNum()).add(item.getDeliveryNumDecimal()).subtract(new BigDecimal(item.getReturnNum())));
            if (receiptNum != 0) {
                ReceiptGoodsDto receiptGoodsDto = new ReceiptGoodsDto();
                receiptGoodsDto.setId(item.getId());
                receiptGoodsDto.setReceiptNum(receiptNum);
                receiptGoodsDtoList.add(receiptGoodsDto);
            }
        });

        org.springframework.util.Assert.noNullElements(shopDeliveryList, "未查询到收货主表数据");
        List<String> deliveryDetailIds = shopDeliveryList.stream().map(ShopDelivery::getOrderId).collect(Collectors.toList());

        //查询采购单，一次收货只会存在一个采购单
        List<ShopPurchaseSubOrder> subOrders = shopPurchaseSubOrderService.listByIds(deliveryDetailIds);
        Map<String, ShopPurchaseSubOrder> subOrderMap = subOrders.stream().collect(Collectors.toMap(ShopPurchaseSubOrder::getOrderNumber, Function.identity()));
        List<String> orderNumberList = subOrders.stream().map(ShopPurchaseSubOrder::getOrderNumber).distinct().collect(Collectors.toList());
        //填充数据
        Map<String, List<ShopPurchaseSubOrderDetail>> purchaseDetailMapByOrderNumberList = subOrderDetailService.getPurchaseDetailMapByOrderNumberList(orderNumberList);
        List<String> purchaseNumberList = subOrders.stream().map(ShopPurchaseSubOrder::getPurchaseNumber).distinct().collect(Collectors.toList());
        List<ShopPurchaseOrder> purchaseList = shopPurchaseOrderService.list(new LambdaQueryWrapperX<ShopPurchaseOrder>().in(ShopPurchaseOrder::getPurchaseNumber, purchaseNumberList));
        Map<String, ShopPurchaseOrder> purchaseMap = purchaseList.stream().collect(Collectors.toMap(ShopPurchaseOrder::getPurchaseNumber, Function.identity()));

        List<DpcaExportDeliveryQueryDto> dpcaDeliveryInfoDtoList = Lists.newArrayList();

        Map<Long, ShopDeliveryDetail> shopDeliveryDetailMap = deliveryDetail.stream().collect(Collectors.toMap(ShopDeliveryDetail::getId, Function.identity()));
        Map<Long, ShopDelivery> shopDeliveryMap = shopDeliveryList.stream().collect(Collectors.toMap(ShopDelivery::getId, Function.identity()));
        for (ReceiptGoodsDto receiptGoodsDto : receiptGoodsDtoList) {
            ShopDeliveryDetail shopDeliveryDetail = shopDeliveryDetailMap.get(receiptGoodsDto.getId());
            DpcaExportDeliveryQueryDto dpcaDeliveryInfoDto = new DpcaExportDeliveryQueryDto();
            ShopDelivery shopDelivery = shopDeliveryMap.get(shopDeliveryDetail.getDeliveryId());
            List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList = purchaseDetailMapByOrderNumberList.get(shopDelivery.getOrderNumber());
            ShopPurchaseSubOrder subOrder = subOrderMap.get(shopDelivery.getOrderNumber());
            dpcaDeliveryInfoDto.setLogisticsOrderSn(StrUtil.sub(shopDelivery.getPackageId() + shopDelivery.getDeliveryCode(), 0, 32));
            dpcaDeliveryInfoDto.setDeliveryCode(shopDelivery.getDeliveryCode());
            dpcaDeliveryInfoDto.setPackageId(shopDelivery.getPackageId());
            dpcaDeliveryInfoDto.setAcceptanceTime(shopDelivery.getCusReceivingTime());
            dpcaDeliveryInfoDto.setPurchaseNumber(subOrder.getPurchaseNumber());
            ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = shopPurchaseSubOrderDetailList.stream().filter(item -> item.getGoodsCode().equals(shopDeliveryDetail.getGoodsCode())).findFirst().get();
            dpcaDeliveryInfoDto.setItemNo(shopPurchaseSubOrderDetail.getRowSerialNumber());
            dpcaDeliveryInfoDto.setGoodsCode(shopDeliveryDetail.getGoodsCode());
            dpcaDeliveryInfoDto.setUnit(shopDeliveryDetail.getSaleUnit());
            dpcaDeliveryInfoDto.setGoodsNum(shopPurchaseSubOrderDetail.getConfirmNum());
            dpcaDeliveryInfoDto.setDeliveryNum(receiptGoodsDto.getReceiptNum());
            dpcaDeliveryInfoDto.setPrice(shopPurchaseSubOrderDetail.getGoodsUnitPriceNaked());
            dpcaDeliveryInfoDto.setRowPrice(shopPurchaseSubOrderDetail.getGoodsUnitPriceNaked().multiply(new BigDecimal(receiptGoodsDto.getReceiptNum())));
            dpcaDeliveryInfoDto.setPriceTax(shopPurchaseSubOrderDetail.getGoodsUnitPriceTax());
            dpcaDeliveryInfoDto.setRowPriceTax(shopPurchaseSubOrderDetail.getGoodsUnitPriceTax().multiply(new BigDecimal(receiptGoodsDto.getReceiptNum())));
            dpcaDeliveryInfoDto.setTaxRate(shopPurchaseSubOrderDetail.getTaxRate());
            ShopPurchaseOrder purchaseOrder = purchaseMap.get(subOrder.getPurchaseNumber());
            dpcaDeliveryInfoDto.setBuyerName(purchaseOrder.getApplyUserName());
            dpcaDeliveryInfoDto.setCompleted(shopDelivery.getCusReceivingState() == 2 ? "X" : "");
            dpcaDeliveryInfoDtoList.add(dpcaDeliveryInfoDto);
        }
        return dpcaDeliveryInfoDtoList;
    }
}
