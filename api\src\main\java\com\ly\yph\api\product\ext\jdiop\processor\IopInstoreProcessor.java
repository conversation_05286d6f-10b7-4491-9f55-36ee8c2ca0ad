package com.ly.yph.api.product.ext.jdiop.processor;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.yph.api.product.config.CodeGeneral;
import com.ly.yph.api.product.ext.common.BaseDataProcessor;
import com.ly.yph.api.product.ext.jdiop.config.JdIopConfig;
import com.ly.yph.api.product.ext.jdiop.config.JdIopConfigInfo;
import com.ly.yph.api.product.ext.jdiop.dto.reponse.getSkuDetailInfo.IopGetSkuPoolInfoGoodsResp;
import com.ly.yph.api.product.ext.jdiop.entity.BackupJdIopGoodsEntity;
import com.ly.yph.api.product.ext.jdiop.service.BackupJdIopGoodsService;
import com.ly.yph.api.product.ext.zkh.entity.QueueMsgSupplierProductProcessInfo;
import com.ly.yph.api.product.ext.zkh.service.QueueMsgSupplierProductProcessInfoService;
import com.ly.yph.core.lock.annotation.DistributedLock;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.util.TenantUtils;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * step4 商品详情入库
 *
 * <AUTHOR>
 * @date 2022/02/19
 */
@Service("IopInstoreProcessor")
@Slf4j
public class IopInstoreProcessor extends BaseDataProcessor<Long> {
    @Getter
    protected String processName = "JD入备份库";
    @Resource
    private JdIopConfig config;
    @Resource
    private QueueMsgSupplierProductProcessInfoService msgSrv;
    @Resource
    private BackupJdIopGoodsService backupSrv;
    @Resource
    private CodeGeneral cg;


    @Override
    public List<Long> supplier(final int c) {
        List<String> sup = config.getConfigList().stream().map(JdIopConfigInfo::getCode).collect(Collectors.toList());
        return msgSrv.listBySups(c, sup).stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
    }

    @Override
    @DistributedLock(value = "jd_iop_in_process_locker", key = "#id", waitLock = false)
    public void processItem(final Long id) {
        this.processBatch(Arrays.asList(id));
    }

    @Override
    @DistributedLock(value = "jd_in_process_locker_batch", leaseTime = 120, waitLock = false)
    public void processBatch(List<Long> ids) {
        val msg = msgSrv.getBaseMapper().selectSimple(ids);
        //分组
        Map<String, List<QueueMsgSupplierProductProcessInfo>> map = msg.stream().collect(Collectors.groupingBy(QueueMsgSupplierProductProcessInfo::getSupplier));

        List<JdIopConfigInfo> configList = config.getConfigList();
        Map<String, JdIopConfigInfo> configMap = configList.stream().collect(Collectors.toMap(JdIopConfigInfo::getCode, Function.identity()));
        map.keySet().forEach(key -> {
            processOne(configMap.get(key), map.get(key), key);
        });
    }
    public void processOne(JdIopConfigInfo configInfo, List<QueueMsgSupplierProductProcessInfo> infoList, String supplierCode) {

        val failArray = new ArrayList<String>();
        val succArray = new ArrayList<String>();

        TenantUtils.execute(configInfo.getTenantIds(), () -> {
            val entities = new ArrayList<BackupJdIopGoodsEntity>();
            var backups = backupSrv.getBaseMapper().selectBySkuIdIn(infoList.stream().map(QueueMsgSupplierProductProcessInfo::getSkuId).map(Long::valueOf).collect(Collectors.toList()), supplierCode);
            // supplierCode + skuId和实体的映射，生成map
            var backupMap = backups.stream().collect(Collectors.toMap(e -> e.getSupplierCode() + e.getSkuId(), item -> item));
            infoList.forEach(item -> {
                try {
                    //记录商品进度
                    entities.add(doExec(item, backupMap.get(item.getSupplier() + item.getSkuId()), configInfo));
                    if (!failArray.contains(item.getSkuId())) {
                        succArray.add(item.getSkuId());
                    }
                } catch (Exception ex) {
                    log.error("JD iop to backup error, skuId:{}", item.getSkuId(), ex);
                    failArray.add(item.getSkuId());
                    succArray.remove(item.getSkuId());
                }
            });

            var entities_list = entities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BackupJdIopGoodsEntity::getSkuId))), ArrayList::new));
            if (!entities_list.isEmpty())
                backupSrv.saveBatch(entities_list.stream().filter(item -> item.getId() == null).collect(Collectors.toList()));
            if (!entities_list.isEmpty())
                backupSrv.getBaseMapper().updateBatchByIdx(entities_list.stream().filter(item -> item.getId() != null).collect(Collectors.toList()));
        });
        //queue_msg_supplier_product_process_info只存租户1的
        if (!succArray.isEmpty())
            msgSrv.getBaseMapper().updateInstoreInfo(1, new ArrayList<>(succArray), supplierCode);
        if (!failArray.isEmpty())
            msgSrv.getBaseMapper().updateInstoreInfo(2, new ArrayList<>(failArray), supplierCode);
    }



    private BackupJdIopGoodsEntity doExec(QueueMsgSupplierProductProcessInfo item, BackupJdIopGoodsEntity backup, JdIopConfigInfo configInfo) {
        var res = JSON.parseObject(item.getInfo(), IopGetSkuPoolInfoGoodsResp.class);
        var entity = backup == null ? new BackupJdIopGoodsEntity() : BeanUtil.copyProperties(backup, BackupJdIopGoodsEntity.class);

        BeanUtil.copyProperties(res, entity);
        entity.setSupplierCode(item.getSupplier());
        entity.setUpdateTime(System.currentTimeMillis());
        if (backup == null) {
            // 生成good code
            entity.setGoodCode(cg.getProductCode(configInfo.getCode()));
            entity.setSkuId(Long.valueOf(item.getSkuId()));
            entity.setCreateTime(System.currentTimeMillis());
        }

        // 分类信息
        if (StringUtil.isNotBlank(res.getCategory())) {
            entity.setContractSkuPoolExt(res.getCategory());
        }
        entity.setImagePrefix(configInfo.getImagePrefix());

        // 如果品类扩展字段不为空，这拼接上扩展的品类字段
        fillField(res, entity);
        return entity;
    }

    private void fillField(IopGetSkuPoolInfoGoodsResp r, BackupJdIopGoodsEntity e) {
        if (!StrUtil.isBlank(e.getContractSkuPoolExt())) {
            e.setCategory(e.getCategory() + ";" + e.getContractSkuPoolExt());
        }
        e.setImagePath(e.getImagePrefix() + "/" + e.getImagePath());
        e.setCategoryAttrListStr(JSONObject.toJSONString(r.getCategoryAttrs()));
        e.setParamGroupAttrlist(JSONObject.toJSONString(r.getParamDetailJson()));

        //不一样的补充
        e.setLowestBuy(Integer.valueOf(r.getLowestBuy()));
        e.setWareInfo(r.getWareQD()); //包装清单
        e.setSpecificParam(r.getParamDetailJson()); //规格
        e.setSkuName(r.getName());//商品名称
        e.setIntroduce(r.getIntroduction());//商品详情
        e.setWarrantDesc(r.getWserve());//质保信息
        e.setSpuName(r.getPName());//spu名称
        e.setTaxRatePercentage(new BigDecimal(r.getTaxInfo()));//税率
        e.setSpuId(r.getSpuId() == null ? 0 : Long.valueOf(r.getSpuId()));//spuid

        //给3sm追加的字段
        e.setGoodsShortName(r.getCategoryName());
        //计算货期天数
        if (StringUtil.isNotBlank(r.getPromiseDate())) {
            Long daysBetween = DateUtil.between(new Date(), DateUtil.parse(r.getPromiseDate()), DateUnit.DAY);
            e.setPromiseDate(daysBetween.toString());
        }
        e.setSelfSellType(StringUtil.isBlank(r.getIsSelf()) ? 1 : Integer.parseInt(r.getIsSelf()));


        // 更新和添加统一设置成未同步状态，标志位全部重设
        e.setSynchronize((byte) 0);
        e.setValidateFlag((byte) 0);
        // 这里不要
        e.setPriceProcFlag((byte) 0);
        e.setShiftProcFlag((byte) 0);
        e.setStockProcFlag((byte) 0);
        e.setImageProcFlag((byte) 0);
        e.setCanSaleProcFlag((byte) 0);
        e.setCategoryProcFlag((byte) 0);
        e.setIsEnable(1);

        String introduct = e.getIntroduce();

        if (StrUtil.isBlank(introduct)) {
            if (StrUtil.isNotBlank(e.getBookExtInfo())) {
                try {
                    JSONObject bookExt = JSON.parseObject(e.getBookExtInfo());
                    String productFeatures = bookExt.getString("productFeatures");
                    introduct += productFeatures;
                } catch (Exception ex) {
                    log.error("jd_iop_json_ext_info_parse_error:{}", ex.getMessage());
                }
            }
        }

        e.setIntroduce(introduct);
        if (StrUtil.isNotBlank(e.getCategory())) {
            final var ca = e.getCategory().split(";");
            e.setCategory(ca[ca.length - 1]);
        }
    }
}
