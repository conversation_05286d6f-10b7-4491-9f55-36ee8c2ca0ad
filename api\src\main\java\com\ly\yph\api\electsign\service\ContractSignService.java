package com.ly.yph.api.electsign.service;

import cn.hutool.json.JSONObject;
import com.ly.yph.api.electsign.config.UserLoadSecurityClientConfig;
import com.ly.yph.api.electsign.util.ConvertNumberToUppercase;
import com.ly.yph.api.electsign.util.SecurityApiUtil;
import com.ly.yph.api.electsign.util.SecurityClientSignUtil;
import com.ly.yph.api.electsign.vo.*;
import com.ly.yph.api.honda.util.HondaHttpUtils;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.owrdandpdf.PdfContractProcessor;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.types.ParameterException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
public class ContractSignService {


    @Resource
    private UserLoadSecurityClientConfig userLoadSecurityClientConfig;

    /**
     * 创建内部用户
     * @param contractUserVo
     * @return
     */
    public JSONObject createInitUser(ContractInnerUserVo contractUserVo){
        String url = "/mp/packages/power/ty/accounts/innerAccounts/create";
        List<ContractInnerUserVo> userVoList = new ArrayList<>();
        userVoList.add(contractUserVo);
        Map<String, Object> bodyParam = new HashMap<>();
        bodyParam.put("accountInfos", userVoList);
       return createFileUpUrl(bodyParam, url);
    }

    /**
     * 创建内部组织
     * @param contractOrgVo
     * @return
     */
    public JSONObject createInitOrg(ContractInnerOrgVo contractOrgVo) {
        String url = "/mp/packages/power/ty/sign/Organs/innerOrgans/create";
        return createFileUpUrl(contractOrgVo, url);
    }

    /**
     * 创建外部用户
     * @param contractUserVo
     * @return
     */
    public JSONObject createOutUser(ContractOutUserVo contractUserVo){
        String url = "/mp/packages/power/ty/accounts/outerAccounts/create";
        return createFileUpUrl(contractUserVo, url);
    }

    /**
     * 创建外部组织
     */
    public JSONObject createOutOrg(ContractOutOrgVo contractOrgVo){
        String url = "/mp/packages/power/ty/sign/Organs/outerOrgans/create";
        return createFileUpUrl(contractOrgVo, url);
    }

    /**
     * 创建内部用户
     * @param orgId
     * @param accountId
     * @return
     */
    public JSONObject bindOutUserAndOrg(String orgId, String accountId){
        String url = "/mp/packages/power/ty/sign/Organs/outerOrgans/bindAgent";
        List<Map<String, Object>> agentList = new ArrayList<>();
        Map<String, Object> agentItem = new HashMap<>();
        agentItem.put("isDefault", 1);
        agentItem.put("accountId", accountId);
        agentList.add(agentItem);
        Map<String, Object> bodyParam = new HashMap<>();
        bodyParam.put("organizeId", orgId);
        bodyParam.put("agentList", agentList);
        return createFileUpUrl(bodyParam, url);
    }

    /**
     * 发起合同签署
     */
    public JSONObject signFlows(SystemContractRenewalEntity contractRenewal, ShopSupplier shopSupplier){
        ContractSignVo result = createSignData(contractRenewal, shopSupplier);
        String url = "/mp/packages/power/ty/sign/signFlows/create";
        JSONObject res = createFileUpUrl(result, url);
        if(res!=null){
            res.set("applyAccountId", userLoadSecurityClientConfig.getApplyAccountId());
        }
        return res;
    }

    /**
     * 发起合同签署
     */
    public JSONObject signFlows(){
        ContractSignVo result = createSignData();
        String url = "/mp/packages/power/ty/sign/signFlows/create";
        return createFileUpUrl(result, url);
    }

    public ContractSignVo createSignData(SystemContractRenewalEntity contractRenewal, ShopSupplier shopSupplier) {

        String fileId = contractRenewal.getDocFileKey();
        String fileName = contractRenewal.getContractName();

        String signAccountId = shopSupplier.getEAccountId();
        String signAuthorizationOrganizeId = shopSupplier.getEOrganizeId();
        String signContactMobile = shopSupplier.getOperatorPhone();
        String signUniqueId = shopSupplier.getOperatorIdCard();


        String applyAccountId = userLoadSecurityClientConfig.getApplyAccountId();
        String applyUniqueId = userLoadSecurityClientConfig.getApplyUniqueId();
        String applyAuthorizationOrganizeId = userLoadSecurityClientConfig.getApplyAuthorizationOrganizeId();
        String applyContactMobile = userLoadSecurityClientConfig.getApplyContactMobile();
        String applyMail = userLoadSecurityClientConfig.getApplyMail();
        String applyName = userLoadSecurityClientConfig.getApplyName();

        Date date = new Date((new Date()).getTime()+ 3*24*60*60*1000L);

        String signValidity = DateUtils.format(date, DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);


        List<ContractSignSignerVo> signers = new ArrayList<>();
        createSigner(signers, 200 ,  200,
                420, fileId, applyAccountId, applyUniqueId, 1,
                applyAuthorizationOrganizeId,applyContactMobile);

        createSigner(signers, 200, 420,
                340, fileId, signAccountId, signUniqueId, 2,
                signAuthorizationOrganizeId,signContactMobile);

        List<ContractSignDocVo> signDocs = new ArrayList<>();
        ContractSignDocVo signDoc = new ContractSignDocVo();
        signDoc.setDocFilekey(fileId);
        signDoc.setDocName(fileName);
        signDocs.add(signDoc);

        ContractSignVo body = new ContractSignVo();
        body.setBizNo(contractRenewal.getId().toString()); // 业务id
        body.setCallbackUrl(userLoadSecurityClientConfig.getCallBackUrl()); // 回调地址
        body.setComments(""); // 备注
        body.setInitiatorAccountId(applyAccountId); // 发起人id
        body.setInitiatorAccountType(1); // 发起人类型 1内部 2外部
        body.setInitiatorEmail(applyMail);
        body.setInitiatorMobile(applyContactMobile);
        body.setInitiatorName(applyName);
        body.setInitiatorOrganizeId(applyAuthorizationOrganizeId);
        body.setInitiatorUniqueId(applyUniqueId);
        body.setSignDocs(signDocs);
        body.setSignFileFormat("PDF");
        body.setSignValidity(signValidity);
        body.setSigners(signers);
        body.setSubject(fileName);

        return body;
    }

    private ContractSignVo createSignData() {

        String fileId = "$d5a68a2c-5ecd-496b-85ee-810ad9f1a9b3$*********";
        String fileName = "东风商城商家合作通则协议";

        String applyAccountId = "d34e31f6-3826-464e-9591-72f84ddad8a6";
        String applyUniqueId = "06890";
        String applyAuthorizationOrganizeId = "963fafe1-3fcc-47df-9919-307d9fa443b4";
        String applyContactMobile = "***********";
        String applyMail = "<EMAIL>";
        String applyName = "张张张";


        String signAccountId = "6329df01-fc58-4694-9444-a4fb87a5fb91";
        String signUniqueId = "07739";
        String signAuthorizationOrganizeId = "2c72470e-2eb9-479a-902c-5219b116fa10";
        String signContactMobile = "***********";

        List<ContractSignSignerVo> signers = new ArrayList<>();
        createSigner(signers, 200 ,  200,
        400, fileId, applyAccountId, applyUniqueId, 1,
                applyAuthorizationOrganizeId,applyContactMobile);

        createSigner(signers, 200, 380,
        350, fileId, signAccountId, applyUniqueId, 2,
                signAuthorizationOrganizeId,signContactMobile);



        List<ContractSignDocVo> signDocs = new ArrayList<>();
        ContractSignDocVo signDoc = new ContractSignDocVo();
        signDoc.setDocFilekey(fileId);
        signDoc.setDocName(fileName);
        signDocs.add(signDoc);

        ContractSignVo body = new ContractSignVo();
        body.setBizNo("**********"); // 业务id
        body.setCallbackUrl("https://dfmallsit.szlanyou.com/v1/eSign/callBack"); // 回调地址
        body.setComments("测试签署合同"); // 备注
        body.setInitiatorAccountId(applyAccountId); // 发起人id
        body.setInitiatorAccountType(1); // 发起人类型 1内部 2外部
        body.setInitiatorEmail(applyMail);
        body.setInitiatorMobile(applyContactMobile);
        body.setInitiatorName(applyName);
        body.setInitiatorOrganizeId(applyAuthorizationOrganizeId);
        body.setInitiatorUniqueId(applyUniqueId);
        body.setSignDocs(signDocs);
        body.setSignFileFormat("PDF");
        body.setSignValidity("2025-07-15 12:23:32");
        body.setSigners(signers);
        body.setSubject(fileName);

        return body;
    }

    private void createSigner(List<ContractSignSignerVo> signers,double  posY,double  posX,
                              double  posYY, String fileId, String accountId, String uniqueId, int accountType,
                              String authorizationOrganizeId, String contactMobile) {

        List<ContractSignPositionVo> signPosList = new ArrayList<>();
        ContractSignPositionVo signPos = new ContractSignPositionVo();
        signPos.setSignType(1);
        signPos.setSignIdentity("ORGANIZE");
        signPos.setPosY(posY);
        signPos.setPosX(posX);
        signPos.setPosPage("1");
        signPos.setEdgePosition(1);

        ContractSignPositionVo signPos1 = new ContractSignPositionVo();
        signPos1.setSignType(3);
        signPos1.setSignIdentity("ORGANIZE");
        signPos1.setPosY(posYY);
        signPos1.setPosPage("1-26");
        signPos1.setEdgePosition(2);

        signPosList.add(signPos);
        signPosList.add(signPos1);

        List<ContractSignDocDetailVo> signDocDetails = new ArrayList<>();
        ContractSignDocDetailVo signDocDetail = new ContractSignDocDetailVo();
        signDocDetail.setDocFilekey(fileId);
        signDocDetail.setSignPos(signPosList);
        signDocDetails.add(signDocDetail);

        ContractSignSignerVo signer = new ContractSignSignerVo();
        signer.setAccountId(accountId);
        signer.setAccountType(accountType);
        signer.setAuthorizationOrganizeId(authorizationOrganizeId);
        signer.setContactMobile(contactMobile);
        signer.setIsOrsign(0);
        signer.setLegalSignFlag(1);
        signer.setSignDocDetails(signDocDetails);
        signer.setSignOrder(1);
        signer.setUniqueId(uniqueId);
        signers.add(signer);
    }

    public  String upContractFile(String sourceUrl) {

        String targetUrl = userLoadSecurityClientConfig.getLoadClientUrl()+"/mp/packages/power/ty/sign/file/upload";

        // 设置边界字符串用于多部分表单
        String boundary = "----" + UUID.randomUUID().toString();

        // 打开到源文件的连接
        HttpURLConnection sourceConnection = null;
        HttpURLConnection targetConnection = null;

        try {
            // 1. 连接到源文件
            URL source = new URL(sourceUrl);
            sourceConnection = (HttpURLConnection) source.openConnection();
            sourceConnection.setRequestMethod("GET");
            sourceConnection.setConnectTimeout(15000); // 15秒连接超时
            sourceConnection.setReadTimeout(30000);    // 30秒读取超时

            String fileName = "东风商城商家合作通则协议";

            // 2. 准备上传到目标服务器
            URL target = new URL(targetUrl);
            targetConnection = (HttpURLConnection) target.openConnection();
            targetConnection.setDoOutput(true);
            targetConnection.setRequestMethod("POST");
            targetConnection.setConnectTimeout(15000); // 15秒连接超时
            // 设置请求头
            targetConnection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
            try (InputStream sourceStream = sourceConnection.getInputStream();
                OutputStream outputStream = targetConnection.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8), true)) {
                writer.append("--").append(boundary).append("\r\n");
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(fileName).append("\"\r\n");
                writer.append("Content-Type: ").append(sourceConnection.getContentType()).append("\r\n\r\n");
                writer.flush();

                byte[] buffer = new byte[8192]; // 8KB缓冲区
                int bytesRead;
                while ((bytesRead = sourceStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
                writer.append("\r\n");
                writer.append("--").append(boundary).append("--\r\n");
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(targetConnection.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }


                if(!response.toString().contains("code")){
                    log.info("调用生态聚合中心接口失败：{}", response.toString());
                    return null;
                }
                JSONObject result = new JSONObject(response.toString());
                if(result.getInt("code")==0){
                    return result.getJSONObject("data").getStr("fileKey");
                }
                log.info("调用生态聚合中心接口失败：{}", response.toString());
                return null;
            }
        }catch (Exception e){
            log.error("上传合同数据失败：{}", e);
            return null;
        }finally {
            // 确保关闭所有连接
            if (sourceConnection != null) sourceConnection.disconnect();
            if (targetConnection != null) targetConnection.disconnect();
        }
    }

    public JSONObject createFileUpUrl(Object bodyParam, String url){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vin","");
        paramMap.put("type","");
        paramMap.put("appId",userLoadSecurityClientConfig.getLoadClientAppId());
        paramMap.put("version","1.0");
        paramMap.put("source","1");
        paramMap.put("bodyParam", bodyParam);

        String reqUrl = userLoadSecurityClientConfig.getLoadClientUrl()+url;

        // 获取加签后body，可以直接使用signJsonBody发起请求
        String signJsonBody = SecurityClientSignUtil.getSignJsonBody(userLoadSecurityClientConfig, paramMap);
        log.info("调用生态聚合中心 url：{}   body： {}", reqUrl, signJsonBody);
        try{
            String resultStr = HondaHttpUtils.sendJSONPost(reqUrl, signJsonBody);
            log.info("生态聚合中心返回结果: {}", resultStr);
            if(StringUtils.isBlank(resultStr) || !resultStr.contains("code")){
                log.info("调用生态聚合中心接口失败：{}", resultStr);
                return null;
            }
            JSONObject result = new JSONObject(resultStr);
            if(result.getInt("code")==0){
                if(result.getStr("data").startsWith("[")){
                    return result.getJSONArray("data").getJSONObject(0);
                }
                return result.getJSONObject("data");
            }
            throw new ParameterException(result.getStr("msg"));
        }catch (Exception e){
            log.error("调用生态聚合中心失败: {}", e.getMessage());
            return null;
        }
    }


    public void fillWordAndToPdf(String docTemplateId) {
        ContractSignTemplateValueVo vo = new ContractSignTemplateValueVo();
        vo.setAMail("<EMAIL>");
        vo.setAName("张四");
        vo.setAPhone("18112121212");
        vo.setBeginDay("07");
        vo.setBeginMonth("07");
        vo.setBeginYear("2025");
        vo.setCreditCode("91420105MA49KPPN7F");
        vo.setDiscount("5");
        vo.setEndDay("07");
        vo.setEndMonth("07");
        vo.setEndYear("2030");
        //vo.setWayA("");
        vo.setWayB("\u25A0");
        vo.setAAddress("深圳市盐田区沙盐路 3018 号盐田现代产业\n服务中心（一期）A座 6 层 6A-2");
        vo.setSupplierName("深圳绿头实业股份有限公司");
        vo.setSupplierName2("深圳绿头实业股份有限公司");
        vo.setSupplierName3("深圳绿头实业股份有限公司");
        vo.setSettlementPeriod("60");
        vo.setSupplierAddress("深圳市盐田区沙盐路 3018 号盐田现代产业服务中心（一期）A座 6 层 6A-2");
        vo.setInvoiceBankAndAccount("中国农业银行  4344564564634534535345345");
        vo.setContractCode("LYCG-2026-07-07-ccccdewfwefw");
        vo.setFirstPerformance("50000.00");
        vo.setBondBig(ConvertNumberToUppercase.toChineseUpper(50000L));

        // 获取所有声明的字段（包括private）
        Field[] fields = ContractSignTemplateValueVo.class.getDeclaredFields();
        Map<String, String> placeholders = new HashMap<>();
        for (Field field : fields) {
            try {
                // 设置可访问private字段
                field.setAccessible(true);
                // 设置componentKey为字段名
                // 获取字段值并转换为字符串
                Object value = field.get(vo);
                placeholders.put(field.getName(), value != null ? value.toString() : "");
            } catch (IllegalAccessException e) {
                // 处理访问异常（实际生产环境应使用日志记录）
                System.err.println("Error accessing field: " + field.getName());
            }
        }
        try{

            PdfContractProcessor.replacePlaceholders(
                    "C:\\Users\\<USER>\\Desktop\\东风商城商家合作通则协议0708.pdf",
                    "C:\\Users\\<USER>\\Desktop\\东风商城商家合作通则协议070888.pdf",
                    placeholders
            );

//            WordReplaceUtil.fillWordTemplate("C:\\Users\\<USER>\\Desktop\\东风商城商家合作通则协议0708.pdf",
//                    "C:\\Users\\<USER>\\Desktop\\东风商城商家合作通则协议070888.pdf", placeholders);
        } catch (Exception e) {
            e.printStackTrace();
            // 处理访问异常（实际生产环境应使用日志记录）
        }
    }
}
