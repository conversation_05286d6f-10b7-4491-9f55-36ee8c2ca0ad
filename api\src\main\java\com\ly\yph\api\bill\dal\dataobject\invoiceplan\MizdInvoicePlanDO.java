package com.ly.yph.api.bill.dal.dataobject.invoiceplan;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 账单计划 DO
 *
 * <AUTHOR>
 */
@TableName("mizd_invoice_plan")
@KeySequence("mizd_invoice_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MizdInvoicePlanDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 支付单号 ZFDH+账单号+001(流水号)
     */
    private String payNo;
    /**
     * 支付计划状态 0：未发起1：审批中；2：审批通过；3：审批驳回
     */
    private String payPlanStatus;
    /**
     * 审批完成时间
     */
    private LocalDateTime approvedCompletedTime;
    /**
     * 账单编号
     */
    private String invoiceNumber;
    /**
     * 账单名称
     */
    private String invoiceName;
    /**
     * 支付类型
     */
    private String payType;
    /**
     * 支付主体
     */
    private String payEntity;
    /**
     * 采购经理
     */
    private String purchasingManager;
    /**
     * 付款日期
     */
    private LocalDateTime payDate;
    /**
     * 项目编号
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 合同编号
     */
    private String businessContractId;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 支付比例
     */
    private BigDecimal payPercent;
    /**
     * 付款期数
     */
    private String paymentPhases;
    /**
     * 开户行
     */
    private String bank;
    /**
     * 收款单位
     */
    private String receiveCompany;
    /**
     * 账号
     */
    private String receiveAccount;
    /**
     * 合同金额
     */
    private BigDecimal contractAmount;
    /**
     * 预算编号
     */
    private String budgetNumber;
    /**
     * 金额小写
     */
    private String moneyLowCase;
    /**
     * yes no 是否对冲
     */
    private String hedge;
    /**
     * 支付方式（电汇/承兑）
     */
    private String payMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否预付 0:非预付;1:是预付
     */
    private String paymentRroperty;

    /**
     * 内部项目标识
     */
    private Integer projectBelongModule;

    /**
     * 申请合同部门名称
     */
    private String purchaseApplyCreatorDeptName;

    /**
     * 采购经理工号
     */
    private String purchasingManagerCode;

    /**
     * 币种
     */
    private String typeOfCurrency;

    /**
     * 收款方开户行联行号
     */
    private String routingNumber;
    /**
     * 外部单号
     */
    private String externalBusinessOrderNumber;
    /**
     * 当前步骤名称
     */
    private String currentStepName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    private Integer isEnable;

    /**
     * 账单excel附件
     */
    private String billUrl;
    /**
     * 发票附件
     */
    private String invoiceUrl;
    /**
     * 验收单附件
     */
    private String checkUrl;
    /**
     * 合同附件
     */
    private String contractUrl;
    /**
     * 额外附件
     */
    private String otherUrl;
}