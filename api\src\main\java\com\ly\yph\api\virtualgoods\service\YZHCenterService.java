package com.ly.yph.api.virtualgoods.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.yph.api.order.entity.ShopPurchaseOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.service.ShopPurchaseOrderService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.virtualgoods.dto.*;
import com.ly.yph.api.virtualgoods.entity.ShopVirtualPurchaseExtended;
import com.ly.yph.api.virtualgoods.utils.YZHConnectHelper;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.util.RRException;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年11月18日
 */
@Slf4j
@Service
public class YZHCenterService {

    @Resource
    YZHCenterService self;
    @Resource
    YZHConnectHelper connectHelper;

    @Resource
    ShopPurchaseOrderService purchaseOrderService;

    @Resource
    ShopPurchaseSubOrderService subOrderService;

    @Resource
    ShopVirtualPurchaseExtendedService virtualPurchaseExtendedService;

    @Resource
    ShopPurchaseSubOrderDetailService subOrderDetailService;


    private static final String getVirtualGoodsSku = "/open/api/goods/virtual/getVirtualGoodsSku";
    private static final String queryVirtualGoodsShelvesList = "/open/api/goods/virtual/queryVirtualGoodsShelvesList";
    private static final String submit = "/open/api/virtualOrder/submit";
    private static final String queryVirtualOrderDetail = "/open/api/virtualOrder/queryVirtualOrderDetail";
    private static final String queryVirtualOrderCardPage = "/open/api/virtualOrder/queryVirtualOrderCardPage";


    /**
     * 获取虚拟商品详情信息
     *
     * @param goodsInfoParam
     * @return
     */
    public List<YZHGoodsInfoDto> queryGoodsInfo(YZHGoodInfoParamDto goodsInfoParam) {
        List<YZHGoodsInfoDto> goodsInfoList = connectHelper.doPost(getVirtualGoodsSku, goodsInfoParam, "获取虚拟商品详情信息", YZHGoodsInfoDto.class);
        //获取商品分类
        for (YZHGoodsInfoDto yzhGoodsInfoDto : goodsInfoList) {
            String categoryCode = yzhGoodsInfoDto.getVirtualGoodType();
            if("2001".equals(categoryCode)){
                yzhGoodsInfoDto.setGoodsType(1);
            }else if("2002".equals(categoryCode)){
                yzhGoodsInfoDto.setGoodsType(2);
            }else if("2004".equals(categoryCode)){
                yzhGoodsInfoDto.setGoodsType(2);
            }
        }
        return goodsInfoList;
    }

    /**
     * 获取商品上下架状态
     *
     * @param goodsSkuCode
     * @return
     */
    public Map<String, Boolean> queryGoodsStatus(List<String> goodsSkuCode) {
        if (CollUtil.isEmpty(goodsSkuCode)) {
            return MapUtil.empty();
        }
        List<YZHGoodsStatusDto> goodsInfoList = Lists.newArrayList();
        //云中鹤最大要求100 只能循环调用
        Lists.partition(goodsSkuCode, 100).forEach(item -> {
            List<YZHGoodsStatusDto> itemGoodsStatus = connectHelper.doPost(queryVirtualGoodsShelvesList, MapUtil.builder("goodsSkuCode", item).build(), "获取虚拟商品上下架", YZHGoodsStatusDto.class);
            goodsInfoList.addAll(itemGoodsStatus);
        });
        return goodsInfoList.stream().collect(Collectors.toMap(YZHGoodsStatusDto::getGoodsSkuCode, item -> item.getShelvesStatus() == 1001));
    }

    public YZHOrderInfoDto submit(YZHOrderParamDto orderParamDto) {
//        List<YZHOrderParamDto.GoodsInfo> goodsList = orderParamDto.getGoodsList();
        //目前只支持一个订单一个商品
//        String sku = goodsList.get(0).getGoodsSkuCode();
//        Double price = goodsList.get(0).getSellPrice();
//        YZHGoodInfoParamDto goodsInfoParamDto = new YZHGoodInfoParamDto();
//        goodsInfoParamDto.setGoodsSkuCode(sku);
//        List<YZHGoodsInfoDto> goodsInfos = self.queryGoodsInfo(goodsInfoParamDto);
//        Double sellPrice = goodsInfos.get(0).getSellPrice();
//        if (Double.compare(price, sellPrice) != 0) {
//            //判断价格不一致 订单异常，不允许提交
//            throw new RRException("商品价格变动，请刷新购物车列表，返回重新下单");
//        }
        YZHOrderInfoDto orderInfo = connectHelper.doPostObj(submit, orderParamDto, "提交虚拟商品订单", YZHOrderInfoDto.class);
        //todo 云中鹤返回的订单信息，我看后续查询订单都是使用的’虚拟商品订单号’ 应该是只用这个订单号保存起来  orderInfo.getVirtualOrderCode
        return orderInfo;
    }

    /**
     * 直充类调用该接口，卡密只用获取卡密之后业务就完了，不需要关心状态
     *
     * @param virtualOrderCode
     * @return
     */
    public YZHOrderDetailDto queryOrderInfo(String virtualOrderCode) {
        YZHOrderDetailDto yzhOrderDetailDto = connectHelper.doPostObj(queryVirtualOrderDetail, MapUtil.builder("virtualOrderCode", virtualOrderCode).build(), "获取虚拟商品订单详情", YZHOrderDetailDto.class);
        //todo 如果需要获取云中鹤的订单信息可以调用这个接口，如果只需要订单状态，就用queryOrderInfoBatch
        return yzhOrderDetailDto;
    }

    /**
     *      * 1、充值中
     *      * 2、充值完成
     *      * 3、充值失败  （卡密发放失败）
     * @param virtualOrderCode
     * @return
     */
    public Map<String, Integer> queryOrderInfoBatch(List<String> virtualOrderCode) {
        if (CollUtil.isEmpty(virtualOrderCode)) {
            return Maps.newHashMap();
        }
        Map<String, Integer> orderStateMap = new HashMap<>();
        for (String item : virtualOrderCode) {
            YZHOrderDetailDto yzhOrderDetailDto = connectHelper.doPostObj(queryVirtualOrderDetail, MapUtil.builder("virtualOrderCode", item).build(), "获取虚拟商品订单详情", YZHOrderDetailDto.class);
            if (yzhOrderDetailDto.getOrderStatus() == 1013) {
                orderStateMap.put(item, 1);
            } else if (yzhOrderDetailDto.getOrderStatus() == 1009 || yzhOrderDetailDto.getOrderStatus() == 1014) {
                orderStateMap.put(item, 2);
            } else if (yzhOrderDetailDto.getOrderStatus() == 1004) {
                orderStateMap.put(item, 3);
            }
        }
        return orderStateMap;
    }

    public List<YZHCardOrderDetailDto> queryCardOrderInfo(String virtualOrderCode) {
        YZHOrderPageDto yzhOrderDetailDto = connectHelper.doPostObj(queryVirtualOrderCardPage, MapUtil.builder("virtualOrderCode", virtualOrderCode).build(), "获取虚拟商品订单卡密列表", YZHOrderPageDto.class);
        if (CollUtil.isEmpty(yzhOrderDetailDto.getVirtualOrderCardInfoList())) {
            return Lists.newArrayList();
        }
        return yzhOrderDetailDto.getVirtualOrderCardInfoList();
    }

    public void submitYzhVirtualOrder(String orderNumber) {
        ShopPurchaseSubOrder subOrder = subOrderService.getOne(new LambdaQueryWrapperX<ShopPurchaseSubOrder>().eq(ShopPurchaseSubOrder::getOrderNumber, orderNumber));
        ShopPurchaseOrder purchaseOrder = purchaseOrderService.getOne(new LambdaQueryWrapperX<ShopPurchaseOrder>().eq(ShopPurchaseOrder::getPurchaseNumber, subOrder.getPurchaseNumber()));
        ShopVirtualPurchaseExtended virtualPurchaseExtended = virtualPurchaseExtendedService.getOne(new LambdaQueryWrapperX<ShopVirtualPurchaseExtended>().eq(ShopVirtualPurchaseExtended::getOrderNumber, orderNumber));
        List<ShopPurchaseSubOrderDetail> subOrderDetails = subOrderDetailService.getDetailByOrderNumber(orderNumber);
        YZHOrderParamDto yZHOrderParamDto = new YZHOrderParamDto();
        yZHOrderParamDto.setTparOrderCode(subOrder.getOrderNumber());
        yZHOrderParamDto.setTarget(virtualPurchaseExtended.getRechargeAccount());
        yZHOrderParamDto.setAccount(purchaseOrder.getApplyUserPhone());
        List<YZHOrderParamDto.GoodsInfo> goodsList = new ArrayList<>();
        YZHOrderParamDto.GoodsInfo goodsInfo = new YZHOrderParamDto.GoodsInfo();
        //每次只能下单一个SKU,所以取第一条就好
        goodsInfo.setGoodsSkuCode(subOrderDetails.get(0).getGoodsSku());
        goodsInfo.setNum(subOrderDetails.get(0).getConfirmNum().intValue());
        goodsInfo.setSellPrice(subOrderDetails.get(0).getSupplierUnitPriceTax().doubleValue());
        goodsList.add(goodsInfo);
        yZHOrderParamDto.setGoodsList(goodsList);
        YZHOrderInfoDto submit = self.submit(yZHOrderParamDto);
        if (submit != null) {
            String virtualOrderCode = submit.getVirtualOrderCode();
            //修改商城订单表的供应商订单号
            UpdateWrapper<ShopPurchaseSubOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ShopPurchaseSubOrder::getOrderNumber, orderNumber).set(ShopPurchaseSubOrder::getSupplierOrderNumber, virtualOrderCode);
            subOrderService.update(updateWrapper);
        }
    }
}
