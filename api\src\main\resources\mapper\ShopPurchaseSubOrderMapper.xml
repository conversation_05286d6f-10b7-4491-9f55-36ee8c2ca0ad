<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderMapper">
    <resultMap type="com.ly.yph.api.order.entity.ShopPurchaseSubOrder" id="ShopPurchaseSubOrderMap">
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <result property="supplierOrderNumber" column="supplier_order_number" jdbcType="VARCHAR"/>
        <result property="purchaseNumber" column="purchase_number" jdbcType="VARCHAR"/>
        <result property="orderPriceTax" column="order_price_tax" jdbcType="VARCHAR"/>
        <result property="orderPriceNaked" column="order_price_naked" jdbcType="VARCHAR"/>
        <result property="supplierOrderPriceTax" column="supplier_order_price_tax" jdbcType="VARCHAR"/>
        <result property="supplierOrderPriceNaked" column="supplier_order_price_naked" jdbcType="VARCHAR"/>
        <result property="orderFreightPrice" column="order_freight_price" jdbcType="VARCHAR"/>
        <result property="freightType" column="freight_type" jdbcType="VARCHAR"/>
        <result property="orderPayMoney" column="order_pay_money" jdbcType="VARCHAR"/>
        <result property="orderPayIntegral" column="order_pay_integral" jdbcType="VARCHAR"/>
        <result property="orderState" column="order_state" jdbcType="INTEGER"/>
        <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="finishedTime" column="finished_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="orderSapNumber" column="order_sap_number" jdbcType="VARCHAR"/>
        <result property="orderModel" column="order_model" jdbcType="INTEGER"/>
        <result property="supplierDataSource" column="supplier_data_source" jdbcType="INTEGER"/>
        <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
        <result property="isEnable" column="is_enable" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isPlatformReconciliation" column="is_platform_reconciliation" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="ShopYflOrderVo" type="com.ly.yph.api.order.vo.ShopYflOrderVo">
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <result property="supplierOrderNumber" column="supplier_order_number" jdbcType="VARCHAR"/>
        <result property="purchaseNumber" column="purchase_number" jdbcType="VARCHAR"/>
        <result property="activityId" column="activity_id" jdbcType="VARCHAR"/>
        <result property="orderPriceTax" column="order_price_tax" jdbcType="VARCHAR"/>
        <result property="supplierOrderPriceTax" column="supplier_order_price_tax" jdbcType="VARCHAR"/>
        <result property="orderFreightPrice" column="order_freight_price" jdbcType="VARCHAR"/>
        <result property="freightType" column="freight_type" jdbcType="VARCHAR"/>
        <result property="orderState" column="order_state" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="transformersDistrictName" column="transformers_district_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="orderPayIntegral" column="order_pay_integral" jdbcType="VARCHAR"/>
        <result property="orderPayMoney" column="order_pay_money" jdbcType="VARCHAR"/>
        <collection property="shopYflOrderDetailVo" ofType="com.ly.yph.api.order.vo.ShopYflOrderDetailVo"
                    javaType="arraylist" column="order_number"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryYflOrderDetailList"/>
    </resultMap>

    <resultMap id="ShopPurchaseSubOrderInfo" type="com.ly.yph.api.order.entity.ShopPurchaseSubOrder">
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <collection property="subOrderDetailList" ofType="com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail"
                    javaType="arraylist" column="order_number"
                    select="com.ly.yph.api.order.mapper.ShopPurchaseSubOrderDetailMapper.queryByOrderNumber"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ShopPurchaseSubOrderMap">
        select order_id,
        order_number,
        supplier_order_number,
        purchase_number,
        order_price_tax,
        order_price_naked,
        supplier_order_price_tax,
        supplier_order_price_naked,
        order_freight_price,
        freight_type,
        order_state,
        supplier_code,
        supplier_name,
        finished_time,
        remark,
        fail_reason,
        order_sap_number,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time
        from shop_purchase_sub_order
        where order_id = #{orderId}
        and is_enable = '1'
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ShopPurchaseSubOrderMap">
        select order_id,
        order_number,
        supplier_order_number,
        purchase_number,
        order_price_tax,
        order_price_naked,
        supplier_order_price_tax,
        supplier_order_price_naked,
        order_freight_price,
        order_state,
        supplier_code,
        supplier_name,
        finished_time,
        remark,
        fail_reason,
        order_sap_number,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time
        from shop_purchase_sub_order
        where is_enable = '1'
        <if test="orderId != null and orderId != ''">
            and order_id = #{orderId}
        </if>
        <if test="orderNumber != null and orderNumber != ''">
            and order_number = #{orderNumber}
        </if>
        <if test="supplierOrderNumber != null and supplierOrderNumber != ''">
            and supplier_order_number = #{supplierOrderNumber}
        </if>
        <if test="purchaseNumber != null and purchaseNumber != ''">
            and purchase_number = #{purchaseNumber}
        </if>
        <if test="orderPriceTax != null">
            and order_price_tax = #{orderPriceTax}
        </if>
        <if test="orderPriceNaked != null">
            and order_price_naked = #{orderPriceNaked}
        </if>
        <if test="supplierOrderPriceTax != null">
            and supplier_order_price_tax = #{supplierOrderPriceTax}
        </if>
        <if test="supplierOrderPriceNaked != null">
            and supplier_order_price_naked = #{supplierOrderPriceNaked}
        </if>
        <if test="orderFreightPrice != null">
            and order_freight_price = #{orderFreightPrice}
        </if>
        <if test="orderState != null">
            and order_state = #{orderState}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and supplier_code = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and supplier_name = #{supplierName}
        </if>
        <if test="finishedTime != null">
            and finished_time = #{finishedTime}
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}
        </if>
        <if test="failReason != null and failReason != ''">
            and fail_reason = #{failReason}
        </if>
        <if test="orderSapNumber != null and orderSapNumber != ''">
            and order_sap_number = #{orderSapNumber}
        </if>
        <if test="isEnable != null">
            and is_enable = #{isEnable}
        </if>
        <if test="creator != null and creator != ''">
            and creator = #{creator}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="modifier != null and modifier != ''">
            and modifier = #{modifier}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <select id="queryConfirmSubOrder" resultMap="ShopPurchaseSubOrderMap">
        select order_id,
               order_number,
               supplier_order_number,
               purchase_number,
               contract_number,
               supplier_code,
               supplier_name,
               order_price_tax,
               order_price_naked,
               order_freight_price,
               freight_type,
               supplier_order_price_naked,
               supplier_order_price_tax,
               order_pay_integral,
               order_pay_money,
               order_model,
               order_state,
               supplier_data_source,
               create_time,
               organization_id,
               is_platform_reconciliation
        from shop_purchase_sub_order
        where purchase_number = #{purchaseNumber}
          and is_enable = '1'
          and order_state not in (-1,0);
    </select>

    <select id="queryConfirmSubOrderByOrderNumber" resultMap="ShopPurchaseSubOrderMap">
        SELECT order_number,
               supplier_order_number,
               purchase_number,
               supplier_code,
               supplier_name,
               order_price_tax,
               order_price_naked,
               order_freight_price,
               supplier_order_price_naked,
               supplier_order_price_tax,
               order_state,
               tenant_id,
               fail_reason
        FROM shop_purchase_sub_order
        WHERE order_number = #{orderNumber}
        and is_enable = '1'
    </select>

    <update id="updateOrderState">
        update shop_purchase_sub_order
        set order_state = #{state}
        where order_id = #{orderId}
          and is_enable = '1'
    </update>
    <update id="updateOrderStateBatch">
        <foreach collection="orderIds" item="item" index="index" separator=";">
            update shop_purchase_sub_order
            set order_state = #{state}
            where order_id = #{item}
              and is_enable = '1'
        </foreach>
    </update>
    <select id="purchaseExecutionReport" resultType="com.ly.yph.api.order.vo.PurchaseExecutionVo">
        select p.purchase_id AS purchaserId,
        p.purchase_number AS purchaseNumber,
        p.apply_user_id AS applyUserId,
        p.apply_user_name AS applyUserName,
        p.apply_dept_id AS applyDeptId,
        p.apply_dept_name AS applyDeptName,
        p.create_time AS applyTime,
        CASE
        WHEN p.purchase_state = 0 THEN "已取消"
        WHEN p.purchase_state = 10 THEN "审批中"
        WHEN p.purchase_state = 20 THEN "审批驳回"
        WHEN p.purchase_state = 30 THEN "审批通过"
        ELSE "未知" END AS purchaseState,
        CONCAT(ROUND((d.goods_unit_price_tax - d.supplier_unit_price_tax) / d.supplier_unit_price_tax * 100, 2), '',
        '%') AS pricing,
        CASE
        WHEN o.order_state = -1 THEN "订单失败"
        WHEN o.order_state = 0 THEN "已取消"
        WHEN o.order_state = 10 THEN "已提交"
        WHEN o.order_state = 20 THEN "待发货"
        WHEN o.order_state = 30 THEN "待收货"
        WHEN o.order_state = 40 THEN "收货完成"
        WHEN o.order_state = 45 THEN "部分退货"
        WHEN o.order_state = 50 THEN "全部退货"
        ELSE "未知" END AS orderState,
        o.order_number AS orderNumber,
        p.budget_code AS budgetCode,
        p.company_name AS companyName,
        P.company_code AS companyCode,
        o.supplier_code AS supplierCode,
        o.supplier_name AS supplierName,
        d.goods_code AS goodsCode,
        d.goods_name AS goodsName,
        s.brand_name AS brandName,
        s.goods_desc AS goodsDescription,
        t.goods_spec AS goodsSpec,
        s.goods_id AS goodsId,
        s.materials_code AS materialsCode,
        s.sale_unit AS saleUnit,
        s.stand_category_name AS standCategoryName,
        d.goods_unit_price_naked AS supplierUnitPriceTax,
        d.goods_unit_price_tax AS goodsUnitPriceTax,
        d.supplier_unit_price_tax AS goodsUnitPriceNaked,
        CONCAT(d.tax_rate, '%') AS taxRate,
        d.confirm_num AS goodsNum,
        CASE
        WHEN d.order_detail_state = -1 THEN "订单失败"
        WHEN d.order_detail_state = 0 THEN "已取消"
        WHEN d.order_detail_state = 10 THEN "已提交"
        WHEN d.order_detail_state = 20 THEN "待发货"
        WHEN d.order_detail_state = 30 THEN "待收货"
        WHEN d.order_detail_state = 40 THEN "收货完成"
        ELSE "未知" END AS orderDetailState,
        o.order_price_tax AS orderPriceTax,
        o.order_sap_number AS orderSapNumber,
        p.purchase_name AS purchaseName,
        P.purchase_comment AS purchaseComment,
        (IFNULL(a.receiving_num, 0)) AS receivedNum,
        (IFNULL(d.confirm_num - a.receiving_num, d.confirm_num)) AS unReceivedNum,
        p.remark AS purchaseMark
        from shop_purchase_order p
        left join shop_purchase_sub_order o on o.purchase_number = p.purchase_number
        left join shop_purchase_sub_order_detail d on d.order_id = o.order_id
        left join shop_goods s on s.goods_code = d.goods_code
        left join shop_goods_detail t on t.goods_code = s.goods_code
        left join shop_delivery h on h.order_id = o.order_id
        left join shop_delivery_detail a on a.delivery_id = h.id
        where p.is_enable = '1'
        and o.is_enable = '1'
        and d.is_enable = '1'
        and s.is_enable = '1'
        and t.is_enable = '1'
        and h.is_enable = '1'
        and a.is_enable = '1'
        <if test="query.startTime != null and query.startTime != ''and query.endTime != null and query.endTime != ''">
            AND DATE_FORMAT(p.create_time, "%Y-%m-%d%H:%i:%S") BETWEEN DATE_FORMAT(#{query.startTime}, "%Y-%m-%d%H:%i:%S") and
            DATE_FORMAT(#{query.endTime}, "%Y-%m-%d%H:%i:%S")
        </if>
        <if test="query.purchaseNumber != null  and query.purchaseNumber != ''">
            AND p.purchase_number like concat('%', #{query.purchaseNumber}, '%')
        </if>
        <if test="query.orderNumber != null  and query.orderNumber != ''">
            AND o.order_number like concat('%', #{query.orderNumber}, '%')
        </if>
        <if test="query.budgetCode != null  and query.budgetCode != ''">
            AND p.budget_code like concat('%', #{query.budgetCode}, '%')
        </if>
        <!--            <if test="query.auditStates != null and query.auditStates != ''">-->
        <!--                and p.audit_state in-->
        <!--                <foreach collection="query.auditStates.split(',')" item="item" open="(" separator="," close=")">-->
        <!--                    #{item}-->
        <!--                </foreach>-->
        <!--            </if>-->
        <if test="query.purchaseState != null">
            AND p.purchase_state = #{query.purchaseState}
        </if>
        <if test="query.applyUserName != null  and query.applyUserName != ''">
            AND p.apply_user_name like concat('%', #{query.applyUserName}, '%')
        </if>
        <if test="query.applyDeptName != null  and query.applyDeptName != ''">
            AND p.apply_dept_name like concat('%', #{query.applyDeptName}, '%')
        </if>
        <if test="query.supplierCode != null  and query.supplierCode != ''">
            AND o.supplier_code = #{query.supplierCode}
        </if>
        <if test="query.supplierName != null  and query.supplierName != ''">
            AND o.supplier_name like concat('%', #{query.supplierName}, '%')
        </if>
        <if test="query.goodsCode != null  and query.goodsCode != ''">
            AND d.goods_code like concat('%', #{query.goodsCode}, '%')
        </if>
        <if test="query.goodsName != null  and query.goodsName != ''">
            AND d.goods_name like concat('%', #{query.goodsName}, '%')
        </if>
        <if test="query.companyCode != null  and query.companyCode != ''">
            AND P.company_code = #{query.companyCode}
        </if>
        <if test="query.goodsInfo != null  and query.goodsInfo != ''">
            and concat(IFNULL(s.brand_name, ''), IFNULL(t.goods_spec, '')
            , IFNULL(s.materials_code, '')) like concat('%', #{query.goodsInfo}, '%')
        </if>
        ORDER BY o.create_time DESC
    </select>

    <select id="querySubOrderVo" resultType="com.ly.yph.api.order.vo.ShopPurchaseSubOrderVo">
        SELECT
            t.order_id,
            t.order_number,
            t.order_model,
            t.supplier_order_number,
            t.purchase_number,
            t.contract_number,
            p.other_relation_number,
            p.apply_user_id,
            p.apply_emp_code,
            p.apply_user_name,
            p.apply_dept_id,
            p.apply_dept_name,
            p.budget_id,
            p.invoice_type,
            d.label invoice_type_name,
            p.activity_id,
            t.order_price_tax,
            t.order_price_naked,
            t.order_freight_price,
            t.freight_type,
            t.order_pay_integral,
            t.order_pay_money,
            t.order_state,
            t.supplier_code,
            t.supplier_name,
            s.supplier_type,
            t.supplier_data_source,
            t.remark,
            t.create_time,
            IF( t.is_company_store = 0, a.address_name, ca.qpc_address_name )as address_user_name,
            IF( t.is_company_store = 0, a.mob_phone, ca.qpc_mob_phone ) as  mobPhone,
            p.company_code,
            p.company_name,
            p.order_label,
            t.is_company_store,
            i.invoice_subject,
            CONCAT(
                    IF( t.is_company_store = 0, A.province, ca.qpc_province ),
                    IF	( t.is_company_store = 0, A.city, ca.qpc_city ),
                    IF	( t.is_company_store = 0, A.district, ca.qpc_district ),
                    IF	( t.is_company_store = 0, A.address, ca.qpc_address )
                )  as address_info,
            t.sale_client
        FROM shop_purchase_sub_order t
        LEFT JOIN shop_purchase_order p ON p.purchase_number = t.purchase_number
        LEFT join shop_supplier s ON s.supplier_code=t.supplier_code AND s.tenant_id=t.tenant_id
        LEFT JOIN shop_order_invoice i ON i.purchase_id = p.purchase_id
        LEFT JOIN shop_order_address a ON a.purchase_id = p.purchase_id
        LEFT JOIN company_store_order_address ca ON ca.order_number = t.order_number
        LEFT join system_dict_data d on d.dict_type = 'A010' AND d.value = p.invoice_type
        WHERE t.order_number = #{orderNumber}
        and t.is_enable = '1'
    </select>

    <select id="getPhoneByOrderId" resultType="java.lang.String">
        select a.mob_phone
        from shop_purchase_sub_order o
        left join shop_purchase_order p on p.purchase_number = o.purchase_number
        left join shop_order_address a on a.purchase_id = p.purchase_id
        where o.order_id = #{orderId}
        and o.is_enable = '1'
    </select>

    <select id="getStorePhoneByOrderId" resultType="java.lang.String">
        select a.qpc_mob_phone
        from shop_purchase_sub_order o
                 left join company_store_order_address a on o.order_number = a.order_number
        where o.order_id = #{orderId}
          and o.is_enable = '1'
    </select>

    <select id="queryYflSubOrderPage" resultMap="ShopYflOrderVo">
        SELECT
            o.order_number,
            o.supplier_order_number,
            o.purchase_number,
            p.activity_id,
            o.create_time,
            o.supplier_name,
            o.order_freight_price,
            o.freight_type,
            o.supplier_order_price_tax,
            o.order_price_tax,
            o.order_pay_integral,
            o.order_pay_money,
            o.order_state,
            o.order_after_sale_state,
            d.transformers_district_name,o.order_model
        FROM
            shop_purchase_sub_order o
        LEFT JOIN shop_purchase_order p ON p.purchase_number = o.purchase_number
        INNER JOIN system_activity a ON a.id = p.activity_id
        INNER JOIN transformers_district d ON d.transformers_district_code = a.activity_type
        where o.creator = #{queryDto.username}
        <if test="queryDto.orderState != null">
            AND o.order_state = #{queryDto.orderState}
        </if>
        ORDER BY o.create_time DESC
    </select>

    <select id="queryYflSubOrder" resultMap="ShopYflOrderVo">
        SELECT
            o.order_id,
            o.order_number,
            o.supplier_order_number,
            p.purchase_id,
            p.purchase_number,
            p.activity_id,
            o.create_time,
            o.supplier_name,
            o.order_freight_price,
            o.freight_type,
            o.supplier_order_price_tax,
            o.order_price_tax,
            o.order_pay_integral,
            o.order_pay_money,
            o.order_state,
            o.order_after_sale_state,
            d.transformers_district_name,
            s.after_sales_people,
            s.pre_sales_people,
            o.supplier_code,o.order_model
        FROM
            shop_purchase_sub_order o
        LEFT JOIN shop_purchase_order p ON p.purchase_number = o.purchase_number
        INNER JOIN system_activity a ON a.id = p.activity_id
        INNER JOIN transformers_district d ON d.transformers_district_code = a.activity_type
        LEFT JOIN shop_supplier s ON s.supplier_code = o.supplier_code
        where o.order_number = #{orderNumber}
    </select>

    <select id="queryYflSubOrderStateCount" resultType="map">
        SELECT
            order_state,
            COUNT( 1 ) AS `count`
        FROM
            shop_purchase_sub_order
        WHERE
            creator = #{userName}
        GROUP BY
            order_state
    </select>
    <select id="queryExcelList" resultType="com.ly.yph.api.system.dto.OrderExportExcelVO">
    SELECT
    @i:=@i+1 AS indexNum ,
	so.order_number,
	so.purchase_number,
	so.supplier_order_number,
	o.organization_name AS companyName,
	o.organization_short_name,
	s.invoice_subject,
	CASE
		WHEN po.invoice_type = 0 THEN
		"增值税普通发票"
		WHEN po.invoice_type = 1 THEN
		"增值税专用发票	"
		ELSE "未知"
	END AS invoiceTypeName,
	po.apply_user_name,
	po.apply_emp_code,
	so.create_time,
	so.supplier_name,
	po.purchase_goods_number AS goodsNum,
	a.activity_name,
	a.activity_code,
	so.order_price_tax + so.order_freight_price AS orderPriceTax,
	so.supplier_code,
	soa.address_name,
CASE
		WHEN so.order_state = - 1 THEN
		"订单失败"
		WHEN so.order_state = 0 THEN
		"已取消"
		WHEN so.order_state = 10 THEN
		"已提交"
		WHEN so.order_state = 20 THEN
		"待发货"
		WHEN so.order_state = 30 THEN
		"待收货"
		WHEN so.order_state = 40 THEN
		"收货完成"
		WHEN so.order_state = 45 THEN
		"部分退货"
		WHEN so.order_state = 50 THEN
		"全部退货"
		ELSE "未知"
	END AS orderStateName,
	so.order_pay_integral,
	so.order_pay_money,
	so.order_price_tax as goodsPriceTax,
	so.order_subsidy_freight,
	so.order_freight_price,
	IFNULL(sr.total_return ,0) as returnAllPriceTax,
	IFNULL(sr.return_freight ,0) as returnFreight,
	sr.return_amount_time
    FROM
	shop_purchase_sub_order so
	LEFT JOIN shop_purchase_order po ON so.purchase_number = po.purchase_number
	AND po.is_enable = 1
	LEFT JOIN system_activity a ON a.id = po.activity_id
	AND a.is_enable = 1
	LEFT JOIN system_organization_purchase_contract o ON o.organization_code = a.company_code
	AND o.tenant_id = a.tenant_id
	AND o.is_enable = 1
	LEFT JOIN shop_order_invoice s ON s.purchase_id = po.purchase_id
	AND s.is_enable = 1
	LEFT JOIN shop_order_address soa ON soa.purchase_id = po.purchase_id
	AND soa.is_enable = 1
	LEFT JOIN (	select SUM(return_all_price_tax + return_all_money_tax + return_freight) AS total_return ,
	 SUM(return_freight) AS return_freight,
	 order_id ,
	 GROUP_CONCAT( return_amount_time ) AS return_amount_time
	    from shop_return where return_type = 1 and return_state in (1,3) and is_enable = 1 GROUP BY order_id) sr on sr.order_id = so.order_id
    , (SELECT @i:=0) AS itable
    WHERE
	so.is_enable = 1
	AND so.order_state in (20,30,40,45)
	AND a.activity_code = #{activityCode} ;
    </select>

    <select id="queryPayOrder" resultType="com.ly.yph.api.system.vo.PayOrderVo">
      SELECT
	poe.NO,
	payo.merchant_order_id
    FROM
	pay_order_extension poe
	LEFT JOIN pay_order payo ON poe.order_id = payo.id
    WHERE
	poe.STATUS = 10
	AND payo.create_time &gt;= #{createTime}
    </select>

    <select id="queryPayOrderByOrderId" resultType="java.lang.Long">
        SELECT
            CASE
                WHEN
                    p.id IS NULL THEN
                    ( SELECT pay.id FROM pay_order pay WHERE pay.merchant_order_id = so.purchase_number and  pay.status = 10  ) ELSE p.id
                END NO
        FROM
            shop_purchase_sub_order so
            LEFT JOIN shop_purchase_order po ON so.purchase_number = po.purchase_number
            LEFT JOIN pay_order p ON p.merchant_order_id = so.order_number and  p.status = 10
        WHERE
	    so.order_id = #{orderId}
    </select>

    <select id="queryDsPurchaseNumber" resultType="string">
        SELECT
            DISTINCT o.purchase_number
        FROM shop_purchase_sub_order o
                 LEFT JOIN shop_supplier s ON s.supplier_code = o.supplier_code
        WHERE o.purchase_number = #{purchaseNumber} AND o.supplier_data_source != #{srmDataSource} AND s.tenant_id = 1
    </select>

    <update id="updateOrderSupplierOrderNumber">
        update shop_purchase_sub_order
        set supplier_order_number = #{supplierOrderNumber}
        where order_id = #{orderId}
          and is_enable = '1'
    </update>

    <select id="queryActivityOrderVo" resultType="com.ly.yph.api.system.dto.OrderExportExcelVO">
        select
        so.order_number,
        so.purchase_number,
        so.supplier_order_number,
        so.order_freight_price,
        so.order_subsidy_freight,
        so.freight_type
        from shop_purchase_sub_order so
        LEFT JOIN shop_purchase_order po ON so.purchase_number = po.purchase_number AND po.is_enable = 1
        LEFT JOIN system_activity a ON a.id = po.activity_id AND a.is_enable = 1
        where a.activity_code =#{query.activityCode}  and so.is_enable = 1 and so.order_state in (20,30,40,45)
        <if test="query.checkedStartTime != null and query.checkedStartTime !=''">
            and so.create_time &gt;= #{query.checkedStartTime}
        </if>
	    <if test="query.checkedEndTime !=null and query.checkedEndTime !=''">
            and so.create_time &lt;= #{query.checkedEndTime}
        </if>
    </select>

    <select id="querySrmApplyInfo" resultType="com.ly.yph.api.order.vo.SrmApplyInfoVo">
        select
            P.purchase_number,
            O.srm_apply_id,
            O.contract_number,
            C.contract_name,
            O.supplier_name,
            P.srm_project_code,
            P.srm_project_name,
            P.budget_code,
            P.srm_demander,
            u.nickname as purchase_manager_number,
            P.demand_desc,
            P.pkg_code,
            P.sub_budget_code as sub_budget_number
        from shop_purchase_order p
         left join shop_purchase_sub_order o on o.purchase_number = p.purchase_number
         left join shop_srm_contract c on c.contract_number = o.contract_number
         left join system_users u on u.username = P.purchase_manager_number
        where p.purchase_number = #{purchaseNumber}
        GROUP BY P.purchase_number
    </select>

    <select id="getPurchaseNaked" resultType="com.ly.yph.api.order.entity.ShopPurchaseOrder">
        select sum(order_price_naked) AS purchaseGoodsPriceNaked,purchase_number from  shop_purchase_sub_order group by purchase_number ;
    </select>
    <select id="getAllSubOrder" resultType="com.ly.yph.api.order.entity.ShopPurchaseSubOrder">
        select
        order_id,
        order_number,
        supplier_order_number,
        purchase_number,
        order_price_tax,
        order_price_naked,
        supplier_order_price_tax,
        supplier_order_price_naked,
        order_freight_price,
        freight_type,
        order_pay_integral,
        order_pay_money,
        order_subsidy_freight,
        order_after_sale_state,
        order_state,
        supplier_code,
        supplier_name,
        supplier_data_source,
        finished_time,
        remark,
        fail_reason,
        order_sap_number,
        order_model,
        contract_number,
        pricing_mode,
        is_pre_pay,
        need_settle_amount,
        settle_state,
        settle_amount,
        is_enable,
        creator,
        create_time,
        modifier,
        update_time,
        tenant_id,
        organization_id,
        srm_apply_id,
        supplier_confirm_state,
        is_platform_reconciliation,
        is_company_store
        from shop_purchase_sub_order where  purchase_number = #{purchaseNumber} and is_enable=1 ;
    </select>

    <update id="fixNakedPrice">
        <foreach collection="list" item="item" separator=";">
            update shop_purchase_order set purchase_goods_price_naked=#{item.purchaseGoodsPriceNaked} where purchase_number =#{item.purchaseNumber}
        </foreach>
    </update>

    <select id="getSettleBillPoolYflList" resultType="com.ly.yph.api.settlement.common.dto.bill.SettleBillPoolYflDto">
        select c.activity_id,
               b.order_detail_id,
               c.purchase_number,
               a.order_number,
               a.supplier_order_number,
               c.apply_user_id,
               c.apply_emp_code,
               c.apply_user_name,
               c.apply_dept_id,
               c.apply_dept_name,
               c.create_time                     as applyTime,
               b.goods_code,
               b.goods_sku,
               b.goods_desc,
               b.sale_unit,
               d.materials_code,
               b.confirm_num + b.confirm_num_decimal as apply_num,
               b.goods_unit_price_naked          as unitPriceNaked,
               b.goods_unit_price_tax            as unitPriceTax,
               b.goods_total_price_tax           as totalPriceTax,
               b.goods_total_price_naked         as totalPriceNaked,
               b.tax_rate,
               b.tax_code,
               b.goods_image,
               e.address_name,
               e.mob_phone,
               e.address,
               b.order_detail_state,
               f.invoice_subject,
               g.integral_name,
               b.goods_pay_integral,
               b.goods_pay_money,
               a.supplier_code as storeCode,
               a.supplier_name as storeName,
               c.company_code,
               c.company_name,
               c.creator,
               c.remark,
               a.order_freight_price,
               a.freight_type,
               g.activity_code
        from shop_purchase_sub_order a
                 left join shop_purchase_sub_order_detail b on a.order_id = b.order_id
                 left join shop_purchase_order c on a.purchase_number = c.purchase_number
                 left join shop_goods d on d.goods_id = b.goods_id AND d.tenant_id = b.tenant_id
                 left join shop_order_address e on e.purchase_id = c.purchase_id
                 left join invoice_subject f on f.id = c.invoice_id
                 left join system_activity g on g.id = c.activity_id
        where a.order_number = #{orderNumber}
          and a.is_enable = 1
          and g.activity_type != 'PSUP'
    </select>

    <select id="queryInfoByPurchaseNumber" resultMap="ShopPurchaseSubOrderInfo">
        SELECT *
        FROM shop_purchase_sub_order
        WHERE purchase_number = #{purchaseNumber}
          and is_enable = '1'
    </select>
</mapper>
