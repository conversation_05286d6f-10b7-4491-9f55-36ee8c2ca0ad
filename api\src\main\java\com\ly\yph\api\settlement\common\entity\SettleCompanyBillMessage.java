package com.ly.yph.api.settlement.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel("账单主体表")
@TableName("settle_company_bill_message")
@EqualsAndHashCode(callSuper = true)
public class SettleCompanyBillMessage extends BaseEntity {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    @ApiModelProperty("验收明细id")
    private Long checkFormDetailId;

    @ApiModelProperty("发货单明细")
    private Long deliveryDetailId;

    @ApiModelProperty("客户验收状态 0:未验收 1：已验收")
    private Integer companyCheckState;

    @ApiModelProperty("企业处理状态 0：未处理 1：处理成功 2：处理失败 3:无需处理")
    private Integer companyProcessState;

    @ApiModelProperty("供应商验收（B端签收单/c端妥投或签收单）0：未验收 1已验收")
    private Integer supplierCheckState;

    @ApiModelProperty("供应商验收类型 0：妥投 1：签收单")
    private Integer supplierCheckType;

    @ApiModelProperty("企业处理状态 0：未处理 1:处理成功 2：处理失败 3：无需处理")
    private Integer supplierProcessState;

    @ApiModelProperty("企业出账备注")
    private String companyRemark;

    @ApiModelProperty("供应商出账备注")
    private String supplierRemark;

    @ApiModelProperty("租户")
    private Long tenantId;

}
