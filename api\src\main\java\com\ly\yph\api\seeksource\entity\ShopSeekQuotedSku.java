package com.ly.yph.api.seeksource.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 寻源比价供应商中标sku提交表
 *
 * <AUTHOR>
 * @date 2024/11/28 16:15
 */
@Data
@ApiModel("寻源比价供应商中标sku提交表")
@TableName("shop_seek_quoted_sku")
public class ShopSeekQuotedSku extends TenantBaseDO {
    private static final long serialVersionUID = 7810340558226680255L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("询价单id")
    private Long seekPriceId;

    @ApiModelProperty("询价商品id")
    private Long seekGoodsId;

    @ApiModelProperty("报价id")
    private Long quotedPriceId;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("商品编码")
    private String goodsCode;

    @ApiModelProperty("供应商sku")
    private String goodsSku;

    @ApiModelProperty("是否已创建物料关系（未创建：0  已创建：1  失败：2）")
    private Integer createMaterial;
}
