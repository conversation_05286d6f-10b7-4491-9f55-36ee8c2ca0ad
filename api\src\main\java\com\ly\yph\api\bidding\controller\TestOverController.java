package com.ly.yph.api.bidding.controller;


import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.yph.api.electsign.service.ContractSignService;
import com.ly.yph.api.electsign.service.ContractSyncService;
import com.ly.yph.api.electsign.vo.ContractInnerUserVo;
import com.ly.yph.api.electsign.vo.ContractOutOrgVo;
import com.ly.yph.api.electsign.vo.ContractOutUserVo;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.XElasticsearchGoodsQueue;
import com.ly.yph.api.goods.service.ShopGoodsService;
import com.ly.yph.api.goods.workflow.ContractWorkFlowHandler;
import com.ly.yph.api.honda.util.HondaHttpUtils;
import com.ly.yph.api.order.mapper.ShopPurchaseOrderMapper;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.organization.service.SystemContractRenewalService;
import com.ly.yph.api.product.ext.common.dto.response.RemoteGoodsInfoResp;
import com.ly.yph.api.product.ext.common.manage.GoodsGetter;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.ErrorCodeConstants;
import com.ly.yph.core.thread.ThreadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.ServerSocket;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 竞价供应商竞价表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-03-27 16:09:28
 */
@Api(value = "竞价供应商竞价表API" , tags = {"竞价供应商API"})
@Slf4j
@RestController
@RequestMapping("biz/test")
public class TestOverController {

    private static final Object lockA = new Object();
    private static final Object lockB = new Object();

    @Resource
    private ShopGoodsService shopGoodsService;

    @Resource
    private ThreadPoolExecutor commonIoExecutors;

    @Resource
    private ShopPurchaseOrderMapper shopPurchaseOrderMapper;

    @Resource
    private GoodsGetter goodsGetter;

    @Resource
    private ContractSignService contractSignService;

    @Resource
    private ContractSyncService contractSyncService;

    @Resource
    private ContractWorkFlowHandler contractWorkFlowHandler;

    @Resource
    private SystemContractRenewalService contractRenewalService;


    @ApiOperation("执行商品报表整理")
    @RequestMapping("/order-detail")
    //@GetMapping("order-detail")
    public ServiceResult<Boolean> orderDetailReportProcess() {
        for(int i=0;i<100000;i++){
            getGoodsInfo();
        }
        return ServiceResult.succ(true);
    }

    @GetMapping("alive")
    @ApiOperation("检查是否存活")
    @ShenyuSpringCloudClient
    // @TLogAspect(value = "name")
    // @InvokeLogRecord
    public ServiceResult<String> getAlive(String name) throws Exception {
        Thread.sleep(500000);
        return ServiceResult.succ("515151");
    }

    public void getGoodsInfo(){
        for(int i=0;i<1500;i++){
            commonIoExecutors.execute(() -> {
                try{
                    //String url = "https://dfmalluat.szlanyou.com/api/biz/test/alive";
                    String url = "http://127.0.0.1:9868/api/biz/test/alive?tenant-id=1";
                   String resultStr = HondaHttpUtils.sendGet(url);
                    log.info("resultStr {}", resultStr);
                }catch (Exception e){
                    log.error("获取商品信息失败", e);
                }
            });
        }
    }

    public void toReport(){
        String createTime = "2022-01-01 00:00:00";
        for(int i=0;i<30;i++){
            commonIoExecutors.execute(() -> {
                try {
                     shopPurchaseOrderMapper.queryOrderDetailPage_report_clear();
                    shopPurchaseOrderMapper.queryOrderDetailPage_report_to_temp(40L, createTime, null);
                    shopPurchaseOrderMapper.queryOrderDetailPage_report_delete();
                    shopPurchaseOrderMapper.queryOrderDetailPage_report_insert();
                } catch (Exception e) {
                    log.error("OrderReportJob process error", e);
                }
            });
        }
    }

    @ApiOperation(value = "竞价供应商竞价表-testOOM" , httpMethod = "GET")
    @GetMapping("/testOom")
    public void testOom(){
        log.info("oom开始操作：。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
        while (true){
            ExecutorService executor = Executors.newFixedThreadPool(3);
            executor.submit(() -> {
                test1();
            });
            executor.submit(() -> {
                test1();
            });
            executor.submit(() -> {
                test1();
            });

            // 新任务将永久排队
            executor.submit(() -> System.out.println("This will never run"));
        }
    }

    private void test1(){
        List<byte[]> memoryHog = new ArrayList<>();
        while (true) {
            // 每次分配 1MB 字节数组（大对象更快触发OOM）
            memoryHog.add(new byte[1024 * 1024*10]);
            System.out.println("已分配: " + memoryHog.size()*10 + " MB");
        }
    }

    private void test2() throws Exception{
        for (int i = 0; i < 100000; i++) {
            new ServerSocket(0); // 打开但不关闭
        }

        // 阻塞读取（永远不会结束）
        System.in.read();
    }

    @ApiOperation(value = "竞价供应商竞价表-test线程Over" , httpMethod = "GET")
    @RequestMapping("/testOver")
    public void testOver() {
        log.info("testOver 开始操作：。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
        new Thread(() -> {
            synchronized (lockA) {
                try { Thread.sleep(1000); }
                catch (InterruptedException e) {}

                synchronized (lockB) { // 永远等待
                    System.out.println("Thread1 got both locks");
                }
            }
        }).start();

        new Thread(() -> {
            synchronized (lockB) {
                synchronized (lockA) { // 永远等待
                    System.out.println("Thread2 got both locks");
                }
            }
        }).start();
        System.out.println("程序执行完成。。。。。。。。。。。。。。。。。。");
    }

    @ApiOperation(value = "生态聚合中心创建内部用户" , httpMethod = "GET")
    @RequestMapping("/createEInnerUser")

    public JSONObject createEInnerUser(String orgId, String mail, String mobile, String name, String idCard, String unId){
        List<String> orgIds = new ArrayList<>();
        //orgIds.add("963fafe1-3fcc-47df-9919-307d9fa443b4");
        orgIds.add(orgId);

        ContractInnerUserVo contractUserVo = new ContractInnerUserVo();
        contractUserVo.setEmail(mail);
        contractUserVo.setName(name);
        contractUserVo.setOrganizeIdList(orgIds);
        contractUserVo.setLicenseType("IDCard");
        contractUserVo.setLicenseNumber(idCard);
        contractUserVo.setUniqueId(unId);
        contractUserVo.setMobile(mobile);
        JSONObject innerUser = contractSignService.createInitUser(contractUserVo);
        log.info("innerUser: {}", innerUser);
        return innerUser;
    }

    @ApiOperation(value = "生态聚合中心创建外部用户" , httpMethod = "GET")
    @RequestMapping("/createEOutUser")
    public void createFileUpUrl(){
        ContractOutUserVo contractUserVo = new ContractOutUserVo();
        contractUserVo.setContactsEmail("<EMAIL>");
        contractUserVo.setName("李莹");
        contractUserVo.setLicenseNumber("42010619801215483X");
        contractUserVo.setLicenseType("IDCard");
        contractUserVo.setContactsMobile("***********");
        contractUserVo.setLoginMobile("***********");
        contractUserVo.setUniqueId("07739");
        JSONObject innerUser = contractSignService.createOutUser(contractUserVo);
        log.info("outUser: {}", innerUser);
    }

    @ApiOperation(value = "生态聚合中心创建外部组织" , httpMethod = "GET")
    @RequestMapping("/createEOutOrg")
    public void createFileUpOrg(){
        ContractOutOrgVo contractUserVo = new ContractOutOrgVo();
        contractUserVo.setAgentAccountId("6329df01-fc58-4694-9444-a4fb87a5fb91");
        contractUserVo.setOrganizeNo("SUP_91440101MA5CBLAM8G");
        contractUserVo.setOrganizeName("广州井口机电设备有限公司");
        contractUserVo.setEmail("<EMAIL>");
        contractUserVo.setLicenseNumber("91440101MA5CBLAM8G");
        contractUserVo.setLicenseType("SOCNO");
        JSONObject innerUser = contractSignService.createOutOrg(contractUserVo);
        log.info("outOrg: {}", innerUser);
    }

    @ApiOperation(value = "生态聚合中心绑定人员为机构经办人" , httpMethod = "GET")
    @RequestMapping("/bindOutUserAndOrg")
    public void bindOutUserAndOrg(String orgId, String accountId){
        JSONObject innerUser = contractSignService.bindOutUserAndOrg(orgId, accountId);
        log.info("outOrg: {}", innerUser);
    }

    @ApiOperation(value = "上传合同到生态聚合中心" , httpMethod = "GET")
    @RequestMapping("/upContractFile")
    public void upContractFile(String path){
        String updateResult = contractSignService.upContractFile(path);
        log.info("updateResult: {}", updateResult);
    }

    @ApiOperation(value = "发起合同签署" , httpMethod = "GET")
    @RequestMapping("/signFlows")
    public void signFlows(Long contractId){
        SystemContractRenewalEntity contractRenewal = contractRenewalService.getById(contractId);
         contractWorkFlowHandler.startESign(contractRenewal);
       // JSONObject signFlows = contractSignService.signFlows();
      //  log.info("signFlows: {}", signFlows);
    }


    @ApiOperation(value = "发起合同签署" , httpMethod = "GET")
    @RequestMapping("/syncContractToSRM")
    public void syncContractToSRM(Long id){
        JSONObject signFlows = contractSyncService.syncContractToSRM(id);
        log.info("signFlows: {}", signFlows);
    }



    @ApiOperation(value = "填充转换WORD" , httpMethod = "GET")
    @RequestMapping("/fillWordAndToPdf")
    public void fillWordAndToPdf(String docTemplateId){
        contractSignService.fillWordAndToPdf(docTemplateId);
    }
}
