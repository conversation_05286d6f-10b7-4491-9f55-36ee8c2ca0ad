package com.ly.yph.api.settlement.supplier.mapper;

import com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoicePostageDetailExcelDto;
import com.ly.yph.api.settlement.supplier.dto.SupplierInvoicePostageExcelDto;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceDetailPostage;
import com.ly.yph.core.base.database.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Mapper
public interface SupplierInvoiceDetailPostageMapper extends BaseMapperX<SupplierInvoiceDetailPostage> {

    List<SettleShopBillPostageDetail> getSupplierInvoicePostageDetail(@Param("orderNumberList") List<String> orderNumberList,
                                                                      @Param("billId") Long billId,
                                                                      @Param("postageIdList") List<Long> postageIdList);

    List<SupplierInvoicePostageDetailExcelDto> getSupplierInvoicePostageDetailList(@Param("invoiceId") Long invoiceId);
}
