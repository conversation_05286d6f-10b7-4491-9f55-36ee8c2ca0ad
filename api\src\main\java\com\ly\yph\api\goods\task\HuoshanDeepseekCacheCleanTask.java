package com.ly.yph.api.goods.task;

import com.ly.yph.api.goods.service.HuoshanDeepseekCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 火山大模型API调用结果缓存清理任务
 * 
 * <AUTHOR>
 * @since 2025-04-17
 */
@Slf4j
@Component
public class HuoshanDeepseekCacheCleanTask {

    @Autowired
    private HuoshanDeepseekCacheService huoshanDeepseekCacheService;
    
    /**
     * 每天凌晨2点执行清理过期缓存任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredCache() {
        log.info("开始执行火山大模型API调用结果缓存清理任务");
        try {
            int cleanCount = huoshanDeepseekCacheService.cleanExpiredCache(1000);
            log.info("火山大模型API调用结果缓存清理任务完成，清理记录数: {}", cleanCount);
        } catch (Exception e) {
            log.error("火山大模型API调用结果缓存清理任务异常", e);
        }
    }
}
