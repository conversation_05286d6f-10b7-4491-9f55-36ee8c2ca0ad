<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.settlement.supplier.mapper.SupplierInvoiceDetailPostageMapper">


    <select id="getSupplierInvoicePostageDetail"
            resultType="com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail">
        select
        bill_detail_postage_id,
        order_number,
        postage,
        tax_rate
        from settle_shop_bill_postage_detail
        where bill_id =#{billId} and is_enable =1 and invoice_flag =2
        <if test="orderNumberList !=null and orderNumberList.size()>0">
            and order_number in
            <foreach collection="orderNumberList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="postageIdList !=null and postageIdList.size()>0">
            and bill_detail_postage_id in
            <foreach collection="postageIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSupplierInvoicePostageDetailList"
            resultType="com.ly.yph.api.settlement.supplier.dto.SupplierInvoicePostageDetailExcelDto">
        select
            ssb.check_year,
            ssb.check_month,
            ssbpd.order_number,
            ssbpd.apply_user_name,
            ssbpd.postage,
            ssbpd.tax_rate,
            ssbpd.supplier_order_number
        from supplier_invoice_detail_postage sidp
        left join supplier_invoice_bill sib on sidp.invoice_id =sib.id
        left join settle_shop_bill_postage_detail ssbpd on ssbpd.bill_detail_postage_id =sidp.postage_detail_id
        left join settle_shop_bill ssb on ssb.bill_id =sib.bill_id
        where sib.id =#{invoiceId}
    </select>


</mapper>