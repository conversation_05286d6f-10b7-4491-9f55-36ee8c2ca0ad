package com.ly.yph.api.settlement.common.mapper;

import com.ly.yph.api.orderlifecycle.dto.UpdateOrderDetailDto;
import com.ly.yph.api.settlement.common.dto.check.PreSupplierBillOutMessageDto;
import com.ly.yph.api.settlement.common.entity.SettleCheckFormDetail;
import com.ly.yph.api.settlement.common.vo.check.CheckFormDetailVo;
import com.ly.yph.core.base.database.BaseMapperX;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 类注释
 *
 * <AUTHOR>
 * @date 2023/5/15 16:20
 */
@Mapper
public interface SettleCheckDetailMapper extends BaseMapperX<SettleCheckFormDetail> {
    /**
     * 根据验收单id查询验收详情
     *
     * @param checkFormId 检查表单id
     * @return {@link List}<{@link SettleCheckFormDetail}>
     */
    default List<SettleCheckFormDetail> getDetailByFormId(Long checkFormId) {
        return selectList(new LambdaQueryWrapperX<SettleCheckFormDetail>()
                .eq(SettleCheckFormDetail::getCheckFormId, checkFormId));
    }

    /**
     * 查询验收单明细vo
     *
     * @param checkFormId 验收单id
     * @return {@link List}<{@link CheckFormDetailVo}>
     */
    List<CheckFormDetailVo> getCheckFormDetailVo(Long checkFormId);

    void deleteByIds(@Param("ids") List<Long> ids);

    List<UpdateOrderDetailDto> getUpdateOrderDetailForCheckForm(@Param("checkFormId") Long checkFormId);

    List<PreSupplierBillOutMessageDto> getCompanyBillMessageForCheckFormId(@Param("checkFormId") Long checkFormId);
}
