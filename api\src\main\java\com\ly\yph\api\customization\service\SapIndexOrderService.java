package com.ly.yph.api.customization.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.yph.api.customization.common.CompanyConstant;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.customization.common.Constant;
import com.ly.yph.api.customization.common.PurchaseBudgetTypeEnum;
import com.ly.yph.api.customization.config.DfgSapConfig;
import com.ly.yph.api.customization.dto.*;
import com.ly.yph.api.customization.entity.*;
import com.ly.yph.api.customization.factory.DfgExtDeliveryService;
import com.ly.yph.api.customization.mapper.SapIndexOrderMapper;
import com.ly.yph.api.customization.vo.SapDeliveryDetailVo;
import com.ly.yph.api.customization.vo.SapDeliveryVo;
import com.ly.yph.api.customization.vo.SapIndexOrderVo;
import com.ly.yph.api.goods.entity.ShopMaterialRelationEntity;
import com.ly.yph.api.goods.service.ShopMaterialRelationService;
import com.ly.yph.api.goods.service.YphStandardClassService;
import com.ly.yph.api.order.dto.PurchaseOrderInfoCollectDto;
import com.ly.yph.api.order.entity.ShopOrderAddress;
import com.ly.yph.api.order.entity.ShopPurchaseOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrder;
import com.ly.yph.api.order.entity.ShopPurchaseSubOrderDetail;
import com.ly.yph.api.order.service.ShopPurchaseOrderService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderDetailService;
import com.ly.yph.api.order.service.ShopPurchaseSubOrderService;
import com.ly.yph.api.organization.entity.SystemBudget;
import com.ly.yph.api.settlement.common.dto.bill.DetailMatchSapDataDto;
import com.ly.yph.api.settlement.common.dto.bill.OutBillPriceDto;
import com.ly.yph.api.settlement.common.dto.invoice.InvoiceBillDto;
import com.ly.yph.api.settlement.common.dto.invoice.OutDataInvoiceDetailDto;
import com.ly.yph.api.settlement.common.dto.invoice.OutDataInvoiceDto;
import com.ly.yph.api.settlement.common.enums.DetailMatchSapEnum;
import com.ly.yph.api.settlement.common.service.BillDetailOutCheckedRelationService;
import com.ly.yph.api.system.query.IdGen;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.service.DictDataService;
import com.ly.yph.core.util.RRException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sap索引单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-28 09:37:12
 */
@Service("sapIndexOrderService")
@Slf4j
public class SapIndexOrderService extends ServiceImpl<SapIndexOrderMapper, SapIndexOrderEntity> {

    @Resource(name = "sapIndexOrderService")
    @Lazy
    private SapIndexOrderService self;
    @Autowired
    private SapIndexOrderMapper sapIndexOrderMapper;

    @Resource
    DfgSapConfig dfgSapConfig;

    @Autowired
    private DfgDeliveryDetailService dfgDeliveryDetailService;

    @Resource
    private DfgDeliveryService dfgDeliveryService;

    @Resource
    private ShopPurchaseOrderService shopPurchaseOrderService;

    @Resource
    private DfgSapService dfgSapService;

    @Resource
    private YphStandardClassService yphStandardClassService;

    @Resource
    private ShopMaterialRelationService shopMaterialRelationService;


    @Autowired
    private DfgExtDeliveryService dfgExtDeliveryService;


    @Autowired
    private SapIndexOrderDetailService sapIndexOrderDetailService;
    @Resource
    private ShopPurchaseSubOrderService shopPurchaseSubOrderService;

    @Resource
    private ShopPurchaseSubOrderDetailService shopPurchaseSubOrderDetailService;

    @Resource
    private BillDetailOutCheckedRelationService billDetailOutCheckedRelationService;

    @Resource
    private DictDataService dictDataSrv;
    @Resource
    private VoyahBudgetService voyahBudgetService;


    public PageResp<SapIndexOrderVo> queryPageVo(PageReq pageReq, IndexOrderListParamDto indexOrderListParamDto) {
        IPage<SapIndexOrderVo> page =
                sapIndexOrderMapper.queryPageVo(DataAdapter.adapterPageReq(pageReq), indexOrderListParamDto);
        return DataAdapter.adapterPage(page, SapIndexOrderVo.class);
    }

    public List<SapIndexOrderDto> exportIndexOrderList(IndexOrderListParamDto indexOrderListParamDto) {
        List<SapIndexOrderDto> sapIndexOrderDtos = sapIndexOrderMapper.exportIndexOrderList(indexOrderListParamDto);
        for (SapIndexOrderDto sapIndexOrderDto : sapIndexOrderDtos) {
            sapIndexOrderDto.setIndexStateTxt(fillIndexStateTxt(sapIndexOrderDto.getIndexState()));
        }
        return sapIndexOrderDtos;
    }

    @Transactional
    public ServiceResult<T> saveSapIndexOrder(SaveIndexOrderParamDto saveIndexOrderParamDto) {
        if (!StringUtils.isBlank(saveIndexOrderParamDto.getSapIndexOrder().getIndexOrderSn())) {
            //删除索引单
            QueryWrapper<SapIndexOrderEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SapIndexOrderEntity::getIndexOrderSn,saveIndexOrderParamDto.getSapIndexOrder().getIndexOrderSn());
            self.remove(queryWrapper);

            QueryWrapper<SapIndexOrderDetailEntity> sapIndexOrderDetailEntityQueryWrapper = new QueryWrapper<>();
            sapIndexOrderDetailEntityQueryWrapper.lambda().eq(SapIndexOrderDetailEntity::getIndexOrderSn,saveIndexOrderParamDto.getSapIndexOrder().getIndexOrderSn());
            sapIndexOrderDetailService.remove(sapIndexOrderDetailEntityQueryWrapper);
        }
        Integer saveType = saveIndexOrderParamDto.getSaveType();
        List<SendIndexOrderParamDto> deliveryInfo = saveIndexOrderParamDto.getDeliveryInfo();
        SapIndexOrderEntity sapIndexOrder = saveIndexOrderParamDto.getSapIndexOrder();
        LoginUser loginUser = LocalUserHolder.get();
        Assert.notNull(loginUser, "未获取到登录信息");
        String sidId= IdGen.uuid();
        sapIndexOrder.setSioId(sidId);
        if (Constant.CG.equals(saveType)) {
            sapIndexOrder.setIndexState(Constant.CG);
        }else if(Constant.SUBMIT.equals(saveType)){
            sapIndexOrder.setIndexState(Constant.SUBMIT);
        }
        self.save(sapIndexOrder);

        List<SapIndexOrderDetailEntity> detailEntities = Lists.newArrayList();
        List<Integer> allMaraId = deliveryInfo.stream().flatMap(item -> item.getDeliveryMaraId().stream()).collect(Collectors.toList());

        for (Integer maraId : allMaraId) {
            SapIndexOrderDetailEntity sapIndexOrderDetailEntity = new SapIndexOrderDetailEntity();
            sapIndexOrderDetailEntity.setSiodId(IdGen.uuid());
            sapIndexOrderDetailEntity.setSioId(sidId);
            sapIndexOrderDetailEntity.setIndexOrderSn(sapIndexOrder.getIndexOrderSn());
            sapIndexOrderDetailEntity.setSddId(maraId);
            detailEntities.add(sapIndexOrderDetailEntity);
        }
        //保存索引单明细
        sapIndexOrderDetailService.saveBatch(detailEntities);

        //修改收货明细状态
        UpdateWrapper<SapDeliveryDetailEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(SapDeliveryDetailEntity::getSddId,allMaraId).set(SapDeliveryDetailEntity::getIndexState,sapIndexOrder.getIndexState());
        dfgDeliveryDetailService.update(updateWrapper);

        //修改收货主表提交状态
        updateDeliverySubmitState(deliveryInfo.stream().map(item-> Convert.toInt(item.getDeliveryId())).collect(Collectors.toList()));

        if(saveType ==2){
            approveBill(saveIndexOrderParamDto.getInvoiceBillDto(), deliveryInfo.stream().flatMap(item -> item.getDeliveryMaraId().stream()).collect(Collectors.toList()), sapIndexOrder);
            //发送
            return sendIndexOrderToDfgSap(deliveryInfo,sapIndexOrder);
        }
        return ServiceResult.succ();
    }

    public void approveBill(InvoiceBillDto invoiceBillDto, List<Integer> deliveryMaraId,SapIndexOrderEntity sapIndexOrder){
        String werksCode= sapIndexOrder.getWerksCode();
        SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigEntity = yphStandardClassService.getOneByWerksCode(werksCode);
        invoiceBillDto.setCompanyName(CompanyEnum.get(sapCompanyWerksBusinessConfigEntity.getCompanyCode().toUpperCase(Locale.ROOT)).getCompanyName());
        invoiceBillDto.setCompanyCode(sapCompanyWerksBusinessConfigEntity.getCompanyCode());
        OutDataInvoiceDto outDataInvoiceDto = new OutDataInvoiceDto();
        outDataInvoiceDto.setInvoiceBillDto(invoiceBillDto);
        List<OutDataInvoiceDetailDto> details = Lists.newArrayList();
        for (Integer integer : deliveryMaraId) {
            OutDataInvoiceDetailDto outDataInvoiceDetailDto = new OutDataInvoiceDetailDto();
            outDataInvoiceDetailDto.setImportId(Convert.toStr(integer));
            details.add(outDataInvoiceDetailDto);
        }
        outDataInvoiceDto.setOutDataInvoiceDetailDtos(details);
        String invoiceApplyNumber = billDetailOutCheckedRelationService.outDataToInvoice(outDataInvoiceDto);
        //发票开好后修改索引单关联发票申请号
        UpdateWrapper<SapIndexOrderEntity>upq = new UpdateWrapper<>();
        upq.lambda().eq(SapIndexOrderEntity::getIndexOrderSn,sapIndexOrder.getIndexOrderSn()).set(SapIndexOrderEntity::getInvoiceApplyNumber,invoiceApplyNumber);
        this.update(upq);
    }

    /**
     * 修改收货主表提交状态
     *
     * @param deliveryIds
     */
    public void updateDeliverySubmitState(List<Integer> deliveryIds) {
        List<SapDeliveryEntity> deliveryEntities = dfgDeliveryService.listByIds(deliveryIds);
        List<SapDeliveryDetailEntity> detailList = dfgDeliveryService.queryDeliveryDetailByMblnrList(deliveryEntities);
        Map<String,List<SapDeliveryDetailEntity>> mblnrMap = detailList.stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getMblnr));
        for (SapDeliveryEntity deliveryEntity : deliveryEntities) {
            List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList = mblnrMap.get(deliveryEntity.getMblnr());
            List<SapDeliveryDetailEntity> indexStateFilter = sapDeliveryDetailEntityList.stream()
                    .filter(item -> Objects.equals(item.getIndexState(), 0) || Objects.equals(item.getIndexState(), 4))
                    .collect(Collectors.toList());

            Integer submitState = Constant.NOT_SUBMIT;
            //有没有提交的数据，并且有部分提交的数据
            if(indexStateFilter.size()>0 && indexStateFilter.size() < sapDeliveryDetailEntityList.size()){
                submitState = Constant.P_SUBMIT;
            }else if(indexStateFilter.size()>0 && indexStateFilter.size() == sapDeliveryDetailEntityList.size()){
                submitState = Constant.NOT_SUBMIT;
            }else if(indexStateFilter.size() == 0){
                submitState = Constant.ALL_SUBMIT;
            }
            UpdateWrapper<SapDeliveryEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SapDeliveryEntity::getSdId, deliveryEntity.getSdId()).set(SapDeliveryEntity::getSubmitState, submitState);
            dfgDeliveryService.update(updateWrapper);
        }
    }



    @Transactional
    public Integer updateSapIndexOrder(SapIndexOrderEntity sapIndexOrderEntity) {
        return sapIndexOrderMapper.updateSapIndexOrder(sapIndexOrderEntity);
    }


    /**
     * 待结算清单
     *
     * @param requestPage
     * @param waitIndexListParamDto
     * @return
     */
    public PageResp<SapDeliveryVo> waitIndexList(PageReq requestPage, WaitIndexListParamDto waitIndexListParamDto) {
        IPage<SapDeliveryVo> page =
                sapIndexOrderMapper.waitIndexList(DataAdapter.adapterPageReq(requestPage), waitIndexListParamDto);

        List<SapDeliveryVo> record =  page.getRecords();
        List<String> purchaseNumberList = record.stream().map(SapDeliveryVo::getOrderSn).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(purchaseNumberList)){
            return PageResp.empty();
        }
        Map<String, PurchaseOrderInfoCollectDto> purchaseInfo = getPurchaseInfoByPurchaseNumberList(purchaseNumberList);
        //查询索引单明细
        Map<Integer, List<SapIndexOrderDetailEntity>> sapDeliveryDetailEntityMap = getIndexDetailByDelivery(record);

        List<SapCompanyWerksBusinessConfigEntity> sapBusinessConfig = yphStandardClassService.getSapCompanyAllConfig();
        Map<String, SapCompanyWerksBusinessConfigEntity> sapBusinessConfigMap = sapBusinessConfig.stream()
                .collect(Collectors.toMap(SapCompanyWerksBusinessConfigEntity::getWerksCode, Function.identity(), (a, b) -> a));

        for (SapDeliveryVo sapDeliveryVo : record) {
            SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigVo = sapBusinessConfigMap.get(sapDeliveryVo.getWerksCode());
            if (sapCompanyWerksBusinessConfigVo!=null){
                sapDeliveryVo.setWerksName(sapCompanyWerksBusinessConfigVo.getWerksName());
            }else {
                log.info("company_code is null! werks_code:{}",sapDeliveryVo.getWerksCode());
                continue;
            }
            if(CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(sapDeliveryVo.getCompanyCode())){
                sapDeliveryVo.setSupplierCode(dfgSapConfig.getDfgmSapSupplier());
            }else{
                sapDeliveryVo.setSupplierCode(dfgSapConfig.getDfgSapSupplier());
            }
            sapDeliveryVo.setCompanyName(getEnumObjByKey(sapDeliveryVo.getCompanyCode()).getCompanyName());
            sapDeliveryVo.setSupplierName(Constant.SUPPLIER_NAME);
            PurchaseOrderInfoCollectDto openOrderVo = purchaseInfo.get(sapDeliveryVo.getOrderSn());
            if(Objects.isNull(openOrderVo)){
                log.error("sap delivery not found order orderSn:"+sapDeliveryVo.getOrderSn());
                continue;
            }
            if(openOrderVo.getShopPurchaseOrder().getExtBudget()==0){
                SystemBudget systemBudget = openOrderVo.getSystemBudget();
                if(!Objects.isNull(systemBudget)){
                    sapDeliveryVo.setOrderType(systemBudget.getIsStock());
                }
                sapDeliveryVo.setBudgetId(systemBudget.getId());
                sapDeliveryVo.setApplyNumber(systemBudget.getApplyNumber());
            }else{
                if (PurchaseBudgetTypeEnum.FY.getBudgetType().equalsIgnoreCase(openOrderVo.getShopPurchaseOrder().getBudgetType())) {
                    sapDeliveryVo.setOrderType(0);
                } else if (PurchaseBudgetTypeEnum.CH.getBudgetType().equalsIgnoreCase(openOrderVo.getShopPurchaseOrder().getBudgetType())) {
                    sapDeliveryVo.setOrderType(1);
                }
                sapDeliveryVo.setApplyNumber(openOrderVo.getShopPurchaseOrder().getBudgetApplyCode());
            }

            if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(openOrderVo.getShopPurchaseOrder().getCompanyCode()) && 5 == openOrderVo.getShopPurchaseOrder().getSapOrderType()) {
                sapDeliveryVo.setOrderType(5);
            }

            for (SapDeliveryDetailVo sapDeliveryDetailVo : sapDeliveryVo.getSapDeliveryDetailVos()) {
                sapDeliveryDetailVo.setGsberName(sapCompanyWerksBusinessConfigVo.getCmyName() + "业务范围");
                List<ShopPurchaseSubOrderDetail> openOrderGoodsVos = openOrderVo.getShopPurchaseSubOrderDetailList();
                List<ShopPurchaseSubOrderDetail> filterGoods = openOrderGoodsVos.stream().filter(item -> item.getRowSerialNumber().toString().equals(sapDeliveryDetailVo.getItemNo())).collect(Collectors.toList());
                if (!filterGoods.isEmpty()) {
                    //会出现多次提交单个收货数据生成索引单，并需保留上次索引单结果，所以存在一个收货明细存在于多个索引单里面，需根据索引单号在过滤一次
                    List<SapIndexOrderDetailEntity> sapIndexOrderDetailEntities = sapDeliveryDetailEntityMap.get(sapDeliveryDetailVo.getSddId());
                    if(!CollectionUtil.isEmpty(sapIndexOrderDetailEntities)){
                        sapIndexOrderDetailEntities = sapIndexOrderDetailEntities.stream().sorted(Comparator.comparing(SapIndexOrderDetailEntity::getCreateTime).reversed()).collect(Collectors.toList());
                        if (!Objects.isNull(sapIndexOrderDetailEntities)) {
                            sapDeliveryDetailVo.setIndexOrderSn(sapIndexOrderDetailEntities.get(0).getIndexOrderSn());
                        }
                    }
                    sapDeliveryDetailVo.setGoodsName(filterGoods.get(0).getGoodsName());
                    sapDeliveryDetailVo.setGoodsNakePrice(filterGoods.get(0).getGoodsUnitPriceNaked());
                    sapDeliveryDetailVo.setGoodsSerial(filterGoods.get(0).getGoodsSku());
                    sapDeliveryDetailVo.setGoodsCode(filterGoods.get(0).getGoodsCode());
                    sapDeliveryDetailVo.setGoodsRowPrice(filterGoods.get(0).getGoodsUnitPriceNaked().multiply(new BigDecimal(sapDeliveryDetailVo.getMenge())));
                    sapDeliveryDetailVo.setTaxRate(filterGoods.get(0).getTaxRate());
                }
            }
        }

        record.removeIf(item->CollectionUtil.isEmpty(item.getSapDeliveryDetailVos()));
        //移除没有匹配上订单号的数据
        record.removeIf(item->Objects.isNull(item.getOrderSn()));
        page.setRecords(record);
        return DataAdapter.adapterPage(page, SapDeliveryVo.class);
    }

    public static CompanyEnum getEnumObjByKey(String key) {
        Optional<CompanyEnum> any = Arrays.stream(CompanyEnum.class.getEnumConstants())
                .filter(e -> e.getCompanyCode().equalsIgnoreCase(key)).findAny();
        if (any.isPresent()) {
            return any.get();
        }
        return null;
    }

    /**
     * 发送索引单
     *
     * @param indexOrderSn1
     * @return
     */
    @Transactional
    public ServiceResult<T> sendIndexOrderToDfgSapTest(String indexOrderSn1) {
        log.info("indexOrderSn1："+indexOrderSn1);
        QueryWrapper<SapIndexOrderEntity> queryWrapper =  new QueryWrapper<>();
        queryWrapper.lambda().eq(SapIndexOrderEntity::getIndexOrderSn,indexOrderSn1);
        SapIndexOrderEntity sapIndexOrder =  self.getOne(queryWrapper);
        List<SendIndexOrderParamDto> deliveryInfo = new ArrayList<>();
        log.info("查询sapIndexOrder："+sapIndexOrder);
        QueryWrapper<SapIndexOrderDetailEntity> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(SapIndexOrderDetailEntity::getIndexOrderSn,indexOrderSn1);
        List<SapIndexOrderDetailEntity> sapIndexOrderDetailEntities  = sapIndexOrderDetailService.list(queryWrapper1);
        log.info("查询sapIndexOrderDetailEntities："+sapIndexOrderDetailEntities);
        List<Integer> sddIds = sapIndexOrderDetailEntities.stream().map(SapIndexOrderDetailEntity::getSddId).collect(Collectors.toList());
        log.info("索引单收货明细："+sddIds);
        List<SapDeliveryDetailEntity> sapDeliveryDetailEntities1 = new ArrayList<>();
        for (SapIndexOrderDetailEntity sapIndexOrderDetailEntity : sapIndexOrderDetailEntities) {
            QueryWrapper<SapDeliveryDetailEntity> queryWrapper4= new QueryWrapper<>();
            queryWrapper4.lambda().eq(SapDeliveryDetailEntity::getSddId,sapIndexOrderDetailEntity.getSddId());
            SapDeliveryDetailEntity sapDeliveryDetailEntity = dfgDeliveryDetailService.getOne(queryWrapper4);
            sapDeliveryDetailEntities1.add(sapDeliveryDetailEntity);
        }
        List<String> mblnrs = sapDeliveryDetailEntities1.stream().map(SapDeliveryDetailEntity::getMblnr).collect(Collectors.toList());

        QueryWrapper<SapDeliveryEntity> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.lambda().in(SapDeliveryEntity::getMblnr,mblnrs);
        List<SapDeliveryEntity> sapDeliveryEntities1 = dfgDeliveryService.list(queryWrapper2);

        for (SapDeliveryEntity sapDeliveryEntity : sapDeliveryEntities1) {
            SendIndexOrderParamDto sendIndexOrderParamDto = new SendIndexOrderParamDto();
            sendIndexOrderParamDto.setDeliveryId(sapDeliveryEntity.getSdId().toString());
            List<SapDeliveryDetailEntity> filterDetail= sapDeliveryDetailEntities1.stream().filter(item->item.getMblnr().equals(sapDeliveryEntity.getMblnr())).collect(Collectors.toList());
            sendIndexOrderParamDto.setDeliveryMaraId(filterDetail.stream().map(SapDeliveryDetailEntity::getSddId).collect(Collectors.toList()));
            deliveryInfo.add(sendIndexOrderParamDto);
        }
        return sendIndexOrderToDfgSap(deliveryInfo, sapIndexOrder);
    }



    /**
     * 发送索引单
     *
     * @param deliveryInfo
     * @return
     */
    @Transactional
    public ServiceResult<T> sendIndexOrderToDfgSap(List<SendIndexOrderParamDto> deliveryInfo,SapIndexOrderEntity sapIndexOrder) {

        LoginUser loginUser = LocalUserHolder.get();

        List<Integer> allMaraId = deliveryInfo.stream().flatMap(item -> item.getDeliveryMaraId().stream()).collect(Collectors.toList());
        List<String> deliveryId = deliveryInfo.stream().map(SendIndexOrderParamDto::getDeliveryId).collect(Collectors.toList());
        List<SapDeliveryDetailEntity> sapDeliveryDetailEntities = dfgDeliveryDetailService.listByIds(allMaraId);

        Map<String,List<SapDeliveryDetailEntity>> sapDeliveryDetailOrderSnMap = sapDeliveryDetailEntities.stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getMblnr));
        List<SapDeliveryEntity> sapDeliveryEntities = dfgDeliveryService.listByIds(deliveryId);

        //判断这些收货是否同一个公司（必须是）取公司编码
        Set<String> companyCodeSet = sapDeliveryEntities.stream().map(SapDeliveryEntity::getCompanyCode).collect(Collectors.toSet());
        if(companyCodeSet.size()>1){
            log.info("索引单存在多个公司订单，数据异常：" + companyCodeSet);
            throw new RRException("索引单存在多个公司订单，数据异常！请联系管理员！");
        }
        String companyCode = companyCodeSet.stream().findFirst().get();
        List<CallSAPCreateIndexOrderParamDto> callSAPCreateIndexOrderParamDtos = Lists.newArrayList();
        //查询所有订单
        Set<String> purchaseNumberSet = sapDeliveryEntities.stream().map(SapDeliveryEntity::getOrderSn).collect(Collectors.toSet());
        Map<String, PurchaseOrderInfoCollectDto> purchaseInfo = Maps.newHashMap();
        List<SapContractRealEntity> sapContractRealEntities  = yphStandardClassService.getSapContractAll();
        //可能存在2个订单100次收货，减少查询次数
        for (String purchaseNumber : purchaseNumberSet) {
            //查询订单
            PurchaseOrderInfoCollectDto purchaseOrderInfoCollectDto = shopPurchaseOrderService.getPurchaseOrderInfoCollect(purchaseNumber);
            purchaseInfo.put(purchaseNumber, purchaseOrderInfoCollectDto);
        }

        //批量查询公司配置 44条数据
        List<SapCompanyWerksBusinessConfigEntity> sapBusinessConfig =yphStandardClassService.getSapCompanyAllConfig();

        Map<String, SapCompanyWerksBusinessConfigEntity> sapBusinessConfigMap = sapBusinessConfig.stream().collect(Collectors.toMap(SapCompanyWerksBusinessConfigEntity::getWerksCode, Function.identity(), (a, b) -> a));

        for (SapDeliveryEntity sapDeliveryEntity : sapDeliveryEntities) {
            String purchaseNumber=sapDeliveryEntity.getOrderSn();
            //查询订单明细
            PurchaseOrderInfoCollectDto purchaseOrderInfoCollectDto  = purchaseInfo.get(purchaseNumber);

            List<ShopPurchaseSubOrderDetail> orderGoodsEntities = purchaseOrderInfoCollectDto.getShopPurchaseSubOrderDetailList();

            ShopPurchaseOrder shopPurchaseOrder = purchaseOrderInfoCollectDto.getShopPurchaseOrder();
            ShopOrderAddress address = purchaseOrderInfoCollectDto.getShopOrderAddress();

            //查询预算
            SystemBudget systemBudget = purchaseOrderInfoCollectDto.getSystemBudget();
            /**
             * 1、费用提交索引单需要根据预算的成本中心来确定
             * 2、存货的固定，需要区分不同公司
             */
            SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigEntity = new SapCompanyWerksBusinessConfigEntity();
            SapContractRealEntity sapContractRealEntity = new SapContractRealEntity();
            if(shopPurchaseOrder.getExtBudget()==0){
                if (systemBudget.getIsStock() == 0) {
                    List<SapCompanyWerksBusinessConfigEntity> sapRealList = yphStandardClassService.getSapCompanyAllConfig();
                    if (CompanyEnum.DFG01.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode()) || CompanyEnum.DFHS.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                        List<SapCompanyWerksBusinessConfigEntity> collect = sapRealList.stream().filter(item -> systemBudget.getBusinessScope().equalsIgnoreCase(item.getBusinessCode())).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(collect)){
                            sapCompanyWerksBusinessConfigEntity = collect.get(0);
                        }
                    }else{
                        if(CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode()) || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())){
                            sapCompanyWerksBusinessConfigEntity  = yphStandardClassService.getSapCompanyWerksByCostCentreCode(systemBudget.getCostCenterCode());
                        }else{
                            List<SapCompanyWerksBusinessConfigEntity> collect = sapRealList.stream().filter(item -> systemBudget.getExtCompanyCode().equalsIgnoreCase(item.getCmyCode())).collect(Collectors.toList());
                            if(CollUtil.isNotEmpty(collect)){
                                if(collect.size()==1){
                                    sapCompanyWerksBusinessConfigEntity = collect.get(0);
                                }else{
                                    throw new RRException("【费用订单发送到SAP】岚图&猛士，费用订单获取公司代码获取到多条！业务范围：" + systemBudget.getBusinessScope());
                                }
                            }
                        }

                    }

                } else if (systemBudget.getIsStock() == 1) {
                    sapCompanyWerksBusinessConfigEntity = new SapCompanyWerksBusinessConfigEntity();
                    if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                        sapCompanyWerksBusinessConfigEntity = sapBusinessConfigMap.get(CompanyConstant.LTKJ_FACTORY_CODE);
                    } else if (CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                        sapCompanyWerksBusinessConfigEntity = sapBusinessConfigMap.get(CompanyConstant.INVENTORY_1600);
                    } else if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())
                            || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                        sapCompanyWerksBusinessConfigEntity = sapBusinessConfigMap.get(address.getFactoryCode());
                    }
                }
                if(CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())&&5==shopPurchaseOrder.getSapOrderType()){
                    sapCompanyWerksBusinessConfigEntity = sapBusinessConfigMap.get(CompanyConstant.LTXS_FACTORY_CODE);
                }
                SapCompanyWerksBusinessConfigEntity finalSapCompanyWerksBusinessConfigEntity1 = sapCompanyWerksBusinessConfigEntity;
                List<SapContractRealEntity> sapContractRealEntities1 = sapContractRealEntities.stream()
                        .filter(item -> item.getSapCompanyCode().equals(finalSapCompanyWerksBusinessConfigEntity1.getCmyCode())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(sapContractRealEntities1)) {
                    log.info("未查询到合同信息：" + sapDeliveryEntity);
                    throw new RRException("未查询到合同信息！请联系管理员！");
                }
                sapContractRealEntity = sapContractRealEntities1.get(0);
            }

            List<SapDeliveryDetailEntity> filterDetail = sapDeliveryDetailOrderSnMap.get(sapDeliveryEntity.getMblnr());

            for (SapDeliveryDetailEntity sapDeliveryDetailEntity : filterDetail) {
                CallSAPCreateIndexOrderParamDto callSAPCreateIndexOrderParamDto = new CallSAPCreateIndexOrderParamDto();
                //SC+业务范围（4位）+会计年度（4位）+流水码（5位）
                //查询流水码
                String indexOrderSn = sapIndexOrder.getIndexOrderSn();
                callSAPCreateIndexOrderParamDto.setZsyh(indexOrderSn);
                callSAPCreateIndexOrderParamDto.setEbeln(sapDeliveryDetailEntity.getEbeln());
                callSAPCreateIndexOrderParamDto.setEbelp(sapDeliveryDetailEntity.getEbelp());
                callSAPCreateIndexOrderParamDto.setBelnr(sapDeliveryDetailEntity.getMblnr());
                callSAPCreateIndexOrderParamDto.setBuzei(sapDeliveryDetailEntity.getZeile());
                callSAPCreateIndexOrderParamDto.setMjahr(sapDeliveryEntity.getMjahr());

                if(shopPurchaseOrder.getExtBudget()==0){
                    if (sapCompanyWerksBusinessConfigEntity!=null && StringUtils.isNotBlank(sapCompanyWerksBusinessConfigEntity.getCompanyCode())){
                        callSAPCreateIndexOrderParamDto.setEkorg(sapCompanyWerksBusinessConfigEntity.getPurchaseOrg());
                        callSAPCreateIndexOrderParamDto.setBukrs(sapCompanyWerksBusinessConfigEntity.getCmyCode());
                    }else {
                        log.info("sapCompanyWerksBusinessConfigVo is null! code:{}",sapContractRealEntity.getSapCompanyCode());
                        throw new RRException("数据源为空！");
                    }
                    callSAPCreateIndexOrderParamDto.setDFM_BUDKOSTL(systemBudget.getCostCenterCode());
                    callSAPCreateIndexOrderParamDto.setDFM_YLZD1(systemBudget.getApplyNumber());
                    callSAPCreateIndexOrderParamDto.setDFM_YLZD2(systemBudget.getLineNo());
                }else{
                    VoyahBudgetResponseDto voyahBudgetResponseDto = new VoyahBudgetResponseDto();
                    if(CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())){
                        voyahBudgetResponseDto = voyahBudgetService.getVoyahBudgetByBudgetApplyNo(shopPurchaseOrder.getApplyEmpCode(), null, shopPurchaseOrder.getBudgetApplyCode(), PurchaseBudgetTypeEnum.FY.getBudgetType().equalsIgnoreCase(shopPurchaseOrder.getBudgetType()) ? Constant.VOYAH1 : Constant.VOYAH2);
                    }
                    callSAPCreateIndexOrderParamDto.setBukrs(voyahBudgetResponseDto.getCompanyNo());
                    //采购组织
                    callSAPCreateIndexOrderParamDto.setEkorg(voyahBudgetResponseDto.getCompanyNo());
                    callSAPCreateIndexOrderParamDto.setWerks(voyahBudgetResponseDto.getFactoryNo());
                    callSAPCreateIndexOrderParamDto.setDFM_BUDKOSTL(voyahBudgetResponseDto.getCostCenterCode());
                    callSAPCreateIndexOrderParamDto.setDFM_YLZD1(voyahBudgetResponseDto.getApplyNo());
                    callSAPCreateIndexOrderParamDto.setDFM_YLZD2("1");
                }

                callSAPCreateIndexOrderParamDto.setLifnr(dfgSapConfig.getDfgSapSupplier());
                callSAPCreateIndexOrderParamDto.setSsxt(Constant.SYSID);
                callSAPCreateIndexOrderParamDto.setZterm("T002");
                if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                    if(shopPurchaseOrder.getExtBudget()==0){
                        //岚图dms 采购组ZO6
                        if (Objects.equals(1, systemBudget.getIsStock()) || shopPurchaseOrder.getSapOrderType() == 5) {
                            callSAPCreateIndexOrderParamDto.setEkgrp(Constant.Z06);
                        } else {
                            callSAPCreateIndexOrderParamDto.setEkgrp(Constant.Z07);
                        }
                    }else{
                        if (PurchaseBudgetTypeEnum.CH.getBudgetType().equalsIgnoreCase(shopPurchaseOrder.getBudgetType())) {
                            callSAPCreateIndexOrderParamDto.setEkgrp(Constant.Z06);
                        } else {
                            callSAPCreateIndexOrderParamDto.setEkgrp(Constant.Z07);
                        }
                    }

                } else if (CompanyEnum.DFHS.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                    callSAPCreateIndexOrderParamDto.setEkgrp(Constant.T09);
                    callSAPCreateIndexOrderParamDto.setZterm("Z100");
                    callSAPCreateIndexOrderParamDto.setLifnr(dfgSapConfig.getDfgSapEccSupplier());
                } else if (CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                    if (Objects.equals(1, systemBudget.getIsStock())) {
                        callSAPCreateIndexOrderParamDto.setEkgrp(Constant.MS4);
                    } else {
                        callSAPCreateIndexOrderParamDto.setEkgrp(Constant.MS5);
                    }
                    callSAPCreateIndexOrderParamDto.setLifnr(dfgSapConfig.getDfgmSapSupplier());
                    callSAPCreateIndexOrderParamDto.setSsxt(Constant.DFGMSYSID);
                } else if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())
                        || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(shopPurchaseOrder.getCompanyCode())) {
                    if(shopPurchaseOrder.getSapOrderType() == 1 || shopPurchaseOrder.getSapOrderType() ==3){
                        callSAPCreateIndexOrderParamDto.setEkgrp(Constant.DFPV603);
                    }else if(shopPurchaseOrder.getSapOrderType() == 2){
                        callSAPCreateIndexOrderParamDto.setEkgrp(Constant.DFPV604);
                    }
                } else {
                    callSAPCreateIndexOrderParamDto.setEkgrp(sapCompanyWerksBusinessConfigEntity.getPurchaseTeam());
                }
                callSAPCreateIndexOrderParamDto.setName1(Constant.SUPPLIER_NAME);
                List<ShopPurchaseSubOrderDetail> openOrderGoodsVos = orderGoodsEntities.stream()
                        .filter(item->item.getRowSerialNumber().toString().equals(sapDeliveryDetailEntity.getItemNo()))
                        .collect(Collectors.toList());
                if(openOrderGoodsVos.isEmpty()){
                    log.error("indexOrder not found ordergoods by itemNo"+sapDeliveryDetailEntity.getItemNo());
                    continue;
                }
                //费用订单可能没有物料
                callSAPCreateIndexOrderParamDto.setMatnr(sapDeliveryDetailEntity.getMatnr());
                LambdaQueryWrapper<ShopMaterialRelationEntity> maraQuery =  new LambdaQueryWrapper<>();
                maraQuery.eq(ShopMaterialRelationEntity::getMaraMatnr,sapDeliveryDetailEntity.getMatnr())
                        .eq(ShopMaterialRelationEntity::getMaraWerks,sapDeliveryDetailEntity.getWerks())
                        .eq(ShopMaterialRelationEntity::getIsDel,0);
               List<ShopMaterialRelationEntity> maraInfo = shopMaterialRelationService.list(maraQuery);
                if (CollectionUtil.isNotEmpty(maraInfo)) {
                    callSAPCreateIndexOrderParamDto.setMaktx(maraInfo.get(0).getMaraMaktx());
                }else{
                    callSAPCreateIndexOrderParamDto.setMaktx(StrUtil.sub(openOrderGoodsVos.get(0).getGoodsName().replaceAll("[^a-zA-Z0-9\\u4E00-\\u9FA5]", ""),0,40));
                }
                callSAPCreateIndexOrderParamDto.setErdat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
                callSAPCreateIndexOrderParamDto.setBudat(sapDeliveryEntity.getBudat());
                callSAPCreateIndexOrderParamDto.setCpudt(sapDeliveryEntity.getCpudt());
                callSAPCreateIndexOrderParamDto.setBwart("101");
                callSAPCreateIndexOrderParamDto.setGrund("");
                callSAPCreateIndexOrderParamDto.setWerks(sapDeliveryDetailEntity.getWerks());
                callSAPCreateIndexOrderParamDto.setGsber(sapDeliveryDetailEntity.getGsber());
                callSAPCreateIndexOrderParamDto.setMenge(sapDeliveryDetailEntity.getMenge());
                //采购单价未税
                callSAPCreateIndexOrderParamDto.setCgdj(openOrderGoodsVos.get(0).getGoodsUnitPriceNaked().toString());
                callSAPCreateIndexOrderParamDto.setMeinh("");
                callSAPCreateIndexOrderParamDto.setJsje(openOrderGoodsVos.get(0).getGoodsUnitPriceNaked().multiply(new BigDecimal(sapDeliveryDetailEntity.getMenge())).toString());
                callSAPCreateIndexOrderParamDto.setMeins("EA");
                callSAPCreateIndexOrderParamDto.setBprme("EA");
                callSAPCreateIndexOrderParamDto.setMwskz("J2");
                callSAPCreateIndexOrderParamDto.setSgtxt(sapDeliveryDetailEntity.getSgtxt());
                callSAPCreateIndexOrderParamDto.setVerkf(sapContractRealEntity.getContractCode());
                callSAPCreateIndexOrderParamDto.setCsdate(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
                callSAPCreateIndexOrderParamDto.setCstime(DateUtil.format(new Date(), DatePattern.PURE_TIME_PATTERN));
                callSAPCreateIndexOrderParamDto.setCsname(loginUser.getNickname());
                callSAPCreateIndexOrderParamDto.setZqrsyh(indexOrderSn);
                callSAPCreateIndexOrderParamDto.setXblnr(sapDeliveryDetailEntity.getWerks() + DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
                callSAPCreateIndexOrderParamDto.setDFM_BUDPRD("");
                callSAPCreateIndexOrderParamDto.setDFM_BUDPROJ("");
                callSAPCreateIndexOrderParamDto.setDFM_EBELN(shopPurchaseOrder.getPurchaseNumber());
                callSAPCreateIndexOrderParamDto.setDFM_EBELP(sapDeliveryDetailEntity.getItemNo());
                callSAPCreateIndexOrderParamDtos.add(callSAPCreateIndexOrderParamDto);
            }
        }
        if (CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(companyCode)
                || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(companyCode)
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(companyCode)) {
            for (CallSAPCreateIndexOrderParamDto callSAPCreateIndexOrderParamDto : callSAPCreateIndexOrderParamDtos) {
                callSAPCreateIndexOrderParamDto.setLifnr(dfgSapConfig.getDfgmSapSupplier());
                if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(companyCode)) {
                    callSAPCreateIndexOrderParamDto.setSsxt(Constant.DFPVSYSID);
                } else if (CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(companyCode)) {
                    callSAPCreateIndexOrderParamDto.setSsxt(Constant.DNASYSID);
                } else {
                    callSAPCreateIndexOrderParamDto.setSsxt(Constant.DFGMSYSID);
                }
            }
            self.callDfgmIndexOrder(callSAPCreateIndexOrderParamDtos);
        }else{
            List<CallDFGSAPCreateIndexOrderParamDto> callDFGSAPCreateIndexOrderParamDtos = Lists.newArrayList();
            for (CallSAPCreateIndexOrderParamDto callSAPCreateIndexOrderParamDto : callSAPCreateIndexOrderParamDtos) {
                CallDFGSAPCreateIndexOrderParamDto callDFGSAPCreateIndexOrderParamDto = BeanUtil.copyProperties(callSAPCreateIndexOrderParamDto, CallDFGSAPCreateIndexOrderParamDto.class);
                callDFGSAPCreateIndexOrderParamDto.setDfmBudprd(callSAPCreateIndexOrderParamDto.getDFM_BUDPRD());
                callDFGSAPCreateIndexOrderParamDto.setDfmBudkostl(callSAPCreateIndexOrderParamDto.getDFM_BUDKOSTL());
                callDFGSAPCreateIndexOrderParamDto.setDfmBudproj(callSAPCreateIndexOrderParamDto.getDFM_BUDPROJ());
                callDFGSAPCreateIndexOrderParamDto.setDfmYlzd1(callSAPCreateIndexOrderParamDto.getDFM_YLZD1());
                callDFGSAPCreateIndexOrderParamDto.setDfmYlzd2(callSAPCreateIndexOrderParamDto.getDFM_YLZD2());
                callDFGSAPCreateIndexOrderParamDto.setDfmEbeln(callSAPCreateIndexOrderParamDto.getDFM_EBELN());
                callDFGSAPCreateIndexOrderParamDto.setDfmEbelp(callSAPCreateIndexOrderParamDto.getDFM_EBELP());
                callDFGSAPCreateIndexOrderParamDtos.add(callDFGSAPCreateIndexOrderParamDto);
            }
            self.callSAPCreateIndexOrder(callDFGSAPCreateIndexOrderParamDtos,companyCode);
        }
        return ServiceResult.succ();
    }

    public ServiceResult<T> callSAPCreateIndexOrder(List<CallDFGSAPCreateIndexOrderParamDto> list,String companyCode) {
        String value = dfgSapService.callSAPSYD(list,companyCode);
        List<Map<String, String>> indexReturnList = Lists.newArrayList();
        Map<String, String> indexReturnMap = null;
        Map<String, List<Map<String, String>>> map = JSON.parseObject(value, Map.class);
        if (map.get("SapT1") instanceof List) {
            indexReturnList = map.get("SapT1");
        } else {
            indexReturnMap = (Map<String, String>) map.get("SapT1");
            indexReturnList.add(indexReturnMap);
        }
        //成功之后，改收货单状态为已发送
        for (Map<String, String> stringObjectMap : indexReturnList) {
            //是收货单物料维度
            if (Constant.S.equals(stringObjectMap.get("Type")) || Constant.W.equals(stringObjectMap.get("Type"))) {
                UpdateWrapper<SapDeliveryDetailEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda()
                        .eq(SapDeliveryDetailEntity::getEbeln, stringObjectMap.get("Ebeln"))
                        .eq(SapDeliveryDetailEntity::getEbelp, stringObjectMap.get("Ebelp"))
                        .set(SapDeliveryDetailEntity::getIndexState, Constant.SUBMIT);
                dfgDeliveryDetailService.update(updateWrapper);
            }else{
                throw new RRException("发送异常！");
            }
        }
        return ServiceResult.succ();
    }

    public void callDfgmIndexOrder(List<CallSAPCreateIndexOrderParamDto> list) {
        DFGMDFSC004ParamDto dfgmdfsc004ParamDto = new DFGMDFSC004ParamDto();
        List<DFGMIndexOrderParamDto> paramList = BeanUtil.copyToList(list, DFGMIndexOrderParamDto.class, new CopyOptions().setIgnoreCase(true));
        for (DFGMIndexOrderParamDto dfgmIndexOrderParamDto : paramList) {
            dfgmIndexOrderParamDto.setGRPID(dfgmIndexOrderParamDto.getZsyh());
        }
        dfgmdfsc004ParamDto.setZSDFSC004_REQ(paramList);
        String reponse = dfgSapService.callDfgmSap(dfgmdfsc004ParamDto);
        Map<String, List<Map<String, String>>> map = JSON.parseObject(reponse, Map.class);
        List<Map<String, String>> sapList = map.get("ZSDFSC004_RES");
        //发送成功只用取一个
        Map<String, String> sapT1 = sapList.get(0);
        String ZRET = sapT1.get("ZRET");
        //成功之后，改收货单状态为已发送
        if (Constant.S.equalsIgnoreCase(ZRET) || Constant.W.equalsIgnoreCase(ZRET)) {
            for (CallSAPCreateIndexOrderParamDto dto : list) {
                //是收货单物料维度
                UpdateWrapper<SapDeliveryDetailEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda()
                        .eq(SapDeliveryDetailEntity::getEbeln, dto.getEbeln())
                        .eq(SapDeliveryDetailEntity::getEbelp, dto.getEbelp())
                        .set(SapDeliveryDetailEntity::getIndexState, Constant.SUBMIT);
                dfgDeliveryDetailService.update(updateWrapper);
            }
        }else{
            throw new RRException("索引单提交异常！");
        }
    }

    public Boolean callNonDeliveryIndexOrder(List<CallSAPCreateIndexOrderParamDto> list) {
        DFGMDFSC004ParamDto dfgmdfsc004ParamDto = new DFGMDFSC004ParamDto();
        List<DFGMIndexOrderParamDto> paramList = BeanUtil.copyToList(list, DFGMIndexOrderParamDto.class, new CopyOptions().setIgnoreCase(true));
        for (DFGMIndexOrderParamDto dfgmIndexOrderParamDto : paramList) {
            dfgmIndexOrderParamDto.setGRPID(dfgmIndexOrderParamDto.getZsyh());
        }
        dfgmdfsc004ParamDto.setZSDFSC004_REQ(paramList);
        String reponse = dfgSapService.callDfgmSap(dfgmdfsc004ParamDto);
        Map<String, List<Map<String, String>>> map = JSON.parseObject(reponse, Map.class);
        List<Map<String, String>> sapList = map.get("ZSDFSC004_RES");
        //发送成功只用取一个
        Map<String, String> sapT1 = sapList.get(0);
        String ZRET = sapT1.get("ZRET");
        //成功之后，改收货单状态为已发送
        if (Constant.S.equalsIgnoreCase(ZRET) || Constant.W.equalsIgnoreCase(ZRET)) {
            return true;
        }
        return false;
    }



    public SapIndexOrderVo collectIndexOrder(CollectIndexOrderParamDto collectIndexOrderParamDto) {
        SapIndexOrderVo sapIndexOrderVo = new SapIndexOrderVo();
        if(collectIndexOrderParamDto.getType()==1){
            SapIndexOrderEntity sapIndexOrderEntity = self.getById(collectIndexOrderParamDto.getIndexOrderId());
            BeanUtil.copyProperties(sapIndexOrderEntity,sapIndexOrderVo);

            SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigVo = yphStandardClassService.getOneByWerksCode(sapIndexOrderEntity.getWerksCode());
            sapIndexOrderVo.setSapCompanyCode(sapCompanyWerksBusinessConfigVo.getCmyCode());
            sapIndexOrderVo.setSapCompanyCodeTxt(sapCompanyWerksBusinessConfigVo.getCmyCode()+sapCompanyWerksBusinessConfigVo.getCmyName());
            sapIndexOrderVo.setBusinessScopeTxt(sapIndexOrderEntity.getWerksCode()+sapCompanyWerksBusinessConfigVo.getCmyName() + "业务范围");

            QueryWrapper<SapIndexOrderDetailEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SapIndexOrderDetailEntity::getSioId,collectIndexOrderParamDto.getIndexOrderId());
            List<SapIndexOrderDetailEntity> sapIndexOrderDetailEntities = sapIndexOrderDetailService.list(queryWrapper);
            List<Integer> sddIds= sapIndexOrderDetailEntities.stream().map(SapIndexOrderDetailEntity::getSddId).collect(Collectors.toList());
            List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList = dfgDeliveryDetailService.listByIds(sddIds);

            List<SapDeliveryVo> sapDeliveryVos = fillSapDeliveryVos(sapDeliveryDetailEntityList);
            sapIndexOrderVo.setSapDeliveryVos(sapDeliveryVos);
        }
        if(collectIndexOrderParamDto.getType()==2){
            //2、行项目汇总
            List<SendIndexOrderParamDto> detailList = collectIndexOrderParamDto.getDetailList();

            //查询收货明细汇总
            List<Integer> sddIds = detailList.stream().flatMap(item -> item.getDeliveryMaraId().stream()).collect(Collectors.toList());
            List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList = dfgDeliveryDetailService.listByIds(sddIds);

            //查询收货汇总
            List<String> allDeliveryId= detailList.stream().map(SendIndexOrderParamDto::getDeliveryId).collect(Collectors.toList());
            List<SapDeliveryEntity> sapDeliveryEntityList = dfgDeliveryService.listByIds(allDeliveryId);

            List<String> werksCode = sapDeliveryEntityList.stream().map(SapDeliveryEntity::getWerksCode).distinct().collect(Collectors.toList());
            if(werksCode.size()>1){
                throw new ParameterException("不同工厂不能创建一个索引单");
            }
            //判断逻辑系统，业务范围是不是唯一
            List<String> logicSystemList = sapDeliveryDetailEntityList.stream().map(SapDeliveryDetailEntity::getLogicSystem).distinct().collect(Collectors.toList());
            if(logicSystemList.size()>1){
                throw new ParameterException("不同逻辑系统不能创建一个索引单");
            }
            List<String> gsberList = sapDeliveryDetailEntityList.stream().map(SapDeliveryDetailEntity::getGsber).distinct().collect(Collectors.toList());
            if(gsberList.size()>1){
                throw new ParameterException("不同业务范围不能创建一个索引单");
            }
            List<String> importIds = sddIds.stream().map(Object::toString).collect(Collectors.toList());
            OutBillPriceDto outBillPriceDto = billDetailOutCheckedRelationService.getInvoiceTaxAmount(importIds);
            sapIndexOrderVo.setInvoiceAmount(outBillPriceDto.getTaxAmount());
            sapIndexOrderVo.setInvoiceTotalPrice(outBillPriceDto.getTotalPriceTax());
            sapIndexOrderVo.setBillTotalPriceNaked(outBillPriceDto.getTotalPriceNaked());
            List<SapDeliveryVo> sapDeliveryVos = fillSapDeliveryVos(sapDeliveryDetailEntityList);
            sapIndexOrderVo.setSapDeliveryVos(sapDeliveryVos);
            //此时还没有索引单
            //创建索引单号 SC+业务范围（4位）+会计年度（4位）+流水码（5位）
            SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigVo = yphStandardClassService.getOneByWerksCode(werksCode.get(0));
            String indexOrderSn = "PC" + sapCompanyWerksBusinessConfigVo.getCmyCode() + DateUtil.format(new Date(),DatePattern.NORM_YEAR_PATTERN);
            List<String> indexOrderSnList = sapIndexOrderMapper.getIndexOrderSnRank(indexOrderSn);

            Integer maxIndexOrderSn = indexOrderSnList.stream()
                    .mapToInt(item -> Convert.toInt(StrUtil.sub(item, 10, item.length())))
                    .max()
                    .orElse(0);

            indexOrderSn = indexOrderSn+String.format("%05d", maxIndexOrderSn + 1);
            sapIndexOrderVo.setIndexOrderSn(indexOrderSn);
            sapIndexOrderVo.setLogicalSystem(logicSystemList.get(0));

//            CompanyConstant.SAPCompanyEnum sapCompanyEnum = CompanyConstant.SAPCompanyEnum.getEnumObjByKey(werksCode.get(0));

            sapIndexOrderVo.setSapCompanyCode(sapCompanyWerksBusinessConfigVo.getCmyCode());
            sapIndexOrderVo.setSapCompanyCodeTxt(sapCompanyWerksBusinessConfigVo.getCmyCode()+sapCompanyWerksBusinessConfigVo.getCmyName());
            sapIndexOrderVo.setBusinessScopeTxt(werksCode.get(0)+sapCompanyWerksBusinessConfigVo.getCmyName() + "业务范围");
            sapIndexOrderVo.setBusinessScope(sapDeliveryDetailEntityList.get(0).getGsber());
            if(CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(sapCompanyWerksBusinessConfigVo.getCompanyCode())){
                sapIndexOrderVo.setSupplierCode(dfgSapConfig.getDfgmSapSupplier());
            }else if(CompanyEnum.DFHS.getCompanyCode().equalsIgnoreCase(sapCompanyWerksBusinessConfigVo.getCompanyCode())){
                sapIndexOrderVo.setSupplierCode(dfgSapConfig.getDfgSapEccSupplier());
            }else{
                sapIndexOrderVo.setSupplierCode(dfgSapConfig.getDfgSapSupplier());
            }
            sapIndexOrderVo.setSupplierName(Constant.SUPPLIER_NAME);
            //结算金额不含税
            Map<String,Map<ShopPurchaseOrder,List<ShopPurchaseSubOrderDetail>>> orderDeliveryMap = getCheckDelivery(sapDeliveryEntityList,sapDeliveryDetailEntityList);
            BigDecimal indexPrice = BigDecimal.ZERO;

            for (SapDeliveryDetailEntity sapDeliveryDetailEntity : sapDeliveryDetailEntityList) {
                Map<ShopPurchaseOrder,List<ShopPurchaseSubOrderDetail>> orderMap = orderDeliveryMap.get(sapDeliveryDetailEntity.getOrderSn());
                List<ShopPurchaseSubOrderDetail> orderGoodsVos = orderMap.entrySet().stream().flatMap(item->item.getValue().stream()).collect(Collectors.toList());
                List<ShopPurchaseSubOrderDetail> filterGoodsVos  = orderGoodsVos.stream().filter(item->item.getRowSerialNumber().toString().equals(sapDeliveryDetailEntity.getItemNo())).collect(Collectors.toList());
                if(filterGoodsVos.isEmpty()){
                    log.error("delivery is not found orderGoods by itemNo。orderSn:{},item:{}",sapDeliveryDetailEntity.getOrderSn(),sapDeliveryDetailEntity.getItemNo());
                    continue;
                }
                ShopPurchaseSubOrderDetail openOrderGoodsVo = filterGoodsVos.get(0);
                indexPrice = indexPrice.add(openOrderGoodsVo.getGoodsUnitPriceNaked().multiply(new BigDecimal(sapDeliveryDetailEntity.getMenge())));
            }
            sapIndexOrderVo.setIndexPrice(indexPrice);
            List<SystemDictDataEntity> dictDataList = dictDataSrv.getDictDatasByDictType("bill_people");
            dictDataList.removeIf(item -> item.getStatus() == 0);
            SystemDictDataEntity systemDictDataEntity = dictDataList.get(0);
            sapIndexOrderVo.setInvoiceMobile(systemDictDataEntity.getValue());
            sapIndexOrderVo.setInvoicePeople(systemDictDataEntity.getLabel());

        }
        return sapIndexOrderVo;
    }


    private List<SapDeliveryVo> fillSapDeliveryVos(List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList){
        List<String> mblnrs = sapDeliveryDetailEntityList.stream().map(SapDeliveryDetailEntity::getMblnr).collect(Collectors.toList());

        Map<String,List<SapDeliveryDetailEntity>> sapDeliveryDetailMap = sapDeliveryDetailEntityList.stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getMblnr));

        QueryWrapper<SapDeliveryEntity> sapDeliveryEntityQueryWrapper = new QueryWrapper<>();
        sapDeliveryEntityQueryWrapper.lambda().in(SapDeliveryEntity::getMblnr,mblnrs);
        List<SapDeliveryEntity> sapDeliveryEntities = dfgDeliveryService.list(sapDeliveryEntityQueryWrapper);
        Map<String,SapDeliveryEntity> sapDeliveryEntityMap = sapDeliveryEntities.stream().collect(Collectors.groupingBy(SapDeliveryEntity::getMblnr,Collectors.collectingAndThen(Collectors.toList(), value->value.get(0))));


        List<String> purchaseNumberList = sapDeliveryEntities.stream().map(SapDeliveryEntity::getOrderSn).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(purchaseNumberList)){
            return Lists.newArrayList();
        }
        Map<String, PurchaseOrderInfoCollectDto> purchaseInfo = getPurchaseInfoByPurchaseNumberList(purchaseNumberList);
        List<SapDeliveryVo> sapDeliveryVos = new ArrayList<>();
        for (Map.Entry<String, SapDeliveryEntity> stringSapDeliveryEntityEntry : sapDeliveryEntityMap.entrySet()) {
            SapDeliveryVo sapDeliveryVo = new SapDeliveryVo();
            PurchaseOrderInfoCollectDto openOrderVo = purchaseInfo.get(stringSapDeliveryEntityEntry.getValue().getOrderSn());
            BeanUtil.copyProperties(stringSapDeliveryEntityEntry.getValue(), sapDeliveryVo);
            List<SapDeliveryDetailEntity> sapDeliveryDetailEntities = sapDeliveryDetailMap.get(stringSapDeliveryEntityEntry.getKey());
            List<SapDeliveryDetailVo> sapDeliveryDetailVos = sapDeliveryDetailEntities.stream().map(item -> {
                SapDeliveryDetailVo sapDeliveryDetailVo = new SapDeliveryDetailVo();
                BeanUtil.copyProperties(item, sapDeliveryDetailVo);
                List<ShopPurchaseSubOrderDetail> openOrderGoodsVos = openOrderVo.getShopPurchaseSubOrderDetailList();
                List<ShopPurchaseSubOrderDetail> filterGoods = openOrderGoodsVos.stream().filter(item1 -> item1.getRowSerialNumber().toString().equals(sapDeliveryDetailVo.getItemNo())).collect(Collectors.toList());
                if(!filterGoods.isEmpty()){
                    sapDeliveryDetailVo.setGoodsName(filterGoods.get(0).getGoodsName());
                    sapDeliveryDetailVo.setGoodsNakePrice(filterGoods.get(0).getGoodsUnitPriceNaked());
                    sapDeliveryDetailVo.setGoodsSerial(filterGoods.get(0).getGoodsSku());
                    sapDeliveryDetailVo.setGoodsCode(filterGoods.get(0).getGoodsCode());
                    sapDeliveryDetailVo.setGoodsRowPrice(filterGoods.get(0).getGoodsUnitPriceNaked().multiply(new BigDecimal(sapDeliveryDetailVo.getMenge())));
                    sapDeliveryDetailVo.setTaxRate(filterGoods.get(0).getTaxRate());
                }
                return sapDeliveryDetailVo;
            }).collect(Collectors.toList());
            sapDeliveryVo.setSapDeliveryDetailVos(sapDeliveryDetailVos);
            sapDeliveryVos.add(sapDeliveryVo);
        }
        return sapDeliveryVos;
    }

    public Map<String,Map<ShopPurchaseOrder,List<ShopPurchaseSubOrderDetail>>> getCheckDelivery(List<SapDeliveryEntity> sapDeliveryEntityList,List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList){
        //查询订单
        List<String> purchaseNumberList= sapDeliveryEntityList.stream().map(SapDeliveryEntity::getOrderSn).distinct().collect(Collectors.toList());
        Map<String, PurchaseOrderInfoCollectDto> purchaseInfo = getPurchaseInfoByPurchaseNumberList(purchaseNumberList);

        //查询收货按orderSn分组收货数据
        Map<String, List<SapDeliveryDetailEntity>> orderSnDeliveryDetailMap = sapDeliveryDetailEntityList.stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getOrderSn));
        List<ShopPurchaseSubOrderDetail> checkDelivery;
        Map<String,Map<ShopPurchaseOrder,List<ShopPurchaseSubOrderDetail>>> orderDeliveryMap = Maps.newHashMap();
        for (Map.Entry<String, PurchaseOrderInfoCollectDto> stringOpenOrderVoEntry : purchaseInfo.entrySet()) {
            checkDelivery = Lists.newArrayList();
            String purchaseNumber = stringOpenOrderVoEntry.getKey();
            PurchaseOrderInfoCollectDto openOrderVo = stringOpenOrderVoEntry.getValue();
            //本次勾选的收货行数据
            List<SapDeliveryDetailEntity> sapDeliveryDetailEntities = orderSnDeliveryDetailMap.get(purchaseNumber);
            for (SapDeliveryDetailEntity sapDeliveryDetailEntity : sapDeliveryDetailEntities) {
                //可能会出现一个行项目多次收货，就会出现一个订单收货下多个itemNo一样
                if(checkDelivery.stream().filter(item->item.getRowSerialNumber().toString().equals(sapDeliveryDetailEntity.getItemNo())).collect(Collectors.toList()).size()==0){
                    checkDelivery.add(openOrderVo.getShopPurchaseSubOrderDetailList().stream().filter(item->item.getRowSerialNumber().toString().equals(sapDeliveryDetailEntity.getItemNo())).findFirst().get());
                }
            }
            Map<ShopPurchaseOrder,List<ShopPurchaseSubOrderDetail>> orderGoodsMap = Maps.newHashMap();
            orderGoodsMap.put(openOrderVo.getShopPurchaseOrder(),checkDelivery);
            orderDeliveryMap.put(purchaseNumber,orderGoodsMap);
        }

        return orderDeliveryMap;
    }

    /**
     * 删除草稿状态的索引单
     *
     * @param indexOrderId
     * @return
     */
    @Transactional
    public ServiceResult<T> deleteDraftIndexOrder(List<String> indexOrderId) {
        QueryWrapper<SapIndexOrderEntity> indexOrderEntityQueryWrapper = new QueryWrapper<>();
        indexOrderEntityQueryWrapper.lambda().in(SapIndexOrderEntity::getSioId,indexOrderId).eq(SapIndexOrderEntity::getIndexState,Constant.CG);
        List<SapIndexOrderEntity> sapIndexOrderEntities = self.list(indexOrderEntityQueryWrapper);
        if(sapIndexOrderEntities.size()!= indexOrderId.size()){
            return ServiceResult.error("不允许删除非草稿状态下的索引单");
        }
        self.removeByIds(indexOrderId);
        //释放收货明细的状态
        QueryWrapper<SapIndexOrderDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SapIndexOrderDetailEntity::getSioId,indexOrderId);
        List<SapIndexOrderDetailEntity> sapIndexOrderDetailEntities = sapIndexOrderDetailService.list(queryWrapper);
        List<Integer> sddIds = sapIndexOrderDetailEntities.stream().map(SapIndexOrderDetailEntity::getSddId).collect(Collectors.toList());

        //修改收货明细状态
        UpdateWrapper<SapDeliveryDetailEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(SapDeliveryDetailEntity::getSddId,sddIds).set(SapDeliveryDetailEntity::getIndexState,Constant.CS).set(SapDeliveryDetailEntity::getBillMatchState,0);
        dfgDeliveryDetailService.update(updateWrapper);

        //删除索引单明细
        QueryWrapper<SapIndexOrderDetailEntity> deleteWapper= new QueryWrapper<>();
        deleteWapper.lambda().in(SapIndexOrderDetailEntity::getSioId,indexOrderId);
        sapIndexOrderDetailService.remove(deleteWapper);

        //修改收货单主表提交状态
        List<SapDeliveryDetailEntity> sapDeliveryDetailEntityList = dfgDeliveryDetailService.listByIds(sddIds);
        List<String> mblnrList = sapDeliveryDetailEntityList.stream().map(SapDeliveryDetailEntity::getMblnr).distinct().collect(Collectors.toList());
        QueryWrapper<SapDeliveryEntity> sapDeliveryQueryWrapper = new QueryWrapper<>();
        sapDeliveryQueryWrapper.lambda().in(SapDeliveryEntity::getMblnr,mblnrList);
        List<SapDeliveryEntity> sapDeliveryEntities = dfgDeliveryService.list(sapDeliveryQueryWrapper);
        self.updateDeliverySubmitState(sapDeliveryEntities.stream().map(SapDeliveryEntity::getSdId).collect(Collectors.toList()));
        return ServiceResult.succ();
    }

    /**
     * 待结算清单导出
     *
     * @param waitIndexListParamDto
     * @return
     */
    public List<IndexOrderExportDto> querySapIndexDetailExport(WaitIndexListParamDto waitIndexListParamDto) {
        List<SapDeliveryVo> list  = sapIndexOrderMapper.querySapIndexDetalExport(waitIndexListParamDto);
        if(list.isEmpty()){
            return Lists.newArrayList();
        }
        //查收货信息
        Map<String, SapDeliveryVo> sapdeliveryVoMap = list.stream().collect(Collectors.toMap(SapDeliveryVo::getMblnr, Function.identity()));
        List<SapDeliveryDetailVo> sapDeliveryDetailVos = list.stream().flatMap(item -> item.getSapDeliveryDetailVos().stream()).collect(Collectors.toList());
        //查订单信息
        List<String> purchaseNumberList = list.stream().map(SapDeliveryVo::getOrderSn).collect(Collectors.toList());
        Map<String, PurchaseOrderInfoCollectDto> purchaseInfo = getPurchaseInfoByPurchaseNumberList(purchaseNumberList);

        Map<Integer, List<SapIndexOrderDetailEntity>> sapDeliveryDetailEntityMap = getIndexDetailByDelivery(list);

        List<IndexOrderExportDto> exportList= Lists.newArrayList();
        for (SapDeliveryDetailVo sapDeliveryDetailVo : sapDeliveryDetailVos) {
            IndexOrderExportDto indexOrderExportDto = new IndexOrderExportDto();
            BeanUtil.copyProperties(sapDeliveryDetailVo,indexOrderExportDto);
            SapDeliveryVo sapDeliveryVo = sapdeliveryVoMap.get(sapDeliveryDetailVo.getMblnr());
            BeanUtil.copyProperties(sapDeliveryVo,indexOrderExportDto);
            //会出现多次提交单个收货数据生成索引单，并需保留上次索引单结果，所以存在一个收货明细存在于多个索引单里面，需根据索引单号在过滤一次
            List<SapIndexOrderDetailEntity> sapIndexOrderDetailEntities = sapDeliveryDetailEntityMap.get(sapDeliveryDetailVo.getSddId());
            if(!CollectionUtil.isEmpty(sapIndexOrderDetailEntities)){
                sapIndexOrderDetailEntities = sapIndexOrderDetailEntities.stream().sorted(Comparator.comparing(SapIndexOrderDetailEntity::getCreateTime).reversed()).collect(Collectors.toList());
                if (!Objects.isNull(sapIndexOrderDetailEntities)) {
                    indexOrderExportDto.setIndexOrderSn(sapIndexOrderDetailEntities.get(0).getIndexOrderSn());
                }
            }
            PurchaseOrderInfoCollectDto openOrderVo = purchaseInfo.get(sapDeliveryVo.getOrderSn());
            if(Objects.isNull(openOrderVo)){
                continue;
            }
            List<ShopPurchaseSubOrderDetail> openOrderGoodsVos = openOrderVo.getShopPurchaseSubOrderDetailList();
            List<ShopPurchaseSubOrderDetail> filterGoods = openOrderGoodsVos.stream().filter(item1 -> item1.getRowSerialNumber().toString().equals(sapDeliveryDetailVo.getItemNo())).collect(Collectors.toList());
            indexOrderExportDto.setGoodsNakePrice(filterGoods.get(0).getGoodsUnitPriceNaked());
            indexOrderExportDto.setGoodsRowPrice(filterGoods.get(0).getGoodsUnitPriceNaked().multiply(new BigDecimal(sapDeliveryDetailVo.getMenge())));
            indexOrderExportDto.setGoodsCode(filterGoods.get(0).getGoodsCode());
            indexOrderExportDto.setGoodsName(filterGoods.get(0).getGoodsName());
            SystemBudget systemBudget = openOrderVo.getSystemBudget();
            indexOrderExportDto.setOrderType(systemBudget.getIsStock() == 0 ? "费用类订单" : "存货类订单");
            SapCompanyWerksBusinessConfigEntity sapCompanyWerksBusinessConfigVo = yphStandardClassService.getOneByWerksCode(indexOrderExportDto.getWerksCode());
            if (sapCompanyWerksBusinessConfigVo!=null){
                indexOrderExportDto.setWerksName(sapCompanyWerksBusinessConfigVo.getWerksName());
            }else {
                log.info("company_code is null! werks_code:{}",indexOrderExportDto.getWerksCode());
                continue;
            }
            indexOrderExportDto.setIndexStateTxt(fillIndexStateTxt(indexOrderExportDto.getIndexState()));
            indexOrderExportDto.setBudgetApplyCode(systemBudget.getApplyNumber());
            exportList.add(indexOrderExportDto);
        }
        return exportList;
    }

    private Map<String,PurchaseOrderInfoCollectDto> getPurchaseInfoByPurchaseNumberList(List<String> purchaseNumberList){
        Map<String, PurchaseOrderInfoCollectDto> purchaseInfo = Maps.newHashMap();
        for (String purchaseNumber : purchaseNumberList) {
            //查询订单
            PurchaseOrderInfoCollectDto purchaseOrderInfoCollectDto = shopPurchaseOrderService.getPurchaseOrderInfoCollect(purchaseNumber);
            purchaseInfo.put(purchaseNumber, purchaseOrderInfoCollectDto);
        }
        return purchaseInfo;
    }
    private String fillIndexStateTxt(Integer indexState) {
        //索引单状态（0、初始，1、草稿，2、已提交索引单，3、索引单已结算，4、索引单被冲销,5、报销单已创建）
        switch (indexState) {
            case 0:
                return "初始";
            case 1:
                return "草稿";
            case 2:
                return "已提交索引单";
            case 3:
                return "索引单已结算";
            case 4:
                return "索引单被冲销";
            case 5:
                return "报销单已创建";
            default:
                return "未知的状态";
        }
    }

    public void billMatchSap(List<DetailMatchSapDataDto> detailMatchSapDataDtos) {
        if (CollectionUtil.isEmpty(detailMatchSapDataDtos)) {
            throw new RRException("【billMatchSap】参数不能为空");
        }

        //账单汇总商品
        List<DetailMatchSapDataDto> collectGoodsCodeList = collectGoodsCodeList(detailMatchSapDataDtos);

        Set<String> purchaseNumberList = detailMatchSapDataDtos.stream().map(DetailMatchSapDataDto::getPurchaseNumber).collect(Collectors.toSet());

        QueryWrapper<ShopPurchaseSubOrder> shopPurchaseSubOrderQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderQueryWrapper.lambda()
                .in(ShopPurchaseSubOrder::getPurchaseNumber, purchaseNumberList);
        //先不考虑取消，如果是取消，就代表需要运维
//                .gt(ShopPurchaseSubOrder::getOrderState, 0);
        List<ShopPurchaseSubOrder> shopPurchaseSubOrderList = shopPurchaseSubOrderService.list(shopPurchaseSubOrderQueryWrapper);

        if (CollectionUtil.isEmpty(shopPurchaseSubOrderList)) {
            log.info("【billMatchSap】未查询到数据");
            return;
        }

        //查询待结算清单未提交的数据
        QueryWrapper<SapDeliveryDetailEntity> deliveryDetailQuery = new QueryWrapper<>();
        deliveryDetailQuery.lambda().in(SapDeliveryDetailEntity::getOrderSn, purchaseNumberList)
                .in(SapDeliveryDetailEntity::getIndexState, 0, 4);
        List<SapDeliveryDetailEntity> detailEntities = dfgDeliveryDetailService.list(deliveryDetailQuery);

        //汇总SAP收货
        List<SapDeliveryDetailEntity> collectItemNoList = collectItemNoList(detailEntities);

        Set<String> orderNumberList = shopPurchaseSubOrderList.stream().map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toSet());

        QueryWrapper<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailQueryWrapper = new QueryWrapper<>();
        shopPurchaseSubOrderDetailQueryWrapper.lambda().in(ShopPurchaseSubOrderDetail::getOrderNumber, orderNumberList);
        //先不考虑取消，如果是取消，就代表需要运维
//                .gt(ShopPurchaseSubOrderDetail::getOrderDetailState, 0);
        List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList = shopPurchaseSubOrderDetailService.list(shopPurchaseSubOrderDetailQueryWrapper);

        //全部匹配
        List<SapDeliveryDetailEntity> matchSuccessSapData = Lists.newArrayList();

        //部分匹配
        List<SapDeliveryDetailEntity> partialMatchSapDate = Lists.newArrayList();

        //用汇总过商品的数据循环，反向找到detailId
        for (DetailMatchSapDataDto detailMatchSapDataDto : collectGoodsCodeList) {
            String purchaseNumber = detailMatchSapDataDto.getPurchaseNumber();
            String goodsCode = detailMatchSapDataDto.getGoodsCode();
            log.info("开始循环匹配订单和商品：purchaseNumber:" + purchaseNumber + ",goodsCode:" + goodsCode);
            ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = filterOrderDetailByGoodsCode(shopPurchaseSubOrderList, shopPurchaseSubOrderDetailList, purchaseNumber, goodsCode);
            Integer itemNo = shopPurchaseSubOrderDetail.getRowSerialNumber();
            SapDeliveryDetailEntity sapDeliveryDetailEntity = filterOrderDetailByItemNo(collectItemNoList, purchaseNumber, itemNo);
            if (Objects.isNull(sapDeliveryDetailEntity)) {
                //在sap没有匹配到数据，表示未查询到SAP收货记录
                setMatchSapDataReason(detailMatchSapDataDtos, purchaseNumber, goodsCode, DetailMatchSapEnum.PARTIAL_MATCHING);
            } else {
                BigDecimal billGoodsNumber = detailMatchSapDataDto.getCheckedNum();
                BigDecimal sapGoodsNumber = Convert.toBigDecimal(sapDeliveryDetailEntity.getMenge());
                //不等于就代表有问题，需要人工去做校验
                if (CompareUtil.compare(billGoodsNumber, sapGoodsNumber) != 0) {
                    //匹配到数据但是数量不匹配
                    setMatchSapDataReason(detailMatchSapDataDtos, purchaseNumber, goodsCode, DetailMatchSapEnum.UNMATCHED);
                    partialMatchSapDate.add(sapDeliveryDetailEntity);
                } else if (CompareUtil.compare(billGoodsNumber, sapGoodsNumber) == 0) {
                    //表示匹配成功
                    setMatchSapDataReason(detailMatchSapDataDtos, purchaseNumber, goodsCode, DetailMatchSapEnum.MATCH);
                    matchSuccessSapData.add(sapDeliveryDetailEntity);
                }
            }
        }
        //修改SAP明细状态
        updateSapDeliveryMatchState(matchSuccessSapData,partialMatchSapDate,detailEntities);
    }

    private void updateSapDeliveryMatchState(List<SapDeliveryDetailEntity> matchSuccessSapData, List<SapDeliveryDetailEntity> partialMatchSapDate,List<SapDeliveryDetailEntity> detailEntities) {
        UpdateWrapper<SapDeliveryEntity> sapDeliveryUpdate = new UpdateWrapper<>();
        sapDeliveryUpdate.lambda().in(SapDeliveryEntity::getMatchState, Constant.ALL_MATCH, Constant.P_MATCH)
                .set(SapDeliveryEntity::getMatchState, Constant.NOT_MATCH);
        dfgDeliveryService.update(sapDeliveryUpdate);

        /**
         * 根据行号 查询是否包含在收货记录里面
         * 如果存在，需要判断是否所有没有提交的都已经能从账单里面找到记录，
         *          如果都能找到，该收货记录设置为全部匹配
         *          如果只找到部分，则该收货记录设置为匹配失败
         *
         */
        //账单对应的全部采购单数据 mblnr:List<SapDeliveryDetailEntity>
        Map<String, List<SapDeliveryDetailEntity>> orderSnMapA = detailEntities.stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getMblnr));

        for (Map.Entry<String, List<SapDeliveryDetailEntity>> stringListEntry : orderSnMapA.entrySet()) {
            List<SapDeliveryDetailEntity> allDetail = stringListEntry.getValue();
            boolean flag = true;
            for (SapDeliveryDetailEntity sapDeliveryDetailEntity : allDetail) {
                List<SapDeliveryDetailEntity> succ = matchSuccessSapData.stream().filter(item -> item.getOrderSn().equals(sapDeliveryDetailEntity.getOrderSn()) && item.getItemNo().equals(sapDeliveryDetailEntity.getItemNo())).collect(Collectors.toList());
                //有一条没有匹配上，就匹配失败
                if (CollectionUtil.isEmpty(succ)) {
                    flag = false;
                }
            }
            if(flag){
                UpdateWrapper<SapDeliveryEntity> successMblnrUpdate = new UpdateWrapper<>();
                successMblnrUpdate.lambda().eq(SapDeliveryEntity::getMblnr, stringListEntry.getKey())
                        .in(SapDeliveryEntity::getSubmitState, 0, 1)
                        .set(SapDeliveryEntity::getMatchState, Constant.ALL_MATCH);
                dfgDeliveryService.update(successMblnrUpdate);
            }else{
                UpdateWrapper<SapDeliveryEntity> successMblnrUpdate = new UpdateWrapper<>();
                successMblnrUpdate.lambda().eq(SapDeliveryEntity::getMblnr, stringListEntry.getKey())
                        .in(SapDeliveryEntity::getSubmitState, 0, 1)
                        .set(SapDeliveryEntity::getMatchState, Constant.P_MATCH);
                dfgDeliveryService.update(successMblnrUpdate);
            }
        }
    }

    private void setMatchSapDataReason(List<DetailMatchSapDataDto> detailMatchSapDataDtos,String purchaseNumber,String goodsCode,DetailMatchSapEnum detailMatchSapEnum ){
        for (DetailMatchSapDataDto matchSapDataDto : detailMatchSapDataDtos) {
            if(matchSapDataDto.getPurchaseNumber().equals(purchaseNumber) && matchSapDataDto.getGoodsCode().equals(goodsCode)){
                matchSapDataDto.setFlag(detailMatchSapEnum.getCode());
                matchSapDataDto.setReason(detailMatchSapEnum.getName());
            }
        }
    }

    private ShopPurchaseSubOrderDetail filterOrderDetailByGoodsCode(List<ShopPurchaseSubOrder> shopPurchaseSubOrderList,List<ShopPurchaseSubOrderDetail> shopPurchaseSubOrderDetailList,String purchaseNumber,String goodsCode){
        List<String> orderNumbers = shopPurchaseSubOrderList.stream().filter(item -> item.getPurchaseNumber().equals(purchaseNumber)).map(ShopPurchaseSubOrder::getOrderNumber).collect(Collectors.toList());
        List<ShopPurchaseSubOrderDetail> purchaseNumberOrderDetail = shopPurchaseSubOrderDetailList.stream().filter(item -> orderNumbers.contains(item.getOrderNumber())).collect(Collectors.toList());
        ShopPurchaseSubOrderDetail shopPurchaseSubOrderDetail = purchaseNumberOrderDetail.stream().filter(item -> item.getGoodsCode().equals(goodsCode)).collect(Collectors.toList()).stream().findFirst().get();
        return shopPurchaseSubOrderDetail;
    }

    private SapDeliveryDetailEntity filterOrderDetailByItemNo(List<SapDeliveryDetailEntity> collectItemNoList, String purchaseNumber, Integer itemNo) {
        List<SapDeliveryDetailEntity> filterPurchaseNumberList = collectItemNoList.stream().filter(item -> item.getOrderSn().equals(purchaseNumber)).collect(Collectors.toList());
        List<SapDeliveryDetailEntity> sapDeliveryDetailEntities = filterPurchaseNumberList.stream().filter(item -> item.getItemNo().equals(itemNo.toString())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(sapDeliveryDetailEntities)){
            return null;
        }
        return sapDeliveryDetailEntities.get(0);
    }

    private List<DetailMatchSapDataDto> collectGoodsCodeList(List<DetailMatchSapDataDto> detailMatchSapDataDtos) {
        //汇总商品
        List<DetailMatchSapDataDto> collectGoodsCodeList = new ArrayList<>();
        Map<String, List<DetailMatchSapDataDto>> purchaseNumberMap = detailMatchSapDataDtos.stream().collect(Collectors.groupingBy(DetailMatchSapDataDto::getPurchaseNumber));
        for (Map.Entry<String, List<DetailMatchSapDataDto>> stringListEntry : purchaseNumberMap.entrySet()) {
            Map<String, List<DetailMatchSapDataDto>> goodsCodeGroupBy = stringListEntry.getValue().stream().collect(Collectors.groupingBy(DetailMatchSapDataDto::getGoodsCode));
            Map<String, BigDecimal> goodsCheckedNum = new HashMap<>();
            for (Map.Entry<String, List<DetailMatchSapDataDto>> listEntry : goodsCodeGroupBy.entrySet()) {
                BigDecimal checkedNum = listEntry.getValue().stream().map(DetailMatchSapDataDto::getCheckedNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                goodsCheckedNum.put(listEntry.getKey(), checkedNum);
            }
            for (Map.Entry<String, BigDecimal> stringBigDecimalEntry : goodsCheckedNum.entrySet()) {
                DetailMatchSapDataDto detailMatchSapDataDto3 = new DetailMatchSapDataDto();
                detailMatchSapDataDto3.setPurchaseNumber(stringListEntry.getKey());
                detailMatchSapDataDto3.setGoodsCode(stringBigDecimalEntry.getKey());
                detailMatchSapDataDto3.setCheckedNum(stringBigDecimalEntry.getValue());
                collectGoodsCodeList.add(detailMatchSapDataDto3);
            }
        }
        return collectGoodsCodeList;
    }

    private List<SapDeliveryDetailEntity> collectItemNoList(List<SapDeliveryDetailEntity> sapDeliveryDetailEntities) {
        //汇总商品
        List<SapDeliveryDetailEntity> collectItemNoList = new ArrayList<>();
        Map<String, List<SapDeliveryDetailEntity>> purchaseNumberMap = sapDeliveryDetailEntities.stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getOrderSn));
        for (Map.Entry<String, List<SapDeliveryDetailEntity>> stringListEntry : purchaseNumberMap.entrySet()) {
            Map<String, List<SapDeliveryDetailEntity>> goodsCodeGroupBy = stringListEntry.getValue().stream().collect(Collectors.groupingBy(SapDeliveryDetailEntity::getItemNo));
            Map<String, BigDecimal> goodsCheckedNum = new HashMap<>();
            for (Map.Entry<String, List<SapDeliveryDetailEntity>> listEntry : goodsCodeGroupBy.entrySet()) {
                BigDecimal checkedNum = listEntry.getValue().stream().map(item -> Convert.toBigDecimal(item.getMenge())).reduce(BigDecimal.ZERO, BigDecimal::add);
                goodsCheckedNum.put(listEntry.getKey(), checkedNum);
            }
            for (Map.Entry<String, BigDecimal> stringBigDecimalEntry : goodsCheckedNum.entrySet()) {
                SapDeliveryDetailEntity sapDeliveryDetailEntity = new SapDeliveryDetailEntity();
                sapDeliveryDetailEntity.setOrderSn(stringListEntry.getKey());
                sapDeliveryDetailEntity.setItemNo(stringBigDecimalEntry.getKey());
                sapDeliveryDetailEntity.setMenge(Convert.toStr(stringBigDecimalEntry.getValue()));
                collectItemNoList.add(sapDeliveryDetailEntity);
            }
        }
        return collectItemNoList;
    }

    private Map<Integer,List<SapIndexOrderDetailEntity>> getIndexDetailByDelivery(List<SapDeliveryVo> deliveryVos){
        List<Integer> sddIds = deliveryVos.stream().flatMap(item->item.getSapDeliveryDetailVos().stream().map(SapDeliveryDetailVo::getSddId)).collect(Collectors.toList());
        QueryWrapper<SapIndexOrderDetailEntity> detailEntityQueryWrapper = new QueryWrapper<>();
        detailEntityQueryWrapper.lambda().in(SapIndexOrderDetailEntity::getSddId,sddIds);
        List<SapIndexOrderDetailEntity> sapDeliveryDetailEntities = sapIndexOrderDetailService.list(detailEntityQueryWrapper);
        return sapDeliveryDetailEntities.stream().collect(Collectors.groupingBy(SapIndexOrderDetailEntity::getSddId));
    }


}

