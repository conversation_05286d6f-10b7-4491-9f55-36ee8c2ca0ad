package com.ly.yph.api.settlement.supplier.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel
@Data
public class SupplierInvoiceByDetailIdDto {

    @NotNull(message = "账单明细不能为空！")
    private Long billId;

    @NotNull(message = "明细数据不能为空！")
    private List<Long> detailIdList;

    @NotNull(message = "明细类型不能为空！")
    private Integer billInvoiceType;
}
