package com.ly.yph.api.goods.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ly.yph.api.customization.common.CompanyConstant;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.customization.common.Constant;
import com.ly.yph.api.customization.dto.SaveShopMaraParamDto;
import com.ly.yph.api.customization.entity.SapIndexOrderEntity;
import com.ly.yph.api.goods.dto.MaraQueryDto;
import com.ly.yph.api.goods.dto.QuerySeekMaterialDto;
import com.ly.yph.api.goods.dto.ShopMaterialRelationUpdateDto;
import com.ly.yph.api.goods.dto.ShopSapMaraDto;
import com.ly.yph.api.goods.entity.ShopGoods;
import com.ly.yph.api.goods.entity.ShopMaterialRelationEntity;
import com.ly.yph.api.goods.mapper.ShopMaterialRelationMapper;
import com.ly.yph.api.goods.vo.MaraImportExcelVo;
import com.ly.yph.api.goods.vo.MaraImportRespVo;
import com.ly.yph.api.goods.vo.ShopMaterialRelationVo;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.seeksource.entity.ShopSeekQuotedSku;
import com.ly.yph.api.seeksource.service.ShopSeekQuotedSkuService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.yph.core.base.ServiceExceptionUtil.exception;
import static com.ly.yph.core.base.exception.SystemErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2022年11月16日
 */
@Service
@Slf4j
public class ShopMaterialRelationService extends ServiceImpl<ShopMaterialRelationMapper, ShopMaterialRelationEntity> {

    @Autowired
    private SystemOrganizationService systemOrganizationService;


    @Autowired
    private ShopMaterialRelationMapper shopMaterialRelationMapper;

    @Resource
    private ShopGoodsService goodsService;

    @Resource
    private ShopSeekQuotedSkuService shopSeekQuotedSkuService;

    /**
     * 分页查询
     *
     * @param pageReq
     * @param maraQueryDto
     * @return
     */
    public PageResp<ShopMaterialRelationVo> queryPage(PageReq pageReq, MaraQueryDto maraQueryDto) {
        LoginUser loginUser = LocalUserHolder.get();
        maraQueryDto.setCompanyCode(loginUser.getEntityOrganizationCode());
        IPage<ShopMaterialRelationVo> iPage = shopMaterialRelationMapper.queryPage(DataAdapter.adapterPageReq(pageReq), maraQueryDto);
        return DataAdapter.adapterPage(iPage, ShopMaterialRelationVo.class);
    }

    public void updateMara(ShopMaterialRelationUpdateDto updateDto) {
        ShopMaterialRelationEntity shopMaterialRelationEntity = DataAdapter.convert(updateDto, ShopMaterialRelationEntity.class);
        this.updateById(shopMaterialRelationEntity);
    }

    public void createMara(ShopMaterialRelationUpdateDto createDto) {
        ShopMaterialRelationEntity shopMaterialRelationEntity = DataAdapter.convert(createDto, ShopMaterialRelationEntity.class);
        QueryWrapper<ShopMaterialRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, shopMaterialRelationEntity.getCompanyCode())
                .eq(ShopMaterialRelationEntity::getMaraMatnr, shopMaterialRelationEntity.getMaraMatnr())
                .eq(ShopMaterialRelationEntity::getGoodsCode, shopMaterialRelationEntity.getGoodsCode())
                .eq(ShopMaterialRelationEntity::getIsDel, 0);
        Long count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw exception(MARA_EXISTS);
        }
        LoginUser loginUser = LocalUserHolder.get();
        shopMaterialRelationEntity.setOrganizationId(loginUser.getEntityOrganizationId());
        shopMaterialRelationEntity.setCompanyCode(loginUser.getEntityOrganizationCode());
        if(CompanyConstant.LTDMS_FACTORY_CODE.equals(createDto.getMaraWerks())){
            shopMaterialRelationEntity.setGoodsPushState(0);
        }
        this.save(shopMaterialRelationEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public MaraImportRespVo batchImportMara(List<MaraImportExcelVo> maraImportExcelVoList) {
        if (CollUtil.isEmpty(maraImportExcelVoList)) {
            throw exception(MARA_IMPORT_LIST_IS_EMPTY);
        }
        LoginUser loginUser = LocalUserHolder.get();
        log.info("导入物料关系数据，当前登录用户：" + loginUser.toString());
        MaraImportRespVo respVO = MaraImportRespVo.builder()
                .failureList(new LinkedHashMap<>())
                .build();

        for (MaraImportExcelVo data : maraImportExcelVoList) {
            log.info("保存物料数据{}：",data);
            Boolean flag = true;
            Integer index = data.getIndex();
            String maraMatnr = data.getMaraMatnr();
            if (StrUtil.isBlank(maraMatnr)) {
                respVO.getFailureList()
                        .put(index, MARA_MARA_SN_IS_BLANK.getMsg());
                flag = false;
            }
            String goodsCode = data.getGoodsCode();
            if (StrUtil.isBlank(goodsCode)) {
                respVO.getFailureList()
                        .put(index, MARA_GOODS_CODE_IS_BLANK.getMsg());
                flag = false;
            }
            QueryWrapper<ShopMaterialRelationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, loginUser.getEntityOrganizationCode())
                    .eq(ShopMaterialRelationEntity::getMaraMatnr, maraMatnr)
                    .eq(ShopMaterialRelationEntity::getIsDel, 0);
            Long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                UpdateWrapper<ShopMaterialRelationEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, loginUser.getEntityOrganizationCode())
                        .eq(ShopMaterialRelationEntity::getMaraMatnr, maraMatnr).eq(ShopMaterialRelationEntity::getIsDel, 0)
                        .set(ShopMaterialRelationEntity::getGoodsCode,goodsCode);
                this.update(updateWrapper);
                flag = false;
            }
            if (flag) {
                ShopMaterialRelationEntity maraEntity = DataAdapter.convert(data, ShopMaterialRelationEntity.class);
                maraEntity.setOrganizationId(loginUser.getEntityOrganizationId());
                maraEntity.setCompanyCode(loginUser.getEntityOrganizationCode());
                if(CompanyConstant.LTDMS_FACTORY_CODE.equals(data.getMaraWerks())){
                    maraEntity.setGoodsPushState(0);
                }
                this.save(maraEntity);
            }
        }
        return respVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public ServiceResult<?> syncShopMaterial(List<ShopSapMaraDto> shopSapMaraDtoList) {
        List<ShopMaterialRelationEntity> shopMaterialRelationEntities = Lists.newArrayList();

        //默认写死
        List<String> dfpvWerkdsList = Arrays.asList("1100","1200","1230","1240","1250","1260","1270","1300","2100","3100","910A");
        List<String> dnaWerksList= Arrays.asList("910B","910C","9184","9190","9196","919A");

        for (ShopSapMaraDto shopSapMaraDto : shopSapMaraDtoList) {
            if(dfpvWerkdsList.contains(shopSapMaraDto.getWerks())){
                shopSapMaraDto.setCompanyCode(CompanyEnum.DFPV.getCompanyCode());
            }else if(dnaWerksList.contains(shopSapMaraDto.getWerks())){
                shopSapMaraDto.setCompanyCode(CompanyEnum.DNA.getCompanyCode());
            }else{
                log.info("【syncShopMaterial】物料未查询到工厂对应的信息，跳过！！！物料信息：" + JSONUtil.toJsonStr(shopSapMaraDto));
                continue;
            }
            QueryWrapper<ShopMaterialRelationEntity> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(ShopMaterialRelationEntity::getMaraMatnr, shopSapMaraDto.getMaraMatnr())
                    .eq(ShopMaterialRelationEntity::getCompanyCode, shopSapMaraDto.getCompanyCode())
                    .eq(ShopMaterialRelationEntity::getIsDel, 0);
            List<ShopMaterialRelationEntity> mara = this.list(queryWrapper);
            //如果存在就删除
            if (!CollectionUtils.isEmpty(mara)) {
//                UpdateWrapper<ShopMaterialRelationEntity> updateWrapper = new UpdateWrapper<>();
//                updateWrapper.lambda().eq(ShopMaterialRelationEntity::getMaraMatnr, shopSapMaraDto.getMaraMatnr())
//                        .eq(ShopMaterialRelationEntity::getCompanyCode, shopSapMaraDto.getCompanyCode()).set(ShopMaterialRelationEntity::getIsDel, 1);
//                this.update(updateWrapper);
                log.info("【syncShopMaterial】已存在相同物料，跳过！！！物料信息：" + JSONUtil.toJsonStr(shopSapMaraDto));
                continue;
            }
            ShopMaterialRelationEntity shopMaterialRelation = convShopMaterial(shopSapMaraDto);
            shopMaterialRelationEntities.add(shopMaterialRelation);
        }
        this.saveBatch(shopMaterialRelationEntities);
        return ServiceResult.succ();
    }

    private ShopMaterialRelationEntity convShopMaterial(ShopSapMaraDto shopSapMaraDto) {
        ShopMaterialRelationEntity shopMaterialRelationEntity = new ShopMaterialRelationEntity();
        BeanUtil.copyProperties(shopSapMaraDto, shopMaterialRelationEntity);

        QueryWrapper<SystemOrganization> orgQueryMapper = new QueryWrapper<>();
        orgQueryMapper.lambda().eq(SystemOrganization::getCode, shopSapMaraDto.getCompanyCode());
        SystemOrganization systemOrganization = systemOrganizationService.getOne(orgQueryMapper);
        shopMaterialRelationEntity.setOrganizationId(systemOrganization.getId());
        shopMaterialRelationEntity.setMaraWgbez(shopSapMaraDto.getWgbez());
        shopMaterialRelationEntity.setMaraWerks(shopSapMaraDto.getWerks());
        shopMaterialRelationEntity.setMaraMaktx(shopSapMaraDto.getMaktMaktx());
        shopMaterialRelationEntity.setIsDel(shopSapMaraDto.getMaraLvorm().equals("X") ? 1 : 0);
        return shopMaterialRelationEntity;
    }

    public List<String> selectByGoodsCode(Collection<String> list, String entityOrganizationCode) {
        return shopMaterialRelationMapper.selectByGoodsCode(list,entityOrganizationCode);
    }

    public Map<String, String> getmaraByCodes(List<String> goodsCodeList, String werksCode, String companyCode) {
        Map<String, String> maraMap = Maps.newHashMap();
        LambdaQueryWrapper<ShopMaterialRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopMaterialRelationEntity::getMaraWerks,werksCode)
                .eq(ShopMaterialRelationEntity::getCompanyCode,companyCode)
                .in(ShopMaterialRelationEntity::getGoodsCode,goodsCodeList)
                .eq(ShopMaterialRelationEntity::getIsDel,0);
        List<ShopMaterialRelationEntity> materialRelationEntities = this.list(queryWrapper);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(materialRelationEntities)) {
            return maraMap;
        }
        return materialRelationEntities.stream()
                .collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr));
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveMaterRelation(List<SaveShopMaraParamDto> saveShopMaraParamDtos){
        //把已有的查询出来
        List<String> maraMatnrs = saveShopMaraParamDtos.stream().map(SaveShopMaraParamDto::getMaraMatnr).collect(Collectors.toList());
        LambdaQueryWrapper<ShopMaterialRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopMaterialRelationEntity::getMaraWerks,saveShopMaraParamDtos.get(0).getMaraWerks()).in(ShopMaterialRelationEntity::getMaraMatnr,maraMatnrs);
        List<ShopMaterialRelationEntity> queryList = this.list(queryWrapper);
        //删除已有的
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(queryList)) {
            List<String> deleteMaraMatnrList = queryList.stream().map(ShopMaterialRelationEntity::getMaraMatnr).collect(Collectors.toList());
            QueryWrapper<ShopMaterialRelationEntity> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.lambda().eq(ShopMaterialRelationEntity::getMaraWerks,queryList.get(0).getMaraWerks()).in(ShopMaterialRelationEntity::getMaraMatnr,deleteMaraMatnrList);
            shopMaterialRelationMapper.delete(queryWrapper);
        }
        //批量插入
        List<ShopMaterialRelationEntity> list = Lists.newArrayList();
        for (SaveShopMaraParamDto saveShopMaraParamDto : saveShopMaraParamDtos) {
            ShopMaterialRelationEntity shopMaterialRelationEntity = new ShopMaterialRelationEntity();
            shopMaterialRelationEntity.setMaraMatnr(saveShopMaraParamDto.getMaraMatnr());
            shopMaterialRelationEntity.setGoodsCode(saveShopMaraParamDto.getGoodsCode());
            shopMaterialRelationEntity.setMaraMaktx(saveShopMaraParamDto.getMaktMaktx());
            shopMaterialRelationEntity.setMaraMeins(saveShopMaraParamDto.getMaraMeins());
            shopMaterialRelationEntity.setMaraZmein(saveShopMaraParamDto.getMaraZmein());
            shopMaterialRelationEntity.setMaraNormt(saveShopMaraParamDto.getMaraNormt());
            shopMaterialRelationEntity.setMaraMatkl(saveShopMaraParamDto.getMaraMatkl());
            shopMaterialRelationEntity.setMaraWgbez(saveShopMaraParamDto.getMaraWgbez());
            shopMaterialRelationEntity.setMaraMtart(saveShopMaraParamDto.getMaraMtart());
            shopMaterialRelationEntity.setMaraUnit(saveShopMaraParamDto.getMaraUnit());
            shopMaterialRelationEntity.setMaraWerks(saveShopMaraParamDto.getMaraWerks());
            shopMaterialRelationEntity.setSourceSysId(saveShopMaraParamDto.getSourceSysId());
            shopMaterialRelationEntity.setOrganizationId(saveShopMaraParamDto.getOrganizationId());
            shopMaterialRelationEntity.setCompanyCode(saveShopMaraParamDto.getCompanyCode());
            list.add(shopMaterialRelationEntity);
        }
        this.saveBatch(list);
    }

    /**
     * 根据物料编码查询绑定关系
     *
     * @param maraCodes
     * @param werksCode
     * @param companyCode
     * @return
     */
    public Map<String, ShopMaterialRelationEntity> getGoodsCodeByMara(List<String> maraCodes, String werksCode, String companyCode) {
        Map<String, ShopMaterialRelationEntity> maraMap = Maps.newHashMap();
        LambdaQueryWrapper<ShopMaterialRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopMaterialRelationEntity::getMaraWerks, werksCode)
                .eq(ShopMaterialRelationEntity::getCompanyCode, companyCode)
                .in(ShopMaterialRelationEntity::getMaraMatnr, maraCodes)
                .eq(ShopMaterialRelationEntity::getIsDel, 0);
        List<ShopMaterialRelationEntity> materialRelationEntities = list(queryWrapper);
        if (CollectionUtil.isEmpty(materialRelationEntities)) {
            return maraMap;
        }
        return materialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getMaraMatnr, Function.identity()));
    }

    public void monitoringSeekMater(){
        List<QuerySeekMaterialDto> seekMaterialDtos = shopMaterialRelationMapper.queryNoCreateMaterial();
        if(CollUtil.isEmpty(seekMaterialDtos)){
            log.info("【monitoringSeekMater】未查询到需要创建物料的信息");
            return;
        }
        SystemOrganization organization = systemOrganizationService.getOrganizationByCode(CompanyEnum.VOYAH.getCompanyCode());
        List<ShopMaterialRelationEntity> relationEntities = com.google.common.collect.Lists.newArrayList();
        List<Long> quoteSkuIds = Lists.newArrayList();
        List<Long> errorQuoteSkuIds = Lists.newArrayList();
        for (QuerySeekMaterialDto seekMaterialDto : seekMaterialDtos) {
            ShopGoods shopGoods = goodsService.selectOneBySkuAndSupplier(seekMaterialDto.getGoodsSku(), seekMaterialDto.getSupplierCode());
            if(ObjectUtil.isEmpty(shopGoods)){
                log.info("【monitoringSeekMater】根据SKU和供应商编码未查询到商品信息");
                errorQuoteSkuIds.add(seekMaterialDto.getId());
                continue;

            }
            List<ShopMaterialRelationEntity> list = this.list(new LambdaQueryWrapperX<ShopMaterialRelationEntity>()
                    .eq(ShopMaterialRelationEntity::getCompanyCode, organization.getCode())
                    .eq(ShopMaterialRelationEntity::getGoodsCode, shopGoods.getGoodsCode()));
            if(CollUtil.isNotEmpty(list)){
                log.info("【monitoringSeekMater】根据goodsCode查询物料关系，已存在关系，本物料跳过");
                errorQuoteSkuIds.add(seekMaterialDto.getId());
                continue;
            }
            ShopMaterialRelationEntity relationEntity1 = new ShopMaterialRelationEntity();
            relationEntity1.setMaraMatnr(seekMaterialDto.getMaraMatnr());
            relationEntity1.setGoodsCode(shopGoods.getGoodsCode());
            relationEntity1.setMaraWerks("1227");
            relationEntity1.setCompanyCode(CompanyEnum.VOYAH.getCompanyCode());
            relationEntity1.setOrganizationId(organization.getId());
            relationEntity1.setSourceSysId(Constant.SOURCE_VOYAH_SRM);
            relationEntities.add(relationEntity1);

            ShopMaterialRelationEntity relationEntity2 = new ShopMaterialRelationEntity();
            relationEntity2.setMaraMatnr(seekMaterialDto.getMaraMatnr());
            relationEntity2.setGoodsCode(shopGoods.getGoodsCode());
            relationEntity2.setMaraWerks("1226");
            relationEntity2.setCompanyCode(CompanyEnum.VOYAH.getCompanyCode());
            relationEntity2.setOrganizationId(organization.getId());
            relationEntity2.setSourceSysId(Constant.SOURCE_VOYAH_SRM);
            relationEntities.add(relationEntity2);

            quoteSkuIds.add(seekMaterialDto.getId());
        }

        if(CollUtil.isNotEmpty(errorQuoteSkuIds)){
            UpdateWrapper<ShopSeekQuotedSku>upq = new UpdateWrapper<>();
            upq.lambda().in(ShopSeekQuotedSku::getId,errorQuoteSkuIds).set(ShopSeekQuotedSku::getCreateMaterial,2);
            shopSeekQuotedSkuService.update(upq);
        }
        if(CollUtil.isNotEmpty(quoteSkuIds)){
            UpdateWrapper<ShopSeekQuotedSku>upq = new UpdateWrapper<>();
            upq.lambda().in(ShopSeekQuotedSku::getId,quoteSkuIds).set(ShopSeekQuotedSku::getCreateMaterial,1);
            shopSeekQuotedSkuService.update(upq);
        }
        if (CollUtil.isNotEmpty(relationEntities)) {
            this.saveBatch(relationEntities);
        }
    }
}
