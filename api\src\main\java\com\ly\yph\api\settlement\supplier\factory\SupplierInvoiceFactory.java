package com.ly.yph.api.settlement.supplier.factory;

import com.ly.yph.api.settlement.common.enums.BillInvoiceTypeEnum;
import com.ly.yph.core.base.exception.types.ParameterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class SupplierInvoiceFactory {

    @Resource
    private SupplierBillDetailStrategy supplierBillDetailStrategy;

    @Resource
    private SupplierPostageDetailStrategy supplierPostageDetailStrategy;

    public SupplierInvoiceStrategy getSupplierInvoiceStrategy(Integer billInvoiceType) {

        if (BillInvoiceTypeEnum.BILL_DETAIL.getCode().equals(billInvoiceType)) {
            return supplierBillDetailStrategy;

        } else if (BillInvoiceTypeEnum.POSTAGE_DETAIL.getCode().equals(billInvoiceType)) {
            return supplierPostageDetailStrategy;

        } else {
            throw new ParameterException("结算明细类型异常");
        }

    }


}
