package com.ly.yph.api.electsign.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ly.yph.api.goods.enums.WorkFlowStatusEnum;
import com.ly.yph.api.order.common.SrmPathConstants;
import com.ly.yph.api.order.dto.srm.SrmResult;
import com.ly.yph.api.organization.entity.SystemContractRenewalEntity;
import com.ly.yph.api.organization.service.SystemContractRenewalService;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.supplier.vo.ShopSupplierSyncInfoVo;
import com.ly.yph.api.utils.SrmHttpUtil;
import com.ly.yph.api.workflow.entity.WorkflowAuditRecord;
import com.ly.yph.api.workflow.mapper.WorkflowAuditRecordMapper;
import com.ly.yph.core.base.DateUtils;
import com.ly.yph.core.base.exception.types.HttpException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ContractSyncService {

    @Resource
    ShopSupplierService shopSupplierService;

    @Resource
    SystemContractRenewalService systemContractRenewalService;

    @Resource
    private WorkflowAuditRecordMapper workflowAuditRecordMapper;

    @Resource
    private SrmHttpUtil srmHttpUtil;

    public JSONObject syncContractToSRM(Long contractId){
        SystemContractRenewalEntity contractRenewal = systemContractRenewalService.getById(contractId);
        return syncContractToSRM(contractRenewal);
    }

    public void autoSyncContractToSRM(){
        log.info("开始自动同步合同到srm...");
        List<SystemContractRenewalEntity> contractRenewalList = systemContractRenewalService.getSyncContract();
        for(SystemContractRenewalEntity contractRenewal : contractRenewalList){
            syncContractToSRM(contractRenewal);
        }
    }

    public JSONObject syncContractToSRM(SystemContractRenewalEntity contractRenewal){

        ShopSupplierSyncInfoVo shopSupplier = shopSupplierService.getSupplierInfoById(contractRenewal.getSupplierId());
        if(shopSupplier==null){
            log.info("未找到供应商信息");
            return null;
        }

        JSONArray approve = this.getAuditRecord(contractRenewal.getId());

        JSONObject contract =  this.getContract(contractRenewal);

        JSONObject param = new JSONObject();
        param.set("supplier", shopSupplier);
        param.set("contract", contract);
        param.set("approve", approve);

        String paramStr = param.toString();

        log.info("paramStr: {}", paramStr);
        try{
            SrmResult<?> srmResult = srmHttpUtil.doPost(SrmPathConstants.SRM_CONTRACT_SYNC_NAME, paramStr, null);
            if ("-1".equals(srmResult.getResult())) {
                log.error("usePrice error:{}", JSONUtil.toJsonStr(srmResult));
                contractRenewal.setIsSync(3);
                contractRenewal.setSignDesc(srmResult.getMsg());
            }else{
                contractRenewal.setIsSync(2);
            }
            contractRenewal.setUpdateTime(new Date());
            systemContractRenewalService.updateById(contractRenewal);
           return null;
        }catch (Exception e){
            log.error("合同数据同步到srm失败: {}", e.getMessage());
            return null;
        }
    }

    private JSONArray getAuditRecord(Long id) {
        List<WorkflowAuditRecord> recordList = workflowAuditRecordMapper.selectAuditRecord(id);
        JSONArray approve = new JSONArray();
        if(CollectionUtil.isNotEmpty(recordList)){
            Collections.reverse(recordList);
            for(WorkflowAuditRecord record : recordList){
                JSONObject item = new JSONObject();
                item.set("approveTime", DateUtils.format(record.getCreatedAt(), DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
                item.set("approver", record.getUserName());
                item.set("node", record.getStepName());
                item.set("status", WorkFlowStatusEnum.getByCode(record.getStatus()).getType());
                item.set("remark", record.getComment());
                approve.add(item);
            }
        }
        return approve;
    }

    private JSONObject getContract(SystemContractRenewalEntity contractRenewal) {
        JSONObject contract = new JSONObject();
        contract.set("contractName",contractRenewal.getContractName());
        contract.set("contractCode",contractRenewal.getContractCode());
        contract.set("initiatorSignCompany",contractRenewal.getCompanyName());
        contract.set("signatorySignCompany",contractRenewal.getSupplierName());
        contract.set("eVisa", 1);
        contract.set("beginTime",contractRenewal.getValidityStart());
        contract.set("endTime",contractRenewal.getValidityEnd());
        contract.set("attachmentUrl",contractRenewal.getAttachmentUrl());
        contract.set("authenticationSource", contractRenewal.getAuthenticationSource());
        return contract;
    }
}
