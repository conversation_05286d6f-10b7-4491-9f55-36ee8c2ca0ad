package com.ly.yph.api.order.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/3/22 09:00
 **/
@Data
@ApiModel("商品池管理Vo对象")
public class ShopCartVo implements Serializable {
    private static final long serialVersionUID = -1138376716418666236L;

    /**
     * 购物车ID
     */
    @ApiModelProperty(value = "购物车ID", dataType = "long")
    private Long cartId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", dataType = "Long")
    private Long userId;
    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID", dataType = "java.lang.Long")
    private Long supplierId;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码", dataType = "String")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称", dataType = "String")
    private String supplierName;
    /**
     * 供应商来源
     */
    @ApiModelProperty(value = "供应商来源", dataType = "String")
    private String supplierDataSource;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id", dataType = "Long")
    private Long goodsId;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码", dataType = "String")
    private String goodsCode;

    @ApiModelProperty(value = "积分上限")
    private BigDecimal integralCeiling;

    @ApiModelProperty("商品SKU")
    private String goodsSku;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", dataType = "String")
    private String goodsName;
    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述", dataType = "String")
    private String goodsDesc;

    @ApiModelProperty("税率")
    private Integer taxRate;
    /**
     * 商品起订量
     */
    @ApiModelProperty(value = "商品起订量", dataType = "Integer")
    private Integer goodsMoq;
    /**
     * 规格内容
     */
    @ApiModelProperty(value = "规格内容", dataType = "String")
    private String specInfo;
    /**
     * 商品价格
     */
    @ApiModelProperty(value = "商品价格", dataType = "String")
    private BigDecimal goodsSalePrice;
    /**
     * 加购数量
     */
    @ApiModelProperty(value = "加购数量", dataType = "Long")
    private Long goodsNum;
    /**
     * (小数)
     */
    @ApiModelProperty(value = "加购数量(小数)", example = "1.1", dataType = "decimal")
    private BigDecimal goodsNumDecimal;

    /**
     * 商品上下架状态 0:下架 1:上架
     */
    @ApiModelProperty(value = "上下架状态", dataType = "Long")
    private Long shelvesState;

    @ApiModelProperty("商品认证方式")
    private String authMode;

    @ApiModelProperty("商品可售标签")
    private String goodsLabel;

    @ApiModelProperty("价格监控(0:价格正常 其他:不正常)")
    private Integer priceMonitorResult;

    @ApiModelProperty("是否可销售 0:否 1:是 默认1")
    private Long isSale;

    @ApiModelProperty("是否超出设置折扣范围 0:否 1:是")
    private Integer discountRange = 0;
    /**
     * 商品图片
     */
    @ApiModelProperty(value = "商品图片", dataType = "String")
    private String goodsImage;
    /**
     * 商品类型ID
     */
    @ApiModelProperty(value = "商品类型ID", dataType = "Integer")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "商品类型(1:实物商品 2:虚拟商品 3:服务) GoodsModelEnum枚举类")
    private Integer goodsModel;

    @ApiModelProperty(value = "结算方式 0非平台结算 1平台结算")
    private Integer settlementType;

    @ApiModelProperty("合同号")
    private String contractNumber;

    @ApiModelProperty("岚图合同号")
    private String voyahContractNumber;
    @ApiModelProperty("合同ID")
    private String contractId;

    @ApiModelProperty("购物车类型:0普通加购;1批量导入购物车")
    private Integer cartType;

    /**
     * 商品使用的活动积分类型
     */
    @ApiModelProperty(value = "商品使用的活动积分类型", dataType = "String")
    private String goodsIntegralType;
    /**
     * 商品类型名称
     */
    @ApiModelProperty(value = "商品类型ID", dataType = "String")
    private String goodsTypeName;
    /**
     * 未税价格
     */
    @ApiModelProperty(value = "未税价格", dataType = "String")
    private BigDecimal goodsSaleNakedPrice;
    /**
     * 含税价-合计
     */
    @ApiModelProperty(value = "含税价-小计", dataType = "String")
    private BigDecimal goodsSaleFinalPrice;
    /**
     * 未税价-合计
     */
    @ApiModelProperty(value = "未税价-小计", dataType = "String")
    private BigDecimal goodsSaleNakedFinalPrice;

    @ApiModelProperty(value = "SAP物料编码", dataType = "String")
    private String maraMatnr;

    @ApiModelProperty("异常信息")
    private String errMessage;

    @ApiModelProperty("待询价异常")
    private String msg;

    @ApiModelProperty(value = "货币代码")
    private String currencyCode;

    @ApiModelProperty(value = "货币名称")
    private String currencyName;

    @ApiModelProperty("专区Id")
    private Long goodsZoneId;

    @ApiModelProperty(value = "专区编码")
    private String zoneCode;

    @ApiModelProperty(value = "专区名称")
    private String zoneName;

    @ApiModelProperty(value = "购物车混合提交 0否 1是")
    private String cartMergeSubmit;

    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    @TableField(value = "delivery_time")
    @ApiModelProperty(value = "参考发货天数")
    private Integer deliveryTime;

    @ApiModelProperty(value = "商品关联专区小类", dataType = "String")
    private String cartZoneGoodsType;

    @ApiModelProperty(value = "商品多规格同款表同款编码")
    private String sameCode;

    @ApiModelProperty(value = "商品多规格数据")
    private Map<String, Object> sameSpecMap;


    @ApiModelProperty("虚拟商品订单类型 1：直冲 2：卡密")
    private String virtualType;
}
