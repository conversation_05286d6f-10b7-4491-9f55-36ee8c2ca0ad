package com.ly.yph.api.settlement.supplier.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel
public class RemoveToNextMonthDto {

    @ApiModelProperty("账单明细id")
    @NotNull(message = "明细数据不能为空！")
    private Long detailId;

    @ApiModelProperty("账单id")
    @NotNull(message = "账单数据不能为空！")
    private Long billId;

    @ApiModelProperty("操作类型 0：移月 1：撤销移月")
    @NotNull(message = "非法操作！")
    private Integer type;

    @NotNull(message = "明细类型不能为空！")
    @ApiModelProperty("tab类型 0：账单明细 1：邮费明细")
    private Integer billInvoiceType;
}
