package com.ly.yph.api.goods.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.yph.api.flowable.components.LiteFlowConfig;
import com.ly.yph.api.flowable.context.GoodsRuleContext;
import com.ly.yph.api.flowable.manage.FlowDispatch;
import com.ly.yph.api.goods.dto.DfmallGoodsPoolSave;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.enums.GoodsPoolLevelEnum;
import com.ly.yph.api.goods.es.EsUpdateType;
import com.ly.yph.api.goods.es.manager.GoodsEsProcessor;
import com.ly.yph.api.goods.es.manager.GoodsLogEsProcessor;
import com.ly.yph.api.goods.mapper.LiteFlowGoodsStandardQueueMapper;
import com.ly.yph.api.goods.mapper.ProductMapper;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.goods.vo.DfmallGoodsPoolVo;
import com.ly.yph.api.openapi.dal.mysql.productpool.MioProductPoolMapper;
import com.ly.yph.api.openapi.v1.vo.mesage.MessageTypeEnum;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.supplier.entity.SupplierProhibitCompanyEntity;
import com.ly.yph.api.supplier.mapper.SupplierProhibitCompanyMapper;
import com.ly.yph.api.utils.OpenApiMessageUtil;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.thread.ThreadUtil;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/10 19:41
 */
@Service
@Slf4j
@RefreshScope
public class GoodsFlowManage {

    @Resource
    private ShopGoodsService goodsService;

    @Resource
    private DfmallGoodsPoolService goodsPoolService;
    @Resource
    private MioProductPoolMapper mioProductPoolMapper;
    @Resource
    private FlowDispatch flowDispatch;

    @Resource
    private ThreadPoolExecutor commonIoExecutors;

    @Resource
    private GoodsEsProcessor goodsEsProcessor;

    @Resource
    private DfmallGoodsPoolSubService goodsPoolSubService;

    @Resource
    private ShopGoodsSaleLabelService goodsSaleLabelService;

    @Resource
    private ShopGoodsSaleLabelHistoryService labelHistoryService;

    @Resource
    private GoodsLogEsProcessor goodsLogEsProcessor;
    @Resource
    private OpenApiMessageUtil openApiMessageUtil;
    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private LiteFlowConfig liteFlowConfig;
    @Resource
    private LiteFlowGoodsStandardQueueMapper goodsStandardQueueMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private SupplierProhibitCompanyMapper supplierProhibitCompanyMapper;


    /**
     * 商品循环校验规则引擎
     */
    @Async
    public void goodsFlowCheck(List<String> suppluerList) {
        int page = 1000;
        long count = 0L;

        for (; ; ) {
            //查询
            List<ShopGoods> goodsList = goodsService.getBaseMapper().queryAllByGoodsId(suppluerList, count, page);
            log.info("poolPushProcess-begin,当前已校验商品数量:{}", count);
            this.poolPushProcess(goodsList);

            count += goodsList.size();
            if (goodsList.size() < page) {
                log.info("poolPushProcess 结束,最后一次校验商品数量:{}", goodsList.size());
                break;
            }
        }
        log.info("poolPushProcess-end,处理商品总数量:{}", count);
    }

    public void saveGoodsStandardQueue(List<ShopGoods> goodsList){
        CollectionUtil.split(goodsList,300).forEach(list ->{
            List<String> existCodes = productMapper.getExistByGoodsCode(list.stream().map(ShopGoods::getGoodsCode).collect(Collectors.toList()));
            List<LiteFlowGoodsStandardQueueEntity> queueEntityList = new ArrayList<>();
            list.forEach(g ->{
                LiteFlowGoodsStandardQueueEntity goodsStandardQueue = new LiteFlowGoodsStandardQueueEntity();
                goodsStandardQueue.setGoodsCode(g.getGoodsCode());
                goodsStandardQueue.setGoodsId(g.getGoodsId());
                goodsStandardQueue.setIsStandard(existCodes.contains(g.getGoodsCode()) ? 1L : 0L);
                goodsStandardQueue.setTenantId(g.getTenantId());
                queueEntityList.add(goodsStandardQueue);
            });
            goodsStandardQueueMapper.batchInsert(queueEntityList);
        });
    }

    /**
     * 商品池流程
     *
     * @param goodsList 商品数据
     */
    public void poolPushProcess(List<ShopGoods> goodsList) {
        long startTime = System.currentTimeMillis();  // 记录开始时间
        log.info("goodsLiteFlowBegin Time{},商品总数量:{}", LocalDateTime.now(), goodsList.size());

        // 数据初始化
        Map<DfmallGoodsPoolVo, Set<Long>> upGoods = new ConcurrentHashMap<>();
        Map<DfmallGoodsPoolVo, Set<Long>> downGoods = new ConcurrentHashMap<>();
        List<ShopGoodsSaleLabelEntity> insertLabel = new ArrayList<>();
        List<Long> removeLabel = new ArrayList<>();

        try {
            // 1. 获取平台池（必须存在）
            DfmallGoodsPoolVo platformPool = getPlatformPool();
            if (platformPool == null) return;

            // 2. 预加载其他池数据
            List<DfmallGoodsPoolVo> companyPools = getCompanyPools();
            List<DfmallGoodsPoolVo> zonePools = getZonePools();
            Map<Long, List<DfmallGoodsPoolVo>> zonePoolsByCompany = groupZonePoolsByCompany(zonePools);

            // 3. 构建供应商禁用映射
            Map<String, List<String>> prohibitCompanyMap = buildProhibitCompanyMap(goodsList);

            // 4. 异步处理商品
            CollectionUtil.split(goodsList,100).forEach(batch ->
                    ThreadUtil.executeArrayAsync(() -> batch, goods -> processSingleGoods(goods, platformPool, zonePools, companyPools, zonePoolsByCompany,
                            prohibitCompanyMap, upGoods, downGoods, insertLabel, removeLabel), this.commonIoExecutors));

            // 5. 批量操作
            executeBatchOperations(upGoods, downGoods, insertLabel, removeLabel, goodsList);
        }catch (Exception e){
            log.error("liteFlowProcessError 商品池流程异常", e);
        }
        // 6. 更新ES
        updateEsIndex(goodsList);

        long costTime = System.currentTimeMillis() - startTime;
        log.info("goodsLiteFlowEnd, 耗时: {}s, 商品数量: {}", costTime / 1000.0, goodsList.size());
    }


    // region 数据获取方法
    private DfmallGoodsPoolVo getPlatformPool() {
        List<DfmallGoodsPoolVo> pools = goodsPoolService.getBaseMapper()
                .queryLiteFlowInfoByPool(GoodsPoolLevelEnum.POOL_LEVEL_PLATFORM.getValue(), null, null);
        if (CollectionUtil.isEmpty(pools)) {
            log.warn("平台池不存在");
            return null;
        }
        return pools.get(0);
    }

    private List<DfmallGoodsPoolVo> getCompanyPools() {
        return goodsPoolService.getBaseMapper()
                .queryPlatPoolLiteFlow(GoodsPoolLevelEnum.POOL_LEVEL_COMPANY.getValue());
    }

    private List<DfmallGoodsPoolVo> getZonePools() {
        return goodsPoolService.getBaseMapper()
                .queryPlatPoolLiteFlow(GoodsPoolLevelEnum.POOL_LEVEL_ZONE.getValue());
    }

    private Map<Long, List<DfmallGoodsPoolVo>> groupZonePoolsByCompany(List<DfmallGoodsPoolVo> zones) {
        return zones.stream()
                .filter(v -> v.getCompanyOrgId() != null)
                .collect(Collectors.groupingBy(DfmallGoodsPoolVo::getCompanyOrgId));
    }

    private Map<String, List<String>> buildProhibitCompanyMap(List<ShopGoods> goodsList) {
        List<String> supplierCodes = goodsList.stream()
                .map(ShopGoods::getSupplierCode)
                .distinct()
                .collect(Collectors.toList());

        return supplierProhibitCompanyMapper.queryDisableCompany(supplierCodes).stream()
                .collect(Collectors.groupingBy(
                        SupplierProhibitCompanyEntity::getSupplierCode,
                        Collectors.mapping(SupplierProhibitCompanyEntity::getCompanyCode, Collectors.toList())
                ));
    }

    // region 核心处理逻辑
    private void processSingleGoods(ShopGoods goods,
                                    DfmallGoodsPoolVo platformPool,
                                    List<DfmallGoodsPoolVo> zonePools,
                                    List<DfmallGoodsPoolVo> companyPools,
                                    Map<Long, List<DfmallGoodsPoolVo>> zonePoolsByCompany,
                                    Map<String, List<String>> prohibitCompanyMap,
                                    Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                                    Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                                    List<ShopGoodsSaleLabelEntity> insertLabel,
                                    List<Long> removeLabel) {
        try {
            // 必须通过平台池检查
            if (!processPlatform(goods, platformPool,upGoods, downGoods, insertLabel, removeLabel)) {
                return;
            }

            // 处理企业池及其关联专区池
            processCompanyAndZones(goods, companyPools,
                    prohibitCompanyMap.getOrDefault(goods.getSupplierCode(), Collections.emptyList()),
                    zonePoolsByCompany, upGoods, downGoods, insertLabel, removeLabel);

            // 处理独立专区池（无企业关联）
            processIndependentZones(goods, zonePools, upGoods, downGoods, insertLabel, removeLabel);
        } catch (Exception e) {
            log.error("liteFlowError 商品处理异常: {}", goods.getGoodsCode(), e);
        }
    }

    private boolean processPlatform(ShopGoods goods, DfmallGoodsPoolVo platformPool,
                                    Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                                    Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                                    List<ShopGoodsSaleLabelEntity> insertLabel,
                                    List<Long> removeLabel) {
        Set<Long> platformDown = downGoods.computeIfAbsent(platformPool, k -> ConcurrentHashMap.newKeySet());
        boolean pass = checkRules(goods, platformDown, insertLabel, removeLabel, platformPool);

        if (pass) {
            upGoods.computeIfAbsent(platformPool, k -> ConcurrentHashMap.newKeySet())
                    .add(goods.getGoodsId());
        }
        return pass;
    }

    private void processCompanyAndZones(ShopGoods goods, List<DfmallGoodsPoolVo> companyPools,
                                        List<String> disabledCompanies,
                                        Map<Long, List<DfmallGoodsPoolVo>> zonePoolsByCompany,
                                        Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                                        Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                                        List<ShopGoodsSaleLabelEntity> insertLabel,
                                        List<Long> removeLabel) {
        companyPools.forEach(pool -> {
            Set<Long> companyDown = downGoods.computeIfAbsent(pool, k -> ConcurrentHashMap.newKeySet());
            // 禁用的企业直接下架
            if (disabledCompanies.contains(pool.getCompanyCode())) {
                companyDown.add(goods.getGoodsId());
                return;
            }

            // 正常处理非禁用企业
            boolean pass = checkRules(goods, companyDown, insertLabel, removeLabel, pool);
            if (pass) {
                upGoods.computeIfAbsent(pool, k -> ConcurrentHashMap.newKeySet())
                        .add(goods.getGoodsId());
                processCompanyZones(goods, pool, zonePoolsByCompany, upGoods, downGoods, insertLabel, removeLabel);
            }

        });
    }

    private void processCompanyZones(ShopGoods goods, DfmallGoodsPoolVo companyPool,
                                     Map<Long, List<DfmallGoodsPoolVo>> zonePoolsByCompany,
                                     Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                                     Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                                     List<ShopGoodsSaleLabelEntity> insertLabel,
                                     List<Long> removeLabel) {
        zonePoolsByCompany.getOrDefault(companyPool.getCompanyOrgId(), Collections.emptyList())
                .forEach(zone -> checkZone(goods, zone, upGoods, downGoods, insertLabel, removeLabel));
    }

    private void processIndependentZones(ShopGoods goods,
                                         List<DfmallGoodsPoolVo> zonePoolsByCompany,
                                         Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                                         Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                                         List<ShopGoodsSaleLabelEntity> insertLabel,
                                         List<Long> removeLabel) {
        zonePoolsByCompany.stream()
                .filter(zone -> zone.getCompanyOrgId() == null)
                .forEach(zone -> checkZone(goods, zone, upGoods, downGoods, insertLabel, removeLabel));
    }

    private void checkZone(ShopGoods goods, DfmallGoodsPoolVo zone,
                           Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                           Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                           List<ShopGoodsSaleLabelEntity> insertLabel,
                           List<Long> removeLabel) {
        // 专区池无规则时跳过
        if (!isZoneRequiresCheck(zone)) return;

        Set<Long> zoneDown = downGoods.computeIfAbsent(zone, k -> ConcurrentHashMap.newKeySet());
        boolean pass = checkRules(goods, zoneDown, insertLabel, removeLabel, zone);
        updateStatus(goods, zone, pass, upGoods, downGoods);
    }

    private void updateStatus(ShopGoods goods, DfmallGoodsPoolVo pool, boolean pass,
                              Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                              Map<DfmallGoodsPoolVo, Set<Long>> downGoods) {
        if (pass) {
            upGoods.computeIfAbsent(pool, k -> ConcurrentHashMap.newKeySet())
                    .add(goods.getGoodsId());
        } else {
            downGoods.computeIfAbsent(pool, k -> ConcurrentHashMap.newKeySet())
                    .add(goods.getGoodsId());
        }
    }

    private boolean isZoneRequiresCheck(DfmallGoodsPoolVo zone) {
        return StringUtils.isNotBlank(zone.getLiteFlowName());
    }
    // endregion

    // region 规则核心
    private boolean checkRules(ShopGoods goods, Set<Long> downSet,
                               List<ShopGoodsSaleLabelEntity> insertLabel,
                               List<Long> removeLabel,
                               DfmallGoodsPoolVo pool) {
        // 非平台池且无规则直接通过（专区池已在checkZone过滤）
        if (!isPlatform(pool) && StringUtils.isBlank(pool.getLiteFlowName())) {
            return true;
        }

        GoodsRuleContext context = executeRuleFlow(goods, pool);
        if (context.getCanShelves()) {
            processLabels(goods, pool, insertLabel, removeLabel);
            return true;
        }

        // 规则不通过时的处理
        boolean shouldDown = true;

        // 如果是企业池，检查是否在排除下架列表中
        if (isCompanyPool(pool)) {
            if (liteFlowConfig.getLiteExcludeDown().contains(pool.getCompanyOrgId())) {
                shouldDown = false;
            }
        }

        // 需要下架的情况
        if (shouldDown) {
            downSet.add(goods.getGoodsId());

            // 只记录平台池商品不通过原因
            if (isPlatform(pool)) {
                goods.setShelvesReason(context.getErrorMsg());
            }
        }

        return false;
    }

    private GoodsRuleContext executeRuleFlow(ShopGoods goods, DfmallGoodsPoolVo pool) {
        String companyId = isCompanyPool(pool) ? String.valueOf(pool.getCompanyOrgId()) : "0";
        return flowDispatch.getGoodsShelves(
                pool.getAppName(),
                pool.getLiteFlowName(),
                String.valueOf(pool.getId()),
                goods.getGoodsCode(),
                companyId
        );
    }

    private void processLabels(ShopGoods goods,DfmallGoodsPoolVo pool,
                               List<ShopGoodsSaleLabelEntity> insertLabel,
                               List<Long> removeLabel) {
        String companyId = isCompanyPool(pool) ? String.valueOf(pool.getCompanyOrgId()) : "0";
        if (StringUtils.isNotBlank(pool.getSaleAppName())) {
            GoodsRuleContext ruleContext = flowDispatch.getGoodsSale(
                    pool.getSaleAppName(),
                    pool.getSaleFlowName(),
                    String.valueOf(pool.getId()),
                    goods.getGoodsCode(),
                    companyId
            );

            insertLabel.addAll(ruleContext.getInsertLabel());
            removeLabel.addAll(ruleContext.getRemoveLabel());
        }
    }
    // endregion

    // region 批量操作
    private void executeBatchOperations(Map<DfmallGoodsPoolVo, Set<Long>> upGoods,
                                        Map<DfmallGoodsPoolVo, Set<Long>> downGoods,
                                        List<ShopGoodsSaleLabelEntity> insertLabel,
                                        List<Long> removeLabel,
                                        List<ShopGoods> goodsList) {
        // 上下架
        upGoods.forEach((pool, ids) -> handleUp(new ArrayList<>(ids), pool));
        downGoods.forEach((pool, ids) -> handleDown(new ArrayList<>(ids), pool,goodsList));

        // 标签
        processLabelsBatch(insertLabel, removeLabel);
    }

    private void handleUp(List<Long> ids, DfmallGoodsPoolVo pool) {
        if (CollectionUtil.isEmpty(ids)) return;

        // 更新商品状态
        goodsService.update(new LambdaUpdateWrapper<ShopGoods>()
                .set(ShopGoods::getShelvesState, 1)
                .in(ShopGoods::getGoodsId, ids));

        // 添加至商品池
        //保存商品池
        DfmallGoodsPoolSave goodsPoolSaveDto = new DfmallGoodsPoolSave();
        goodsPoolSaveDto.setGoodsIds(StringUtils.join(ids, ","));
        goodsPoolService.saveGoodsPoolSub(goodsPoolSaveDto, pool.getId());

        // 新增上架日志记录
        if(isPlatform(pool)) { // 仅平台池记录
            List<ShopGoods> goodsList = goodsService.getBaseMapper()
                    .selectList(new LambdaQueryWrapperX<ShopGoods>()
                            .select(ShopGoods::getGoodsCode,ShopGoods::getGoodsSku,ShopGoods::getSupplierCode,ShopGoods::getTenantId)
                    .in(ShopGoods::getGoodsId, ids)
                    .eq(ShopGoods::getIsEnable, "1"));
            insertGoodsLogQueue(goodsList, "UP");
        }
    }

    private void handleDown(List<Long> ids, DfmallGoodsPoolVo pool,List<ShopGoods> goodsList) {
        if (CollectionUtil.isEmpty(ids)) return;

        if (isPlatform(pool)) {
            // 平台池下架
            goodsService.update(new LambdaUpdateWrapper<ShopGoods>()
                    .set(ShopGoods::getShelvesState, 0)
                    .in(ShopGoods::getGoodsId, ids));
            goodsPoolSubService.deleteByGoodsIds(ids);
            // 新增下架日志记录
            List<ShopGoods> downGoodsList = goodsList.stream().filter(goods -> ids.contains(goods.getGoodsId())).collect(Collectors.toList());
            insertGoodsLogQueue(downGoodsList, "DOWN");
        } else if (isCompanyPool(pool)) {
            handleCompanyDown(ids, pool);
        } else if (isZonePool(pool)) {
            goodsPoolSubService.deleteByGoodsPoolId(pool.getId(), ids);
        }
    }

    private void handleCompanyDown(List<Long> ids, DfmallGoodsPoolVo companyPool) {
        // 删除关联专区
        List<Long> zoneIds = goodsPoolService.list(
                new LambdaQueryWrapper<DfmallGoodsPoolEntity>()
                        .select(DfmallGoodsPoolEntity::getId)
                        .eq(DfmallGoodsPoolEntity::getCompanyCode, companyPool.getCompanyCode())
                        .eq(DfmallGoodsPoolEntity::getGoodsPoolLevel, GoodsPoolLevelEnum.POOL_LEVEL_ZONE.getValue())
        ).stream().map(DfmallGoodsPoolEntity::getId).collect(Collectors.toList());

        zoneIds.forEach(zoneId -> goodsPoolSubService.deleteByGoodsPoolId(zoneId, ids));
        goodsPoolSubService.deleteByGoodsPoolId(companyPool.getId(), ids);

        // 发送通知
        notifyCompany(ids, companyPool.getCompanyCode());
    }

    private void processLabelsBatch(List<ShopGoodsSaleLabelEntity> insert, List<Long> remove) {
        if (CollectionUtil.isNotEmpty(insert)) {
            goodsSaleLabelService.saveBatch(insert);
        }
        if (CollectionUtil.isNotEmpty(remove)) {
            try {
                //记录商品标签历史
                labelHistoryService.copyLabelHistory(remove);
            } catch (Exception e) {
                log.error("记录商品标签历史失败", e);
            }
            goodsSaleLabelService.delByIds(remove);
        }
    }
    // endregion

    // region 工具方法
    private boolean isPlatform(DfmallGoodsPoolVo pool) {
        return pool.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_PLATFORM.getValue());
    }

    private boolean isCompanyPool(DfmallGoodsPoolVo pool) {
        return pool.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_COMPANY.getValue());
    }

    private boolean isZonePool(DfmallGoodsPoolVo pool) {
        return pool.getGoodsPoolLevel().equals(GoodsPoolLevelEnum.POOL_LEVEL_ZONE.getValue());
    }

    private void updateEsIndex(List<ShopGoods> goodsList) {
        goodsList.stream()
                .collect(Collectors.groupingBy(ShopGoods::getTenantId))
                .forEach((tenantId, list) -> TenantUtils.execute(tenantId, () ->
                        goodsEsProcessor.updateIndex(
                                list.stream().map(ShopGoods::getGoodsId).distinct().collect(Collectors.toList()),
                                EsUpdateType.ALL, 9
                        )
                ));
    }

    private void notifyCompany(List<Long> goodsIds, String companyCode) {
        if (openApiConfig.getPushProductCustomers().contains(companyCode)) {
            List<ShopGoods> goods = goodsService.getBaseMapper().selectByGoodsIdIn(goodsIds);
            goods.forEach(g ->
                    openApiMessageUtil.sendMessage(companyCode, MessageTypeEnum.DELETE_GOODS,
                            MapUtil.of("goodsCode", g.getGoodsCode()))
            );
        }
    }



    /**
     * 日志记录
     * @param goodsList 商品列表
     * @param operate 操作类型 UP/DOWN
     */
    @Async
    public void insertGoodsLogQueue(List<ShopGoods> goodsList, String operate) {
        try {
            if (CollectionUtil.isEmpty(goodsList)) return;

            List<XElasticsearchGoodsLogQueueEntity> logQueueList = goodsList.stream()
                    .map(goods -> buildLogEntity(goods, operate))
                    .collect(Collectors.toList());

            goodsLogEsProcessor.batchInsertQueue(logQueueList);
        } catch (Exception e) {
            log.error("商品操作日志记录失败 操作类型:{} 错误信息:{}",
                    operate, ExceptionUtil.stacktraceToString(e));
        }
    }

    private XElasticsearchGoodsLogQueueEntity buildLogEntity(ShopGoods goods, String operate) {
        XElasticsearchGoodsLogQueueEntity entity = new XElasticsearchGoodsLogQueueEntity();
        entity.setOperate(operate);
        entity.setGoodsCode(goods.getGoodsCode());
        entity.setGoodsSku(goods.getGoodsSku());
        entity.setSupplierCode(goods.getSupplierCode());
        entity.setRemark(getOperateRemark(operate,goods));
        entity.setCreator("liteFlow");
        entity.setStatus(1L);
        entity.setTenantId(goods.getTenantId());
        entity.setCreateTime(new Date());
        return entity;
    }

    private String getOperateRemark(String operate,ShopGoods goods) {
        return "UP".equals(operate) ?
                "平台规则上架" :
                ("平台规则下架:"+ goods.getShelvesReason());
    }


}
