package com.ly.yph.api.customization.common;

/**
 * <AUTHOR>
 * @date 2023年02月13日
 */
public class DfgSapInterfaceCode {
    /**
     * 物料创建
     */
    public static final String DFG_SAP_129 = "DFG_SAP_129";
    /**
     * 测试环境-物料创建
     */
    public static final String DFG_SAP_129_QAS ="DFG_SAP_129_QAS";

    /**
     * 订单创建 （华神）
     */
    public static final String DFG_SAP_136 = "DFG_SAP_136";

    /**
     * 订单创建  (非华神)
     */
    public static final String DFG_SAP_173 = "DFG_SAP_173";
    /**
     * 订单创建-测试环境
     */
    public static final String DFG_SAP_136_QAS = "DFG_SAP_136_QAS";

    /**
     * 索引单 （华神）
     */
    public static final String DFG_SAP_130 = "DFG_SAP_130";
    /**
     * 索引单 （非华神）
     */
    public static final String DFG_SAP_178 = "DFG_SAP_178";

    /**
     * 测试环境 - 索引单
     */
    public static final String DFG_SAP_130_QAS = "DFG_SAP_130_QAS";

    /**
     * 投资类订单获取wbs订单 （华神）
     */
    public static final String DFG_SAP_153 = "DFG_SAP_153";
    /**
     * 投资类订单获取wbs订单  （非华神）
     */
    public static final String DFG_SAP_171 = "DFG_SAP_171";

    /**
     * 修改投资类wbs订单（只支持冲销）
     */
    public static final String DFG_SAP_154 = "DFG_SAP_154";

    /**
     * 投资类订单收货
     */
    public static final String DFG_SAP_155 = "DFG_SAP_155";

    /**
     * 投资类获取索引单
     */
    public static final String DFG_SAP_156 = "DFG_SAP_156";
    /**
     * 投资类删除索引单
     */
    public static final String DFG_SAP_157 = "DFG_SAP_157";


    public static final String DFG_SAP_153_FUNC = "SapZfunMm040";

    public static final String DFG_SAP_154_FUNC = "SapZfunMm041";

    public static final String DFG_SAP_155_FUNC = "SapZfunMm042";

    public static final String DFG_SAP_156_FUNC = "SapZfunMm043";

    public static final String DFG_SAP_157_FUNC = "SapZfunMm044";

    /**
     * 获取成本中心数据
     */
    public static final String getCostCenterDataUrl= "/ext/voyahController/callGetDfgSapCostCenterDate";
}
