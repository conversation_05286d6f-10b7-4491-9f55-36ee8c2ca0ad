package com.ly.yph.api.settlement.supplier.factory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.ly.yph.api.settlement.common.entity.SettleShopBill;
import com.ly.yph.api.settlement.common.entity.SettleShopBillPostageDetail;
import com.ly.yph.api.settlement.common.enums.ReconciliationStatusEnum;
import com.ly.yph.api.settlement.common.enums.SupplierBillDetailStagingFlagEnum;
import com.ly.yph.api.settlement.common.enums.SupplierInvoiceStateEnum;
import com.ly.yph.api.settlement.common.service.SettleShopBillPostageDetailService;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillDetailPageReqVo;
import com.ly.yph.api.settlement.common.vo.bill.SettleShopBillPostageExcelVo;
import com.ly.yph.api.settlement.supplier.dto.*;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceBill;
import com.ly.yph.api.settlement.supplier.entity.SupplierInvoiceDetailPostage;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceBillService;
import com.ly.yph.api.settlement.supplier.service.SupplierInvoiceDetailPostageService;
import com.ly.yph.core.base.BaseEntity;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.excel.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupplierPostageDetailStrategy implements SupplierInvoiceStrategy {

    @Resource
    private SupplierInvoiceDetailPostageService supplierInvoiceDetailPostageService;
    @Resource
    private SupplierInvoiceBillService supplierInvoiceBillService;
    @Resource
    private SettleShopBillPostageDetailService settleShopBillPostageDetailService;

    @Override
    public void exportInvoiceTemplate(HttpServletResponse response) throws IOException {
        List<SupplierInvoicePostageExcelDto> list = Arrays.asList(
                SupplierInvoicePostageExcelDto.builder()
                        .orderNumber("'" +"240719135401579994")
                        .postage(new BigDecimal("6"))
                        .build()
        );
        ExcelUtils.write(response, "账单邮费开票模板.xlsx", "邮费明细", SupplierInvoicePostageExcelDto.class, list);
    }

    @Override
    @Transactional
    public String excelInvoice(MultipartFile file, SettleShopBill settleShopBill) throws IOException {
        List<SupplierInvoicePostageExcelDto> supplierInvoicePostageExcelDtoList = ExcelUtils.read(file, SupplierInvoicePostageExcelDto.class);
        supplierInvoicePostageExcelDtoList.removeIf(ExcelUtils::objCheckIsNull);

        List<SettleShopBillPostageDetail> SettleShopBillPostageDetailList = supplierInvoiceDetailPostageService.getSupplierInvoicePostageDetailListForExcel
                (supplierInvoicePostageExcelDtoList, settleShopBill.getBillId());

        supplierInvoiceBillService.doSupplierInvoiceBillForPostage(SettleShopBillPostageDetailList, settleShopBill);

        return "邮费开票完成！";

    }

    @Override
    @Transactional
    public String allDetailInvoice(SettleShopBill settleShopBill) {

        List<SettleShopBillPostageDetail> supplierInvoicePostageDetails = supplierInvoiceDetailPostageService.getBaseMapper()
                .getSupplierInvoicePostageDetail(null, settleShopBill.getBillId(), null);

        if (CollectionUtil.isEmpty(supplierInvoicePostageDetails)) {
            throw new ParameterException("暂无可用邮费数据可以进行开票申请!");
        }

        supplierInvoiceBillService.doSupplierInvoiceBillForPostage(supplierInvoicePostageDetails, settleShopBill);

        return "邮费开票完成";

    }

    @Override
    @Transactional
    public String supplierInvoiceByDetailIds(SupplierInvoiceByDetailIdDto supplierInvoiceByDetailIdDto, SettleShopBill settleShopBill) {

        List<SettleShopBillPostageDetail> supplierInvoicePostageDetails = supplierInvoiceDetailPostageService.getBaseMapper().getSupplierInvoicePostageDetail(
                null,
                supplierInvoiceByDetailIdDto.getBillId(),
                supplierInvoiceByDetailIdDto.getDetailIdList());

        if (CollectionUtil.isEmpty(supplierInvoicePostageDetails)) {
            throw new ParameterException("暂无可用邮费数据可以进行开票申请!");
        }

        supplierInvoiceBillService.doSupplierInvoiceBillForPostage(supplierInvoicePostageDetails, settleShopBill);

        return "邮费开票完成";
    }

    @Override
    @Transactional
    public void supplierInvoicePass(SupplierInvoiceBill supplierInvoiceBill) {
        // 供应商邮费发票通过
        QueryWrapper<SupplierInvoiceDetailPostage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupplierInvoiceDetailPostage::getInvoiceId, supplierInvoiceBill.getId())
                .select(SupplierInvoiceDetailPostage::getPostageDetailId);
        List<SupplierInvoiceDetailPostage> supplierInvoiceDetailPostageList = supplierInvoiceDetailPostageService.list(queryWrapper);

        if (CollectionUtil.isEmpty(supplierInvoiceDetailPostageList)) {
            throw new ParameterException("未查询到相关明细数据！");
        }


        Lists.partition(
                supplierInvoiceDetailPostageList.stream().map(SupplierInvoiceDetailPostage::getPostageDetailId).collect(Collectors.toList()), 1000)
                .forEach(subList -> {
                    //更新邮费明细开票状态
                    UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().in(SettleShopBillPostageDetail::getBillDetailPostageId, subList)
                            .set(SettleShopBillPostageDetail::getInvoiceFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_INVOICED.getCode());
                    settleShopBillPostageDetailService.update(updateWrapper);
                });

        // 更新供应商发票状态
        UpdateWrapper<SupplierInvoiceBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SupplierInvoiceBill::getId, supplierInvoiceBill.getId())
                .set(SupplierInvoiceBill::getState, SupplierInvoiceStateEnum.INVOICED.getCode())
                .set(SupplierInvoiceBill::getConfirmName, LocalUserHolder.get().getNickname())
                .set(SupplierInvoiceBill::getInvoiceConfirmTime, new Date())
                .set(BaseEntity::getModifier, LocalUserHolder.get().getNickname())
                .set(BaseEntity::getUpdateTime, new Date());
        supplierInvoiceBillService.update(updateWrapper);

        //检查账单是否全部开票，如果全部开票，则可发起验收单
        supplierInvoiceBillService.checkIsToApproveBill(supplierInvoiceBill);

    }

    @Override
    public void supplierInvoiceExport(HttpServletResponse response, Long id) throws IOException {
        //供应商邮费导出
        List<SupplierInvoicePostageDetailExcelDto> supplierInvoicePostageExcelDtoList = supplierInvoiceDetailPostageService
                .getBaseMapper().getSupplierInvoicePostageDetailList(id);

        if (CollectionUtil.isEmpty(supplierInvoicePostageExcelDtoList)) {
            throw new ParameterException("该发票下无明细数据！");
        }
        ExcelUtils.write(response, "供应商发票" + ".xls", "邮费明细列表",
                SupplierInvoicePostageDetailExcelDto.class, supplierInvoicePostageExcelDtoList);
    }

    @Override
    @Transactional
    public void invoiceDel(SupplierInvoiceBill supplierInvoiceBill) {
        QueryWrapper<SupplierInvoiceDetailPostage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupplierInvoiceDetailPostage::getInvoiceId, supplierInvoiceBill.getId())
                .select(SupplierInvoiceDetailPostage::getPostageDetailId,
                        SupplierInvoiceDetailPostage::getId);
        List<SupplierInvoiceDetailPostage> supplierInvoiceDetailPostageList = supplierInvoiceDetailPostageService.list(queryWrapper);

        if (CollectionUtil.isEmpty(supplierInvoiceDetailPostageList)) {
            throw new ParameterException("未查询到相关明细数据！");
        }

        Lists.partition(
                supplierInvoiceDetailPostageList.stream().map(SupplierInvoiceDetailPostage::getPostageDetailId).collect(Collectors.toList()), 1000)
                .forEach(subList -> {
                    //更新邮费明细开票状态
                    UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().in(SettleShopBillPostageDetail::getBillDetailPostageId, subList)
                            .set(SettleShopBillPostageDetail::getInvoiceFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                    settleShopBillPostageDetailService.update(updateWrapper);
                });

        supplierInvoiceBillService.removeById(supplierInvoiceBill.getId());

        supplierInvoiceDetailPostageService.removeBatchByIds(
                supplierInvoiceDetailPostageList.stream().map(SupplierInvoiceDetailPostage::getId).collect(Collectors.toList())
        );
    }

    /**
     * 供应商邮费发票驳回
     *
     * @param supplierInvoiceBill
     * @param rejectReason
     */
    @Override
    @Transactional
    public void supplierInvoiceReject(SupplierInvoiceBill supplierInvoiceBill, String rejectReason) {

        QueryWrapper<SupplierInvoiceDetailPostage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupplierInvoiceDetailPostage::getInvoiceId, supplierInvoiceBill.getId())
                .select(SupplierInvoiceDetailPostage::getPostageDetailId);
        List<SupplierInvoiceDetailPostage> supplierInvoiceDetailPostageList = supplierInvoiceDetailPostageService.list(queryWrapper);

        if (CollectionUtil.isEmpty(supplierInvoiceDetailPostageList)) {
            throw new ParameterException("未查询到相关明细数据！");
        }

        Lists.partition(
                supplierInvoiceDetailPostageList.stream().map(SupplierInvoiceDetailPostage::getPostageDetailId).collect(Collectors.toList()), 1000)
                .forEach(subList -> {
                    //更新邮费明细开票状态
                    UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().in(SettleShopBillPostageDetail::getBillDetailPostageId, subList)
                            .set(SettleShopBillPostageDetail::getInvoiceFlag, ReconciliationStatusEnum.RECONCILIATION_BILL_CONFIRMED.getCode());
                    settleShopBillPostageDetailService.update(updateWrapper);
                });

        UpdateWrapper<SupplierInvoiceBill> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SupplierInvoiceBill::getId, supplierInvoiceBill.getId())
                .set(SupplierInvoiceBill::getState, SupplierInvoiceStateEnum.REJECTED.getCode())
                .set(SupplierInvoiceBill::getApproveReason, rejectReason)
                .set(BaseEntity::getModifier, LocalUserHolder.get().getNickname())
                .set(BaseEntity::getUpdateTime, new Date());

        supplierInvoiceBillService.update(updateWrapper);
    }

    @Override
    @Transactional
    public String removeToNextMonth(RemoveToNextMonthDto removeToNextMonthDto) {

        QueryWrapper<SettleShopBillPostageDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SettleShopBillPostageDetail::getBillId, removeToNextMonthDto.getBillId())
                .eq(SettleShopBillPostageDetail::getBillDetailPostageId, removeToNextMonthDto.getDetailId());
        SettleShopBillPostageDetail one = settleShopBillPostageDetailService.getOne(queryWrapper);
        if (one == null) {
            throw new ParameterException("未查询到明细数据！");
        }

        UpdateWrapper<SettleShopBillPostageDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SettleShopBillPostageDetail::getBillDetailPostageId, one.getBillDetailPostageId());
        if (removeToNextMonthDto.getType() == 0) {
            updateWrapper.lambda().set(SettleShopBillPostageDetail::getPostageStagingFlag, SupplierBillDetailStagingFlagEnum.HAD_STAGING.getCode());
        } else if (removeToNextMonthDto.getType() == 1) {
            updateWrapper.lambda().set(SettleShopBillPostageDetail::getPostageStagingFlag, SupplierBillDetailStagingFlagEnum.NOT_STAGING.getCode());
        } else {
            throw new ParameterException("操作异常！");
        }
        settleShopBillPostageDetailService.update(updateWrapper);

        return "邮费移月成功！";
    }

    @Override
    public void exportBillDetail(HttpServletResponse response, SettleShopBillDetailPageReqVo reqVo) throws IOException {
        SettleShopBill settleShopBill = supplierInvoiceBillService.checkSupplierBillAndOperator(reqVo.getBillId());
        List<SettleShopBillPostageExcelVo> postageExcel = settleShopBillPostageDetailService.getBaseMapper().getPostageExcel(reqVo);
        ExcelUtils.write(response, "邮费明细.xlsx", "邮费明细", SettleShopBillPostageExcelVo.class, postageExcel);
    }

}
