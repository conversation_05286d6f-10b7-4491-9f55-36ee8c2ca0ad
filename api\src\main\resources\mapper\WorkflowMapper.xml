<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.yph.api.workflow.mapper.WorkflowMapper">
    <resultMap id="BaseResultMap" type="com.ly.yph.api.workflow.entity.Workflow">
        <!--@mbg.generated-->
        <!--@Table workflow-->
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="bus_id" property="busId"/>
        <result column="bus_code" property="busCode"/>
        <result column="bus_code_x" property="busCodeX"/>
        <result column="context" property="context"/>
        <result column="handler" property="handler"/>
        <result column="current_step" property="currentStep"/>
        <result column="status" property="status"/>
        <result column="total_step" property="totalStep"/>
        <result column="creator_id" property="creatorId"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="supplier_code" property="supplierCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        `name`,
        description,
        bus_id,
        bus_code,
        bus_code_x,
        context,
        `handler`,
        current_step,
        `status`,
        total_step,
        creator_id,
        created_at,
        updated_at,
        tenant_id,
        supplier_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from workflow
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from workflow
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.Workflow"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into workflow (`name`, description, bus_id, bus_code, bus_code_x, context, `handler`,
                              current_step, `status`, total_step, creator_id, created_at, updated_at,
                              tenant_id, supplier_code)
        values (#{name}, #{description}, #{busId}, #{busCode}, #{busCodeX}, #{context}, #{handler},
                #{currentStep}, #{status}, #{totalStep}, #{creatorId}, #{createdAt}, #{updatedAt},
                #{tenantId}, #{supplierCode})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.Workflow"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into workflow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="busId != null">
                bus_id,
            </if>
            <if test="busCode != null">
                bus_code,
            </if>
            <if test="busCodeX != null">
                bus_code_x,
            </if>
            <if test="context != null">
                context,
            </if>
            <if test="handler != null">
                `handler`,
            </if>
            <if test="currentStep != null">
                current_step,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="totalStep != null">
                total_step,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="busId != null">
                #{busId},
            </if>
            <if test="busCode != null">
                #{busCode},
            </if>
            <if test="busCodeX != null">
                #{busCodeX},
            </if>
            <if test="context != null">
                #{context},
            </if>
            <if test="handler != null">
                #{handler},
            </if>
            <if test="currentStep != null">
                #{currentStep},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="totalStep != null">
                #{totalStep},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="supplierCode != null">
                #{supplierCode},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ly.yph.api.workflow.entity.Workflow">
        <!--@mbg.generated-->
        update workflow
        <set>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="busId != null">
                bus_id = #{busId},
            </if>
            <if test="busCode != null">
                bus_code = #{busCode},
            </if>
            <if test="busCodeX != null">
                bus_code_x = #{busCodeX},
            </if>
            <if test="context != null">
                context = #{context},
            </if>
            <if test="handler != null">
                `handler` = #{handler},
            </if>
            <if test="currentStep != null">
                current_step = #{currentStep},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="totalStep != null">
                total_step = #{totalStep},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ly.yph.api.workflow.entity.Workflow">
        <!--@mbg.generated-->
        update workflow
        set `name`        = #{name},
            description   = #{description},
            bus_id        = #{busId},
            bus_code      = #{busCode},
            bus_code_x    = #{busCodeX},
            context       = #{context},
            `handler`     = #{handler},
            current_step  = #{currentStep},
            `status`      = #{status},
            total_step    = #{totalStep},
            creator_id    = #{creatorId},
            created_at    = #{createdAt},
            updated_at    = #{updatedAt},
            tenant_id     = #{tenantId},
            supplier_code = #{supplierCode}
        where id = #{id}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update workflow
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.name}
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.description}
                </foreach>
            </trim>
            <trim prefix="bus_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.busId}
                </foreach>
            </trim>
            <trim prefix="bus_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.busCode}
                </foreach>
            </trim>
            <trim prefix="bus_code_x = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.busCodeX}
                </foreach>
            </trim>
            <trim prefix="context = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.context}
                </foreach>
            </trim>
            <trim prefix="`handler` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.handler}
                </foreach>
            </trim>
            <trim prefix="current_step = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.currentStep}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.status}
                </foreach>
            </trim>
            <trim prefix="total_step = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.totalStep}
                </foreach>
            </trim>
            <trim prefix="creator_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.creatorId}
                </foreach>
            </trim>
            <trim prefix="created_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.createdAt}
                </foreach>
            </trim>
            <trim prefix="updated_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.updatedAt}
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.tenantId}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.supplierCode}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update workflow
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id} then #{item.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.description != null">
                        when id = #{item.id} then #{item.description}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bus_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busId != null">
                        when id = #{item.id} then #{item.busId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bus_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busCode != null">
                        when id = #{item.id} then #{item.busCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bus_code_x = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busCodeX != null">
                        when id = #{item.id} then #{item.busCodeX}
                    </if>
                </foreach>
            </trim>
            <trim prefix="context = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.context != null">
                        when id = #{item.id} then #{item.context}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`handler` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.handler != null">
                        when id = #{item.id} then #{item.handler}
                    </if>
                </foreach>
            </trim>
            <trim prefix="current_step = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currentStep != null">
                        when id = #{item.id} then #{item.currentStep}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_step = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalStep != null">
                        when id = #{item.id} then #{item.totalStep}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creator_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creatorId != null">
                        when id = #{item.id} then #{item.creatorId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="created_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createdAt != null">
                        when id = #{item.id} then #{item.createdAt}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updated_at = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updatedAt != null">
                        when id = #{item.id} then #{item.updatedAt}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tenant_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantId != null">
                        when id = #{item.id} then #{item.tenantId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.supplierCode != null">
                        when id = #{item.id} then #{item.supplierCode}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into workflow
        (`name`, description, bus_id, bus_code, bus_code_x, context, `handler`, current_step,
         `status`, total_step, creator_id, created_at, updated_at, tenant_id, supplier_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.name}, #{item.description}, #{item.busId}, #{item.busCode}, #{item.busCodeX},
             #{item.context}, #{item.handler}, #{item.currentStep}, #{item.status}, #{item.totalStep},
             #{item.creatorId}, #{item.createdAt}, #{item.updatedAt}, #{item.tenantId}, #{item.supplierCode})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.ly.yph.api.workflow.entity.Workflow"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into workflow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            `name`,
            description,
            bus_id,
            bus_code,
            bus_code_x,
            context,
            `handler`,
            current_step,
            `status`,
            total_step,
            creator_id,
            created_at,
            updated_at,
            tenant_id,
            supplier_code,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            #{name},
            #{description},
            #{busId},
            #{busCode},
            #{busCodeX},
            #{context},
            #{handler},
            #{currentStep},
            #{status},
            #{totalStep},
            #{creatorId},
            #{createdAt},
            #{updatedAt},
            #{tenantId},
            #{supplierCode},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id},
            </if>
            `name`        = #{name},
            description   = #{description},
            bus_id        = #{busId},
            bus_code      = #{busCode},
            bus_code_x    = #{busCodeX},
            context       = #{context},
            `handler`     = #{handler},
            current_step  = #{currentStep},
            `status`      = #{status},
            total_step    = #{totalStep},
            creator_id    = #{creatorId},
            created_at    = #{createdAt},
            updated_at    = #{updatedAt},
            tenant_id     = #{tenantId},
            supplier_code = #{supplierCode},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ly.yph.api.workflow.entity.Workflow" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into workflow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="busId != null">
                bus_id,
            </if>
            <if test="busCode != null">
                bus_code,
            </if>
            <if test="busCodeX != null">
                bus_code_x,
            </if>
            <if test="context != null">
                context,
            </if>
            <if test="handler != null">
                `handler`,
            </if>
            <if test="currentStep != null">
                current_step,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="totalStep != null">
                total_step,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="busId != null">
                #{busId},
            </if>
            <if test="busCode != null">
                #{busCode},
            </if>
            <if test="busCodeX != null">
                #{busCodeX},
            </if>
            <if test="context != null">
                #{context},
            </if>
            <if test="handler != null">
                #{handler},
            </if>
            <if test="currentStep != null">
                #{currentStep},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="totalStep != null">
                #{totalStep},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="supplierCode != null">
                #{supplierCode},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="busId != null">
                bus_id = #{busId},
            </if>
            <if test="busCode != null">
                bus_code = #{busCode},
            </if>
            <if test="busCodeX != null">
                bus_code_x = #{busCodeX},
            </if>
            <if test="context != null">
                context = #{context},
            </if>
            <if test="handler != null">
                `handler` = #{handler},
            </if>
            <if test="currentStep != null">
                current_step = #{currentStep},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="totalStep != null">
                total_step = #{totalStep},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode},
            </if>
        </trim>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2023-09-14-->
    <select id="selectByBusId" resultType="com.ly.yph.api.workflow.entity.Workflow">
        select id,bus_id
        from workflow
        where bus_id = #{busId}
    </select>

    <delete id="deleteByBusId">
        delete
        from workflow
        where bus_id = #{busId}
    </delete>


    <delete id="deleteByBusCode">
        delete
        from workflow
        where bus_code = #{busCode}
    </delete>

    <select id="selectByBusCode" resultType="com.ly.yph.api.workflow.entity.Workflow">
        select id
        from workflow
        where bus_code = #{busCode}
        and tenant_id = #{tid}
    </select>
    <select id="selectByBusCodeX" resultType="com.ly.yph.api.workflow.entity.Workflow">
        select
            id,
            bus_id,
            bus_code,
            bus_code_x,
            supplier_code,
            status
        from workflow
        where bus_code_x = #{busCodeX}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <delete id="deleteByCreatedAtBefore">
        delete from workflow
        where created_at <![CDATA[<]]> #{maxCreatedAt}
    </delete>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <select id="selectByBusIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from workflow
        where bus_id in
        <foreach item="item" index="index" collection="busIdCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <select id="selectByBusCodeXIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from workflow
        where bus_code_x in
        <foreach item="item" index="index" collection="busCodeXCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <select id="selectByBusCodeIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from workflow
        where bus_code in
        <foreach item="item" index="index" collection="busCodeCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <select id="selectByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from workflow
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectListWorkFlow" resultType="com.ly.yph.api.workflow.controller.vo.WorkFlowExcelVO">
        SELECT
        (@i := @i + 1) AS indexNum,
        case
        ws.status
        when -1 then '初始化'
        when 0 then '待审核'
        when 1 then '审核通过'
        when 2 then '审核拒绝'
        when 3 then '审核过期'
        else '未知'
        end as statusName,
        ss.supplier_full_name as supplierName,
        sg.goods_sku,
        sg.goods_desc,
        sg.brand_name,
        IF(sgp.validity_start = NULL, '', CONCAT(sgp.validity_start, '至', sgp.validity_end)) AS validity,
        sg.supplier_class_name,
        sg.first_class_name,
        sg.second_class_name,
        sg.third_class_name,
        sgp.goods_original_price,
        sgp.goods_pact_price,
        sgp.goods_pact_naked_price,
        sgp.goods_sale_price,
        sgp.goods_sale_naked_price,
        sgd.goods_moq,
        sg.tax_code,
        sg.tax_rate,
        ssc.agreement_discount * 100 as platformDiscount,
        round(sgp.goods_pact_price / sgp.goods_original_price * 100 , 0 ) AS agreementDiscount,
        IF(d.goods_source = 0, '自主上新', '寻源比价') AS goodsSource,
        IF(d.goods_source = 1, D.seek_price_numbers, '') AS seek_price_numbers,
        wk.created_at as createTime,
        max(ws.updated_at) as updateTime
        FROM workflow wk
        LEFT JOIN shop_goods sg ON wk.bus_code_x = sg.goods_code
        LEFT JOIN shop_goods_detail sgd on sgd.goods_code = sg.goods_code
        LEFT JOIN shop_goods_price sgp ON sgp.goods_code = sg.goods_code and sgp.is_enable =  1
        LEFT JOIN shop_supplier ss on ss.supplier_code = sg.supplier_code
        LEFT JOIN shop_supplier_class ssc ON ssc.class_code = sg.supplier_class and ssc.supplier_code = sg.supplier_code
        LEFT JOIN shop_goods_incidental_detail D ON D.goods_code = wk.bus_code_x
        LEFT JOIN workflow_step ws ON ws.workflow_id = wk.id,
        (SELECT @i := 0) AS itable
        <where>
            <if test="query.tenantId != null">and wk.tenant_id = #{query.tenantId}
            </if>
            <if test="query.isBlacklist != null">and ss.is_blacklist = #{query.isBlacklist}
            </if>
            <if test="query.description != null and query.description != ''">and wk.description like
                concat('%',#{query.description},'%')
            </if>
            <if test="query.name != null and query.name != ''">and wk.name like
                concat('%',#{query.name},'%')
            </if>
            <if test="query.status != null">and ws.status like
                concat('%',#{query.status},'%')
            </if>
            <if test="query.supplierCode != null and query.supplierCode != ''">and wk.supplier_code like
                concat('%',#{query.supplierCode},'%')
            </if>
            <if test="query.bid != null and query.bid != ''">and wk.bus_id like
                concat('%',#{query.bid},'%')
            </if>
            <if test="query.bid != null and query.bid != ''">and wk.bus_id like
                concat('%',#{query.bid},'%')
            </if>
            <if test="query.code != null and query.code != ''">
                and wk.bus_code in
                <foreach item="item" index="index" collection="query.code.split(',')" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="query.goodsCode != null and query.goodsCode != ''">
                AND ws.bus_code_x IN
                <foreach item="item" index="index" collection="query.goodsCode.split(',')" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="query.createTimeStart != null and query.createTimeStart != '' and query.createTimeEnd !=null and query.createTimeEnd != '' ">
                and wk.created_at BETWEEN #{query.createTimeStart} and #{query.createTimeEnd}
            </if>
            <if test="query.tenantId != null">
                AND ws.tenant_id = #{query.tenantId}
            </if>
            <if test="query.goodsSource != null">
                AND d.goods_source = #{query.goodsSource}
            </if>
            AND ws.description = '商品上架审核'
        </where>
        group by wk.id order by wk.id desc
    </select>
    <select id="selectPageWorkFlow" resultType="com.ly.yph.api.workflow.entity.Workflow">
        SELECT w.*
        FROM workflow w
        LEFT JOIN shop_supplier s ON w.supplier_code = s.supplier_code
        <where>
            <if test="wf.description != null and wf.description != ''">
                AND w.description LIKE CONCAT('%', #{wf.description}, '%')
            </if>
            <if test="wf.name != null and wf.name != ''">
                AND w.name LIKE CONCAT('%', #{wf.name}, '%')
            </if>
            <if test="wf.status != null">
                AND w.status = #{wf.status}
            </if>
            <if test="wf.supplierCode != null and wf.supplierCode != ''">
                AND w.supplier_code = #{wf.supplierCode}
            </if>
            <if test="wf.isBlacklist != null">
                AND S.is_blacklist = #{wf.isBlacklist}
            </if>
            <if test="wf.bid != null">
                AND w.bus_id = #{wf.bid}
            </if>
            <if test="wf.code != null and wf.code != ''">
                AND w.bus_code IN
                <foreach item="item" index="index" collection="wf.code.split(',')" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="wf.createTimeStart != null and wf.createTimeStart != null">
                AND w.created_at BETWEEN #{wf.createTimeStart} AND #{wf.createTimeEnd}
            </if>
            <if test="wf.handler != null and wf.handler != ''">
                AND w.handler = #{wf.handler}
            </if>
            <if test="wf.tenantId != null">
                AND w.tenant_id = #{wf.tenantId}
            </if>
            and w.handler = 'productWorkFlowHandler'
        </where>

    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <delete id="deleteByIdIn">
        delete from workflow
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>