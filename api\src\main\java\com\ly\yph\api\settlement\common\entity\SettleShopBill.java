package com.ly.yph.api.settlement.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.yph.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单主体表
 *
 * <AUTHOR>
 */
@Data
@ApiModel("账单主体表")
@TableName("settle_shop_bill")
@EqualsAndHashCode(callSuper = true)
public class SettleShopBill extends BaseEntity {

    private static final long serialVersionUID = -8245312305798626721L;


    @TableId(type = IdType.AUTO, value = "bill_id")
    private Long billId;

    @ApiModelProperty("账单编号")
    private String billSn;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("参考BillCustomerTypeEnum")
    private Integer customerType;

    @ApiModelProperty("参考CustomerSourceTypeEnum")
    private Integer customerSourceType;

    @ApiModelProperty("出账年")
    private Integer checkYear;

    @ApiModelProperty("出账月")
    private Integer checkMonth;

    @ApiModelProperty("商品含税总金额")
    private BigDecimal amountTax;

    @ApiModelProperty("商品未税总金额")
    private BigDecimal amount;

    @ApiModelProperty("积分金额")
    private BigDecimal pointAmount;

    @ApiModelProperty("个人支付金额")
    private BigDecimal individualPaymentAmount;

    @ApiModelProperty("邮费金额")
    private BigDecimal postage;

    @ApiModelProperty("含税总金额，友福利=积分金额+邮费;其他=含税总金额+邮费")
    private BigDecimal totalTaxAmount;

    @ApiModelProperty("容差金额")
    private BigDecimal toleranceAmount;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("参考账单状态BillStatusEnum")
    private Integer billStatus;

    @ApiModelProperty("账单审批状态")
    private Integer billApproveStatus;

    @ApiModelProperty("区域类型 0：华中，1：华南")
    private Integer areaType;

    @ApiModelProperty("账单类型 0：B端，1:C端")
    private Integer billType;

    @ApiModelProperty("企业实体编码")
    private String entityCustomerCode;

    @ApiModelProperty("友福利活动code")
    private String activityCode;

    @ApiModelProperty("账单审批完成时间")
    private Date billApprovedTime;

    @ApiModelProperty("备注")
    private String remark;
    /**
     * 加字段备注：南方企业的独立供应商的账单明细，不需要我们平台运营对账开票，不计入平台有效账单金额
     * 默认
     */
    @ApiModelProperty("是否平台内对账;1：平台内对账，2：平台外对账")
    private Integer isPlatformReconciliation;

    @ApiModelProperty("账单出账维度 0：按月出账，1：按活动出账")
    private Integer billCheckedType;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("是否推送电商账单 0：默认不推送，1：已推送")
    private Integer isPush;

    @ApiModelProperty("验收单路径")
    private String checkUrl;

    @ApiModelProperty("excel路径")
    private String excelUrl;

    @ApiModelProperty("支付计划状态 0:初始状态 1：待生成 2：已生成")
    private Integer payFlag;

    @ApiModelProperty("支付计划已支付金额")
    private BigDecimal payMoney;

}
