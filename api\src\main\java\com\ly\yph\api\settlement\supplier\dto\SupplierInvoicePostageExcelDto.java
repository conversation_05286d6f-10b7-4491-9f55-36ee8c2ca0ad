package com.ly.yph.api.settlement.supplier.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class SupplierInvoicePostageExcelDto {

    @ExcelProperty("商城订单号")
    @ContentStyle(horizontalAlignment = HorizontalAlignment.LEFT)
    @HeadFontStyle(fontHeightInPoints = 11, bold = true)
    private String orderNumber;

    @ExcelProperty("邮费金额")
    @HeadFontStyle(fontHeightInPoints = 11, bold = true)
    private BigDecimal postage;
}
