package com.ly.yph.api.workflow.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.yph.api.goods.common.HuoshanDeepseekProduct;
import com.ly.yph.api.goods.dto.HuoshanArkApiResultDTO;
import com.ly.yph.api.goods.enums.GoodsLabelEnum;
import com.ly.yph.api.goods.enums.WorkFlowStatusEnum;
import com.ly.yph.api.goods.mapper.ProductMapper;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.service.SystemTenantService;
import com.ly.yph.api.system.ErrorCodeConstants;
import com.ly.yph.api.workflow.IWorkFlowHandler;
import com.ly.yph.api.workflow.IWorkFlowStepHandler;
import com.ly.yph.api.workflow.WorkflowContext;
import com.ly.yph.api.workflow.controller.vo.ApproveImportExcelVO;
import com.ly.yph.api.workflow.controller.vo.ApproveImportRespVO;
import com.ly.yph.api.workflow.controller.vo.WorkFlowExcelVO;
import com.ly.yph.api.workflow.dto.WorkflowAuditRecordReq;
import com.ly.yph.api.workflow.dto.WorkflowReq;
import com.ly.yph.api.workflow.dto.WorkflowStepReq;
import com.ly.yph.api.workflow.entity.Workflow;
import com.ly.yph.api.workflow.entity.WorkflowAuditRecord;
import com.ly.yph.api.workflow.entity.WorkflowStep;
import com.ly.yph.api.workflow.mapper.WorkflowAuditRecordMapper;
import com.ly.yph.api.workflow.mapper.WorkflowMapper;
import com.ly.yph.api.workflow.mapper.WorkflowStepMapper;
import com.ly.yph.core.base.datapermission.core.annotation.DataPermission;
import com.ly.yph.core.base.exception.types.HttpException;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.page.PageReq;
import com.ly.yph.core.base.page.PageResp;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.mapper.SystemDictDataMapper;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/09/05
 */
@Service
@Slf4j
@RefreshScope
@DataPermission(enable = false)
public class WorkFlowService {
    @Resource
    private ThreadPoolExecutor commonIoExecutors;
    private final static int EXPIRED_DAY = 7;
    private static final long SCHEDULER_PERIOD = 1000 * 60 * 60 * 24;
    @Resource
    private WorkflowMapper wfmapper;
    @Resource
    private WorkflowStepMapper wfsMapper;
    @Resource
    private WorkFlowServiceX x;
    @Resource
    private SystemDictDataMapper systemDictDataMapper;
    @Resource
    private HuoshanDeepseekProduct huoshanDeepseekProduct;
    @Resource
    private ProductMapper productMapper;
    /**
     *
     * 多久后日志过期，单位月
     */
    @Value("${workflow.log-expired-month:6}")
    private Integer logExpiredMonth;
    /**
     * 是否启动AI审核
     */
    @Value("${workflow.aiAutoApprove:true}")
    private Boolean aiAutoApprove;
    @Resource
    private WorkflowAuditRecordMapper workflowAuditRecordMapper;
    @Resource
    private SystemTenantService tenantSrv;

    @Scheduled(fixedDelay = SCHEDULER_PERIOD, initialDelay = SCHEDULER_PERIOD)
    public void checkExpired() {
        wfsMapper.selectByStatusAndCreatedAtBefore(WorkFlowStatusEnum.PENDING.getCode(), new Date(System.currentTimeMillis() - EXPIRED_DAY * 24 * 60 * 60 * 1000L)).forEach(workflowStep -> {
            // 清理过期数据
            wfmapper.deleteByCreatedAtBefore(new Date(System.currentTimeMillis() - logExpiredMonth * 90 * 24 * 60 * 60 * 1000L));
            wfsMapper.deleteByCreatedAtBefore(new Date(System.currentTimeMillis() - logExpiredMonth * 90 * 24 * 60 * 60 * 1000L));
            workflowAuditRecordMapper.deleteByCreatedAtBefore(new Date(System.currentTimeMillis() - logExpiredMonth * 30 * 24 * 60 * 60 * 1000L));
        });
    }

    public Integer createWorkFlow(Collection<Workflow> wfs) {
        _deleteWorkFlowExist(wfs);

        List<Workflow> needCreate = new ArrayList<>(16);
        wfs.forEach(wf -> {
            wf.setCreatedAt(new Date());
            wf.setUpdatedAt(new Date());
            if (wf.getTotalStep() < 1) {
                throw HttpException.exception(ErrorCodeConstants.INVALID_PARAMETER, 0);
            }
            wf.setCurrentStep(1);
            wf.setStatus(WorkFlowStatusEnum.INIT.getCode());
            wf.setCreatorId(Math.toIntExact(LocalUserHolder.get() == null ? 0 : LocalUserHolder.get().getId()));
            needCreate.add(wf);
        });
        return wfmapper.batchInsert(needCreate);
    }

    private void _deleteWorkFlowExist(Collection<Workflow> wfps) {
        if (CollectionUtil.isEmpty(wfps)) {
            return;
        }
        Set<Long> busIds = new HashSet<>();
        Set<String> busCodes = new HashSet<>();
        Set<String> busCodeXs = new HashSet<>();

        wfps.forEach(wfp -> {
            if (wfp.getBusId() != null) {
                busIds.add(wfp.getBusId());
            }
            if (wfp.getBusCode() != null) {
                busCodes.add(wfp.getBusCode());
            }
            if (wfp.getBusCodeX() != null) {
                busCodeXs.add(wfp.getBusCodeX());
            }
        });

        List<Workflow> workflowsByBusId = wfmapper.selectByBusIdIn(busIds);
        // 创建 workflow 要 删除指定的数据时，要根据租户来确定。
        List<Workflow> workflowsByBusCode = wfmapper.selectByBusCodeIn(busCodes);
        List<Workflow> workflowsByBusCodeX = wfmapper.selectByBusCodeXIn(busCodeXs);

        List<Integer> needDelete = new ArrayList<>(16);
        Stream.concat(Stream.concat(workflowsByBusId.stream(), workflowsByBusCode.stream()), workflowsByBusCodeX.stream())
                .distinct()
                .forEach(wf -> needDelete.add(wf.getId()));

        if (!needDelete.isEmpty()) {
            deleteWorkFlow(needDelete);
        }
    }

    public void beginWorkFlow(Collection<Integer> ids, String c) {
        List<Workflow> wfls = wfmapper.selectByIdIn(ids);
        List<WorkflowStep> wfss = wfsMapper.selectByOrderIds(ids, 1);
        // 生成map
        Map<Integer, WorkflowStep> wfsMap = wfss.stream().collect(Collectors.toMap(WorkflowStep::getWorkflowId, Function.identity()));

        List<Workflow> updateListwf = new ArrayList<>(16);
        List<WorkflowStep> updateListwfs = new ArrayList<>(16);
        List<WorkflowAuditRecord> recordList = new ArrayList<>(16);

        wfls.stream().collect(Collectors.toMap(Workflow::getId, wf -> wf)).forEach((id, wf) -> {
            if (wf == null) {
                throw HttpException.exception(ErrorCodeConstants.WORKFLOW_NONE, id);
            }
            wf.setStatus(WorkFlowStatusEnum.PENDING.getCode());
            WorkflowStep wfs = wfsMap.get(id);
            if (wfs == null) {
                throw HttpException.exception(ErrorCodeConstants.STEP_NONE, id, 1);
            }
            wfs.setStatus(WorkFlowStatusEnum.PENDING.getCode());

            // 子流程初始化
            IWorkFlowStepHandler sh = SpringUtil.getBean(wfs.getHandler());
            IWorkFlowHandler h = SpringUtil.getBean(wf.getHandler());
            _handlerPendingAll(h, sh, wf.getContext());

            updateListwf.add(wf);
            updateListwfs.add(wfs);

        });
        // 插入记录
        _saveLog(wfls, wfss, WorkFlowStatusEnum.APPROVED, c);
        wfmapper.updateBatchSelective(updateListwf);
        wfsMapper.updateBatchSelective(updateListwfs);
    }

    public void deleteWorkFlow(Collection<Integer> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }

        List<Workflow> wf = wfmapper.selectByIdIn(ids);
        Map<Integer, Workflow> wfmap = wf.stream().collect(Collectors.toMap(Workflow::getId, Function.identity()));

        List<WorkflowStep> workflowSteps = wfsMapper.selectAllByWorkflowIdIn(ids);
        Map<Integer, WorkflowStep> workflowStepMap = workflowSteps.stream().collect(Collectors.toMap(WorkflowStep::getId, Function.identity()));
        ids.forEach(id -> {
            if (wfmap.get(id) == null) {
                return;
            }
            // 子流程也删除
            workflowStepMap.forEach((stepid, wfs) -> {
                IWorkFlowStepHandler stepHandler = SpringUtil.getBean(wfs.getHandler());
                stepHandler.processRemove(_getWorkflowContext(JSON.parseObject(wfmap.get(id).getContext())));
            });
            IWorkFlowHandler h = SpringUtil.getBean(wfmap.get(id).getHandler());
            h.processRemove(_getWorkflowContext(JSON.parseObject(wfmap.get(id).getContext())));
        });
        val collect = workflowSteps.stream().map(WorkflowStep::getId).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(collect)) {
            wfsMapper.deleteByIdIn(collect);
        }

        wfmapper.deleteByIdIn(ids);
    }

    public void updateWorkFlow(Workflow wf) {
        wf.setUpdatedAt(new Date());
        wfmapper.updateByPrimaryKeySelective(wf);
    }

    public PageResp<Workflow> selectPageWorkFlow(WorkflowReq wf, PageReq p) {
        wf.setTenantId(TenantContextHolder.getTenantId());
        IPage<Workflow> workflowIPage = wfmapper.selectPageWorkFlow(wf, DataAdapter.adapterPageReq(p));
        return DataAdapter.adapterPage(workflowIPage, Workflow.class);
    }

    public void createWorkFlowStep(Collection<WorkflowStep> wfss) {
        _deleteWorkFlowStepExist(wfss);
        List<WorkflowStep> steps = new ArrayList<>(16);
        wfss.forEach(wfs -> {
            wfs.setCreatedAt(new Date());
            wfs.setUpdatedAt(new Date());
            wfs.setStatus(WorkFlowStatusEnum.INIT.getCode());
            steps.add(wfs);
        });
        wfsMapper.batchInsert(steps);
    }

    private void _deleteWorkFlowStepExist(Collection<WorkflowStep> wfsp) {
        if (CollectionUtil.isEmpty(wfsp)) {
            return;
        }
        Set<Long> busIds = new HashSet<>();
        Set<String> busCodes = new HashSet<>();
        Set<String> busCodeXs = new HashSet<>();

        wfsp.forEach(wfs -> {
            if (wfs.getBusId() != null) {
                busIds.add(wfs.getBusId());
            }
            if (wfs.getBusCode() != null) {
                busCodes.add(wfs.getBusCode());
            }
            if (wfs.getBusCodeX() != null) {
                busCodeXs.add(wfs.getBusCodeX());
            }
        });

        List<WorkflowStep> workflowStepsByBusId = wfsMapper.selectByBusIdIn(busIds);
        // todo : tenant problem
        List<WorkflowStep> workflowStepsByBusCode = wfsMapper.selectByBusCodeInAndTenantId(busCodes, TenantContextHolder.getTenantId());
        List<WorkflowStep> workflowStepsByBusCodeX = wfsMapper.selectByBusCodeXIn(busCodeXs);
        List<Integer> needDelete = new ArrayList<>(16);
        Stream.concat(Stream.concat(workflowStepsByBusId.stream(), workflowStepsByBusCode.stream()), workflowStepsByBusCodeX.stream())
                .distinct()
                .forEach(wfs -> needDelete.add(wfs.getId()));
        if (!needDelete.isEmpty()) {
            deleteWorkFlowStep(needDelete);
        }
    }

    public void deleteWorkFlowStep(Collection<Integer> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        List<WorkflowStep> wfss = new ArrayList<>(wfsMapper.selectByIdIn(ids));
        Map<Integer, WorkflowStep> wfssm = wfss.stream().collect(Collectors.toMap(WorkflowStep::getId, Function.identity()));
        List<Workflow> wfls = wfmapper.selectByIdIn(wfss.stream().map(WorkflowStep::getWorkflowId).collect(Collectors.toList()));
        Map<Integer, Workflow> wfMap = wfls.stream().collect(Collectors.toMap(Workflow::getId, Function.identity()));
        ids.forEach(id -> {
            var wfs = wfssm.get(id);
            if (wfs == null) {
                log.error("stepIsNone:{}:{}", id, id);
                return;
                // throw HttpException.exception(ErrorCodeConstants.STEP_NONE, id, id);
            }

            var wf = wfMap.get(wfs.getWorkflowId());
            if (wf == null) {
                log.error("wfIsNone:{}", wfs.getWorkflowId());
                return;
                //throw HttpException.exception(ErrorCodeConstants.WORKFLOW_NONE, wfs.getWorkflowId());
            }

            IWorkFlowStepHandler sh = SpringUtil.getBean(wfs.getHandler());
            sh.processRemove(_getWorkflowContext(JSON.parseObject(wf.getContext())));
        });
        wfsMapper.deleteByIdIn(ids);
    }

    public void updateWorkFlowStep(WorkflowStep wfs) {
        wfs.setUpdatedAt(new Date());
        wfsMapper.updateByPrimaryKeySelective(wfs);
    }

    public WorkflowContext getContextDetail(Long id) {
        Workflow wf = wfmapper.selectByPrimaryKey(Math.toIntExact(id));
        return _getWorkflowContext(JSON.parseObject(wf.getContext()));
    }

    public Workflow selectByBusCodeX(String busCodex){
        return wfmapper.selectByBusCodeX(busCodex);
    }

    public PageResp<WorkflowStep> selectPageWorkFlowStep(WorkflowStepReq wfs, PageReq pageReq) {
        if(Objects.nonNull(wfs.getCode()) && wfs.getCode().split(",").length > 50){
            throw new ParameterException("sku查询数量不能超过50个");
        }
        if(Objects.nonNull(wfs.getGoodsCode()) && wfs.getGoodsCode().split(",").length > 50){
            throw new ParameterException("商品编码查询数量不能超过50个");
        }
        wfs.setTenantId(TenantContextHolder.getTenantId());
        IPage<WorkflowStep> workflowIPage = wfsMapper.selectPageWorkFlowStep(wfs, DataAdapter.adapterPageReq(pageReq));
        return DataAdapter.adapterPage(workflowIPage, WorkflowStep.class);
    }

    public void createWorkflowAuditRecord(WorkflowAuditRecord record) {
        record.setCreatedAt(new Date());
        record.setUpdatedAt(new Date());
        record.setTenantId(TenantContextHolder.getTenantId());
        workflowAuditRecordMapper.insert(record);
    }

    public void deleteWorkflowAuditRecord(Integer id) {
        workflowAuditRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 更新日志
     *
     * @param record 日志实体
     */
    public void updateWorkf(WorkflowAuditRecord record) {
        record.setUpdatedAt(new Date());
        workflowAuditRecordMapper.updateByPrimaryKeySelective(record);
    }

    public PageResp<WorkflowAuditRecord> selectPageWorkflowAuditRecord(WorkflowAuditRecordReq record, PageReq pageReq) {
        return workflowAuditRecordMapper.selectPage(pageReq, new LambdaQueryWrapperX<WorkflowAuditRecord>()
                .eqIfPresent(WorkflowAuditRecord::getWorkflowId, record.getWorkflowId())
                .eqIfPresent(WorkflowAuditRecord::getTenantId, TenantContextHolder.getTenantId())
                .eqIfPresent(WorkflowAuditRecord::getStatus, record.getStatus())
                .eqIfPresent(WorkflowAuditRecord::getStepId, record.getWorkflowStepId())
                .inIfPresent(WorkflowAuditRecord::getBusCode, StrUtil.split(record.getCode(), ","))
                .eqIfPresent(WorkflowAuditRecord::getBusId, record.getBid())
                .eqIfPresent(WorkflowAuditRecord::getSupplierCode, record.getSupplierCode())
                .likeIfPresent(WorkflowAuditRecord::getComment, record.getComment())
                .likeIfPresent(WorkflowAuditRecord::getWorkflowName, record.getWorkflowName())
                .likeIfPresent(WorkflowAuditRecord::getStepName, record.getWorkflowStepName())
                .betweenIfPresent(WorkflowAuditRecord::getCreatedAt, record.getCreatedAt()), WorkflowAuditRecord.class);
    }

    /**
     * 根据审批流ID查询审批记录
     * @param busId 审批流程ID
     * @return 返回结果集
     */
    public List<WorkflowAuditRecord> selectAuditRecordByWorkId(Long busId) {
        return workflowAuditRecordMapper.selectAuditRecord(busId);
    }

    // 批量审批
    public void approveBatch(List<Integer> stepIds, String comment) {
        stepIds.forEach(stepId -> {
            approve(stepId, comment);
        });
    }

    //多租户审批通过
    public void tenantIdApprove(Integer stepId, String comment){
        WorkflowStep wfs = getAndValidate(stepId);
        TenantUtils.execute(tenantSrv.getTenantIds(),()->{
            List<WorkflowStep> wfss = wfsMapper.selectByBusCodeAndStatus(wfs.getBusCode(), WorkFlowStatusEnum.PENDING.getCode());
            if (wfss.isEmpty()) {
                log.error("不存在SKU[{}]对应的审核流程:",wfs.getBusCode());
                return;
            }
            wfss.forEach(wf -> approve(wf.getId(), comment));
        });
    }


    // 审核通过
    public void approve(Integer stepId, String comment) {
        WorkflowStep wfs = getAndValidate(stepId);
        Workflow wf = wfmapper.selectByPrimaryKey(wfs.getWorkflowId());

        IWorkFlowStepHandler sh = SpringUtil.getBean(wfs.getHandler());
        IWorkFlowHandler h = SpringUtil.getBean(wf.getHandler());

        wf.setStatus(WorkFlowStatusEnum.APPROVED.getCode());
        wfs.setStatus(WorkFlowStatusEnum.APPROVED.getCode());

        if (wf.getCurrentStep() < wf.getTotalStep()) {
            wf.setCurrentStep(wf.getCurrentStep() + 1);
            wf.setStatus(WorkFlowStatusEnum.PENDING.getCode());

            // 下一步骤修改成待审核
            WorkflowStep next = wfsMapper.selectByOrderId(wf.getId(),wf.getCurrentStep());
            next.setStatus(WorkFlowStatusEnum.PENDING.getCode());
            wfsMapper.updateByPrimaryKeySelective(next);

            _handlerPass(sh, wf.getContext());
        } else {
            _handlerPassAll(h, sh, wf.getContext());
        }

        wfmapper.updateByPrimaryKeySelective(wf);
        wfsMapper.updateByPrimaryKeySelective(wfs);
        // 插入记录
        _saveLog(CollectionUtil.toList(wf), CollectionUtil.toList(wfs), WorkFlowStatusEnum.APPROVED, comment);
    }

    @NotNull
    private WorkflowStep getAndValidate(Integer stepId) {
        WorkflowStep wfs = wfsMapper.selectByPrimaryKey(stepId);
        if (!wfs.getStatus().equals(WorkFlowStatusEnum.PENDING.getCode())) {
            throw HttpException.exception(ErrorCodeConstants.WORKFLOW_STEP_STATUS_ERROR, stepId, wfs.getStatus());
        }

        // 商品第一个节点为AI校验审批节点，无需用户验证
        if(!"productWorkFlowStepHandlerStep0".equals(wfs.getHandler())){
            _checkUser(stepId, wfs);
        }
        return wfs;
    }

    // 批量驳回
    public void rejectBatch(List<Integer> stepIds, String comment) {
        stepIds.forEach(stepId -> {
            reject(stepId, comment);
        });
    }

    // 审核驳回
    public void reject(Integer stepId, String comment) {
        // 设置步骤为最后一步
        WorkflowStep wfs = getAndValidate(stepId);
        Workflow wf = wfmapper.selectByPrimaryKey(wfs.getWorkflowId());

        wf.setStatus(WorkFlowStatusEnum.REJECTED.getCode());
        wfs.setStatus(WorkFlowStatusEnum.REJECTED.getCode());

        IWorkFlowStepHandler stepHandler = SpringUtil.getBean(wfs.getHandler());
        IWorkFlowHandler handler = SpringUtil.getBean(wf.getHandler());
        _handlerReject(handler, stepHandler, wf.getContext(), comment);


        wfmapper.updateByPrimaryKeySelective(wf);
        wfsMapper.updateByPrimaryKeySelective(wfs);
        // 插入记录
        _saveLog(CollectionUtil.toList(wf), CollectionUtil.toList(wfs), WorkFlowStatusEnum.REJECTED, comment);
    }

    public void tenantIdReject(Integer stepId, String comment){
        WorkflowStep wfs = getAndValidate(stepId);
        TenantUtils.execute(tenantSrv.getTenantIds(),()->{
            List<WorkflowStep> wfss = wfsMapper.selectByBusCodeAndStatus(wfs.getBusCode(), WorkFlowStatusEnum.PENDING.getCode());
            if (wfss.isEmpty()) {
                log.error("不存在SKU[{}]对应的审核流程:",wfs.getBusCode());
                return;
            }
            wfss.forEach(wf -> reject(wf.getId(), comment));
        });
    }

    public void revoke(String goodsCode, String comment){
        TenantUtils.execute(tenantSrv.getTenantIds(),()->{
            List<WorkflowStep> wfss = wfsMapper.selectByBusCodeAndStatus(goodsCode, WorkFlowStatusEnum.PENDING.getCode());
            if (wfss.isEmpty()) {
                log.error("不存在SKU[{}]对应的审核流程:",goodsCode);
                return;
            }
            wfss.forEach(wf -> reject(wf.getId(), comment));
        });
    }

    @NotNull
    private static WorkflowContext _getWorkflowContext(JSONObject o) {
        WorkflowContext wfc = new WorkflowContext();
        if (o == null) {
            return wfc;
        }

        wfc.setId(o.getLong("id"));
        wfc.setTenantId(o.getLong("tenantId"));
        wfc.setUserId(o.getLong("userId"));

        o.getJSONObject("params").forEach((k, v) -> {
            wfc.getParams().put(k, v.toString());
        });
        return wfc;
    }

    private static void _handlerExpired(IWorkFlowHandler h, IWorkFlowStepHandler sh, String context) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        if (h != null) {
            h.processExpired(workflowContext);
        }
        if (sh != null) {
            sh.processExpired(workflowContext);
        }
    }

    private static void _handlerReject(IWorkFlowHandler h, IWorkFlowStepHandler sh, String context, String comment) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        workflowContext.getParams().put("failReason", comment);
        if (h != null) {
            h.processReject(workflowContext);
        }
        if (sh != null) {
            sh.processReject(workflowContext);
        }
    }

    private static void _handlerPassAll(IWorkFlowHandler h, IWorkFlowStepHandler sh, String context) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        if (h != null) {
            h.processPass(workflowContext);
        }
        if (sh != null) {
            sh.processPass(workflowContext);
        }
    }

    private static void _handlerPass(IWorkFlowStepHandler sh, String context) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        if (sh != null) {
            sh.processPass(workflowContext);
        }
    }

    private static void _handlerPendingAll(IWorkFlowHandler h, IWorkFlowStepHandler sh, String context) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        if (h != null) {
            h.processPending(workflowContext);
        }
        if (sh != null) {
            sh.processPending(workflowContext);
        }
    }

    private static void _handlerPendingStep(IWorkFlowStepHandler sh, String context) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        if (sh != null) {
            sh.processPending(workflowContext);
        }
    }

    private static void _handlerPending(IWorkFlowHandler h, String context) {
        var workflowContext = _getWorkflowContext(JSON.parseObject(context));
        if (h != null) {
            h.processPending(workflowContext);
        }
    }

    private static void _checkUser(Integer stepId, WorkflowStep wfs) {
        if (LocalUserHolder.get() == null) {
            throw HttpException.exception(ErrorCodeConstants.WORKFLOW_STEP_USER_NONE, stepId, "null");
        }

        AtomicBoolean isUser = new AtomicBoolean(false);
        StrUtil.split(wfs.getUserIds(), ",").forEach(userId -> {
            if (LocalUserHolder.get().getId().equals(Long.valueOf(userId))) {
                isUser.set(true);
            }
        });
        if (!isUser.get()) {
            throw HttpException.exception(ErrorCodeConstants.WORKFLOW_STEP_USER_NONE, stepId, LocalUserHolder.get().getId());
        }
    }

    private void _saveLog(Collection<Workflow> wfls, Collection<WorkflowStep> wfsls, WorkFlowStatusEnum status, String comment) {
        x.saveLog(wfls, wfsls, status, comment);
    }

    /**
     * 批量审核
     *
     * @param list {@link List<ApproveImportExcelVO>} 导入的数据
     * @return {@link ApproveImportRespVO}
     */
    public ApproveImportRespVO importApprove(List<ApproveImportExcelVO> list) {
        ApproveImportRespVO rvo = ApproveImportRespVO.builder().build();
        rvo.setFailureList(new ArrayList<>(6));

        TenantUtils.execute(tenantSrv.getTenantIds(),()->{
            list.forEach(item -> {
                List<WorkflowStep> wfss = wfsMapper.selectByBusCodeAndStatus(item.getSku(), WorkFlowStatusEnum.PENDING.getCode());
                if (wfss.isEmpty()) {
                    rvo.getFailureList().add("不存在该SKU对应的审核流程:" + item.getSku());
                    return;
                }

                // 检查一下商品标签字段
                if (StrUtil.isNotBlank(item.getShopGoodsLabel())) {
                    List<String> goods_Label = StrUtil.split(item.getShopGoodsLabel(), ",").stream().map(String::trim).collect(Collectors.toList());

                    systemDictDataMapper.selectList(new LambdaQueryWrapperX<SystemDictDataEntity>()
                            .in(SystemDictDataEntity::getDictType,GoodsLabelEnum.getCodeList()));
                }

                wfss.forEach(wfs -> {
                    if (item.getStatus().equals(WorkFlowStatusEnum.APPROVED.getCode())) {
                        try {
                            approve(wfs.getId(), item.getContent());
                        } catch (Exception ex) {
                            rvo.getFailureList().add("审核失败：" + item.getSku());
                        }

                    } else if (item.getStatus().equals(WorkFlowStatusEnum.REJECTED.getCode())) {
                        try {
                            reject(wfs.getId(), item.getContent());
                        } catch (Exception ex) {
                            rvo.getFailureList().add("驳回失败：" + item.getSku());
                        }
                    } else {
                        rvo.getFailureList().add("该商品已经审批：" + item.getSku());
                    }
                });
            });
        });
        rvo.setFailureList(CollUtil.distinct(rvo.getFailureList()));
        return rvo;
    }

    public List<WorkFlowExcelVO> selectListWorkFlow(WorkflowReq queryDto) {
        if(Objects.nonNull(queryDto.getCode()) && queryDto.getCode().split(",").length > 50){
            throw new ParameterException("sku查询数量不能超过50个");
        }
        if(Objects.nonNull(queryDto.getGoodsCode()) && queryDto.getGoodsCode().split(",").length > 50){
            throw new ParameterException("商品编码查询数量不能超过50个");
        }
        queryDto.setTenantId(TenantContextHolder.getTenantId());
       return this.wfmapper.selectListWorkFlow(queryDto);
    }

    public List<WorkflowAuditRecord> getAuditRecordList(String busCodeX){
        return workflowAuditRecordMapper.selectList(new LambdaQueryWrapperX<WorkflowAuditRecord>()
                .eq(WorkflowAuditRecord::getBusCodeX,busCodeX)
                .eq(WorkflowAuditRecord::getStepName,"商品上架审核")
        );
    }

    /**
     * AI自动审批
     */
    public void deepSeekAutoApprove(){
        //1. 查询全部待审批的节点
        List<WorkflowStep> wfsList = wfsMapper.selectAllByAiAudit();

        //2. 数据交给deepseek解析
        wfsList.parallelStream().forEach( wfs ->{
            if(!aiAutoApprove){
                // 不启动AI审核，直接通过
                this.approve(wfs.getId(),"自动审核");
                return;
            }

            HuoshanArkApiResultDTO result = huoshanDeepseekProduct.processProductInfo(wfs.getBusCodeX(),null);

            // 暂时只考虑AI已处理的情况，AI无法处理的异常需要人工干预
            if (result != null && result.isSuccess()) {
                // 合并非空校验与空字符串校验（避免两次遍历）

                boolean isValid = Stream.of(
                                result.getMaterialName(),
                                result.getBrand(),
                                result.getModel(),
                                result.getUnit()
                        )
                        .allMatch(str -> str != null && !str.isEmpty());

                List<String> missingFields = Stream.of(
                                new AbstractMap.SimpleEntry<>("物料名称", result.getMaterialName()),
                                new AbstractMap.SimpleEntry<>("品牌信息", result.getBrand()),
                                new AbstractMap.SimpleEntry<>("商品型号", result.getModel()),
                                new AbstractMap.SimpleEntry<>("计量单位", result.getUnit())
                        )
                        .filter(entry -> StringUtils.isBlank(entry.getValue()))
                        .map(entry -> entry.getKey() + "缺失")
                        .collect(Collectors.toList());

                String stateDesc = missingFields.isEmpty()
                        ? "校验通过"
                        : "校验未通过:" + String.join(";", missingFields);

                if(isValid){
                    this.approve(wfs.getId(),stateDesc);
                }else {
                    this.reject(wfs.getId(),stateDesc);
                }
                log.info("deepSeekAutoApprove 商品[{}]信息处理完成", wfs.getBusCodeX());
            }
        });
    }


    public List<SystemUsers> getNextStep(Integer stepId) {
        return wfsMapper.getNextStep(stepId);
    }
}


