package com.ly.yph.api.order.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ly.yph.api.customization.common.CompanyConstant;
import com.ly.yph.api.customization.common.CompanyEnum;
import com.ly.yph.api.goods.entity.*;
import com.ly.yph.api.goods.enums.GoodsModelEnum;
import com.ly.yph.api.goods.es.index.GoodsIndex;
import com.ly.yph.api.goods.es.manager.GoodsEsManager;
import com.ly.yph.api.goods.service.*;
import com.ly.yph.api.goods.vo.ContractInfoVo;
import com.ly.yph.api.goods.vo.ShopGoodsContractVo;
import com.ly.yph.api.order.common.SpinnerWriteHandler;
import com.ly.yph.api.order.config.DfsYamlConfig;
import com.ly.yph.api.order.config.OpenApiConfig;
import com.ly.yph.api.order.config.YflYamlConfig;
import com.ly.yph.api.order.convert.ShopCartConvert;
import com.ly.yph.api.order.dto.CartSaveDto;
import com.ly.yph.api.order.dto.CartSubmitDto;
import com.ly.yph.api.order.dto.FloatPriceGoodsDto;
import com.ly.yph.api.order.dto.ShopCartExportDto;
import com.ly.yph.api.order.entity.*;
import com.ly.yph.api.order.exception.CartException;
import com.ly.yph.api.order.exception.GoodsCheckException;
import com.ly.yph.api.order.exception.OrderException;
import com.ly.yph.api.order.mapper.ShopCartMapper;
import com.ly.yph.api.order.vo.*;
import com.ly.yph.api.organization.entity.SystemOrganization;
import com.ly.yph.api.organization.entity.SystemUserRole;
import com.ly.yph.api.organization.entity.SystemUsers;
import com.ly.yph.api.organization.service.SystemOrganizationService;
import com.ly.yph.api.organization.service.SystemUserRoleService;
import com.ly.yph.api.organization.service.SystemUsersService;
import com.ly.yph.api.price.monitor.service.PriceMonitorService;
import com.ly.yph.api.product.ext.common.dto.request.RegionDto;
import com.ly.yph.api.product.ext.common.dto.response.RemotePriceInfoResp;
import com.ly.yph.api.product.ext.common.manage.BatchPriceGetter;
import com.ly.yph.api.supplier.entity.ShopSupplier;
import com.ly.yph.api.supplier.entity.SystemContractEntity;
import com.ly.yph.api.supplier.service.ShopSupplierService;
import com.ly.yph.api.supplier.service.SystemContractService;
import com.ly.yph.api.system.dto.CartQueryDto;
import com.ly.yph.api.system.entity.ShopAddress;
import com.ly.yph.api.system.entity.ShopRequirement;
import com.ly.yph.api.system.mapper.ShopRequirementMapper;
import com.ly.yph.api.system.service.ShopAddressService;
import com.ly.yph.api.system.vo.ShopAddressVo;
import com.ly.yph.api.utils.FormulaUtil;
import com.ly.yph.api.utils.dto.PriceUtilDto;
import com.ly.yph.api.zone.entity.GoodsZoneEntity;
import com.ly.yph.api.zone.service.GoodsZoneService;
import com.ly.yph.core.base.ServiceResult;
import com.ly.yph.core.base.exception.types.ParameterException;
import com.ly.yph.core.base.page.DataAdapter;
import com.ly.yph.core.base.query.LambdaQueryWrapperX;
import com.ly.yph.core.base.user.LocalUserHolder;
import com.ly.yph.core.base.user.LoginUser;
import com.ly.yph.core.dic.core.entity.SystemDictDataEntity;
import com.ly.yph.core.dic.core.service.DictDataService;
import com.ly.yph.core.excel.util.ExcelUtils;
import com.ly.yph.core.lock.RedissonLock;
import com.ly.yph.core.thread.ThreadUtil;
import com.ly.yph.core.util.BeanUtil;
import com.ly.yph.tenant.core.context.TenantContextHolder;
import com.ly.yph.tenant.core.util.TenantUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 购物车表(ShopCart)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-16 18:51:46
 */
@Service
@Slf4j
public class ShopCartService extends ServiceImpl<ShopCartMapper, ShopCart> {
    public static final int KM = 1000;
    public static final String DFMALL_DATASOURCE = "dfmall";

    public static final String ORDER_LIMIT_ROLE_CODE = "JZXD";
    @Resource
    private ShopCartMapper shopCartMapper;

    @Resource
    private ShopGoodsService shopGoodsService;

    @Resource
    private BatchPriceGetter priceGetter;

    @Resource
    private YphStandardClassService yphStandardClassService;

    @Resource
    private ShopGoodsDetailService shopGoodsDetailService;

    @Resource
    private ShopPurchaseOrderService purchaseOrderService;

    @Resource
    private ShopAddressService shopAddressService;

    @Resource
    private DictDataService dictDataService;

    @Resource
    private GoodsEsManager goodsEsManager;

    @Resource
    private ShopMaterialRelationService shopMaterialRelationService;

    @Resource
    private SystemUsersService systemUsersService;

    @Resource
    private ShopSupplierService supplierSrv;

    @Resource
    private DfsYamlConfig dfsYamlConfig;

    @Resource
    private ShopRequirementMapper shopRequirementMapper;

    @Resource
    private SystemContractService systemContractService;

    @Resource
    private FormulaUtil formulaUtil;

    @Resource
    private ShopSrmContractDetailService shopSrmContractDetailService;

    @Resource
    private ShopSrmContractService srmContractSrv;

    @Resource
    private ShopSrmContractService shopSrmContractService;

    @Resource
    private RedissonLock redissonLock;

    @Resource
    private YflYamlConfig yflYamlConfig;

    @Resource
    private GoodsZoneService goodsZoneService;

    @Resource
    private DfmallGoodsPoolSubService dfmallGoodsPoolSubService;

    @Resource
    private ShopGoodsContractService shopGoodsContractService;

    @Resource
    private ShopGoodsContractService goodsSettlementService;

    @Resource
    private ShopGoodsSameService goodsSameService;

    @Resource
    private SystemOrganizationService organizationService;

    @Resource
    private OpenApiConfig openApiConfig;
    @Resource
    private PriceMonitorService priceMonitorSrv;

    @Resource
    private SystemUserRoleService systemUserRoleService;

    /**
     * 通过ID查询单条数据
     *
     * @param cartId 主键
     * @return 实例对象
     */
    public ShopCart queryById(final long cartId) {
        return this.shopCartMapper.queryById(cartId);
    }

    /**
     * 购物车列表实时数据查询
     *
     * @return {@link List}<{@link ShopCartVo}>
     */
    public List<ShopCartVo> cartList(Long areaId) {
        LoginUser user = LocalUserHolder.get();
        // 先查购物车数据库中数据
        List<ShopCartVo> cartResult = this.baseMapper.cartList(user.getId(), areaId);
        if (CollUtil.isEmpty(cartResult)) {
            return cartResult;
        }
        // 填充SAP参考编码
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            cartResult.forEach(shopCartVo -> {
                QueryWrapper<ShopMaterialRelationEntity> maraQueryWrapper = new QueryWrapper<>();
                maraQueryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, user.getEntityOrganizationCode())
                        .eq(ShopMaterialRelationEntity::getGoodsCode, shopCartVo.getGoodsCode())
                        .eq(ShopMaterialRelationEntity::getIsDel, 0);
                List<ShopMaterialRelationEntity> shopMaterialRelationEntities = shopMaterialRelationService.list(maraQueryWrapper);
                Map<String, String> goodsCodeMap = shopMaterialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr, (key1, key2) -> key1));
                shopCartVo.setMaraMatnr(goodsCodeMap.get(shopCartVo.getGoodsCode()));
            });
        }
        // 再批量查询价格,batch
        Map<String, RemotePriceInfoResp> goodsPriceMap = priceGetter.batchGetSalePrice(cartResult.stream().map(ShopCartVo::getGoodsCode).collect(Collectors.toSet()));

        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            List<Long> goodsIds = cartResult.stream().map(ShopCartVo::getGoodsId).collect(Collectors.toList());
            SystemOrganization organization = organizationService.getOrganizationByCode(user.getEntityOrganizationCode());
            List<ContractInfoVo> contractInfoVos = goodsSettlementService.getContractInfoByGoodsIdAndCompany(goodsIds, organization.getId());
            Map<Long, ContractInfoVo> collect = contractInfoVos.stream().collect(Collectors.toMap(ContractInfoVo::getGoodsId, Function.identity(), (key1, key2) -> key1));
            cartResult.forEach(item -> {
                ContractInfoVo contractInfoVo = collect.get(item.getGoodsId());
                if (!Objects.isNull(contractInfoVo)) {
                    item.setContractId(Convert.toStr(contractInfoVo.getContractId()));
                    item.setVoyahContractNumber(contractInfoVo.getContractCode());
                }
            });
        }


        // 更新实时价格、校验是否超出设置折扣范围。若实时价格与购物车价格不同,则更新到数据库。
        cartResult.forEach(cart -> {
            if (StringUtils.hasText(cart.getContractNumber()) && "DFS".equals(user.getOrgRangeMark())) {
                //币种显示
                ShopSrmContractEntity srmContractNumber = shopSrmContractService.getBySrmContractNumber(cart.getContractNumber());
                cart.setCurrencyCode(srmContractNumber.getCurrencyCode());
                cart.setCurrencyName(srmContractNumber.getCurrencyName());
            }
            String goodsCode = cart.getGoodsCode();
            if (StrUtil.isBlank(goodsCode)) {
                // 不存在的商品直接跳过
                return;
            }
            RemotePriceInfoResp priceInfo = goodsPriceMap.get(goodsCode);
            // 本次查不出价格的商品视为下架,下次进入购物车回再查询
            if (priceInfo == null || BigDecimal.ZERO.equals(priceInfo.getGoodsSalePrice())) {
                cart.setShelvesState(0L);
                cart.setMsg(priceInfo == null ? "查询为空" : priceInfo.getMsg());
                return;
            }

            Integer priceMonitorResult = priceMonitorSrv.doPriceMonitor(goodsCode, user.getEntityOrganizationCode(), priceInfo.getGoodsPactPrice());
            cart.setPriceMonitorResult(priceMonitorResult);

            //查询商品多规格数据
            Map<String,Object> sameMap = goodsSameService.queryGoodsSpecArray(cart.getSameCode());
            cart.setSameSpecMap(sameMap);

            //实时查询数据商品可售标签
            LambdaQueryWrapper<ShopGoods> getGoodsLabel = new LambdaQueryWrapper<>();
            getGoodsLabel.select(ShopGoods::getAuthMode,ShopGoods::getGoodsLabel,ShopGoods::getShelvesState,ShopGoods::getThirdClassName)
                    .eq(ShopGoods::getGoodsId,cart.getGoodsId());
            ShopGoods goods = shopGoodsService.getOne(getGoodsLabel);
            cart.setAuthMode(goods.getAuthMode());
            cart.setGoodsLabel(goods.getGoodsLabel());

            if (yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
                cart.setShelvesState(Long.valueOf(goods.getShelvesState()));
                if(GoodsModelEnum.VIRTUAL.getModelCode().equals(cart.getGoodsModel())){
                    if ("直充类".equalsIgnoreCase(goods.getThirdClassName())) {
                        cart.setVirtualType("1");
                    } else if ("卡密类".equalsIgnoreCase(goods.getThirdClassName())) {
                        cart.setVirtualType("2");
                    }
                }
            } else {
                //查询商品在企业上架状态
                Long shelves = goodsZoneService.queryUserGoodIsSale(cart.getGoodsId(), user.getId());
                cart.setShelvesState(shelves);
            }

            // 更新返回给前端的是否超出设置折扣范围状态
            cart.setDiscountRange(priceInfo.getDiscountRange());
            cart.setMsg(priceInfo.getMsg());
            // 能查询价格的比较价格是否有变化,有则更新到数据库;
            if (!cart.getGoodsSalePrice().equals(priceInfo.getGoodsSalePrice()) || !cart.getGoodsSaleNakedPrice().equals(priceInfo.getGoodsNakedSalePrice())) {
                // 更新购物车数据
                ShopCart updateCart = new ShopCart();
                updateCart.setCartId(cart.getCartId());
                updateCart.setGoodsSalePrice(priceInfo.getGoodsSalePrice());
                updateCart.setGoodsSaleNakedPrice(priceInfo.getGoodsNakedSalePrice());
                this.updateById(updateCart);

                // 更新当前查询返回结果
                cart.setGoodsSalePrice(priceInfo.getGoodsSalePrice());
                cart.setGoodsSaleNakedPrice(priceInfo.getGoodsNakedSalePrice());
            }
            String contractNumber = cart.getContractNumber();
            // 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式  3:发动机模式  4:岚图模式
            Integer pricingMode = 0;
            if (StrUtil.isBlank(contractNumber)) {
                pricingMode = 0;
            }else {
                if("DFS".equals(user.getOrgRangeMark())){
                    // 走srm合同的单子按照合同要求计价
                    ShopSrmContractEntity srmContract = srmContractSrv.getBySrmContractNumber(contractNumber);
                    // 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式
                    pricingMode = srmContract.getPricingMode();
                }
            }
            if(StrUtil.startWith(goodsCode,CompanyEnum.DHEC.getCompanyCode())){
                pricingMode = 3;
            }
            if(StrUtil.isNotBlank(cart.getVoyahContractNumber())){
                pricingMode = 4;
            }
            PriceUtilDto priceUtilDto = formulaUtil.pricingModeUtil(BigDecimal.ZERO, BigDecimal.ZERO, priceInfo.getGoodsNakedSalePrice(), priceInfo.getGoodsSalePrice(), cart.getTaxRate(), cart.getGoodsNum().intValue(), cart.getGoodsNumDecimal(), pricingMode);
            cart.setGoodsSaleNakedFinalPrice(priceUtilDto.getGoodsPrice().getTotalPriceNaked());
            cart.setGoodsSaleFinalPrice(priceUtilDto.getGoodsPrice().getTotalPriceTax());

            cart.setSettlementType(getGoodsSettlement(cart.getGoodsId(),cart.getSupplierCode(),user.getEntityOrganizationId()));
        });
        return cartResult;
    }

    public ShopCartVo cartRow(Long areaId, Long cartId) {
        ShopCartVo cartResult = baseMapper.cartRow(areaId, cartId);
        if (cartResult == null) {
            return cartResult;
        }

        LoginUser user = LocalUserHolder.get();
        //填充SAP参考编码
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            QueryWrapper<ShopMaterialRelationEntity> maraQueryWrapper = new QueryWrapper<>();
            maraQueryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, user.getEntityOrganizationCode())
                    .eq(ShopMaterialRelationEntity::getGoodsCode, cartResult.getGoodsCode())
                    .eq(ShopMaterialRelationEntity::getIsDel, 0);
            List<ShopMaterialRelationEntity> shopMaterialRelationEntities = shopMaterialRelationService.list(maraQueryWrapper);
            Map<String, String> goodsCodeMap = shopMaterialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr, (key1, key2) -> key1));
            cartResult.setMaraMatnr(goodsCodeMap.get(cartResult.getGoodsCode()));
        }

        // 查询价格
        String goodsCode = cartResult.getGoodsCode();
        RemotePriceInfoResp priceInfo = priceGetter.getSalePrice(goodsCode);

        // 更新实时价格、校验是否超出设置折扣范围。若实时价格与购物车价格不同,则更新到数据库。
        List<ShopCart> updateCartList = new ArrayList<>();
        if (StrUtil.isNotBlank(cartResult.getContractNumber())) {
            if("DFS".equals(user.getOrgRangeMark())) {
                // 币种显示
                ShopSrmContractEntity srmContractNumber = shopSrmContractService.getBySrmContractNumber(cartResult.getContractNumber());
                cartResult.setCurrencyCode(srmContractNumber.getCurrencyCode());
                cartResult.setCurrencyName(srmContractNumber.getCurrencyName());
            }
        }

        if (StrUtil.isBlank(goodsCode)) {
            // 不存在的商品直接跳过
            return cartResult;
        }
        // 本次查不出价格的商品视为下架,下次进入购物车回再查询
        if (priceInfo == null || BigDecimal.ZERO.equals(priceInfo.getGoodsSalePrice())) {
            cartResult.setShelvesState(0L);
            cartResult.setMsg(priceInfo == null ? "查询为空" : priceInfo.getMsg());
            return cartResult;
        }

        Integer priceMonitorResult = priceMonitorSrv.doPriceMonitor(goodsCode, user.getEntityOrganizationCode(), priceInfo.getGoodsPactPrice());
        cartResult.setPriceMonitorResult(priceMonitorResult);

        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            Long goodsId = cartResult.getGoodsId();
            SystemOrganization organization = organizationService.getOrganizationByCode(user.getEntityOrganizationCode());
            List<ContractInfoVo> contractInfoVos = goodsSettlementService.getContractInfoByGoodsIdAndCompany(Collections.singletonList(goodsId), organization.getId());
            Map<Long, ContractInfoVo> collect = contractInfoVos.stream().collect(Collectors.toMap(ContractInfoVo::getGoodsId, Function.identity(), (key1, key2) -> key1));
            ContractInfoVo contractInfoVo = collect.get(goodsId);
            if (!Objects.isNull(contractInfoVo)) {
                cartResult.setContractId(Convert.toStr(contractInfoVo.getContractId()));
                cartResult.setVoyahContractNumber(contractInfoVo.getContractCode());
            }
        }

        // 实时查询数据商品可售标签
        LambdaQueryWrapper<ShopGoods> getGoodsLabel = new LambdaQueryWrapper<>();
        getGoodsLabel.select(ShopGoods::getAuthMode, ShopGoods::getGoodsLabel, ShopGoods::getShelvesState)
                .eq(ShopGoods::getGoodsId, cartResult.getGoodsId());
        ShopGoods goods = shopGoodsService.getOne(getGoodsLabel);
        cartResult.setAuthMode(goods.getAuthMode());
        cartResult.setGoodsLabel(goods.getGoodsLabel());

        if (yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
            cartResult.setShelvesState(Long.valueOf(goods.getShelvesState()));
        } else {
            //查询商品在企业上架状态
            Long shelves = goodsZoneService.queryUserGoodIsSale(cartResult.getGoodsId(), user.getId());
            cartResult.setShelvesState(shelves);
        }

        // 更新返回给前端的是否超出设置折扣范围状态
        cartResult.setDiscountRange(priceInfo.getDiscountRange());
        cartResult.setMsg(priceInfo.getMsg());
        // 能查询价格的比较价格是否有变化,有则更新到数据库;
        if (!cartResult.getGoodsSalePrice().equals(priceInfo.getGoodsSalePrice()) || !cartResult.getGoodsSaleNakedPrice().equals(priceInfo.getGoodsNakedSalePrice())) {
            // 更新购物车数据
            ShopCart updateCart = new ShopCart();
            updateCart.setCartId(cartResult.getCartId());
            updateCart.setGoodsSalePrice(priceInfo.getGoodsSalePrice());
            updateCart.setGoodsSaleNakedPrice(priceInfo.getGoodsNakedSalePrice());
            updateCartList.add(updateCart);

            // 更新当前查询返回结果
            cartResult.setGoodsSalePrice(priceInfo.getGoodsSalePrice());
            cartResult.setGoodsSaleNakedPrice(priceInfo.getGoodsNakedSalePrice());
        }
        String contractNumber = cartResult.getContractNumber();
        // 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式  3:发动机模式  4:岚图模式
        Integer pricingMode = 0;
        if (StrUtil.isBlank(contractNumber)) {
            pricingMode = 0;
        }else {
            if("DFS".equals(user.getOrgRangeMark())) {
                // 走srm合同的单子按照合同要求计价
                ShopSrmContractEntity srmContract = srmContractSrv.getBySrmContractNumber(contractNumber);
                // 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式
                pricingMode = srmContract.getPricingMode();
            }
        }
        if (StrUtil.startWith(goodsCode, CompanyEnum.DHEC.getCompanyCode())) {
            pricingMode = 3;
        }
        if (StrUtil.isNotBlank(cartResult.getVoyahContractNumber())) {
            pricingMode = 4;
        }
        PriceUtilDto priceUtilDto = formulaUtil.pricingModeUtil(BigDecimal.ZERO, BigDecimal.ZERO, priceInfo.getGoodsNakedSalePrice(), priceInfo.getGoodsSalePrice(), cartResult.getTaxRate(), cartResult.getGoodsNum().intValue(), cartResult.getGoodsNumDecimal(), pricingMode);
        cartResult.setGoodsSaleNakedFinalPrice(priceUtilDto.getGoodsPrice().getTotalPriceNaked());
        cartResult.setGoodsSaleFinalPrice(priceUtilDto.getGoodsPrice().getTotalPriceTax());

        cartResult.setSettlementType(getGoodsSettlement(cartResult.getGoodsId(), cartResult.getSupplierCode(), user.getEntityOrganizationId()));
        this.updateBatchById(updateCartList);
        return cartResult;
    }

    /**
     * 商品普通加购物车
     *
     * @param dto dto
     */
    public Long cartSave(CartSaveDto dto) {
        verifyYfl(dto);
        LoginUser user = LocalUserHolder.get();
        String goodsCode = dto.getGoodsCode();
        //需求提报id，不为空表示此次加入购物车操作为需求提报。
        String shopReqId = dto.getShopReqId();
        // 校验srm商品合同是否过期
        if (!yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
            Map<String, ShopSrmContractEntity> stringShopSrmContractEntityMap = shopGoodsService.goodsCheckContract(CollectionUtil.toList(goodsCode), null);
            if (!stringShopSrmContractEntityMap.isEmpty()) {
                throw new ParameterException("[" + goodsCode + "] 合同已过期，请重新选择商品");
            }
        }
        //校验商品合同 dfmall 并且 独立供应商 非对接接口平台供应商
        var contractCheck =  purchaseOrderService.goodsContractCheck(CollectionUtil.toList(goodsCode));
        if (!contractCheck.isEmpty()) {
            throw new GoodsCheckException(JSON.toJSONString(contractCheck));
        }

        GoodsIndex goodsIndex;
        try {
            goodsIndex = goodsEsManager.getByGoodsCode(goodsCode);
        } catch (Exception e) {
            throw new CartException("[" + goodsCode + "]商品已下架");
        }
        if (goodsIndex == null) {
            throw new CartException("[" + goodsCode + "]商品已下架");
        }

        if (StrUtil.isNotBlank(dto.getCartId())) {
            // 有cartId则为购物车中增减数量,不做校验,列表查询的地方会校验价格。此处有小坑,没校验库存,之前逻辑也没校验,保持一致
            ShopCart shopCart = this.getById(dto.getCartId());
            //需求提报中加入购物车
            if (StringUtils.hasText(shopReqId)) {
                shopCart.setShopReqId(shopReqId);
            }



            // 如果加购数量小于起订量,则变为起订量
            if (dto.getGoodsNumDecimal().compareTo(BigDecimal.ZERO) == 0 ) {
                //普通购物车
                shopCart.setGoodsNum(Math.max(Long.parseLong(goodsIndex.getGoodsMoq()), shopCart.getGoodsNum() + dto.getGoodsNum()));
            } else if (dto.getGoodsNum() == 0) {
                //srm购物车
                shopCart.setGoodsNumDecimal(new BigDecimal(goodsIndex.getGoodsMoq()).max(shopCart.getGoodsNumDecimal().add(dto.getGoodsNumDecimal())));
            }
            if (yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
                if (GoodsModelEnum.VIRTUAL.getModelCode().equals(shopCart.getGoodsModel())) {
                    //友福利的虚拟商品购买数量只能是起订量 ，不能加
                    shopCart.setGoodsNum(Convert.toLong(goodsIndex.getGoodsMoq()));
                }
            }
            if (shopCart.getGoodsNum() <= 0 && shopCart.getGoodsNumDecimal().compareTo(BigDecimal.ZERO) <= 0) {
                throw new CartException("[" + goodsCode + "]商品加购数量异常");
            }
            this.updateById(shopCart);
            return shopCart.getCartId();
        } else {

            // 没有cartId则为新增商品进购物车,需要校验
            ShopAddress shopAddress;
            if (StrUtil.isNotBlank(dto.getAddressId())) {
                shopAddress = this.shopAddressService.getById(dto.getAddressId());
            } else {
                // 查询默认地址
                shopAddress = this.shopAddressService.getDefaultAddress(user.getUsername());
                if (shopAddress == null) {
                    // 如果个人地址为空，则取工厂地址
                    shopAddress = this.shopAddressService.getFactoryAddress(user.getUsername());
                }
            }
            if (shopAddress == null) {
                throw new CartException("请在【个人中心】-【地址管理】中设置默认地址");
            }

            String zoneGoodsType = null;
            GoodsZoneEntity goodsZoneEntity;
            if(dto.getGoodsZoneId()!= null){
                goodsZoneEntity = goodsZoneService.getById(dto.getGoodsZoneId());
                if(goodsZoneEntity != null){
                    if(goodsZoneEntity.getCartMergeSubmit()==1){
                        dto.setGoodsZoneId(null);
                    }
                    // 获取专区小分类
                    DfmallGoodsPoolSubEntity dfmallGoods = dfmallGoodsPoolSubService.queryGoodsPoolsSub(goodsZoneEntity.getGoodsPoolId(), Long.valueOf(goodsIndex.getGoodsId()));
                    if(dfmallGoods != null){
                        zoneGoodsType = dfmallGoods.getZoneGoodsType();
                    }
                }
            }

            //由于重复点击，导致上一条未加购成功，第二次请求来未查询到数据，会保存两条数据
            String lockName = user.getId() + '_' + goodsIndex.getGoodsCode();
            redissonLock.lock(lockName, 10);

            QueryWrapper<ShopCart> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopCart::getGoodsCode, goodsIndex.getGoodsCode()).eq(ShopCart::getUserId, user.getId()).eq(ShopCart::getCartType, 0);
            if (StrUtil.isNotBlank(dto.getGoodsIntegralType())) {
                queryWrapper.lambda().eq(ShopCart::getGoodsIntegralType, dto.getGoodsIntegralType());
            }
            if (dto.getGoodsZoneId()!=null) {
                queryWrapper.lambda().eq(ShopCart::getGoodsZoneId, dto.getGoodsZoneId());
            }else{
                queryWrapper.lambda().isNull(ShopCart::getGoodsZoneId);
            }
            ShopCart shopCart = this.getOne(queryWrapper);
            if (shopCart == null) {
                shopCart = ShopCartConvert.INSTANCE.convert(goodsIndex);
                if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                        || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                        || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                        || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
                    if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(goodsIndex.getSupplierDataSource())
                            || CompanyConstant.S4_DATA_SOURCE.equalsIgnoreCase(goodsIndex.getSupplierDataSource())
                            || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(goodsIndex.getSupplierDataSource())) {
                        ShopGoodsContractEntity shopGoodsContractEntity = shopGoodsContractService.queryByGoodsId(Long.valueOf(goodsIndex.getGoodsId()), user.getEntityOrganizationId());
                        SystemContractEntity byId = systemContractService.getById(shopGoodsContractEntity.getContractId());
                        shopCart.setContractNumber(byId.getContractCode());
                    }
                }
                shopCart.setGoodsId(Long.valueOf(goodsIndex.getGoodsId()));
                shopCart.setGoodsNum(dto.getGoodsNum());
                shopCart.setGoodsNumDecimal(dto.getGoodsNumDecimal());

                BigDecimal sum = shopCart.getGoodsNumDecimal().add(new BigDecimal(shopCart.getGoodsNum()));
                if(sum.compareTo(BigDecimal.ZERO) == 0){
                    throw new CartException("[" + goodsCode + "]购买数量不能为0");
                }
                ShopGoods goods = shopGoodsService.getOne(new LambdaQueryWrapperX<ShopGoods>().select(ShopGoods::getTaxRate,ShopGoods::getGoodsModel).eq(ShopGoods::getGoodsCode, goodsCode));
                shopCart.setGoodsModel(goods.getGoodsModel());
                shopCart.setTaxRate(goods.getTaxRate());
                shopCart.setCartType(0);
                shopCart.setUserId(user.getId());
                shopCart.setOrganizationId(user.getOrganizationId());
                YphStandardClassEntity standardClassEntity = yphStandardClassService.getById(goodsIndex.getThirdLevelGcid());
                if (standardClassEntity == null) {
                    throw new CartException("[" + goodsCode + "]对应的分类不存在，请联系管理员");
                }

                SystemDictDataEntity dictDataEty = dictDataService.getDictData("A002", standardClassEntity.getTypeCode());
                shopCart.setGoodsTypeId(dictDataEty.getId());
                shopCart.setGoodsIntegralType(dto.getGoodsIntegralType());
                shopCart.setGoodsZoneId(dto.getGoodsZoneId());
                shopCart.setGoodsSalePrice(goods.getPrice());
                shopCart.setGoodsSaleNakedPrice(goods.getNakePrice());
                shopCart.setCartZoneGoodsType(zoneGoodsType);

                // 查询商品结算方式
                ShopSupplier supplier = supplierSrv.selectByCode(goodsIndex.getSupplierCode());
                if("dfmall".equals(supplier.getDataSource())){


                }else {
                    // 非商城供应商，全都不走商城结算
                    shopCart.setCartType(0);
                }
            } else {
                shopCart.setGoodsNum(shopCart.getGoodsNum() + dto.getGoodsNum());
                shopCart.setGoodsNumDecimal(shopCart.getGoodsNumDecimal().add(dto.getGoodsNumDecimal()));
                if (yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
                    if (GoodsModelEnum.VIRTUAL.getModelCode().equals(shopCart.getGoodsModel())) {
                        //友福利的虚拟商品购买数量只能是起订量 ，不能加
                        shopCart.setGoodsNum(Convert.toLong(goodsIndex.getGoodsMoq()));
                    }
                }
            }
            //需求提报中加入购物车
            if (StringUtils.hasText(shopReqId)) {
                shopCart.setShopReqId(shopReqId);
            }
            // 如果加购数量小于起订量,则变为起订量
            if (shopCart.getGoodsNumDecimal().compareTo(BigDecimal.ZERO) > 0 && shopCart.getGoodsNumDecimal().compareTo(new BigDecimal(goodsIndex.getGoodsMoq())) < 0) {
                shopCart.setGoodsNumDecimal(new BigDecimal(goodsIndex.getGoodsMoq()));
            } else if (shopCart.getGoodsNumDecimal().compareTo(BigDecimal.ZERO) == 0) {
                shopCart.setGoodsNum(Math.max(Long.parseLong(goodsIndex.getGoodsMoq()), shopCart.getGoodsNum()));
            }

            RegionDto regionDto = new RegionDto();
            regionDto.setProvince(shopAddress.getProvince());
            regionDto.setCity(shopAddress.getCity());
            regionDto.setCounty(shopAddress.getDistrict());
            // 可售、上下架、区域限售、库存、价格校验
            ShopCart finalShopCart = shopCart;
            var checkMap = purchaseOrderService.goodsCheck(MapUtil.of(goodsCode, Math.toIntExact(shopCart.getGoodsNum())), regionDto, shopAddress.getAddress(), priceMap -> {
                RemotePriceInfoResp resp = priceMap.get(goodsCode);
                finalShopCart.setGoodsSalePrice(resp.getGoodsSalePrice());
                finalShopCart.setGoodsSaleNakedPrice(resp.getGoodsNakedSalePrice());
            });

            if (!checkMap.isEmpty()) {
                throw new CartException(checkMap.get(goodsCode));
            } else {
                this.saveOrUpdate(shopCart);
            }
            redissonLock.unlock(lockName);
            return shopCart.getCartId();
        }
    }

    public Long nonPlatformCartSave(CartSaveDto dto) {
        verifyYfl(dto);
        LoginUser user = LocalUserHolder.get();
        String goodsCode = dto.getGoodsCode();
        //需求提报id，不为空表示此次加入购物车操作为需求提报。
        String shopReqId = dto.getShopReqId();
        //校验商品合同 dfmall 并且 独立供应商 非对接接口平台供应商
        var contractCheck =  purchaseOrderService.goodsContractCheck(CollectionUtil.toList(goodsCode));
        if (!contractCheck.isEmpty()) {
            throw new GoodsCheckException(JSON.toJSONString(contractCheck));
        }

        if (StrUtil.isNotBlank(dto.getCartId())) {
            // 有cartId则为购物车中增减数量,不做校验,列表查询的地方会校验价格。此处有小坑,没校验库存,之前逻辑也没校验,保持一致
            ShopCart shopCart = this.getById(dto.getCartId());
            //需求提报中加入购物车
            if (StringUtils.hasText(shopReqId)) {
                shopCart.setShopReqId(shopReqId);
            }
            shopCart.setGoodsNum(shopCart.getGoodsNum() + dto.getGoodsNum());
            shopCart.setGoodsNumDecimal(shopCart.getGoodsNumDecimal().add(dto.getGoodsNumDecimal()));
            this.updateById(shopCart);
            return shopCart.getCartId();
        } else {

            // 没有cartId则为新增商品进购物车,需要校验
            ShopAddress shopAddress;
            if (StrUtil.isNotBlank(dto.getAddressId())) {
                shopAddress = this.shopAddressService.getById(dto.getAddressId());
            } else {
                // 查询默认地址
                shopAddress = this.shopAddressService.getDefaultAddress(user.getUsername());
                if (shopAddress == null) {
                    // 如果个人地址为空，则取工厂地址
                    shopAddress = this.shopAddressService.getFactoryAddress(user.getUsername());
                }
            }
            if (shopAddress == null) {
                throw new CartException("请在【个人中心】-【地址管理】中设置默认地址");
            }

            GoodsIndex goodsIndex;
            try {
                goodsIndex = goodsEsManager.getByGoodsCode(goodsCode);
            } catch (Exception e) {
                throw new CartException("[" + goodsCode + "]商品已下架");
            }
            if (goodsIndex == null) {
                throw new CartException("[" + goodsCode + "]商品已下架");
            }

            String zoneGoodsType = null;
            GoodsZoneEntity goodsZoneEntity;
            if(dto.getGoodsZoneId()!= null){
                goodsZoneEntity = goodsZoneService.getById(dto.getGoodsZoneId());
                if(goodsZoneEntity != null){
                    if(goodsZoneEntity.getCartMergeSubmit()==1){
                        dto.setGoodsZoneId(null);
                    }
                    // 获取专区小分类
                    DfmallGoodsPoolSubEntity dfmallGoods = dfmallGoodsPoolSubService.queryGoodsPoolsSub(goodsZoneEntity.getGoodsPoolId(), Long.valueOf(goodsIndex.getGoodsId()));
                    if(dfmallGoods != null){
                        zoneGoodsType = dfmallGoods.getZoneGoodsType();
                    }
                }
            }

            //由于重复点击，导致上一条未加购成功，第二次请求来未查询到数据，会保存两条数据
            String lockName = user.getId() + '_' + goodsIndex.getGoodsCode();
            redissonLock.lock(lockName, 10);

            QueryWrapper<ShopCart> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopCart::getGoodsCode, goodsIndex.getGoodsCode()).eq(ShopCart::getUserId, user.getId()).eq(ShopCart::getCartType, dto.getCartType());
            if (StrUtil.isNotBlank(dto.getGoodsIntegralType())) {
                queryWrapper.lambda().eq(ShopCart::getGoodsIntegralType, dto.getGoodsIntegralType());
            }
            if (dto.getGoodsZoneId()!=null) {
                queryWrapper.lambda().eq(ShopCart::getGoodsZoneId, dto.getGoodsZoneId());
            }else{
                queryWrapper.lambda().isNull(ShopCart::getGoodsZoneId);
            }
            ShopCart shopCart = this.getOne(queryWrapper);
            if (shopCart == null) {
                shopCart = ShopCartConvert.INSTANCE.convert(goodsIndex);
                if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                        || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                        || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                        || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
                    if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(goodsIndex.getSupplierDataSource())
                            || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(goodsIndex.getSupplierDataSource())
                            || CompanyConstant.S4_DATA_SOURCE.equalsIgnoreCase(goodsIndex.getSupplierDataSource())) {
                        ShopGoodsContractEntity shopGoodsContractEntity = shopGoodsContractService.queryByGoodsId(Long.valueOf(goodsIndex.getGoodsId()), user.getEntityOrganizationId());
                        SystemContractEntity byId = systemContractService.getById(shopGoodsContractEntity.getContractId());
                        shopCart.setContractNumber(byId.getContractCode());
                    }
                }
                shopCart.setGoodsId(Long.valueOf(goodsIndex.getGoodsId()));
                shopCart.setGoodsNum(dto.getGoodsNum());
                shopCart.setGoodsNumDecimal(dto.getGoodsNumDecimal());

                BigDecimal sum = shopCart.getGoodsNumDecimal().add(new BigDecimal(shopCart.getGoodsNum()));
                if(sum.compareTo(BigDecimal.ZERO) == 0){
                    throw new CartException("[" + goodsCode + "]购买数量不能为0");
                }

                ShopGoods goods = shopGoodsService.getOne(new LambdaQueryWrapperX<ShopGoods>().select(ShopGoods::getTaxRate).eq(ShopGoods::getGoodsCode, goodsCode));
                shopCart.setTaxRate(goods.getTaxRate());
                shopCart.setCartType(dto.getCartType());
                shopCart.setUserId(user.getId());
                shopCart.setOrganizationId(user.getOrganizationId());
                YphStandardClassEntity standardClassEntity = yphStandardClassService.getById(goodsIndex.getThirdLevelGcid());
                if (standardClassEntity == null) {
                    throw new CartException("[" + goodsCode + "]对应的分类不存在，请联系管理员");
                }

                SystemDictDataEntity dictDataEty = dictDataService.getDictData("A002", standardClassEntity.getTypeCode());
                shopCart.setGoodsTypeId(dictDataEty.getId());
                shopCart.setGoodsIntegralType(dto.getGoodsIntegralType());
                shopCart.setGoodsZoneId(dto.getGoodsZoneId());
                shopCart.setGoodsSalePrice(goods.getPrice());
                shopCart.setGoodsSaleNakedPrice(goods.getNakePrice());
                shopCart.setCartZoneGoodsType(zoneGoodsType);
            } else {
                shopCart.setGoodsNum(shopCart.getGoodsNum() + dto.getGoodsNum());
                shopCart.setGoodsNumDecimal(shopCart.getGoodsNumDecimal().add(dto.getGoodsNumDecimal()));
            }
            //需求提报中加入购物车
            if (StringUtils.hasText(shopReqId)) {
                shopCart.setShopReqId(shopReqId);
            }
            // 如果加购数量小于起订量,则变为起订量
            if (shopCart.getGoodsNumDecimal().compareTo(BigDecimal.ZERO) > 0 && shopCart.getGoodsNumDecimal().compareTo(new BigDecimal(goodsIndex.getGoodsMoq())) < 0) {
                shopCart.setGoodsNumDecimal(new BigDecimal(goodsIndex.getGoodsMoq()));
            } else if (shopCart.getGoodsNumDecimal().compareTo(BigDecimal.ZERO) == 0) {
                shopCart.setGoodsNum(Math.max(Long.parseLong(goodsIndex.getGoodsMoq()), shopCart.getGoodsNum()));
            }

            RegionDto regionDto = new RegionDto();
            regionDto.setProvince(shopAddress.getProvince());
            regionDto.setCity(shopAddress.getCity());
            regionDto.setCounty(shopAddress.getDistrict());
            // 可售、上下架、区域限售、库存、价格校验
            ShopCart finalShopCart = shopCart;
            var checkMap = purchaseOrderService.goodsCheck(MapUtil.of(goodsCode, Math.toIntExact(shopCart.getGoodsNum())), regionDto, shopAddress.getAddress(), priceMap -> {
                RemotePriceInfoResp resp = priceMap.get(goodsCode);
                finalShopCart.setGoodsSalePrice(resp.getGoodsSalePrice());
                finalShopCart.setGoodsSaleNakedPrice(resp.getGoodsNakedSalePrice());
            });

            if (!checkMap.isEmpty()) {
                throw new CartException(checkMap.get(goodsCode));
            } else {
                this.saveOrUpdate(shopCart);
            }
            redissonLock.unlock(lockName);
            return shopCart.getCartId();
        }
    }

    private void verifyYfl(CartSaveDto dto) {
        if (yflYamlConfig.getTenantId().equals(TenantContextHolder.getRequiredTenantId())) {
            if (StringUtil.isBlank(dto.getGoodsIntegralType())) {
                throw new CartException("暂无可使用积分");
            }
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param cartIds 主键
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(final List<Long> cartIds) {
        return this.removeByIds(cartIds);
    }

    /**
     * 购物车提交
     *
     * @param cartIds
     * @return
     */
    public ShopPurchaseOrderVo submitCart(final String cartIds) {
        final LoginUser user = LocalUserHolder.get();
        final List<String> idList = new ArrayList<>(Arrays.asList(cartIds.split(",")));
        final List<Long> cartIdList = idList.stream().map(id -> Long.parseLong(id)).collect(Collectors.toList());

        if (cartIdList.size() > 150) {
            throw new ParameterException("商品数量超过150种,请筛选后重新提交");
        }

        LambdaQueryWrapperX<ShopCart> queryWrapperX = new LambdaQueryWrapperX();
        queryWrapperX.in(ShopCart::getCartId, cartIdList);
        List<ShopCart> shopCartList = this.list(queryWrapperX);

        // 提单商品判断
        judgeCartGoods(CollStreamUtil.toList(shopCartList, ShopCart::getSupplierCode));

        List<String> goodsCodeList = shopCartList.stream().map(ShopCart::getGoodsCode).collect(Collectors.toList());


        //批量查询价格
        final Map<String, RemotePriceInfoResp> remotePriceInfoMap = this.priceGetter.batchGetSalePrice(shopCartList.stream().map(ShopCart::getGoodsCode).collect(Collectors.toSet()));

        //如果是需求提报的订单，查询提报订单的需求人和需求部门
        List<String> reqShopReqIdList = shopCartList.stream().map(ShopCart::getShopReqId).filter(StringUtils::hasText).collect(Collectors.toList());
        final Map<String, ShopRequirement> requirementMap = new HashMap<>(2);
        if (!CollectionUtils.isEmpty(reqShopReqIdList)) {
            QueryWrapper<ShopRequirement> qw = new QueryWrapper<>();
            qw.lambda().select(ShopRequirement::getId, ShopRequirement::getNeederName, ShopRequirement::getNeederDepartmentName).in(ShopRequirement::getId, reqShopReqIdList);
            List<ShopRequirement> requirementList = shopRequirementMapper.selectList(qw);
            requirementMap.putAll(requirementList.stream().collect(Collectors.toMap(ShopRequirement::getId, Function.identity())));
        }

        final List<PurchaseSubOrderDetailVo> orderDetailVoList = new ArrayList<>();
        AtomicReference<BigDecimal> orderPriceCny = new AtomicReference<>(BigDecimal.ZERO);
        shopCartList.forEach(shopCart -> {
            final PurchaseSubOrderDetailVo purchaseSubOrderDetailVo = new PurchaseSubOrderDetailVo();

            // 校验商品合同是否过期
            Map<String, ShopSrmContractEntity> errors = shopGoodsService.goodsCheckContract(CollectionUtil.toList(shopCart.getGoodsCode()), succMap -> {
                var shopSrmContractEntity = succMap.get(shopCart.getGoodsCode());
                RemotePriceInfoResp remotePriceInfoResp = remotePriceInfoMap.get(shopCart.getGoodsCode());
                ShopSupplier supplier = supplierSrv.selectByCode(shopCart.getSupplierCode());
                checkGoodsTaxPrice(shopCart.getGoodsCode(), supplier, remotePriceInfoResp);

                //需求提报订单，加入需求人和需求人部门
                if (StringUtils.hasText(shopCart.getShopReqId())) {
                    ShopRequirement shopRequirement = requirementMap.get(shopCart.getShopReqId());
                    purchaseSubOrderDetailVo.setNeederName(shopRequirement.getNeederName());
                    purchaseSubOrderDetailVo.setNeederDepartmentName(shopRequirement.getNeederDepartmentName());
                }

                purchaseSubOrderDetailVo.setGoodsId(shopCart.getGoodsId());
                purchaseSubOrderDetailVo.setGoodsCode(shopCart.getGoodsCode());
                purchaseSubOrderDetailVo.setGoodsSku(shopCart.getGoodsSku());
                purchaseSubOrderDetailVo.setGoodsDesc(shopCart.getGoodsName());
                purchaseSubOrderDetailVo.setConfirmNum(shopCart.getGoodsNum().longValue());
                purchaseSubOrderDetailVo.setConfirmNumDecimal(shopCart.getGoodsNumDecimal());
                purchaseSubOrderDetailVo.setSaleUnit(shopCart.getSaleUnit());
                purchaseSubOrderDetailVo.setSupplierCode(shopCart.getSupplierCode());
                purchaseSubOrderDetailVo.setSupplierName(shopCart.getSupplierName());
                purchaseSubOrderDetailVo.setSupplierType(supplier.getSupplierType());
                purchaseSubOrderDetailVo.setSupplierDataSource(supplier.getDataSource());
                purchaseSubOrderDetailVo.setGoodsModel(shopCart.getGoodsModel());
                if (Objects.equals(GoodsModelEnum.VIRTUAL.getModelCode(), purchaseSubOrderDetailVo.getGoodsModel())) {
                    ShopGoods byGoodsCode = shopGoodsService.getByGoodsCode(shopCart.getGoodsCode());
                    if ("直充类".equalsIgnoreCase(byGoodsCode.getThirdClassName())) {
                        purchaseSubOrderDetailVo.setVirtualType("1");
                    } else if ("卡密类".equalsIgnoreCase(byGoodsCode.getThirdClassName())) {
                        purchaseSubOrderDetailVo.setVirtualType("2");
                    }
                }
                purchaseSubOrderDetailVo.setContractNumber(shopCart.getContractNumber());
                final QueryWrapper<ShopGoodsDetail> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ShopGoodsDetail::getGoodsCode, shopCart.getGoodsCode());
                final ShopGoodsDetail goodsDetailEntity = this.shopGoodsDetailService.getOne(queryWrapper);
                purchaseSubOrderDetailVo.setGoodsImage(goodsDetailEntity.getGoodsImage());

                ShopPurchaseSubOrderDetail purchaseSubOrderDetail = new ShopPurchaseSubOrderDetail();
                purchaseSubOrderDetail.setGoodsCode(shopCart.getGoodsCode());
                purchaseSubOrderDetail.setApplyNum(shopCart.getGoodsNum());
                purchaseSubOrderDetail.setTaxRate(shopCart.getTaxRate());
                purchaseSubOrderDetail.setApplyNumDecimal(shopCart.getGoodsNumDecimal());

                String contractNumber = shopCart.getContractNumber();
                // 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式
                Integer pricingMode;
                if (StrUtil.isBlank(contractNumber)) {
                    // 不走合同则用通用计价逻辑
                    pricingMode = 0;
                } else {
                    // 走srm合同的单子按照合同要求计价
                    ShopSrmContractEntity srmContract = srmContractSrv.getBySrmContractNumber(contractNumber);
                    pricingMode = srmContract.getPricingMode();
                }
                if(StrUtil.startWith(shopCart.getGoodsCode(),CompanyEnum.DHEC.getCompanyCode())){
                    pricingMode = 3;
                }
                purchaseSubOrderDetail = purchaseOrderService.computeOrderDetail(pricingMode, purchaseSubOrderDetail, remotePriceInfoResp);

                purchaseSubOrderDetailVo.setPricingMode(pricingMode);
                purchaseSubOrderDetailVo.setGoodsUnitPriceTax(remotePriceInfoResp.getGoodsSalePrice());
                purchaseSubOrderDetailVo.setGoodsTotalPriceTax(purchaseSubOrderDetail.getGoodsTotalPriceTax());
                purchaseSubOrderDetailVo.setGoodsUnitPriceNaked(remotePriceInfoResp.getGoodsNakedSalePrice());
                purchaseSubOrderDetailVo.setGoodsTotalPriceNaked(purchaseSubOrderDetail.getGoodsTotalPriceNaked());
                purchaseSubOrderDetailVo.setTaxRate(remotePriceInfoResp.getTaxRate());
                if (!ObjectUtils.isEmpty(shopSrmContractEntity)) {
                    // 人民币金额 =（1+关税）* 商品价格 * 汇率
                    BigDecimal goodsPriceCny = shopSrmContractEntity.getTariff().add(BigDecimal.ONE).multiply(purchaseSubOrderDetail.getGoodsUnitPriceTax()).multiply(shopSrmContractEntity.getExchangeRate());
                    purchaseSubOrderDetailVo.setGoodsUnitNakedPriceCny(goodsPriceCny.setScale(4, RoundingMode.HALF_UP));
                    purchaseSubOrderDetailVo.setGoodsUnitTaxPriceCny(goodsPriceCny.setScale(4, RoundingMode.HALF_UP));
                    purchaseSubOrderDetailVo.setCurrencyCode(shopSrmContractEntity.getCurrencyCode());
                    purchaseSubOrderDetailVo.setCurrencyName(shopSrmContractEntity.getCurrencyName());
                    purchaseSubOrderDetailVo.setExchangeRate(shopSrmContractEntity.getExchangeRate());
                    purchaseSubOrderDetailVo.setTariff(shopSrmContractEntity.getTariff());
                    BigDecimal goodsTotalPriceCny = goodsPriceCny.multiply(purchaseSubOrderDetailVo.getConfirmNumDecimal());
                    purchaseSubOrderDetailVo.setGoodsTotalPriceCny(goodsTotalPriceCny.setScale(2, RoundingMode.HALF_UP));
                    orderPriceCny.set(goodsTotalPriceCny.add(orderPriceCny.get()));
                }

                purchaseSubOrderDetailVo.setSettlementType(getGoodsSettlement(shopCart.getGoodsId(),shopCart.getSupplierCode(),user.getEntityOrganizationId()));
                orderDetailVoList.add(purchaseSubOrderDetailVo);
            });
        });

        //乘用车填充物料数据
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            List<String> cartGoodsCodes = orderDetailVoList.stream().map(PurchaseSubOrderDetailVo::getGoodsCode).collect(Collectors.toList());
            QueryWrapper<ShopMaterialRelationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, user.getEntityOrganizationCode())
                    .in(ShopMaterialRelationEntity::getGoodsCode, cartGoodsCodes)
                    .eq(ShopMaterialRelationEntity::getIsDel,0);
            List<ShopMaterialRelationEntity> shopMaterialRelationEntities = shopMaterialRelationService.list(queryWrapper);
            Map<String, String> goodsCodeMap = shopMaterialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr, (key1, key2) -> key1));
            for (PurchaseSubOrderDetailVo purchaseSubOrderDetailVo : orderDetailVoList) {
                purchaseSubOrderDetailVo.setMaraMatnr(goodsCodeMap.get(purchaseSubOrderDetailVo.getGoodsCode()));
            }
        }

        final List<ShopAddressVo> shopAddressList = this.shopAddressService.queryByUser(user.getId());
        final List<ShopOrderAddress> shopOrderAddressList = new ArrayList<>();
        shopAddressList.forEach(shopAddressVo -> {
            final ShopOrderAddress orderAddress = new ShopOrderAddress();
            BeanUtil.copyProperties(shopAddressVo, orderAddress);
            shopOrderAddressList.add(orderAddress);
        });
        final ShopPurchaseOrderVo purchaseOrderVo = new ShopPurchaseOrderVo();
        purchaseOrderVo.setOrderAddressList(shopOrderAddressList);
        purchaseOrderVo.setOrderDetailList(orderDetailVoList);
        purchaseOrderVo.setCartIds(cartIds);
        purchaseOrderVo.setFloatPriceGoodsList(getFloatPriceGoodsByOrderDetail(orderDetailVoList));
        purchaseOrderVo.setOrderPriceCny(orderPriceCny.get().setScale(2, RoundingMode.HALF_UP));
        return purchaseOrderVo;
    }

    /**
     * 购物车提交
     *
     * @param dto
     * @return
     */
    public ShopPurchaseOrderVo submitCartNew(final CartSubmitDto dto) {
        log.info("submitCartNew:::------------------------------------------------开始 {}", dto);
        LoginUser user = LocalUserHolder.get();
        // 获取下单地址
        ShopAddress shopAddress = this.getOrderAddress(dto, user);
        // 获取商品数据
        List<ShopCart> shopCartList = this.getShopCartList(dto);
        // 提单商品判断
        this.judgeCartGoods(CollStreamUtil.toList(shopCartList, ShopCart::getSupplierCode));
        // 向供应商校验商品
        Map<String, RemotePriceInfoResp> priceInfoRespMap = this.checkGoodsAndGetPrice(shopCartList, shopAddress);
        final List<PurchaseSubOrderDetailVo> orderDetailVoList = new ArrayList<>();
        AtomicReference<BigDecimal> orderPriceCny = new AtomicReference<>(BigDecimal.ZERO);
        // 校验商品合同及生成订单商品详情
        this.checkContractAndInitOrderDetail(shopCartList, orderDetailVoList, orderPriceCny, priceInfoRespMap, user);
        // 校验用户是否可以下平台结算商品
        this.ckeckOrderLimit(orderDetailVoList);
        //乘用车填充物料数据
        this.fullMaraMatnr(user, orderDetailVoList);
        final ShopPurchaseOrderVo purchaseOrderVo = new ShopPurchaseOrderVo();
        purchaseOrderVo.setOrderDetailList(orderDetailVoList);
        purchaseOrderVo.setCartIds(dto.getCartIds());
        purchaseOrderVo.setFloatPriceGoodsList(getFloatPriceGoodsByOrderDetail(orderDetailVoList));
        purchaseOrderVo.setOrderPriceCny(orderPriceCny.get().setScale(2, RoundingMode.HALF_UP));
        purchaseOrderVo.setAddressId(dto.getAddressId());
        purchaseOrderVo.setRemarkMaps(dto.getRemarkMaps());
        purchaseOrderVo.setPriceInfoRespMap(priceInfoRespMap);
        purchaseOrderVo.setCompareRecordList(dto.getCompareRecordList());

        purchaseOrderService.cartToPreOrder(purchaseOrderVo);
        log.info("submitCartNew:::------------------------------------------------结束");
        return purchaseOrderVo;
    }

    private void ckeckOrderLimit(List<PurchaseSubOrderDetailVo> orderDetailVoList) {
        Integer settlementType = orderDetailVoList.get(0).getSettlementType();
        settlementType = (settlementType == null || settlementType == 1) ? 1 : 2;
        if(settlementType == 1){
            LoginUser user = LocalUserHolder.get();
            SystemUserRole systemUserRole = systemUserRoleService.getSystemUserRole(ORDER_LIMIT_ROLE_CODE, user.getId());
            if(systemUserRole!=null){
                throw new ParameterException("当前账户存在逾期未结清订单，请确认付款进度。 如有问题请及时联系商城运营处理！");
            }
        }
    }

    /**
     * 校验商品合同及生成订单商品详情
     * @param shopCartList
     * @param orderDetailVoList
     * @param orderPriceCny
     * @param priceInfoRespMap
     * @param user
     */
    private void checkContractAndInitOrderDetail(List<ShopCart> shopCartList, List<PurchaseSubOrderDetailVo> orderDetailVoList, AtomicReference<BigDecimal> orderPriceCny, Map<String, RemotePriceInfoResp> priceInfoRespMap, LoginUser user) {
        List<String> goodsList = shopCartList.stream().map(ShopCart::getGoodsCode).distinct().collect(Collectors.toList());
        List<String> supplierCodes = shopCartList.stream().map(ShopCart::getSupplierCode).distinct().collect(Collectors.toList());
        List<Long> goodsIds = shopCartList.stream().map(ShopCart::getGoodsId).distinct().collect(Collectors.toList());
        List<ShopSupplier> supplierList = supplierSrv.getSupplierInfoByCodes(supplierCodes);
        Map<String, ShopSupplier> shopSupplierMap = supplierList.stream().collect(Collectors.toMap(ShopSupplier::getSupplierCode, Function.identity(), (key1,key2)->key1));
        List<ShopGoodsContractVo> settlementList = goodsSettlementService.queryVoByGoodsIds(goodsIds,1);
        Map<Long, List<ShopGoodsContractVo>> settlementListMap = settlementList.stream().collect(Collectors.groupingBy(ShopGoodsContractVo::getGoodsId));

        // 校验商品合同是否过期
        Map<String, ShopSrmContractEntity> errors = shopGoodsService.goodsCheckContract(goodsList, succMap -> {
            shopCartList.forEach(shopCart->{
                final PurchaseSubOrderDetailVo purchaseSubOrderDetailVo = new PurchaseSubOrderDetailVo();
                var shopSrmContractEntity = succMap.get(shopCart.getGoodsCode());
                RemotePriceInfoResp remotePriceInfoResp = priceInfoRespMap.get(shopCart.getGoodsCode());
                checkGoodsTaxPrice(shopCart.getGoodsCode(), shopSupplierMap.get(shopCart.getSupplierCode()), remotePriceInfoResp);
                ShopSupplier supplier = shopSupplierMap.get(shopCart.getSupplierCode());
                purchaseSubOrderDetailVo.setGoodsId(shopCart.getGoodsId());
                purchaseSubOrderDetailVo.setGoodsCode(shopCart.getGoodsCode());
                purchaseSubOrderDetailVo.setGoodsSku(shopCart.getGoodsSku());
                purchaseSubOrderDetailVo.setGoodsDesc(shopCart.getGoodsName());
                purchaseSubOrderDetailVo.setConfirmNum(shopCart.getGoodsNum().longValue());
                purchaseSubOrderDetailVo.setConfirmNumDecimal(shopCart.getGoodsNumDecimal());
                purchaseSubOrderDetailVo.setSaleUnit(shopCart.getSaleUnit());
                purchaseSubOrderDetailVo.setSupplierCode(shopCart.getSupplierCode());
                purchaseSubOrderDetailVo.setSupplierName(shopCart.getSupplierName());
                purchaseSubOrderDetailVo.setSupplierType(supplier.getSupplierType());
                purchaseSubOrderDetailVo.setSupplierDataSource(supplier.getDataSource());
                purchaseSubOrderDetailVo.setGoodsModel(shopCart.getGoodsModel());
                purchaseSubOrderDetailVo.setContractNumber(shopCart.getContractNumber());
                purchaseSubOrderDetailVo.setGoodsImage(shopCart.getGoodsImage());
                purchaseSubOrderDetailVo.setGoodsZoneId(shopCart.getGoodsZoneId());
                purchaseSubOrderDetailVo.setCartZoneGoodsType(shopCart.getCartZoneGoodsType());

                ShopPurchaseSubOrderDetail purchaseSubOrderDetail = new ShopPurchaseSubOrderDetail();
                purchaseSubOrderDetail.setGoodsCode(shopCart.getGoodsCode());
                purchaseSubOrderDetail.setApplyNum(shopCart.getGoodsNum());
                purchaseSubOrderDetail.setTaxRate(shopCart.getTaxRate());
                purchaseSubOrderDetail.setApplyNumDecimal(shopCart.getGoodsNumDecimal());

                String contractNumber = shopCart.getContractNumber();
                // 计价模式 0:商城通用模式 1:srm未税模式 2:srm含税模式
                Integer pricingMode = 0;
                if (StrUtil.isBlank(contractNumber)) {
                    // 不走合同则用通用计价逻辑
                    pricingMode = 0;
                } else {
                    if("DFS".equals(user.getOrgRangeMark())) {
                        // 走srm合同的单子按照合同要求计价
                        ShopSrmContractEntity srmContract = srmContractSrv.getBySrmContractNumber(contractNumber);
                        pricingMode = srmContract.getPricingMode();
                    }
                }
                purchaseSubOrderDetail = purchaseOrderService.computeOrderDetail(pricingMode, purchaseSubOrderDetail, remotePriceInfoResp);

                purchaseSubOrderDetailVo.setPricingMode(pricingMode);
                purchaseSubOrderDetailVo.setGoodsUnitPriceTax(remotePriceInfoResp.getGoodsSalePrice());
                purchaseSubOrderDetailVo.setGoodsTotalPriceTax(purchaseSubOrderDetail.getGoodsTotalPriceTax());
                purchaseSubOrderDetailVo.setGoodsUnitPriceNaked(remotePriceInfoResp.getGoodsNakedSalePrice());
                purchaseSubOrderDetailVo.setGoodsTotalPriceNaked(purchaseSubOrderDetail.getGoodsTotalPriceNaked());
                purchaseSubOrderDetailVo.setTaxRate(remotePriceInfoResp.getTaxRate());
                if (!ObjectUtils.isEmpty(shopSrmContractEntity)) {
                    // 人民币金额 =（1+关税）* 商品价格 * 汇率
                    BigDecimal goodsPriceCny = shopSrmContractEntity.getTariff().add(BigDecimal.ONE).multiply(purchaseSubOrderDetail.getGoodsUnitPriceTax()).multiply(shopSrmContractEntity.getExchangeRate());
                    purchaseSubOrderDetailVo.setGoodsUnitNakedPriceCny(goodsPriceCny.setScale(4, RoundingMode.HALF_UP));
                    purchaseSubOrderDetailVo.setGoodsUnitTaxPriceCny(goodsPriceCny.setScale(4, RoundingMode.HALF_UP));
                    purchaseSubOrderDetailVo.setCurrencyCode(shopSrmContractEntity.getCurrencyCode());
                    purchaseSubOrderDetailVo.setCurrencyName(shopSrmContractEntity.getCurrencyName());
                    purchaseSubOrderDetailVo.setExchangeRate(shopSrmContractEntity.getExchangeRate());
                    purchaseSubOrderDetailVo.setTariff(shopSrmContractEntity.getTariff());
                    BigDecimal goodsTotalPriceCny = goodsPriceCny.multiply(purchaseSubOrderDetailVo.getConfirmNumDecimal());
                    purchaseSubOrderDetailVo.setGoodsTotalPriceCny(goodsTotalPriceCny.setScale(2, RoundingMode.HALF_UP));
                    orderPriceCny.set(goodsTotalPriceCny.add(orderPriceCny.get()));
                }
                purchaseSubOrderDetailVo.setSettlementType(toGoodsSettlement(shopSupplierMap.get(shopCart.getSupplierCode()), settlementListMap.get(shopCart.getGoodsId()),user.getEntityOrganizationId()));
                orderDetailVoList.add(purchaseSubOrderDetailVo);
            });
        });
        if(CollectionUtil.isNotEmpty(errors)){
            log.info("校验商品合同是否过期 errors: {}", errors);
        }
        if(CollectionUtil.isEmpty(orderDetailVoList)){
            throw new OrderException("无合同期内有效商品");
        }
    }

    /**
     * 商品校验
     * @param shopCartList
     * @param shopAddress
     * @return
     */
    private Map<String, RemotePriceInfoResp> checkGoodsAndGetPrice(List<ShopCart> shopCartList, ShopAddress shopAddress) {
        Map<String, Object> cnt = shopCartList.stream().collect(Collectors.toMap(ShopCart::getGoodsCode, ShopCart::getGoodsNum));

        final RegionDto regionDto = new RegionDto();
        regionDto.setProvince(shopAddress.getProvince());
        regionDto.setCity(shopAddress.getCity());
        regionDto.setCounty(shopAddress.getDistrict());

        String address =  shopAddress.getAddress();
        String street = shopAddress.getStreet();
        if (StrUtil.isNotBlank(street)) {
            address = street + shopAddress.getAddress();
        }

        //商品合同校验
        var contractCheck =  purchaseOrderService.goodsContractCheck(cnt.keySet());
        if (!contractCheck.isEmpty()) {
            throw new OrderException(JSON.toJSONString(contractCheck));
        }

        //校验商品
        Map<String, RemotePriceInfoResp> priceInfoRespMap = new HashMap<>();
        var checkResult = purchaseOrderService.goodsCheck(cnt, regionDto, address, priceInfoRespMap::putAll);
        if (!checkResult.isEmpty()) {
            throw new OrderException(JSON.toJSONString(checkResult));
        }
        return priceInfoRespMap;
    }

    /**
     * 获取购物车数据
     * @param dto
     * @return
     */
    private List<ShopCart> getShopCartList(CartSubmitDto dto) {
        final List<String> idList = new ArrayList<>(Arrays.asList(dto.getCartIds().split(",")));
        final List<Long> cartIdList = idList.stream().map(id -> Long.parseLong(id)).collect(Collectors.toList());
        LambdaQueryWrapperX<ShopCart> queryWrapperX = new LambdaQueryWrapperX();
        queryWrapperX.in(ShopCart::getCartId, cartIdList);
        List<ShopCart> shopCartList = this.list(queryWrapperX);
        if(CollectionUtil.isEmpty(shopCartList)){
            throw new OrderException("购物车中无商品，请刷新购物车页面");
        }
        return shopCartList;
    }

    /**
     * 获取地址数据
     * @param dto
     * @param user
     * @return
     */
    private ShopAddress getOrderAddress(CartSubmitDto dto, LoginUser user) {
        // 没有cartId则为新增商品进购物车,需要校验
        ShopAddress shopAddress;
        if (StrUtil.isNotBlank(dto.getAddressId())) {
            shopAddress = this.shopAddressService.getById(dto.getAddressId());
        } else {
            // 查询默认地址
            shopAddress = this.shopAddressService.getDefaultAddress(user.getUsername());
            if (shopAddress == null) {
                // 如果个人地址为空，则取工厂地址
                shopAddress = this.shopAddressService.getFactoryAddress(user.getUsername());
            }
        }
        if (shopAddress == null) {
            throw new CartException("请在【个人中心】-【地址管理】中设置默认地址");
        }
        return shopAddress;
    }

    /**
     * 填充商品物料数据
     * @param user
     * @param orderDetailVoList
     */
    private void fullMaraMatnr(LoginUser user, List<PurchaseSubOrderDetailVo> orderDetailVoList) {
        if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.DFGM.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            List<String> cartGoodsCodes = orderDetailVoList.stream().map(PurchaseSubOrderDetailVo::getGoodsCode).collect(Collectors.toList());
            QueryWrapper<ShopMaterialRelationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopMaterialRelationEntity::getCompanyCode, user.getEntityOrganizationCode())
                    .in(ShopMaterialRelationEntity::getGoodsCode, cartGoodsCodes)
                    .eq(ShopMaterialRelationEntity::getIsDel,0);
            List<ShopMaterialRelationEntity> shopMaterialRelationEntities = shopMaterialRelationService.list(queryWrapper);
            Map<String, String> goodsCodeMap = shopMaterialRelationEntities.stream().collect(Collectors.toMap(ShopMaterialRelationEntity::getGoodsCode, ShopMaterialRelationEntity::getMaraMatnr, (key1, key2) -> key1));
            for (PurchaseSubOrderDetailVo purchaseSubOrderDetailVo : orderDetailVoList) {
                purchaseSubOrderDetailVo.setMaraMatnr(goodsCodeMap.get(purchaseSubOrderDetailVo.getGoodsCode()));
            }
        }
    }

    /**
     * 通过订单明细获取浮动价格商品集合
     *
     * @param orderDetailList 订单明细
     * @return 浮动价格商品集合
     */
    private List<FloatPriceGoodsDto> getFloatPriceGoodsByOrderDetail(List<PurchaseSubOrderDetailVo> orderDetailList) {

        Map<String, PurchaseSubOrderDetailVo> orderDetailMap = CollStreamUtil.toIdentityMap(orderDetailList, PurchaseSubOrderDetailVo::getGoodsCode);
        Map<String, ShopSrmContractDetailEntity> floatGoodsMap = shopSrmContractDetailService.getFloatContractDetailMap(orderDetailMap.keySet());

        List<FloatPriceGoodsDto> resList = new ArrayList<>();
        String template = "请联系管理员检查合同编号为【{}】，商品编码为【{}】的合同明细最【{}】价格浮动百分比是否为空";
        BigDecimal hundred = new BigDecimal(100);
        floatGoodsMap.forEach((k, v) -> {
            PurchaseSubOrderDetailVo orderDetail = orderDetailMap.get(k);
            BigDecimal goodsUnitPriceTax = orderDetail.getGoodsUnitPriceTax();
            BigDecimal goodsUnitPriceNaked = orderDetail.getGoodsUnitPriceNaked();
            String contractNumber = v.getContractNumber();
            // 校验是否有浮动比例异常的合同明细
            BigDecimal minPercent = Opt.ofNullable(v.getMinPercent()).orElseThrow(() -> new ParameterException(StrUtil.format(template, contractNumber, k, "小")));
            minPercent = minPercent.divide(hundred, 4, RoundingMode.HALF_UP);

            BigDecimal maxPercent = Opt.ofNullable(v.getMaxPercent()).orElseThrow(() -> new ParameterException(StrUtil.format(template, contractNumber, k, "大")));
            maxPercent = maxPercent.divide(hundred, 4, RoundingMode.HALF_UP);

            BigDecimal minRate = BigDecimal.ONE.subtract(minPercent);
            BigDecimal maxRate = BigDecimal.ONE.add(maxPercent);
            // 初始化浮动商品对象
            FloatPriceGoodsDto build = FloatPriceGoodsDto.builder().goodsCode(k).goodsDesc(orderDetail.getGoodsDesc()).goodsUnitPriceTax(goodsUnitPriceTax).goodsUnitPriceNaked(goodsUnitPriceNaked).confirmNum(orderDetail.getConfirmNum()).confirmNumDecimal(orderDetail.getConfirmNumDecimal()).minUnitPriceTax(goodsUnitPriceTax.multiply(minRate).setScale(4, RoundingMode.HALF_UP)).maxUnitPriceTax(goodsUnitPriceTax.multiply(maxRate).setScale(4, RoundingMode.HALF_UP)).minUnitPriceNaked(goodsUnitPriceNaked.multiply(minRate).setScale(4, RoundingMode.HALF_UP)).maxUnitPriceNaked(goodsUnitPriceNaked.multiply(maxRate).setScale(4, RoundingMode.HALF_UP)).confirmUnitPriceTax(goodsUnitPriceTax).confirmUnitPriceNaked(goodsUnitPriceNaked).taxRate(orderDetail.getTaxRate()).build();
            resList.add(build);
        });

        return resList;
    }

    /**
     * 提单前购物车商品判断
     * <p>
     * 1.开了预付款的企业，独立供应商和电商不能一起提单
     * </p>
     *
     * @param supplierCodes 供应商编码集合
     */
    private void judgeCartGoods(List<String> supplierCodes) {
        LoginUser loginUser = LocalUserHolder.get();
        String entityOrganizationCode = loginUser.getEntityOrganizationCode();
        // 规则1
        List<String> companyCodes = dfsYamlConfig.getCompanycode();
        if (CollUtil.isNotEmpty(companyCodes) && companyCodes.contains(entityOrganizationCode)) {
            LambdaQueryWrapperX<ShopSupplier> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.select(ShopSupplier::getDataSource).in(ShopSupplier::getSupplierCode, supplierCodes);
            List<ShopSupplier> suppliers = supplierSrv.list(queryWrapperX);
            Set<String> source = new HashSet<>(CollStreamUtil.toList(suppliers, ShopSupplier::getDataSource));
            if (CollUtil.isNotEmpty(source) && CollUtil.contains(source, dfsYamlConfig.getSrmDataSource()) && source.size() > 1) {
                throw new OrderException("您所属企业不支持【SRM供应商商品】和【东风商城供应商商品】同时提单");
            }
        }
    }

    /**
     * 购物车导出
     *
     * @param dto dto
     * @return {@link List}<{@link ShopCartExcelVo}>
     */
    public List<ShopCartExcelVo> cartExport(ShopCartExportDto dto) {
        List<ShopCart> cartList = this.baseMapper.cartExport(dto, LocalUserHolder.get().getId());
        List<ShopCartExcelVo> result = new ArrayList<>();
        cartList.forEach(cart -> {
            ShopCartExcelVo vo = ShopCartConvert.INSTANCE.convertExcelVo(cart);
            BigDecimal goodsNum = cart.getGoodsNumDecimal().add(new BigDecimal(cart.getGoodsNum()));
            vo.setGoodsNum(goodsNum);
            if (StrUtil.isBlank(vo.getErrMessage())) {
                vo.setImportState("成功");
                BigDecimal tax = new BigDecimal(vo.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).add(BigDecimal.ONE);
                // 未税小计 = 未税 * 数量
                vo.setGoodsSaleNakedPriceTotal(vo.getGoodsSaleNakedPrice().multiply(vo.getGoodsNum()).setScale(2, RoundingMode.HALF_UP));
                // 含税小计 = 未税 * 税率 * 数量
                vo.setGoodsSalePriceTotal(vo.getGoodsSaleNakedPrice().multiply(tax).multiply(vo.getGoodsNum()).setScale(2, RoundingMode.HALF_UP));
            } else {
                vo.setImportState("失败");
            }
            result.add(vo);
        });
        return result;
    }

    /**
     * 购物车导出
     *
     * @param response
     */
    public void cartExportAll(final HttpServletResponse response, String organizationCode) {
        try (final ExcelWriter writer = ExcelUtil.getWriter(); final ServletOutputStream out = response.getOutputStream()) {
            final List<ShopCartVo> shopCartVoList = new ArrayList<>();
            QueryWrapper<SystemUsers> usersQueryWrapper = new QueryWrapper<>();
            usersQueryWrapper.lambda().eq(SystemUsers::getEntityOrganizationCode, organizationCode);
            List<SystemUsers> users = systemUsersService.list(usersQueryWrapper);
            QueryWrapper<ShopCart> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShopCart::getCartType, 0).in(ShopCart::getUserId, users.stream().map(SystemUsers::getId).collect(Collectors.toList()));
            List<ShopCart> cartlist = shopCartMapper.queryAllCartId(organizationCode);
            if (CollectionUtil.isEmpty(cartlist)) {
                return;
            }
            for (final Long cartId : cartlist.stream().map(ShopCart::getCartId).collect(Collectors.toList())) {
                final List<ShopCartVo> cartVoList = this.baseMapper.exportShopCart(cartId);
                for (final ShopCartVo shopCartVo : cartVoList) {
                    shopCartVoList.add(shopCartVo);
                }
            }
            writer.writeHeadRow(CollUtil.newArrayList("电商名称", "商品描述", "商品编码", "商品SKU", "SAP参考编码", "未税价格", "含税价格", "数量", "未税价-小计", "含税价格-小计"));
            final List<List<String>> rows = new ArrayList<>();
            shopCartVoList.forEach(data -> {
                log.info("循环遍历购物车数据，data:" + data);
                final String supplierName = data.getSupplierName();
                final String goodsName = data.getGoodsName();
                final String goodsCode = data.getGoodsCode();
                final String goodsSaleNakedPrice = data.getGoodsSaleNakedPrice() == null ? "" : data.getGoodsSaleNakedPrice().toString();
                final String goodsSalePrice = data.getGoodsSalePrice() == null ? "" : data.getGoodsSalePrice().toString();
                final String goodsNum = data.getGoodsNum().toString();
                final String goodsSaleNakedFinalPrice = data.getGoodsSaleNakedPrice() == null ? "" : data.getGoodsSaleNakedPrice().multiply(new BigDecimal(data.getGoodsNum())).toString();
                log.info("循环遍历购物车数据，查询商品：goodsCode:" + goodsCode);
                // 查询 税率
                ShopGoods goods = shopGoodsService.getOne(new LambdaQueryWrapperX<ShopGoods>().select(ShopGoods::getTaxRate, ShopGoods::getGoodsSku, ShopGoods::getGoodsCode).eq(ShopGoods::getGoodsCode, goodsCode));
                final String goodsSku = goods.getGoodsSku();

                //查询物料编码
                QueryWrapper<ShopMaterialRelationEntity> queryMara = new QueryWrapper<>();
                queryMara.lambda().eq(ShopMaterialRelationEntity::getGoodsCode, goods.getGoodsCode()).eq(ShopMaterialRelationEntity::getIsDel, 0).eq(ShopMaterialRelationEntity::getCompanyCode, CompanyEnum.DFPV.getCompanyCode());
                List<ShopMaterialRelationEntity> shopMaterialRelationEntities = shopMaterialRelationService.list(queryMara);
                String mara = "";
                if (CollectionUtil.isNotEmpty(shopMaterialRelationEntities)) {
                    mara = shopMaterialRelationEntities.get(0).getMaraMatnr();
                }
                BigDecimal taxRate = goods.getTaxRate() == null ? BigDecimal.ZERO : new BigDecimal(goods.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                // 未税单价 * 税率 * 商品数量 保留2位小数
                final String goodsSaleFinalPrice = data.getGoodsSalePrice() == null ? "" : data.getGoodsSaleNakedPrice().multiply(BigDecimal.ONE.add(taxRate)).multiply(new BigDecimal(data.getGoodsNum())).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                rows.add(ListUtil.toList(supplierName, goodsName, goodsCode, goodsSku, mara, goodsSaleNakedPrice, goodsSalePrice, goodsNum, goodsSaleNakedFinalPrice, goodsSaleFinalPrice));
            });
            writer.write(rows);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("购物车信息表.xls", "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            writer.flush(out, true);
        } catch (final IOException e) {
            e.printStackTrace();
        }
    }

    public Map<Object, Long> getCartGoodsNum(final String goodsCode, Long goodsZoneId) {
        final LoginUser user = LocalUserHolder.get();
        // 合并购物车的专区不传goodsZoneId
        if(goodsZoneId!= null){
            GoodsZoneEntity  goodsZoneEntity = goodsZoneService.getById(goodsZoneId);
            if(goodsZoneEntity != null){
                if(goodsZoneEntity.getCartMergeSubmit()==1){
                    goodsZoneId = null;
                }
            }
        }
        return this.shopCartMapper.getCartGoodsNum(goodsCode, user.getId(),goodsZoneId);
    }

    public List<ShopCart> queryCartIdList(final List<CartQueryDto> cartQueryDtoList) {
        return this.shopCartMapper.queryCartIdList(cartQueryDtoList);
    }

    public void delCartIds(final List<Long> cartIds) {
        this.shopCartMapper.delCartIds(cartIds);
    }

    /**
     * 获得购物车商品导入模板
     *
     * @param response
     * @throws IOException
     */
    public void getImportGoodsTemplate(HttpServletResponse response) throws IOException {
        List<GoodsToCartImportVo> list = Collections.singletonList(GoodsToCartImportVo.builder().supplierName("震坤行工业超市（上海）有限公司").goodsSku("*********").goodsNum(new BigDecimal(10)).build());
        List<ShopSupplier> allSupplier = supplierSrv.list(new LambdaQueryWrapperX<ShopSupplier>().select(ShopSupplier::getSupplierFullName).eq(ShopSupplier::getApproveState, 1).eq(ShopSupplier::getDataSource, "dfmall").eq(ShopSupplier::getIsEnable, 1));
        List<String> collect = allSupplier.stream().map(ShopSupplier::getSupplierFullName).collect(Collectors.toList());
        List<Map<Integer, List<String>>> dropList = new ArrayList<>();
        HashMap<Integer, List<String>> integerListHashMap = new HashMap<>();
        // 0: 第一列  collect：下拉列表的值
        integerListHashMap.put(0, collect);
        dropList.add(integerListHashMap);
        ExcelUtils.write(response, "购物车商品导入模板.xls", "商品列表", GoodsToCartImportVo.class, list, new SpinnerWriteHandler(dropList));
    }

    public void voyahGetImportGoodsTemplate(HttpServletResponse response) throws IOException {
        List<NonPlatformGoodsToCartImportVo> list = Collections.singletonList(NonPlatformGoodsToCartImportVo.builder().supplierName("联友智连科技有限公司").goodsSku("*********").goodsNum(new BigDecimal(10)).build());
        List<ShopSupplier> allSupplier = supplierSrv.list(new LambdaQueryWrapperX<ShopSupplier>().select(ShopSupplier::getSupplierFullName).eq(ShopSupplier::getApproveState, 1).eq(ShopSupplier::getDataSource, CompanyEnum.VOYAH.getCompanyCode()).eq(ShopSupplier::getIsEnable, 1));
        List<String> collect = allSupplier.stream().map(ShopSupplier::getSupplierFullName).collect(Collectors.toList());
        List<Map<Integer, List<String>>> dropList = new ArrayList<>();
        HashMap<Integer, List<String>> integerListHashMap = new HashMap<>();
        // 0: 第一列  collect：下拉列表的值
        integerListHashMap.put(0, collect);
        dropList.add(integerListHashMap);
        ExcelUtils.write(response, "购物车商品导入模板.xls", "商品列表", NonPlatformGoodsToCartImportVo.class, list, new SpinnerWriteHandler(dropList));
    }

    /**
     * 商品导入购物车
     *
     * @param importVos 导入数据集合
     */
    public void importGoodsToCart(List<GoodsToCartImportVo> importVos) {
        var user = LocalUserHolder.get();
        _d_data(user.getId(), 1);
        var cst = _pre_check_data(importVos);
        var dz = this.shopAddressService.getDefaultAddress(user.getUsername());
        if (dz == null) {
            dz = this.shopAddressService.getFactoryAddress(user.getUsername());
        }
        if (dz == null) {
            throw new CartException("请在【个人中心】-【地址管理】中设置默认地址");
        }

        var dzp = dz.getProvince();
        var dzc = dz.getCity();
        var dzd = dz.getDistrict();
        var dza = dz.getAddress();

        List<ShopCart> nave = Collections.synchronizedList(new ArrayList<>());
        List<SystemDictDataEntity> a002 = dictDataService.getDictDatasByDictType("A002");
        Map<String, Long> typeMap = a002.stream().collect(Collectors.toMap(SystemDictDataEntity::getValue, SystemDictDataEntity::getId));

        var mcart = cst.stream().collect(Collectors.groupingBy(GoodsToCartImportVo::getSupplierCode));
        Map<String, ShopGoods> mgoods = new HashMap<>();
        var mcount = new HashMap<String, Object>();
        var mskugoods = new HashMap<String, ShopCart>();
        var nest = new ArrayList<GoodsToCartImportVo>();
        mcart.forEach((sup, icart) -> {
            var skulst = icart.stream().map(GoodsToCartImportVo::getGoodsSku).collect(Collectors.toList());

            var tgoods = shopGoodsService.getBaseMapper().getBySkuInAndSupplier(skulst, sup);

            skulst.removeAll(tgoods.stream().map(ShopGoods::getGoodsSku).collect(Collectors.toList()));
            nest.addAll(icart);

            List<ShopCart> qcart = shopGoodsService.queryGoodsBySku(icart.stream().map(GoodsToCartImportVo::getGoodsSku).collect(Collectors.toList()), sup);
            qcart.forEach(sub -> mskugoods.putIfAbsent(sub.getGoodsSku() + "_" + sub.getSupplierCode(), sub));

            mgoods.putAll(tgoods.stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, Function.identity())));
            tgoods.forEach(sub -> {
                icart.stream().filter(gs -> gs.getGoodsSku().equals(sub.getGoodsSku())).findFirst().ifPresent(g -> {
                    mcount.put(sub.getGoodsCode(), g.getGoodsNum());
                });
            });
        });
        Map<String, String> crr = new HashMap<>();
        if (mgoods.isEmpty()) {
            nest.forEach(iicart -> {
                ShopCart sscart = ShopCartConvert.INSTANCE.convert(iicart);
                sscart.setUserId(user.getId());
                sscart.setCartType(1);
                sscart.setOrganizationId(user.getOrganizationId());
                sscart.setErrMessage(StrUtil.format("sku:[{}]不存在", iicart.getGoodsSku()));
                nave.add(sscart);
            });
        } else {
            List<YphStandardClassEntity> standCls = yphStandardClassService.getBaseMapper().selectSimpleByClassCodeIn(mgoods.values().stream().map(ShopGoods::getThirdClass).collect(Collectors.toList()));
            Map<String, String> mclass = standCls.stream().collect(Collectors.toMap(YphStandardClassEntity::getClassCode, YphStandardClassEntity::getTypeCode));
            Map<String, ShopSrmContractEntity> elst = shopGoodsService.goodsCheckContract(mcount.keySet(), null);

            RegionDto rego = new RegionDto();
            rego.setProvince(dzp);
            rego.setCity(dzc);
            rego.setCounty(dzd);

            var tcrr = purchaseOrderService.goodsCheck(mcount, rego, dza, price_resp_map -> {
                ThreadUtil.executeArrayAsync(() -> cst, iicart -> TenantUtils.execute(user.getTenantId(), user, () -> {
                    ShopCart sscart = mskugoods.get(iicart.getGoodsSku() + "_" + iicart.getSupplierCode());

                    if (sscart == null) {
                        sscart = ShopCartConvert.INSTANCE.convert(iicart);
                        sscart.setUserId(user.getId());
                        sscart.setCartType(1);
                        sscart.setOrganizationId(user.getOrganizationId());
                        sscart.setErrMessage(StrUtil.format("sku:[{}]不存在", iicart.getGoodsSku()));
                        nave.add(sscart);
                        return;
                    }

                    sscart.setUserId(user.getId());
                    sscart.setOrganizationId(user.getOrganizationId());
                    sscart.setCartType(1);
                    sscart.setGoodsTypeId(typeMap.get(mclass.get(mgoods.get(sscart.getGoodsCode()).getThirdClass())));
                    if (elst.get(sscart.getGoodsCode()) != null) {
                        sscart.setErrMessage("商品[" + sscart.getGoodsCode() + "]合同过期。");
                        nave.add(sscart);
                        return;
                    }

                    this.setCartNum(sscart, null, String.valueOf(sscart.getGoodsNum()), iicart.getGoodsNum());

                    sscart.setGoodsSalePrice(price_resp_map.get(sscart.getGoodsCode()).getGoodsSalePrice());
                    sscart.setGoodsSaleNakedPrice(price_resp_map.get(sscart.getGoodsCode()).getGoodsNakedSalePrice());
                    nave.add(sscart);
                }), ThreadUtil.getIoIntenseTargetThreadPool());
            });
            crr.putAll(tcrr);
        }

        TenantUtils.execute(user.getTenantId(), user, () -> {
            var need_save_not_null = nave.stream().filter(s -> StrUtil.isNotBlank(s.getGoodsCode())).collect(Collectors.toList());
            var need_save_null = nave.stream().filter(s -> StrUtil.isBlank(s.getGoodsCode())).collect(Collectors.toList());
            Map<String, ShopCart> sclMap = need_save_not_null.stream().collect(Collectors.toMap(ShopCart::getGoodsCode, Function.identity()));
            crr.forEach((k, v) -> sclMap.get(k).setErrMessage(v));
            this.saveBatch(need_save_not_null);
            this.saveBatch(need_save_null);
        });
    }
    public void nonPlatformBatchImportGoodsByExcel(List<NonPlatformGoodsToCartImportVo> importVos) {
        var user = LocalUserHolder.get();
        _d_data(user.getId(), 2);
        var cst = _nonPlatform_pre_check_data(importVos);
        var dz = this.shopAddressService.getDefaultAddress(user.getUsername());
        if (dz == null) {
            dz = this.shopAddressService.getFactoryAddress(user.getUsername());
        }
        if (dz == null) {
            throw new CartException("请在【个人中心】-【地址管理】中设置默认地址");
        }

        var dzp = dz.getProvince();
        var dzc = dz.getCity();
        var dzd = dz.getDistrict();
        var dza = dz.getAddress();

        List<ShopCart> nave = Collections.synchronizedList(new ArrayList<>());
        List<SystemDictDataEntity> a002 = dictDataService.getDictDatasByDictType("A002");
        Map<String, Long> typeMap = a002.stream().collect(Collectors.toMap(SystemDictDataEntity::getValue, SystemDictDataEntity::getId));

        var mcart = cst.stream().collect(Collectors.groupingBy(NonPlatformGoodsToCartImportVo::getSupplierCode));
        Map<String, ShopGoods> mgoods = new HashMap<>();
        var mcount = new HashMap<String, Object>();
        var mskugoods = new HashMap<String, ShopCart>();
        var nest = new ArrayList<NonPlatformGoodsToCartImportVo>();
        mcart.forEach((sup, icart) -> {
            var skulst = icart.stream().map(NonPlatformGoodsToCartImportVo::getGoodsSku).collect(Collectors.toList());

            var tgoods = shopGoodsService.getBaseMapper().getBySkuInAndSupplier(skulst, sup);

            skulst.removeAll(tgoods.stream().map(ShopGoods::getGoodsSku).collect(Collectors.toList()));
            nest.addAll(icart);

            List<ShopCart> qcart = shopGoodsService.queryGoodsBySku(icart.stream().map(NonPlatformGoodsToCartImportVo::getGoodsSku).collect(Collectors.toList()), sup);
            qcart.forEach(sub -> mskugoods.putIfAbsent(sub.getGoodsSku() + "_" + sub.getSupplierCode(), sub));

            mgoods.putAll(tgoods.stream().collect(Collectors.toMap(ShopGoods::getGoodsCode, Function.identity())));
            tgoods.forEach(sub -> {
                icart.stream().filter(gs -> gs.getGoodsSku().equals(sub.getGoodsSku())).findFirst().ifPresent(g -> {
                    mcount.put(sub.getGoodsCode(), g.getGoodsNum());
                });
            });
        });
        Map<String, String> crr = new HashMap<>();
        if (mgoods.isEmpty()) {
            nest.forEach(iicart -> {
                ShopCart sscart = DataAdapter.convert(iicart,ShopCart.class);
                sscart.setUserId(user.getId());
                sscart.setCartType(2);
                sscart.setOrganizationId(user.getOrganizationId());
                sscart.setErrMessage(StrUtil.format("sku:[{}]不存在", iicart.getGoodsSku()));
                nave.add(sscart);
            });
        } else {
            List<YphStandardClassEntity> standCls = yphStandardClassService.getBaseMapper().selectSimpleByClassCodeIn(mgoods.values().stream().map(ShopGoods::getThirdClass).collect(Collectors.toList()));
            Map<String, String> mclass = standCls.stream().collect(Collectors.toMap(YphStandardClassEntity::getClassCode, YphStandardClassEntity::getTypeCode));
            //校验商品合同 dfmall 并且 独立供应商 非对接接口平台供应商
            Map<String, String> contractCheck =  purchaseOrderService.goodsContractCheck(mcount.keySet());

            //合同没权限会提示这个
            if(StrUtil.isNotBlank(contractCheck.get(""))){
                throw new GoodsCheckException(JSON.toJSONString(contractCheck));
            }
            RegionDto rego = new RegionDto();
            rego.setProvince(dzp);
            rego.setCity(dzc);
            rego.setCounty(dzd);

            var tcrr = purchaseOrderService.goodsCheck(mcount, rego, dza, price_resp_map -> {
                ThreadUtil.executeArrayAsync(() -> cst, iicart -> TenantUtils.execute(user.getTenantId(), user, () -> {
                    ShopCart sscart = mskugoods.get(iicart.getGoodsSku() + "_" + iicart.getSupplierCode());
                    sscart.setGoodsNum(0L);
                    if (sscart == null) {
                        sscart = DataAdapter.convert(iicart,ShopCart.class);
                        sscart.setUserId(user.getId());
                        sscart.setCartType(2);
                        sscart.setOrganizationId(user.getOrganizationId());
                        sscart.setErrMessage(StrUtil.format("sku:[{}]不存在", iicart.getGoodsSku()));
                        nave.add(sscart);
                        return;
                    }
                    if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                            || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                            || CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())
                            || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
                        ShopGoodsContractEntity shopGoodsContractEntity = shopGoodsContractService.queryByGoodsId(sscart.getGoodsId(), user.getEntityOrganizationId());
                        SystemContractEntity byId = systemContractService.getById(shopGoodsContractEntity.getContractId());
                        sscart.setContractNumber(byId.getContractCode());
                    }
                    sscart.setUserId(user.getId());
                    sscart.setOrganizationId(user.getOrganizationId());
                    sscart.setCartType(2);
                    sscart.setGoodsTypeId(typeMap.get(mclass.get(mgoods.get(sscart.getGoodsCode()).getThirdClass())));
                    if (contractCheck.get(sscart.getGoodsCode()) != null) {
                        sscart.setErrMessage("商品[" + sscart.getGoodsCode() + "]:" + contractCheck.get(sscart.getGoodsCode()));
                        nave.add(sscart);
                        return;
                    }

                    this.setCartNum(sscart, CompanyEnum.VOYAH.getCompanyCode(), String.valueOf(sscart.getGoodsNum()), iicart.getGoodsNum());

                    sscart.setGoodsSalePrice(price_resp_map.get(sscart.getGoodsCode()).getGoodsSalePrice());
                    sscart.setGoodsSaleNakedPrice(price_resp_map.get(sscart.getGoodsCode()).getGoodsNakedSalePrice());
                    nave.add(sscart);
                }), ThreadUtil.getIoIntenseTargetThreadPool());
            });
            crr.putAll(tcrr);
        }

        TenantUtils.execute(user.getTenantId(), user, () -> {
            var need_save_not_null = nave.stream().filter(s -> StrUtil.isNotBlank(s.getGoodsCode())).collect(Collectors.toList());
            var need_save_null = nave.stream().filter(s -> StrUtil.isBlank(s.getGoodsCode())).collect(Collectors.toList());
            Map<String, ShopCart> sclMap = need_save_not_null.stream().collect(Collectors.toMap(ShopCart::getGoodsCode, Function.identity()));
            crr.forEach((k, v) -> sclMap.get(k).setErrMessage(v));
            this.saveBatch(need_save_not_null);
            this.saveBatch(need_save_null);
            ThreadUtil.getSeqOrScheduledExecutorService()
                    .schedule(() -> this.getBaseMapper().delCartIds(need_save_not_null.stream().map(ShopCart::getCartId).collect(Collectors.toList())), 15, TimeUnit.MINUTES);
        });
    }

    /**
     * 导入购物车前置校验
     *
     * @return {@link List}<{@link GoodsToCartImportVo}>
     */
    // 定义一个私有方法，接收一个GoodsToCartImportVo列表作为参数，返回一个经过预检查的列表。
    private List<GoodsToCartImportVo> _pre_check_data(List<GoodsToCartImportVo> params) {
        // 获取已批准的供应商列表
        val supplier_lst = supplierSrv.list(new LambdaQueryWrapperX<ShopSupplier>()
                .eq(ShopSupplier::getApproveState, 1)
                .eq(ShopSupplier::getDataSource, DFMALL_DATASOURCE)
                .select(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));

        // 将供应商列表转换为一个Map，键为供应商全名，值为供应商代码
        val supplier_map = supplier_lst.stream().collect(Collectors.toMap(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));

        // 创建一个HashMap用于检查重复的供应商和商品SKU
        Map<String, GoodsToCartImportVo> dup_chk_map = new HashMap<>(params.size());

        // 遍历params列表，过滤掉供应商名称为空的项
        params.stream().filter(k -> !StrUtil.isBlank(k.getSupplierName())).forEach(vo_item -> {
            // 获取导入项的供应商名称和商品SKU
            String import_supplier = vo_item.getSupplierName();
            String goodsSku = vo_item.getGoodsSku();
            // 如果供应商名称、商品SKU或商品数量为空，则抛出参数异常
            if (StrUtil.isBlank(import_supplier) || StrUtil.isBlank(goodsSku) || vo_item.getGoodsNum() == null) {
                throw new ParameterException(StrUtil.format("文档格式不对，存在空的供应商和sku"));
            }

            // 如果供应商Map中不包含导入项的供应商，则抛出参数异常
            if (!supplier_map.containsKey(import_supplier)) {
                throw new ParameterException(StrUtil.format("供应商[{}]不存在，或者供应商不是东风商城签约的电商。", import_supplier));
            } else {
                // 如果供应商存在，则从Map中获取供应商代码
                String supplier = supplier_map.get(import_supplier);
                // 如果重复检查Map中已经包含了该供应商和商品SKU的组合，则合并商品数量
                if (dup_chk_map.containsKey(supplier + goodsSku)) {
                    GoodsToCartImportVo importVo = dup_chk_map.get(supplier + goodsSku);
                    importVo.setGoodsNum(importVo.getGoodsNum().add(vo_item.getGoodsNum()));
                } else {
                    // 如果不包含，则设置导入项的供应商代码，并将其添加到重复检查Map中
                    vo_item.setSupplierCode(supplier);
                    dup_chk_map.put(supplier + goodsSku, vo_item);
                }
            }
        });
        // 获取重复检查Map中的所有值
        Collection<GoodsToCartImportVo> importVos = dup_chk_map.values();
        // 如果导入的数量超过了预设的KM值，则抛出参数异常
        if (importVos.size() > KM) throw new ParameterException("最大导入数量限制" + KM + "条");
        // 将结果转换为ArrayList并返回
        return new ArrayList<>(importVos);
    }

    /**
     * 导入购物车前置校验
     *
     * @return {@link List}<{@link GoodsToCartImportVo}>
     */
    // 定义一个私有方法，接收一个GoodsToCartImportVo列表作为参数，返回一个经过预检查的列表。
    private List<NonPlatformGoodsToCartImportVo> _nonPlatform_pre_check_data(List<NonPlatformGoodsToCartImportVo> params) {
        var user = LocalUserHolder.get();
        List<ShopSupplier> supplier_lst = Lists.newArrayList();
        if (CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode()) || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            // 获取已批准的供应商列表
            supplier_lst = supplierSrv.list(new LambdaQueryWrapperX<ShopSupplier>()
                    .eq(ShopSupplier::getApproveState, 1)
                    .eq(ShopSupplier::getDataSource, user.getEntityOrganizationCode())
                    .select(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));
        } else if (CompanyEnum.DFPV.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode()) || CompanyEnum.DNA.getCompanyCode().equalsIgnoreCase(user.getEntityOrganizationCode())) {
            // 获取已批准的供应商列表
            supplier_lst = supplierSrv.list(new LambdaQueryWrapperX<ShopSupplier>()
                    .eq(ShopSupplier::getApproveState, 1)
                    .eq(ShopSupplier::getDataSource, CompanyConstant.S4_DATA_SOURCE)
                    .select(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));
        }


        // 将供应商列表转换为一个Map，键为供应商全名，值为供应商代码
        val supplier_map = supplier_lst.stream().collect(Collectors.toMap(ShopSupplier::getSupplierFullName, ShopSupplier::getSupplierCode));

        // 创建一个HashMap用于检查重复的供应商和商品SKU
        Map<String, NonPlatformGoodsToCartImportVo> dup_chk_map = new HashMap<>(params.size());

        // 遍历params列表，过滤掉供应商名称为空的项
        params.stream().filter(k -> !StrUtil.isBlank(k.getSupplierName())).forEach(vo_item -> {
            // 获取导入项的供应商名称和商品SKU
            String import_supplier = vo_item.getSupplierName();
            String goodsSku = vo_item.getGoodsSku();
            // 如果供应商名称、商品SKU或商品数量为空，则抛出参数异常
            if (StrUtil.isBlank(import_supplier) || StrUtil.isBlank(goodsSku) || vo_item.getGoodsNum() == null) {
                throw new ParameterException(StrUtil.format("文档格式不对，存在空的供应商和sku"));
            }

            // 如果供应商Map中不包含导入项的供应商，则抛出参数异常
            if (!supplier_map.containsKey(import_supplier)) {
                throw new ParameterException(StrUtil.format("供应商[{}]不存在。", import_supplier));
            } else {
                // 如果供应商存在，则从Map中获取供应商代码
                String supplier = supplier_map.get(import_supplier);
                // 如果重复检查Map中已经包含了该供应商和商品SKU的组合，则合并商品数量
                if (dup_chk_map.containsKey(supplier + goodsSku)) {
                    NonPlatformGoodsToCartImportVo importVo = dup_chk_map.get(supplier + goodsSku);
                    importVo.setGoodsNum(importVo.getGoodsNum().add(vo_item.getGoodsNum()));
                } else {
                    // 如果不包含，则设置导入项的供应商代码，并将其添加到重复检查Map中
                    vo_item.setSupplierCode(supplier);
                    dup_chk_map.put(supplier + goodsSku, vo_item);
                }
            }
        });
        // 获取重复检查Map中的所有值
        Collection<NonPlatformGoodsToCartImportVo> importVos = dup_chk_map.values();
        // 如果导入的数量超过了预设的KM值，则抛出参数异常
        if (importVos.size() > KM) throw new ParameterException("最大导入数量限制" + KM + "条");
        // 将结果转换为ArrayList并返回
        return new ArrayList<>(importVos);
    }

    public void _d_data(Long userId, Integer cartType) {
        this.getBaseMapper().deleteByUserAndType(userId, cartType);
    }

    public Map<Object, Long> getCartGoodsNumByActivity(String activityCode) {
        if (StrUtil.isBlank(activityCode)) {
            return new HashMap<>();
        }
        List<String> activityCodeList = StrUtil.split(activityCode, ",");
        final LoginUser user = LocalUserHolder.get();
        return this.shopCartMapper.getCartGoodsNumByActivity(activityCodeList, user.getId());
    }

    public void setCartNum(ShopCart shopCart, String supplierSource, String goodsMoq, BigDecimal goodsNum) {
        if ("srm".equals(supplierSource) || CompanyEnum.VOYAH.getCompanyCode().equalsIgnoreCase(supplierSource) || CompanyEnum.HONDA.getCompanyCode().equalsIgnoreCase(supplierSource)
                || CompanyConstant.S4_DATA_SOURCE.equalsIgnoreCase(supplierSource)) {
            shopCart.setGoodsNumDecimal(new BigDecimal(goodsMoq).compareTo(goodsNum) > 0 ? new BigDecimal(goodsMoq) : goodsNum);
        } else {
            shopCart.setGoodsNum(Math.max(Long.parseLong(goodsMoq), goodsNum.longValue()));
        }
    }

    /**
     * 获取商品结算方式
     */
    public int getGoodsSettlement(Long goodsId,String supplierCode,Long companyId){
        ShopSupplier supplier = supplierSrv.selectByCode(supplierCode);
        if("dfmall".equals(supplier.getDataSource())){
            List<ShopGoodsContractVo> settlementList = goodsSettlementService.queryVoByGoodsIds(Collections.singleton(goodsId),1);
            return toGoodsSettlement(supplier, settlementList, companyId);
        }else {
            return 0;
        }
    }
    /**
     * 计算结算方式
     */
    private int toGoodsSettlement(ShopSupplier supplier, List<ShopGoodsContractVo> settlementList, Long companyId){
        if("dfmall".equals(supplier.getDataSource())){
            List<ShopGoodsContractVo> companyList = new ArrayList<>();
            String nowDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            if(CollUtil.isNotEmpty(settlementList)){
                settlementList.forEach( settle ->{
                    if(settle.getSignCompanyId().equals(companyId)
                            && settle.getContractStartDate().compareTo(nowDate) <= 0
                            && settle.getContractEndDate().compareTo(nowDate) >= 0
                            && settle.getAuditState() == 1){
                        companyList.add(settle);
                    }
                });
            }

            if(supplier.getSupplierType() == 0 || openApiConfig.getSuppliers().contains(supplier.getSupplierCode())){
                return 1;
            }

            //与企业存在合同 则非平台结算
            if(CollectionUtil.isNotEmpty(companyList)){
                return 0;
            }

            if(CollUtil.isNotEmpty(settlementList)){
                List<ShopGoodsContractVo> paltContract = settlementList.stream()
                        .filter( c -> c.getOrgType() == 3 && c.getAuditState() == 1 )
                        .collect(Collectors.toList());
                return CollectionUtil.isNotEmpty(paltContract)  ? 1 : 0;
            }
            return 0;
        }else {
            return 0;
        }
    }
    public ServiceResult<?> checkGoodsIsExistInCart(String activityCode, Long goodsId) {
        if (StrUtil.isBlank(activityCode) || goodsId==null) {
            return ServiceResult.error("商品id活动编码不能为空");
        }
        final LoginUser user = LocalUserHolder.get();
        List<ShopCart> list = this.shopCartMapper.checkGoodsIsExistInCart(activityCode, goodsId, user.getId());
        if(CollectionUtil.isEmpty(list)){
            return ServiceResult.succ(false);
        }
        return ServiceResult.succ(true);
    }

    private void checkGoodsTaxPrice(String goodsCode,  ShopSupplier supplier, RemotePriceInfoResp remotePriceInfoResp ) {
        if (null == remotePriceInfoResp || remotePriceInfoResp.getGoodsSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new OrderException("商品[" + goodsCode + "]无售卖价格");
        }

        if(supplier.getSupplierType()==0 && remotePriceInfoResp.getGoodsSalePrice().compareTo(remotePriceInfoResp.getGoodsNakedSalePrice())<=0){
            throw new OrderException("商品[" + goodsCode + "]税率异常");
        }
    }
}
