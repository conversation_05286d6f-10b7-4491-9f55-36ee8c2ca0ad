package com.ly.yph.api.virtualgoods.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年11月25日
 */
@Data
public class YZHCardOrderDetailDto implements Serializable {
    private static final long serialVersionUID = 5396163619471067103L;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 卡密
     */
    private String cardPwd;
    /**
     * 卡密兑换地址
     */
    private String url;
    /**
     * 虚拟商品订单编号
     */
    private String virtualOrderCode;
    /**
     * 有效期
     */
    private Date useEndTime;
    /**
     * 卡密状态（1 已核销，其他 /）
     */
    private Integer cardStatus;
}
